"""create payroll_details table

Revision ID: 47c47ba6783c
Revises: d1a482404be0
Create Date: 2024-04-03 15:05:28.132207

"""
from typing import Sequence, Union

from alembic import op
from sqlalchemy import func, TIMESTAMP, ForeignKey
from sqlalchemy import Column, Integer, String, Float

# revision identifiers, used by Alembic.
revision: str = '47c47ba6783c'
down_revision: Union[str, None] = 'd1a482404be0'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    op.create_table(
        'payroll_details',
        Column('id', Integer, primary_key=True),
        Column("processing_id", Integer, nullable=False),
        <PERSON>umn("employee_id", Integer, ForeignKey("employees.id")),
        Column('net_salary', Float, nullable=False),
        Column("approval_status", Integer, nullable=False),
        Column('gross_salary', Float, nullable=False),
        Column('tax', Float, nullable=False),
        Column('cost_compony', Float, nullable=False),
        Column('total_earnings', Float, nullable=False),
        Column('total_deduction', Float, nullable=False),
        Column('payment_status', Integer, nullable=False),
        Column('pension', Integer, nullable=False),
        Column('nhf', Integer, nullable=False),
        Column("timestamp", TIMESTAMP, server_default=func.now()),

    )

def downgrade() -> None:
    op.drop_table("payroll_details")