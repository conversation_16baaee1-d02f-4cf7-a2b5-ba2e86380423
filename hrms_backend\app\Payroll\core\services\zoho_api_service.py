import requests
from datetime import datetime, timedelta
from core.services.zoho_config import ZohoConfigService

class ZohoAPIError(Exception):
    """Base exception for Zoho API errors"""
    def __init__(self, message, status_code=None):
        self.message = message
        self.status_code = status_code or 400
        super().__init__(message)

class ZohoAuthError(ZohoAPIError):
    """Authentication/authorization specific errors"""
    def __init__(self, message, status_code=401):
        super().__init__(message, status_code)

class ZohoAPIService:
    AUTH_URL = "https://accounts.zoho.com/oauth/v2/token"
    API_BASE_URL = f"https://people.zoho.com/people/"
        
    def __init__(self):
        self.zoho_configuration = ZohoConfigService()
    
    def _get_config(self):
        return self.zoho_configuration.getConfig()
    
    def _refresh_access_token(self, config):
        params = {
            "refresh_token": config.refresh_token,
            "client_id": config.client_id,
            "client_secret": config.client_secret,
            "grant_type": "refresh_token"
        }
        
        response = requests.post(self.AUTH_URL, params=params)
        response.raise_for_status()
        
        data = response.json()
        config.access_token = data["access_token"]
        config.token_expiry = datetime.now() + timedelta(seconds=data["expires_in"])
        
        return self.zoho_configuration.updateConfig(config)
    
    def _get_authenticated_config(self):
        config = self._get_config()
        if not config or not config.is_enabled:
            raise ZohoAuthError(f"Zoho configuration disabled")

        params = {
            "refresh_token": config.refresh_token,
            "client_id": config.client_id,
            "client_secret": config.client_secret,
            "grant_type": "refresh_token"
        }
        
        response = requests.post(self.AUTH_URL, params=params)
        response.raise_for_status()
        
        data = response.json()
        access_token = data["access_token"]
        # config.token_expiry = datetime.now() + timedelta(seconds=data["expires_in"])
        
        return access_token, config
                
        # return config
    
    def get_employees(self):
        index_value = 0
        endpoint = F"api/forms/employee/getRecords?sIndex={index_value}&limit=200"
        access_token, config = self._get_authenticated_config()
        if not access_token:
            return []
        
        headers = {
            "Authorization": f"Zoho-oauthtoken {access_token}",
            # "orgId": config.org_id
        }
        
        response = requests.get(
            f"{self.API_BASE_URL}/{endpoint}",
            headers=headers
        )
        response.raise_for_status()
        
        employees = response.json().get("response", []).get("result", [])
        # print("employees ", employees)

        # Update last sync time
        self.zoho_configuration.updateConfig(config.id, {"last_sync": datetime.now()})
        
        return employees
    
    def _get_tokens(self, client_id: str, client_secret: str, code: str) -> tuple:
        headers = {
            "grant_type": "authorization_code",
            "client_id": client_id,
            "client_secret": client_secret,
            "code": code
        }

        try:
            response = requests.post(
                self.AUTH_URL,
                data=headers, 
                headers={'Content-Type': 'application/x-www-form-urlencoded'},
                timeout=10
            )
            
            response.raise_for_status()
            data = response.json()
            # print("data ", data)

            if 'error' in data:
                error = data.get('error', 'unknown_error')
                
                if error == 'invalid_client':
                    raise ZohoAuthError(f"Invalid client credentials")
                elif error == 'invalid_code':
                    raise ZohoAuthError(f"Invalid authorization code")
                else:
                    raise ZohoAPIError(f"Zoho API error: {error}")
            
            access_token = data.get('access_token')
            refresh_token = data.get('refresh_token')
            expires_in = data.get('expires_in')
            
            if not access_token or not refresh_token:
                raise ZohoAPIError("Missing tokens in successful response")
                
            return access_token, refresh_token, expires_in
            
        except requests.exceptions.HTTPError as e:
            if e.response.status_code == 401:
                raise ZohoAuthError("Invalid API credentials")
            elif e.response.status_code == 400:
                try:
                    error_data = e.response.json()
                    raise ZohoAuthError(error_data.get('error_description', 'Bad request'))
                except ValueError:
                    raise ZohoAuthError("Invalid request parameters")
            else:
                raise ZohoAPIError(f"HTTP error: {str(e)}")
                
        except requests.exceptions.RequestException as e:
            raise ZohoAPIError(f"Network error: {str(e)}")