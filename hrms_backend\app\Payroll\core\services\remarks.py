from datetime import datetime
from core.models.employee_component_pivot import EmployeeComponentsPivotModel
from core.models.remark import RemarkModel
from core.models.salary_tempplate_component_pivot import SalaryTemplateComponentsPivotModel
from core.repositories.remarks import RemarksRepository
from core.databases.database import db


    
    
class RemarkService:
    def __init__(self) -> None:
        self.repository = RemarksRepository()

    @staticmethod
    def createRemark(employee_id, payroll_history_id, remark):
        if not employee_id or not payroll_history_id or not remark:
            raise ValueError("Employee ID, payroll history ID, and remark are required.")
        remark_data = {"employee_id": employee_id, "payroll_history_id": payroll_history_id, "remark": remark}
        return RemarksRepository.createRemark(**remark_data)

    @staticmethod
    def getRemarkById(id):
        return RemarksRepository.getRemarkById(id)

    @staticmethod
    def getRemarksByPayrollHistoryId(payroll_history_id):
        return RemarksRepository.getRemarksByPayrollHistoryId(payroll_history_id)

    @staticmethod
    def getRemarksByEmployeeId(employee_id):
        return RemarksRepository.getRemarksByEmployeeId(employee_id)

    @staticmethod
    def updateRemark(remark_id, **kwargs):
        # Ensure only valid fields are passed to the repository
        valid_fields = ["employee_id", "payroll_history_id", "remark", "updated_at"]
        filtered_kwargs = {key: value for key, value in kwargs.items() if key in valid_fields}

        # Ensure remark is not empty
        if "remark" in filtered_kwargs and not filtered_kwargs["remark"]:
            raise ValueError("Remark cannot be empty.")

        # Update the timestamp for updates
        filtered_kwargs["updated_at"] = datetime.now()

        return RemarksRepository.updateRemark(remark_id, **filtered_kwargs)

    @staticmethod
    def deleteRemark(remark_id):
    # Call the repository to delete the remark
        return RemarksRepository.deleteRemark(remark_id)

