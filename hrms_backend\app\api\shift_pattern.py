from fastapi import APIRouter, HTTPException, status, Depends, Query
from sqlalchemy.orm import Session
from typing import List, Optional, Dict
from uuid import UUID
from datetime import date

from ..core.security import get_current_user, require_permission, CurrentUser
from ..core.rbac import Permissions
from ..db.session import get_db
from ..schemas.shift import (
    ShiftPatternCreate, ShiftPatternUpdate, ShiftPatternResponse,
    ShiftPatternAssignmentCreate, ShiftPatternAssignmentResponse
)
from ..services.shift_management.shift_pattern_service import shift_pattern_service

router = APIRouter()


@router.post("/", response_model=ShiftPatternResponse)
async def create_shift_pattern(
    pattern_data: ShiftPatternCreate,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.SHIFT_CREATE))
):
    """Create a new shift pattern"""
    return await shift_pattern_service.create_shift_pattern(db, pattern_data, current_user)


@router.get("/", response_model=List[ShiftPatternResponse])
async def get_shift_patterns(
    skip: int = Query(0, ge=0),
    limit: int = Query(20, ge=1, le=100),
    search: Optional[str] = Query(None),
    pattern_type: Optional[str] = Query(None),
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.SHIFT_READ))
):
    """Get shift patterns with filtering"""
    return await shift_pattern_service.get_shift_patterns(
        db, current_user, skip, limit, search, pattern_type
    )


@router.get("/{pattern_id}", response_model=ShiftPatternResponse)
async def get_shift_pattern(
    pattern_id: UUID,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.SHIFT_READ))
):
    """Get shift pattern by ID"""
    return await shift_pattern_service.get_shift_pattern_by_id(db, pattern_id, current_user)


@router.put("/{pattern_id}", response_model=ShiftPatternResponse)
async def update_shift_pattern(
    pattern_id: UUID,
    pattern_data: ShiftPatternUpdate,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.SHIFT_UPDATE))
):
    """Update shift pattern"""
    return await shift_pattern_service.update_shift_pattern(db, pattern_id, pattern_data, current_user)


@router.delete("/{pattern_id}")
async def delete_shift_pattern(
    pattern_id: UUID,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.SHIFT_DELETE))
):
    """Delete shift pattern"""
    await shift_pattern_service.delete_shift_pattern(db, pattern_id, current_user)
    return {"message": "Shift pattern deleted successfully"}


# Pattern Assignments
@router.post("/assignments/", response_model=ShiftPatternAssignmentResponse)
async def assign_pattern_to_employee(
    assignment_data: ShiftPatternAssignmentCreate,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.SHIFT_CREATE))
):
    """Assign shift pattern to employee"""
    return await shift_pattern_service.assign_pattern_to_employee(db, assignment_data, current_user)


@router.get("/assignments/", response_model=List[ShiftPatternAssignmentResponse])
async def get_pattern_assignments(
    employee_id: Optional[UUID] = Query(None),
    pattern_id: Optional[UUID] = Query(None),
    start_date: Optional[date] = Query(None),
    end_date: Optional[date] = Query(None),
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.SHIFT_READ))
):
    """Get pattern assignments with filtering"""
    return await shift_pattern_service.get_pattern_assignments(
        db, current_user, employee_id, pattern_id, start_date, end_date
    )


@router.get("/assignments/{assignment_id}", response_model=ShiftPatternAssignmentResponse)
async def get_pattern_assignment(
    assignment_id: UUID,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.SHIFT_READ))
):
    """Get pattern assignment by ID"""
    return await shift_pattern_service.get_pattern_assignment_by_id(db, assignment_id, current_user)


@router.put("/assignments/{assignment_id}/deactivate")
async def deactivate_pattern_assignment(
    assignment_id: UUID,
    end_date: Optional[date] = None,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.SHIFT_UPDATE))
):
    """Deactivate pattern assignment"""
    await shift_pattern_service.deactivate_pattern_assignment(db, assignment_id, end_date, current_user)
    return {"message": "Pattern assignment deactivated successfully"}


# Schedule Generation
@router.get("/schedule/{employee_id}", response_model=Dict)
async def get_employee_pattern_schedule(
    employee_id: UUID,
    start_date: date = Query(...),
    end_date: date = Query(...),
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.SHIFT_READ))
):
    """Get employee's schedule based on assigned patterns"""
    return await shift_pattern_service.get_employee_pattern_schedule(
        db, employee_id, start_date, end_date, current_user
    )


@router.post("/schedule/bulk-generate")
async def bulk_generate_pattern_schedules(
    start_date: date,
    end_date: date,
    employee_ids: Optional[List[UUID]] = None,
    department_id: Optional[UUID] = None,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.SHIFT_CREATE))
):
    """Bulk generate schedules for multiple employees based on their patterns"""
    return await shift_pattern_service.bulk_generate_schedules(
        db, start_date, end_date, employee_ids, department_id, current_user
    )


@router.get("/preview/{pattern_id}")
async def preview_pattern_schedule(
    pattern_id: UUID,
    start_date: date = Query(...),
    days: int = Query(30, ge=1, le=365),
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.SHIFT_READ))
):
    """Preview what a pattern schedule would look like"""
    return await shift_pattern_service.preview_pattern_schedule(
        db, pattern_id, start_date, days, current_user
    )


# Pattern Analytics
@router.get("/analytics/utilization")
async def get_pattern_utilization_analytics(
    start_date: Optional[date] = Query(None),
    end_date: Optional[date] = Query(None),
    pattern_id: Optional[UUID] = Query(None),
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.SHIFT_READ))
):
    """Get pattern utilization analytics"""
    return await shift_pattern_service.get_pattern_analytics(
        db, current_user, start_date, end_date, pattern_id
    )


@router.get("/templates/")
async def get_pattern_templates(
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.SHIFT_READ))
):
    """Get predefined shift pattern templates"""
    return {
        "templates": [
            {
                "name": "Standard 5-Day Week",
                "description": "Monday to Friday, 9 AM to 6 PM",
                "pattern_type": "fixed",
                "rotation_days": 7,
                "working_days": [0, 1, 2, 3, 4],
                "rest_days": [5, 6],
                "shifts_config": {
                    "default": {
                        "name": "Day Shift",
                        "start_time": "09:00",
                        "end_time": "18:00",
                        "type": "regular",
                        "break_duration": 60,
                        "is_overnight": False
                    }
                }
            },
            {
                "name": "3-Shift Rotation",
                "description": "Rotating day, evening, and night shifts",
                "pattern_type": "rotating",
                "rotation_days": 21,
                "working_days": [0, 1, 2, 3, 4, 5, 6],
                "rest_days": [],
                "shifts_config": {
                    "0": {"name": "Day Shift", "start_time": "06:00", "end_time": "14:00", "type": "day"},
                    "1": {"name": "Day Shift", "start_time": "06:00", "end_time": "14:00", "type": "day"},
                    "2": {"name": "Day Shift", "start_time": "06:00", "end_time": "14:00", "type": "day"},
                    "3": {"name": "Day Shift", "start_time": "06:00", "end_time": "14:00", "type": "day"},
                    "4": {"name": "Day Shift", "start_time": "06:00", "end_time": "14:00", "type": "day"},
                    "5": {"name": "Rest Day", "start_time": "00:00", "end_time": "00:00", "type": "rest"},
                    "6": {"name": "Rest Day", "start_time": "00:00", "end_time": "00:00", "type": "rest"},
                    "7": {"name": "Evening Shift", "start_time": "14:00", "end_time": "22:00", "type": "evening"},
                    "8": {"name": "Evening Shift", "start_time": "14:00", "end_time": "22:00", "type": "evening"},
                    "9": {"name": "Evening Shift", "start_time": "14:00", "end_time": "22:00", "type": "evening"},
                    "10": {"name": "Evening Shift", "start_time": "14:00", "end_time": "22:00", "type": "evening"},
                    "11": {"name": "Evening Shift", "start_time": "14:00", "end_time": "22:00", "type": "evening"},
                    "12": {"name": "Rest Day", "start_time": "00:00", "end_time": "00:00", "type": "rest"},
                    "13": {"name": "Rest Day", "start_time": "00:00", "end_time": "00:00", "type": "rest"},
                    "14": {"name": "Night Shift", "start_time": "22:00", "end_time": "06:00", "type": "night", "is_overnight": True},
                    "15": {"name": "Night Shift", "start_time": "22:00", "end_time": "06:00", "type": "night", "is_overnight": True},
                    "16": {"name": "Night Shift", "start_time": "22:00", "end_time": "06:00", "type": "night", "is_overnight": True},
                    "17": {"name": "Night Shift", "start_time": "22:00", "end_time": "06:00", "type": "night", "is_overnight": True},
                    "18": {"name": "Night Shift", "start_time": "22:00", "end_time": "06:00", "type": "night", "is_overnight": True},
                    "19": {"name": "Rest Day", "start_time": "00:00", "end_time": "00:00", "type": "rest"},
                    "20": {"name": "Rest Day", "start_time": "00:00", "end_time": "00:00", "type": "rest"}
                }
            },
            {
                "name": "4-Day Work Week",
                "description": "Tuesday to Friday, 10-hour shifts",
                "pattern_type": "fixed",
                "rotation_days": 7,
                "working_days": [1, 2, 3, 4],
                "rest_days": [0, 5, 6],
                "shifts_config": {
                    "default": {
                        "name": "Extended Day Shift",
                        "start_time": "08:00",
                        "end_time": "18:00",
                        "type": "regular",
                        "break_duration": 90,
                        "is_overnight": False
                    }
                }
            }
        ]
    }
