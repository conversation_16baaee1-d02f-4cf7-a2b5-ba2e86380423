from celery import Celery
from celery.schedules import crontab
import logging

from .config import settings

logger = logging.getLogger(__name__)

# Create Celery app
celery_app = Celery(
    "hrms_worker",
    broker=settings.CELERY_BROKER_URL,
    backend=settings.CELERY_RESULT_BACKEND,
    include=[
        "app.workers.tasks",
        "app.workers.attendance_tasks",
        "app.workers.leave_tasks",
        "app.workers.payroll_tasks",
        "app.workers.notification_tasks",
        "app.workers.report_tasks"
    ]
)

# Celery configuration
celery_app.conf.update(
    task_serializer="json",
    accept_content=["json"],
    result_serializer="json",
    timezone="UTC",
    enable_utc=True,
    task_track_started=True,
    task_time_limit=30 * 60,  # 30 minutes
    task_soft_time_limit=25 * 60,  # 25 minutes
    worker_prefetch_multiplier=1,
    worker_max_tasks_per_child=1000,
)

# Periodic tasks schedule
celery_app.conf.beat_schedule = {
    # Daily attendance auto-checkout
    "auto-checkout-attendance": {
        "task": "app.workers.attendance_tasks.auto_checkout_employees",
        "schedule": crontab(hour=23, minute=59),  # 11:59 PM daily
    },

    # Daily leave balance updates
    "update-leave-balances": {
        "task": "app.workers.leave_tasks.update_leave_balances",
        "schedule": crontab(hour=1, minute=0),  # 1:00 AM daily
    },

    # Weekly timesheet reminders
    "timesheet-reminders": {
        "task": "app.workers.notification_tasks.send_timesheet_reminders",
        "schedule": crontab(day_of_week=1, hour=9, minute=0),  # Monday 9:00 AM
    },

    # Monthly payroll processing reminder
    "payroll-processing-reminder": {
        "task": "app.workers.payroll_tasks.send_payroll_processing_reminder",
        "schedule": crontab(day_of_month=25, hour=10, minute=0),  # 25th of month, 10:00 AM
    },

    # Daily SLA breach checks
    "check-sla-breaches": {
        "task": "app.workers.tasks.check_sla_breaches",
        "schedule": crontab(minute="*/30"),  # Every 30 minutes
    },

    # Daily performance goal reminders
    "goal-deadline-reminders": {
        "task": "app.workers.notification_tasks.send_goal_deadline_reminders",
        "schedule": crontab(hour=9, minute=0),  # 9:00 AM daily
    },

    # Weekly engagement survey reminders
    "survey-reminders": {
        "task": "app.workers.notification_tasks.send_survey_reminders",
        "schedule": crontab(day_of_week=3, hour=14, minute=0),  # Wednesday 2:00 PM
    },

    # Daily delegation expiry checks
    "check-delegation-expiry": {
        "task": "app.workers.tasks.check_delegation_expiry",
        "schedule": crontab(hour=8, minute=0),  # 8:00 AM daily
    },

    # Monthly report generation
    "generate-monthly-reports": {
        "task": "app.workers.report_tasks.generate_monthly_reports",
        "schedule": crontab(day_of_month=1, hour=6, minute=0),  # 1st of month, 6:00 AM
    },

    # Daily cleanup tasks
    "cleanup-expired-tokens": {
        "task": "app.workers.tasks.cleanup_expired_tokens",
        "schedule": crontab(hour=2, minute=0),  # 2:00 AM daily
    },
}

# Task routing
celery_app.conf.task_routes = {
    "app.workers.attendance_tasks.*": {"queue": "attendance"},
    "app.workers.leave_tasks.*": {"queue": "leave"},
    "app.workers.payroll_tasks.*": {"queue": "payroll"},
    "app.workers.notification_tasks.*": {"queue": "notifications"},
    "app.workers.report_tasks.*": {"queue": "reports"},
    "app.workers.tasks.*": {"queue": "general"},
}

# Error handling
@celery_app.task(bind=True)
def debug_task(self):
    print(f"Request: {self.request!r}")


# Celery signals
@celery_app.signals.worker_ready.connect
def worker_ready(sender=None, **kwargs):
    logger.info("Celery worker is ready")


@celery_app.signals.worker_shutdown.connect
def worker_shutdown(sender=None, **kwargs):
    logger.info("Celery worker is shutting down")


@celery_app.signals.task_failure.connect
def task_failure_handler(sender=None, task_id=None, exception=None, traceback=None, einfo=None, **kwargs):
    logger.error(f"Task {task_id} failed: {exception}")


@celery_app.signals.task_success.connect
def task_success_handler(sender=None, task_id=None, result=None, **kwargs):
    logger.info(f"Task {task_id} completed successfully")


if __name__ == "__main__":
    celery_app.start()
