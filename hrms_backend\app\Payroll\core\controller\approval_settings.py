from flask import url_for, jsonify
from flask.views import <PERSON><PERSON>iew
from flask_smorest import Blueprint, abort
from core.middleware import roles_required
from schemas import ApprovalSettingSchema
from sqlalchemy.orm.exc import NoResultFound
from sqlalchemy.exc import SQLAlchemyError, IntegrityError

import core.utils.response_message as RESPONSEMESSAGE
from core.services.approval_settings import ApprovalSettingsService
from core.utils.responseBuilder import ResponseBuilder

blueprint = Blueprint("Approval Settings", __name__, description="Operations for Approval Settings")
    
@blueprint.route("/approval_settings/<id>")
class ApprovalSettingList(MethodView):
    @roles_required(['admin'])
    @blueprint.response(200, ApprovalSettingSchema)
    def get(self, id):
        service = ApprovalSettingsService()
        approval_settings = service.getApprovalSettings(id)
        if not approval_settings:
            abort(401, message="Approval setting does not exist")
        approval_settings_details = ApprovalSettingSchema().dump(approval_settings)
        return ResponseBuilder(data=approval_settings_details, status_code=200).build()  
    
    @roles_required(['admin'])
    def delete(self, id):
        service = ApprovalSettingsService()
        approval_settings = service.getApprovalSettings(id)
        if not approval_settings:
            abort(404, message="Approval setting does not exist")
        service.deleteApprovalSettings(id)
        return {"message" : "Approval setting deleted"}
    
    @roles_required(['admin'])
    @blueprint.arguments(ApprovalSettingSchema)
    @blueprint.response(201, ApprovalSettingSchema)
    def put(self, data, id):
        service = ApprovalSettingsService()
        approval_settings = service.getApprovalSettings(id)
        if not approval_settings:
            abort(404, message="Approval setting does not exist")
        try :
            new_approval_setting = service.updateApprovalSettings(id, data)
            return new_approval_setting
        except SQLAlchemyError:
                abort(500, message="Error while updating Approval setting")
    
@blueprint.route("/approval_settings")
class ApprovalSetting(MethodView):
    @roles_required(['admin'])
    @blueprint.response(200, ApprovalSettingSchema)
    def get(self):
        service = ApprovalSettingsService()
        approval_settings_list, total_approval_settings = service.fetchAll()
        approval_settings_schema = ApprovalSettingSchema(many=True)
        approval_settings_list = approval_settings_schema.dump(approval_settings_list)
        return ResponseBuilder(data=approval_settings_list, status_code=200, total=total_approval_settings).build()

    @roles_required(['admin'])
    @blueprint.arguments(ApprovalSettingSchema)
    @blueprint.response(200, ApprovalSettingSchema)
    def post(self, data):
        service = ApprovalSettingsService()
        try:
            service = ApprovalSettingsService()
            new_approval_setting = service.createOrUpdateApprovalSettingss(data)
        except IntegrityError:
            abort(500, message="Error while creating Approval setting")
        except SQLAlchemyError as e:
            print(e)
            abort(500, message="Error while creating Approval setting")
        return new_approval_setting