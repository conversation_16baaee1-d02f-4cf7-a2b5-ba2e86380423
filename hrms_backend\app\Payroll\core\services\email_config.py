from core.repositories.email_config import EmailConfigurationRepository

class EmailConfig:
    def __init__(self) -> None:
        self.repository = EmailConfigurationRepository()

    def createConfig(self, Kwargs):
        return self.repository.createConfiguration(**Kwargs)
    
    def fetchAll(self):
        configurations = self.repository.getConfig()
        total_config = len(configurations)
        return configurations, total_config

    def getActiveConfig(self):
        return self.repository.getActiveConfig()
    
    def getConfig(self):
        return self.repository.getConfig()
    
    def updateBanks(self, id, Kwargs):
        return self.repository.updateConfig(id, **Kwargs)
        
    def deleteConfig(self):
        return self.repository.deleteConfig()
    
    