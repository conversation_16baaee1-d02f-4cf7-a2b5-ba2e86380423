"""Add prorate_gross to Payroll_history table

Revision ID: 86532b95d501
Revises: 647b698381f9
Create Date: 2025-02-04 15:25:44.412659

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '86532b95d501'
down_revision: Union[str, None] = '647b698381f9'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    op.add_column("payroll_history", sa.Column("prorated_gross", sa.Float(), nullable=True))


def downgrade() -> None:
    op.drop_column("payroll_history","prorated_gross")
