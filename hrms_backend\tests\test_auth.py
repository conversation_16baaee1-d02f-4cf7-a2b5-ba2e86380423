#!/usr/bin/env python3
"""
Test authentication endpoints
"""

import requests
import json

def test_login():
    """Test login endpoint"""
    url = "http://localhost:8085/api/auth/login"
    
    # Test with admin user
    payload = {
        "email": "ADMIN001",  # Using employee_id as email
        "password": "password123"  # Password is not validated yet
    }
    
    headers = {
        "Content-Type": "application/json"
    }
    
    try:
        print("Testing login with admin user...")
        response = requests.post(url, json=payload, headers=headers)
        
        print(f"Status Code: {response.status_code}")
        print(f"Response: {json.dumps(response.json(), indent=2)}")
        
        if response.status_code == 200:
            print("✅ Login successful!")
            return response.json()
        else:
            print("❌ Login failed!")
            return None
            
    except Exception as e:
        print(f"❌ Error testing login: {e}")
        return None

def test_employee_login():
    """Test login with regular employee"""
    url = "http://localhost:8085/api/auth/login"
    
    # Test with regular employee
    payload = {
        "email": "EMP001",  # Using employee_id as email
        "password": "password123"  # Password is not validated yet
    }
    
    headers = {
        "Content-Type": "application/json"
    }
    
    try:
        print("\nTesting login with regular employee...")
        response = requests.post(url, json=payload, headers=headers)
        
        print(f"Status Code: {response.status_code}")
        print(f"Response: {json.dumps(response.json(), indent=2)}")
        
        if response.status_code == 200:
            print("✅ Employee login successful!")
            return response.json()
        else:
            print("❌ Employee login failed!")
            return None
            
    except Exception as e:
        print(f"❌ Error testing employee login: {e}")
        return None

def test_invalid_login():
    """Test login with invalid credentials"""
    url = "http://localhost:8085/api/auth/login"
    
    # Test with invalid user
    payload = {
        "email": "INVALID001",
        "password": "password123"
    }
    
    headers = {
        "Content-Type": "application/json"
    }
    
    try:
        print("\nTesting login with invalid credentials...")
        response = requests.post(url, json=payload, headers=headers)
        
        print(f"Status Code: {response.status_code}")
        print(f"Response: {json.dumps(response.json(), indent=2)}")
        
        if response.status_code == 401:
            print("✅ Invalid login correctly rejected!")
        else:
            print("❌ Invalid login should have been rejected!")
            
    except Exception as e:
        print(f"❌ Error testing invalid login: {e}")

if __name__ == "__main__":
    print("🧪 Testing HRMS Authentication Endpoints")
    print("=" * 50)
    
    # Test admin login
    admin_result = test_login()
    
    # Test employee login
    employee_result = test_employee_login()
    
    # Test invalid login
    test_invalid_login()
    
    print("\n" + "=" * 50)
    print("🎯 Authentication tests completed!")
    
    if admin_result:
        print(f"\n🔑 Admin Token: {admin_result.get('access_token', 'N/A')[:50]}...")
    
    if employee_result:
        print(f"🔑 Employee Token: {employee_result.get('access_token', 'N/A')[:50]}...")
