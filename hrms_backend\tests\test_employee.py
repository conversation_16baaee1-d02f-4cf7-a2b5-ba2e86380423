import pytest
from fastapi.testclient import TestClient
from uuid import uuid4


class TestEmployeeAPI:
    """Test cases for Employee API endpoints"""
    
    def test_create_employee(self, client: TestClient, auth_headers, sample_employee):
        """Test creating a new employee"""
        response = client.post(
            "/api/employees/",
            json=sample_employee,
            headers=auth_headers
        )
        
        assert response.status_code == 201
        data = response.json()
        assert data["email"] == sample_employee["email"]
        assert data["first_name"] == sample_employee["first_name"]
        assert data["last_name"] == sample_employee["last_name"]
        assert "id" in data
    
    def test_get_employees(self, client: TestClient, auth_headers):
        """Test getting list of employees"""
        response = client.get("/api/employees/", headers=auth_headers)
        
        assert response.status_code == 200
        data = response.json()
        assert "employees" in data
        assert "total" in data
        assert "skip" in data
        assert "limit" in data
    
    def test_get_employee_by_id(self, client: TestClient, auth_headers, sample_employee):
        """Test getting employee by ID"""
        # First create an employee
        create_response = client.post(
            "/api/employees/",
            json=sample_employee,
            headers=auth_headers
        )
        employee_id = create_response.json()["id"]
        
        # Then get the employee
        response = client.get(f"/api/employees/{employee_id}", headers=auth_headers)
        
        assert response.status_code == 200
        data = response.json()
        assert data["id"] == employee_id
        assert data["email"] == sample_employee["email"]
    
    def test_update_employee(self, client: TestClient, auth_headers, sample_employee):
        """Test updating an employee"""
        # First create an employee
        create_response = client.post(
            "/api/employees/",
            json=sample_employee,
            headers=auth_headers
        )
        employee_id = create_response.json()["id"]
        
        # Update the employee
        update_data = {"job_title": "Senior Software Engineer"}
        response = client.put(
            f"/api/employees/{employee_id}",
            json=update_data,
            headers=auth_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["job_title"] == "Senior Software Engineer"
    
    def test_get_employee_not_found(self, client: TestClient, auth_headers):
        """Test getting non-existent employee"""
        fake_id = str(uuid4())
        response = client.get(f"/api/employees/{fake_id}", headers=auth_headers)
        
        assert response.status_code == 404
    
    def test_create_employee_invalid_data(self, client: TestClient, auth_headers):
        """Test creating employee with invalid data"""
        invalid_data = {
            "email": "invalid-email",  # Invalid email format
            "first_name": "",  # Empty required field
        }
        
        response = client.post(
            "/api/employees/",
            json=invalid_data,
            headers=auth_headers
        )
        
        assert response.status_code == 422  # Validation error
    
    def test_search_employees(self, client: TestClient, auth_headers, sample_employee):
        """Test searching employees"""
        # First create an employee
        client.post("/api/employees/", json=sample_employee, headers=auth_headers)
        
        # Search for the employee
        response = client.get(
            "/api/employees/",
            params={"search": "John"},
            headers=auth_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert len(data["employees"]) > 0
        assert any(emp["first_name"] == "John" for emp in data["employees"])
    
    def test_filter_employees_by_department(self, client: TestClient, auth_headers, sample_employee):
        """Test filtering employees by department"""
        # First create an employee
        client.post("/api/employees/", json=sample_employee, headers=auth_headers)
        
        # Filter by department
        response = client.get(
            "/api/employees/",
            params={"department": "Engineering"},
            headers=auth_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert all(emp["department"] == "Engineering" for emp in data["employees"])
    
    def test_employee_pagination(self, client: TestClient, auth_headers, sample_employee):
        """Test employee list pagination"""
        # Create multiple employees
        for i in range(5):
            employee_data = sample_employee.copy()
            employee_data["email"] = f"test{i}@test.com"
            employee_data["employee_id"] = f"EMP00{i}"
            client.post("/api/employees/", json=employee_data, headers=auth_headers)
        
        # Test pagination
        response = client.get(
            "/api/employees/",
            params={"skip": 0, "limit": 3},
            headers=auth_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert len(data["employees"]) <= 3
        assert data["skip"] == 0
        assert data["limit"] == 3
    
    def test_unauthorized_access(self, client: TestClient, sample_employee):
        """Test accessing employee endpoints without authentication"""
        response = client.get("/api/employees/")
        assert response.status_code == 401
        
        response = client.post("/api/employees/", json=sample_employee)
        assert response.status_code == 401


class TestEmployeeProfile:
    """Test cases for Employee Profile endpoints"""
    
    def test_get_my_profile(self, client: TestClient, auth_headers):
        """Test getting current user's profile"""
        response = client.get("/api/employees/me", headers=auth_headers)
        
        assert response.status_code == 200
        data = response.json()
        assert "id" in data
        assert "email" in data
    
    def test_update_my_profile(self, client: TestClient, auth_headers):
        """Test updating current user's profile"""
        update_data = {
            "phone": "+1987654321",
            "emergency_contact_name": "Jane Doe",
            "emergency_contact_phone": "+1234567890"
        }
        
        response = client.put(
            "/api/employees/me",
            json=update_data,
            headers=auth_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["phone"] == update_data["phone"]
        assert data["emergency_contact_name"] == update_data["emergency_contact_name"]


class TestEmployeeDocuments:
    """Test cases for Employee Documents endpoints"""
    
    def test_upload_document(self, client: TestClient, auth_headers):
        """Test uploading employee document"""
        # This would typically involve file upload
        # For now, we'll test the endpoint structure
        response = client.get("/api/employees/me/documents", headers=auth_headers)
        
        # Should return empty list initially
        assert response.status_code == 200
        data = response.json()
        assert isinstance(data, list)
    
    def test_get_employee_documents(self, client: TestClient, auth_headers, sample_employee):
        """Test getting employee documents"""
        # First create an employee
        create_response = client.post(
            "/api/employees/",
            json=sample_employee,
            headers=auth_headers
        )
        employee_id = create_response.json()["id"]
        
        # Get documents
        response = client.get(
            f"/api/employees/{employee_id}/documents",
            headers=auth_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert isinstance(data, list)
