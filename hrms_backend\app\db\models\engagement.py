from sqlalchemy import Column, String, DateTime, Date, <PERSON><PERSON><PERSON>, <PERSON>ole<PERSON>, Text, Numeric, Integer, Enum
from sqlalchemy.dialects.postgresql import UUID, JSONB
from sqlalchemy.orm import relationship
from enum import Enum as PyEnum
from datetime import datetime, date
from typing import Optional

from ..base import BaseModel, AuditMixin


class SurveyStatus(PyEnum):
    DRAFT = "draft"
    ACTIVE = "active"
    PAUSED = "paused"
    COMPLETED = "completed"
    CANCELLED = "cancelled"


class SurveyType(PyEnum):
    ENGAGEMENT = "engagement"
    SATISFACTION = "satisfaction"
    PULSE = "pulse"
    EXIT = "exit"
    ONBOARDING = "onboarding"
    CUSTOM = "custom"


class QuestionType(PyEnum):
    MULTIPLE_CHOICE = "multiple_choice"
    RATING_SCALE = "rating_scale"
    TEXT = "text"
    YES_NO = "yes_no"
    RANKING = "ranking"
    MATRIX = "matrix"


class ResponseStatus(PyEnum):
    NOT_STARTED = "not_started"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    SKIPPED = "skipped"


class Survey(BaseModel, AuditMixin):
    """Employee survey model"""
    __tablename__ = "surveys"

    # Basic information
    title = Column(String(200), nullable=False)
    description = Column(Text, nullable=True)
    organization_id = Column(UUID(as_uuid=True), nullable=False, index=True)

    # Survey configuration
    survey_type = Column(Enum(SurveyType), nullable=False)
    status = Column(Enum(SurveyStatus), nullable=False, default=SurveyStatus.DRAFT)

    # Timeline
    start_date = Column(DateTime(timezone=True), nullable=False)
    end_date = Column(DateTime(timezone=True), nullable=False)

    # Participation settings
    is_anonymous = Column(Boolean, default=True)
    is_mandatory = Column(Boolean, default=False)
    allow_partial_responses = Column(Boolean, default=True)

    # Target audience
    target_all_employees = Column(Boolean, default=True)
    target_departments = Column(JSONB, nullable=True)  # Array of department IDs
    target_designations = Column(JSONB, nullable=True)  # Array of designation IDs
    target_employees = Column(JSONB, nullable=True)  # Array of employee IDs

    # Notifications
    send_reminders = Column(Boolean, default=True)
    reminder_frequency_days = Column(Integer, nullable=True, default=3)

    # Results
    show_results_to_participants = Column(Boolean, default=False)
    results_available_date = Column(DateTime(timezone=True), nullable=True)

    # Relationships
    questions = relationship("SurveyQuestion", back_populates="survey", order_by="SurveyQuestion.order_index")
    responses = relationship("SurveyResponse", back_populates="survey")


class SurveyQuestion(BaseModel, AuditMixin):
    """Survey question model"""
    __tablename__ = "survey_questions"

    survey_id = Column(UUID(as_uuid=True), ForeignKey("surveys.id"), nullable=False, index=True)

    # Question details
    question_text = Column(Text, nullable=False)
    question_type = Column(Enum(QuestionType), nullable=False)
    order_index = Column(Integer, nullable=False)

    # Question configuration
    is_required = Column(Boolean, default=False)
    options = Column(JSONB, nullable=True)  # Array of options for multiple choice
    rating_scale_min = Column(Integer, nullable=True)
    rating_scale_max = Column(Integer, nullable=True)
    rating_scale_labels = Column(JSONB, nullable=True)  # Array of scale labels

    # Conditional logic
    depends_on_question_id = Column(UUID(as_uuid=True), ForeignKey("survey_questions.id"), nullable=True)
    show_condition = Column(JSONB, nullable=True)  # Condition for showing this question

    # Categorization
    category = Column(String(100), nullable=True)
    tags = Column(JSONB, nullable=True)  # Array of tags

    # Relationships
    survey = relationship("Survey", back_populates="questions")
    depends_on_question = relationship("SurveyQuestion", remote_side="SurveyQuestion.id")
    answers = relationship("SurveyAnswer", back_populates="question")


class SurveyResponse(BaseModel, AuditMixin):
    """Survey response tracking"""
    __tablename__ = "survey_responses"

    survey_id = Column(UUID(as_uuid=True), ForeignKey("surveys.id"), nullable=False, index=True)
    employee_id = Column(UUID(as_uuid=True), ForeignKey("employees.id"), nullable=True, index=True)  # NULL for anonymous

    # Response details
    status = Column(Enum(ResponseStatus), nullable=False, default=ResponseStatus.NOT_STARTED)
    started_at = Column(DateTime(timezone=True), nullable=True)
    completed_at = Column(DateTime(timezone=True), nullable=True)

    # Progress tracking
    total_questions = Column(Integer, nullable=False)
    answered_questions = Column(Integer, nullable=False, default=0)
    progress_percentage = Column(Numeric(5, 2), nullable=False, default=0)

    # Session information
    ip_address = Column(String(45), nullable=True)
    user_agent = Column(Text, nullable=True)

    # Relationships
    survey = relationship("Survey", back_populates="responses")
    employee = relationship("Employee")
    answers = relationship("SurveyAnswer", back_populates="response")


class SurveyAnswer(BaseModel, AuditMixin):
    """Individual survey answer"""
    __tablename__ = "survey_answers"

    response_id = Column(UUID(as_uuid=True), ForeignKey("survey_responses.id"), nullable=False, index=True)
    question_id = Column(UUID(as_uuid=True), ForeignKey("survey_questions.id"), nullable=False, index=True)

    # Answer data
    answer_text = Column(Text, nullable=True)
    answer_number = Column(Numeric(10, 2), nullable=True)
    answer_options = Column(JSONB, nullable=True)  # Array of selected options

    # Metadata
    answered_at = Column(DateTime(timezone=True), nullable=False, default=datetime.utcnow)
    time_spent_seconds = Column(Integer, nullable=True)

    # Relationships
    response = relationship("SurveyResponse", back_populates="answers")
    question = relationship("SurveyQuestion", back_populates="answers")


class SurveyTemplate(BaseModel):
    """Survey template for reusable surveys"""
    __tablename__ = "survey_templates"

    name = Column(String(200), nullable=False)
    description = Column(Text, nullable=True)
    organization_id = Column(UUID(as_uuid=True), nullable=False, index=True)

    # Template configuration
    survey_type = Column(Enum(SurveyType), nullable=False)
    template_data = Column(JSONB, nullable=False)  # Survey structure and questions

    # Settings
    is_public = Column(Boolean, default=False)
    category = Column(String(100), nullable=True)

    # Usage tracking
    usage_count = Column(Integer, default=0)
    is_active = Column(Boolean, default=True)


class EngagementMetric(BaseModel, AuditMixin):
    """Engagement metrics and KPIs"""
    __tablename__ = "engagement_metrics"

    organization_id = Column(UUID(as_uuid=True), nullable=False, index=True)

    # Metric details
    metric_name = Column(String(200), nullable=False)
    metric_type = Column(String(50), nullable=False)  # enps, satisfaction, engagement_score

    # Time period
    period_start = Column(Date, nullable=False)
    period_end = Column(Date, nullable=False)

    # Metric values
    overall_score = Column(Numeric(5, 2), nullable=False)
    participation_rate = Column(Numeric(5, 2), nullable=True)
    response_count = Column(Integer, nullable=False)

    # Segmentation
    department_scores = Column(JSONB, nullable=True)  # Scores by department
    designation_scores = Column(JSONB, nullable=True)  # Scores by designation
    demographic_scores = Column(JSONB, nullable=True)  # Scores by demographics

    # Trends
    previous_score = Column(Numeric(5, 2), nullable=True)
    score_change = Column(Numeric(5, 2), nullable=True)
    trend_direction = Column(String(20), nullable=True)  # improving, declining, stable

    # Insights
    key_insights = Column(JSONB, nullable=True)  # Array of insight objects
    action_items = Column(JSONB, nullable=True)  # Array of recommended actions


class FeedbackRequest(BaseModel, AuditMixin):
    """360-degree feedback request"""
    __tablename__ = "feedback_requests"

    # Basic information
    title = Column(String(200), nullable=False)
    description = Column(Text, nullable=True)
    organization_id = Column(UUID(as_uuid=True), nullable=False, index=True)

    # Feedback subject and requester
    subject_employee_id = Column(UUID(as_uuid=True), ForeignKey("employees.id"), nullable=False)
    requested_by = Column(UUID(as_uuid=True), ForeignKey("employees.id"), nullable=False)

    # Feedback providers
    feedback_providers = Column(JSONB, nullable=False)  # Array of employee IDs

    # Configuration
    is_anonymous = Column(Boolean, default=True)
    feedback_template_id = Column(UUID(as_uuid=True), ForeignKey("feedback_templates.id"), nullable=True)

    # Timeline
    due_date = Column(Date, nullable=False)

    # Status
    status = Column(String(50), nullable=False, default="pending")
    completed_count = Column(Integer, nullable=False, default=0)
    total_requested = Column(Integer, nullable=False)

    # Relationships
    subject_employee = relationship("Employee", foreign_keys=[subject_employee_id])
    requester = relationship("Employee", foreign_keys=[requested_by])
    feedback_template = relationship("FeedbackTemplate")
    feedback_responses = relationship("FeedbackResponse", back_populates="feedback_request")


class FeedbackTemplate(BaseModel):
    """Feedback template"""
    __tablename__ = "feedback_templates"

    name = Column(String(200), nullable=False)
    description = Column(Text, nullable=True)
    organization_id = Column(UUID(as_uuid=True), nullable=False, index=True)

    # Template configuration
    template_data = Column(JSONB, nullable=False)  # Questions and structure

    # Settings
    is_active = Column(Boolean, default=True)
    category = Column(String(100), nullable=True)

    # Relationships
    feedback_requests = relationship("FeedbackRequest", back_populates="feedback_template")


class FeedbackResponse(BaseModel, AuditMixin):
    """360-degree feedback response"""
    __tablename__ = "feedback_responses"

    feedback_request_id = Column(UUID(as_uuid=True), ForeignKey("feedback_requests.id"), nullable=False)
    provider_id = Column(UUID(as_uuid=True), ForeignKey("employees.id"), nullable=True)  # NULL for anonymous

    # Response data
    feedback_data = Column(JSONB, nullable=False)  # Structured feedback responses

    # Completion
    completed_at = Column(DateTime(timezone=True), nullable=False, default=datetime.utcnow)

    # Relationships
    feedback_request = relationship("FeedbackRequest", back_populates="feedback_responses")
    provider = relationship("Employee", foreign_keys=[provider_id])


class EngagementAction(BaseModel, AuditMixin):
    """Engagement improvement actions"""
    __tablename__ = "engagement_actions"

    organization_id = Column(UUID(as_uuid=True), nullable=False, index=True)

    # Action details
    title = Column(String(200), nullable=False)
    description = Column(Text, nullable=False)
    category = Column(String(100), nullable=True)

    # Ownership
    owner_id = Column(UUID(as_uuid=True), ForeignKey("employees.id"), nullable=False)
    department_id = Column(UUID(as_uuid=True), ForeignKey("departments.id"), nullable=True)

    # Timeline
    start_date = Column(Date, nullable=False)
    target_completion_date = Column(Date, nullable=False)
    actual_completion_date = Column(Date, nullable=True)

    # Status
    status = Column(String(50), nullable=False, default="planned")
    progress_percentage = Column(Numeric(5, 2), nullable=False, default=0)

    # Impact tracking
    target_metrics = Column(JSONB, nullable=True)  # Target improvements
    actual_impact = Column(JSONB, nullable=True)  # Measured impact

    # Relationships
    owner = relationship("Employee", foreign_keys=[owner_id])
    department = relationship("Department")
