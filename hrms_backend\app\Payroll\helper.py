import secrets
from flask import url_for, request
import os

from dotenv import load_dotenv
load_dotenv()

def generate_reset_token(token_length=32):
    token = secrets.token_hex(token_length)
    return token

'''
    TODO => send an email to the user 
'''
def send_password_reset_email(email, message):
    pass

def get_base_url():
    base_url = request.url_root
    return base_url

def get_frontend_host():
    return os.getenv("FRONTEND_HOST")