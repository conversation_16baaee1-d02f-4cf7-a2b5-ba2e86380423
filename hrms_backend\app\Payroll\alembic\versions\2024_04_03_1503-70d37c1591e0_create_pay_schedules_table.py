"""create pay_schedules table

Revision ID: 70d37c1591e0
Revises: e5bbbe25fdb7
Create Date: 2024-04-03 15:03:41.558101

"""
from typing import Sequence, Union

from alembic import op
from sqlalchemy import func, TIMESTAMP, ForeignKey
from sqlalchemy import Column, Integer, String

# revision identifiers, used by Alembic.
revision: str = '70d37c1591e0'
down_revision: Union[str, None] = 'e5bbbe25fdb7'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    op.create_table(
        'pay_schedules',
        Column('id', Integer, primary_key=True),
        <PERSON><PERSON>n('name', String(45), nullable=False),
        <PERSON>umn('pay_date', TIMESTAMP, nullable=False),
        <PERSON>umn('clear_all', String(100), nullable=False),
        <PERSON>umn('number_of_employees', Integer, nullable=True),
        <PERSON>umn("user_id", Integer, Foreign<PERSON>ey("users.id")),
        <PERSON><PERSON>n('payment_based', String(100), nullable=True),
        <PERSON><PERSON><PERSON>("timestamp", TIMESTAMP, server_default=func.now()),
    )

def downgrade() -> None:
    op.drop_table("pay_schedules")
