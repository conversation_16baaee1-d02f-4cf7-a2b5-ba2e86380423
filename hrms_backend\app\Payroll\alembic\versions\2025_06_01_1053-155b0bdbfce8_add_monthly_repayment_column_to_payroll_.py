"""add monthly repayment column to payroll history table


Revision ID: 155b0bdbfce8
Revises: f1049b5ff644
Create Date: 2025-06-01 10:53:08.643867

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '155b0bdbfce8'
down_revision: Union[str, None] = 'f1049b5ff644'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    op.add_column('payroll_history', sa.Column('monthly_loan_repayment', sa.Float(), nullable=True, server_default="0.0"))

    


def downgrade() -> None:
    op.drop_column('payroll_history', 'monthly_loan_repayment')

