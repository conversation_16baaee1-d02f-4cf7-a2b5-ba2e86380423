from typing import Optional, Dict, Any, Union
from sqlalchemy.orm import Session
from fastapi import Request
from datetime import datetime
import json
import logging
from uuid import UUID

from ..db.models.user import AuditLog
from ..db.session import get_db
from .security import CurrentUser

logger = logging.getLogger(__name__)


class AuditLogger:
    """Comprehensive audit logging service"""

    @staticmethod
    def log_action(
        db: Session,
        action: str,
        resource_type: str,
        resource_id: Optional[str] = None,
        user: Optional[CurrentUser] = None,
        user_id: Optional[Union[str, UUID]] = None,
        user_email: Optional[str] = None,
        user_role: Optional[str] = None,
        old_values: Optional[Dict[str, Any]] = None,
        new_values: Optional[Dict[str, Any]] = None,
        changes: Optional[Dict[str, Any]] = None,
        request: Optional[Request] = None,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None,
        request_method: Optional[str] = None,
        request_url: Optional[str] = None,
        status: str = "SUCCESS",
        error_message: Optional[str] = None,
        organization_id: Optional[Union[str, UUID]] = None,
        session_id: Optional[str] = None,
        correlation_id: Optional[str] = None
    ) -> Optional[AuditLog]:
        """
        Log an audit event
        
        Args:
            db: Database session
            action: Action performed (CREATE, UPDATE, DELETE, LOGIN, etc.)
            resource_type: Type of resource (employees, leave_requests, etc.)
            resource_id: ID of the affected resource
            user: Current user object
            user_id: User ID (if user object not available)
            user_email: User email (if user object not available)
            user_role: User role (if user object not available)
            old_values: Previous values before change
            new_values: New values after change
            changes: Summary of what changed
            request: FastAPI request object
            ip_address: IP address (if request not available)
            user_agent: User agent (if request not available)
            request_method: HTTP method (if request not available)
            request_url: Request URL (if request not available)
            status: Action status (SUCCESS, FAILED, ERROR)
            error_message: Error message if status is FAILED or ERROR
            organization_id: Organization ID
            session_id: Session ID
            correlation_id: Correlation ID for tracking related actions
        """
        try:
            # Extract user information
            if user:
                user_id = user.user_id
                user_email = user.email
                user_role = user.role
                organization_id = organization_id or user.organization_id

            # Extract request information
            if request:
                ip_address = ip_address or (request.client.host if request.client else None)
                user_agent = user_agent or request.headers.get("user-agent")
                request_method = request_method or request.method
                request_url = request_url or str(request.url)

            # Create audit log entry
            audit_log = AuditLog(
                action=action,
                resource_type=resource_type,
                resource_id=str(resource_id) if resource_id else None,
                user_id=UUID(str(user_id)) if user_id else None,
                user_email=user_email,
                user_role=user_role,
                ip_address=ip_address,
                user_agent=user_agent,
                request_method=request_method,
                request_url=request_url,
                old_values=old_values,
                new_values=new_values,
                changes=changes,
                organization_id=UUID(str(organization_id)) if organization_id else None,
                session_id=session_id,
                correlation_id=correlation_id,
                status=status,
                error_message=error_message
            )

            db.add(audit_log)
            db.commit()
            
            return audit_log

        except Exception as e:
            logger.error(f"Failed to create audit log: {e}")
            db.rollback()
            return None

    @staticmethod
    def log_login_attempt(
        db: Session,
        email: str,
        success: bool,
        request: Optional[Request] = None,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None,
        error_message: Optional[str] = None,
        user_id: Optional[Union[str, UUID]] = None,
        two_fa_used: bool = False
    ) -> Optional[AuditLog]:
        """Log login attempt"""
        action = "LOGIN_SUCCESS" if success else "LOGIN_FAILED"
        if two_fa_used:
            action = "LOGIN_2FA_SUCCESS" if success else "LOGIN_2FA_FAILED"
        
        return AuditLogger.log_action(
            db=db,
            action=action,
            resource_type="authentication",
            user_id=user_id,
            user_email=email,
            request=request,
            ip_address=ip_address,
            user_agent=user_agent,
            status="SUCCESS" if success else "FAILED",
            error_message=error_message,
            new_values={"two_fa_used": two_fa_used} if two_fa_used else None
        )

    @staticmethod
    def log_logout(
        db: Session,
        user: CurrentUser,
        request: Optional[Request] = None,
        logout_all_devices: bool = False
    ) -> Optional[AuditLog]:
        """Log logout event"""
        return AuditLogger.log_action(
            db=db,
            action="LOGOUT",
            resource_type="authentication",
            user=user,
            request=request,
            new_values={"logout_all_devices": logout_all_devices}
        )

    @staticmethod
    def log_password_change(
        db: Session,
        user: CurrentUser,
        request: Optional[Request] = None,
        forced: bool = False
    ) -> Optional[AuditLog]:
        """Log password change"""
        action = "PASSWORD_FORCED_CHANGE" if forced else "PASSWORD_CHANGE"
        return AuditLogger.log_action(
            db=db,
            action=action,
            resource_type="authentication",
            user=user,
            request=request,
            new_values={"forced": forced}
        )

    @staticmethod
    def log_2fa_setup(
        db: Session,
        user: CurrentUser,
        request: Optional[Request] = None
    ) -> Optional[AuditLog]:
        """Log 2FA setup"""
        return AuditLogger.log_action(
            db=db,
            action="2FA_SETUP",
            resource_type="authentication",
            user=user,
            request=request
        )

    @staticmethod
    def log_2fa_enable(
        db: Session,
        user: CurrentUser,
        request: Optional[Request] = None
    ) -> Optional[AuditLog]:
        """Log 2FA enable"""
        return AuditLogger.log_action(
            db=db,
            action="2FA_ENABLED",
            resource_type="authentication",
            user=user,
            request=request
        )

    @staticmethod
    def log_2fa_disable(
        db: Session,
        user: CurrentUser,
        request: Optional[Request] = None
    ) -> Optional[AuditLog]:
        """Log 2FA disable"""
        return AuditLogger.log_action(
            db=db,
            action="2FA_DISABLED",
            resource_type="authentication",
            user=user,
            request=request
        )

    @staticmethod
    def log_employee_create(
        db: Session,
        employee_id: str,
        employee_data: Dict[str, Any],
        user: CurrentUser,
        request: Optional[Request] = None
    ) -> Optional[AuditLog]:
        """Log employee creation"""
        return AuditLogger.log_action(
            db=db,
            action="CREATE",
            resource_type="employees",
            resource_id=employee_id,
            user=user,
            request=request,
            new_values=employee_data
        )

    @staticmethod
    def log_employee_update(
        db: Session,
        employee_id: str,
        old_data: Dict[str, Any],
        new_data: Dict[str, Any],
        changes: Dict[str, Any],
        user: CurrentUser,
        request: Optional[Request] = None
    ) -> Optional[AuditLog]:
        """Log employee update"""
        return AuditLogger.log_action(
            db=db,
            action="UPDATE",
            resource_type="employees",
            resource_id=employee_id,
            user=user,
            request=request,
            old_values=old_data,
            new_values=new_data,
            changes=changes
        )

    @staticmethod
    def log_employee_delete(
        db: Session,
        employee_id: str,
        employee_data: Dict[str, Any],
        user: CurrentUser,
        request: Optional[Request] = None
    ) -> Optional[AuditLog]:
        """Log employee deletion"""
        return AuditLogger.log_action(
            db=db,
            action="DELETE",
            resource_type="employees",
            resource_id=employee_id,
            user=user,
            request=request,
            old_values=employee_data
        )

    @staticmethod
    def log_leave_request(
        db: Session,
        leave_request_id: str,
        leave_data: Dict[str, Any],
        user: CurrentUser,
        request: Optional[Request] = None
    ) -> Optional[AuditLog]:
        """Log leave request submission"""
        return AuditLogger.log_action(
            db=db,
            action="CREATE",
            resource_type="leave_requests",
            resource_id=leave_request_id,
            user=user,
            request=request,
            new_values=leave_data
        )

    @staticmethod
    def log_leave_approval(
        db: Session,
        leave_request_id: str,
        old_status: str,
        new_status: str,
        approver: CurrentUser,
        request: Optional[Request] = None,
        comments: Optional[str] = None
    ) -> Optional[AuditLog]:
        """Log leave request approval/rejection"""
        action = "APPROVE" if new_status == "approved" else "REJECT"
        return AuditLogger.log_action(
            db=db,
            action=action,
            resource_type="leave_requests",
            resource_id=leave_request_id,
            user=approver,
            request=request,
            old_values={"status": old_status},
            new_values={"status": new_status, "comments": comments},
            changes={"status": f"{old_status} -> {new_status}"}
        )

    @staticmethod
    def log_data_access(
        db: Session,
        resource_type: str,
        resource_id: Optional[str] = None,
        user: Optional[CurrentUser] = None,
        request: Optional[Request] = None,
        query_params: Optional[Dict[str, Any]] = None
    ) -> Optional[AuditLog]:
        """Log data access for GDPR compliance"""
        return AuditLogger.log_action(
            db=db,
            action="READ",
            resource_type=resource_type,
            resource_id=resource_id,
            user=user,
            request=request,
            new_values={"query_params": query_params} if query_params else None
        )

    @staticmethod
    def log_data_export(
        db: Session,
        resource_type: str,
        export_format: str,
        record_count: int,
        user: CurrentUser,
        request: Optional[Request] = None,
        filters: Optional[Dict[str, Any]] = None
    ) -> Optional[AuditLog]:
        """Log data export"""
        return AuditLogger.log_action(
            db=db,
            action="EXPORT",
            resource_type=resource_type,
            user=user,
            request=request,
            new_values={
                "export_format": export_format,
                "record_count": record_count,
                "filters": filters
            }
        )

    @staticmethod
    def log_permission_change(
        db: Session,
        target_user_id: str,
        old_role: str,
        new_role: str,
        admin_user: CurrentUser,
        request: Optional[Request] = None
    ) -> Optional[AuditLog]:
        """Log permission/role changes"""
        return AuditLogger.log_action(
            db=db,
            action="PERMISSION_CHANGE",
            resource_type="users",
            resource_id=target_user_id,
            user=admin_user,
            request=request,
            old_values={"role": old_role},
            new_values={"role": new_role},
            changes={"role": f"{old_role} -> {new_role}"}
        )

    @staticmethod
    def log_system_event(
        db: Session,
        event_type: str,
        description: str,
        details: Optional[Dict[str, Any]] = None,
        status: str = "SUCCESS",
        error_message: Optional[str] = None
    ) -> Optional[AuditLog]:
        """Log system events"""
        return AuditLogger.log_action(
            db=db,
            action=event_type,
            resource_type="system",
            new_values={"description": description, "details": details},
            status=status,
            error_message=error_message
        )
