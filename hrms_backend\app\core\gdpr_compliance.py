from typing import Dict, List, Optional, Any, Tuple
from sqlalchemy.orm import Session
from sqlalchemy import text, and_, or_
from datetime import datetime, timedelta, date
from uuid import UUID
import logging
import json
from enum import Enum

from ..db.models.user import User, AuditLog
from ..db.models.employee import Employee
from .encryption import field_encryption, PII_FIELDS, SENSITIVE_FIELDS
from .audit_logger import AuditLogger

logger = logging.getLogger(__name__)


class DataProcessingPurpose(str, Enum):
    """GDPR data processing purposes"""
    EMPLOYMENT = "employment"
    PAYROLL = "payroll"
    BENEFITS = "benefits"
    PERFORMANCE = "performance"
    TRAINING = "training"
    COMPLIANCE = "compliance"
    SECURITY = "security"
    MARKETING = "marketing"
    ANALYTICS = "analytics"


class ConsentStatus(str, Enum):
    """Consent status for data processing"""
    GIVEN = "given"
    WITHDRAWN = "withdrawn"
    PENDING = "pending"
    EXPIRED = "expired"


class DataSubjectRights(str, Enum):
    """GDPR data subject rights"""
    ACCESS = "access"  # Right to access
    RECTIFICATION = "rectification"  # Right to rectification
    ERASURE = "erasure"  # Right to erasure (right to be forgotten)
    RESTRICT_PROCESSING = "restrict_processing"  # Right to restrict processing
    DATA_PORTABILITY = "data_portability"  # Right to data portability
    OBJECT = "object"  # Right to object
    AUTOMATED_DECISION = "automated_decision"  # Rights related to automated decision making


class GDPRComplianceManager:
    """GDPR compliance manager for data protection"""

    def __init__(self):
        self.retention_periods = {
            "employee_data": 2555,  # 7 years in days
            "payroll_data": 2555,   # 7 years in days
            "performance_data": 1825,  # 5 years in days
            "training_data": 1095,  # 3 years in days
            "audit_logs": 2555,    # 7 years in days
            "recruitment_data": 365,  # 1 year in days
            "marketing_data": 1095,  # 3 years in days
        }

    async def handle_data_subject_request(
        self,
        db: Session,
        employee_id: UUID,
        request_type: DataSubjectRights,
        current_user_id: UUID,
        additional_data: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Handle GDPR data subject requests"""
        try:
            # Verify employee exists
            employee = db.query(Employee).filter(Employee.id == employee_id).first()
            if not employee:
                raise ValueError("Employee not found")

            # Log the request
            AuditLogger.log_action(
                db=db,
                action=f"GDPR_{request_type.value.upper()}",
                resource_type="employee_data",
                resource_id=str(employee_id),
                user_id=current_user_id,
                new_values={
                    "request_type": request_type.value,
                    "additional_data": additional_data
                }
            )

            # Handle different types of requests
            if request_type == DataSubjectRights.ACCESS:
                return await self._handle_access_request(db, employee_id)
            
            elif request_type == DataSubjectRights.RECTIFICATION:
                return await self._handle_rectification_request(db, employee_id, additional_data)
            
            elif request_type == DataSubjectRights.ERASURE:
                return await self._handle_erasure_request(db, employee_id, current_user_id)
            
            elif request_type == DataSubjectRights.RESTRICT_PROCESSING:
                return await self._handle_restrict_processing_request(db, employee_id)
            
            elif request_type == DataSubjectRights.DATA_PORTABILITY:
                return await self._handle_data_portability_request(db, employee_id)
            
            elif request_type == DataSubjectRights.OBJECT:
                return await self._handle_objection_request(db, employee_id, additional_data)
            
            else:
                raise ValueError(f"Unsupported request type: {request_type}")

        except Exception as e:
            logger.error(f"Error handling GDPR request: {e}")
            raise

    async def _handle_access_request(self, db: Session, employee_id: UUID) -> Dict[str, Any]:
        """Handle right to access request - provide all personal data"""
        try:
            # Get employee data
            employee = db.query(Employee).filter(Employee.id == employee_id).first()
            
            # Collect all personal data from various tables
            personal_data = {
                "employee_profile": await self._get_employee_profile_data(db, employee_id),
                "employment_history": await self._get_employment_history(db, employee_id),
                "payroll_data": await self._get_payroll_data(db, employee_id),
                "performance_data": await self._get_performance_data(db, employee_id),
                "training_data": await self._get_training_data(db, employee_id),
                "leave_data": await self._get_leave_data(db, employee_id),
                "attendance_data": await self._get_attendance_data(db, employee_id),
                "audit_trail": await self._get_audit_trail(db, employee_id),
                "consent_records": await self._get_consent_records(db, employee_id),
            }

            # Decrypt sensitive fields for the data subject
            for category, data in personal_data.items():
                if isinstance(data, list):
                    for item in data:
                        if isinstance(item, dict):
                            personal_data[category] = [
                                field_encryption.decrypt_sensitive_data(item, SENSITIVE_FIELDS)
                                for item in data
                            ]
                elif isinstance(data, dict):
                    personal_data[category] = field_encryption.decrypt_sensitive_data(data, SENSITIVE_FIELDS)

            return {
                "status": "completed",
                "data": personal_data,
                "generated_at": datetime.utcnow().isoformat(),
                "retention_info": await self._get_retention_information(db, employee_id)
            }

        except Exception as e:
            logger.error(f"Error handling access request: {e}")
            raise

    async def _handle_erasure_request(self, db: Session, employee_id: UUID, current_user_id: UUID) -> Dict[str, Any]:
        """Handle right to erasure (right to be forgotten)"""
        try:
            # Check if erasure is legally possible
            legal_obligations = await self._check_legal_obligations(db, employee_id)
            if legal_obligations:
                return {
                    "status": "rejected",
                    "reason": "Cannot erase data due to legal obligations",
                    "legal_obligations": legal_obligations
                }

            # Perform anonymization instead of deletion for audit trail
            anonymized_data = await self._anonymize_employee_data(db, employee_id, current_user_id)

            return {
                "status": "completed",
                "action": "anonymized",
                "anonymized_records": anonymized_data,
                "completed_at": datetime.utcnow().isoformat()
            }

        except Exception as e:
            logger.error(f"Error handling erasure request: {e}")
            raise

    async def _handle_data_portability_request(self, db: Session, employee_id: UUID) -> Dict[str, Any]:
        """Handle right to data portability - provide data in structured format"""
        try:
            # Get structured data for portability
            portable_data = await self._get_portable_data(db, employee_id)

            return {
                "status": "completed",
                "format": "JSON",
                "data": portable_data,
                "generated_at": datetime.utcnow().isoformat(),
                "instructions": "This data is provided in a structured, commonly used format for portability."
            }

        except Exception as e:
            logger.error(f"Error handling data portability request: {e}")
            raise

    async def check_data_retention_compliance(self, db: Session) -> Dict[str, Any]:
        """Check and enforce data retention policies"""
        try:
            compliance_report = {
                "checked_at": datetime.utcnow().isoformat(),
                "expired_data": {},
                "actions_taken": []
            }

            # Check each data category
            for data_type, retention_days in self.retention_periods.items():
                cutoff_date = datetime.utcnow() - timedelta(days=retention_days)
                
                expired_records = await self._find_expired_records(db, data_type, cutoff_date)
                if expired_records:
                    compliance_report["expired_data"][data_type] = len(expired_records)
                    
                    # Anonymize or delete expired records
                    for record_id in expired_records:
                        await self._handle_expired_record(db, data_type, record_id)
                        compliance_report["actions_taken"].append({
                            "data_type": data_type,
                            "record_id": str(record_id),
                            "action": "anonymized",
                            "date": datetime.utcnow().isoformat()
                        })

            return compliance_report

        except Exception as e:
            logger.error(f"Error checking data retention compliance: {e}")
            raise

    async def manage_consent(
        self,
        db: Session,
        employee_id: UUID,
        purpose: DataProcessingPurpose,
        consent_status: ConsentStatus,
        current_user_id: UUID
    ) -> Dict[str, Any]:
        """Manage consent for data processing"""
        try:
            # Record consent change
            consent_record = {
                "employee_id": str(employee_id),
                "purpose": purpose.value,
                "status": consent_status.value,
                "timestamp": datetime.utcnow().isoformat(),
                "recorded_by": str(current_user_id)
            }

            # Store consent record (this would be in a dedicated consent table)
            # For now, we'll log it in audit logs
            AuditLogger.log_action(
                db=db,
                action="CONSENT_CHANGE",
                resource_type="consent_management",
                resource_id=str(employee_id),
                user_id=current_user_id,
                new_values=consent_record
            )

            # If consent is withdrawn, restrict processing
            if consent_status == ConsentStatus.WITHDRAWN:
                await self._restrict_data_processing(db, employee_id, purpose)

            return {
                "status": "recorded",
                "consent_record": consent_record
            }

        except Exception as e:
            logger.error(f"Error managing consent: {e}")
            raise

    async def generate_privacy_impact_assessment(
        self,
        processing_activity: str,
        data_types: List[str],
        purposes: List[DataProcessingPurpose],
        recipients: List[str]
    ) -> Dict[str, Any]:
        """Generate Privacy Impact Assessment (PIA)"""
        try:
            # Calculate risk score based on data sensitivity
            risk_score = await self._calculate_privacy_risk(data_types, purposes, recipients)
            
            pia = {
                "assessment_id": f"PIA-{datetime.utcnow().strftime('%Y%m%d-%H%M%S')}",
                "processing_activity": processing_activity,
                "data_types": data_types,
                "purposes": [p.value for p in purposes],
                "recipients": recipients,
                "risk_assessment": {
                    "overall_risk": risk_score,
                    "risk_level": self._get_risk_level(risk_score),
                    "mitigation_measures": await self._get_mitigation_measures(risk_score)
                },
                "legal_basis": await self._determine_legal_basis(purposes),
                "retention_period": await self._determine_retention_period(data_types),
                "generated_at": datetime.utcnow().isoformat()
            }

            return pia

        except Exception as e:
            logger.error(f"Error generating PIA: {e}")
            raise

    async def _anonymize_employee_data(self, db: Session, employee_id: UUID, current_user_id: UUID) -> Dict[str, int]:
        """Anonymize employee data while preserving statistical value"""
        try:
            anonymized_count = {}

            # Anonymize personal identifiers
            employee = db.query(Employee).filter(Employee.id == employee_id).first()
            if employee:
                # Replace with anonymized values
                employee.first_name = f"ANON_{employee_id.hex[:8]}"
                employee.last_name = "ANONYMIZED"
                employee.email = f"anonymized_{employee_id.hex[:8]}@example.com"
                employee.phone = None
                employee.address = None
                employee.emergency_contact_name = None
                employee.emergency_contact_phone = None
                
                anonymized_count["employee_records"] = 1

            # Anonymize related data in other tables
            # This would include payroll, performance, etc.
            
            db.commit()

            # Log anonymization
            AuditLogger.log_action(
                db=db,
                action="DATA_ANONYMIZED",
                resource_type="employee_data",
                resource_id=str(employee_id),
                user_id=current_user_id,
                new_values={"anonymized_records": anonymized_count}
            )

            return anonymized_count

        except Exception as e:
            logger.error(f"Error anonymizing employee data: {e}")
            db.rollback()
            raise

    async def _get_employee_profile_data(self, db: Session, employee_id: UUID) -> Dict[str, Any]:
        """Get employee profile data"""
        employee = db.query(Employee).filter(Employee.id == employee_id).first()
        if not employee:
            return {}

        return {
            "id": str(employee.id),
            "employee_id": employee.employee_id,
            "first_name": employee.first_name,
            "last_name": employee.last_name,
            "email": employee.email,
            "phone": employee.phone,
            "date_of_birth": employee.date_of_birth.isoformat() if employee.date_of_birth else None,
            "hire_date": employee.hire_date.isoformat() if employee.hire_date else None,
            "created_at": employee.created_at.isoformat(),
            "updated_at": employee.updated_at.isoformat()
        }

    async def _check_legal_obligations(self, db: Session, employee_id: UUID) -> List[str]:
        """Check if there are legal obligations preventing data erasure"""
        obligations = []
        
        # Check for ongoing legal proceedings
        # Check for tax/audit requirements
        # Check for employment law requirements
        
        # This would be implemented based on specific legal requirements
        return obligations

    async def _calculate_privacy_risk(
        self, data_types: List[str], purposes: List[DataProcessingPurpose], recipients: List[str]
    ) -> float:
        """Calculate privacy risk score (0-100)"""
        risk_score = 0.0
        
        # Risk based on data sensitivity
        sensitive_data_types = ["ssn", "medical", "financial", "biometric"]
        for data_type in data_types:
            if any(sensitive in data_type.lower() for sensitive in sensitive_data_types):
                risk_score += 20
        
        # Risk based on processing purpose
        high_risk_purposes = [DataProcessingPurpose.MARKETING, DataProcessingPurpose.ANALYTICS]
        for purpose in purposes:
            if purpose in high_risk_purposes:
                risk_score += 15
        
        # Risk based on data sharing
        if len(recipients) > 2:
            risk_score += 10
        
        return min(risk_score, 100.0)

    def _get_risk_level(self, risk_score: float) -> str:
        """Get risk level based on score"""
        if risk_score >= 70:
            return "HIGH"
        elif risk_score >= 40:
            return "MEDIUM"
        else:
            return "LOW"

    async def _get_mitigation_measures(self, risk_score: float) -> List[str]:
        """Get recommended mitigation measures"""
        measures = ["Data encryption at rest and in transit", "Access controls and authentication"]
        
        if risk_score >= 40:
            measures.extend([
                "Regular security audits",
                "Data minimization practices",
                "Pseudonymization where possible"
            ])
        
        if risk_score >= 70:
            measures.extend([
                "Enhanced monitoring and logging",
                "Data Protection Impact Assessment",
                "Regular penetration testing",
                "Staff training on data protection"
            ])
        
        return measures

    # Additional helper methods would be implemented here
    async def _get_employment_history(self, db: Session, employee_id: UUID) -> List[Dict[str, Any]]:
        return []

    async def _get_payroll_data(self, db: Session, employee_id: UUID) -> List[Dict[str, Any]]:
        return []

    async def _get_performance_data(self, db: Session, employee_id: UUID) -> List[Dict[str, Any]]:
        return []

    async def _get_training_data(self, db: Session, employee_id: UUID) -> List[Dict[str, Any]]:
        return []

    async def _get_leave_data(self, db: Session, employee_id: UUID) -> List[Dict[str, Any]]:
        return []

    async def _get_attendance_data(self, db: Session, employee_id: UUID) -> List[Dict[str, Any]]:
        return []

    async def _get_audit_trail(self, db: Session, employee_id: UUID) -> List[Dict[str, Any]]:
        return []

    async def _get_consent_records(self, db: Session, employee_id: UUID) -> List[Dict[str, Any]]:
        return []

    async def _get_retention_information(self, db: Session, employee_id: UUID) -> Dict[str, Any]:
        return {}

    async def _get_portable_data(self, db: Session, employee_id: UUID) -> Dict[str, Any]:
        return {}

    async def _find_expired_records(self, db: Session, data_type: str, cutoff_date: datetime) -> List[UUID]:
        return []

    async def _handle_expired_record(self, db: Session, data_type: str, record_id: UUID):
        pass

    async def _restrict_data_processing(self, db: Session, employee_id: UUID, purpose: DataProcessingPurpose):
        pass

    async def _determine_legal_basis(self, purposes: List[DataProcessingPurpose]) -> List[str]:
        return ["Legitimate interest", "Contract performance"]

    async def _determine_retention_period(self, data_types: List[str]) -> str:
        return "7 years"

    async def _handle_rectification_request(self, db: Session, employee_id: UUID, additional_data: Optional[Dict[str, Any]]) -> Dict[str, Any]:
        return {"status": "completed"}

    async def _handle_restrict_processing_request(self, db: Session, employee_id: UUID) -> Dict[str, Any]:
        return {"status": "completed"}

    async def _handle_objection_request(self, db: Session, employee_id: UUID, additional_data: Optional[Dict[str, Any]]) -> Dict[str, Any]:
        return {"status": "completed"}


# Global GDPR compliance manager instance
gdpr_manager = GDPRComplianceManager()
