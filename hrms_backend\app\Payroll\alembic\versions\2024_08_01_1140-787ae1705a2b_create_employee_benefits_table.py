"""Create employee benefits table

Revision ID: 787ae1705a2b
Revises: 6803367aa4ba
Create Date: 2024-08-01 11:40:12.834699

"""
from typing import Sequence, Union

from alembic import op
from sqlalchemy import func, TIMESTAMP, Foreign<PERSON>ey
from sqlalchemy import <PERSON>umn, Integer, String


# revision identifiers, used by Alembic.
revision: str = '787ae1705a2b'
down_revision: Union[str, None] = '6803367aa4ba'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    op.create_table(
        'employee_benefits_pivot',
        Column('id', Integer, primary_key=True),
        <PERSON>umn('employee_id', Integer, ForeignKey('employees.id')),
        Column('benefits_id', Integer, ForeignKey('benefits.id'))
    )

def downgrade() -> None:
    op.drop_table("employee_benefits_pivot")
