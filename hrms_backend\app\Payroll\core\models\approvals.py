from core.databases.database import db
from core.models.basemodel import ModelBase
from datetime import datetime

class ApprovalModel(ModelBase):
    __tablename__ = "approvals"

    id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    role = db.Column(db.String(50), nullable=False)
    level = db.Column(db.String(50), nullable=False)
    employee_id = db.Column(db.Integer, db.ForeignKey((ModelBase.dbSchema() + '.employees.id')), nullable=False) 
    user_id = db.Column(db.Integer, db.<PERSON>Key((ModelBase.dbSchema() + '.users.id')), nullable=False) 
    # owner = db.relationship('UserModel', backref='approvals', lazy=True)
    timestamp = db.Column(db.DateTime, nullable=False, default=datetime.now())
    pay_schedules_id = db.Column(db.<PERSON><PERSON><PERSON>, db.<PERSON><PERSON>((ModelBase.dbSchema() + '.pay_schedules.id')), nullable=True)
    name = db.Column(db.String(150), nullable=True)
    email = db.Column(db.String(150), nullable=True)
    phone = db.Column(db.String(50), nullable=True)
    
    approval_histories = db.relationship("ApprovalHistoryModel", cascade="all, delete", backref="approval")
    user = db.relationship("UserModel", backref="users", lazy="joined")
