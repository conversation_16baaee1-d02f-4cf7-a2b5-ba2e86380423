#!/usr/bin/env python3
"""
Debug employee organization assignment
"""

import psycopg2
import os
from dotenv import load_dotenv

load_dotenv()

def debug_employee_org():
    """Check employee organization assignments"""
    
    conn = psycopg2.connect(
        host=os.getenv("DB_HOST", "localhost"),
        database=os.getenv("DB_NAME", "hrms_db"),
        user=os.getenv("DB_USER", "postgres"),
        password=os.getenv("DB_PASSWORD", "password"),
        port=os.getenv("DB_PORT", "5432")
    )
    
    try:
        cursor = conn.cursor()
        
        # Check if there are employee records for our user
        cursor.execute("""
            SELECT e.email, e.organization_id, u.organization_id as user_org_id
            FROM employees e
            LEFT JOIN users u ON e.email = u.email
            WHERE e.email = '<EMAIL>' OR u.email = '<EMAIL>'
        """)
        
        records = cursor.fetchall()
        print("Employee and User <NAME_EMAIL>:")
        for record in records:
            print(f"  Email: {record[0]}")
            print(f"  Employee org_id: {record[1]}")
            print(f"  User org_id: {record[2]}")
            print()
        
        # Check what organization has the ID from the token
        token_org_id = "259cccee-69a0-4e4e-8475-1dcc660462ce"
        cursor.execute("SELECT name FROM organizations WHERE id = %s", (token_org_id,))
        org_result = cursor.fetchone()
        if org_result:
            print(f"Token organization: {org_result[0]} (ID: {token_org_id})")
        else:
            print(f"Token organization ID {token_org_id} not found in organizations table!")
        
        # Check if this organization has leave policies
        cursor.execute("""
            SELECT COUNT(*) FROM leave_policies 
            WHERE organization_id = %s AND is_active = true
        """, (token_org_id,))
        policy_count = cursor.fetchone()[0]
        print(f"This organization has {policy_count} leave policies")
        
        if policy_count == 0:
            print("❌ This explains why we get 0 policies!")
            print("Need to either:")
            print("1. Update employee organization_id to match an org with policies")
            print("2. Create policies for this organization")
            
            # Option 1: Update employee organization
            cursor.execute("""
                SELECT organization_id, COUNT(*) as policy_count
                FROM leave_policies 
                WHERE is_active = true
                GROUP BY organization_id
                ORDER BY policy_count DESC
                LIMIT 1
            """)
            
            best_org = cursor.fetchone()
            if best_org:
                best_org_id = best_org[0]
                cursor.execute("SELECT name FROM organizations WHERE id = %s", (best_org_id,))
                best_org_name = cursor.fetchone()[0]
                
                print(f"\nRecommendation: Update employee to organization '{best_org_name}' (ID: {best_org_id})")
                print("This organization has the most leave policies.")
                
                # Update employee organization
                cursor.execute("""
                    UPDATE employees 
                    SET organization_id = %s 
                    WHERE email = '<EMAIL>'
                """, (best_org_id,))
                
                conn.commit()
                print("✅ Updated employee organization!")
        
    except Exception as e:
        print(f"Error: {e}")
    finally:
        cursor.close()
        conn.close()

if __name__ == "__main__":
    debug_employee_org()
