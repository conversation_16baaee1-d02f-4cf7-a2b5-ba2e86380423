import cloudinary.uploader
import cloudinary
import cloudinary.api
from dotenv import load_dotenv
import os


class CloudinaryService:
    def __init__(self):
        self.folder = "organisation_logos"
       

 

    def upload_image(self, file, allowed_formats=["jpg", "jpeg", "png", "svg"]):
        """
        Uploads an image to Cloudinary.
        Args:
            file: The image file to be uploaded.
            allowed_formats: List of allowed image formats.
        Returns:
            dict: The Cloudinary upload result, including the secure URL.
        Raises:
            Exception: If the upload fails.
        """
        print(f"File to go Cloudinary: {file}")
        if not hasattr(file, "read"):
            raise ValueError("Invalid file object provided. Expected a file-like object.")
        
        try:
            upload_result = cloudinary.uploader.upload(
                file,
                folder=self.folder,
                resource_type="image",
                allowed_formats=allowed_formats,
            )
            return upload_result
        except Exception as e:
            raise Exception(f"Failed to upload image: {e}")

    def delete_image(self, url):
        """
        Deletes an image from Cloudinary.
        Args:
            url: The URL of the image to be deleted.
        Returns:
            dict: The Cloudinary deletion result.
        Raises:
            Exception: If the deletion fails.
        """
        try:
            # Extract the public ID from the URL
            public_id = url.split("/")[-1].split(".")[0]
            public_id_with_folder = f"{self.folder}/{public_id}"
            delete_result = cloudinary.uploader.destroy(public_id_with_folder)
            return delete_result
        except Exception as e:
            raise Exception(f"Failed to delete image: {e}")
