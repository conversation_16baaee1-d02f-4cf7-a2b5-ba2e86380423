from datetime import datetime
from core.repositories.loan_request import LoanRequestRepository

class LoanRequestService:
    def __init__(self) -> None:
        self.repository = LoanRequestRepository()

    def createLoanRequest(self, Kwargs):
        # Inject disbursed_at based on logic
        if Kwargs.get("status") == "disbursed":
            Kwargs["disbursed_at"] = datetime.utcnow()
        else:
            Kwargs["disbursed_at"] = None
        
        return self.repository.createLoanRequest(**Kwargs)
    def fetchAll(self):
        loan_requests = self.repository.fetchAll()
        total_requests = len(loan_requests)
        return loan_requests, total_requests

    def getLoanRequests(self):
        return self.repository.getLoanRequests()
    
    def updateEmployeePendingLoan(self, employee_id, update_data):
        return self.repository.updatePendingLoanByEmployee(employee_id, update_data)
    
    def updateLoanApprovalStatus(self, loan_id, data):
        return self.repository.updateLoanApprovalStatus(loan_id, data)

    
    def getLoanRequestsByKey(self, Kwarg):
        return self.repository.getLoanRequestsByKeys(Kwarg)
    
    def deleteLoanRequests(self, id):
        return self.repository.deleteLoanRequests(id)