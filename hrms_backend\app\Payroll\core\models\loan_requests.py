from core.databases.database import db
from core.models.basemodel import ModelBase
from datetime import datetime

class LoanRequestModel(ModelBase):
    __tablename__ = "loan_request"

    id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    month = db.Column(db.String(45), nullable=False)
    timestamp = db.Column(db.DateTime, nullable=False, default=datetime.now()) 
    principal = db.Column(db.Float, nullable=False)
    date = db.Column(db.String(50), nullable=True)
    interest = db.Column(db.Float, nullable=False)
    status = db.Column(db.String(45), nullable=False)
    duration = db.Column(db.String(45), nullable=False)
    monthly_repayment = db.Column(db.Float, nullable=False)
    total_repayment = db.Column(db.Float, nullable=False)
    user_id = db.Column(db.Integer, db.<PERSON>ey((ModelBase.dbSchema() + '.users.id')), nullable=False) 
    disbursed_at = db.Column(db.DateTime, nullable=True)
    balance = db.Column(db.Float, nullable=True)
    employee_id = db.Column(db.Integer, db.ForeignKey((ModelBase.dbSchema() + '.employees.id')), nullable=False)


