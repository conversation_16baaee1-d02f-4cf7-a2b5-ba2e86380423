from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, func, case
from typing import Optional, List, Dict, Any
from uuid import UUID
from datetime import datetime, date, timedelta
from fastapi import HTTPException, status
import logging

from ...db.models.project import Project, Task, ProjectMilestone, ProjectAssignment
from ...db.models.kanban import KanbanBoard, KanbanCard, KanbanColumn
from ...db.models.employee import Employee
from ...schemas.project import ProjectStatus, TaskStatus
from ...core.security import CurrentUser
from ...core.websocket_manager import notification_manager

logger = logging.getLogger(__name__)


class ProgressMonitoringService:
    """Service for comprehensive project progress monitoring and analytics"""

    async def get_project_progress_overview(
        self,
        db: Session,
        current_user: CurrentUser,
        project_id: Optional[UUID] = None
    ) -> Dict[str, Any]:
        """Get comprehensive project progress overview"""
        try:
            if current_user.role.upper() != "SUPER_ADMIN":
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="Only <PERSON>Ad<PERSON> can access comprehensive progress monitoring"
                )

            if project_id:
                # Get specific project progress
                return await self._get_single_project_progress(db, project_id, current_user)
            else:
                # Get organization-wide progress overview
                return await self._get_organization_progress_overview(db, current_user)

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error getting project progress overview: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Error retrieving project progress data"
            )

    async def _get_organization_progress_overview(
        self,
        db: Session,
        current_user: CurrentUser
    ) -> Dict[str, Any]:
        """Get organization-wide project progress overview"""
        
        # Get all active projects
        projects = db.query(Project).filter(
            Project.organization_id == current_user.organization_id,
            Project.is_active == True
        ).all()

        project_summaries = []
        total_projects = len(projects)
        total_budget = 0
        total_actual_cost = 0
        overall_progress = 0

        for project in projects:
            progress_data = await self._calculate_project_progress(db, project)
            project_summaries.append(progress_data)
            
            if project.budget:
                total_budget += float(project.budget)
            if project.actual_cost:
                total_actual_cost += float(project.actual_cost)
            
            overall_progress += progress_data["completion_percentage"]

        # Calculate averages
        avg_progress = overall_progress / total_projects if total_projects > 0 else 0
        budget_utilization = (total_actual_cost / total_budget * 100) if total_budget > 0 else 0

        # Get status distribution
        status_distribution = db.query(
            Project.status,
            func.count(Project.id).label('count')
        ).filter(
            Project.organization_id == current_user.organization_id,
            Project.is_active == True
        ).group_by(Project.status).all()

        # Get overdue projects
        overdue_projects = db.query(Project).filter(
            Project.organization_id == current_user.organization_id,
            Project.end_date < date.today(),
            Project.status.in_([ProjectStatus.PLANNING, ProjectStatus.IN_PROGRESS]),
            Project.is_active == True
        ).count()

        # Get projects by priority
        priority_distribution = db.query(
            Project.priority,
            func.count(Project.id).label('count')
        ).filter(
            Project.organization_id == current_user.organization_id,
            Project.is_active == True
        ).group_by(Project.priority).all()

        return {
            "organization_overview": {
                "total_projects": total_projects,
                "average_progress": round(avg_progress, 2),
                "total_budget": total_budget,
                "total_actual_cost": total_actual_cost,
                "budget_utilization": round(budget_utilization, 2),
                "overdue_projects": overdue_projects
            },
            "status_distribution": [
                {"status": status.value, "count": count}
                for status, count in status_distribution
            ],
            "priority_distribution": [
                {"priority": priority.value, "count": count}
                for priority, count in priority_distribution
            ],
            "project_summaries": project_summaries,
            "generated_at": datetime.utcnow().isoformat()
        }

    async def _get_single_project_progress(
        self,
        db: Session,
        project_id: UUID,
        current_user: CurrentUser
    ) -> Dict[str, Any]:
        """Get detailed progress for a single project"""
        
        project = db.query(Project).filter(
            Project.id == project_id,
            Project.organization_id == current_user.organization_id
        ).first()

        if not project:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Project not found"
            )

        # Get detailed progress data
        progress_data = await self._calculate_project_progress(db, project)
        
        # Get task breakdown
        task_breakdown = await self._get_task_breakdown(db, project_id)
        
        # Get milestone progress
        milestone_progress = await self._get_milestone_progress(db, project_id)
        
        # Get team performance
        team_performance = await self._get_project_team_performance(db, project_id)
        
        # Get kanban board progress
        kanban_progress = await self._get_kanban_board_progress(db, project_id)
        
        # Get timeline analysis
        timeline_analysis = await self._get_timeline_analysis(db, project)

        return {
            "project_details": progress_data,
            "task_breakdown": task_breakdown,
            "milestone_progress": milestone_progress,
            "team_performance": team_performance,
            "kanban_progress": kanban_progress,
            "timeline_analysis": timeline_analysis,
            "generated_at": datetime.utcnow().isoformat()
        }

    async def _calculate_project_progress(
        self,
        db: Session,
        project: Project
    ) -> Dict[str, Any]:
        """Calculate comprehensive project progress metrics"""
        
        # Get all tasks for the project
        tasks = db.query(Task).filter(Task.project_id == project.id).all()
        
        total_tasks = len(tasks)
        completed_tasks = len([t for t in tasks if t.status == TaskStatus.COMPLETED])
        in_progress_tasks = len([t for t in tasks if t.status == TaskStatus.IN_PROGRESS])
        todo_tasks = len([t for t in tasks if t.status == TaskStatus.TODO])
        
        # Calculate task-based completion
        task_completion = (completed_tasks / total_tasks * 100) if total_tasks > 0 else 0
        
        # Get estimated vs actual hours
        total_estimated_hours = sum(float(t.estimated_hours or 0) for t in tasks)
        total_actual_hours = sum(float(t.actual_hours or 0) for t in tasks)
        
        # Calculate budget metrics
        budget_used = float(project.actual_cost or 0)
        budget_total = float(project.budget or 0)
        budget_remaining = budget_total - budget_used if budget_total > 0 else 0
        budget_utilization = (budget_used / budget_total * 100) if budget_total > 0 else 0
        
        # Calculate timeline metrics
        timeline_progress = 0
        days_remaining = 0
        is_overdue = False
        
        if project.start_date and project.end_date:
            total_days = (project.end_date - project.start_date).days
            elapsed_days = (date.today() - project.start_date).days
            timeline_progress = (elapsed_days / total_days * 100) if total_days > 0 else 0
            days_remaining = (project.end_date - date.today()).days
            is_overdue = days_remaining < 0

        # Overall completion (weighted average of different metrics)
        completion_percentage = float(project.progress_percentage or 0)
        if completion_percentage == 0 and total_tasks > 0:
            completion_percentage = task_completion

        return {
            "project_id": str(project.id),
            "project_name": project.name,
            "project_code": project.code,
            "status": project.status.value,
            "priority": project.priority.value,
            "completion_percentage": round(completion_percentage, 2),
            "task_metrics": {
                "total_tasks": total_tasks,
                "completed_tasks": completed_tasks,
                "in_progress_tasks": in_progress_tasks,
                "todo_tasks": todo_tasks,
                "task_completion_rate": round(task_completion, 2)
            },
            "time_metrics": {
                "total_estimated_hours": total_estimated_hours,
                "total_actual_hours": total_actual_hours,
                "hour_variance": total_actual_hours - total_estimated_hours,
                "hour_efficiency": round((total_estimated_hours / total_actual_hours * 100) if total_actual_hours > 0 else 0, 2)
            },
            "budget_metrics": {
                "total_budget": budget_total,
                "budget_used": budget_used,
                "budget_remaining": budget_remaining,
                "budget_utilization": round(budget_utilization, 2)
            },
            "timeline_metrics": {
                "start_date": project.start_date.isoformat() if project.start_date else None,
                "end_date": project.end_date.isoformat() if project.end_date else None,
                "timeline_progress": round(timeline_progress, 2),
                "days_remaining": days_remaining,
                "is_overdue": is_overdue
            },
            "team_info": {
                "project_manager": f"{project.project_manager.first_name} {project.project_manager.last_name}" if project.project_manager else None,
                "department": project.department.name if project.department else None
            }
        }

    async def _get_task_breakdown(
        self,
        db: Session,
        project_id: UUID
    ) -> Dict[str, Any]:
        """Get detailed task breakdown for project"""
        
        tasks = db.query(Task).filter(Task.project_id == project_id).all()
        
        # Group tasks by status
        status_breakdown = {}
        priority_breakdown = {}
        assignee_breakdown = {}
        
        for task in tasks:
            # Status breakdown
            status = task.status.value
            if status not in status_breakdown:
                status_breakdown[status] = []
            status_breakdown[status].append({
                "id": str(task.id),
                "title": task.title,
                "assignee": f"{task.assignee.first_name} {task.assignee.last_name}" if task.assignee else None,
                "due_date": task.due_date.isoformat() if task.due_date else None,
                "estimated_hours": float(task.estimated_hours or 0),
                "actual_hours": float(task.actual_hours or 0)
            })
            
            # Priority breakdown
            priority = task.priority.value if task.priority else "medium"
            priority_breakdown[priority] = priority_breakdown.get(priority, 0) + 1
            
            # Assignee breakdown
            if task.assignee:
                assignee_name = f"{task.assignee.first_name} {task.assignee.last_name}"
                if assignee_name not in assignee_breakdown:
                    assignee_breakdown[assignee_name] = {"total": 0, "completed": 0}
                assignee_breakdown[assignee_name]["total"] += 1
                if task.status == TaskStatus.COMPLETED:
                    assignee_breakdown[assignee_name]["completed"] += 1

        return {
            "status_breakdown": status_breakdown,
            "priority_breakdown": priority_breakdown,
            "assignee_breakdown": assignee_breakdown,
            "total_tasks": len(tasks)
        }

    async def _get_milestone_progress(
        self,
        db: Session,
        project_id: UUID
    ) -> Dict[str, Any]:
        """Get milestone progress for project"""
        
        milestones = db.query(ProjectMilestone).filter(
            ProjectMilestone.project_id == project_id
        ).order_by(ProjectMilestone.due_date).all()
        
        milestone_data = []
        completed_milestones = 0
        overdue_milestones = 0
        
        for milestone in milestones:
            is_completed = milestone.is_completed
            is_overdue = milestone.due_date < date.today() and not is_completed
            
            if is_completed:
                completed_milestones += 1
            if is_overdue:
                overdue_milestones += 1
                
            milestone_data.append({
                "id": str(milestone.id),
                "title": milestone.title,
                "description": milestone.description,
                "due_date": milestone.due_date.isoformat(),
                "is_completed": is_completed,
                "is_overdue": is_overdue,
                "completion_date": milestone.completion_date.isoformat() if milestone.completion_date else None
            })
        
        completion_rate = (completed_milestones / len(milestones) * 100) if milestones else 0
        
        return {
            "milestones": milestone_data,
            "total_milestones": len(milestones),
            "completed_milestones": completed_milestones,
            "overdue_milestones": overdue_milestones,
            "completion_rate": round(completion_rate, 2)
        }

    async def _get_project_team_performance(
        self,
        db: Session,
        project_id: UUID
    ) -> Dict[str, Any]:
        """Get team performance metrics for project"""

        # Get project team members
        team_members = db.query(Employee).join(ProjectAssignment).filter(
            ProjectAssignment.project_id == project_id
        ).all()

        team_performance = []

        for member in team_members:
            # Get member's tasks in this project
            member_tasks = db.query(Task).filter(
                Task.project_id == project_id,
                Task.assignee_id == member.id
            ).all()

            completed_tasks = len([t for t in member_tasks if t.status == TaskStatus.COMPLETED])
            total_tasks = len(member_tasks)

            # Calculate performance metrics
            completion_rate = (completed_tasks / total_tasks * 100) if total_tasks > 0 else 0

            # Get time metrics
            estimated_hours = sum(float(t.estimated_hours or 0) for t in member_tasks)
            actual_hours = sum(float(t.actual_hours or 0) for t in member_tasks)

            team_performance.append({
                "employee_id": str(member.id),
                "employee_name": f"{member.first_name} {member.last_name}",
                "email": member.email,
                "total_tasks": total_tasks,
                "completed_tasks": completed_tasks,
                "completion_rate": round(completion_rate, 2),
                "estimated_hours": estimated_hours,
                "actual_hours": actual_hours,
                "efficiency": round((estimated_hours / actual_hours * 100) if actual_hours > 0 else 0, 2)
            })

        return {
            "team_size": len(team_members),
            "team_performance": team_performance
        }

    async def _get_kanban_board_progress(
        self,
        db: Session,
        project_id: UUID
    ) -> Dict[str, Any]:
        """Get kanban board progress for project"""

        # Get kanban boards for this project
        boards = db.query(KanbanBoard).filter(
            KanbanBoard.project_id == project_id
        ).all()

        board_progress = []

        for board in boards:
            # Get all cards in this board
            cards = db.query(KanbanCard).join(KanbanColumn).filter(
                KanbanColumn.board_id == board.id
            ).all()

            total_cards = len(cards)
            completed_cards = len([c for c in cards if c.completed_date is not None])
            overdue_cards = len([c for c in cards if c.due_date and c.due_date < datetime.utcnow() and c.completed_date is None])

            completion_rate = (completed_cards / total_cards * 100) if total_cards > 0 else 0

            board_progress.append({
                "board_id": str(board.id),
                "board_name": board.name,
                "total_cards": total_cards,
                "completed_cards": completed_cards,
                "overdue_cards": overdue_cards,
                "completion_rate": round(completion_rate, 2)
            })

        return {
            "total_boards": len(boards),
            "board_progress": board_progress
        }

    async def _get_timeline_analysis(
        self,
        db: Session,
        project: Project
    ) -> Dict[str, Any]:
        """Get timeline analysis for project"""

        analysis = {
            "project_timeline": {
                "start_date": project.start_date.isoformat() if project.start_date else None,
                "end_date": project.end_date.isoformat() if project.end_date else None,
                "current_date": date.today().isoformat()
            },
            "timeline_status": "unknown",
            "days_elapsed": 0,
            "days_remaining": 0,
            "total_duration": 0,
            "progress_vs_timeline": 0
        }

        if project.start_date and project.end_date:
            total_duration = (project.end_date - project.start_date).days
            days_elapsed = (date.today() - project.start_date).days
            days_remaining = (project.end_date - date.today()).days

            timeline_progress = (days_elapsed / total_duration * 100) if total_duration > 0 else 0
            actual_progress = float(project.progress_percentage or 0)

            # Determine timeline status
            if days_remaining < 0:
                timeline_status = "overdue"
            elif actual_progress >= timeline_progress:
                timeline_status = "on_track"
            elif actual_progress < timeline_progress - 10:
                timeline_status = "behind_schedule"
            else:
                timeline_status = "slightly_behind"

            analysis.update({
                "timeline_status": timeline_status,
                "days_elapsed": max(0, days_elapsed),
                "days_remaining": days_remaining,
                "total_duration": total_duration,
                "timeline_progress": round(timeline_progress, 2),
                "actual_progress": actual_progress,
                "progress_vs_timeline": round(actual_progress - timeline_progress, 2)
            })

        return analysis

    async def update_project_progress(
        self,
        db: Session,
        project_id: UUID,
        current_user: CurrentUser
    ) -> Dict[str, Any]:
        """Recalculate and update project progress"""
        try:
            project = db.query(Project).filter(
                Project.id == project_id,
                Project.organization_id == current_user.organization_id
            ).first()

            if not project:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Project not found"
                )

            # Calculate new progress
            progress_data = await self._calculate_project_progress(db, project)

            # Update project progress percentage
            new_progress = progress_data["completion_percentage"]
            old_progress = float(project.progress_percentage or 0)

            project.progress_percentage = new_progress
            project.updated_at = datetime.utcnow()

            db.commit()
            db.refresh(project)

            # Send notification if significant progress change
            if abs(new_progress - old_progress) >= 5:  # 5% threshold
                await notification_manager.notify_project_update(
                    str(project_id),
                    {
                        "type": "progress_update",
                        "old_progress": old_progress,
                        "new_progress": new_progress,
                        "updated_by": current_user.email,
                        "timestamp": datetime.utcnow().isoformat()
                    }
                )

            logger.info(f"Project {project_id} progress updated from {old_progress}% to {new_progress}%")

            return {
                "project_id": str(project_id),
                "old_progress": old_progress,
                "new_progress": new_progress,
                "progress_change": new_progress - old_progress,
                "updated_at": project.updated_at.isoformat()
            }

        except HTTPException:
            raise
        except Exception as e:
            db.rollback()
            logger.error(f"Error updating project progress: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Error updating project progress"
            )
