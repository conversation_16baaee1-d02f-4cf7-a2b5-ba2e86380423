from core.models.employee_component_pivot import EmployeeComponentsPivotModel
from core.databases.database import db

class EmployeesComponentRepository:
    def __init__(self) -> None:
        pass

    @classmethod
    def create(self, employee_id, salary_components_id ):
        salary_pivot = EmployeeComponentsPivotModel(
            employee_id = employee_id,
            salary_component_id = salary_components_id,
        )
        db.session.add(salary_pivot)
        db.session.commit()
        return salary_pivot
    
    # @classmethod
    # def getAttachedEmployee(self, id):
    #     return EmployeeComponentsPivotModel.query.filter_by(employee_id=id).all()
    
    @classmethod
    def getAttachedEmployee(cls, employee_id):
        """
        Get all salary components attached to a specific employee.
        """
        return EmployeeComponentsPivotModel.query.filter_by(employee_id=employee_id).all()
    
    @classmethod
    def getAttachedEmployeeComponent(cls, employee_id, component_id):
        """
        Check if a salary component is already assigned to a given employee.
        """
        return EmployeeComponentsPivotModel.query.filter_by(
            employee_id=employee_id, salary_component_id=component_id
        ).first()

    
    @classmethod
    def getSingleComponent(cls, employee_id, component_id):
        """
        Get a single salary component assigned to a specific employee.
        
        Args:
            employee_id (int): The ID of the employee.
            component_id (int): The ID of the salary component.
        
        Returns:
            EmployeeComponentsPivotModel: The model instance if found, otherwise None.
        """
        return EmployeeComponentsPivotModel.query.filter_by(
            employee_id=employee_id,
            salary_component_id=component_id
        ).first()


    # @classmethod
    # def delete(self, ids):
    #     db.session.query(EmployeeComponentsPivotModel).filter(EmployeeComponentsPivotModel.id.in_(ids)).delete(synchronize_session=False)      
    #     db.session.commit()
    #     return 
    
    @classmethod
    def delete(cls, employee_id, component_ids):
        """
        Delete specific salary component pivot records for a given employee.
        
        Args:
            employee_id (int): The ID of the employee whose components should be deleted.
            component_ids (list): A list of IDs of the salary component pivots to delete.
        """
        db.session.query(EmployeeComponentsPivotModel).filter(
            EmployeeComponentsPivotModel.employee_id == employee_id,
            EmployeeComponentsPivotModel.id.in_(component_ids)
        ).delete(synchronize_session=False)
        db.session.commit()
