"""
Test Quick Onboarding API
Simple test for the quick onboarding functionality
"""

import requests
import json

def test_quick_onboard():
    """Test the quick onboarding API endpoint"""
    
    base_url = "http://localhost:8085/api"
    
    print("🧪 Testing Quick Onboarding API")
    print("=" * 50)
    
    # Step 1: Login to get token
    print("1. Logging in...")
    login_response = requests.post(f"{base_url}/auth/login", json={
        "email": "<EMAIL>",
        "password": "password123"
    })
    
    if login_response.status_code != 200:
        print(f"❌ Login failed: {login_response.status_code}")
        print(login_response.text)
        return
    
    token = login_response.json()["access_token"]
    headers = {"Authorization": f"Bearer {token}"}
    print("✅ Login successful")
    
    # Step 2: Test quick onboarding
    print("\n2. Testing quick onboarding...")
    
    onboard_data = {
        "employee_name": "Test Employee",
        "employee_email": "<EMAIL>",
        "start_date": "2024-01-15"
    }
    
    response = requests.post(
        f"{base_url}/onboarding/quick-onboard",
        json=onboard_data,
        headers=headers
    )
    
    print(f"Status Code: {response.status_code}")
    
    if response.status_code == 200:
        result = response.json()
        print("✅ Quick onboarding successful!")
        print(f"Employee ID: {result['employee']['employee_id']}")
        print(f"Employee Name: {result['employee']['name']}")
        print(f"Email: {result['employee']['email']}")
        print(f"Workflow ID: {result['workflow']['id']}")
        print(f"Workflow Status: {result['workflow']['status']}")
        print(f"Login Email: {result['credentials']['email']}")
        print(f"Temporary Password: {result['credentials']['temporary_password']}")
        print(f"Message: {result['message']}")
    else:
        print(f"❌ Quick onboarding failed: {response.status_code}")
        print("Response:", response.text)
    
    # Step 3: Test onboarding endpoints
    print("\n3. Testing onboarding endpoints...")
    
    endpoints = [
        "/onboarding/templates",
        "/onboarding/onboarding"
    ]
    
    for endpoint in endpoints:
        try:
            resp = requests.get(f"{base_url}{endpoint}", headers=headers)
            status = "✅ Working" if resp.status_code == 200 else f"❌ {resp.status_code}"
            print(f"   {endpoint}: {status}")
            if resp.status_code != 200:
                print(f"      Error: {resp.text}")
        except Exception as e:
            print(f"   {endpoint}: ❌ Error - {e}")
    
    print("\n📊 Test Summary")
    print("=" * 50)
    print("Quick onboarding API test completed!")

if __name__ == "__main__":
    test_quick_onboard()
