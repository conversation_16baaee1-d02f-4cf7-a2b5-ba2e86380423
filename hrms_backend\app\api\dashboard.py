from fastapi import APIRouter, HTTPException, status, Depends, Query
from sqlalchemy.orm import Session
from typing import Optional, Dict
from datetime import date

from ..core.security import get_current_user, require_permission, CurrentUser
from ..core.rbac import Permissions
from ..db.session import get_db
from ..services.leave_management.dashboard_service import dashboard_service
from ..services.leave_management.holiday_service import holiday_service

router = APIRouter()


@router.get("/employee", response_model=Dict)
async def get_employee_dashboard(
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(get_current_user)
):
    """Get employee dashboard"""
    return await dashboard_service.get_employee_dashboard(db, current_user)


@router.get("/manager", response_model=Dict)
async def get_manager_dashboard(
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.LEAVE_APPROVE))
):
    """Get manager dashboard"""
    return await dashboard_service.get_manager_dashboard(db, current_user)


@router.get("/hr", response_model=Dict)
async def get_hr_dashboard(
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.LEAVE_ADMIN))
):
    """Get HR dashboard"""
    return await dashboard_service.get_hr_dashboard(db, current_user)


@router.get("/calendar")
async def get_leave_calendar(
    start_date: Optional[date] = Query(None),
    end_date: Optional[date] = Query(None),
    location: Optional[str] = Query(None),
    holiday_type: Optional[str] = Query(None),
    include_weekly_offs: bool = Query(False),
    include_team_leaves: bool = Query(False),
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(get_current_user)
):
    """Get comprehensive leave calendar"""
    try:
        # Get holidays and weekly offs
        calendar_data = await holiday_service.get_holidays(
            db=db,
            current_user=current_user,
            start_date=start_date,
            end_date=end_date,
            location=location,
            holiday_type=holiday_type,
            include_weekly_offs=include_weekly_offs
        )
        
        # Add team leaves if requested and user has permission
        if include_team_leaves and current_user.role.upper() in ["MANAGER", "HR", "ADMIN"]:
            team_leaves = await dashboard_service._get_team_leave_calendar(
                db, [], start_date or date.today(), end_date or date.today()
            )
            calendar_data.extend(team_leaves)
        
        return {
            "calendar": sorted(calendar_data, key=lambda x: x.get("date", "")),
            "period": {
                "start_date": (start_date or date.today()).isoformat(),
                "end_date": (end_date or date.today()).isoformat()
            }
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error retrieving calendar data"
        )


@router.get("/analytics/leave-trends")
async def get_leave_analytics(
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.LEAVE_READ))
):
    """Get leave analytics and trends"""
    try:
        # This would be implemented based on specific analytics requirements
        return {
            "message": "Leave analytics endpoint - to be implemented based on specific requirements",
            "available_analytics": [
                "leave_trends_by_month",
                "policy_utilization",
                "department_wise_usage",
                "seasonal_patterns",
                "approval_time_analysis"
            ]
        }
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error retrieving analytics"
        )


@router.get("/reports/summary")
async def get_leave_summary_report(
    start_date: Optional[date] = Query(None),
    end_date: Optional[date] = Query(None),
    department_id: Optional[str] = Query(None),
    leave_type: Optional[str] = Query(None),
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.LEAVE_READ))
):
    """Get leave summary report"""
    try:
        # This would generate comprehensive reports
        return {
            "message": "Leave summary report endpoint - to be implemented",
            "parameters": {
                "start_date": start_date.isoformat() if start_date else None,
                "end_date": end_date.isoformat() if end_date else None,
                "department_id": department_id,
                "leave_type": leave_type
            },
            "available_reports": [
                "employee_wise_summary",
                "department_wise_summary",
                "policy_wise_utilization",
                "pending_approvals_report",
                "attendance_correlation"
            ]
        }
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error generating report"
        )


@router.get("/quick-stats")
async def get_quick_stats(
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(get_current_user)
):
    """Get quick statistics based on user role"""
    try:
        if current_user.role.upper() == "EMPLOYEE":
            dashboard_data = await dashboard_service.get_employee_dashboard(db, current_user)
            return dashboard_data.get("quick_stats", {})
        elif current_user.role.upper() == "MANAGER":
            dashboard_data = await dashboard_service.get_manager_dashboard(db, current_user)
            return dashboard_data.get("quick_stats", {})
        elif current_user.role.upper() in ["HR", "ADMIN"]:
            dashboard_data = await dashboard_service.get_hr_dashboard(db, current_user)
            return dashboard_data.get("quick_stats", {})
        else:
            return {"message": "No quick stats available for this role"}
            
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error retrieving quick stats"
        )


@router.get("/notifications")
async def get_dashboard_notifications(
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(get_current_user)
):
    """Get dashboard notifications"""
    try:
        notifications = []
        
        # Add role-specific notifications
        if current_user.role.upper() == "EMPLOYEE":
            # Check for expiring leaves
            # Check for pending requests
            notifications.append({
                "type": "info",
                "message": "Employee notifications - to be implemented",
                "priority": "low"
            })
        elif current_user.role.upper() == "MANAGER":
            # Check for pending approvals
            # Check for team staffing issues
            notifications.append({
                "type": "warning",
                "message": "Manager notifications - to be implemented",
                "priority": "medium"
            })
        elif current_user.role.upper() in ["HR", "ADMIN"]:
            # Check for policy violations
            # Check for system issues
            notifications.append({
                "type": "alert",
                "message": "HR notifications - to be implemented",
                "priority": "high"
            })
        
        return {
            "notifications": notifications,
            "count": len(notifications)
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error retrieving notifications"
        )


@router.get("/widgets/{widget_type}")
async def get_dashboard_widget(
    widget_type: str,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(get_current_user)
):
    """Get specific dashboard widget data"""
    try:
        widget_data = {}
        
        if widget_type == "leave_balance":
            # Return leave balance widget data
            widget_data = {"message": "Leave balance widget - to be implemented"}
        elif widget_type == "upcoming_holidays":
            # Return upcoming holidays widget data
            widget_data = {"message": "Upcoming holidays widget - to be implemented"}
        elif widget_type == "team_calendar":
            # Return team calendar widget data
            widget_data = {"message": "Team calendar widget - to be implemented"}
        elif widget_type == "pending_approvals":
            # Return pending approvals widget data
            widget_data = {"message": "Pending approvals widget - to be implemented"}
        else:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Widget type not found"
            )
        
        return {
            "widget_type": widget_type,
            "data": widget_data
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error retrieving widget data"
        )
