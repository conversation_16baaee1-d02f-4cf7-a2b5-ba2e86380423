from pydantic import BaseModel, <PERSON>, validator
from typing import Optional, List, Dict, Any
from uuid import UUID
from datetime import datetime, date
from enum import Enum


class SurveyStatus(str, Enum):
    DRAFT = "draft"
    ACTIVE = "active"
    PAUSED = "paused"
    COMPLETED = "completed"
    ARCHIVED = "archived"


class SurveyType(str, Enum):
    ENGAGEMENT = "engagement"
    SATISFACTION = "satisfaction"
    PULSE = "pulse"
    EXIT = "exit"
    ONBOARDING = "onboarding"
    FEEDBACK = "feedback"
    CUSTOM = "custom"


class QuestionType(str, Enum):
    MULTIPLE_CHOICE = "multiple_choice"
    SINGLE_CHOICE = "single_choice"
    TEXT = "text"
    RATING = "rating"
    SCALE = "scale"
    YES_NO = "yes_no"
    DATE = "date"
    NUMBER = "number"


class ResponseStatus(str, Enum):
    STARTED = "started"
    COMPLETED = "completed"
    ABANDONED = "abandoned"


# Survey Schemas
class SurveyBase(BaseModel):
    title: str = Field(..., min_length=1, max_length=200)
    description: Optional[str] = None
    survey_type: SurveyType = SurveyType.ENGAGEMENT
    instructions: Optional[str] = None
    start_date: datetime
    end_date: datetime
    is_anonymous: bool = True
    allow_multiple_responses: bool = False
    send_reminders: bool = True
    reminder_frequency_days: int = Field(3, ge=1, le=30)

    @validator('end_date')
    def end_date_must_be_after_start_date(cls, v, values):
        if 'start_date' in values and v <= values['start_date']:
            raise ValueError('End date must be after start date')
        return v


class SurveyCreate(SurveyBase):
    target_departments: Optional[List[UUID]] = None
    target_employees: Optional[List[UUID]] = None
    exclude_employees: Optional[List[UUID]] = None


class SurveyUpdate(BaseModel):
    title: Optional[str] = Field(None, min_length=1, max_length=200)
    description: Optional[str] = None
    instructions: Optional[str] = None
    end_date: Optional[datetime] = None
    status: Optional[SurveyStatus] = None
    send_reminders: Optional[bool] = None
    reminder_frequency_days: Optional[int] = Field(None, ge=1, le=30)


class SurveyResponse(SurveyBase):
    id: UUID
    organization_id: UUID
    created_by: UUID
    status: SurveyStatus
    total_questions: int
    total_responses: int
    completion_rate: float
    target_departments: Optional[List[UUID]] = None
    target_employees: Optional[List[UUID]] = None
    exclude_employees: Optional[List[UUID]] = None
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class SurveyListResponse(BaseModel):
    surveys: List[SurveyResponse]
    total: int
    skip: int
    limit: int


# Question Schemas
class QuestionBase(BaseModel):
    question_text: str = Field(..., min_length=1)
    question_type: QuestionType
    is_required: bool = True
    order_index: int = Field(..., ge=0)
    help_text: Optional[str] = None
    validation_rules: Optional[Dict[str, Any]] = None


class QuestionCreate(QuestionBase):
    survey_id: UUID
    options: Optional[List[str]] = None  # For multiple choice questions
    scale_min: Optional[int] = None  # For scale questions
    scale_max: Optional[int] = None
    scale_labels: Optional[Dict[str, str]] = None


class QuestionUpdate(BaseModel):
    question_text: Optional[str] = Field(None, min_length=1)
    is_required: Optional[bool] = None
    order_index: Optional[int] = Field(None, ge=0)
    help_text: Optional[str] = None
    options: Optional[List[str]] = None
    scale_min: Optional[int] = None
    scale_max: Optional[int] = None
    scale_labels: Optional[Dict[str, str]] = None
    validation_rules: Optional[Dict[str, Any]] = None


class QuestionResponse(QuestionBase):
    id: UUID
    survey_id: UUID
    options: Optional[List[str]] = None
    scale_min: Optional[int] = None
    scale_max: Optional[int] = None
    scale_labels: Optional[Dict[str, str]] = None
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


# Survey Response Schemas
class SurveyResponseBase(BaseModel):
    started_at: datetime
    completed_at: Optional[datetime] = None
    is_anonymous: bool = True


class SurveyResponseCreate(BaseModel):
    survey_id: UUID


class SurveyResponseUpdate(BaseModel):
    completed_at: Optional[datetime] = None
    status: Optional[ResponseStatus] = None


class SurveyResponseResponse(SurveyResponseBase):
    id: UUID
    survey_id: UUID
    respondent_id: Optional[UUID] = None  # Null if anonymous
    status: ResponseStatus
    progress_percentage: float
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


# Answer Schemas
class AnswerBase(BaseModel):
    answer_text: Optional[str] = None
    answer_number: Optional[float] = None
    answer_date: Optional[date] = None
    selected_options: Optional[List[str]] = None


class AnswerCreate(AnswerBase):
    question_id: UUID
    survey_response_id: UUID


class AnswerUpdate(AnswerBase):
    pass


class AnswerResponse(AnswerBase):
    id: UUID
    question_id: UUID
    survey_response_id: UUID
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


# Survey Analytics Schemas
class SurveyAnalytics(BaseModel):
    survey_id: UUID
    total_invited: int
    total_responses: int
    completion_rate: float
    average_completion_time_minutes: float
    response_rate_by_department: Dict[str, float]
    question_analytics: List[Dict[str, Any]]
    sentiment_analysis: Optional[Dict[str, Any]] = None


class QuestionAnalytics(BaseModel):
    question_id: UUID
    question_text: str
    question_type: QuestionType
    total_responses: int
    response_distribution: Dict[str, Any]
    average_rating: Optional[float] = None
    sentiment_score: Optional[float] = None


# Feedback Schemas
class FeedbackBase(BaseModel):
    title: str = Field(..., min_length=1, max_length=200)
    content: str = Field(..., min_length=1)
    category: Optional[str] = Field(None, max_length=100)
    is_anonymous: bool = False
    priority: str = Field("medium", pattern="^(low|medium|high|urgent)$")


class FeedbackCreate(FeedbackBase):
    recipient_id: Optional[UUID] = None  # If feedback is for specific person
    department_id: Optional[UUID] = None  # If feedback is for department
    attachment_urls: Optional[List[str]] = None


class FeedbackUpdate(BaseModel):
    title: Optional[str] = Field(None, min_length=1, max_length=200)
    content: Optional[str] = Field(None, min_length=1)
    category: Optional[str] = Field(None, max_length=100)
    priority: Optional[str] = Field(None, pattern="^(low|medium|high|urgent)$")
    status: Optional[str] = Field(None, pattern="^(open|in_review|addressed|closed)$")
    response: Optional[str] = None
    attachment_urls: Optional[List[str]] = None


class FeedbackResponse(FeedbackBase):
    id: UUID
    organization_id: UUID
    submitter_id: Optional[UUID] = None  # Null if anonymous
    recipient_id: Optional[UUID] = None
    department_id: Optional[UUID] = None
    status: str
    response: Optional[str] = None
    responded_by: Optional[UUID] = None
    responded_at: Optional[datetime] = None
    attachment_urls: Optional[List[str]] = None
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class FeedbackListResponse(BaseModel):
    feedback: List[FeedbackResponse]
    total: int
    skip: int
    limit: int


# Recognition Schemas
class RecognitionBase(BaseModel):
    title: str = Field(..., min_length=1, max_length=200)
    message: str = Field(..., min_length=1)
    recognition_type: str = Field(..., max_length=50)
    points_awarded: int = Field(0, ge=0)
    is_public: bool = True


class RecognitionCreate(RecognitionBase):
    recipient_id: UUID
    badge_id: Optional[UUID] = None


class RecognitionUpdate(BaseModel):
    title: Optional[str] = Field(None, min_length=1, max_length=200)
    message: Optional[str] = Field(None, min_length=1)
    points_awarded: Optional[int] = Field(None, ge=0)
    is_public: Optional[bool] = None


class RecognitionResponse(RecognitionBase):
    id: UUID
    organization_id: UUID
    giver_id: UUID
    recipient_id: UUID
    badge_id: Optional[UUID] = None
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class RecognitionListResponse(BaseModel):
    recognitions: List[RecognitionResponse]
    total: int
    skip: int
    limit: int


# Badge Schemas
class BadgeBase(BaseModel):
    name: str = Field(..., min_length=1, max_length=100)
    description: str = Field(..., min_length=1)
    icon_url: Optional[str] = Field(None, max_length=500)
    color: str = Field("#3B82F6", pattern="^#[0-9A-Fa-f]{6}$")
    points_value: int = Field(0, ge=0)
    criteria: Optional[str] = None


class BadgeCreate(BadgeBase):
    pass


class BadgeUpdate(BaseModel):
    name: Optional[str] = Field(None, min_length=1, max_length=100)
    description: Optional[str] = Field(None, min_length=1)
    icon_url: Optional[str] = Field(None, max_length=500)
    color: Optional[str] = Field(None, pattern="^#[0-9A-Fa-f]{6}$")
    points_value: Optional[int] = Field(None, ge=0)
    criteria: Optional[str] = None
    is_active: Optional[bool] = None


class BadgeResponse(BadgeBase):
    id: UUID
    organization_id: UUID
    times_awarded: int
    created_at: datetime
    updated_at: datetime
    is_active: bool

    class Config:
        from_attributes = True


# Employee Engagement Metrics
class EngagementMetrics(BaseModel):
    employee_id: UUID
    engagement_score: float
    satisfaction_score: float
    recognition_points: int
    badges_earned: int
    feedback_given: int
    feedback_received: int
    survey_participation_rate: float
    last_survey_date: Optional[date] = None
    trend_direction: str  # "up", "down", "stable"


class TeamEngagementMetrics(BaseModel):
    department_id: UUID
    department_name: str
    average_engagement_score: float
    average_satisfaction_score: float
    total_recognition_points: int
    survey_participation_rate: float
    feedback_volume: int
    top_performers: List[Dict[str, Any]]
    areas_for_improvement: List[str]


# Engagement Dashboard
class EngagementDashboard(BaseModel):
    organization_metrics: Dict[str, Any]
    department_metrics: List[TeamEngagementMetrics]
    recent_recognitions: List[RecognitionResponse]
    active_surveys: List[SurveyResponse]
    trending_feedback: List[FeedbackResponse]
    engagement_trends: Dict[str, List[float]]


# Bulk Operations
class BulkSurveyInvite(BaseModel):
    survey_id: UUID
    employee_ids: List[UUID]
    send_email: bool = True
    custom_message: Optional[str] = None


class BulkRecognition(BaseModel):
    recipient_ids: List[UUID]
    title: str = Field(..., min_length=1, max_length=200)
    message: str = Field(..., min_length=1)
    recognition_type: str = Field(..., max_length=50)
    points_awarded: int = Field(0, ge=0)
    badge_id: Optional[UUID] = None
