# 🚀 HRMS Quick Start Guide

## 🎯 One-Command Launch

### Option 1: Python Launcher (Recommended)
```bash
python run_hrms.py
```

### Option 2: Windows Batch File
```bash
start_hrms.bat
```

### Option 3: Linux/Mac Shell Script
```bash
./start_hrms.sh
```

### Option 4: NPM Scripts
```bash
npm run start
```

## 📋 What Happens When You Run

1. **Dependency Check** ✅
   - Verifies Python 3.8+
   - Verifies Node.js 16+
   - Checks project directories

2. **Backend Startup** 🚀
   - Starts FastAPI server on port 8085
   - Loads database models
   - Initializes RBAC permissions
   - Enables API endpoints

3. **Frontend Startup** 🎨
   - Starts React dev server on port 5173
   - Builds and serves the UI
   - Connects to backend API

4. **Browser Launch** 🌐
   - Automatically opens http://localhost:5173
   - Ready for immediate use

## 🔐 Login Credentials

| Role | Email | Password |
|------|-------|----------|
| **Admin** | <EMAIL> | password123 |
| **HR** | <EMAIL> | password123 |
| **Manager** | <EMAIL> | password123 |

## ⚡ Quick Onboarding Test

1. **Login** as Admin or HR
2. **Go to** Onboarding page
3. **Click** "Quick Onboard" (green button)
4. **Enter:**
   - Name: `Test Employee`
   - Email: `<EMAIL>`
5. **Click** "Start Onboarding"
6. **See** instant employee creation with credentials!

## 🛠️ Manual Startup (If Launcher Fails)

### Start Backend
```bash
cd hrms_backend
python -m uvicorn app.main:app --host 0.0.0.0 --port 8085 --reload
```

### Start Frontend (New Terminal)
```bash
cd hrm_frontend
npm run dev
```

### Open Browser
```
http://localhost:5173
```

## 📊 Available URLs

- **Frontend UI**: http://localhost:5173
- **Backend API**: http://localhost:8085
- **API Docs**: http://localhost:8085/docs
- **Health Check**: http://localhost:8085/health

## 🎉 Key Features to Test

### ✅ Employee Management
- View employee directory
- Create new employees
- Update employee profiles

### ✅ Quick Onboarding
- **Star Feature**: Create employee with just name + email
- Automatic credential generation
- Instant workflow creation

### ✅ Attendance & Time
- Time tracking
- Attendance reports
- Timesheet approvals

### ✅ Leave Management
- Leave requests
- Approval workflows
- Balance tracking

### ✅ Project Management
- Project creation
- Task assignment
- Progress tracking

### ✅ Ticket System
- IT support tickets
- HR requests
- Issue resolution

## 🔧 Troubleshooting

### Backend Won't Start
```bash
cd hrms_backend
pip install -r requirements.txt
python -m uvicorn app.main:app --port 8085
```

### Frontend Won't Start
```bash
cd hrm_frontend
npm install
npm run dev
```

### Database Issues
```bash
cd hrms_backend
python create_test_employees.py
python create_default_onboarding_template.py
```

### Port Conflicts
- Backend: Change port in `run_hrms.py` (line with `--port 8085`)
- Frontend: Change port in `hrm_frontend/vite.config.js`

## 🐳 Docker Alternative

```bash
docker-compose up --build
```

## 📞 Support

- Check `/docs` for API documentation
- Review test files for examples
- Check console logs for errors

---

**🎊 Enjoy your HRMS experience!**
