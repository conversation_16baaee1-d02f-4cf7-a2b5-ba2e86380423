from core.repositories.transaction_history import TransactionHistoryRepository
import db
from core.databases.database import db

class TransactionHistoryService:
    def __init__(self) -> None:
        self.repository = TransactionHistoryRepository()
    # Create or add a new Paystack Integration record
    def createTransaction(self, Kwargs):
        return self.repository.create_transaction(**Kwargs)

    # Get a Paystack Integration record by ID
    def getTransactionById(self, id):
        return self.repository.get_transactionById(id)

    # Update a Paystack Integration record by ID
    # def updateTransaction(self, id, **Kwargs):
    #     return self.repository.update_transaction(id,**Kwargs)
    def updateTransaction(self, transaction_id, **kwargs):
        return self.repository.update_transaction(transaction_id=transaction_id, **kwargs)  # ✅ Fix argument passing
    
    def getTransactionByReference(self, reference_code):
        """Fetch transaction by reference_code instead of ID."""
        return self.repository.get_transactionByReference(reference_code)

   
    # Delete a Paystack Integration record by ID
    def deleteTransaction(self, id):
        return self.repository.deleteTransaction(id)

    def update_transaction_history_webhook_response(self, transaction_id, reference_code, message, transaction_date):
        """Update transaction history `transaction_message` field using `reference_code`."""
        
        transaction_history = TransactionHistoryRepository.get_transaction_by_reference(transaction_id, reference_code)

        if transaction_history:
            transaction_history.transaction_message = message  # ✅ Assign `message` to `transaction_message`
            transaction_history.transaction_date = transaction_date  
            db.session.commit()
            # print(f"✅ Transaction history updated for reference_code: {reference_code}")
            return {"status": "success", "message": "Transaction processed"}, 200
        else:
            # print(f"❌ Error: Transaction history not found for reference_code: {reference_code}")
            return {"status": "error", "message": "Transaction history not found"}, 404
        