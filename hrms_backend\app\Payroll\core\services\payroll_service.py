from core.models.payroll_summary import PayrollSummary
from core.models.salary_components import SalaryComponentsModel
from core.models.salary_templates import SalaryTemplatesModel
# from core.models.payroll_details import PayrollDetailsModel
# from core.models.employee_salary_details import EmployeeSalaryDetailsModel
from datetime import datetime
from core.repositories.employee import EmployeeRepository
from core.repositories.salary_components import SalaryComponentsRepository
from core.repositories.salary_templates import SalaryTemplatesRepository
from sqlalchemy import text
# from core.repositories.payroll_details import PayrollDetailsRepository
from core.databases.database import db
from core.services.tax_calculator_service import TaxCalculatorService
from core.models.employees import EmployeeModel
# from core.models.payroll_summary import PayrollSummary



class PayrollService:
    def __init__(self):
        self.db_session = db.session  # Only keep if you need it for other interactions
        self.employees = EmployeeRepository()
        self.salary_component = SalaryComponentsRepository()
        self.salary_template = SalaryTemplatesRepository()
        # self.tax_service = TaxCalculatorService("Nigeria")
        self.current_date = datetime.now()
        """
        Set the code to calculate to for Nigeria
        
        """

    def create_payroll_schedule(self, payroll_period):
        employees = self.employees.fetchAll()
        payroll_data = []

        for employee in employees:
            tax_type = employee.tax_type
            gross_pay = employee.gross_pay
            
            cra = 0
            taxable_income = 0
            total_statutory_deduction = 0
            final_monthly_tax = 0
            balance_after_tax_deduction = 0

            # Step 1: Calculate the tax for the employee (In case of NA, no tax deduction)
            if tax_type == 'WHT':
                tax_deduction = gross_pay * 0.075
            elif tax_type == 'MIN_TAX':
                tax_deduction = gross_pay * 0.01
            elif tax_type == 'NA':
                tax_deduction = 0  # No tax for NA
            elif tax_type == 'PAYE':
                tax, paye_total, cra, taxable_income, gross_after_statutory_deduction, total_statutory_deduction = self.calculate_paye(gross_pay)
                gross_after_paye_deduction = gross_pay - tax
                tax_deduction = tax
                print(f"Printing out taxable_income  : {taxable_income }")
                print(f"Printing out gross_after_statutory_deduction  : {gross_after_statutory_deduction }")
                print(f"Printing out total_statutory_deduction  : {total_statutory_deduction }")
                print(f"Printing out the PAYE RESULTS initial_monthly_tax : {paye_total}")
                print(f"Printing out tax : {tax}")
                print(f"Printing out gross_after_paye_deduction : {gross_after_paye_deduction}")
                

                
            else:
                tax_deduction = 0

            # Balance after tax deduction for the current employee
            balance_after_tax_deduction = gross_pay - tax_deduction
            
            
            # Step 2: Deduct other salary components (non-statutory components)
            # other_deductions = self.deduct_other_salary_components(employee, gross_pay)


            # Calculate net pay by calling the calculate_netpay function
            # net_pay_sum = self.calculate_netpay(gross_pay, final_monthly_tax, balance_after_tax_deduction)
            # print(f" Net pay Earnings returns: {net_pay_sum}")
            
            components = self.salary_component.fetchAll()
            net_pay_sum, total_earnings_after_tax_and_other_deductions = self.calculate_netpay(gross_pay, components, final_monthly_tax, balance_after_tax_deduction)
            # print(f" Net pay Earnings returns: {net_pay_sum}")

            
            # final_tax = gross_pay - other_deductions
            # annual_tax = final_tax + tax_deduction
            
            # final_tax = tax_deduction
            annual_tax = tax_deduction

            # Divide annual tax by 12 to get monthly tax
            if tax_type == 'NA':
                final_annual_tax = annual_tax  
                final_monthly_tax = final_annual_tax / 12
            elif tax_type == 'MIN_TAX':
                final_annual_tax = annual_tax
                final_monthly_tax = final_annual_tax / 12
            elif tax_type == 'WHT':
                final_annual_tax = annual_tax
                final_monthly_tax = final_annual_tax / 12
            elif tax_type == 'PAYE':
                final_annual_tax = annual_tax
                final_monthly_tax = final_annual_tax / 12
            else:
                # final_annual_tax = tax_deduction * 12  # For other types
                final_monthly_tax = tax_deduction

            # Prepare the data to return
            employee_info = {
                "employee_id": employee.id,
                "first_name": employee.first_name,
                "last_name": employee.last_name,
                "gross_pay": gross_pay,
                "tax_type": tax_type,
                "monthly_tax": round(final_monthly_tax, 2),  # Monthly tax derived from annual tax
                # "other_deductions": round(other_deductions, 2),
                "annual_tax": round(annual_tax, 2),  # Remaining salary after all deductions
                "cra": round(cra, 2),
                "taxable_income": round(taxable_income, 2),
                "statutory_deductions": round(total_statutory_deduction, 2),
                "pay_after_tax_deduction": round(balance_after_tax_deduction, 2),
                "net_pay_sum": round(net_pay_sum, 2),
                "total_earnings_after_tax_and_other_deductions": round(total_earnings_after_tax_and_other_deductions, 2)
            }

            # Debugging print statement
            # print(employee_info)

            # Append the formatted employee information to the payroll data list
            payroll_data.append(employee_info)
            
            # Step 3: Save or update the payroll record for this employee
            existing_payroll = PayrollSummary.query.filter_by(employee_id=employee.id).first()
            
            if existing_payroll:
                # Update existing payroll record
                existing_payroll.gross_pay = gross_pay
                existing_payroll.tax_type = tax_type
                existing_payroll.monthly_tax = round(final_monthly_tax, 2)
                existing_payroll.annual_tax = round(annual_tax, 2)
                existing_payroll.cra = round(cra, 2)
                existing_payroll.taxable_income = round(taxable_income, 2)
                existing_payroll.statutory_deductions = round(total_statutory_deduction, 2)
                existing_payroll.pay_after_tax_deduction = round(balance_after_tax_deduction, 2)
                existing_payroll.net_pay_sum = round(net_pay_sum, 2)
                existing_payroll.total_earnings_after_tax_and_other_deductions = round(total_earnings_after_tax_and_other_deductions, 2)
            else:
                # Create a new payroll record
                new_payroll = PayrollSummary(
                    employee_id=employee.id,
                    gross_pay=gross_pay,
                    tax_type=tax_type,
                    monthly_tax=round(final_monthly_tax, 2),
                    annual_tax=round(annual_tax, 2),
                    cra=round(cra, 2),
                    taxable_income=round(taxable_income, 2),
                    statutory_deductions=round(total_statutory_deduction, 2),
                    pay_after_tax_deduction=round(balance_after_tax_deduction, 2),
                    net_pay_sum=round(net_pay_sum, 2),
                    total_earnings_after_tax_and_other_deductions=round(total_earnings_after_tax_and_other_deductions, 2)
                )
                db.session.add(new_payroll)

        # Commit changes to the database
        db.session.commit()


        # Return the aggregated payroll data for all employees
        return payroll_data


    def calculate_paye(self, gross_pay):
        """
        Calculate PAYE based on statutory deductions.
        This assumes that statutory deductions are already handled elsewhere.
        """
        print(f"Printing GROSSXXXXXXXXXX: {gross_pay}")
        
        tax = 0  # Placeholder calculation
        paye_total = 0  # Placeholder calculation
        cra = 0  # Placeholder calculation
        taxable_income = 0  # Placeholder calculation
        total_statutory_deduction = 0  # Placeholder calculation
        
        components = self.salary_component.fetchAll()
        
        total_statutory_deduction = self.calculate_statutory_deductions(gross_pay, components)
        # print(f"Printing out the annual_gross_cra: {annual_gross_cra}")
        print(f"Printing out the total statutory deduction: {total_statutory_deduction}")
        
        
        gross_after_statutory_deduction = gross_pay - total_statutory_deduction
        print(f"Gross after statutory deduction: {gross_after_statutory_deduction}")
        
        cra = gross_after_statutory_deduction * 0.2 + 200000  
        
        taxable_income = gross_after_statutory_deduction - cra
        
        # print(f"Printing out the CRA: {cra}")
        # print(f"Printing out the taxable income: {taxable_income}")
        
        if taxable_income <= 0:
            # print("Taxable income is zero or negative")
            return 0, 0

        tax = 0
        # Apply the tax brackets as before
        if taxable_income > 300000:
            tax += 300000 * 0.07
            taxable_income -= 300000
        else:
            return taxable_income * 0.07, (taxable_income * 0.07) / 12
        
        if taxable_income > 300000:
            tax += 300000 * 0.11
            taxable_income -= 300000
        else:
            return tax + taxable_income * 0.11, (tax + taxable_income * 0.11) / 12
        
        if taxable_income > 500000:
            tax += 500000 * 0.15
            taxable_income -= 500000
        else:
            return tax + taxable_income * 0.15, (tax + taxable_income * 0.15) / 12
        
        if taxable_income > 500000:
            tax += 500000 * 0.19
            taxable_income -= 500000
        else:
            return tax + taxable_income * 0.19, (tax + taxable_income * 0.19) / 12
        
        if taxable_income > 1600000:
            tax += 1600000 * 0.21
            taxable_income -= 1600000
        else:
            return tax + taxable_income * 0.21, (tax + taxable_income * 0.21) / 12

        tax += taxable_income * 0.24
        paye_total = tax / 12
        
        print(f"Total statutory deductions: {total_statutory_deduction}")
        print(f"CRA: {cra}")
        print(f"Taxable income: {taxable_income}")
        
        # Just before returning values
        print(f"Tax: {tax}, PAYE Total: {paye_total}, CRA: {cra}, Taxable Income: {taxable_income}, Total Statutory Deduction: {total_statutory_deduction}, Gross After Statutory Deduction: {gross_after_statutory_deduction}")
        
        return tax, paye_total, cra, taxable_income, total_statutory_deduction, gross_after_statutory_deduction

    
    def calculate_statutory_deductions(self, gross_pay, components):
        statutory_deductions = 0
        for salary_components in components:
            component_type = salary_components.component_type
            # print(f"Component Type: '{component_type}' (Length: {len(component_type)})")
            value = salary_components.value
            amount = salary_components.amount
            
            print(f"Calculating statutory deductions for gross payxxx: {gross_pay}")


            # Process only if component type is 'statutory_deduction'
            if component_type == 'Statutory deduction':
                print(f"returning value statutory deduction: {salary_components.component_name}")
                
                # Calculate deduction based on percentage or flat amount
                if value is not None:
                    deduction = gross_pay * (value / 100)
                    statutory_deductions += deduction
                    print(f"Deducting {salary_components.value}% ({deduction}) for {salary_components.component_name}")
                elif amount is not None:
                    statutory_deductions += amount
                    print(f"Deducting flat amount {salary_components.amount} for {salary_components.component_name}")

            else:
                # Skip components that are not statutory deductions
                print(f"Component {salary_components.component_name} is not a statutory deduction, skipping...")

        # Return gross pay after subtracting statutory deductions
            print(f"statutory_deductions Results: {statutory_deductions}")
        
            return statutory_deductions
                
    
    
    
    
    def calculate_netpay(self, gross_pay, components, final_monthly_tax, balance_after_tax_deduction):
        #get all components
        components = self.salary_component.fetchAll()
        
        #get all components sum by calling those functions that calculates them
        sum_of_total_monthly_taxable_earnings = self.calculate_taxable_earnings(gross_pay, components)  # Pass gross_pay
        total_non_taxable_earning = self.calculate_non_taxable_earnings(gross_pay, components)
        sum_of_statutory_deductions = self.calculate_statutory_deductions_2(gross_pay, components)
        sum_of_other_deductions = self.calculate_other_deductions(gross_pay, components)
        
        # print(f"sum_of_total_monthly_taxable_earnings: {sum_of_total_monthly_taxable_earnings} ({type(sum_of_total_monthly_taxable_earnings)})")
        # print(f"total_non_taxable_earning: {total_non_taxable_earning} ({type(total_non_taxable_earning)})")
        # print(f"sum_of_statutory_deductions: {sum_of_statutory_deductions} ({type(sum_of_statutory_deductions)})")
        # print(f"sum_of_other_deductions: {sum_of_other_deductions} ({type(sum_of_other_deductions)})")

        
        # balance_after_tax_deduction = self.calculate_paye(balance_after_tax_deduction)
        
        # total_earning_after_tax = sum_of_total_monthly_taxable_earnings(STC) - monthly_tax
        """
        here: balance_after_tax_deduction is being used temporaly pending when the data is being sent from the client side as 
        eg. annual_gross (after taxabel_earning is summed)
        """
        #1
        total_earning_after_tax = sum_of_total_monthly_taxable_earnings - final_monthly_tax
        
        #2
        total_sum_of_taxable_after_tax_and_sum_of_non_taxable_earnings = total_non_taxable_earning + total_earning_after_tax
        
        #3
        total_earnings_after_tax_and_other_deductions = total_sum_of_taxable_after_tax_and_sum_of_non_taxable_earnings - sum_of_other_deductions
        
        # calculate the netpay
         # Net pay formula breakdown:
        # [sum_of_taxable - monthly_tax] + sum_non_taxable_earnings - [sum_of_statutory_deductions + sum_of_other_deductions]
        net_pay = ((sum_of_total_monthly_taxable_earnings - final_monthly_tax) + total_non_taxable_earning) - (sum_of_statutory_deductions + sum_of_other_deductions)
        
        
        # print(f"Total Taxable Earnings: {sum_of_total_monthly_taxable_earnings}")
        # print(f"Total Non-Taxable Earnings: {total_non_taxable_earning}")
        # print(f"Total Statutory Deductions: {sum_of_statutory_deductions}")
        # print(f"Total Other Deductions: {sum_of_other_deductions}")
        # print(f"Final Monthly Tax: {final_monthly_tax}")
        # print(f"Net Pay: {net_pay}")
        return net_pay, total_earnings_after_tax_and_other_deductions
            
    
    def calculate_taxable_earnings(self, gross_pay, components):
            
        taxable_earnings = 0
        for salary_components in components:
            component_type = salary_components.component_type
            value = salary_components.value
            amount = salary_components.amount

            # Process only if component type is 'Taxable earning'
            if component_type == 'Taxable earning':
                # print(f"Returning taxable earnings for: {salary_components.component_name}")

                if value is not None:
                    earning = gross_pay * (value / 100)
                    taxable_earnings += earning
                    # print(f"Adding {salary_components.value}% ({earning}) for {salary_components.component_name}")
                elif amount is not None:
                    taxable_earnings += amount
                    # print(f"Adding flat amount {salary_components.amount} for {salary_components.component_name}")

        # print(f"Total Taxable Earnings: {taxable_earnings}")
        return taxable_earnings

    def calculate_non_taxable_earnings(self, gross_pay, components):
        non_taxable_earnings = 0
        for salary_components in components:
            component_type = salary_components.component_type
            value = salary_components.value
            amount = salary_components.amount

            # Process only if component type is 'Non-taxable earning'
            if component_type == 'Non-taxable earning':
                # print(f"Returning non-taxable earnings for: {salary_components.component_name}")

                if value is not None:
                    earning = gross_pay * (value / 100)
                    non_taxable_earnings += earning
                    # print(f"Adding {salary_components.value}% ({earning}) for {salary_components.component_name}")
                elif amount is not None:
                    non_taxable_earnings += amount
                    # print(f"Adding flat amount {salary_components.amount} for {salary_components.component_name}")

        # print(f"Total Non-Taxable Earnings: {non_taxable_earnings}")
        return non_taxable_earnings

    def calculate_statutory_deductions_2(self, gross_pay, components):
        statutory_sum = 0
        for salary_components in components:
            component_type = salary_components.component_type
            value = salary_components.value
            amount = salary_components.amount

            if component_type == 'Statutory deduction':
                # print(f"Returning statutory deductions for: {salary_components.component_name}")

                if value is not None:
                    deduction = gross_pay * (value / 100)
                    statutory_sum += deduction
                    # print(f"Adding {salary_components.value}% ({deduction}) for {salary_components.component_name}")
                elif amount is not None:
                    statutory_sum += amount
                    # print(f"Adding flat amount {salary_components.amount} for {salary_components.component_name}")

        # print(f"Total Statutory Deductions: {statutory_sum}")
        return statutory_sum

    def calculate_other_deductions(self, gross_pay, components):
        other_deductions = 0
        for salary_components in components:
            component_type = salary_components.component_type
            value = salary_components.value
            amount = salary_components.amount

            # Process only if component type is 'Other deductions'
            if component_type == 'Other deductions':
                # print(f"Returning other deductions for: {salary_components.component_name}")

                if value is not None:
                    deduction = gross_pay * (value / 100)
                    other_deductions += deduction
                    # print(f"Adding {salary_components.value}% ({deduction}) for {salary_components.component_name}")
                elif amount is not None:
                    other_deductions += amount
                    # print(f"Adding flat amount {salary_components.amount} for {salary_components.component_name}")

        # print(f"Total Other Deductions: {other_deductions}")
        return other_deductions

        





    








# class PayrollService:
#     def __init__(self):
#         self.employees = EmployeeRepository()
#         self.salary_component = SalaryComponentsRepository()
#         self.salary_template = SalaryTemplatesRepository()
#         self.tax_service = TaxCalculatorService("Nigeria")
#         self.current_date = datetime.now()

#     def create_payroll_schedule(self, payroll_period):
#         """
#         TODO: create a method to select active empoloyees
#         """
#         employees = self.employees.fetchAll()

#         for employee in employees:
#             salary_components = self._get_salary_components(employee.id)
#             salary_template = self._get_salary_template(employee.id)
#             annual_pen = self._get_pension_values(employee.id)
#             annual_nhf = self._get_nhf_values(employee.id)

#             payroll_details = self._calculate_payroll(employee.id, salary_components, salary_template, annual_pen, annual_nhf, payroll_period)

#             payroll_entry = PayrollDetailsRepository.createPayrollDetails(
#                 employee_id=employee.id,
#                 pay_after_statutory_deductions=payroll_details['pay_after_statutory_deductions'],
#                 gross_salary=payroll_details['gross_salary'],
#                 tax=payroll_details['tax'],
#                 cost_company=payroll_details['cost_company'],
#                 total_earnings=payroll_details['total_earnings'],
#                 total_deduction=payroll_details['total_deduction'],
#                 payment_status="Pending",
#                 pension=payroll_details['pension'],
#                 nhf=payroll_details['nhf'],
#                 month=payroll_period.month,
#                 year=payroll_period.year,
#                 org_id=employee.organisation_id
#             )

#             db.session.add(payroll_entry)
        
#         db.session.commit()

#     def _get_salary_components(self, employee_id):
#         query = text("""
#             SELECT sc.*
#             FROM salary_components sc
#             JOIN employee_salary_components esc ON sc.id = esc.salary_component_id
#             WHERE esc.employee_id = :employee_id
#         """)
    
#         result = db.session.execute(query, {'employee_id': employee_id})
#         return result.fetchall()

#     def _get_salary_template(self, employee_id):
#         query = text("""
#             SELECT st.*
#             FROM salary_templates st
#             JOIN employee_salary_details esd ON st.id = esd.salary_template_id
#             WHERE esd.employee_id = :employee_id
#             LIMIT 1
#         """)
    
#         result = db.session.execute(query, {'employee_id': employee_id})
#         return result.fetchone()

#     def _get_pension_values(self, employee_id):
#         query = text("""
#             SELECT id, pension_value
#             FROM employees
#             WHERE id = :id
#         """)
        
#         result = self.session.execute(query, {'id': employee_id})
#         return result.fetchone()
    
#     def _get_nhf_values(self, employee_id):
#         query = text("""
#             SELECT id, nhf_value 
#             FROM employees
#             WHERE id = :id
#         """)
        
#         result = self.session.execute(query, {'id': employee_id})
#         return result.fetchone()

#     def calculate_total_earnings(self, employee_id):
#         salary_components = self._get_salary_components(employee_id)
        
#         total_earnings = 0
#         for component in salary_components:
#             calculation_type = component['calculation_type']
#             component_type = component['component_type']
#             amount = component['amount']
#             value = component['value']
#             base_salary = self._get_base_salary(employee_id)
            
#             if calculation_type == 'fixed':
#                 total_earnings += amount
#             elif calculation_type == 'percentage':
                
#                 total_earnings += (base_salary * (amount / 100))
#             else:
#                 # Handle other calculation types if necessary
#                 pass
        
#         return total_earnings
    
#     def _get_base_salary(self, employee_id):
#         query = text("""
#             SELECT gross_pay
#             FROM employees
#             WHERE id = :employee_id
#         """)

#         result = db.execute(query, {'employee_id': employee_id}).fetchone()
#         return result['gross_pay'] if result else 0

#     def _calculate_payroll(self, employee_id, salary_components=None, salary_template=None, annual_pen=0, annual_nhf=0, payroll_period=None):
#         if salary_components is not None:
#             total_earnings = self._calculate_annual_earnings(salary_components)
#             total_deduction = self._calculate_statutory_deductions(salary_components)
        
#         if salary_template is not None:
#             total_earnings = self._calculate_annual_earnings(salary_template)
#             total_deduction = self._calculate_statutory_deductions(salary_template)
        
#         annual_earnings = self._calculate_annual_earnings(total_earnings)

#         gross_salary = self._get_base_salary(employee_id)
#         pay_after_statutory_deductions = self._calculate_pay_after_statutory_deductions(gross_salary)
#         cost_company = self._calculate_cost_to_company(gross_salary)
#         pension_nhf = self._calculate_pension_nhf(gross_salary)
#         annual_tax = self._calculate_annual_tax(annual_earnings, annual_pen, annual_nhf)
#         monthly_tax = self._calculate_monthly_tax(annual_tax)

#         return {
#             'employee_id': employee_id,
#             'gross_salary': gross_salary,
#             "approval_status": None,
#             'pay_after_statutory_deductions': pay_after_statutory_deductions,
#             'tax': monthly_tax,
#             'cost_company': cost_company,
#             'total_earnings': total_earnings,
#             'total_deduction': total_deduction,
#             "payment_status": None,
#             'pension': pension_nhf,
#             'nhf': pension_nhf,
#             'month': self.current_date.year,
#             'year': self.current_date.month,
#             'organisation_Id': 1
#         }

#     def calculate_total_earnings(self, employee_id):
#         """
#         TODO: calculate the sum from the totl sum based on the calculation type a componet
#         """
#         sql = text("""
#             SELECT 
#                 SUM(sc.amount) AS total_earnings
#             FROM 
#                 salary_components sc
#             JOIN 
#                 employee_salary_components esc ON sc.id = esc.salary_components_id
#             WHERE 
#                 esc.employee_id = :employee_id
#                 AND sc.calculation_type = 'earning';
#         """)
        
#         result = db.execute(sql, {'employee_id': employee_id}).fetchone()
#         return result['total_earnings'] if result else 0


#     # def _calculate_and_store_salary_components(self, employee_id):
#     #     sql = text("""
#     #         INSERT INTO payroll_details (
#     #             employee_id, 
#     #             component_id, 
#     #             gross_salary, 
#     #             pay_after_statutory_deductions, 
#     #             calculation_type, 
#     #             calculated_amount, 
#     #             month, 
#     #             year
#     #         )
#     #         SELECT 
#     #             :employee_id,
#     #             sc.id,
#     #             CASE 
#     #                 WHEN sc.calculation_type = 'fixed' THEN sc.amount
#     #                 WHEN sc.calculation_type = 'percentage' THEN (SELECT gross_pay FROM employees WHERE id = :employee_id) * (sc.amount / 100)
#     #                 ELSE 0 
#     #             END AS calculated_amount,
#     #             CASE 
#     #                 WHEN sc.calculation_type = 'fixed' THEN sc.amount
#     #                 WHEN sc.calculation_type = 'percentage' THEN (SELECT base_salary FROM employees WHERE id = :employee_id) * (sc.amount / 100)
#     #                 ELSE 0 
#     #             END AS pay_after_statutory_deductions,
#     #             sc.calculation_type,
#     #             NOW()::DATE AS month,
#     #             EXTRACT(YEAR FROM NOW()) AS year
#     #         FROM 
#     #             salary_components sc
#     #         JOIN 
#     #             employee_salary_components esc ON sc.id = esc.salary_components_id
#     #         WHERE 
#     #             esc.employee_id = :employee_id;
#     #     """)
        
#     #     db.session.execute(sql, {'employee_id': employee_id})
#     #     db.session.commit()

#     def _calculate_annual_earnings(self, total_earnings = 0):
#         return total_earnings * 12
    
#     def _calculate_statutory_deductions(self, salary_components):
#         return sum([component.amount for component in salary_components if component.calculation_type == 'deduction'])

#     def _calculate_monthly_tax(self):
#         return self.tax_service.calculate_monthly_tax(self._calculate_annual_tax)
    
#     def _calculate_annual_tax(self, annual_earnings, annual_pen, annual_nhf):
#         return self.tax_service.calculate_annual_tax(annual_earnings, annual_pen, annual_nhf)

#     def _calculate_pay_after_statutory_deductions(self, gross_salary):
#         pass

#     def _calculate_cost_to_company(self, gross_salary):
#         pass

#     def _calculate_pension_nhf(self, gross_salary):
#         pass




# # Example usage:
# # if __name__ == "__main__":
# #     engine = create_engine('your_database_url')
# #     Session = sessionmaker(bind=engine)
# #     session = Session()

# #     payroll_service = PayrollService(session)
# #     payroll_period = datetime(year=2024, month=8, day=1)

# #     payroll_service.create_payroll_schedule(payroll_period)
# #     payroll_service.process_payroll(payroll_period)