from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, func, case, text
from typing import Optional, List, Dict, Any
from uuid import UUID
from datetime import datetime, date, timedelta
from fastapi import HTTPException, status
import logging

from ...db.models.project import Project, Task, ProjectAssignment, TaskComment
from ...db.models.kanban import KanbanBoard, KanbanCard, KanbanColumn
from ...db.models.employee import Employee
from ...schemas.project import ProjectStatus, TaskStatus
from ...core.security import CurrentUser

logger = logging.getLogger(__name__)


class ProjectDashboardService:
    """Project Management Dashboard Service for comprehensive monitoring"""

    async def get_dashboard_overview(
        self,
        db: Session,
        current_user: CurrentUser,
        date_range: Optional[int] = 30  # days
    ) -> Dict[str, Any]:
        """Get comprehensive project management dashboard overview"""
        try:
            end_date = datetime.utcnow()
            start_date = end_date - timedelta(days=date_range)

            # Get basic project statistics
            project_stats = await self._get_project_statistics(db, current_user, start_date, end_date)
            
            # Get task statistics
            task_stats = await self._get_task_statistics(db, current_user, start_date, end_date)
            
            # Get kanban board statistics
            kanban_stats = await self._get_kanban_statistics(db, current_user, start_date, end_date)
            
            # Get overdue items
            overdue_items = await self._get_overdue_items(db, current_user)
            
            # Get recent activity
            recent_activity = await self._get_recent_activity(db, current_user, limit=20)
            
            # Get team performance metrics
            team_metrics = await self._get_team_performance_metrics(db, current_user, start_date, end_date)
            
            # Get project progress trends
            progress_trends = await self._get_progress_trends(db, current_user, start_date, end_date)

            return {
                "dashboard_type": "project_management",
                "date_range": {
                    "start_date": start_date.isoformat(),
                    "end_date": end_date.isoformat(),
                    "days": date_range
                },
                "project_statistics": project_stats,
                "task_statistics": task_stats,
                "kanban_statistics": kanban_stats,
                "overdue_items": overdue_items,
                "recent_activity": recent_activity,
                "team_metrics": team_metrics,
                "progress_trends": progress_trends,
                "generated_at": datetime.utcnow().isoformat()
            }

        except Exception as e:
            logger.error(f"Error getting project dashboard overview: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Error retrieving dashboard data"
            )

    async def _get_project_statistics(
        self,
        db: Session,
        current_user: CurrentUser,
        start_date: datetime,
        end_date: datetime
    ) -> Dict[str, Any]:
        """Get comprehensive project statistics"""
        
        # Base query for projects user can access
        base_query = db.query(Project).filter(
            Project.organization_id == current_user.organization_id,
            Project.is_active == True
        )

        # Role-based filtering
        if current_user.role.upper() == "MANAGER":
            # Managers see projects they manage or are assigned to
            base_query = base_query.filter(
                or_(
                    Project.project_manager_id == current_user.user_id,
                    Project.id.in_(
                        db.query(ProjectAssignment.project_id).filter(
                            ProjectAssignment.employee_id == current_user.user_id
                        )
                    )
                )
            )
        elif current_user.role.upper() == "EMPLOYEE":
            # Employees see only projects they're assigned to
            base_query = base_query.filter(
                Project.id.in_(
                    db.query(ProjectAssignment.project_id).filter(
                        ProjectAssignment.employee_id == current_user.user_id
                    )
                )
            )

        # Total projects
        total_projects = base_query.count()

        # Projects by status
        status_counts = db.query(
            Project.status,
            func.count(Project.id).label('count')
        ).filter(
            Project.organization_id == current_user.organization_id,
            Project.is_active == True
        ).group_by(Project.status).all()

        # Projects created in date range
        new_projects = base_query.filter(
            Project.created_at >= start_date,
            Project.created_at <= end_date
        ).count()

        # Completed projects in date range
        completed_projects = base_query.filter(
            Project.status == ProjectStatus.COMPLETED,
            Project.updated_at >= start_date,
            Project.updated_at <= end_date
        ).count()

        # Overdue projects
        overdue_projects = base_query.filter(
            Project.end_date < date.today(),
            Project.status.in_([ProjectStatus.PLANNING, ProjectStatus.IN_PROGRESS])
        ).count()

        # Average completion percentage
        avg_completion = db.query(
            func.avg(Project.progress_percentage)
        ).filter(
            Project.organization_id == current_user.organization_id,
            Project.is_active == True,
            Project.status != ProjectStatus.COMPLETED
        ).scalar() or 0

        return {
            "total_projects": total_projects,
            "new_projects": new_projects,
            "completed_projects": completed_projects,
            "overdue_projects": overdue_projects,
            "average_completion": round(float(avg_completion), 2),
            "status_breakdown": [
                {"status": status.value, "count": count}
                for status, count in status_counts
            ]
        }

    async def _get_task_statistics(
        self,
        db: Session,
        current_user: CurrentUser,
        start_date: datetime,
        end_date: datetime
    ) -> Dict[str, Any]:
        """Get comprehensive task statistics"""
        
        # Base query for tasks user can access
        base_query = db.query(Task).join(Project).filter(
            Project.organization_id == current_user.organization_id,
            Project.is_active == True
        )

        # Role-based filtering
        if current_user.role.upper() == "MANAGER":
            base_query = base_query.filter(
                or_(
                    Task.assignee_id == current_user.user_id,
                    Task.reporter_id == current_user.user_id,
                    Project.project_manager_id == current_user.user_id
                )
            )
        elif current_user.role.upper() == "EMPLOYEE":
            base_query = base_query.filter(
                or_(
                    Task.assignee_id == current_user.user_id,
                    Task.reporter_id == current_user.user_id
                )
            )

        # Total tasks
        total_tasks = base_query.count()

        # Tasks by status
        status_counts = base_query.with_entities(
            Task.status,
            func.count(Task.id).label('count')
        ).group_by(Task.status).all()

        # Tasks created in date range
        new_tasks = base_query.filter(
            Task.created_at >= start_date,
            Task.created_at <= end_date
        ).count()

        # Tasks completed in date range
        completed_tasks = base_query.filter(
            Task.status == TaskStatus.COMPLETED,
            Task.updated_at >= start_date,
            Task.updated_at <= end_date
        ).count()

        # Overdue tasks
        overdue_tasks = base_query.filter(
            Task.due_date < date.today(),
            Task.status.in_([TaskStatus.TODO, TaskStatus.IN_PROGRESS])
        ).count()

        # My tasks (for current user)
        my_tasks = base_query.filter(
            Task.assignee_id == current_user.user_id
        ).count()

        return {
            "total_tasks": total_tasks,
            "my_tasks": my_tasks,
            "new_tasks": new_tasks,
            "completed_tasks": completed_tasks,
            "overdue_tasks": overdue_tasks,
            "status_breakdown": [
                {"status": status.value, "count": count}
                for status, count in status_counts
            ]
        }

    async def _get_kanban_statistics(
        self,
        db: Session,
        current_user: CurrentUser,
        start_date: datetime,
        end_date: datetime
    ) -> Dict[str, Any]:
        """Get kanban board statistics"""
        
        # Base query for boards user can access
        base_query = db.query(KanbanBoard).filter(
            KanbanBoard.organization_id == current_user.organization_id
        )

        # Total boards
        total_boards = base_query.count()

        # Active boards (with recent activity)
        active_boards = base_query.join(KanbanColumn).join(KanbanCard).filter(
            KanbanCard.updated_at >= start_date
        ).distinct().count()

        # Total cards
        total_cards = db.query(KanbanCard).join(KanbanColumn).join(KanbanBoard).filter(
            KanbanBoard.organization_id == current_user.organization_id
        ).count()

        # Cards with overdue dates
        overdue_cards = db.query(KanbanCard).join(KanbanColumn).join(KanbanBoard).filter(
            KanbanBoard.organization_id == current_user.organization_id,
            KanbanCard.due_date < datetime.utcnow(),
            KanbanCard.completed_date.is_(None)
        ).count()

        return {
            "total_boards": total_boards,
            "active_boards": active_boards,
            "total_cards": total_cards,
            "overdue_cards": overdue_cards
        }

    async def _get_overdue_items(
        self,
        db: Session,
        current_user: CurrentUser
    ) -> Dict[str, Any]:
        """Get all overdue items (projects, tasks, cards)"""

        today = date.today()

        # Overdue projects
        overdue_projects = db.query(Project).filter(
            Project.organization_id == current_user.organization_id,
            Project.end_date < today,
            Project.status.in_([ProjectStatus.PLANNING, ProjectStatus.IN_PROGRESS]),
            Project.is_active == True
        ).limit(10).all()

        # Overdue tasks
        overdue_tasks = db.query(Task).join(Project).filter(
            Project.organization_id == current_user.organization_id,
            Task.due_date < today,
            Task.status.in_([TaskStatus.TODO, TaskStatus.IN_PROGRESS])
        ).limit(10).all()

        # Overdue kanban cards
        overdue_cards = db.query(KanbanCard).join(KanbanColumn).join(KanbanBoard).filter(
            KanbanBoard.organization_id == current_user.organization_id,
            KanbanCard.due_date < datetime.utcnow(),
            KanbanCard.completed_date.is_(None)
        ).limit(10).all()

        return {
            "projects": [
                {
                    "id": str(project.id),
                    "name": project.name,
                    "due_date": project.end_date.isoformat() if project.end_date else None,
                    "days_overdue": (today - project.end_date).days if project.end_date else 0,
                    "status": project.status.value,
                    "progress": float(project.progress_percentage or 0)
                }
                for project in overdue_projects
            ],
            "tasks": [
                {
                    "id": str(task.id),
                    "title": task.title,
                    "due_date": task.due_date.isoformat() if task.due_date else None,
                    "days_overdue": (today - task.due_date).days if task.due_date else 0,
                    "status": task.status.value,
                    "project_name": task.project.name if task.project else None
                }
                for task in overdue_tasks
            ],
            "cards": [
                {
                    "id": str(card.id),
                    "title": card.title,
                    "due_date": card.due_date.isoformat() if card.due_date else None,
                    "days_overdue": (datetime.utcnow().date() - card.due_date.date()).days if card.due_date else 0,
                    "board_name": card.column.board.name if card.column and card.column.board else None,
                    "column_name": card.column.name if card.column else None
                }
                for card in overdue_cards
            ]
        }

    async def _get_recent_activity(
        self,
        db: Session,
        current_user: CurrentUser,
        limit: int = 20
    ) -> List[Dict[str, Any]]:
        """Get recent project management activity"""

        activities = []

        # Recent project updates
        recent_projects = db.query(Project).filter(
            Project.organization_id == current_user.organization_id,
            Project.updated_at >= datetime.utcnow() - timedelta(days=7)
        ).order_by(Project.updated_at.desc()).limit(5).all()

        for project in recent_projects:
            activities.append({
                "type": "project_update",
                "timestamp": project.updated_at.isoformat(),
                "title": f"Project '{project.name}' updated",
                "description": f"Status: {project.status.value}",
                "entity_id": str(project.id),
                "entity_type": "project"
            })

        # Recent task updates
        recent_tasks = db.query(Task).join(Project).filter(
            Project.organization_id == current_user.organization_id,
            Task.updated_at >= datetime.utcnow() - timedelta(days=7)
        ).order_by(Task.updated_at.desc()).limit(10).all()

        for task in recent_tasks:
            activities.append({
                "type": "task_update",
                "timestamp": task.updated_at.isoformat(),
                "title": f"Task '{task.title}' updated",
                "description": f"Status: {task.status.value}",
                "entity_id": str(task.id),
                "entity_type": "task"
            })

        # Recent kanban card updates
        recent_cards = db.query(KanbanCard).join(KanbanColumn).join(KanbanBoard).filter(
            KanbanBoard.organization_id == current_user.organization_id,
            KanbanCard.updated_at >= datetime.utcnow() - timedelta(days=7)
        ).order_by(KanbanCard.updated_at.desc()).limit(10).all()

        for card in recent_cards:
            activities.append({
                "type": "card_update",
                "timestamp": card.updated_at.isoformat(),
                "title": f"Card '{card.title}' updated",
                "description": f"Board: {card.column.board.name if card.column and card.column.board else 'Unknown'}",
                "entity_id": str(card.id),
                "entity_type": "kanban_card"
            })

        # Sort all activities by timestamp and limit
        activities.sort(key=lambda x: x["timestamp"], reverse=True)
        return activities[:limit]

    async def _get_team_performance_metrics(
        self,
        db: Session,
        current_user: CurrentUser,
        start_date: datetime,
        end_date: datetime
    ) -> Dict[str, Any]:
        """Get team performance metrics"""

        # Get team members based on role
        if current_user.role.upper() == "SUPER_ADMIN":
            # SuperAdmin sees all employees
            team_members = db.query(Employee).filter(
                Employee.organization_id == current_user.organization_id,
                Employee.is_active == True
            ).all()
        elif current_user.role.upper() == "MANAGER":
            # Manager sees their team
            team_members = db.query(Employee).filter(
                Employee.organization_id == current_user.organization_id,
                Employee.manager_id == current_user.user_id,
                Employee.is_active == True
            ).all()
        else:
            # Employee sees only themselves
            team_members = db.query(Employee).filter(
                Employee.id == current_user.user_id
            ).all()

        team_metrics = []

        for member in team_members:
            # Get member's task statistics
            member_tasks = db.query(Task).filter(
                Task.assignee_id == member.id,
                Task.updated_at >= start_date,
                Task.updated_at <= end_date
            ).all()

            completed_tasks = len([t for t in member_tasks if t.status == TaskStatus.COMPLETED])
            total_tasks = len(member_tasks)

            # Get member's project involvement
            member_projects = db.query(Project).join(ProjectAssignment).filter(
                ProjectAssignment.employee_id == member.id,
                Project.updated_at >= start_date,
                Project.updated_at <= end_date
            ).count()

            team_metrics.append({
                "employee_id": str(member.id),
                "employee_name": f"{member.first_name} {member.last_name}",
                "email": member.email,
                "total_tasks": total_tasks,
                "completed_tasks": completed_tasks,
                "completion_rate": round((completed_tasks / total_tasks * 100) if total_tasks > 0 else 0, 2),
                "active_projects": member_projects
            })

        return {
            "team_size": len(team_members),
            "metrics": team_metrics,
            "period": {
                "start_date": start_date.isoformat(),
                "end_date": end_date.isoformat()
            }
        }

    async def _get_progress_trends(
        self,
        db: Session,
        current_user: CurrentUser,
        start_date: datetime,
        end_date: datetime
    ) -> Dict[str, Any]:
        """Get project progress trends over time"""

        # Generate weekly data points
        trends = []
        current_date = start_date

        while current_date <= end_date:
            week_end = min(current_date + timedelta(days=7), end_date)

            # Projects completed in this week
            completed_projects = db.query(Project).filter(
                Project.organization_id == current_user.organization_id,
                Project.status == ProjectStatus.COMPLETED,
                Project.updated_at >= current_date,
                Project.updated_at < week_end
            ).count()

            # Tasks completed in this week
            completed_tasks = db.query(Task).join(Project).filter(
                Project.organization_id == current_user.organization_id,
                Task.status == TaskStatus.COMPLETED,
                Task.updated_at >= current_date,
                Task.updated_at < week_end
            ).count()

            # New projects started
            new_projects = db.query(Project).filter(
                Project.organization_id == current_user.organization_id,
                Project.created_at >= current_date,
                Project.created_at < week_end
            ).count()

            trends.append({
                "week_start": current_date.isoformat(),
                "week_end": week_end.isoformat(),
                "completed_projects": completed_projects,
                "completed_tasks": completed_tasks,
                "new_projects": new_projects
            })

            current_date = week_end

        return {
            "trends": trends,
            "period": {
                "start_date": start_date.isoformat(),
                "end_date": end_date.isoformat()
            }
        }

    async def get_project_details(
        self,
        db: Session,
        project_id: UUID,
        current_user: CurrentUser
    ) -> Dict[str, Any]:
        """Get detailed project information for monitoring"""

        project = db.query(Project).filter(
            Project.id == project_id,
            Project.organization_id == current_user.organization_id
        ).first()

        if not project:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Project not found"
            )

        # Get project tasks
        tasks = db.query(Task).filter(Task.project_id == project_id).all()

        # Get project team
        team_members = db.query(Employee).join(ProjectAssignment).filter(
            ProjectAssignment.project_id == project_id
        ).all()

        # Get kanban boards for this project
        kanban_boards = db.query(KanbanBoard).filter(
            KanbanBoard.project_id == project_id
        ).all()

        return {
            "project": {
                "id": str(project.id),
                "name": project.name,
                "description": project.description,
                "status": project.status.value,
                "progress": float(project.progress_percentage or 0),
                "start_date": project.start_date.isoformat() if project.start_date else None,
                "end_date": project.end_date.isoformat() if project.end_date else None,
                "budget": float(project.budget) if project.budget else None,
                "actual_cost": float(project.actual_cost) if project.actual_cost else None
            },
            "tasks": {
                "total": len(tasks),
                "completed": len([t for t in tasks if t.status == TaskStatus.COMPLETED]),
                "in_progress": len([t for t in tasks if t.status == TaskStatus.IN_PROGRESS]),
                "todo": len([t for t in tasks if t.status == TaskStatus.TODO])
            },
            "team": {
                "size": len(team_members),
                "members": [
                    {
                        "id": str(member.id),
                        "name": f"{member.first_name} {member.last_name}",
                        "email": member.email
                    }
                    for member in team_members
                ]
            },
            "kanban_boards": [
                {
                    "id": str(board.id),
                    "name": board.name,
                    "description": board.description
                }
                for board in kanban_boards
            ]
        }
