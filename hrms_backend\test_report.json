{"test_summary": {"total_tests": 16, "passed_tests": 9, "failed_tests": 7, "success_rate": 56.25}, "database_info": {"host": "localhost", "port": 5432, "database": "agnoconnect_hrms", "schema": "public"}, "test_results": [{"test_name": "Database Connection", "success": true, "message": "Connected to PostgreSQL", "timestamp": "2025-07-01T12:40:14.922053", "data": null}, {"test_name": "PostgreSQL Version", "success": true, "message": "Version: PostgreSQL 16.8, compiled by Visual C++ build 1942, 64-bit", "timestamp": "2025-07-01T12:40:14.923054", "data": null}, {"test_name": "Current Database", "success": true, "message": "Database: agnoconnect_hrms", "timestamp": "2025-07-01T12:40:14.925053", "data": null}, {"test_name": "Current Schema", "success": true, "message": "Schema: public", "timestamp": "2025-07-01T12:40:14.925053", "data": null}, {"test_name": "Table Creation", "success": true, "message": "All tables created successfully", "timestamp": "2025-07-01T12:40:15.021053", "data": null}, {"test_name": "Required Tables", "success": true, "message": "All 13 required tables exist", "timestamp": "2025-07-01T12:40:15.023053", "data": null}, {"test_name": "Ticket Table Structure", "success": true, "message": "All required columns present", "timestamp": "2025-07-01T12:40:15.037053", "data": null}, {"test_name": "Test Data Creation", "success": false, "message": "Error: Could not determine join condition between parent/child tables on relationship Organization.employees - there are no foreign keys linking these tables.  Ensure that referencing columns are associated with a ForeignKey or ForeignKeyConstraint, or specify a 'primaryjoin' expression.", "timestamp": "2025-07-01T12:40:15.039054", "data": null}, {"test_name": "Ticket CRUD Operations", "success": false, "message": "Error: 'employee_id'", "timestamp": "2025-07-01T12:40:15.040062", "data": null}, {"test_name": "Ticket Relationships", "success": false, "message": "Error: 'ticket_id'", "timestamp": "2025-07-01T12:40:15.041055", "data": null}, {"test_name": "Ticket Management Features", "success": false, "message": "Error: 'organization_id'", "timestamp": "2025-07-01T12:40:15.042054", "data": null}, {"test_name": "Unique Constraint Test", "success": true, "message": "Duplicate ticket number correctly rejected", "timestamp": "2025-07-01T12:40:15.042054", "data": null}, {"test_name": "Foreign Key Constraint Test", "success": true, "message": "Invalid foreign key correctly rejected", "timestamp": "2025-07-01T12:40:15.043054", "data": null}, {"test_name": "Data Integrity Test", "success": false, "message": "Error: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'mapped class Organization->organizations'. Original exception was: Could not determine join condition between parent/child tables on relationship Organization.employees - there are no foreign keys linking these tables.  Ensure that referencing columns are associated with a ForeignKey or ForeignKeyConstraint, or specify a 'primaryjoin' expression.", "timestamp": "2025-07-01T12:40:15.043054", "data": null}, {"test_name": "Performance Queries", "success": false, "message": "Error: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'mapped class Organization->organizations'. Original exception was: Could not determine join condition between parent/child tables on relationship Organization.employees - there are no foreign keys linking these tables.  Ensure that referencing columns are associated with a ForeignKey or ForeignKeyConstraint, or specify a 'primaryjoin' expression.", "timestamp": "2025-07-01T12:40:15.045053", "data": null}, {"test_name": "Test Data Cleanup", "success": false, "message": "Error: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'mapped class Organization->organizations'. Original exception was: Could not determine join condition between parent/child tables on relationship Organization.employees - there are no foreign keys linking these tables.  Ensure that referencing columns are associated with a ForeignKey or ForeignKeyConstraint, or specify a 'primaryjoin' expression.", "timestamp": "2025-07-01T12:40:15.046053", "data": null}], "generated_at": "2025-07-01T12:40:15.047053"}