from pydantic import BaseModel, <PERSON>, validator
from typing import Optional, List
from uuid import UUID
from datetime import date, datetime
from decimal import Decimal
from enum import Enum


class ProjectStatus(str, Enum):
    PLANNING = "planning"
    ACTIVE = "active"
    ON_HOLD = "on_hold"
    COMPLETED = "completed"
    CANCELLED = "cancelled"
    ARCHIVED = "archived"


class ProjectPriority(str, Enum):
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class TaskStatus(str, Enum):
    TODO = "todo"
    IN_PROGRESS = "in_progress"
    IN_REVIEW = "in_review"
    TESTING = "testing"
    DONE = "done"
    BLOCKED = "blocked"
    CANCELLED = "cancelled"


class TaskPriority(str, Enum):
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    URGENT = "urgent"


# Project Schemas
class ProjectBase(BaseModel):
    name: str = Field(..., min_length=1, max_length=200)
    description: Optional[str] = None
    status: ProjectStatus = ProjectStatus.PLANNING
    priority: ProjectPriority = ProjectPriority.MEDIUM
    start_date: Optional[date] = None
    end_date: Optional[date] = None
    estimated_hours: Optional[Decimal] = Field(None, ge=0)
    budget: Optional[Decimal] = Field(None, ge=0)
    client_name: Optional[str] = Field(None, max_length=200)
    client_contact: Optional[str] = Field(None, max_length=255)
    is_billable: bool = True
    hourly_rate: Optional[Decimal] = Field(None, ge=0)

    @validator('end_date')
    def end_date_must_be_after_start_date(cls, v, values):
        if 'start_date' in values and values['start_date'] and v and v < values['start_date']:
            raise ValueError('End date must be after start date')
        return v


class ProjectCreate(ProjectBase):
    code: str = Field(..., min_length=1, max_length=50)
    project_manager_id: Optional[UUID] = None
    department_id: Optional[UUID] = None


class ProjectUpdate(BaseModel):
    name: Optional[str] = Field(None, min_length=1, max_length=200)
    description: Optional[str] = None
    status: Optional[ProjectStatus] = None
    priority: Optional[ProjectPriority] = None
    start_date: Optional[date] = None
    end_date: Optional[date] = None
    estimated_hours: Optional[Decimal] = Field(None, ge=0)
    budget: Optional[Decimal] = Field(None, ge=0)
    client_name: Optional[str] = Field(None, max_length=200)
    client_contact: Optional[str] = Field(None, max_length=255)
    is_billable: Optional[bool] = None
    hourly_rate: Optional[Decimal] = Field(None, ge=0)
    project_manager_id: Optional[UUID] = None
    department_id: Optional[UUID] = None
    progress_percentage: Optional[Decimal] = Field(None, ge=0, le=100)


class ProjectResponse(ProjectBase):
    id: UUID
    code: str
    organization_id: UUID
    project_manager_id: Optional[UUID] = None
    department_id: Optional[UUID] = None
    actual_hours: Optional[Decimal] = None
    actual_cost: Optional[Decimal] = None
    progress_percentage: Optional[Decimal] = None
    created_at: datetime
    updated_at: datetime
    is_active: bool

    class Config:
        from_attributes = True


class ProjectListResponse(BaseModel):
    projects: List[ProjectResponse]
    total: int
    skip: int
    limit: int


# Project Assignment Schemas
class ProjectAssignmentBase(BaseModel):
    role: Optional[str] = Field(None, max_length=100)
    start_date: date
    end_date: Optional[date] = None
    allocation_percentage: Decimal = Field(100, ge=0, le=100)
    hourly_rate: Optional[Decimal] = Field(None, ge=0)
    can_edit_tasks: bool = False
    can_view_budget: bool = False
    can_approve_timesheets: bool = False
    is_lead: bool = False


class ProjectAssignmentCreate(ProjectAssignmentBase):
    employee_id: UUID


class ProjectAssignmentUpdate(BaseModel):
    role: Optional[str] = Field(None, max_length=100)
    end_date: Optional[date] = None
    allocation_percentage: Optional[Decimal] = Field(None, ge=0, le=100)
    hourly_rate: Optional[Decimal] = Field(None, ge=0)
    can_edit_tasks: Optional[bool] = None
    can_view_budget: Optional[bool] = None
    can_approve_timesheets: Optional[bool] = None
    is_lead: Optional[bool] = None


class ProjectAssignmentResponse(ProjectAssignmentBase):
    id: UUID
    project_id: UUID
    employee_id: UUID
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


# Task Schemas
class TaskBase(BaseModel):
    title: str = Field(..., min_length=1, max_length=200)
    description: Optional[str] = None
    status: TaskStatus = TaskStatus.TODO
    priority: TaskPriority = TaskPriority.MEDIUM
    start_date: Optional[date] = None
    due_date: Optional[date] = None
    estimated_hours: Optional[Decimal] = Field(None, ge=0)
    labels: Optional[List[str]] = None
    tags: Optional[List[str]] = None

    @validator('due_date')
    def due_date_must_be_after_start_date(cls, v, values):
        if 'start_date' in values and values['start_date'] and v and v < values['start_date']:
            raise ValueError('Due date must be after start date')
        return v


class TaskCreate(TaskBase):
    project_id: UUID
    parent_task_id: Optional[UUID] = None
    assignee_id: Optional[UUID] = None
    attachment_urls: Optional[List[str]] = None


class TaskUpdate(BaseModel):
    title: Optional[str] = Field(None, min_length=1, max_length=200)
    description: Optional[str] = None
    status: Optional[TaskStatus] = None
    priority: Optional[TaskPriority] = None
    start_date: Optional[date] = None
    due_date: Optional[date] = None
    estimated_hours: Optional[Decimal] = Field(None, ge=0)
    assignee_id: Optional[UUID] = None
    parent_task_id: Optional[UUID] = None
    progress_percentage: Optional[Decimal] = Field(None, ge=0, le=100)
    labels: Optional[List[str]] = None
    tags: Optional[List[str]] = None
    attachment_urls: Optional[List[str]] = None


class TaskResponse(TaskBase):
    id: UUID
    task_number: Optional[str] = None
    project_id: UUID
    parent_task_id: Optional[UUID] = None
    assignee_id: Optional[UUID] = None
    reporter_id: Optional[UUID] = None
    actual_hours: Optional[Decimal] = None
    progress_percentage: Optional[Decimal] = None
    attachment_urls: Optional[List[str]] = None
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class TaskListResponse(BaseModel):
    tasks: List[TaskResponse]
    total: int
    skip: int
    limit: int


# Task Comment Schemas
class TaskCommentBase(BaseModel):
    content: str = Field(..., min_length=1)
    attachment_urls: Optional[List[str]] = None


class TaskCommentCreate(TaskCommentBase):
    parent_comment_id: Optional[UUID] = None


class TaskCommentUpdate(BaseModel):
    content: Optional[str] = Field(None, min_length=1)
    attachment_urls: Optional[List[str]] = None


class TaskCommentResponse(TaskCommentBase):
    id: UUID
    task_id: UUID
    author_id: UUID
    parent_comment_id: Optional[UUID] = None
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


# Task Dependency Schemas
class TaskDependencyCreate(BaseModel):
    depends_on_task_id: UUID
    dependency_type: str = Field("finish_to_start", pattern="^(finish_to_start|start_to_start|finish_to_finish|start_to_finish)$")


class TaskDependencyResponse(BaseModel):
    id: UUID
    task_id: UUID
    depends_on_task_id: UUID
    dependency_type: str
    created_at: datetime

    class Config:
        from_attributes = True


# Project Milestone Schemas
class ProjectMilestoneBase(BaseModel):
    name: str = Field(..., min_length=1, max_length=200)
    description: Optional[str] = None
    due_date: date


class ProjectMilestoneCreate(ProjectMilestoneBase):
    depends_on_tasks: Optional[List[UUID]] = None


class ProjectMilestoneUpdate(BaseModel):
    name: Optional[str] = Field(None, min_length=1, max_length=200)
    description: Optional[str] = None
    due_date: Optional[date] = None
    depends_on_tasks: Optional[List[UUID]] = None
    is_completed: Optional[bool] = None
    completed_date: Optional[date] = None


class ProjectMilestoneResponse(ProjectMilestoneBase):
    id: UUID
    project_id: UUID
    completed_date: Optional[date] = None
    is_completed: bool
    depends_on_tasks: Optional[List[UUID]] = None
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


# Bulk Operations
class BulkTaskUpdate(BaseModel):
    task_ids: List[UUID]
    status: Optional[TaskStatus] = None
    assignee_id: Optional[UUID] = None
    priority: Optional[TaskPriority] = None
    labels: Optional[List[str]] = None


class TaskAssignmentRequest(BaseModel):
    task_ids: List[UUID]
    assignee_id: UUID
    notify_assignee: bool = True
