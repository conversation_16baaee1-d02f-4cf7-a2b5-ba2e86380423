#!/usr/bin/env python3
"""
Check what users exist in the database
"""

import psycopg2
import os
from dotenv import load_dotenv

load_dotenv()

def check_users():
    """Check what users exist"""
    
    conn = psycopg2.connect(
        host=os.getenv("DB_HOST", "localhost"),
        database=os.getenv("DB_NAME", "hrms_db"),
        user=os.getenv("DB_USER", "postgres"),
        password=os.getenv("DB_PASSWORD", "password"),
        port=os.getenv("DB_PORT", "5432")
    )
    
    try:
        cursor = conn.cursor()
        
        # Check users table
        cursor.execute("SELECT email, role, is_active FROM users LIMIT 10")
        users = cursor.fetchall()
        print("Users in database:")
        for user in users:
            print(f"  - {user[0]} (role: {user[1]}, active: {user[2]})")
        
        if not users:
            print("No users found in database")
            
        # Check employees table
        cursor.execute("SELECT email, role, is_active FROM employees LIMIT 10")
        employees = cursor.fetchall()
        print("\nEmployees in database:")
        for emp in employees:
            print(f"  - {emp[0]} (role: {emp[1]}, active: {emp[2]})")
            
        if not employees:
            print("No employees found in database")
        
    except Exception as e:
        print(f"Error checking users: {e}")
    finally:
        cursor.close()
        conn.close()

if __name__ == "__main__":
    check_users()
