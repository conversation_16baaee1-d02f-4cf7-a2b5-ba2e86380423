from core.models.pay_schedules import PaySchedulesModel
from core.repositories.pay_schedules import PaySchedulesRepository
from core.repositories.user import UserRepository

class PaySchedulesService:
    def __init__(self) -> None:
        self.repository = PaySchedulesRepository()

    def createPaySchedules(self, Kwargs):
        return self.repository.createPaySchedules(**Kwargs)
    
    def getAllPaySchedule(self, page=1, limit=10):
        pay_schedule = self.repository.fetchPaySchedule(page, limit)
        total_schedule = PaySchedulesModel.query.filter_by(user_id=UserRepository().authUserId()).count()
        return pay_schedule, total_schedule
    
    def getPaySchedule(self, id):
        return self.repository.getPaySchedules(id)
    
    def updatePaySchedules(self, id, Kwargs):
        return self.repository.updatePaySchedules(id, **Kwargs)
    
    def getPaySchedulesByKey(self, Kwarg):
        return self.repository.getPaySchedulesByKeys(Kwarg)
    
    def deletePaySchedules(self, id):
        return self.repository.deletePaySchedules(id)
    
    def hasUnapprovedPaySchedule(self):
        return self.repository.hasUnapprovedPaySchedule()

    
    