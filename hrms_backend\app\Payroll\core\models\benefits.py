from core.databases.database import db
from core.models.basemodel import ModelBase
from datetime import datetime

class BenefitsModel(ModelBase):
    __tablename__ = "benefits"

    id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    benefit_name = db.Column(db.String(100), nullable=False)
    payslip_name = db.Column(db.String(45), nullable=False)
    component_type = db.Column(db.String(45), nullable=False)
    calculation_type = db.Column(db.String(45), nullable=False)
    duration = db.Column(db.String(45), nullable=True)
    cycle = db.Column(db.String(45), nullable=True)
    amount = db.Column(db.Float, nullable=True)
    value = db.Column(db.Float, nullable=True)
    preview_payslip = db.Column(db.Bo<PERSON>an, default=False, nullable=False)
    user_id = db.Column(db.Integer, db.<PERSON><PERSON><PERSON>((ModelBase.dbSchema() + '.users.id')), nullable=False) 
    timestamp = db.Column(db.DateTime, nullable=False, default=datetime.now()) 
    salary_template_benefits = db.relationship('SalaryTemplateBenefitsPivotModel', back_populates='benefit')
    employee_benefits = db.relationship('EmployeeBenefitsPivotModel', back_populates='benefit')

    # template_id = db.Column(db.Integer, db.ForeignKey((ModelBase.dbSchema() + '.salary_templates.id')))
 