from fastapi import APIRouter, HTTPException, status, Depends, Query
from sqlalchemy.orm import Session
from typing import List, Optional
from uuid import UUID

from ..core.security import get_current_user, require_permission, CurrentUser
from ..core.rbac import Permissions
from ..db.session import get_db
from ..schemas.engagement import (
    SurveyCreate, SurveyUpdate, SurveyResponse, SurveyListResponse,
    QuestionCreate, QuestionUpdate, QuestionResponse,
    SurveyResponseCreate, SurveyResponseUpdate, SurveyResponseResponse,
    AnswerCreate, AnswerUpdate, AnswerResponse, FeedbackCreate,
    FeedbackUpdate, FeedbackResponse, FeedbackListResponse,
    RecognitionCreate, RecognitionUpdate, RecognitionResponse,
    RecognitionListResponse, BadgeCreate, BadgeUpdate, BadgeResponse,
    SurveyAnalytics, EngagementMetrics, EngagementDashboard,
    BulkSurveyInvite, BulkRecognition, SurveyStatus, SurveyType
)
from ..services.hr_management.engagement_service import EngagementService

router = APIRouter()
engagement_service = EngagementService()


# Dashboard endpoint
@router.get("/dashboard", response_model=EngagementDashboard)
async def get_engagement_dashboard(
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.ENGAGEMENT_READ))
):
    """Get engagement dashboard"""
    return await engagement_service.get_engagement_dashboard(db, current_user)


# Survey endpoints
@router.get("/surveys", response_model=SurveyListResponse)
async def get_surveys(
    survey_type: Optional[SurveyType] = Query(None),
    status: Optional[SurveyStatus] = Query(None),
    search: Optional[str] = Query(None),
    skip: int = Query(0, ge=0),
    limit: int = Query(20, ge=1, le=100),
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.ENGAGEMENT_READ))
):
    """Get surveys with filtering"""
    return await engagement_service.get_surveys(
        db=db,
        survey_type=survey_type,
        status=status,
        search=search,
        skip=skip,
        limit=limit,
        current_user=current_user
    )


@router.post("/surveys", response_model=SurveyResponse)
async def create_survey(
    survey_data: SurveyCreate,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.ENGAGEMENT_CREATE))
):
    """Create new survey"""
    return await engagement_service.create_survey(db, survey_data, current_user)


@router.get("/surveys/{survey_id}", response_model=SurveyResponse)
async def get_survey(
    survey_id: UUID,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.ENGAGEMENT_READ))
):
    """Get survey by ID"""
    survey = await engagement_service.get_survey_by_id(db, survey_id, current_user)
    if not survey:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Survey not found"
        )
    return survey


@router.put("/surveys/{survey_id}", response_model=SurveyResponse)
async def update_survey(
    survey_id: UUID,
    survey_data: SurveyUpdate,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.ENGAGEMENT_UPDATE))
):
    """Update survey"""
    survey = await engagement_service.update_survey(db, survey_id, survey_data, current_user)
    if not survey:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Survey not found"
        )
    return survey


@router.get("/surveys/{survey_id}/analytics", response_model=SurveyAnalytics)
async def get_survey_analytics(
    survey_id: UUID,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.ENGAGEMENT_READ))
):
    """Get survey analytics"""
    analytics = await engagement_service.get_survey_analytics(db, survey_id, current_user)
    if not analytics:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Survey not found"
        )
    return analytics


# Recognition endpoints
@router.get("/recognitions", response_model=RecognitionListResponse)
async def get_recognitions(
    recipient_id: Optional[UUID] = Query(None),
    giver_id: Optional[UUID] = Query(None),
    recognition_type: Optional[str] = Query(None),
    skip: int = Query(0, ge=0),
    limit: int = Query(20, ge=1, le=100),
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.ENGAGEMENT_READ))
):
    """Get recognitions with filtering"""
    return await engagement_service.get_recognitions(
        db=db,
        recipient_id=recipient_id,
        giver_id=giver_id,
        recognition_type=recognition_type,
        skip=skip,
        limit=limit,
        current_user=current_user
    )


@router.post("/recognitions", response_model=RecognitionResponse)
async def create_recognition(
    recognition_data: RecognitionCreate,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.ENGAGEMENT_CREATE))
):
    """Create employee recognition"""
    return await engagement_service.create_recognition(db, recognition_data, current_user)


# Feedback endpoints
@router.get("/feedback", response_model=FeedbackListResponse)
async def get_feedback(
    category: Optional[str] = Query(None),
    status: Optional[str] = Query(None),
    recipient_id: Optional[UUID] = Query(None),
    skip: int = Query(0, ge=0),
    limit: int = Query(20, ge=1, le=100),
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.ENGAGEMENT_READ))
):
    """Get feedback with filtering"""
    return await engagement_service.get_feedback(
        db=db,
        category=category,
        status=status,
        recipient_id=recipient_id,
        skip=skip,
        limit=limit,
        current_user=current_user
    )


@router.post("/feedback", response_model=FeedbackResponse)
async def create_feedback(
    feedback_data: FeedbackCreate,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(get_current_user)
):
    """Create feedback"""
    return await engagement_service.create_feedback(db, feedback_data, current_user)


# Badge endpoints
@router.get("/badges", response_model=List[BadgeResponse])
async def get_badges(
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.ENGAGEMENT_READ))
):
    """Get badges"""
    return await engagement_service.get_badges(db, current_user)


@router.post("/badges", response_model=BadgeResponse)
async def create_badge(
    badge_data: BadgeCreate,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.ENGAGEMENT_CREATE))
):
    """Create badge"""
    return await engagement_service.create_badge(db, badge_data, current_user)
