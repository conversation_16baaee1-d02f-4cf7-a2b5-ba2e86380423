/**
 * Leave Balance Tracking Component
 * Features: Real-time balance, accrual tracking, encashment options
 */

import React, { useState, useEffect } from 'react';
import {
  TrendingUp,
  Calendar,
  DollarSign,
  Clock,
  Plus,
  Minus,
  RefreshCw,
  Download,
  Eye,
  AlertTriangle
} from 'lucide-react';
import apiService from '../../services/api';

const LeaveBalanceTracker = ({ employeeId, isMyBalance = true }) => {
  const [balances, setBalances] = useState([]);
  const [loading, setLoading] = useState(true);
  const [selectedYear, setSelectedYear] = useState(new Date().getFullYear());
  const [showEncashmentModal, setShowEncashmentModal] = useState(false);
  const [selectedBalance, setSelectedBalance] = useState(null);
  const [encashmentDays, setEncashmentDays] = useState(0);

  useEffect(() => {
    loadBalances();
  }, [employeeId, selectedYear]);

  const loadBalances = async () => {
    try {
      setLoading(true);
      const response = isMyBalance 
        ? await apiService.getMyLeaveBalance()
        : await apiService.get(`/leave/balance/${employeeId}?year=${selectedYear}`);
      setBalances(response || []);
    } catch (error) {
      console.error('Error loading leave balances:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleEncashment = async () => {
    try {
      await apiService.post('/leave/encashment', {
        leave_policy_id: selectedBalance.leave_policy_id,
        days_to_encash: encashmentDays
      });
      
      await loadBalances();
      setShowEncashmentModal(false);
      setSelectedBalance(null);
      setEncashmentDays(0);
    } catch (error) {
      console.error('Error processing encashment:', error);
    }
  };

  const getBalanceStatus = (balance) => {
    const utilizationRate = (balance.used_balance / balance.total_entitlement) * 100;
    
    if (utilizationRate >= 80) return { color: 'text-red-600', status: 'High Usage' };
    if (utilizationRate >= 60) return { color: 'text-amber-600', status: 'Moderate Usage' };
    return { color: 'text-green-600', status: 'Low Usage' };
  };

  const calculateEncashmentAmount = (balance, days) => {
    // This would typically come from the backend calculation
    const dailyRate = 200; // Example daily rate
    return days * dailyRate * (balance.encashment_rate_percentage / 100);
  };

  const years = Array.from({ length: 5 }, (_, i) => new Date().getFullYear() - i);

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">
            {isMyBalance ? 'My Leave Balance' : 'Leave Balance'}
          </h2>
          <p className="text-gray-600 mt-1">Track leave entitlements and usage</p>
        </div>
        
        <div className="flex items-center space-x-3">
          <select
            value={selectedYear}
            onChange={(e) => setSelectedYear(parseInt(e.target.value))}
            className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-agno-primary focus:border-transparent"
          >
            {years.map(year => (
              <option key={year} value={year}>{year}</option>
            ))}
          </select>
          
          <button
            onClick={loadBalances}
            className="flex items-center px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50"
          >
            <RefreshCw size={16} className="mr-2" />
            Refresh
          </button>
          
          <button className="flex items-center px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50">
            <Download size={16} className="mr-2" />
            Export
          </button>
        </div>
      </div>

      {/* Balance Cards */}
      {loading ? (
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-agno-primary"></div>
        </div>
      ) : balances.length === 0 ? (
        <div className="text-center py-12 bg-white rounded-lg shadow-sm border border-gray-200">
          <Calendar className="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <p className="text-gray-500">No leave balances found for {selectedYear}</p>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {balances.map((balance) => {
            const status = getBalanceStatus(balance);
            const utilizationRate = (balance.used_balance / balance.total_entitlement) * 100;
            
            return (
              <div key={balance.id} className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                {/* Header */}
                <div className="flex items-center justify-between mb-4">
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 capitalize">
                      {balance.leave_type || 'Leave'}
                    </h3>
                    <p className="text-sm text-gray-500">{balance.policy_name}</p>
                  </div>
                  <div className={`text-sm font-medium ${status.color}`}>
                    {status.status}
                  </div>
                </div>

                {/* Balance Overview */}
                <div className="space-y-3 mb-6">
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600">Total Entitlement</span>
                    <span className="font-semibold text-gray-900">{balance.total_entitlement} days</span>
                  </div>
                  
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600">Used</span>
                    <span className="font-semibold text-red-600">{balance.used_balance} days</span>
                  </div>
                  
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600">Pending</span>
                    <span className="font-semibold text-amber-600">{balance.pending_balance || 0} days</span>
                  </div>
                  
                  <div className="flex justify-between items-center border-t pt-2">
                    <span className="text-sm font-medium text-gray-700">Available</span>
                    <span className="font-bold text-green-600 text-lg">{balance.available_balance} days</span>
                  </div>
                </div>

                {/* Progress Bar */}
                <div className="mb-4">
                  <div className="flex justify-between text-xs text-gray-600 mb-1">
                    <span>Usage: {utilizationRate.toFixed(1)}%</span>
                    <span>{balance.used_balance}/{balance.total_entitlement}</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div 
                      className={`h-2 rounded-full ${
                        utilizationRate >= 80 ? 'bg-red-500' :
                        utilizationRate >= 60 ? 'bg-amber-500' : 'bg-green-500'
                      }`}
                      style={{ width: `${Math.min(utilizationRate, 100)}%` }}
                    ></div>
                  </div>
                </div>

                {/* Accrual Information */}
                {balance.accrual_rate && (
                  <div className="bg-blue-50 border border-blue-200 rounded-lg p-3 mb-4">
                    <div className="flex items-center text-sm text-blue-800">
                      <TrendingUp size={14} className="mr-2" />
                      <span>Accrues {balance.accrual_rate} days {balance.accrual_frequency}</span>
                    </div>
                    {balance.next_accrual_date && (
                      <div className="text-xs text-blue-600 mt-1">
                        Next accrual: {new Date(balance.next_accrual_date).toLocaleDateString()}
                      </div>
                    )}
                  </div>
                )}

                {/* Carry Forward */}
                {balance.carry_forward > 0 && (
                  <div className="bg-green-50 border border-green-200 rounded-lg p-3 mb-4">
                    <div className="flex items-center text-sm text-green-800">
                      <Plus size={14} className="mr-2" />
                      <span>Carried forward: {balance.carry_forward} days</span>
                    </div>
                  </div>
                )}

                {/* Actions */}
                <div className="flex items-center justify-between pt-4 border-t">
                  <button className="text-agno-primary hover:text-blue-700 text-sm font-medium flex items-center">
                    <Eye size={14} className="mr-1" />
                    View History
                  </button>
                  
                  {balance.allow_encashment && balance.available_balance >= balance.min_balance_for_encashment && (
                    <button
                      onClick={() => {
                        setSelectedBalance(balance);
                        setShowEncashmentModal(true);
                      }}
                      className="text-green-600 hover:text-green-700 text-sm font-medium flex items-center"
                    >
                      <DollarSign size={14} className="mr-1" />
                      Encash
                    </button>
                  )}
                </div>
              </div>
            );
          })}
        </div>
      )}

      {/* Encashment Modal */}
      {showEncashmentModal && selectedBalance && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-2xl shadow-2xl w-full max-w-md">
            {/* Header */}
            <div className="p-6 border-b border-gray-200">
              <h3 className="text-xl font-semibold text-gray-900">Leave Encashment</h3>
              <p className="text-gray-600 mt-1">Convert leave days to cash</p>
            </div>

            {/* Content */}
            <div className="p-6 space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Leave Type
                </label>
                <p className="text-sm text-gray-900 capitalize">{selectedBalance.leave_type}</p>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Available Balance
                </label>
                <p className="text-sm text-gray-900">{selectedBalance.available_balance} days</p>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Days to Encash
                </label>
                <input
                  type="number"
                  value={encashmentDays}
                  onChange={(e) => setEncashmentDays(parseFloat(e.target.value) || 0)}
                  max={Math.min(
                    selectedBalance.available_balance - selectedBalance.min_balance_for_encashment,
                    selectedBalance.max_encashment_days_per_year
                  )}
                  min="0.5"
                  step="0.5"
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-agno-primary focus:border-transparent"
                />
                <p className="text-xs text-gray-500 mt-1">
                  Maximum: {Math.min(
                    selectedBalance.available_balance - selectedBalance.min_balance_for_encashment,
                    selectedBalance.max_encashment_days_per_year
                  )} days
                </p>
              </div>

              {encashmentDays > 0 && (
                <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium text-green-800">Estimated Amount:</span>
                    <span className="text-lg font-bold text-green-600">
                      ${calculateEncashmentAmount(selectedBalance, encashmentDays).toFixed(2)}
                    </span>
                  </div>
                  <p className="text-xs text-green-600 mt-1">
                    Rate: {selectedBalance.encashment_rate_percentage}% of daily salary
                  </p>
                </div>
              )}

              {selectedBalance.available_balance <= selectedBalance.min_balance_for_encashment && (
                <div className="bg-amber-50 border border-amber-200 rounded-lg p-4">
                  <div className="flex items-center text-amber-800">
                    <AlertTriangle size={16} className="mr-2" />
                    <span className="text-sm">
                      Minimum balance of {selectedBalance.min_balance_for_encashment} days must be maintained
                    </span>
                  </div>
                </div>
              )}
            </div>

            {/* Footer */}
            <div className="px-6 py-4 bg-gray-50 flex justify-end space-x-3">
              <button
                onClick={() => {
                  setShowEncashmentModal(false);
                  setSelectedBalance(null);
                  setEncashmentDays(0);
                }}
                className="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50"
              >
                Cancel
              </button>
              <button
                onClick={handleEncashment}
                disabled={encashmentDays <= 0 || encashmentDays > (selectedBalance.available_balance - selectedBalance.min_balance_for_encashment)}
                className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Submit Request
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default LeaveBalanceTracker;
