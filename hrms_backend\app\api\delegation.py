from fastapi import APIRouter, HTTPException, status, Depends, Query
from sqlalchemy.orm import Session
from typing import List, Optional
from uuid import UUID

from ..core.security import get_current_user, require_permission, CurrentUser
from ..core.rbac import Permissions
from ..db.session import get_db
from ..schemas.delegation import (
    DelegationCreate, DelegationUpdate, DelegationResponse,
    DelegationListResponse, DelegationActivityResponse,
    ApprovalDelegationCreate, ApprovalDelegationUpdate, ApprovalDelegationResponse,
    DelegationApprovalRequest, DelegationRevocationRequest,
    BulkDelegationUpdate, DelegationSummary, DelegationStatus, DelegationType
)
from ..services.hr_management.delegation_service import DelegationService

router = APIRouter()
delegation_service = DelegationService()


# Delegation endpoints
@router.get("/", response_model=DelegationListResponse)
async def get_delegations(
    status: Optional[DelegationStatus] = Query(None),
    delegation_type: Optional[DelegationType] = Query(None),
    delegator_id: Optional[UUID] = Query(None),
    delegate_to: Optional[UUID] = Query(None),
    search: Optional[str] = Query(None),
    skip: int = Query(0, ge=0),
    limit: int = Query(20, ge=1, le=100),
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.DELEGATION_READ))
):
    """Get delegations with filtering"""
    return await delegation_service.get_delegations(
        db=db,
        status=status,
        delegation_type=delegation_type,
        delegator_id=delegator_id,
        delegate_to=delegate_to,
        search=search,
        skip=skip,
        limit=limit,
        current_user=current_user
    )


@router.post("/", response_model=DelegationResponse)
async def create_delegation(
    delegation_data: DelegationCreate,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.DELEGATION_CREATE))
):
    """Create new delegation"""
    return await delegation_service.create_delegation(db, delegation_data, current_user)


@router.get("/{delegation_id}", response_model=DelegationResponse)
async def get_delegation(
    delegation_id: UUID,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.DELEGATION_READ))
):
    """Get delegation by ID"""
    delegations = await delegation_service.get_delegations(
        db=db,
        skip=0,
        limit=1,
        current_user=current_user
    )

    # Find the specific delegation
    delegation = next((d for d in delegations.delegations if d.id == delegation_id), None)
    if not delegation:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Delegation not found"
        )

    return delegation


@router.put("/{delegation_id}", response_model=DelegationResponse)
async def update_delegation(
    delegation_id: UUID,
    delegation_data: DelegationUpdate,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.DELEGATION_UPDATE))
):
    """Update delegation"""
    delegation = await delegation_service.update_delegation(db, delegation_id, delegation_data, current_user)
    if not delegation:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Delegation not found"
        )
    return delegation


@router.put("/{delegation_id}/approve", response_model=DelegationResponse)
async def approve_delegation(
    delegation_id: UUID,
    approved: bool = True,
    comments: Optional[str] = None,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.DELEGATION_APPROVE))
):
    """Approve or reject delegation"""
    delegation = await delegation_service.approve_delegation(
        db, delegation_id, approved, comments, current_user
    )
    if not delegation:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Delegation not found"
        )
    return delegation


@router.put("/{delegation_id}/revoke", response_model=DelegationResponse)
async def revoke_delegation(
    delegation_id: UUID,
    revocation_data: DelegationRevocationRequest,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.DELEGATION_UPDATE))
):
    """Revoke delegation"""
    delegation = await delegation_service.revoke_delegation(
        db, delegation_id, revocation_data, current_user
    )
    if not delegation:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Delegation not found"
        )
    return delegation


# My delegations endpoints
@router.get("/my/delegated", response_model=DelegationListResponse)
async def get_my_delegations(
    status: Optional[DelegationStatus] = Query(None),
    skip: int = Query(0, ge=0),
    limit: int = Query(20, ge=1, le=100),
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(get_current_user)
):
    """Get delegations I have created"""
    return await delegation_service.get_delegations(
        db=db,
        delegator_id=UUID(current_user.user_id),
        status=status,
        skip=skip,
        limit=limit,
        current_user=current_user
    )


@router.get("/my/received", response_model=DelegationListResponse)
async def get_received_delegations(
    status: Optional[DelegationStatus] = Query(None),
    skip: int = Query(0, ge=0),
    limit: int = Query(20, ge=1, le=100),
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(get_current_user)
):
    """Get delegations received by me"""
    return await delegation_service.get_delegations(
        db=db,
        delegate_to=UUID(current_user.user_id),
        status=status,
        skip=skip,
        limit=limit,
        current_user=current_user
    )


@router.get("/pending/approval", response_model=DelegationListResponse)
async def get_pending_approvals(
    skip: int = Query(0, ge=0),
    limit: int = Query(20, ge=1, le=100),
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.DELEGATION_APPROVE))
):
    """Get delegations pending my approval"""
    return await delegation_service.get_delegations(
        db=db,
        status=DelegationStatus.PENDING,
        skip=skip,
        limit=limit,
        current_user=current_user
    )


# Bulk operations
@router.put("/bulk/approve")
async def bulk_approve_delegations(
    approval_data: DelegationApprovalRequest,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.DELEGATION_APPROVE))
):
    """Bulk approve or reject delegations"""
    results = []
    for delegation_id in approval_data.delegation_ids:
        try:
            delegation = await delegation_service.approve_delegation(
                db, delegation_id, approval_data.approved, approval_data.comments, current_user
            )
            results.append({
                "delegation_id": delegation_id,
                "success": True,
                "delegation": delegation
            })
        except Exception as e:
            results.append({
                "delegation_id": delegation_id,
                "success": False,
                "error": str(e)
            })

    return {"results": results}
