{"name": "agnoconnect-hrm-frontend", "private": true, "version": "1.0.0", "description": "AgnoConnect HRM Frontend Application with RBAC and Advanced Attendance Tracking", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint . --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.1", "@fullcalendar/daygrid": "^6.1.18", "@fullcalendar/interaction": "^6.1.18", "@fullcalendar/react": "^6.1.18", "@mui/icons-material": "^7.2.0", "@mui/material": "^7.2.0", "@mui/styled-engine-sc": "^7.2.0", "@tailwindcss/vite": "^4.1.7", "date-fns": "^4.1.0", "lucide-react": "^0.344.0", "react": "^18.2.0", "react-dom": "^18.2.0", "styled-components": "^6.1.19", "tailwindcss-animate": "^1.0.7"}, "devDependencies": {"@babel/eslint-parser": "^7.27.1", "@eslint/js": "^9.27.0", "@types/node": "^20.11.24", "@types/react": "^18.2.56", "@types/react-dom": "^18.2.19", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "^10.4.17", "eslint": "^8.57.1", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^4.6.2", "eslint-plugin-react-refresh": "^0.4.5", "globals": "^16.1.0", "postcss": "^8.4.35", "tailwindcss": "^4.1.7", "vite": "^5.1.4"}}