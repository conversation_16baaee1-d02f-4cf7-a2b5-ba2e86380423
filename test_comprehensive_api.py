#!/usr/bin/env python3
"""
Comprehensive test of all fixed API endpoints
"""

import requests
import json

BASE_URL = "http://localhost:8085"

def test_comprehensive_api():
    """Test all API endpoints comprehensively"""
    try:
        # Login first
        credentials = {
            "email": "<EMAIL>",
            "password": "password123"
        }
        
        login_response = requests.post(f"{BASE_URL}/api/auth/login", json=credentials)
        if login_response.status_code != 200:
            print("❌ Login failed")
            return
            
        token = login_response.json().get('access_token')
        headers = {"Authorization": f"Bearer {token}"}
        
        print("🔧 Comprehensive API Endpoint Testing")
        print("=" * 60)
        
        # Test each endpoint with detailed verification
        endpoints = [
            {
                "name": "Employees",
                "url": "/api/employees/",
                "expected_keys": ["employees", "total"]
            },
            {
                "name": "Attendance", 
                "url": "/api/attendance/",
                "expected_keys": ["attendance_records", "total"]
            },
            {
                "name": "Leave Management",
                "url": "/api/leave/",
                "expected_keys": ["message", "endpoints"]
            },
            {
                "name": "Payroll",
                "url": "/api/payroll/",
                "expected_keys": ["message", "endpoints"]
            },
            {
                "name": "Performance",
                "url": "/api/performance/",
                "expected_keys": ["message", "endpoints"]
            },
            {
                "name": "Shift Management",
                "url": "/api/shift/",
                "expected_keys": ["shifts", "total"]
            },
            {
                "name": "Timesheet",
                "url": "/api/timesheet/",
                "expected_keys": ["entries", "total", "total_hours"]
            },
            {
                "name": "Project Management",
                "url": "/api/project/",
                "expected_keys": ["projects", "total"]
            },
            {
                "name": "Kanban Boards",
                "url": "/api/kanban/",
                "expected_keys": ["message", "endpoints"]
            },
            {
                "name": "Ticket Management",
                "url": "/api/ticket/",
                "expected_keys": ["tickets", "total"]
            },
            {
                "name": "Reports & Analytics",
                "url": "/api/reports/",
                "expected_keys": ["message", "endpoints"]
            },
            {
                "name": "Settings",
                "url": "/api/settings/",
                "expected_keys": ["message", "endpoints"]
            }
        ]
        
        working_count = 0
        total_count = len(endpoints)
        
        for endpoint in endpoints:
            try:
                response = requests.get(f"{BASE_URL}{endpoint['url']}", headers=headers, timeout=5)
                
                if response.status_code == 200:
                    data = response.json()
                    
                    # Check if expected keys are present
                    has_expected_keys = all(key in data for key in endpoint['expected_keys'])
                    
                    if has_expected_keys:
                        status = "✅ WORKING"
                        working_count += 1
                        
                        # Show additional info
                        if 'total' in data:
                            print(f"{endpoint['name']:<25} {status} → {data['total']} items")
                        elif 'message' in data:
                            print(f"{endpoint['name']:<25} {status} → {data['message']}")
                        else:
                            print(f"{endpoint['name']:<25} {status}")
                    else:
                        print(f"{endpoint['name']:<25} ⚠️  PARTIAL → Missing expected keys")
                        
                else:
                    print(f"{endpoint['name']:<25} ❌ ERROR → Status {response.status_code}")
                    
            except Exception as e:
                print(f"{endpoint['name']:<25} ❌ FAILED → {str(e)[:30]}...")
        
        print("\n" + "=" * 60)
        print(f"📊 API Test Results: {working_count}/{total_count} endpoints working")
        
        if working_count == total_count:
            print("🎉 ALL API ENDPOINTS ARE WORKING PERFECTLY!")
        elif working_count >= total_count * 0.8:
            print("✅ Most API endpoints are working well!")
        else:
            print("⚠️  Some API endpoints need attention")
            
        print(f"\n🌐 API Documentation: {BASE_URL}/docs")
        print(f"🔗 Frontend Application: http://localhost:5173")
        
        # Test different user roles
        print("\n👥 Testing Different User Roles:")
        test_users = [
            {"email": "<EMAIL>", "role": "HR"},
            {"email": "<EMAIL>", "role": "MANAGER"},
            {"email": "<EMAIL>", "role": "EMPLOYEE"}
        ]
        
        for test_user in test_users:
            try:
                response = requests.post(f"{BASE_URL}/api/auth/login", json={
                    "email": test_user["email"],
                    "password": "password123"
                })
                if response.status_code == 200:
                    print(f"   ✅ {test_user['role']} role authentication working")
                else:
                    print(f"   ❌ {test_user['role']} role authentication failed")
            except:
                print(f"   ❌ {test_user['role']} role authentication error")
        
    except Exception as e:
        print(f"❌ Comprehensive test failed: {e}")

if __name__ == "__main__":
    test_comprehensive_api()
