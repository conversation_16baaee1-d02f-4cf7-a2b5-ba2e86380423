from core.repositories.nhf import NhfRepository

class NhfService:
    def __init__(self) -> None:
        self.repository = NhfRepository()

    def createNhf(self, Kwargs):
        # print(Kwargs)
        return self.repository.createNhf(**Kwargs)
    
    def getNhf(self, id):
        return self.repository.getNhf(id)
    
    def updateNhf(self, id, **Kwargs):
        return self.repository.updateNhf(id, **Kwargs)
    
    def getNhfByKey(self, Kwarg):
        return self.repository.getNhfByKeys(Kwarg)
    
    def deleteNhf(self, id):
        return self.repository.deleteNhf(id)