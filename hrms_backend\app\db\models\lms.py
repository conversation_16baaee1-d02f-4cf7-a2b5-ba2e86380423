from sqlalchemy import <PERSON>um<PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, DateTime, Text, Integer, ForeignKey, Enum as SQLEnum, Numeric, Date
from sqlalchemy.dialects.postgresql import UUID, JSONB
from sqlalchemy.orm import relationship
from uuid import uuid4
from datetime import datetime, date
import enum

from ..base import BaseModel, AuditMixin


class CourseStatus(str, enum.Enum):
    """Course status enumeration"""
    DRAFT = "draft"
    PUBLISHED = "published"
    ARCHIVED = "archived"
    SUSPENDED = "suspended"


class CourseType(str, enum.Enum):
    """Course type enumeration"""
    MANDATORY = "mandatory"
    OPTIONAL = "optional"
    CERTIFICATION = "certification"
    ONBOARDING = "onboarding"
    COMPLIANCE = "compliance"
    SKILL_DEVELOPMENT = "skill_development"


class EnrollmentStatus(str, enum.Enum):
    """Enrollment status enumeration"""
    ENROLLED = "enrolled"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    DROPPED = "dropped"
    FAILED = "failed"
    EXPIRED = "expired"


class AssessmentType(str, enum.Enum):
    """Assessment type enumeration"""
    QUIZ = "quiz"
    ASSIGNMENT = "assignment"
    PRACTICAL = "practical"
    FINAL_EXAM = "final_exam"
    PEER_REVIEW = "peer_review"


class CertificationStatus(str, enum.Enum):
    """Certification status enumeration"""
    ACTIVE = "active"
    EXPIRED = "expired"
    REVOKED = "revoked"
    PENDING = "pending"


class Course(BaseModel, AuditMixin):
    """Course model for LMS"""
    __tablename__ = "courses"

    # Basic Information
    title = Column(String(200), nullable=False, index=True)
    description = Column(Text, nullable=False)
    short_description = Column(String(500), nullable=True)
    
    # Course Details
    course_type = Column(SQLEnum(CourseType), nullable=False)
    status = Column(SQLEnum(CourseStatus), nullable=False, default=CourseStatus.DRAFT)
    category = Column(String(100), nullable=True, index=True)
    
    # Content and Structure
    duration_hours = Column(Integer, nullable=True)  # Estimated duration in hours
    difficulty_level = Column(String(50), nullable=True)  # Beginner, Intermediate, Advanced
    prerequisites = Column(JSONB, nullable=True)  # Array of prerequisite course IDs
    learning_objectives = Column(JSONB, nullable=True)  # Array of learning objectives
    
    # Media and Resources
    thumbnail_url = Column(String(500), nullable=True)
    video_url = Column(String(500), nullable=True)
    materials = Column(JSONB, nullable=True)  # Array of course materials/resources
    
    # Instructor Information
    instructor_id = Column(UUID(as_uuid=True), ForeignKey("employees.id"), nullable=True)
    instructor_name = Column(String(200), nullable=True)  # External instructor name
    instructor_bio = Column(Text, nullable=True)
    
    # Enrollment and Completion
    max_enrollments = Column(Integer, nullable=True)
    current_enrollments = Column(Integer, nullable=False, default=0)
    completion_criteria = Column(JSONB, nullable=True)  # Criteria for course completion
    
    # Dates
    start_date = Column(Date, nullable=True)
    end_date = Column(Date, nullable=True)
    enrollment_deadline = Column(Date, nullable=True)
    
    # Certification
    provides_certificate = Column(Boolean, default=False, nullable=False)
    certificate_template_url = Column(String(500), nullable=True)
    certificate_validity_months = Column(Integer, nullable=True)
    
    # Pricing (if applicable)
    is_free = Column(Boolean, default=True, nullable=False)
    price = Column(Numeric(10, 2), nullable=True)
    currency = Column(String(3), nullable=True, default="USD")
    
    # Organization
    organization_id = Column(UUID(as_uuid=True), nullable=True, index=True)
    
    # Relationships
    instructor = relationship("Employee", back_populates="taught_courses")
    enrollments = relationship("CourseEnrollment", back_populates="course")
    modules = relationship("CourseModule", back_populates="course")
    assessments = relationship("Assessment", back_populates="course")

    def __repr__(self):
        return f"<Course(title='{self.title}', type='{self.course_type}', status='{self.status}')>"


class CourseModule(BaseModel, AuditMixin):
    """Course module model"""
    __tablename__ = "course_modules"

    # References
    course_id = Column(UUID(as_uuid=True), ForeignKey("courses.id"), nullable=False)
    
    # Module Information
    title = Column(String(200), nullable=False)
    description = Column(Text, nullable=True)
    order_index = Column(Integer, nullable=False)  # Order within the course
    
    # Content
    content_type = Column(String(50), nullable=False)  # video, text, document, interactive
    content_url = Column(String(500), nullable=True)
    content_text = Column(Text, nullable=True)
    duration_minutes = Column(Integer, nullable=True)
    
    # Requirements
    is_mandatory = Column(Boolean, default=True, nullable=False)
    prerequisites = Column(JSONB, nullable=True)  # Array of prerequisite module IDs
    
    # Organization
    organization_id = Column(UUID(as_uuid=True), nullable=True, index=True)
    
    # Relationships
    course = relationship("Course", back_populates="modules")
    progress_records = relationship("ModuleProgress", back_populates="module")

    def __repr__(self):
        return f"<CourseModule(title='{self.title}', course_id='{self.course_id}')>"


class CourseEnrollment(BaseModel, AuditMixin):
    """Course enrollment model"""
    __tablename__ = "course_enrollments"

    # References
    course_id = Column(UUID(as_uuid=True), ForeignKey("courses.id"), nullable=False)
    employee_id = Column(UUID(as_uuid=True), ForeignKey("employees.id"), nullable=False)
    
    # Enrollment Details
    status = Column(SQLEnum(EnrollmentStatus), nullable=False, default=EnrollmentStatus.ENROLLED)
    enrolled_date = Column(DateTime, nullable=False, default=datetime.utcnow)
    start_date = Column(DateTime, nullable=True)
    completion_date = Column(DateTime, nullable=True)
    deadline = Column(Date, nullable=True)
    
    # Progress Tracking
    progress_percentage = Column(Integer, nullable=False, default=0)  # 0-100
    modules_completed = Column(Integer, nullable=False, default=0)
    total_modules = Column(Integer, nullable=True)
    time_spent_minutes = Column(Integer, nullable=False, default=0)
    
    # Assessment Results
    final_score = Column(Numeric(5, 2), nullable=True)  # Final score percentage
    passing_score = Column(Numeric(5, 2), nullable=True)  # Required passing score
    attempts = Column(Integer, nullable=False, default=0)
    max_attempts = Column(Integer, nullable=True)
    
    # Enrollment Source
    enrolled_by_id = Column(UUID(as_uuid=True), ForeignKey("employees.id"), nullable=True)
    enrollment_type = Column(String(50), nullable=False, default="self")  # self, assigned, mandatory
    
    # Organization
    organization_id = Column(UUID(as_uuid=True), nullable=True, index=True)
    
    # Relationships
    course = relationship("Course", back_populates="enrollments")
    employee = relationship("Employee", foreign_keys=[employee_id], back_populates="course_enrollments")
    enrolled_by = relationship("Employee", foreign_keys=[enrolled_by_id])
    module_progress = relationship("ModuleProgress", back_populates="enrollment")
    assessment_attempts = relationship("AssessmentAttempt", back_populates="enrollment")

    def __repr__(self):
        return f"<CourseEnrollment(employee_id='{self.employee_id}', course_id='{self.course_id}', status='{self.status}')>"


class ModuleProgress(BaseModel):
    """Module progress tracking model"""
    __tablename__ = "module_progress"

    # References
    enrollment_id = Column(UUID(as_uuid=True), ForeignKey("course_enrollments.id"), nullable=False)
    module_id = Column(UUID(as_uuid=True), ForeignKey("course_modules.id"), nullable=False)
    
    # Progress Details
    status = Column(String(50), nullable=False, default="not_started")  # not_started, in_progress, completed
    started_date = Column(DateTime, nullable=True)
    completed_date = Column(DateTime, nullable=True)
    time_spent_minutes = Column(Integer, nullable=False, default=0)
    
    # Interaction Data
    last_accessed = Column(DateTime, nullable=True)
    access_count = Column(Integer, nullable=False, default=0)
    bookmarks = Column(JSONB, nullable=True)  # Array of bookmarked positions
    notes = Column(Text, nullable=True)
    
    # Organization
    organization_id = Column(UUID(as_uuid=True), nullable=True, index=True)
    
    # Relationships
    enrollment = relationship("CourseEnrollment", back_populates="module_progress")
    module = relationship("CourseModule", back_populates="progress_records")

    def __repr__(self):
        return f"<ModuleProgress(enrollment_id='{self.enrollment_id}', module_id='{self.module_id}', status='{self.status}')>"


class Assessment(BaseModel, AuditMixin):
    """Assessment model"""
    __tablename__ = "assessments"

    # References
    course_id = Column(UUID(as_uuid=True), ForeignKey("courses.id"), nullable=False)
    module_id = Column(UUID(as_uuid=True), ForeignKey("course_modules.id"), nullable=True)
    
    # Assessment Information
    title = Column(String(200), nullable=False)
    description = Column(Text, nullable=True)
    assessment_type = Column(SQLEnum(AssessmentType), nullable=False)
    
    # Configuration
    questions = Column(JSONB, nullable=False)  # Array of questions with answers
    time_limit_minutes = Column(Integer, nullable=True)
    max_attempts = Column(Integer, nullable=True, default=3)
    passing_score = Column(Numeric(5, 2), nullable=False, default=70.0)
    
    # Availability
    is_active = Column(Boolean, default=True, nullable=False)
    available_from = Column(DateTime, nullable=True)
    available_until = Column(DateTime, nullable=True)
    
    # Grading
    auto_grade = Column(Boolean, default=True, nullable=False)
    show_results_immediately = Column(Boolean, default=True, nullable=False)
    
    # Organization
    organization_id = Column(UUID(as_uuid=True), nullable=True, index=True)
    
    # Relationships
    course = relationship("Course", back_populates="assessments")
    attempts = relationship("AssessmentAttempt", back_populates="assessment")

    def __repr__(self):
        return f"<Assessment(title='{self.title}', type='{self.assessment_type}', course_id='{self.course_id}')>"


class AssessmentAttempt(BaseModel):
    """Assessment attempt model"""
    __tablename__ = "assessment_attempts"

    # References
    assessment_id = Column(UUID(as_uuid=True), ForeignKey("assessments.id"), nullable=False)
    enrollment_id = Column(UUID(as_uuid=True), ForeignKey("course_enrollments.id"), nullable=False)
    
    # Attempt Details
    attempt_number = Column(Integer, nullable=False)
    started_date = Column(DateTime, nullable=False, default=datetime.utcnow)
    submitted_date = Column(DateTime, nullable=True)
    time_taken_minutes = Column(Integer, nullable=True)
    
    # Responses and Scoring
    responses = Column(JSONB, nullable=True)  # User responses to questions
    score = Column(Numeric(5, 2), nullable=True)  # Score percentage
    passed = Column(Boolean, nullable=True)
    
    # Status
    status = Column(String(50), nullable=False, default="in_progress")  # in_progress, submitted, graded
    
    # Grading
    graded_by_id = Column(UUID(as_uuid=True), ForeignKey("employees.id"), nullable=True)
    graded_date = Column(DateTime, nullable=True)
    feedback = Column(Text, nullable=True)
    
    # Organization
    organization_id = Column(UUID(as_uuid=True), nullable=True, index=True)
    
    # Relationships
    assessment = relationship("Assessment", back_populates="attempts")
    enrollment = relationship("CourseEnrollment", back_populates="assessment_attempts")
    graded_by = relationship("Employee")

    def __repr__(self):
        return f"<AssessmentAttempt(assessment_id='{self.assessment_id}', attempt='{self.attempt_number}', score='{self.score}')>"


class Certification(BaseModel, AuditMixin):
    """Certification model"""
    __tablename__ = "certifications"

    # References
    employee_id = Column(UUID(as_uuid=True), ForeignKey("employees.id"), nullable=False)
    course_id = Column(UUID(as_uuid=True), ForeignKey("courses.id"), nullable=True)
    
    # Certification Information
    name = Column(String(200), nullable=False)
    issuing_organization = Column(String(200), nullable=False)
    certification_number = Column(String(100), nullable=True, unique=True)
    
    # Dates
    issued_date = Column(Date, nullable=False)
    expiry_date = Column(Date, nullable=True)
    
    # Status and Verification
    status = Column(SQLEnum(CertificationStatus), nullable=False, default=CertificationStatus.ACTIVE)
    verification_url = Column(String(500), nullable=True)
    certificate_url = Column(String(500), nullable=True)
    
    # Additional Information
    description = Column(Text, nullable=True)
    skills_gained = Column(JSONB, nullable=True)  # Array of skills
    
    # Organization
    organization_id = Column(UUID(as_uuid=True), nullable=True, index=True)
    
    # Relationships
    employee = relationship("Employee", back_populates="certifications")
    course = relationship("Course")

    def __repr__(self):
        return f"<Certification(name='{self.name}', employee_id='{self.employee_id}', status='{self.status}')>"
