<!DOCTYPE html>
<html lang="en">

<head>
 
  
  <style>
    /* Laptop-specific styles */
@media only screen and (min-width: 1024px) {
  body {
    font-size: 16px;  /* Adjust font size */
  }
  
  .payslip-content {
    width: 80%;  /* Adjust content width */
    margin: 0 auto; /* Center content */
  }
}

  </style>
</head>

<body>

  
  <main id="main" class="main" style="background: #FFFFFF;">
    
    <div class="row mt-4">
     
        <div class="col-md-6 align-items-right">
            <button id="toggleButton" class="btn px-4 py-2" style="background-color: #fff; color: #BB3C2D;border:1px solid #BB3C2D; border-radius: 25px;font-weight: bold;"><small>Send payslip</small> </button>
        </div>
    </div>

    <h3 style="text-align: center;" class="fw-bold">{{employee_payslip["employee"]["organisation"]["organisation_name"]}}</h1>
        <h4 style="text-align: center;">{{employee_payslip["employee"]["organisation"]["address"]}}</h1>
            <h5 style="text-align: center;">{{employee_payslip["employee"]["organisation"]["email"]}}</h1>
                <br>
            <h4 id="paymentMonth" style="text-align: center;">Payment slip for the month of </h4>
        <div style="display: flex; align-items: flex-start; border: 1px solid white; border-radius: 10px; padding: 20px;">
         
        
            <div style="display: flex; flex-wrap: wrap; justify-content: space-between; flex-grow: 1;"> <!-- Flex container for employee details -->
                <div style="flex: 1; text-align: left;">
                    <h5>Employee Name: {{ employee_payslip["employee"]["first_name"] }} {{ employee_payslip["employee"]["last_name"] }}</h5>
                    <h5>Employee ID: {{ employee_payslip["employee"]["id"] }}</h5>
                </div>
                <div style="flex: 1; text-align: center;">
                    <h5>Department: {{ employee_payslip["employee"]["department"]["name"] }}</h5>
                    <h5>Designation: {{ employee_payslip["employee"]["designation"]["name"] }}</h5>
                </div>
                <div style="flex: 1; text-align: right;">
                    <h5>Email: {{ employee_payslip["email"] }}</h5>
                    <h5>Bank Name: {{ employee_payslip["bank_name"] }}</h5>
                </div>
            </div>
        </div>
        
        <!-- <div>
            <table id="basetab" style="width: 140px; height: 70px; text-align: left;">
                <tr>
                    <th>Month</th>
                    <th>Year</th>
                </tr>
                <tr>
                    <td>March</td>
                    <td>2024</td>
                </tr>
            </table>
        </div> -->
    
    
    
<div id="detailstable" class="row">
    <div style="width: 48%;" class="col-md-6">
        <table cellpadding="10px" cellspacing="5px" style="table-layout: fixed; width: 100%;">
            <thead >
                <tr>
                    <th style="width: 70%;background: #BB3C2D; color: white; font-size: 14px;">Earnings</th>
                    <th style=" width: 29%;background: #BB3C2D; color: white; font-size: 14px; text-align: center;">Amount</th>
                </tr>
            </thead>

            <tbody id="tdata1" >
                {% if employee_payslip["all_components"] %}
                    {% for component in employee_payslip["all_components"] %}
                    <tr>
                        {% if component["component_type"].lower() == "taxable earning" %}
                        <td>{{ component["payslip_name"] }}</td>
                        <td>{{ component["total_monthly_calculation"] }}</td>
                        {% endif %}
                    </tr>
                    {% endfor %}
                {% endif %}
                <tr style="background: lightgrey; font-weight: bold;">
                    <td>Total Taxable</td>
                    <td id="tdata">{{ employee_payslip["total_earnings"] }}</td>
                </tr>
            </tbody>
        </table>
    </div>

    <div style="background: white; width: 48%;" class="col-md-6">
        <table cellpadding="10px" cellspacing="5px" style="table-layout: fixed; width: 100%;">
            <thead >
                <tr>
                    <th style="width: 70%;background: #BB3C2D; color: white; font-size: 14px;">Non Taxable earnings</th>
                    <th style=" width: 29%;background: #BB3C2D; color: white; font-size: 14px; text-align: center;">Amount</th>
                </tr>
            </thead>
    
            <tbody id="tdata1" >
            {% if employee_payslip["all_components"] %}
                {% for component in employee_payslip["all_components"] %}
                <tr>
                    {% if component["component_type"].lower() == "non-taxable earning" %}
                    <td>{{ component["payslip_name"] }}</td>
                    <td>{{ component["total_monthly_calculation"] }}</td>
                    {% endif %}
                </tr>
                {% endfor %}
            {% endif %}
            </tbody>
        </table>
    </div>
</div>

  
<div id="detailstable" class="row mt-4">
    <div style="width: 48%;" class="col-md-6">
        <table cellpadding="10px" cellspacing="5px" style="table-layout: fixed; width: 100%;">
            <thead >
                <tr>
                    <th style="width: 70%;background: #BB3C2D; color: white; font-size: 14px;">Statutory Deduction</th>
                    <th style=" width: 29%;background: #BB3C2D; color: white; font-size: 14px; text-align: center;">Amount</th>
                </tr>
            </thead>

            <tbody id="tdata1" >
                {% if employee_payslip["all_components"] %}
                    {% for component in employee_payslip["all_components"] %}
                    <tr>
                        {% if component["component_type"].lower() == "statutory deduction" %}
                        <td>{{ component["payslip_name"] }}</td>
                        <td>{{ component["total_monthly_calculation"] }}</td>
                        {% endif %}
                        
                    </tr>
                    {% endfor %}
                {% endif %}
                <tr>
                    <td>Monthly Tax</td>
                    <td> {{ (employee_payslip["employee"].get("monthly_tax", 0) | float) + (employee_payslip.get("total_deduction", 0) | float) }}</td>
                </tr>
                <tr style="background: lightgrey; font-weight: bold;">
                    <td>Total Deduction</td>
                    <td id="tdata">{{ employee_payslip["total_deduction"] }}</td>
                </tr>
            </tbody>
        </table>
    </div>

    <div style="background: white; width: 48%;" class="col-md-6">
        <table cellpadding="10px" cellspacing="5px" style="table-layout: fixed; width: 100%;">
            <thead >
                <tr>
                    <th style="width: 70%;background: #BB3C2D; color: white; font-size: 14px;">Other deduction</th>
                    <th style=" width: 29%;background: #BB3C2D; color: white; font-size: 14px; text-align: center;">Amount</th>
                </tr>
            </thead>
    
            <tbody id="tdata1" >
                {% if employee_payslip["all_components"] %}
                    {% for component in employee_payslip["all_components"] %}
                    <tr>
                        {% if component["component_type"].lower() == "other deductions" %}
                        <td>{{ component["payslip_name"] }}</td>
                        <td>{{ component["total_monthly_calculation"] }}</td>
                        {% endif %}
                    </tr>
                    {% endfor %}
                {% endif %}
                 
            </tbody>
            <!-- <tfoot style="background: lightgrey; font-weight: bold;">
                
            </tfoot> -->
        </table>
    </div>
</div>





  </main><!-- End #main -->

  

</body>

</html>