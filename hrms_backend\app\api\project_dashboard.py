from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from typing import Optional, Dict, Any
from uuid import UUID

from ..db.database import get_db
from ..core.security import get_current_user, require_permission, CurrentUser
from ..core.rbac import Permissions
from ..services.project_management.dashboard_service import ProjectDashboardService
from ..services.project_management.due_date_service import DueDateService
from ..services.project_management.collaboration_service import CollaborationService
from ..services.project_management.progress_monitoring_service import ProgressMonitoringService
from ..schemas.kanban import KanbanBoardMemberCreate, KanbanBoardMemberUpdate

router = APIRouter()
dashboard_service = ProjectDashboardService()
due_date_service = DueDateService()
collaboration_service = CollaborationService()
progress_service = ProgressMonitoringService()


# Dashboard endpoints
@router.get("/dashboard/overview", response_model=Dict[str, Any])
async def get_project_dashboard_overview(
    date_range: int = Query(30, ge=1, le=365, description="Number of days to include in analysis"),
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.PROJECT_READ))
):
    """Get comprehensive project management dashboard overview"""
    return await dashboard_service.get_dashboard_overview(db, current_user, date_range)


@router.get("/dashboard/project/{project_id}", response_model=Dict[str, Any])
async def get_project_details(
    project_id: UUID,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.PROJECT_READ))
):
    """Get detailed project information for monitoring"""
    return await dashboard_service.get_project_details(db, project_id, current_user)


# Due date and overdue management endpoints
@router.get("/overdue", response_model=Dict[str, Any])
async def get_overdue_items(
    item_type: Optional[str] = Query(None, regex="^(projects|tasks|cards)$"),
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.PROJECT_READ))
):
    """Get all overdue items (projects, tasks, cards)"""
    return await due_date_service.get_overdue_items(db, current_user, item_type)


@router.get("/upcoming-due-dates", response_model=Dict[str, Any])
async def get_upcoming_due_dates(
    days_ahead: int = Query(7, ge=1, le=30, description="Number of days to look ahead"),
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.PROJECT_READ))
):
    """Get items due in the next N days"""
    return await due_date_service.get_upcoming_due_dates(db, current_user, days_ahead)


@router.post("/send-due-date-reminders", response_model=Dict[str, Any])
async def send_due_date_reminders(
    days_ahead: int = Query(1, ge=0, le=7, description="Days ahead to send reminders"),
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.PROJECT_MANAGE))
):
    """Send due date reminders to users (SuperAdmin and Manager only)"""
    if current_user.role.upper() not in ["SUPER_ADMIN", "MANAGER"]:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Only SuperAdmin and Manager can send reminders"
        )
    
    return await due_date_service.send_due_date_reminders(
        db, current_user.organization_id, days_ahead
    )


# Board collaboration endpoints
@router.post("/boards/{board_id}/members", response_model=Dict[str, Any])
async def invite_board_member(
    board_id: UUID,
    member_data: KanbanBoardMemberCreate,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.KANBAN_MANAGE))
):
    """Invite a user to join a kanban board"""
    return await collaboration_service.invite_user_to_board(
        db, board_id, member_data, current_user
    )


@router.get("/boards/{board_id}/members")
async def get_board_members(
    board_id: UUID,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.KANBAN_READ))
):
    """Get all members of a kanban board"""
    return await collaboration_service.get_board_members(db, board_id, current_user)


@router.put("/boards/{board_id}/members/{member_id}", response_model=Dict[str, Any])
async def update_board_member_permissions(
    board_id: UUID,
    member_id: UUID,
    permission_updates: KanbanBoardMemberUpdate,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.KANBAN_MANAGE))
):
    """Update member permissions on a kanban board"""
    return await collaboration_service.update_member_permissions(
        db, board_id, member_id, permission_updates, current_user
    )


@router.delete("/boards/{board_id}/members/{member_id}", response_model=Dict[str, Any])
async def remove_board_member(
    board_id: UUID,
    member_id: UUID,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.KANBAN_MANAGE))
):
    """Remove a member from a kanban board"""
    return await collaboration_service.remove_board_member(
        db, board_id, member_id, current_user
    )


# Statistics endpoints for SuperAdmin monitoring
@router.get("/stats/organization", response_model=Dict[str, Any])
async def get_organization_project_stats(
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.ADMIN_REPORTS))
):
    """Get organization-wide project statistics (SuperAdmin only)"""
    if current_user.role.upper() != "SUPER_ADMIN":
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Only SuperAdmin can access organization-wide statistics"
        )
    
    return await dashboard_service.get_dashboard_overview(db, current_user, 365)


@router.get("/stats/team-performance", response_model=Dict[str, Any])
async def get_team_performance_stats(
    date_range: int = Query(30, ge=1, le=365),
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.PROJECT_READ))
):
    """Get team performance statistics"""
    dashboard_data = await dashboard_service.get_dashboard_overview(db, current_user, date_range)
    return dashboard_data.get("team_metrics", {})


@router.get("/activity-feed", response_model=Dict[str, Any])
async def get_project_activity_feed(
    limit: int = Query(20, ge=1, le=100),
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.PROJECT_READ))
):
    """Get recent project management activity feed"""
    dashboard_data = await dashboard_service.get_dashboard_overview(db, current_user, 7)
    return {
        "activities": dashboard_data.get("recent_activity", [])[:limit],
        "total_activities": len(dashboard_data.get("recent_activity", [])),
        "limit": limit
    }


# Progress monitoring endpoints (SuperAdmin only)
@router.get("/progress/overview", response_model=Dict[str, Any])
async def get_progress_overview(
    project_id: Optional[UUID] = Query(None),
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.ADMIN_REPORTS))
):
    """Get comprehensive project progress overview (SuperAdmin only)"""
    return await progress_service.get_project_progress_overview(db, current_user, project_id)


@router.put("/progress/{project_id}/update", response_model=Dict[str, Any])
async def update_project_progress(
    project_id: UUID,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.PROJECT_MANAGE))
):
    """Recalculate and update project progress"""
    return await progress_service.update_project_progress(db, project_id, current_user)


# Health check endpoint for project management
@router.get("/health")
async def project_management_health():
    """Health check for project management services"""
    return {
        "status": "healthy",
        "service": "project-management",
        "features": [
            "dashboard",
            "due_date_management",
            "collaboration",
            "activity_tracking",
            "overdue_monitoring",
            "progress_monitoring"
        ]
    }
