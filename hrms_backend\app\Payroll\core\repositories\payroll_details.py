from core.models.payroll_details import PayrollDetailsModel
from core.databases.database import db

class PayrollDetailsRepository:
    def __init__(self) -> None:
        pass

    @classmethod
    def createPayrollDetails(self, processing_id, employee_id, net_salary, approval_status, gross_salary, tax, cost_compony, total_earnings, total_deduction, payment_status, pension, nhf):
        payroll_details = PayrollDetailsModel(
            processing_id=processing_id,
            employee_id=employee_id,
            net_salary=net_salary,
            approval_status=approval_status,
            gross_salary=gross_salary,
            tax=tax,
            cost_compony=cost_compony,
            total_earnings=total_earnings,
            total_deduction=total_deduction,
            payment_status=payment_status,
            pension=pension,
            nhf=nhf
        )
        db.session.add(payroll_details)
        db.session.commit()
        return payroll_details

    @classmethod
    def getPayrollDetails(self, id):
        return PayrollDetailsModel.query.filter(PayrollDetailsModel.id == id).first()
    
    @classmethod
    def getPayrollDetailsByKeys(self, kwargs):
        return PayrollDetailsModel.query.filter_by(**kwargs).all()

    @classmethod
    def updatePayrollDetails(self, id, **kwargs):
        payroll_details = PayrollDetailsModel.query.filter_by(id=id).first()
        if payroll_details:
            for key, value in kwargs.items():
                setattr(payroll_details, key, value)
            db.session.commit()
            return payroll_details
        else:
            return None

    @classmethod
    def deletePayrollDetails(self, id):
        return PayrollDetailsModel.query.filter(PayrollDetailsModel.id == id).delete()
        