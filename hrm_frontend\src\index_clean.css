@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&family=Poppins:wght@300;400;500;600;700;800;900&family=Roboto:wght@300;400;500;700;900&display=swap');
@tailwind base;
@tailwind components;
@tailwind utilities;

/* AgnoConnect CSS Variables */
:root {
  /* Brand Colors */
  --agno-primary-dark: #073763;
  --agno-primary: #0B2A5A;
  --agno-orange: #F47C20;
  --agno-gray-medium: #6E7C8E;
  --agno-gray-dark: #2E2E2E;
  --agno-white: #FFFFFF;

  /* Semantic Colors */
  --color-primary: var(--agno-primary);
  --color-primary-dark: var(--agno-primary-dark);
  --color-accent: var(--agno-orange);
  --color-text: var(--agno-gray-dark);
  --color-text-secondary: var(--agno-gray-medium);
  --color-background: var(--agno-white);

  /* Gradients */
  --gradient-primary: linear-gradient(135deg, var(--agno-primary-dark) 0%, var(--agno-primary) 100%);
  --gradient-accent: linear-gradient(135deg, var(--agno-orange) 0%, #ea580c 100%);
  --gradient-soft: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);

  /* Shadows */
  --shadow-soft: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  --shadow-medium: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-large: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);

  /* Typography */
  --font-primary: 'Inter', system-ui, -apple-system, sans-serif;
  --font-heading: 'Poppins', system-ui, -apple-system, sans-serif;
  --font-mono: 'Roboto Mono', monospace;
}

/* Global Styles */
body {
  font-family: var(--font-primary);
  color: var(--color-text);
  background-color: #f8fafc;
  line-height: 1.6;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Typography Enhancements */
h1, h2, h3, h4, h5, h6 {
  font-family: var(--font-heading);
  font-weight: 600;
  line-height: 1.3;
  letter-spacing: -0.025em;
}

h1 { font-size: 2.25rem; font-weight: 700; }
h2 { font-size: 1.875rem; font-weight: 600; }
h3 { font-size: 1.5rem; font-weight: 600; }
h4 { font-size: 1.25rem; font-weight: 600; }
h5 { font-size: 1.125rem; font-weight: 500; }
h6 { font-size: 1rem; font-weight: 500; }

/* AgnoConnect specific utility classes */
.agno-gradient {
  background: var(--gradient-primary);
}

.agno-gradient-accent {
  background: var(--gradient-accent);
}

.agno-text-primary {
  color: var(--agno-primary);
}

.agno-text-orange {
  color: var(--agno-orange);
}

.agno-bg-primary {
  background-color: var(--agno-primary);
}

.agno-bg-primary-dark {
  background-color: var(--agno-primary-dark);
}

.agno-bg-orange {
  background-color: var(--agno-orange);
}

/* Animation Classes */
.fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

.slide-up {
  animation: slideUp 0.3s ease-out;
}

.scale-in {
  animation: scaleIn 0.2s ease-out;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* Button styles */
.btn-agno-primary {
  background-color: var(--agno-primary);
  color: var(--agno-white);
  border: 1px solid var(--agno-primary);
  transition: all 0.2s ease-in-out;
  padding: 0.75rem 1.5rem;
  border-radius: 0.5rem;
  font-weight: 500;
  cursor: pointer;
}

.btn-agno-primary:hover {
  background-color: var(--agno-primary-dark);
  border-color: var(--agno-primary-dark);
}

.btn-agno-accent {
  background-color: var(--agno-orange);
  color: var(--agno-white);
  border: 1px solid var(--agno-orange);
  transition: all 0.2s ease-in-out;
  padding: 0.75rem 1.5rem;
  border-radius: 0.5rem;
  font-weight: 500;
  cursor: pointer;
}

.btn-agno-accent:hover {
  background-color: #ea580c;
  border-color: #ea580c;
}

/* Card shadows with AgnoConnect colors */
.shadow-agno {
  box-shadow: 0 4px 6px -1px rgba(7, 55, 99, 0.1), 0 2px 4px -1px rgba(7, 55, 99, 0.06);
}

.shadow-agno-lg {
  box-shadow: 0 10px 15px -3px rgba(7, 55, 99, 0.1), 0 4px 6px -2px rgba(7, 55, 99, 0.05);
}
