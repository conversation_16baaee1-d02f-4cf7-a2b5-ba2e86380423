/**
 * Cover Image Upload Component
 * Allows users to upload and manage their cover image with AgnoConnect branding
 */

import { useState, useRef } from 'react';
import { Camera, Upload, X, RotateCcw, Grid } from 'lucide-react';
import { defaultImages, coverPresets } from '../utils/defaultImages';

export default function CoverImageUpload({ onImageChange }) {
  const [coverImage, setCoverImage] = useState(defaultImages.cover);
  const [isUploading, setIsUploading] = useState(false);
  const [showUploadOptions, setShowUploadOptions] = useState(false);
  const [showPresets, setShowPresets] = useState(false);
  const fileInputRef = useRef(null);

  // Handle image upload
  const handleImageUpload = async (event) => {
    const file = event.target.files[0];
    if (file) {
      setIsUploading(true);
      
      // Simulate upload delay
      setTimeout(() => {
        const reader = new FileReader();
        reader.onload = (e) => {
          const imageUrl = e.target.result;
          setCoverImage(imageUrl);
          setIsUploading(false);
          setShowUploadOptions(false);
          
          // Callback to parent component
          if (onImageChange) {
            onImageChange(imageUrl);
          }
        };
        reader.readAsDataURL(file);
      }, 1000);
    }
  };

  // Reset to default background
  const resetToDefault = () => {
    setCoverImage(defaultImages.cover);
    setShowUploadOptions(false);
    if (onImageChange) {
      onImageChange(defaultImages.cover);
    }
  };

  // Select preset image
  const selectPreset = (preset) => {
    setCoverImage(preset.url);
    setShowUploadOptions(false);
    setShowPresets(false);
    if (onImageChange) {
      onImageChange(preset.url);
    }
  };

  // Remove current image (reset to default)
  const removeImage = () => {
    setCoverImage(defaultImages.cover);
    if (onImageChange) {
      onImageChange(defaultImages.cover);
    }
  };

  return (
    <div className="relative w-full h-[300px] overflow-hidden">
      {/* Background Image */}
      <img
        src={coverImage}
        alt="Cover"
        className="w-full h-full object-cover"
      />

      {/* Upload Controls */}
      <div className="absolute top-4 right-4">
        {!showUploadOptions ? (
          <button
            onClick={() => setShowUploadOptions(true)}
            className="bg-black bg-opacity-50 text-white p-2 rounded-full hover:bg-opacity-70 transition-all"
            title="Change cover image"
          >
            <Camera size={20} />
          </button>
        ) : (
          <div className="bg-white rounded-lg shadow-lg p-3 space-y-2 min-w-[200px]">
            <div className="text-sm font-medium text-gray-900 mb-2">Cover Image Options</div>
            
            {/* Upload new image */}
            <button
              onClick={() => fileInputRef.current?.click()}
              disabled={isUploading}
              className="w-full flex items-center gap-2 px-3 py-2 text-sm agno-bg-orange text-white rounded hover:bg-accent-600 disabled:opacity-50"
            >
              <Upload size={16} />
              {isUploading ? 'Uploading...' : 'Upload Image'}
            </button>

            {/* Browse presets */}
            <button
              onClick={() => setShowPresets(!showPresets)}
              className="w-full flex items-center gap-2 px-3 py-2 text-sm bg-gray-100 text-gray-700 rounded hover:bg-gray-200"
            >
              <Grid size={16} />
              Browse Presets
            </button>

            {/* Reset to default */}
            <button
              onClick={resetToDefault}
              className="w-full flex items-center gap-2 px-3 py-2 text-sm bg-gray-100 text-gray-700 rounded hover:bg-gray-200"
            >
              <RotateCcw size={16} />
              Reset to Default
            </button>

            {/* Remove current image */}
            {coverImage !== defaultImages.cover && (
              <button
                onClick={removeImage}
                className="w-full flex items-center gap-2 px-3 py-2 text-sm bg-red-100 text-red-700 rounded hover:bg-red-200"
              >
                <X size={16} />
                Remove Custom Image
              </button>
            )}

            {/* Close options */}
            <button
              onClick={() => setShowUploadOptions(false)}
              className="w-full px-3 py-2 text-sm text-gray-500 hover:text-gray-700"
            >
              Cancel
            </button>
          </div>
        )}
      </div>

      {/* Preset Selection Panel */}
      {showPresets && (
        <div className="absolute top-16 right-4 bg-white rounded-lg shadow-lg p-4 w-80 max-h-96 overflow-y-auto">
          <div className="text-sm font-medium text-gray-900 mb-3">Choose a Preset Cover</div>
          <div className="grid grid-cols-2 gap-3">
            {coverPresets.map((preset) => (
              <button
                key={preset.id}
                onClick={() => selectPreset(preset)}
                className="relative h-20 rounded-lg overflow-hidden border-2 border-gray-200 hover:border-accent-500 transition-colors group"
              >
                <img
                  src={preset.thumbnail}
                  alt={preset.name}
                  className="w-full h-full object-cover"
                />
                <div className="absolute inset-0 bg-black bg-opacity-40 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity">
                  <span className="text-white text-xs font-medium text-center px-2">{preset.name}</span>
                </div>
              </button>
            ))}
          </div>
          <button
            onClick={() => setShowPresets(false)}
            className="w-full mt-3 px-3 py-2 text-sm text-gray-500 hover:text-gray-700"
          >
            Close
          </button>
        </div>
      )}

      {/* Upload Progress */}
      {isUploading && (
        <div className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center">
          <div className="bg-white rounded-lg p-6 text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 agno-text-orange mx-auto mb-4"></div>
            <div className="text-gray-700">Uploading image...</div>
          </div>
        </div>
      )}

      {/* Hidden file input */}
      <input
        ref={fileInputRef}
        type="file"
        accept="image/*"
        onChange={handleImageUpload}
        className="hidden"
      />

      {/* Overlay gradient for better text readability */}
      <div className="absolute inset-0 bg-gradient-to-b from-transparent via-transparent to-black/20 pointer-events-none"></div>
    </div>
  );
}

// Preset cover images component (optional)
export function PresetCoverImages({ onSelect }) {
  const presetImages = [
    {
      id: 'gradient-blue',
      name: 'AgnoConnect Blue',
      type: 'gradient',
      className: 'agno-gradient'
    },
    {
      id: 'gradient-orange',
      name: 'AgnoConnect Orange',
      type: 'gradient', 
      className: 'agno-gradient-accent'
    },
    {
      id: 'office',
      name: 'Modern Office',
      type: 'image',
      url: '/images/office-background.jpg'
    },
    {
      id: 'workspace',
      name: 'Workspace',
      type: 'image',
      url: '/images/workspace-background.jpg'
    }
  ];

  return (
    <div className="grid grid-cols-2 gap-3">
      {presetImages.map((preset) => (
        <button
          key={preset.id}
          onClick={() => onSelect(preset)}
          className="relative h-20 rounded-lg overflow-hidden border-2 border-gray-200 hover:border-accent-500 transition-colors"
        >
          {preset.type === 'gradient' ? (
            <div className={`w-full h-full ${preset.className}`}></div>
          ) : (
            <img
              src={preset.url}
              alt={preset.name}
              className="w-full h-full object-cover"
            />
          )}
          <div className="absolute inset-0 bg-black bg-opacity-40 flex items-center justify-center">
            <span className="text-white text-xs font-medium">{preset.name}</span>
          </div>
        </button>
      ))}
    </div>
  );
}
