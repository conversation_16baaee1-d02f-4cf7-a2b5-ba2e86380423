from core.repositories.employeeincometax import EmployeeIncTaxRepository

class EmployeeIncTaxService:
    def __init__(self) -> None:
        self.repository = EmployeeIncTaxRepository()

    def createEmployeeIncTax(self, Kwargs):
        # print(Kwargs)
        return self.repository.createEmployeeIncTaxes(**Kwargs)
    
    def getEmployeeIncTax(self, id):
        return self.repository.getEmployeeIncTaxes(id)
    
    def updateEmployeeIncTax(self, id, **Kwargs):
        return self.repository.updateEmployeeIncTaxes(id, **Kwargs)
    
    def getEmployeeIncTaxByKey(self, Kwarg):
        return self.repository.getEmployeeIncTaxesByKeys(Kwarg)
    
    def deleteEmployeeIncTax(self, id):
        return self.repository.deleteEmployeeIncTaxes(id)