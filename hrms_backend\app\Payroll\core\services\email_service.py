from flask_mail import Message
from flask import current_app

class EmailService:
    @staticmethod
    def send_email(subject, recipient, text_body=None, html_body=None, attachments=None):

        try:
            from app import mail
            
            if isinstance(recipient, str):
                recipient = [recipient]

            msg = Message(subject, 
                          sender=current_app.config['MAIL_USERNAME'], 
                          recipients=recipient)
            
            if text_body:
                msg.body = text_body
            if html_body:
                msg.html = html_body
            
            if attachments:
                for attachment in attachments:
                    msg.attach(
                        filename=attachment['filename'],
                        content_type=attachment['content_type'],
                        data=attachment['data']
                    )
            
            mail.send(msg)
            return True
        
        except Exception as e:
            print(f"Failed to send email: {str(e)}")
            return False

