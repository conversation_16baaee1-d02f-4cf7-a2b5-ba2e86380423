from core.models.activity_employee import EmployeeActivityModel
from core.databases.database import db
from sqlalchemy.exc import SQLAlchemyError

class EmployeeActivityRepository:
    def __init__(self) -> None:
        pass

    @classmethod
    def log_activity(cls, employee_id, message):
        try:
            activity = EmployeeActivityModel(
                employee_id=employee_id,
                message=message
            )
            db.session.add(activity)
            db.session.commit()
            return activity
        except SQLAlchemyError as e:
            print(f"Database Error: {str(e)}")
            db.session.rollback()
            raise e
        except Exception as e:
            print(f"Unexpected Error: {str(e)}")
            raise e

    @classmethod
    def fetch_all_by_employee(cls, employee_id):
        return EmployeeActivityModel.query.filter_by(employee_id=employee_id).order_by(EmployeeActivityModel.created_at.desc()).all()

    @classmethod
    def fetch_by_id(cls, id):
        return EmployeeActivityModel.query.filter_by(id=id).first()

    @classmethod
    def delete_activity(cls, id):
        EmployeeActivityModel.query.filter_by(id=id).delete()
        db.session.commit()

