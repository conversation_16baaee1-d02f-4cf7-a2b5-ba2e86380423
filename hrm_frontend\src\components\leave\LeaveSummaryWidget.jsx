/**
 * Professional Leave Summary Widget
 * Features: Quick overview, visual indicators, responsive design
 */

import React, { useState, useEffect } from 'react';
import {
  Calendar,
  Clock,
  TrendingUp,
  TrendingDown,
  Users,
  CheckCircle,
  AlertTriangle,
  ArrowRight
} from 'lucide-react';
import apiService from '../../services/api';

const LeaveSummaryWidget = ({ className = '', onViewDetails }) => {
  const [summary, setSummary] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadSummary();
  }, []);

  const loadSummary = async () => {
    try {
      setLoading(true);
      const dashboardData = await apiService.getLeaveDashboard();
      setSummary(dashboardData);
    } catch (error) {
      console.error('Error loading leave summary:', error);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className={`card ${className}`}>
        <div className="card-body">
          <div className="flex items-center justify-center h-48">
            <div className="spinner h-8 w-8"></div>
          </div>
        </div>
      </div>
    );
  }

  const { summary: balanceSummary, analytics, upcoming_leaves, pending_approvals } = summary || {};
  const currentYearStats = analytics?.current_year_stats || {};

  return (
    <div className={`card fade-in ${className}`}>
      <div className="card-header">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-semibold text-gray-900">Leave Overview</h3>
          <button
            onClick={onViewDetails}
            className="text-agno-primary hover:text-blue-700 text-sm font-medium flex items-center"
          >
            View Details
            <ArrowRight size={14} className="ml-1" />
          </button>
        </div>
      </div>

      <div className="card-body space-y-6">
        {/* Quick Stats Grid */}
        <div className="grid grid-cols-2 lg:grid-cols-4 gap-4">
          {/* Available Balance */}
          <div className="bg-gradient-to-br from-green-50 to-green-100 rounded-lg p-4 border border-green-200">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-xs font-medium text-green-600 uppercase tracking-wide">Available</p>
                <p className="text-2xl font-bold text-green-700 mt-1">
                  {balanceSummary?.my_balance?.[0]?.available_balance || 0}
                </p>
                <p className="text-xs text-green-600">days</p>
              </div>
              <div className="w-10 h-10 bg-green-200 rounded-lg flex items-center justify-center">
                <Calendar className="w-5 h-5 text-green-600" />
              </div>
            </div>
          </div>

          {/* Pending Requests */}
          <div className="bg-gradient-to-br from-amber-50 to-amber-100 rounded-lg p-4 border border-amber-200">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-xs font-medium text-amber-600 uppercase tracking-wide">Pending</p>
                <p className="text-2xl font-bold text-amber-700 mt-1">
                  {currentYearStats.pending_requests || 0}
                </p>
                <p className="text-xs text-amber-600">requests</p>
              </div>
              <div className="w-10 h-10 bg-amber-200 rounded-lg flex items-center justify-center">
                <Clock className="w-5 h-5 text-amber-600" />
              </div>
            </div>
          </div>

          {/* Approved This Year */}
          <div className="bg-gradient-to-br from-blue-50 to-blue-100 rounded-lg p-4 border border-blue-200">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-xs font-medium text-blue-600 uppercase tracking-wide">Approved</p>
                <p className="text-2xl font-bold text-blue-700 mt-1">
                  {currentYearStats.approved_requests || 0}
                </p>
                <p className="text-xs text-blue-600">this year</p>
              </div>
              <div className="w-10 h-10 bg-blue-200 rounded-lg flex items-center justify-center">
                <CheckCircle className="w-5 h-5 text-blue-600" />
              </div>
            </div>
          </div>

          {/* Approval Rate */}
          <div className="bg-gradient-to-br from-purple-50 to-purple-100 rounded-lg p-4 border border-purple-200">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-xs font-medium text-purple-600 uppercase tracking-wide">Rate</p>
                <p className="text-2xl font-bold text-purple-700 mt-1">
                  {currentYearStats.approval_rate || 0}%
                </p>
                <p className="text-xs text-purple-600">approval</p>
              </div>
              <div className="w-10 h-10 bg-purple-200 rounded-lg flex items-center justify-center">
                <TrendingUp className="w-5 h-5 text-purple-600" />
              </div>
            </div>
          </div>
        </div>

        {/* Balance Breakdown */}
        {balanceSummary?.my_balance?.[0] && (
          <div className="bg-gray-50 rounded-lg p-4 border border-gray-200">
            <h4 className="text-sm font-semibold text-gray-700 mb-3">Leave Balance Breakdown</h4>
            <div className="space-y-3">
              {balanceSummary.my_balance.map((balance, index) => {
                const utilizationRate = (balance.used_balance / balance.total_entitlement) * 100;
                
                return (
                  <div key={index} className="bg-white rounded-lg p-3 border border-gray-200">
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-sm font-medium text-gray-900 capitalize">
                        {balance.leave_type || 'Leave'}
                      </span>
                      <span className="text-sm text-gray-500">
                        {balance.available_balance}/{balance.total_entitlement} days
                      </span>
                    </div>
                    
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div 
                        className={`h-2 rounded-full transition-all duration-300 ${
                          utilizationRate >= 80 ? 'bg-red-500' :
                          utilizationRate >= 60 ? 'bg-amber-500' : 'bg-green-500'
                        }`}
                        style={{ width: `${Math.min(utilizationRate, 100)}%` }}
                      ></div>
                    </div>
                    
                    <div className="flex justify-between text-xs text-gray-500 mt-1">
                      <span>Used: {balance.used_balance}</span>
                      <span>{utilizationRate.toFixed(1)}% utilized</span>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        )}

        {/* Upcoming Leaves */}
        {upcoming_leaves && upcoming_leaves.length > 0 && (
          <div className="bg-blue-50 rounded-lg p-4 border border-blue-200">
            <h4 className="text-sm font-semibold text-blue-700 mb-3 flex items-center">
              <Calendar className="w-4 h-4 mr-2" />
              Upcoming Leaves
            </h4>
            <div className="space-y-2">
              {upcoming_leaves.slice(0, 3).map((leave, index) => (
                <div key={index} className="flex items-center justify-between bg-white rounded-lg p-3 border border-blue-200">
                  <div>
                    <p className="text-sm font-medium text-gray-900">{leave.employee_name}</p>
                    <p className="text-xs text-gray-500 capitalize">{leave.leave_type}</p>
                  </div>
                  <div className="text-right">
                    <p className="text-xs font-medium text-blue-600">
                      {new Date(leave.start_date).toLocaleDateString()}
                    </p>
                    <p className="text-xs text-gray-500">{leave.total_days} days</p>
                  </div>
                </div>
              ))}
              
              {upcoming_leaves.length > 3 && (
                <div className="text-center">
                  <button
                    onClick={onViewDetails}
                    className="text-xs text-blue-600 hover:text-blue-700 font-medium"
                  >
                    View {upcoming_leaves.length - 3} more
                  </button>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Pending Approvals Alert */}
        {pending_approvals && pending_approvals.length > 0 && (
          <div className="bg-amber-50 rounded-lg p-4 border border-amber-200">
            <div className="flex items-center">
              <AlertTriangle className="w-5 h-5 text-amber-600 mr-3" />
              <div>
                <p className="text-sm font-medium text-amber-800">
                  {pending_approvals.length} request{pending_approvals.length > 1 ? 's' : ''} awaiting approval
                </p>
                <button
                  onClick={onViewDetails}
                  className="text-xs text-amber-600 hover:text-amber-700 font-medium mt-1"
                >
                  Review pending requests →
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Quick Actions */}
        <div className="flex flex-col sm:flex-row gap-3">
          <button className="btn-primary flex-1 text-sm py-2">
            <Calendar className="w-4 h-4 mr-2" />
            Apply for Leave
          </button>
          <button
            onClick={onViewDetails}
            className="btn-secondary flex-1 text-sm py-2"
          >
            <Users className="w-4 h-4 mr-2" />
            View Calendar
          </button>
        </div>
      </div>
    </div>
  );
};

export default LeaveSummaryWidget;
