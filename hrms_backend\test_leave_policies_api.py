#!/usr/bin/env python3
"""
Test the leave policies API
"""

import requests
import json

def test_leave_policies_api():
    """Test the leave policies API endpoint"""
    
    base_url = "http://localhost:8085"
    
    # Test token (you may need to update this)
    token = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************************************************************************************************.Hs8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8"
    
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    try:
        # Test GET /api/leave/policies
        print("Testing GET /api/leave/policies...")
        response = requests.get(f"{base_url}/api/leave/policies", headers=headers)
        
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            policies = response.json()
            print(f"✅ Found {len(policies)} leave policies:")
            
            for policy in policies:
                print(f"  - {policy['name']} ({policy['leave_type']}) - {policy['annual_entitlement']} days/year")
        else:
            print(f"❌ Error: {response.status_code}")
            print(f"Response: {response.text}")
            
    except Exception as e:
        print(f"❌ Error testing API: {e}")

if __name__ == "__main__":
    test_leave_policies_api()
