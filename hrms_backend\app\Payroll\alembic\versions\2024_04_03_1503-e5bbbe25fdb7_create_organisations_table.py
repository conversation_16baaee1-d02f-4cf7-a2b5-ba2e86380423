"""create organisations table

Revision ID: e5bbbe25fdb7
Revises: dd39533fe0bf
Create Date: 2024-04-03 15:03:34.171117

"""
from typing import Sequence, Union

from alembic import op
from sqlalchemy import TIMESTAMP, func
from sqlalchemy import Integer, String, ForeignKey, Column

# revision identifiers, used by Alembic.
revision: str = 'e5bbbe25fdb7'
down_revision: Union[str, None] = 'dd39533fe0bf'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
   
    op.create_table(
        'organisations',
        Column('id', Integer, primary_key=True),
        Column('slug', String(50),unique=True, nullable=False), #specify the organisation created id 
        Column('organisation_name', String(50), nullable=False),
        Column('email', String(50), nullable=False),
        Column('address', String(80), nullable=True, server_default="null"),
        Column('country', String(50), nullable=True, server_default="null"),
        Column('city', String(50), nullable=True, server_default="null"),
        Column('state', String(50), nullable=True, server_default="null"),
        Column('zipcode', String(50), nullable=True, server_default="null"),
        Column('phone', String(50), nullable=True, server_default="null"),
        Column('industry', String(50), nullable=True, server_default="null"),
        Column('type', String(50), nullable=True, server_default="null"),  
        Column("user_id", Integer, ForeignKey("users.id")),
        Column("timestamp", TIMESTAMP, server_default=func.now()),
        Column("logoUrl", String(200), nullable=True,)
    )

def downgrade() -> None:
    op.drop_table('organisations')
