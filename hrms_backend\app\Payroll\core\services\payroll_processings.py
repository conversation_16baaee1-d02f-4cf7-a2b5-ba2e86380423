from core.repositories.payroll_processings import PayrollProcessingsRepository

class PayrollProcessingsService:
    def __init__(self) -> None:
        self.repository = PayrollProcessingsRepository()

    def createPayrollProcessings(self, Kwargs):
        # print(Kwargs)
        return self.repository.createPayrollProcessings(**Kwargs)
    
    def getPayrollProcessings(self, id):
        return self.repository.getPayrollProcessings(id)
    
    def updatePayrollProcessings(self, id, **Kwargs):
        return self.repository.updatePayrollProcessings(id, **Kwargs)
    
    def getPayrollProcessingsByKey(self, Kwarg):
        return self.repository.getPayrollProcessingsByKeys(Kwarg)
    
    def deletePayrollProcessings(self, id):
        return self.repository.deletePayrollProcessings(id)