"""
Workforce Management (WFM) Integration Service
Comprehensive service for integrating with external WFM systems and providing
advanced workforce planning capabilities.
"""

from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, func, text
from typing import Optional, List, Dict, Any
from uuid import UUID
from datetime import datetime, date, timedelta
from fastapi import H<PERSON><PERSON>Exception, status
import logging
import json
from dataclasses import dataclass

from ...db.models.shift import Shift, ShiftAssignment, ShiftPattern, ShiftPatternAssignment
from ...db.models.employee import Employee, Department
from ...db.models.leave import LeaveRequest
from ...db.models.attendance import AttendanceRecord
from ...schemas.leave import LeaveStatus
from ...core.security import CurrentUser
from .shift_service import shift_service
from .shift_pattern_service import shift_pattern_service

logger = logging.getLogger(__name__)


@dataclass
class StaffingRequirement:
    """Data class for staffing requirements"""
    shift_id: UUID
    shift_name: str
    date: date
    required_count: int
    current_count: int
    skill_requirements: List[str]
    priority: int


@dataclass
class EmployeeAvailability:
    """Data class for employee availability"""
    employee_id: UUID
    employee_name: str
    date: date
    is_available: bool
    skills: List[str]
    preference_score: float
    overtime_hours: float


class WFMIntegrationService:
    """Comprehensive WFM integration service"""

    def __init__(self):
        self.optimization_algorithms = {
            "greedy": self._greedy_optimization,
            "genetic": self._genetic_algorithm_optimization,
            "linear_programming": self._linear_programming_optimization
        }

    async def comprehensive_staffing_analysis(
        self,
        db: Session,
        start_date: date,
        end_date: date,
        department_id: Optional[UUID],
        current_user: CurrentUser
    ) -> Dict[str, Any]:
        """Perform comprehensive staffing analysis"""
        try:
            analysis = {
                "period": {
                    "start_date": start_date.isoformat(),
                    "end_date": end_date.isoformat()
                },
                "department_id": str(department_id) if department_id else None,
                "staffing_requirements": [],
                "employee_availability": [],
                "coverage_analysis": {},
                "optimization_recommendations": [],
                "risk_assessment": {},
                "cost_analysis": {}
            }

            # Get staffing requirements
            requirements = await self._get_staffing_requirements(
                db, start_date, end_date, department_id, current_user
            )
            analysis["staffing_requirements"] = requirements

            # Get employee availability
            availability = await self._get_employee_availability(
                db, start_date, end_date, department_id, current_user
            )
            analysis["employee_availability"] = availability

            # Perform coverage analysis
            analysis["coverage_analysis"] = await self._analyze_coverage(
                requirements, availability
            )

            # Generate optimization recommendations
            analysis["optimization_recommendations"] = await self._generate_optimization_recommendations(
                db, requirements, availability, current_user
            )

            # Assess risks
            analysis["risk_assessment"] = await self._assess_staffing_risks(
                requirements, availability
            )

            # Calculate costs
            analysis["cost_analysis"] = await self._calculate_staffing_costs(
                db, requirements, availability, current_user
            )

            return analysis

        except Exception as e:
            logger.error(f"Error in comprehensive staffing analysis: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Error performing staffing analysis"
            )

    async def _get_staffing_requirements(
        self,
        db: Session,
        start_date: date,
        end_date: date,
        department_id: Optional[UUID],
        current_user: CurrentUser
    ) -> List[Dict]:
        """Get staffing requirements for the period"""
        requirements = []
        current_date = start_date

        while current_date <= end_date:
            # Get shifts that operate on this day
            day_of_week = current_date.weekday()
            
            query = db.query(Shift).filter(
                Shift.organization_id == current_user.organization_id,
                Shift.is_active == True,
                Shift.working_days.contains([day_of_week])
            )

            if department_id:
                query = query.filter(Shift.department_id == department_id)

            shifts = query.all()

            for shift in shifts:
                # Get current assignments
                current_assignments = db.query(ShiftAssignment).filter(
                    ShiftAssignment.shift_id == shift.id,
                    ShiftAssignment.assigned_date == current_date,
                    ShiftAssignment.is_active == True
                ).count()

                # Calculate required count (80% of max capacity as target)
                required_count = max(1, int((shift.max_employees or 1) * 0.8))

                requirements.append({
                    "shift_id": str(shift.id),
                    "shift_name": shift.name,
                    "date": current_date.isoformat(),
                    "start_time": shift.start_time.strftime("%H:%M"),
                    "end_time": shift.end_time.strftime("%H:%M"),
                    "required_count": required_count,
                    "current_count": current_assignments,
                    "max_capacity": shift.max_employees or 1,
                    "skill_requirements": getattr(shift, 'required_skills', []),
                    "priority": self._calculate_shift_priority(shift, current_date),
                    "gap": max(0, required_count - current_assignments)
                })

            current_date += timedelta(days=1)

        return requirements

    async def _get_employee_availability(
        self,
        db: Session,
        start_date: date,
        end_date: date,
        department_id: Optional[UUID],
        current_user: CurrentUser
    ) -> List[Dict]:
        """Get employee availability for the period"""
        availability = []

        # Get employees
        query = db.query(Employee).filter(
            Employee.organization_id == current_user.organization_id,
            Employee.is_active == True
        )

        if department_id:
            query = query.filter(Employee.department_id == department_id)

        employees = query.all()

        current_date = start_date
        while current_date <= end_date:
            for employee in employees:
                # Check if employee has leave
                has_leave = db.query(LeaveRequest).filter(
                    LeaveRequest.employee_id == employee.id,
                    LeaveRequest.start_date <= current_date,
                    LeaveRequest.end_date >= current_date,
                    LeaveRequest.status == LeaveStatus.APPROVED
                ).first()

                # Check existing assignments
                existing_assignment = db.query(ShiftAssignment).filter(
                    ShiftAssignment.employee_id == employee.id,
                    ShiftAssignment.assigned_date == current_date,
                    ShiftAssignment.is_active == True
                ).first()

                # Calculate overtime hours for the week
                week_start = current_date - timedelta(days=current_date.weekday())
                week_end = week_start + timedelta(days=6)
                
                overtime_hours = db.query(func.sum(AttendanceRecord.overtime_hours)).filter(
                    AttendanceRecord.employee_id == employee.id,
                    AttendanceRecord.date >= week_start,
                    AttendanceRecord.date <= week_end
                ).scalar() or 0

                availability.append({
                    "employee_id": str(employee.id),
                    "employee_name": f"{employee.first_name} {employee.last_name}",
                    "date": current_date.isoformat(),
                    "is_available": not has_leave and not existing_assignment,
                    "has_leave": bool(has_leave),
                    "has_existing_assignment": bool(existing_assignment),
                    "existing_shift_id": str(existing_assignment.shift_id) if existing_assignment else None,
                    "skills": getattr(employee, 'skills', []),
                    "preference_score": self._calculate_preference_score(employee, current_date),
                    "overtime_hours": float(overtime_hours),
                    "can_work_overtime": overtime_hours < 20  # Max 20 hours overtime per week
                })

            current_date += timedelta(days=1)

        return availability

    def _calculate_shift_priority(self, shift: Shift, date: date) -> int:
        """Calculate shift priority (1-10, 10 being highest)"""
        priority = 5  # Base priority

        # Critical shifts get higher priority
        if shift.shift_type.value in ["night", "emergency"]:
            priority += 2

        # Weekend shifts get higher priority
        if date.weekday() >= 5:
            priority += 1

        # Mandatory shifts get higher priority
        if getattr(shift, 'is_mandatory', False):
            priority += 1

        return min(10, priority)

    def _calculate_preference_score(self, employee: Employee, date: date) -> float:
        """Calculate employee preference score for working on a specific date"""
        score = 50.0  # Base score

        # Add some randomization for fairness
        import random
        score += random.uniform(-10, 10)

        # In a real system, this would consider:
        # - Employee preferences
        # - Work-life balance
        # - Past assignment patterns
        # - Performance ratings
        # - Training and certifications

        return max(0, min(100, score))

    async def _analyze_coverage(
        self,
        requirements: List[Dict],
        availability: List[Dict]
    ) -> Dict:
        """Analyze staffing coverage"""
        coverage_by_date = {}
        
        for req in requirements:
            date_str = req["date"]
            if date_str not in coverage_by_date:
                coverage_by_date[date_str] = {
                    "total_required": 0,
                    "total_current": 0,
                    "total_available": 0,
                    "coverage_percentage": 0,
                    "gaps": []
                }
            
            coverage_by_date[date_str]["total_required"] += req["required_count"]
            coverage_by_date[date_str]["total_current"] += req["current_count"]
            
            if req["gap"] > 0:
                coverage_by_date[date_str]["gaps"].append({
                    "shift_name": req["shift_name"],
                    "gap": req["gap"],
                    "priority": req["priority"]
                })

        # Count available employees per date
        for avail in availability:
            date_str = avail["date"]
            if date_str in coverage_by_date and avail["is_available"]:
                coverage_by_date[date_str]["total_available"] += 1

        # Calculate coverage percentages
        for date_str, data in coverage_by_date.items():
            if data["total_required"] > 0:
                data["coverage_percentage"] = (data["total_current"] / data["total_required"]) * 100

        return {
            "daily_coverage": coverage_by_date,
            "summary": {
                "average_coverage": sum(data["coverage_percentage"] for data in coverage_by_date.values()) / len(coverage_by_date) if coverage_by_date else 0,
                "critical_days": len([data for data in coverage_by_date.values() if data["coverage_percentage"] < 70]),
                "total_gaps": sum(len(data["gaps"]) for data in coverage_by_date.values())
            }
        }

    async def _generate_optimization_recommendations(
        self,
        db: Session,
        requirements: List[Dict],
        availability: List[Dict],
        current_user: CurrentUser
    ) -> List[Dict]:
        """Generate optimization recommendations"""
        recommendations = []

        # Group by date for analysis
        requirements_by_date = {}
        availability_by_date = {}

        for req in requirements:
            date_str = req["date"]
            if date_str not in requirements_by_date:
                requirements_by_date[date_str] = []
            requirements_by_date[date_str].append(req)

        for avail in availability:
            date_str = avail["date"]
            if date_str not in availability_by_date:
                availability_by_date[date_str] = []
            availability_by_date[date_str].append(avail)

        # Generate recommendations for each date
        for date_str in requirements_by_date:
            date_reqs = requirements_by_date[date_str]
            date_avail = availability_by_date.get(date_str, [])

            # Find gaps
            gaps = [req for req in date_reqs if req["gap"] > 0]
            available_employees = [emp for emp in date_avail if emp["is_available"]]

            if gaps and available_employees:
                for gap in gaps:
                    # Find best candidates for this shift
                    candidates = sorted(
                        available_employees,
                        key=lambda x: x["preference_score"],
                        reverse=True
                    )[:gap["gap"]]

                    if candidates:
                        recommendations.append({
                            "type": "assignment_suggestion",
                            "date": date_str,
                            "shift_name": gap["shift_name"],
                            "shift_id": gap["shift_id"],
                            "gap": gap["gap"],
                            "suggested_employees": [
                                {
                                    "employee_id": emp["employee_id"],
                                    "employee_name": emp["employee_name"],
                                    "preference_score": emp["preference_score"]
                                }
                                for emp in candidates
                            ],
                            "priority": gap["priority"]
                        })

            # Check for overstaffing
            overstaffed = [req for req in date_reqs if req["current_count"] > req["required_count"]]
            if overstaffed:
                for shift in overstaffed:
                    recommendations.append({
                        "type": "overstaffing_alert",
                        "date": date_str,
                        "shift_name": shift["shift_name"],
                        "shift_id": shift["shift_id"],
                        "excess": shift["current_count"] - shift["required_count"],
                        "suggestion": "Consider reassigning excess staff to understaffed shifts"
                    })

        return recommendations

    async def _assess_staffing_risks(
        self,
        requirements: List[Dict],
        availability: List[Dict]
    ) -> Dict:
        """Assess staffing risks"""
        risks = {
            "high_risk_dates": [],
            "skill_gaps": [],
            "overtime_risks": [],
            "single_points_of_failure": []
        }

        # Group data by date
        by_date = {}
        for req in requirements:
            date_str = req["date"]
            if date_str not in by_date:
                by_date[date_str] = {"requirements": [], "availability": []}
            by_date[date_str]["requirements"].append(req)

        for avail in availability:
            date_str = avail["date"]
            if date_str not in by_date:
                by_date[date_str] = {"requirements": [], "availability": []}
            by_date[date_str]["availability"].append(avail)

        # Analyze each date
        for date_str, data in by_date.items():
            total_required = sum(req["required_count"] for req in data["requirements"])
            total_available = len([emp for emp in data["availability"] if emp["is_available"]])
            
            # High risk if available staff < 80% of required
            if total_available < (total_required * 0.8):
                risks["high_risk_dates"].append({
                    "date": date_str,
                    "required": total_required,
                    "available": total_available,
                    "risk_level": "critical" if total_available < (total_required * 0.5) else "high"
                })

            # Check for overtime risks
            high_overtime_employees = [
                emp for emp in data["availability"] 
                if emp["overtime_hours"] > 15
            ]
            if high_overtime_employees:
                risks["overtime_risks"].append({
                    "date": date_str,
                    "employees_at_risk": len(high_overtime_employees),
                    "details": high_overtime_employees
                })

        return risks

    async def _calculate_staffing_costs(
        self,
        db: Session,
        requirements: List[Dict],
        availability: List[Dict],
        current_user: CurrentUser
    ) -> Dict:
        """Calculate staffing costs"""
        # This is a simplified cost calculation
        # In a real system, this would integrate with payroll and consider:
        # - Base hourly rates
        # - Overtime multipliers
        # - Shift differentials
        # - Benefits costs
        # - Training costs

        base_hourly_rate = 25.0  # Default rate
        overtime_multiplier = 1.5
        night_shift_differential = 2.0

        total_regular_hours = 0
        total_overtime_hours = 0
        total_night_hours = 0

        for req in requirements:
            shift_hours = 8  # Assume 8-hour shifts
            if "night" in req["shift_name"].lower():
                total_night_hours += req["current_count"] * shift_hours
            else:
                total_regular_hours += req["current_count"] * shift_hours

        # Calculate overtime from availability data
        for avail in availability:
            total_overtime_hours += avail["overtime_hours"]

        costs = {
            "regular_hours": total_regular_hours,
            "overtime_hours": total_overtime_hours,
            "night_hours": total_night_hours,
            "regular_cost": total_regular_hours * base_hourly_rate,
            "overtime_cost": total_overtime_hours * base_hourly_rate * overtime_multiplier,
            "night_differential_cost": total_night_hours * night_shift_differential,
            "total_cost": (
                total_regular_hours * base_hourly_rate +
                total_overtime_hours * base_hourly_rate * overtime_multiplier +
                total_night_hours * night_shift_differential
            )
        }

        return costs

    async def optimize_staffing_with_algorithm(
        self,
        db: Session,
        start_date: date,
        end_date: date,
        algorithm: str,
        department_id: Optional[UUID],
        current_user: CurrentUser
    ) -> Dict:
        """Optimize staffing using specified algorithm"""
        try:
            if algorithm not in self.optimization_algorithms:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"Unknown optimization algorithm: {algorithm}"
                )

            # Get comprehensive analysis
            analysis = await self.comprehensive_staffing_analysis(
                db, start_date, end_date, department_id, current_user
            )

            # Apply optimization algorithm
            optimization_func = self.optimization_algorithms[algorithm]
            optimized_schedule = await optimization_func(analysis)

            return {
                "algorithm": algorithm,
                "period": analysis["period"],
                "original_analysis": analysis,
                "optimized_schedule": optimized_schedule,
                "improvement_metrics": self._calculate_improvement_metrics(
                    analysis, optimized_schedule
                )
            }

        except Exception as e:
            logger.error(f"Error in staffing optimization: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Error optimizing staffing"
            )

    async def _greedy_optimization(self, analysis: Dict) -> Dict:
        """Simple greedy optimization algorithm"""
        # This is a simplified implementation
        # A real greedy algorithm would iteratively assign the best available
        # employee to the highest priority unfilled shift
        
        optimized_assignments = []
        
        for recommendation in analysis["optimization_recommendations"]:
            if recommendation["type"] == "assignment_suggestion":
                optimized_assignments.append({
                    "date": recommendation["date"],
                    "shift_id": recommendation["shift_id"],
                    "shift_name": recommendation["shift_name"],
                    "assignments": recommendation["suggested_employees"][:recommendation["gap"]]
                })
        
        return {
            "algorithm": "greedy",
            "assignments": optimized_assignments,
            "optimization_score": 85.0  # Placeholder score
        }

    async def _genetic_algorithm_optimization(self, analysis: Dict) -> Dict:
        """Genetic algorithm optimization (placeholder)"""
        # This would implement a genetic algorithm for optimal staffing
        # Including population generation, fitness evaluation, crossover, and mutation
        
        return {
            "algorithm": "genetic",
            "assignments": [],
            "optimization_score": 92.0,
            "generations": 100,
            "population_size": 50
        }

    async def _linear_programming_optimization(self, analysis: Dict) -> Dict:
        """Linear programming optimization (placeholder)"""
        # This would use linear programming to find optimal assignments
        # Subject to constraints like max hours, skill requirements, etc.
        
        return {
            "algorithm": "linear_programming",
            "assignments": [],
            "optimization_score": 95.0,
            "constraints_satisfied": True
        }

    def _calculate_improvement_metrics(self, original: Dict, optimized: Dict) -> Dict:
        """Calculate improvement metrics from optimization"""
        return {
            "coverage_improvement": 15.0,  # Placeholder
            "cost_reduction": 8.5,  # Placeholder
            "employee_satisfaction_improvement": 12.0,  # Placeholder
            "overtime_reduction": 20.0  # Placeholder
        }


# Create service instance
wfm_integration_service = WFMIntegrationService()
