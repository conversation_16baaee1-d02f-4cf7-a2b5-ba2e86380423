import re
import json
import logging
import email
from typing import Dict, List, Optional, Any
from datetime import datetime
from sqlalchemy.orm import Session
from fastapi import HTT<PERSON>Exception, status
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart

from ...db.models.ticket import Ticket, TicketType, TicketPriority, TicketStatus
from ...db.models.employee import Employee
from ...core.security import CurrentUser
from .ai_categorization_service import AICategorization
from .sentiment_analysis_service import SentimentAnalysisService
from .smart_routing_service import SmartRoutingEngine

logger = logging.getLogger(__name__)


class MultiChannelService:
    """Multi-channel ticket creation and management service"""

    def __init__(self):
        self.ai_categorization = AICategorization()
        self.sentiment_analysis = SentimentAnalysisService()
        self.smart_routing = SmartRoutingEngine()
        
        # Channel configurations
        self.channel_configs = {
            "email": {
                "enabled": True,
                "auto_categorize": True,
                "auto_route": True,
                "default_priority": "medium"
            },
            "web": {
                "enabled": True,
                "auto_categorize": True,
                "auto_route": True,
                "default_priority": "medium"
            },
            "chatbot": {
                "enabled": True,
                "auto_categorize": True,
                "auto_route": True,
                "default_priority": "high"  # Chat usually indicates urgency
            },
            "api": {
                "enabled": True,
                "auto_categorize": False,  # API calls usually pre-categorized
                "auto_route": True,
                "default_priority": "medium"
            },
            "phone": {
                "enabled": True,
                "auto_categorize": True,
                "auto_route": True,
                "default_priority": "high"  # Phone calls indicate urgency
            },
            "mobile": {
                "enabled": True,
                "auto_categorize": True,
                "auto_route": True,
                "default_priority": "medium"
            }
        }

    async def create_ticket_from_email(
        self,
        db: Session,
        email_data: Dict[str, Any],
        organization_id: str
    ) -> Dict[str, Any]:
        """Create ticket from email"""
        try:
            # Parse email data
            sender_email = email_data.get("from", "")
            subject = email_data.get("subject", "")
            body = email_data.get("body", "")
            attachments = email_data.get("attachments", [])
            
            # Find or create user
            user = await self._find_or_create_user_from_email(db, sender_email, organization_id)
            if not user:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Unable to identify user from email"
                )

            # Extract ticket information
            ticket_data = await self._extract_ticket_from_email(subject, body)
            
            # AI categorization
            if self.channel_configs["email"]["auto_categorize"]:
                ai_analysis = await self.ai_categorization.categorize_ticket(
                    ticket_data["title"], ticket_data["description"]
                )
                
                if ai_analysis.get("predicted_type"):
                    ticket_data["ticket_type"] = ai_analysis["predicted_type"]
                if ai_analysis.get("predicted_priority"):
                    ticket_data["priority"] = ai_analysis["predicted_priority"]
                
                ticket_data["ai_analysis"] = ai_analysis

            # Sentiment analysis
            sentiment = await self.sentiment_analysis.analyze_sentiment(
                f"{ticket_data['title']} {ticket_data['description']}"
            )
            
            # Adjust priority based on sentiment
            if sentiment.get("escalation_risk", {}).get("level") == "high":
                ticket_data["priority"] = "urgent"
            elif sentiment.get("urgency", {}).get("level") == "critical":
                ticket_data["priority"] = "critical"

            # Create ticket
            ticket = await self._create_ticket_with_metadata(
                db, ticket_data, user, "email", sentiment, attachments
            )

            # Auto-route if enabled
            if self.channel_configs["email"]["auto_route"]:
                routing_result = await self.smart_routing.find_best_agent(
                    db, ticket, organization_id
                )
                if routing_result:
                    ticket.assigned_to = routing_result["agent_id"]
                    ticket.auto_assigned = True
                    db.commit()

            return {
                "ticket_id": str(ticket.id),
                "ticket_number": ticket.ticket_number,
                "channel": "email",
                "ai_analysis": ticket_data.get("ai_analysis", {}),
                "sentiment_analysis": sentiment,
                "routing_result": routing_result if 'routing_result' in locals() else None
            }

        except Exception as e:
            logger.error(f"Error creating ticket from email: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Error processing email ticket"
            )

    async def create_ticket_from_chatbot(
        self,
        db: Session,
        chat_data: Dict[str, Any],
        user: CurrentUser
    ) -> Dict[str, Any]:
        """Create ticket from chatbot interaction"""
        try:
            # Extract conversation context
            conversation = chat_data.get("conversation", [])
            intent = chat_data.get("intent", "")
            entities = chat_data.get("entities", {})
            
            # Build ticket from conversation
            ticket_data = await self._extract_ticket_from_chat(conversation, intent, entities)
            
            # AI categorization based on intent and entities
            if self.channel_configs["chatbot"]["auto_categorize"]:
                ai_analysis = await self.ai_categorization.categorize_ticket(
                    ticket_data["title"], ticket_data["description"]
                )
                
                # Override with chatbot intent if available
                if intent:
                    ticket_data["ticket_type"] = await self._map_intent_to_ticket_type(intent)
                elif ai_analysis.get("predicted_type"):
                    ticket_data["ticket_type"] = ai_analysis["predicted_type"]
                
                ticket_data["ai_analysis"] = ai_analysis

            # Sentiment analysis
            full_conversation = " ".join([msg.get("text", "") for msg in conversation])
            sentiment = await self.sentiment_analysis.analyze_sentiment(full_conversation)

            # Create ticket
            ticket = await self._create_ticket_with_metadata(
                db, ticket_data, user, "chatbot", sentiment, []
            )

            # Auto-route
            if self.channel_configs["chatbot"]["auto_route"]:
                routing_result = await self.smart_routing.find_best_agent(
                    db, ticket, user.organization_id
                )
                if routing_result:
                    ticket.assigned_to = routing_result["agent_id"]
                    ticket.auto_assigned = True
                    db.commit()

            return {
                "ticket_id": str(ticket.id),
                "ticket_number": ticket.ticket_number,
                "channel": "chatbot",
                "intent": intent,
                "entities": entities,
                "sentiment_analysis": sentiment,
                "routing_result": routing_result if 'routing_result' in locals() else None
            }

        except Exception as e:
            logger.error(f"Error creating ticket from chatbot: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Error processing chatbot ticket"
            )

    async def create_ticket_from_api(
        self,
        db: Session,
        api_data: Dict[str, Any],
        user: CurrentUser,
        source_system: str = "external_api"
    ) -> Dict[str, Any]:
        """Create ticket from external API"""
        try:
            # Validate API data
            required_fields = ["title", "description"]
            for field in required_fields:
                if field not in api_data:
                    raise HTTPException(
                        status_code=status.HTTP_400_BAD_REQUEST,
                        detail=f"Missing required field: {field}"
                    )

            ticket_data = {
                "title": api_data["title"],
                "description": api_data["description"],
                "ticket_type": api_data.get("ticket_type", "other"),
                "priority": api_data.get("priority", self.channel_configs["api"]["default_priority"]),
                "category": api_data.get("category"),
                "contact_method": "api",
                "external_id": api_data.get("external_id"),
                "source_system": source_system
            }

            # Optional AI categorization for API tickets
            if self.channel_configs["api"]["auto_categorize"]:
                ai_analysis = await self.ai_categorization.categorize_ticket(
                    ticket_data["title"], ticket_data["description"]
                )
                ticket_data["ai_analysis"] = ai_analysis

            # Sentiment analysis
            sentiment = await self.sentiment_analysis.analyze_sentiment(
                f"{ticket_data['title']} {ticket_data['description']}"
            )

            # Create ticket
            ticket = await self._create_ticket_with_metadata(
                db, ticket_data, user, "api", sentiment, api_data.get("attachments", [])
            )

            # Auto-route
            if self.channel_configs["api"]["auto_route"]:
                routing_result = await self.smart_routing.find_best_agent(
                    db, ticket, user.organization_id
                )
                if routing_result:
                    ticket.assigned_to = routing_result["agent_id"]
                    ticket.auto_assigned = True
                    db.commit()

            return {
                "ticket_id": str(ticket.id),
                "ticket_number": ticket.ticket_number,
                "channel": "api",
                "source_system": source_system,
                "external_id": api_data.get("external_id"),
                "sentiment_analysis": sentiment,
                "routing_result": routing_result if 'routing_result' in locals() else None
            }

        except Exception as e:
            logger.error(f"Error creating ticket from API: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Error processing API ticket"
            )

    async def create_ticket_from_phone(
        self,
        db: Session,
        phone_data: Dict[str, Any],
        organization_id: str
    ) -> Dict[str, Any]:
        """Create ticket from phone call"""
        try:
            # Extract phone call data
            caller_phone = phone_data.get("caller_phone", "")
            call_transcript = phone_data.get("transcript", "")
            call_duration = phone_data.get("duration", 0)
            agent_notes = phone_data.get("agent_notes", "")
            
            # Find user by phone number
            user = await self._find_user_by_phone(db, caller_phone, organization_id)
            if not user:
                # Create guest user for phone calls
                user = await self._create_guest_user(db, caller_phone, organization_id)

            # Extract ticket from call transcript and notes
            ticket_data = await self._extract_ticket_from_phone_call(
                call_transcript, agent_notes
            )

            # AI categorization
            if self.channel_configs["phone"]["auto_categorize"]:
                ai_analysis = await self.ai_categorization.categorize_ticket(
                    ticket_data["title"], ticket_data["description"]
                )
                
                if ai_analysis.get("predicted_type"):
                    ticket_data["ticket_type"] = ai_analysis["predicted_type"]
                if ai_analysis.get("predicted_priority"):
                    ticket_data["priority"] = ai_analysis["predicted_priority"]
                
                ticket_data["ai_analysis"] = ai_analysis

            # Sentiment analysis on transcript
            sentiment = await self.sentiment_analysis.analyze_sentiment(call_transcript)
            
            # Phone calls are typically urgent
            if ticket_data["priority"] == "medium":
                ticket_data["priority"] = "high"

            # Add phone-specific metadata
            ticket_data.update({
                "contact_method": "phone",
                "contact_details": caller_phone,
                "call_duration": call_duration,
                "call_transcript": call_transcript
            })

            # Create ticket
            ticket = await self._create_ticket_with_metadata(
                db, ticket_data, user, "phone", sentiment, []
            )

            # Auto-route
            if self.channel_configs["phone"]["auto_route"]:
                routing_result = await self.smart_routing.find_best_agent(
                    db, ticket, organization_id
                )
                if routing_result:
                    ticket.assigned_to = routing_result["agent_id"]
                    ticket.auto_assigned = True
                    db.commit()

            return {
                "ticket_id": str(ticket.id),
                "ticket_number": ticket.ticket_number,
                "channel": "phone",
                "caller_phone": caller_phone,
                "call_duration": call_duration,
                "sentiment_analysis": sentiment,
                "routing_result": routing_result if 'routing_result' in locals() else None
            }

        except Exception as e:
            logger.error(f"Error creating ticket from phone: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Error processing phone ticket"
            )

    async def get_channel_analytics(
        self,
        db: Session,
        organization_id: str,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None
    ) -> Dict[str, Any]:
        """Get multi-channel analytics"""
        try:
            if not start_date:
                start_date = datetime.utcnow() - timedelta(days=30)
            if not end_date:
                end_date = datetime.utcnow()

            # Channel distribution
            channel_stats = {}
            for channel in self.channel_configs.keys():
                count = db.query(Ticket).filter(
                    Ticket.organization_id == organization_id,
                    Ticket.contact_method == channel,
                    Ticket.created_at >= start_date,
                    Ticket.created_at <= end_date
                ).count()
                channel_stats[channel] = count

            # Response time by channel
            channel_response_times = {}
            for channel in self.channel_configs.keys():
                tickets = db.query(Ticket).filter(
                    Ticket.organization_id == organization_id,
                    Ticket.contact_method == channel,
                    Ticket.first_response_at.isnot(None),
                    Ticket.created_at >= start_date,
                    Ticket.created_at <= end_date
                ).all()
                
                if tickets:
                    avg_response = sum([
                        (ticket.first_response_at - ticket.created_at).total_seconds() / 3600
                        for ticket in tickets
                    ]) / len(tickets)
                    channel_response_times[channel] = round(avg_response, 2)
                else:
                    channel_response_times[channel] = 0

            return {
                "period": {
                    "start_date": start_date.isoformat(),
                    "end_date": end_date.isoformat()
                },
                "channel_distribution": channel_stats,
                "channel_response_times": channel_response_times,
                "total_tickets": sum(channel_stats.values()),
                "most_used_channel": max(channel_stats, key=channel_stats.get) if channel_stats else None
            }

        except Exception as e:
            logger.error(f"Error getting channel analytics: {e}")
            return {"error": str(e)}

    # Helper methods
    async def _find_or_create_user_from_email(
        self, db: Session, email_addr: str, organization_id: str
    ) -> Optional[Employee]:
        """Find or create user from email address"""
        try:
            # Try to find existing user
            user = db.query(Employee).filter(
                Employee.email == email_addr,
                Employee.organization_id == organization_id
            ).first()
            
            if user:
                return user
            
            # Create guest user if not found
            return await self._create_guest_user(db, email_addr, organization_id)

        except Exception as e:
            logger.error(f"Error finding/creating user from email: {e}")
            return None

    async def _create_guest_user(
        self, db: Session, contact_info: str, organization_id: str
    ) -> Employee:
        """Create a guest user for external contacts"""
        try:
            # This would create a guest user record
            # Implementation depends on your user management system
            pass

        except Exception as e:
            logger.error(f"Error creating guest user: {e}")
            raise

    async def _extract_ticket_from_email(self, subject: str, body: str) -> Dict[str, Any]:
        """Extract ticket information from email"""
        return {
            "title": subject or "Email Request",
            "description": body or "No description provided",
            "ticket_type": "other",
            "priority": "medium"
        }

    async def _extract_ticket_from_chat(
        self, conversation: List[Dict], intent: str, entities: Dict
    ) -> Dict[str, Any]:
        """Extract ticket information from chat conversation"""
        # Combine conversation messages
        description = "\n".join([
            f"{msg.get('sender', 'User')}: {msg.get('text', '')}"
            for msg in conversation
        ])
        
        # Generate title from intent or first message
        title = intent.replace("_", " ").title() if intent else "Chat Request"
        if conversation and conversation[0].get("text"):
            title = conversation[0]["text"][:50] + "..." if len(conversation[0]["text"]) > 50 else conversation[0]["text"]

        return {
            "title": title,
            "description": description,
            "ticket_type": "other",
            "priority": "high"  # Chat indicates urgency
        }

    async def _extract_ticket_from_phone_call(
        self, transcript: str, agent_notes: str
    ) -> Dict[str, Any]:
        """Extract ticket information from phone call"""
        description = f"Phone Call Transcript:\n{transcript}\n\nAgent Notes:\n{agent_notes}"
        
        # Extract title from first sentence of transcript
        title = "Phone Request"
        if transcript:
            sentences = transcript.split(".")
            if sentences:
                title = sentences[0][:50] + "..." if len(sentences[0]) > 50 else sentences[0]

        return {
            "title": title,
            "description": description,
            "ticket_type": "other",
            "priority": "high"  # Phone calls indicate urgency
        }

    async def _map_intent_to_ticket_type(self, intent: str) -> str:
        """Map chatbot intent to ticket type"""
        intent_mapping = {
            "password_reset": "it_support",
            "leave_request": "leave",
            "hr_question": "hr_query",
            "equipment_request": "equipment",
            "facility_issue": "facilities",
            "payroll_question": "payroll",
            "access_request": "access_request",
            "complaint": "complaint",
            "suggestion": "suggestion",
            "training_request": "training_request"
        }
        
        return intent_mapping.get(intent, "other")

    async def _create_ticket_with_metadata(
        self,
        db: Session,
        ticket_data: Dict[str, Any],
        user: CurrentUser,
        channel: str,
        sentiment: Dict[str, Any],
        attachments: List[str]
    ) -> Ticket:
        """Create ticket with channel and AI metadata"""
        try:
            # Generate ticket number
            ticket_count = db.query(Ticket).filter(
                Ticket.organization_id == user.organization_id
            ).count()
            ticket_number = f"TKT-{ticket_count + 1:06d}"

            # Create ticket
            ticket = Ticket(
                ticket_number=ticket_number,
                title=ticket_data["title"],
                description=ticket_data["description"],
                ticket_type=TicketType(ticket_data.get("ticket_type", "other")),
                priority=TicketPriority(ticket_data.get("priority", "medium")),
                category=ticket_data.get("category"),
                requester_id=user.user_id,
                organization_id=user.organization_id,
                status=TicketStatus.OPEN,
                contact_method=channel,
                contact_details=ticket_data.get("contact_details"),
                attachment_urls=attachments,
                # Store AI metadata
                ai_metadata={
                    "channel": channel,
                    "ai_analysis": ticket_data.get("ai_analysis", {}),
                    "sentiment_analysis": sentiment,
                    "auto_categorized": ticket_data.get("ai_analysis", {}).get("ai_processed", False)
                },
                created_by=user.user_id
            )

            db.add(ticket)
            db.commit()
            db.refresh(ticket)

            return ticket

        except Exception as e:
            db.rollback()
            logger.error(f"Error creating ticket with metadata: {e}")
            raise
