from core.databases.database import db
from core.models.basemodel import ModelBase

class PayrollDetailsModel(ModelBase):
    __tablename__ = "payroll_details"
    
    id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    processing_id = db.Column(db.<PERSON><PERSON>,db.<PERSON><PERSON>('payroll_processings.id'), nullable=False)
    employee_id = db.<PERSON>umn(db.Integer, db.ForeignKey('employees.id'), nullable=False)
    net_salary = db.Column(db.Double, nullable=False)
    approval_status = db.Column(db.Integer, nullable=False)
    gross_salary = db.Column(db.Double, nullable=False)
    tax = db.Column(db.Double, nullable=False)    
    cost_compony = db.Column(db.Double, nullable=False)
    total_earnings = db.Column(db.Double, nullable=False)
    total_deduction = db.Column(db.Double, nullable=False)
    payment_status = db.Column(db.Integer, nullable=False)
    pension = db.Column(db.Integer, nullable=False)
    nhf = db.Column(db.Integer, nullable=False)

      

    
      