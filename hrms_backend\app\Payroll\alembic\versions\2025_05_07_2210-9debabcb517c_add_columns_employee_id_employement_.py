"""add columns employee_id, employement_type and template_id topayschedle table

Revision ID: 9debabcb517c
Revises: 3f8ab3b6dbf3
Create Date: 2025-05-07 22:10:00.293541

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '9debabcb517c'
down_revision: Union[str, None] = '3f8ab3b6dbf3'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade():
    op.add_column('pay_schedules', sa.Column('organisation_id', sa.Integer(), nullable=True))
    op.add_column('pay_schedules', sa.Column('salary_template_id', sa.Integer(), nullable=True))
    op.add_column('pay_schedules', sa.Column('employment_type', sa.String(length=100), nullable=True))

    op.create_foreign_key(
        'fk_pay_schedules_organisation_id_organisations',
        'pay_schedules',
        'organisations',
        ['organisation_id'],
        ['id'],
        ondelete='SET NULL'
    )

    op.create_foreign_key(
        'fk_pay_schedules_salary_template_id_salary_templates',
        'pay_schedules',
        'salary_templates',
        ['salary_template_id'],
        ['id'],
        ondelete='SET NULL'
    )

def downgrade():
    op.drop_constraint('fk_pay_schedules_organisation_id_organisations', 'pay_schedules', type_='foreignkey')
    op.drop_constraint('fk_pay_schedules_salary_template_id_salary_templates', 'pay_schedules', type_='foreignkey')

    op.drop_column('pay_schedules', 'organisation_id')
    op.drop_column('pay_schedules', 'salary_template_id')
    op.drop_column('pay_schedules', 'employment_type')