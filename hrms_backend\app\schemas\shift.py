"""
Shift Pydantic schemas for request/response validation
"""

from pydantic import BaseModel, Field, validator
from typing import Optional, List
from datetime import datetime, date, time
from uuid import UUID
from enum import Enum

class ShiftType(str, Enum):
    MORNING = "morning"
    AFTERNOON = "afternoon"
    EVENING = "evening"
    NIGHT = "night"
    FLEXIBLE = "flexible"

class ShiftStatus(str, Enum):
    ACTIVE = "active"
    INACTIVE = "inactive"
    ARCHIVED = "archived"

class SwapStatus(str, Enum):
    PENDING = "pending"
    APPROVED = "approved"
    REJECTED = "rejected"
    CANCELLED = "cancelled"

# Base schemas
class ShiftBase(BaseModel):
    name: str = Field(..., min_length=1, max_length=100)
    description: Optional[str] = Field(None, max_length=500)
    start_time: time
    end_time: time
    shift_type: ShiftType
    is_overnight: bool = False
    break_duration: int = Field(0, ge=0, le=480)  # Break duration in minutes
    max_employees: Optional[int] = Field(None, ge=1)

class ShiftAssignmentBase(BaseModel):
    shift_id: UUID
    employee_id: UUID
    assigned_date: date
    is_mandatory: bool = True

class ShiftSwapBase(BaseModel):
    requesting_employee_id: UUID
    target_employee_id: UUID
    original_shift_assignment_id: UUID
    target_shift_assignment_id: UUID
    reason: str = Field(..., min_length=1, max_length=500)

# Create schemas
class ShiftCreate(ShiftBase):
    department_id: Optional[UUID] = None

class ShiftAssignmentCreate(ShiftAssignmentBase):
    pass

class ShiftSwapRequest(ShiftSwapBase):
    pass

# Update schemas
class ShiftUpdate(BaseModel):
    name: Optional[str] = Field(None, min_length=1, max_length=100)
    description: Optional[str] = Field(None, max_length=500)
    start_time: Optional[time] = None
    end_time: Optional[time] = None
    shift_type: Optional[ShiftType] = None
    is_overnight: Optional[bool] = None
    break_duration: Optional[int] = Field(None, ge=0, le=480)
    max_employees: Optional[int] = Field(None, ge=1)
    status: Optional[ShiftStatus] = None

class ShiftAssignmentUpdate(BaseModel):
    assigned_date: Optional[date] = None
    is_mandatory: Optional[bool] = None

# Response schemas
class ShiftResponse(ShiftBase):
    id: UUID
    department_id: Optional[UUID] = None
    status: ShiftStatus
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True

class ShiftAssignmentResponse(ShiftAssignmentBase):
    id: UUID
    shift: ShiftResponse
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True

class ShiftSwapResponse(ShiftSwapBase):
    id: UUID
    status: SwapStatus
    approved_by: Optional[UUID] = None
    approved_at: Optional[datetime] = None
    rejected_by: Optional[UUID] = None
    rejected_at: Optional[datetime] = None
    rejection_reason: Optional[str] = None
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True

class ShiftListResponse(BaseModel):
    shifts: List[ShiftResponse]
    total: int
    page: int
    per_page: int
    pages: int

class ShiftScheduleResponse(BaseModel):
    employee_id: UUID
    assignments: List[ShiftAssignmentResponse]
    total_hours: float
    week_start: date
    week_end: date
