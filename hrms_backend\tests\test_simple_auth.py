#!/usr/bin/env python3
"""
Simple test for authentication
"""

import requests
import json

def test_admin_login():
    """Test admin login specifically"""
    url = "http://localhost:8085/api/auth/login"
    
    payload = {
        "email": "ADMIN001",
        "password": "password123"
    }
    
    headers = {
        "Content-Type": "application/json"
    }
    
    try:
        print("Testing admin login...")
        response = requests.post(url, json=payload, headers=headers)
        
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ Admin login successful!")
            print(f"Response: {json.dumps(response.json(), indent=2)}")
        else:
            print("❌ Admin login failed!")
            print(f"Error: {response.text}")
            
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    test_admin_login()
