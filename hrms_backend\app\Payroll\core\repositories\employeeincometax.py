from core.models.employee_income_taxes import EmployeeIncTaxModel
from core.databases.database import db

class EmployeeIncTaxRepository:
    def __init__(self) -> None:
        pass

    @classmethod
    def createEmployeeIncTaxes(self, employee_id,regulation_id , taxable_income, tax_amount, organisation_id):
        employeeIncTaxes = EmployeeIncTaxModel(
            employee_id = employee_id
            regulation_id = regulation_id
            taxable_income = taxable_income
            tax_amount = tax_amount
            organisation_id = organisation_id      
        )
        db.session.add(employeeIncTaxes)
        db.session.commit()
        return employeeIncTaxes

    @classmethod
    def getEmployeeIncTaxes(self, id):
        return EmployeeIncTaxModel.query.filter(EmployeeIncTaxModel.id == id).first()
    
    @classmethod
    def getEmployeeIncTaxesByKeys(self, kwargs):
        return EmployeeIncTaxModel.query.filter_by(**kwargs).all()
       

    @classmethod
    def updateEmployeeIncTaxes(self, id, **kwargs):
        employeeIncTaxes = EmployeeIncTaxModel.query.filter_by(id=id).first()
        if employeeIncTaxes:
            for key, value in kwargs.items():
                setattr(employeeIncTaxes, key, value)
            db.session.commit()
            return employeeIncTaxes
        else:
            return None

    @classmethod
    def deleteEmployeeIncTaxes(self, id):
        return EmployeeIncTaxModel.query.filter(EmployeeIncTaxModel.id == id).delete()
        