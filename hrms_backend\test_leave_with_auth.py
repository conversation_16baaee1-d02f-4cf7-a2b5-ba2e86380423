#!/usr/bin/env python3
"""
Test leave policies with proper authentication
"""

import requests
import json

def test_leave_policies_with_auth():
    """Test leave policies with proper authentication"""
    
    base_url = "http://localhost:8085"
    
    try:
        # First, try to login to get a valid token
        login_data = {
            "username": "<EMAIL>",
            "password": "admin123"
        }
        
        print("🔐 Attempting to login...")
        login_response = requests.post(f"{base_url}/api/auth/login", json=login_data)
        
        if login_response.status_code == 200:
            token_data = login_response.json()
            token = token_data.get("access_token")
            print(f"✅ Login successful! Got token: {token[:50]}...")
            
            # Now test the leave policies endpoint
            headers = {
                "Authorization": f"Bearer {token}",
                "Content-Type": "application/json"
            }
            
            print("\n📋 Testing GET /api/leave/policies...")
            response = requests.get(f"{base_url}/api/leave/policies", headers=headers)
            
            print(f"Status Code: {response.status_code}")
            
            if response.status_code == 200:
                policies = response.json()
                print(f"✅ Found {len(policies)} leave policies:")
                
                for policy in policies:
                    print(f"  - {policy['name']} ({policy['leave_type']}) - {policy['annual_entitlement']} days/year")
                    
                print(f"\n🎉 All leave types are now available in the system!")
                print("Available leave types:")
                leave_types = list(set([policy['leave_type'] for policy in policies]))
                for leave_type in sorted(leave_types):
                    print(f"  ✓ {leave_type}")
                    
            else:
                print(f"❌ Error: {response.status_code}")
                print(f"Response: {response.text}")
                
        else:
            print(f"❌ Login failed: {login_response.status_code}")
            print(f"Response: {login_response.text}")
            
            # Try with a different approach - test without auth first
            print("\n🔍 Testing without authentication...")
            response = requests.get(f"{base_url}/api/leave/")
            print(f"Leave overview status: {response.status_code}")
            if response.status_code == 200:
                print("✅ Leave API is accessible")
            
    except Exception as e:
        print(f"❌ Error testing API: {e}")

if __name__ == "__main__":
    test_leave_policies_with_auth()
