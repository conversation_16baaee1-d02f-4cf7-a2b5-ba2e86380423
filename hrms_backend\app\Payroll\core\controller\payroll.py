from dotenv import load_dotenv
from flask import url_for, jsonify
from flask.views import MethodView
from core.services.paystack_webhook_service import PaystackWebhookService
from flask_smorest import Blueprint, abort
from core.middleware import roles_required
from core.utils.helper import get_nigeria_time_to_string
from schemas import PayrollProcesSchema, PayrollHistorySchema
from sqlalchemy.orm.exc import NoResultFound
from sqlalchemy.exc import SQLAlchemyError, IntegrityError
from core.services.payroll_history import PayrollHistoryService
from core.services.paystack_service import PaystackService
import core.utils.response_message as RESPONSEMESSAGE
from core.utils.responseBuilder import ResponseBuilder
from schemas import TransactionHistorySchema
from schemas import PayrollPaymentSchema
from core.services.transaction_history import TransactionHistoryService
from core.services.approval_settings import ApprovalSettingsService
from core.services.component_processor import ComponentProcessor
from schemas import RemarkSchema
from core.services.remarks import RemarkService

blueprint = Blueprint("Payroll", __name__, description="Operations for Payroll")
        
@blueprint.route("/payroll_process")
class PayrollProcess(MethodView):    
    @roles_required(['admin'])
    @blueprint.arguments(PayrollProcesSchema)
    @blueprint.response(200, PayrollProcesSchema)
    def post(self, data):
        payroll_ids = data.get("payroll_ids", [])
        if not payroll_ids:
            abort(400, message="No payroll records provided")
            
        # print("calling pay schedle check")
        ApprovalSettingsService().validate_payroll_approval_requirements(payroll_ids)
        # print("After calling pay schedle check")
        
        employees_data = self.process_payroll_records(payroll_ids, data.get("payment_gateway_id"))
        return ResponseBuilder(data=employees_data, status_code=200).build()
    
    def process_payroll_records(self, payroll_ids, payment_gateway_id):
        service = PayrollHistoryService()
        process_response, verified_payroll_ids = service.process_payroll(payroll_ids)
        employees_data = {"processed_payroll": f"{process_response} records updated successfully"}
        
        success_transactions, failed_transactions = [], []
        for employee_id in verified_payroll_ids:
            employee_object = self.get_employee_payroll_data(employee_id)
            if not employee_object:
                continue
            
            if payment_gateway_id:
                self.handle_payroll_payment(
                    employee_object, payment_gateway_id, success_transactions, failed_transactions
                )
        
        employees_data["disburse_transaction"] = success_transactions
        employees_data["failed_transactions"] = failed_transactions
        return employees_data
    
    def get_employee_payroll_data(self, employee_id):
        employee_schema = PayrollHistoryService().get_payroll_history_by_id(employee_id)
        return PayrollHistorySchema().dump(employee_schema)
    
    def handle_payroll_payment(self, employee_object, payment_gateway_id, success_transactions, failed_transactions):
        payroll_id = employee_object.get("id")
        try:
            #TODO check if a trasaction is successful before proceeding with the payroll payment
            payroll_transaction = self.process_payment(employee_object, payment_gateway_id, payroll_id)
            transaction_created = TransactionHistoryService().createTransaction(TransactionHistorySchema().dump(payroll_transaction))
            if not transaction_created:
                raise Exception("Error creating transaction.")

            PayrollHistoryService().payroll_history_update(
                payroll_id,
                {
                    "netpay": payroll_transaction["amount_paid"],
                    "message": payroll_transaction["transaction_message"],
                    "payment_status": payroll_transaction["paystack_status"],
                    "transaction_date": payroll_transaction["createdAt"],
                    "reference_code": payroll_transaction["reference_code"]
                }
            )
            success_transactions.append(payroll_transaction)
        except Exception as e:
            self.handle_failed_transaction(employee_object, payroll_id, failed_transactions, str(e))
    
    def process_payment(self, employee_object, payment_gateway_id, payroll_id):
        payment = PaystackService(
            payroll_id,
            net_pay=int(float(employee_object.get("netpay")) * 100),
            prorated_net=employee_object.get("prorated_net"),
            prorated_status=employee_object.get("is_prorated"),
            account_number=employee_object.get("employee", {}).get("bank_account"),
            sort_code=employee_object.get("employee", {}).get("sort_code"),
        )
        payroll_response = payment.disburse_payment()
        transfer_res_data = payroll_response.get('transfer_res', ())[0]
        
        return {
            "payroll_history_id": employee_object.get("id"),
            "employee_id": employee_object.get("employee", {}).get("id"),
            "amount_paid": transfer_res_data.get('data', {}).get('amount'),
            "currency": transfer_res_data.get('data', {}).get('currency'),
            "failures": transfer_res_data.get('data', {}).get('failures'),
            "transaction_id": transfer_res_data.get('data', {}).get('id'),
            "integration": transfer_res_data.get('data', {}).get('integration'),
            "reason": transfer_res_data.get('data', {}).get('reason'),
            "recipient_code": transfer_res_data.get('data', {}).get('recipient'),
            "reference_code": transfer_res_data.get('data', {}).get('reference'),
            "request": transfer_res_data.get('data', {}).get('request'),
            "transaction_process_status": transfer_res_data.get('data', {}).get('status'),
            "transfer_code": transfer_res_data.get('data', {}).get('transfer_code'),
            "transaction_message": payroll_response.get('message'),
            "paystack_status": payroll_response.get('status'),
            "verified_bank_name": payroll_response.get('verified_bank_name'),
            "verified_account_name": payroll_response.get('verified_account_name'),
            "verified_account_number": payroll_response.get('verified_account_number'),
            "verified_bank_sort_code": payroll_response.get('verified_bank_sort_code'),
            "transferred_at": transfer_res_data.get('data', {}).get('transferred_at'),
            "createdAt": PaystackService(payroll_id).format_timestamp(transfer_res_data.get('data', {}).get('createdAt')),
            "updatedAt": PaystackService(payroll_id).format_timestamp(transfer_res_data.get('data', {}).get('updatedAt')),
        }
    
    def handle_failed_transaction(self, employee_object, payroll_id, failed_transactions, error_message):
        PayrollHistoryService().payroll_history_update(
            payroll_id,
            {
                "message": error_message,
                "payment_status": "False",
                "transaction_date": get_nigeria_time_to_string()
            }
        )
        failed_transactions.append({
            "employee_id": employee_object.get("employee", {}).get("id"),
            "account_name": f"{employee_object.get('employee', {}).get('first_name', '')} {employee_object.get('employee', {}).get('last_name', '')}",
            "status": "failed",
            "error_message": error_message
        })


@blueprint.route("/paystack-webhook", methods=["POST"])
def paystack_webhook():
    """Webhook to handle Paystack transfer events."""
    return PaystackWebhookService.process_webhook()


@blueprint.route("/payroll-list")
class PayrollList(MethodView):
    @roles_required(['admin'])
    @blueprint.response(200, PayrollHistorySchema(many=True))
    def get(self):
        payroll_service = PayrollHistoryService()
        remark_service = RemarkService()

        # Get PayrollHistoryModel instances filtered by latest pay schedule
        filtered_models = payroll_service.getPayrollsByLatestSchedule()

        if not filtered_models:
            return ResponseBuilder(data=[], status_code=200, total=0).build()

        # Serialize the models so filtered_histories is a list of dicts
        payroll_history_schema = PayrollHistorySchema(many=True)
        filtered_histories = payroll_history_schema.dump(filtered_models)

        res = []
        payroll_history = []
        for item in filtered_histories:
            prorated_gross = item["prorated_gross"]
            prorated_net = item["prorated_net"]
            prorated_status = item["is_prorated"]
            prorated_monthly_tax = item["prorated_monthly_tax"]
            prorated_annual_tax = item["prorated_annual_tax"]
            processed_status = item["is_processed"]
            processed_time = item["is_processed_created"]
            arrears = item["salary_arrears"]
            overtime = item["overtime_amount"]
            gross_pay = item["gross_pay"]            
            
            component_processor = ComponentProcessor(
                item["employee"]["id"],
                item["employee"]["gross_pay"],
                prorated_gross,
                prorated_status,
                salary_benefit=item["employee"]["employee_benefits"],
                salary_component=item["employee"]["employee_components"]
            )
            res = component_processor.generate_salary_response()
            res["employee"] = item.get("employee")
            res["created_at"] = item.get("created_at")
            res["updated_at"] = item.get("updated_at")
            res["payroll_id"] = item.get("id")
            res["prorated_gross"] = prorated_gross
            res["prorated_status"] = prorated_status 
            res["prorated_net"] = float(prorated_net or 0) + float(arrears or 0) + float(overtime or 0)
            res["prorated_monthly_tax"] = prorated_monthly_tax 
            res["prorated_annual_tax"] = prorated_annual_tax 
            res["is_processed"] = processed_status 
            res["salary_arrears"] = arrears
            res["overtime_amount"] = overtime
            res["gross_pay"] = gross_pay
            res["netpay"] = item["netpay"] 
            res["monthly_tax"] = item["monthly_tax"] 
            res["annual_tax"] = item["annual_tax"] 
            res["total_taxable_monthly_sum"] = item["total_taxable_monthly_sum"] 
            res["total_taxable_annual_sum"] = item['total_taxable_annual_sum']
            res["total_non_taxable_monthly_sum"] = item['total_non_taxable_monthly_sum']
            res["total_non_taxable_annual_sum"] = item['total_non_taxable_annual_sum']
            res["total_statutory_monthly_sum"] = item['total_statutory_monthly_sum']
            res["total_statutory_annual_sum"] = item['total_statutory_annual_sum']
            res["total_other_deductions_monthly_sum"] = item['total_other_deductions_monthly_sum']
            res["total_other_deductions_annual_sum"] = item['total_other_deductions_annual_sum']
            res["processed_time"] = processed_time
            res["currency"] = item.get("employee").get("currency")
            payroll_history.append(res)
            
            # Get remarks for this payroll history
            payroll_history_id = item.get("id")
            remark_obj = remark_service.getRemarksByPayrollHistoryId(payroll_history_id) if payroll_history_id else None
            # Extract only the "remark" field
            remark_text = remark_obj[0].remark if remark_obj else None  # Get the first remark if available
            res["remark"] = remark_text  # Store directly as a string
   
        return ResponseBuilder(
            data=payroll_history,
            status_code=200,
            total=len(payroll_history)
        ).build()
