from pydantic import BaseModel, EmailStr, Field
from typing import Optional, List, Dict, Any
from datetime import datetime
from uuid import UUID


class TwoFactorSetupRequest(BaseModel):
    """Request to setup 2FA"""
    password: str = Field(..., description="Current password for verification")


class TwoFactorSetupResponse(BaseModel):
    """Response for 2FA setup"""
    secret: str = Field(..., description="TOTP secret key")
    qr_code: str = Field(..., description="QR code as base64 image")
    backup_codes: Optional[List[str]] = Field(None, description="Backup codes (only shown once)")


class TwoFactorEnableRequest(BaseModel):
    """Request to enable 2FA"""
    totp_token: str = Field(..., description="6-digit TOTP token", min_length=6, max_length=6)


class TwoFactorEnableResponse(BaseModel):
    """Response for enabling 2FA"""
    enabled: bool = Field(..., description="Whether 2FA was successfully enabled")
    backup_codes: List[str] = Field(..., description="Backup codes (save these securely)")


class TwoFactorDisableRequest(BaseModel):
    """Request to disable 2FA"""
    password: str = Field(..., description="Current password for verification")


class TwoFactorVerifyRequest(BaseModel):
    """Request to verify 2FA token"""
    token: str = Field(..., description="6-digit token (TOTP or backup code)", min_length=6, max_length=8)
    token_type: str = Field(default="totp", description="Type of token: 'totp' or 'backup'")


class LoginRequest(BaseModel):
    """Enhanced login request with 2FA support"""
    email: str = Field(..., description="Email or employee ID")
    password: str = Field(..., description="Password")
    device_info: Optional[Dict[str, Any]] = Field(None, description="Device information")
    remember_me: bool = Field(default=False, description="Remember this device")


class LoginResponse(BaseModel):
    """Enhanced login response"""
    access_token: str = Field(..., description="JWT access token")
    refresh_token: Optional[str] = Field(None, description="JWT refresh token")
    token_type: str = Field(default="bearer", description="Token type")
    expires_in: int = Field(..., description="Token expiration time in seconds")
    requires_2fa: bool = Field(default=False, description="Whether 2FA verification is required")
    user: Optional["UserResponse"] = Field(None, description="User information")


class TwoFactorLoginRequest(BaseModel):
    """2FA login verification request"""
    email: str = Field(..., description="Email or employee ID")
    password: str = Field(..., description="Password")
    two_fa_token: str = Field(..., description="2FA token")
    token_type: str = Field(default="totp", description="Type of 2FA token")
    device_info: Optional[Dict[str, Any]] = Field(None, description="Device information")
    remember_me: bool = Field(default=False, description="Remember this device")


class RefreshTokenRequest(BaseModel):
    """Refresh token request"""
    refresh_token: str = Field(..., description="Refresh token")


class RefreshTokenResponse(BaseModel):
    """Refresh token response"""
    access_token: str = Field(..., description="New JWT access token")
    refresh_token: str = Field(..., description="New refresh token")
    token_type: str = Field(default="bearer", description="Token type")
    expires_in: int = Field(..., description="Token expiration time in seconds")


class LogoutRequest(BaseModel):
    """Logout request"""
    refresh_token: Optional[str] = Field(None, description="Refresh token to revoke")
    logout_all_devices: bool = Field(default=False, description="Logout from all devices")


class PasswordChangeRequest(BaseModel):
    """Password change request"""
    current_password: str = Field(..., description="Current password")
    new_password: str = Field(..., min_length=8, description="New password (minimum 8 characters)")
    confirm_password: str = Field(..., description="Confirm new password")


class PasswordResetRequest(BaseModel):
    """Password reset request"""
    email: EmailStr = Field(..., description="Email address")


class PasswordResetConfirmRequest(BaseModel):
    """Password reset confirmation request"""
    token: str = Field(..., description="Password reset token")
    new_password: str = Field(..., min_length=8, description="New password")
    confirm_password: str = Field(..., description="Confirm new password")


class EmailVerificationRequest(BaseModel):
    """Email verification request"""
    email: EmailStr = Field(..., description="Email address to verify")


class EmailVerificationConfirmRequest(BaseModel):
    """Email verification confirmation request"""
    token: str = Field(..., description="Email verification token")


class PhoneVerificationRequest(BaseModel):
    """Phone verification request"""
    phone: str = Field(..., description="Phone number to verify")


class PhoneVerificationConfirmRequest(BaseModel):
    """Phone verification confirmation request"""
    phone: str = Field(..., description="Phone number")
    code: str = Field(..., min_length=6, max_length=6, description="6-digit verification code")


class UserResponse(BaseModel):
    """User response model"""
    id: UUID = Field(..., description="User ID")
    username: str = Field(..., description="Username")
    email: str = Field(..., description="Email address")
    first_name: str = Field(..., description="First name")
    last_name: str = Field(..., description="Last name")
    phone: Optional[str] = Field(None, description="Phone number")
    is_active: bool = Field(..., description="Whether user is active")
    is_verified: bool = Field(..., description="Whether user is verified")
    email_verified: bool = Field(..., description="Whether email is verified")
    phone_verified: bool = Field(..., description="Whether phone is verified")
    two_fa_enabled: bool = Field(..., description="Whether 2FA is enabled")
    role: "RoleResponse" = Field(..., description="User role")
    last_login: Optional[datetime] = Field(None, description="Last login timestamp")
    created_at: datetime = Field(..., description="Account creation timestamp")

    class Config:
        from_attributes = True


class RoleResponse(BaseModel):
    """Role response model"""
    id: UUID = Field(..., description="Role ID")
    name: str = Field(..., description="Role name")
    display_name: str = Field(..., description="Role display name")
    description: Optional[str] = Field(None, description="Role description")
    permissions: List[str] = Field(..., description="Role permissions")

    class Config:
        from_attributes = True


class SessionResponse(BaseModel):
    """Active session response"""
    id: UUID = Field(..., description="Session ID")
    device_info: Optional[Dict[str, Any]] = Field(None, description="Device information")
    ip_address: Optional[str] = Field(None, description="IP address")
    created_at: datetime = Field(..., description="Session creation time")
    last_accessed: Optional[datetime] = Field(None, description="Last access time")
    expires_at: datetime = Field(..., description="Session expiration time")
    is_current: bool = Field(..., description="Whether this is the current session")

    class Config:
        from_attributes = True


class SecuritySettingsResponse(BaseModel):
    """Security settings response"""
    two_fa_enabled: bool = Field(..., description="Whether 2FA is enabled")
    email_verified: bool = Field(..., description="Whether email is verified")
    phone_verified: bool = Field(..., description="Whether phone is verified")
    password_changed_at: Optional[datetime] = Field(None, description="Last password change")
    active_sessions: int = Field(..., description="Number of active sessions")
    login_attempts: int = Field(..., description="Recent failed login attempts")
    account_locked: bool = Field(..., description="Whether account is locked")

    class Config:
        from_attributes = True


class AuditLogResponse(BaseModel):
    """Audit log response"""
    id: UUID = Field(..., description="Audit log ID")
    action: str = Field(..., description="Action performed")
    resource_type: str = Field(..., description="Type of resource affected")
    resource_id: Optional[str] = Field(None, description="ID of affected resource")
    ip_address: Optional[str] = Field(None, description="IP address")
    user_agent: Optional[str] = Field(None, description="User agent")
    status: str = Field(..., description="Action status")
    created_at: datetime = Field(..., description="Timestamp")

    class Config:
        from_attributes = True


# Update forward references
UserResponse.model_rebuild()
RoleResponse.model_rebuild()
