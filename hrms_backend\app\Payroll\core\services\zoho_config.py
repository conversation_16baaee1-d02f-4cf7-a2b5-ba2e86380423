from core.repositories.zoho_config import ZohoConfigRepository

class ZohoConfigService:
    def __init__(self) -> None:
        self.repository = ZohoConfigRepository()

    def createConfig(self,Kwargs):
        return self.repository.create_config(**Kwargs)

    def getConfig(self):
        return self.repository.get_config()

    def updateConfig(self, id, Kwargs):
        return self.repository.update_config(id, **Kwargs)

    def deleteConfig(self):
        return self.repository.delete_config()



