#!/usr/bin/env python3
"""
Test with fresh login after organization update
"""

import requests
import json
import time

def test_fresh_login():
    """Test with fresh login"""
    
    base_url = "http://localhost:8085"
    
    try:
        # Fresh login
        login_data = {
            "email": "<EMAIL>", 
            "password": "admin123"
        }
        
        print("🔐 Fresh login attempt...")
        response = requests.post(f"{base_url}/api/auth/login", json=login_data)
        print(f"Login status: {response.status_code}")
        
        if response.status_code == 200:
            token_data = response.json()
            token = token_data.get("access_token")
            print(f"✅ Got fresh token: {token[:50]}...")
            
            # Test leave policies with fresh token
            headers = {"Authorization": f"Bearer {token}"}
            response = requests.get(f"{base_url}/api/leave/policies", headers=headers)
            print(f"\nLeave policies status: {response.status_code}")
            
            if response.status_code == 200:
                policies = response.json()
                print(f"✅ Got {len(policies)} policies")
                
                if len(policies) > 0:
                    print("Available leave types:")
                    for policy in policies:
                        print(f"  - {policy.get('name', 'Unknown')} ({policy.get('leave_type', 'Unknown')})")
                        
                    print("\n🎉 SUCCESS! Leave policies are now accessible!")
                    print("The frontend should now be able to load leave types.")
                else:
                    print("❌ Still getting 0 policies. Let me debug further...")
                    
                    # Debug the token payload
                    import base64
                    try:
                        # Decode JWT payload (without verification)
                        parts = token.split('.')
                        payload = parts[1]
                        # Add padding if needed
                        payload += '=' * (4 - len(payload) % 4)
                        decoded = base64.b64decode(payload)
                        payload_data = json.loads(decoded)
                        print(f"Token organization_id: {payload_data.get('organization_id', 'Not found')}")
                    except Exception as e:
                        print(f"Could not decode token: {e}")
                        
            else:
                print(f"❌ Error: {response.text}")
        else:
            print(f"❌ Login failed: {response.text}")
        
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    test_fresh_login()
