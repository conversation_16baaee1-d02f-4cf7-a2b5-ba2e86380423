<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Payslip Details</title>
    <!-- <link rel="stylesheet" href="./PaySlip.css"> -->
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Arial, sans-serif;
        }
        
        body {
            background-color: #fff;
            padding: 20px;
            color: #333;
        }
        
        .container {
            max-width: 1000px;
            min-width: 320px;
            margin: 0 auto;
            padding: 20px;
            padding-left: 1em;
            padding-right: 1em;
        }
        
        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 40px;
        }
        
        .back-title {
            display: flex;
            align-items: center;
            gap: 10px;
            font-size: 24px;
            color: #333;
            text-decoration: none;
        }
        
        .send-payslip {
            padding: 8px 16px;
            border: 1px solid #BB3C2D;
            border-radius: 4px;
            color: #BB3C2D;
            background: none;
            cursor: pointer;
        }
        
        .company-details {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .company-name {
            font-size: 24px;
            margin-bottom: 10px;
        }
        
        .company-address, .company-email {
            color: #666;
            margin-bottom: 5px;
        }
        
        .payment-period {
            text-align: center;
            color: #666;
            margin-bottom: 30px;
        }
        
        .employee-details {
            display: flex;
            flex-wrap: wrap;
            justify-content: space-between;
            margin-bottom: 30px;
            gap: 20px;
        }
        
        .employee-left {
            display: flex;
            align-items: center;
            gap: 20px;
        }
        
        .employee-photo {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            object-fit: cover;
        }
        
        .employee-info {
            color: #666;
        }
        
        .employee-name {
            font-weight: bold;
            color: #333;
        }
        
        .department-info {
            text-align: center;
        }
        
        .bank-info {
            text-align: right;
        }
        
        .earnings-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .earnings-section {
            border: 1px solid #ddd;
            border-radius: 4px;
            overflow: hidden;
        }
        
        .section-header {
            background-color: #BB3C2D;
            color: white;
            padding: 10px;
            display: flex;
            justify-content: space-between;
        }
        
        .section-content {
            padding: 10px;
        }
        
        .earning-row {
            display: flex;
            justify-content: space-between;
            padding: 8px 10px;
        }
        
        .total-row {
            background-color: #F5F5F5;
            font-weight: bold;
            padding: 8px 10px;
            display: flex;
            justify-content: space-between;
        }
        
        /* @media screen and (max-width: 768px) {
            .container {
                padding: 10px;
            }
            .header {
                flex-direction: column;
                align-items: flex-start;
                gap: 10px;
            }
            .employee-details {
                flex-direction: column;
                align-items: flex-start;
                gap: 20px;
            }
            .department-info, .bank-info {
                text-align: left;
            }
            .earnings-grid {
                grid-template-columns: 1fr;
            }
        }
        
        @media screen and (max-width: 480px) {
            .employee-left {
                flex-direction: column;
                align-items: flex-start;
            }
            .employee-photo {
                margin-bottom: 10px;
            }
            .section-header, .earning-row, .total-row {
                font-size: 14px;
            }
        } */


    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <a href="#" class="back-title">
                Payslip details
            </a>
            <!-- <button class="send-payslip">Send payslip</button> -->
        </div>

         <!-- Company Details -->
        <div class="company-details">
            <h1 class="company-name">{{employee_payslip["employee"]["organisation"]["organisation_name"]}}</h1>
            <div class="company-address">{{employee_payslip["employee"]["organisation"]["address"]}}</div>
            <div class="company-email">{{employee_payslip["employee"]["organisation"]["email"]}}</div>
        </div>

        <div class="payment-period">
            Payment slip for the month of December 2024
        </div>

        <!-- Employee Details -->

        <div class="row">
            <div class=" row  employee-details">
                <div class="col-4">
                    <div class="employee-left">
                
                        <div class="employee-info">
                            <div class="employee-name">Employee Name:{{ employee_payslip["employee"]["first_name"] }} {{ employee_payslip["employee"]["last_name"] }}</div>
                            <div>Employee ID: {{ employee_payslip["employee"]["id"] }}</div>
                        </div>
                    </div>    
                </div>
                <div class="col-4">
                    <div class="department-info">
                        <div>Department: {{ employee_payslip["employee"]["department"]["name"] }}</div>
                        <div>Designation: {{ employee_payslip["employee"]["designation"]["name"] }}</div>
                    </div>
                </div>
                <div class="col-4">
                    <div class="bank-info">
                        <div>Email: {{ employee_payslip["email"] }}</div>
                        <div>Bank Name: {{ employee_payslip["bank_name"] }}</div>
                    </div>
                </div>
               
              
               
           </div>
        </div>
       

          <!-- Payslip Tables -->
        <div class="earnings-grid">
             <!-- Earnings Table -->
            <div class="earnings-section">
                <div class="section-header">
                    <span>Earnings</span>
                    <span>Amount</span>
                </div>
                <div class="section-content">
                    <div class="earning-row">
                        <span>Benefits</span>
                        <span>16.28</span>
                    </div>
                    <div class="earning-row">
                        <span>Salary</span>
                        <span>400.7</span>
                    </div>
                    <div class="total-row">
                        <span>Total Taxable</span>
                        <span>156.98</span>
                    </div>
                </div>
            </div>
           <!-- Non Taxable earnings Table -->
            <div class="earnings-section">
                <div class="section-header">
                    <span>Non Taxable earnings</span>
                    <span>Amount</span>
                </div>
                <div class="section-content">
                    
                </div>
            </div>
            <!-- Statutory Deduction Table -->
            <div class="earnings-section">
                <div class="section-header">
                    <span>Statutory Deduction</span>
                    <span>Amount</span>
                </div>
                <div class="section-content">
                    <div class="earning-row">
                        <span>Monthly Tax</span>
                        <span>10.57</span>
                    </div>
                    <div class="total-row">
                        <span>Total Deduction</span>
                        <span>20.0</span>
                    </div>
                </div>
            </div>
             <!-- Other deduction Table -->
            <div class="earnings-section">
                <div class="section-header">
                    <span>Other deduction</span>
                    <span>Amount</span>
                </div>
                <div class="section-content">
                    
                </div>
            </div>
        </div>
    </div>

    
</body>
</html>

