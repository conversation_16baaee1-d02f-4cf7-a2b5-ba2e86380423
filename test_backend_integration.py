#!/usr/bin/env python3
"""
Test script to verify backend API integration
"""

import requests
import json

BASE_URL = "http://localhost:8085"

def test_health():
    """Test health endpoint"""
    try:
        response = requests.get(f"{BASE_URL}/health")
        print(f"Health check: {response.status_code}")
        if response.status_code == 200:
            print(f"Response: {response.json()}")
            return True
    except Exception as e:
        print(f"Health check failed: {e}")
    return False

def test_login():
    """Test login endpoint"""
    try:
        # Test with demo credentials
        credentials = {
            "email": "<EMAIL>",
            "password": "password123"
        }
        
        response = requests.post(f"{BASE_URL}/api/auth/login", json=credentials)
        print(f"Login test: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"Login successful: {data}")
            return data.get('access_token')
        else:
            print(f"Login failed: {response.text}")
    except Exception as e:
        print(f"Login test failed: {e}")
    return None

def test_protected_endpoint(token):
    """Test a protected endpoint"""
    if not token:
        print("No token available for protected endpoint test")
        return
        
    try:
        headers = {"Authorization": f"Bearer {token}"}
        response = requests.get(f"{BASE_URL}/api/employees/", headers=headers)
        print(f"Protected endpoint test: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"Employees data: {data}")
        else:
            print(f"Protected endpoint failed: {response.text}")
    except Exception as e:
        print(f"Protected endpoint test failed: {e}")

def main():
    print("Testing Backend API Integration")
    print("=" * 50)
    
    # Test health
    if not test_health():
        print("Backend is not running or not healthy")
        return
    
    # Test login
    token = test_login()
    
    # Test protected endpoint
    test_protected_endpoint(token)
    
    print("\nTest completed!")

if __name__ == "__main__":
    main()
