from jinja2 import Template
from core.services.email_service import EmailService

class PayslipService:
    def __init__(self):
        self.payslip_attachment = "employee_payslip.pdf" #@TODO create the payslip 

    @staticmethod
    def send_payslips(self, employees=None, attachments=None):
   
        with open("email/payslip_email.html", "r") as file:
            template_content = file.read()

        emp = {
            "name": "<PERSON> Do<PERSON>",
            "month": "November",
            "year": "2024",
            "email": "<EMAIL>"
        }

        template = Template(template_content)
        html_body = template.render(name=emp['name'], month=emp['month'], year=emp['year'])

        EmailService.send_email(
            subject=f"Your Payslip for {emp['month']} {emp['year']}" ,
            recipient=emp['email'],
            # text_body="Your payslip for October 2024 has been released. Please check your email for details.",
            html_body=html_body
            # attachments=self.payslip_attachment
        )
