from sqlalchemy.orm import joinedload
from core.models.email_config import EmailConfiguration
from typing import List, Optional
from core.databases.database import db
from core.repositories.user import UserRepository
from sqlalchemy import func

class EmailConfigurationRepository:
    def __init__(self) -> None:
        pass

    @classmethod
    def createConfiguration(self, config_data):
        configuration = EmailConfiguration(
            smtp_server=config_data.smtp_server,
            smtp_port=config_data.smtp_port,
            username=config_data.username,
            use_ssl=config_data.use_ssl,
            use_tls=config_data.use_tls,
            sender_email=config_data.sender_email,
            is_active=config_data.is_active,
            user_id=UserRepository.authUserId()
        )
        configuration.set_password(config_data.password)
        db.session.add(configuration)
        db.session.commit()
        # If this is set as active, deactivate others
        if config_data.is_active:
            self.deactivate_other_configurations()
        
        return configuration
    
    def deactivate_other_configurations():
        EmailConfiguration.query.filter(
            EmailConfiguration.user_id == UserRepository().authUserId(),
            EmailConfiguration.is_active == True
        ).update({"is_active": False})
        db.commit()    

    @classmethod
    def getConfig(self):
        user_id=UserRepository.authUserId()
        return EmailConfiguration.query.filter(EmailConfiguration.id == user_id).first()
    
    @classmethod
    def getActiveConfig(self):
        logged_in_user=UserRepository.authUserId()
        return EmailConfiguration.query.filter(
            EmailConfiguration.id == logged_in_user,
            EmailConfiguration.is_active == True
        ).first()
    
    @classmethod
    def getConfigsByKeys(self, kwargs):
        return EmailConfiguration.query.filter_by(user_id=UserRepository().authUserId(), **kwargs).all()

    @classmethod
    def updateConfig(self, **kwargs):
        configuration = EmailConfiguration.query.filter_by(id=UserRepository().authUserId()).first()
        if configuration:
            for key, value in kwargs.items():
                setattr(configuration, key, value)
            db.session.commit()
            return configuration
        else:
            return None
    
    @classmethod
    def deleteConfig(self):
        user_id = UserRepository.authUserId()
        EmailConfiguration.query.filter(EmailConfiguration.user_id == user_id).delete()
        db.session.commit()
        return
    




