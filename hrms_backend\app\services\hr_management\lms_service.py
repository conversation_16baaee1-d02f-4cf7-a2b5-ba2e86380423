from typing import Optional, List, Dict, Any, Tuple
from sqlalchemy.orm import Session, joinedload
from sqlalchemy import and_, or_, func, desc, asc
from fastapi import HTTPException, status
from uuid import UUID
from datetime import datetime, date
import logging

from ...db.models.lms import (
    Course, CourseModule, CourseEnrollment, ModuleProgress, Assessment,
    AssessmentAttempt, Certification, CourseStatus, EnrollmentStatus, CertificationStatus
)
from ...db.models.employee import Employee
from ...schemas.lms import (
    CourseCreate, CourseUpdate, CourseModuleCreate, CourseEnrollmentCreate,
    ModuleProgressUpdate, AssessmentCreate, AssessmentAttemptCreate, CertificationCreate
)
from ...core.security import CurrentUser
from ...core.audit_logger import AuditLogger

logger = logging.getLogger(__name__)


class LMSService:
    """Service for Learning Management System"""

    # Course Methods
    async def create_course(
        self,
        db: Session,
        course_data: CourseCreate,
        current_user: CurrentUser
    ) -> Course:
        """Create a new course"""
        try:
            # Verify instructor exists if provided
            if course_data.instructor_id:
                instructor = db.query(Employee).filter(
                    Employee.id == course_data.instructor_id
                ).first()
                if not instructor:
                    raise HTTPException(
                        status_code=status.HTTP_404_NOT_FOUND,
                        detail="Instructor not found"
                    )

            # Create course
            course = Course(
                **course_data.dict(),
                organization_id=current_user.organization_id,
                status=CourseStatus.DRAFT
            )

            db.add(course)
            db.commit()
            db.refresh(course)

            # Log audit event
            AuditLogger.log_action(
                db=db,
                action="CREATE",
                resource_type="courses",
                resource_id=str(course.id),
                user=current_user,
                new_values=course_data.dict()
            )

            return course

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error creating course: {e}")
            db.rollback()
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to create course"
            )

    async def get_courses(
        self,
        db: Session,
        skip: int = 0,
        limit: int = 20,
        status: Optional[CourseStatus] = None,
        course_type: Optional[str] = None,
        category: Optional[str] = None,
        instructor_id: Optional[UUID] = None,
        search: Optional[str] = None,
        current_user: CurrentUser = None
    ) -> Tuple[List[Course], int]:
        """Get courses with filtering"""
        try:
            query = db.query(Course).options(
                joinedload(Course.instructor)
            )

            # Apply organization filter
            if current_user and current_user.organization_id:
                query = query.filter(Course.organization_id == current_user.organization_id)

            # Apply filters
            if status:
                query = query.filter(Course.status == status)
            
            if course_type:
                query = query.filter(Course.course_type == course_type)
            
            if category:
                query = query.filter(Course.category == category)
            
            if instructor_id:
                query = query.filter(Course.instructor_id == instructor_id)
            
            if search:
                search_filter = or_(
                    Course.title.ilike(f"%{search}%"),
                    Course.description.ilike(f"%{search}%"),
                    Course.category.ilike(f"%{search}%")
                )
                query = query.filter(search_filter)

            # Get total count
            total = query.count()

            # Apply pagination and ordering
            courses = query.order_by(desc(Course.created_at)).offset(skip).limit(limit).all()

            return courses, total

        except Exception as e:
            logger.error(f"Error getting courses: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to retrieve courses"
            )

    async def get_course(self, db: Session, course_id: UUID) -> Course:
        """Get a specific course"""
        course = db.query(Course).options(
            joinedload(Course.instructor),
            joinedload(Course.modules),
            joinedload(Course.enrollments),
            joinedload(Course.assessments)
        ).filter(Course.id == course_id).first()

        if not course:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Course not found"
            )

        return course

    async def update_course(
        self,
        db: Session,
        course_id: UUID,
        course_data: CourseUpdate,
        current_user: CurrentUser
    ) -> Course:
        """Update a course"""
        try:
            course = await self.get_course(db, course_id)

            # Store old values for audit
            old_values = {
                "title": course.title,
                "status": course.status.value if course.status else None,
                "description": course.description
            }

            # Update fields
            update_data = course_data.dict(exclude_unset=True)
            for field, value in update_data.items():
                setattr(course, field, value)

            db.commit()
            db.refresh(course)

            # Log audit event
            AuditLogger.log_action(
                db=db,
                action="UPDATE",
                resource_type="courses",
                resource_id=str(course.id),
                user=current_user,
                old_values=old_values,
                new_values=update_data
            )

            return course

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error updating course: {e}")
            db.rollback()
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to update course"
            )

    async def publish_course(
        self,
        db: Session,
        course_id: UUID,
        current_user: CurrentUser
    ) -> Course:
        """Publish a course"""
        try:
            course = await self.get_course(db, course_id)

            if course.status != CourseStatus.DRAFT:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Only draft courses can be published"
                )

            course.status = CourseStatus.PUBLISHED

            db.commit()
            db.refresh(course)

            # Log audit event
            AuditLogger.log_action(
                db=db,
                action="PUBLISH",
                resource_type="courses",
                resource_id=str(course.id),
                user=current_user,
                new_values={"status": "published"}
            )

            return course

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error publishing course: {e}")
            db.rollback()
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to publish course"
            )

    # Course Module Methods
    async def create_course_module(
        self,
        db: Session,
        module_data: CourseModuleCreate,
        current_user: CurrentUser
    ) -> CourseModule:
        """Create a course module"""
        try:
            # Verify course exists
            course = await self.get_course(db, module_data.course_id)

            # Create module
            module = CourseModule(
                **module_data.dict(),
                organization_id=current_user.organization_id
            )

            db.add(module)
            db.commit()
            db.refresh(module)

            # Log audit event
            AuditLogger.log_action(
                db=db,
                action="CREATE",
                resource_type="course_modules",
                resource_id=str(module.id),
                user=current_user,
                new_values=module_data.dict()
            )

            return module

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error creating course module: {e}")
            db.rollback()
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to create course module"
            )

    # Enrollment Methods
    async def enroll_employee(
        self,
        db: Session,
        enrollment_data: CourseEnrollmentCreate,
        current_user: CurrentUser
    ) -> CourseEnrollment:
        """Enroll an employee in a course"""
        try:
            # Verify course and employee exist
            course = await self.get_course(db, enrollment_data.course_id)
            employee = db.query(Employee).filter(
                Employee.id == enrollment_data.employee_id
            ).first()

            if not employee:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Employee not found"
                )

            # Check if already enrolled
            existing_enrollment = db.query(CourseEnrollment).filter(
                and_(
                    CourseEnrollment.course_id == enrollment_data.course_id,
                    CourseEnrollment.employee_id == enrollment_data.employee_id
                )
            ).first()

            if existing_enrollment:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Employee is already enrolled in this course"
                )

            # Check enrollment limits
            if course.max_enrollments and course.current_enrollments >= course.max_enrollments:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Course enrollment limit reached"
                )

            # Create enrollment
            enrollment = CourseEnrollment(
                **enrollment_data.dict(),
                organization_id=current_user.organization_id,
                enrolled_by_id=current_user.user_id,
                status=EnrollmentStatus.ENROLLED
            )

            db.add(enrollment)

            # Update course enrollment count
            course.current_enrollments += 1

            db.commit()
            db.refresh(enrollment)

            # Log audit event
            AuditLogger.log_action(
                db=db,
                action="ENROLL",
                resource_type="course_enrollments",
                resource_id=str(enrollment.id),
                user=current_user,
                new_values=enrollment_data.dict()
            )

            return enrollment

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error enrolling employee: {e}")
            db.rollback()
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to enroll employee"
            )

    async def update_module_progress(
        self,
        db: Session,
        enrollment_id: UUID,
        module_id: UUID,
        progress_data: ModuleProgressUpdate,
        current_user: CurrentUser
    ) -> ModuleProgress:
        """Update module progress"""
        try:
            # Get or create module progress
            progress = db.query(ModuleProgress).filter(
                and_(
                    ModuleProgress.enrollment_id == enrollment_id,
                    ModuleProgress.module_id == module_id
                )
            ).first()

            if not progress:
                progress = ModuleProgress(
                    enrollment_id=enrollment_id,
                    module_id=module_id,
                    organization_id=current_user.organization_id
                )
                db.add(progress)

            # Update progress
            for field, value in progress_data.dict(exclude_unset=True).items():
                setattr(progress, field, value)

            # Update timestamps
            if progress_data.status == "in_progress" and not progress.started_date:
                progress.started_date = datetime.utcnow()
            elif progress_data.status == "completed" and not progress.completed_date:
                progress.completed_date = datetime.utcnow()

            progress.last_accessed = datetime.utcnow()
            progress.access_count += 1

            db.commit()
            db.refresh(progress)

            # Update enrollment progress
            await self._update_enrollment_progress(db, enrollment_id)

            return progress

        except Exception as e:
            logger.error(f"Error updating module progress: {e}")
            db.rollback()
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to update module progress"
            )

    async def create_assessment(
        self,
        db: Session,
        assessment_data: AssessmentCreate,
        current_user: CurrentUser
    ) -> Assessment:
        """Create an assessment"""
        try:
            # Verify course exists
            course = await self.get_course(db, assessment_data.course_id)

            # Create assessment
            assessment = Assessment(
                **assessment_data.dict(),
                organization_id=current_user.organization_id,
                is_active=True
            )

            db.add(assessment)
            db.commit()
            db.refresh(assessment)

            # Log audit event
            AuditLogger.log_action(
                db=db,
                action="CREATE",
                resource_type="assessments",
                resource_id=str(assessment.id),
                user=current_user,
                new_values=assessment_data.dict()
            )

            return assessment

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error creating assessment: {e}")
            db.rollback()
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to create assessment"
            )

    async def submit_assessment_attempt(
        self,
        db: Session,
        attempt_data: AssessmentAttemptCreate,
        current_user: CurrentUser
    ) -> AssessmentAttempt:
        """Submit an assessment attempt"""
        try:
            # Get assessment
            assessment = db.query(Assessment).filter(
                Assessment.id == attempt_data.assessment_id
            ).first()

            if not assessment:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Assessment not found"
                )

            # Find enrollment
            enrollment = db.query(CourseEnrollment).filter(
                CourseEnrollment.course_id == assessment.course_id,
                CourseEnrollment.employee_id == current_user.user_id
            ).first()

            if not enrollment:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Course enrollment not found"
                )

            # Check attempt limits
            attempt_count = db.query(AssessmentAttempt).filter(
                AssessmentAttempt.assessment_id == attempt_data.assessment_id,
                AssessmentAttempt.enrollment_id == enrollment.id
            ).count()

            if assessment.max_attempts and attempt_count >= assessment.max_attempts:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Maximum attempts exceeded"
                )

            # Create attempt
            attempt = AssessmentAttempt(
                assessment_id=attempt_data.assessment_id,
                enrollment_id=enrollment.id,
                attempt_number=attempt_count + 1,
                responses=attempt_data.responses,
                organization_id=current_user.organization_id,
                status="submitted",
                submitted_date=datetime.utcnow()
            )

            # Auto-grade if enabled
            if assessment.auto_grade:
                score = await self._calculate_assessment_score(assessment, attempt_data.responses)
                attempt.score = score
                attempt.passed = score >= assessment.passing_score
                attempt.status = "graded"

            db.add(attempt)
            db.commit()
            db.refresh(attempt)

            return attempt

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error submitting assessment attempt: {e}")
            db.rollback()
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to submit assessment attempt"
            )

    async def _calculate_assessment_score(self, assessment: Assessment, responses: Dict[str, Any]) -> float:
        """Calculate assessment score based on responses"""
        try:
            total_questions = len(assessment.questions)
            if total_questions == 0:
                return 0.0

            correct_answers = 0

            for question in assessment.questions:
                question_id = question.get("id")
                correct_answer = question.get("correct_answer")
                user_answer = responses.get(question_id)

                if user_answer == correct_answer:
                    correct_answers += 1

            return (correct_answers / total_questions) * 100.0

        except Exception as e:
            logger.error(f"Error calculating assessment score: {e}")
            return 0.0

    async def _update_enrollment_progress(self, db: Session, enrollment_id: UUID):
        """Update overall enrollment progress"""
        try:
            enrollment = db.query(CourseEnrollment).filter(
                CourseEnrollment.id == enrollment_id
            ).first()

            if not enrollment:
                return

            # Get course modules
            modules = db.query(CourseModule).filter(
                CourseModule.course_id == enrollment.course_id
            ).all()

            if not modules:
                return

            # Get completed modules
            completed_modules = db.query(ModuleProgress).filter(
                and_(
                    ModuleProgress.enrollment_id == enrollment_id,
                    ModuleProgress.status == "completed"
                )
            ).count()

            # Update enrollment progress
            enrollment.total_modules = len(modules)
            enrollment.modules_completed = completed_modules
            enrollment.progress_percentage = int((completed_modules / len(modules)) * 100)

            # Check if course is completed
            if completed_modules == len(modules):
                enrollment.status = EnrollmentStatus.COMPLETED
                enrollment.completion_date = datetime.utcnow()

            db.commit()

        except Exception as e:
            logger.error(f"Error updating enrollment progress: {e}")
            db.rollback()
