from flask import url_for, jsonify
from flask.views import <PERSON><PERSON>ie<PERSON>
from flask_smorest import Blueprint, abort
from core.middleware import roles_required
from schemas import DesignationSchema
from sqlalchemy.orm.exc import NoResultFound
from sqlalchemy.exc import SQLAlchemyError, IntegrityError
import core.utils.response_message as RESPONSEMESSAGE
from core.services.designations import DesignationsService
from core.utils.responseBuilder import ResponseBuilder

blueprint = Blueprint("Designation", __name__, description="Operations for Designations")
    
@blueprint.route("/designations/<id>")
class DesignationList(MethodView):
    @roles_required(['admin'])
    @blueprint.response(200, DesignationSchema)
    def get(self, id):
        department_service = DesignationsService()
        designation = department_service.getDesignations(id)
        if not designation:
            abort(401, message="Designation does not exist")
        designation_details = DesignationSchema().dump(designation)
        return ResponseBuilder(data=designation_details, status_code=200).build()     
    
    @roles_required(['admin'])
    def delete(self, id):
        department_service = DesignationsService()
        designation = department_service.getDesignations(id)
        if not designation:
            abort(404, message="Designation does not exist")

        if designation.employees:
            abort(400, message=f"Cannot delete designation. It is still assigned to employees.")

        department_service.deleteDesignations(id)
        return {"message" : "Designation deleted"}
    
    @roles_required(['admin'])
    @blueprint.arguments(DesignationSchema)
    @blueprint.response(201, DesignationSchema)
    def put(self, data, id):
        department_service = DesignationsService()
        designation = department_service.getDesignations(id)
        if not designation:
            abort(404, message="Designation does not exist")
        try :
            designation = department_service.updateDesignations(id, data)
            new_designation = DesignationSchema().dump(designation)
            return ResponseBuilder(data=new_designation, status_code=200).build()

        except SQLAlchemyError:
                abort(500, message="Error while updating Designation")
    
@blueprint.route("/designations")
class Designation(MethodView):
    @roles_required(['admin'])
    @blueprint.response(200, DesignationSchema)
    def get(self):
        designation_service = DesignationsService()
        designation_list, total_designation = designation_service.fetchAll()
        return ResponseBuilder(data=designation_list, status_code=200, total=total_designation).build()
    
    @roles_required(['admin'])
    @blueprint.arguments(DesignationSchema)
    @blueprint.response(200, DesignationSchema)
    def post(self, data):
        try:
            department_service = DesignationsService()
            designation = department_service.getDesignationsByKey({"name": data['name']})
            if not designation:
                new_designation = department_service.createDesignations(data)
            else:
                abort(400, message="Designation already exist")
        except IntegrityError:
            abort(500, message="Error while creating Designation")
        except SQLAlchemyError as e:
            print(e)
            abort(500, message="Error while creating Designation")
        return new_designation