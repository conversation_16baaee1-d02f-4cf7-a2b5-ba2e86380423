"""
Enhanced data validation utilities for the HRMS system
"""

import re
import logging
from typing import Any, Dict, List, Optional, Union, Callable
from datetime import date, datetime, time
from decimal import Decimal, InvalidOperation
from uuid import UUID
from email_validator import validate_email, EmailNotValidError
from pydantic import validator, ValidationError
from fastapi import HTTPException, status

logger = logging.getLogger(__name__)


class ValidationError(Exception):
    """Custom validation error"""
    
    def __init__(self, message: str, field: str = None, code: str = None):
        self.message = message
        self.field = field
        self.code = code
        super().__init__(message)


class DataValidator:
    """Enhanced data validation utilities"""
    
    @staticmethod
    def validate_email_address(email: str) -> str:
        """Validate email address format and domain"""
        if not email or not isinstance(email, str):
            raise ValidationError("Email is required", "email", "REQUIRED")
        
        try:
            # Use email-validator library for comprehensive validation
            validated_email = validate_email(email)
            return validated_email.email
        except EmailNotValidError as e:
            raise ValidationError(f"Invalid email format: {str(e)}", "email", "INVALID_FORMAT")
    
    @staticmethod
    def validate_phone_number(phone: str, country_code: str = "US") -> str:
        """Validate phone number format"""
        if not phone:
            return phone
        
        # Remove all non-digit characters
        digits_only = re.sub(r'\D', '', phone)
        
        # Basic validation for different formats
        if country_code == "US":
            if len(digits_only) == 10:
                # Format as (XXX) XXX-XXXX
                return f"({digits_only[:3]}) {digits_only[3:6]}-{digits_only[6:]}"
            elif len(digits_only) == 11 and digits_only[0] == '1':
                # Remove leading 1 and format
                digits_only = digits_only[1:]
                return f"({digits_only[:3]}) {digits_only[3:6]}-{digits_only[6:]}"
            else:
                raise ValidationError("Invalid US phone number format", "phone", "INVALID_FORMAT")
        else:
            # Basic international format validation
            if len(digits_only) < 7 or len(digits_only) > 15:
                raise ValidationError("Invalid phone number length", "phone", "INVALID_LENGTH")
            return f"+{digits_only}"
    
    @staticmethod
    def validate_employee_id(employee_id: str) -> str:
        """Validate employee ID format"""
        if not employee_id:
            raise ValidationError("Employee ID is required", "employee_id", "REQUIRED")
        
        # Remove whitespace
        employee_id = employee_id.strip().upper()
        
        # Check format: Should be alphanumeric, 3-20 characters
        if not re.match(r'^[A-Z0-9]{3,20}$', employee_id):
            raise ValidationError(
                "Employee ID must be 3-20 alphanumeric characters",
                "employee_id",
                "INVALID_FORMAT"
            )
        
        return employee_id
    
    @staticmethod
    def validate_name(name: str, field_name: str = "name") -> str:
        """Validate person name"""
        if not name:
            raise ValidationError(f"{field_name.title()} is required", field_name, "REQUIRED")
        
        # Remove extra whitespace
        name = re.sub(r'\s+', ' ', name.strip())
        
        # Check length
        if len(name) < 2:
            raise ValidationError(
                f"{field_name.title()} must be at least 2 characters",
                field_name,
                "TOO_SHORT"
            )
        
        if len(name) > 50:
            raise ValidationError(
                f"{field_name.title()} must be less than 50 characters",
                field_name,
                "TOO_LONG"
            )
        
        # Check for valid characters (letters, spaces, hyphens, apostrophes)
        if not re.match(r"^[a-zA-Z\s\-'\.]+$", name):
            raise ValidationError(
                f"{field_name.title()} contains invalid characters",
                field_name,
                "INVALID_CHARACTERS"
            )
        
        return name
    
    @staticmethod
    def validate_date_range(start_date: date, end_date: date) -> tuple:
        """Validate date range"""
        if not start_date or not end_date:
            raise ValidationError("Both start and end dates are required", "date_range", "REQUIRED")
        
        if start_date > end_date:
            raise ValidationError("Start date must be before end date", "date_range", "INVALID_RANGE")
        
        # Check for reasonable date range (not more than 1 year)
        if (end_date - start_date).days > 365:
            raise ValidationError("Date range cannot exceed 1 year", "date_range", "RANGE_TOO_LARGE")
        
        return start_date, end_date
    
    @staticmethod
    def validate_time_range(start_time: time, end_time: time) -> tuple:
        """Validate time range"""
        if not start_time or not end_time:
            raise ValidationError("Both start and end times are required", "time_range", "REQUIRED")
        
        if start_time >= end_time:
            raise ValidationError("Start time must be before end time", "time_range", "INVALID_RANGE")
        
        return start_time, end_time
    
    @staticmethod
    def validate_duration_minutes(duration: int) -> int:
        """Validate duration in minutes"""
        if duration is None:
            raise ValidationError("Duration is required", "duration", "REQUIRED")
        
        if not isinstance(duration, int):
            try:
                duration = int(duration)
            except (ValueError, TypeError):
                raise ValidationError("Duration must be a number", "duration", "INVALID_TYPE")
        
        if duration <= 0:
            raise ValidationError("Duration must be positive", "duration", "INVALID_VALUE")
        
        if duration > 1440:  # 24 hours
            raise ValidationError("Duration cannot exceed 24 hours", "duration", "TOO_LARGE")
        
        return duration
    
    @staticmethod
    def validate_salary(salary: Union[int, float, Decimal]) -> Decimal:
        """Validate salary amount"""
        if salary is None:
            raise ValidationError("Salary is required", "salary", "REQUIRED")
        
        try:
            salary_decimal = Decimal(str(salary))
        except (InvalidOperation, ValueError):
            raise ValidationError("Invalid salary format", "salary", "INVALID_FORMAT")
        
        if salary_decimal < 0:
            raise ValidationError("Salary cannot be negative", "salary", "INVALID_VALUE")
        
        if salary_decimal > Decimal('10000000'):  # 10 million
            raise ValidationError("Salary amount too large", "salary", "TOO_LARGE")
        
        # Round to 2 decimal places
        return salary_decimal.quantize(Decimal('0.01'))
    
    @staticmethod
    def validate_percentage(percentage: Union[int, float]) -> float:
        """Validate percentage value"""
        if percentage is None:
            raise ValidationError("Percentage is required", "percentage", "REQUIRED")
        
        try:
            percentage_float = float(percentage)
        except (ValueError, TypeError):
            raise ValidationError("Invalid percentage format", "percentage", "INVALID_FORMAT")
        
        if percentage_float < 0 or percentage_float > 100:
            raise ValidationError("Percentage must be between 0 and 100", "percentage", "OUT_OF_RANGE")
        
        return percentage_float
    
    @staticmethod
    def validate_uuid(uuid_string: str, field_name: str = "id") -> UUID:
        """Validate UUID format"""
        if not uuid_string:
            raise ValidationError(f"{field_name.title()} is required", field_name, "REQUIRED")
        
        try:
            return UUID(str(uuid_string))
        except (ValueError, TypeError):
            raise ValidationError(f"Invalid {field_name} format", field_name, "INVALID_FORMAT")
    
    @staticmethod
    def validate_text_length(text: str, min_length: int = 0, max_length: int = 1000, field_name: str = "text") -> str:
        """Validate text length"""
        if text is None:
            text = ""
        
        text = str(text).strip()
        
        if len(text) < min_length:
            raise ValidationError(
                f"{field_name.title()} must be at least {min_length} characters",
                field_name,
                "TOO_SHORT"
            )
        
        if len(text) > max_length:
            raise ValidationError(
                f"{field_name.title()} must be less than {max_length} characters",
                field_name,
                "TOO_LONG"
            )
        
        return text
    
    @staticmethod
    def validate_choice(value: str, choices: List[str], field_name: str = "choice") -> str:
        """Validate value is in allowed choices"""
        if not value:
            raise ValidationError(f"{field_name.title()} is required", field_name, "REQUIRED")
        
        if value not in choices:
            raise ValidationError(
                f"{field_name.title()} must be one of: {', '.join(choices)}",
                field_name,
                "INVALID_CHOICE"
            )
        
        return value
    
    @staticmethod
    def sanitize_html(text: str) -> str:
        """Basic HTML sanitization"""
        if not text:
            return text
        
        # Remove potentially dangerous HTML tags and attributes
        dangerous_patterns = [
            r'<script[^>]*>.*?</script>',
            r'<iframe[^>]*>.*?</iframe>',
            r'<object[^>]*>.*?</object>',
            r'<embed[^>]*>.*?</embed>',
            r'<form[^>]*>.*?</form>',
            r'javascript:',
            r'vbscript:',
            r'onload=',
            r'onerror=',
            r'onclick=',
            r'onmouseover='
        ]
        
        for pattern in dangerous_patterns:
            text = re.sub(pattern, '', text, flags=re.IGNORECASE | re.DOTALL)
        
        return text
    
    @staticmethod
    def validate_file_upload(filename: str, allowed_extensions: List[str], max_size_mb: int = 10) -> str:
        """Validate file upload"""
        if not filename:
            raise ValidationError("Filename is required", "filename", "REQUIRED")
        
        # Check extension
        file_extension = filename.lower().split('.')[-1] if '.' in filename else ''
        
        if file_extension not in [ext.lower() for ext in allowed_extensions]:
            raise ValidationError(
                f"File type not allowed. Allowed types: {', '.join(allowed_extensions)}",
                "filename",
                "INVALID_TYPE"
            )
        
        # Additional security checks
        dangerous_names = ['..', '/', '\\', '<', '>', ':', '"', '|', '?', '*']
        for dangerous in dangerous_names:
            if dangerous in filename:
                raise ValidationError("Filename contains invalid characters", "filename", "INVALID_CHARACTERS")
        
        return filename


class BusinessRuleValidator:
    """Business rule validation utilities"""
    
    @staticmethod
    def validate_leave_request(start_date: date, end_date: date, leave_type: str, employee_id: str) -> Dict[str, Any]:
        """Validate leave request business rules"""
        errors = []
        
        # Date validation
        try:
            DataValidator.validate_date_range(start_date, end_date)
        except ValidationError as e:
            errors.append(e.message)
        
        # Check if dates are in the future (for new requests)
        if start_date and start_date < date.today():
            errors.append("Leave start date cannot be in the past")
        
        # Check for weekends (optional business rule)
        if start_date and start_date.weekday() >= 5:  # Saturday = 5, Sunday = 6
            errors.append("Leave cannot start on weekends")
        
        # Validate leave type
        valid_leave_types = ["ANNUAL", "SICK", "PERSONAL", "MATERNITY", "PATERNITY", "EMERGENCY"]
        try:
            DataValidator.validate_choice(leave_type, valid_leave_types, "leave_type")
        except ValidationError as e:
            errors.append(e.message)
        
        return {
            "is_valid": len(errors) == 0,
            "errors": errors
        }
    
    @staticmethod
    def validate_timesheet_entry(start_time: datetime, duration_minutes: int, project_id: str = None) -> Dict[str, Any]:
        """Validate timesheet entry business rules"""
        errors = []
        
        # Duration validation
        try:
            DataValidator.validate_duration_minutes(duration_minutes)
        except ValidationError as e:
            errors.append(e.message)
        
        # Check if entry is not too far in the future
        if start_time and start_time.date() > date.today():
            errors.append("Timesheet entry cannot be in the future")
        
        # Check if entry is not too old (e.g., more than 30 days)
        if start_time and (date.today() - start_time.date()).days > 30:
            errors.append("Timesheet entry cannot be more than 30 days old")
        
        # Check working hours (8 AM to 8 PM)
        if start_time and (start_time.hour < 6 or start_time.hour > 22):
            errors.append("Timesheet entry outside normal working hours")
        
        return {
            "is_valid": len(errors) == 0,
            "errors": errors
        }
    
    @staticmethod
    def validate_employee_data(employee_data: Dict[str, Any]) -> Dict[str, Any]:
        """Validate employee data business rules"""
        errors = []
        
        # Required fields
        required_fields = ["first_name", "last_name", "email", "employee_id"]
        for field in required_fields:
            if not employee_data.get(field):
                errors.append(f"{field.replace('_', ' ').title()} is required")
        
        # Validate individual fields
        try:
            if employee_data.get("email"):
                DataValidator.validate_email_address(employee_data["email"])
        except ValidationError as e:
            errors.append(e.message)
        
        try:
            if employee_data.get("first_name"):
                DataValidator.validate_name(employee_data["first_name"], "first_name")
        except ValidationError as e:
            errors.append(e.message)
        
        try:
            if employee_data.get("last_name"):
                DataValidator.validate_name(employee_data["last_name"], "last_name")
        except ValidationError as e:
            errors.append(e.message)
        
        try:
            if employee_data.get("employee_id"):
                DataValidator.validate_employee_id(employee_data["employee_id"])
        except ValidationError as e:
            errors.append(e.message)
        
        # Age validation
        if employee_data.get("date_of_birth"):
            birth_date = employee_data["date_of_birth"]
            if isinstance(birth_date, str):
                try:
                    birth_date = datetime.strptime(birth_date, "%Y-%m-%d").date()
                except ValueError:
                    errors.append("Invalid date of birth format")
                    birth_date = None
            
            if birth_date:
                age = (date.today() - birth_date).days // 365
                if age < 16:
                    errors.append("Employee must be at least 16 years old")
                elif age > 100:
                    errors.append("Invalid date of birth")
        
        return {
            "is_valid": len(errors) == 0,
            "errors": errors
        }


# Validation decorators
def validate_request_data(validation_func: Callable):
    """Decorator to validate request data"""
    def decorator(func):
        def wrapper(*args, **kwargs):
            # Extract request data (assuming it's in kwargs)
            request_data = kwargs.get('data') or kwargs.get('request_data')
            
            if request_data:
                validation_result = validation_func(request_data)
                if not validation_result.get("is_valid"):
                    raise HTTPException(
                        status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
                        detail={
                            "message": "Validation failed",
                            "errors": validation_result.get("errors", [])
                        }
                    )
            
            return func(*args, **kwargs)
        return wrapper
    return decorator


# Export main components
__all__ = [
    "DataValidator",
    "BusinessRuleValidator",
    "ValidationError",
    "validate_request_data"
]
