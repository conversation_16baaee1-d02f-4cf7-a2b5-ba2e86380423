"""update approvals constraint

Revision ID: 996091fd865e
Revises: 9debabcb517c
Create Date: 2025-05-11 08:43:21.410748

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '996091fd865e'
down_revision: Union[str, None] = '9debabcb517c'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    with op.batch_alter_table('approvals') as batch_op:
        batch_op.drop_constraint('approvals_level_key', type_='unique')
        batch_op.create_unique_constraint('uq_user_level', ['user_id', 'level'])

def downgrade() -> None:
    with op.batch_alter_table('approvals') as batch_op:
        batch_op.drop_constraint('uq_user_level', type_='unique')
        batch_op.create_unique_constraint('approvals_level_key', ['level'])
