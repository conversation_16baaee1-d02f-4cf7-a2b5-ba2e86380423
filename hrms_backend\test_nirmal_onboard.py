"""
Test the exact onboarding request from the frontend
"""

import requests
import j<PERSON>

def test_nirmal_onboarding():
    """Test onboarding for <PERSON><PERSON>"""
    
    base_url = "http://localhost:8085/api"
    
    print("🧪 Testing Nirmal Kumar M Onboarding")
    print("=" * 50)
    
    # Step 1: Login as admin
    print("1. Logging in as admin...")
    login_response = requests.post(f"{base_url}/auth/login", json={
        "email": "<EMAIL>",
        "password": "password123"
    })
    
    if login_response.status_code != 200:
        print(f"❌ Login failed: {login_response.status_code}")
        print("Response:", login_response.text)
        return
    
    token = login_response.json()["access_token"]
    headers = {"Authorization": f"Bearer {token}"}
    print("✅ Login successful")
    
    # Step 2: Test the exact data from the frontend
    print("\n2. Testing onboarding with frontend data...")
    
    onboard_data = {
        "employee_name": "<PERSON><PERSON> M Test",
        "employee_email": "<EMAIL>",
        "start_date": "2025-07-03"
    }
    
    print(f"Request data: {json.dumps(onboard_data, indent=2)}")
    
    response = requests.post(
        f"{base_url}/simple-onboarding/simple-onboard",
        json=onboard_data,
        headers=headers
    )
    
    print(f"\nStatus Code: {response.status_code}")
    
    if response.status_code == 200:
        result = response.json()
        print("✅ Onboarding successful!")
        print(f"Employee ID: {result['employee_id']}")
        print(f"Employee Name: {result['employee_name']}")
        print(f"Email: {result['email']}")
        print(f"Temporary Password: {result['temporary_password']}")
        print(f"Message: {result['message']}")
        
        # This is what the frontend should show
        print("\n🎉 SUCCESS MESSAGE FOR FRONTEND:")
        print("-" * 40)
        print(f"Employee {result['employee_name']} onboarded successfully!")
        print(f"Employee ID: {result['employee_id']}")
        print(f"Login Email: {result['email']}")
        print(f"Temporary Password: {result['temporary_password']}")
        print("Please share these credentials with the new employee.")
        
    elif response.status_code == 400:
        error_detail = response.json().get('detail', 'Unknown error')
        if 'already exists' in error_detail:
            print("⚠️  Employee with this email already exists")
            print("This is expected if you've already tested with this email")
            print("\n💡 Try with a different email or delete the existing employee")
        else:
            print(f"❌ Bad request: {error_detail}")
    else:
        print(f"❌ Onboarding failed: {response.status_code}")
        print("Response:", response.text)
    
    # Step 3: Check current employee count
    print("\n3. Checking employee count...")
    employees_response = requests.get(f"{base_url}/employees/", headers=headers)
    if employees_response.status_code == 200:
        employees = employees_response.json()
        print(f"✅ Total employees: {len(employees)}")
        
        # Find Nirmal if created
        try:
            nirmal = next((emp for emp in employees if '<EMAIL>' in str(emp)), None)
            if nirmal:
                print(f"✅ Found Nirmal Kumar M Test in employee list")
            else:
                print("ℹ️  Nirmal Kumar M Test not found in employee list")
        except Exception as e:
            print(f"ℹ️  Could not search employees: {e}")
    else:
        print(f"⚠️  Could not fetch employees: {employees_response.status_code}")

if __name__ == "__main__":
    test_nirmal_onboarding()
