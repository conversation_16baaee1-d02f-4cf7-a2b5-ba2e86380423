from flask import url_for, jsonify
from flask.views import <PERSON><PERSON>iew
from flask_smorest import Blueprint, abort
from core.middleware import roles_required
from schemas import DepartmentSchema
from sqlalchemy.orm.exc import NoResultFound
from sqlalchemy.exc import SQLAlchemyError, IntegrityError
import core.utils.response_message as RESPONSEMESSAGE
from core.services.departments import DepartmentsService
from core.utils.responseBuilder import ResponseBuilder

blueprint = Blueprint("Department", __name__, description="Operations for Departments")
    
@blueprint.route("/departments/<id>")
class DepartmentList(MethodView):
    @roles_required(['admin'])
    @blueprint.response(200, DepartmentSchema)
    def get(self, id):
        service = DepartmentsService()
        department = service.getDepartments(id)
        if not department:
            abort(401, message="Department does not exist")
        department_details = DepartmentSchema().dump(department)
        return ResponseBuilder(data=department_details, status_code=200).build()    
    
    @roles_required(['admin'])
    def delete(self, id):
        service = DepartmentsService()
        department = service.getDepartments(id)
        if not department:
            abort(404, message="Department does not exist")

        if department.employees:
            abort(400, message=f"Cannot delete department. It is still assigned to employees.")

        service.deleteDepartments(id)
        return {"message" : "Department deleted"}
    
    @roles_required(['admin'])
    @blueprint.arguments(DepartmentSchema)
    @blueprint.response(201, DepartmentSchema)
    def put(self, data, id):
        department_service = DepartmentsService()
        department = department_service.getDepartments(id)
        if not department:
            abort(404, message="Department does not exist")
        try :
            department = department_service.updateDepartments(id, data)
            new_department = DepartmentSchema().dump(department)
            return ResponseBuilder(data=new_department, status_code=200).build()
            
        except SQLAlchemyError:
                abort(500, message="Error while updating Department")
    
@blueprint.route("/departments")
class Department(MethodView):
    @roles_required(['admin'])
    @blueprint.response(200, DepartmentSchema)
    def get(self):
        department_service = DepartmentsService()
        department_list, total_departmentn = department_service.fetchAll()
        return ResponseBuilder(data=department_list, status_code=200, total=total_departmentn).build()

    @roles_required(['admin'])
    @blueprint.arguments(DepartmentSchema)
    @blueprint.response(200, DepartmentSchema)
    def post(self, data):
        try:
            service = DepartmentsService()
            department = service.getDepartmentsByKey({"name": data['name']})
            if not department:
                new_department = service.createDepartments(data)
            else:
                abort(400, message="Department already exist")
        except IntegrityError:
            abort(500, message="Error while creating Department")
        except SQLAlchemyError as e:
            print(e)
            abort(500, message="Error while creating Department")
        return new_department