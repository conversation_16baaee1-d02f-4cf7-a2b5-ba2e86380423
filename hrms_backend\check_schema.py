#!/usr/bin/env python3
"""
Check database schema
"""

import sys
import os

# Add the app directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'app'))

from sqlalchemy.orm import Session
from sqlalchemy import text, inspect
from app.db.session import SessionLocal, engine

def check_schema():
    """Check database schema"""
    db = SessionLocal()
    
    try:
        print("Checking database schema...")
        
        # Get table names
        inspector = inspect(engine)
        tables = inspector.get_table_names()
        
        print(f"\nTables found: {len(tables)}")
        for table in tables:
            print(f"  - {table}")
        
        # Check employees table specifically
        if 'employees' in tables:
            print("\nEmployees table columns:")
            columns = inspector.get_columns('employees')
            for col in columns:
                print(f"  - {col['name']} ({col['type']})")
        else:
            print("\n❌ Employees table not found!")
        
        # Check if there are any employees
        try:
            result = db.execute(text("SELECT COUNT(*) FROM employees"))
            count = result.scalar()
            print(f"\nEmployees count: {count}")
        except Exception as e:
            print(f"Error counting employees: {e}")
        
    except Exception as e:
        print(f"❌ Error checking schema: {e}")
        raise
    finally:
        db.close()

if __name__ == "__main__":
    check_schema()
