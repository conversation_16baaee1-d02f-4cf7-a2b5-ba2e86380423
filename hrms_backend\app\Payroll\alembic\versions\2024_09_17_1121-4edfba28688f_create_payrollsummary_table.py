"""Create payrollsummary table

Revision ID: 4edfba28688f
Revises: 8baee9f32a4f
Create Date: 2024-09-17 11:21:00.504253

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy import Column, Integer, String, Float
from sqlalchemy import func, DateTime, ForeignKey



# revision identifiers, used by Alembic.
revision: str = '4edfba28688f'
down_revision: Union[str, None] = '8baee9f32a4f'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    op.create_table(
        'payrollsummary',
        Column('id', sa.Integer, primary_key=True, autoincrement=True),
        Column('employee_id', Integer, ForeignKey('employees.id'), nullable=False),
        Column('gross_pay', Float, nullable=False),
        Column('tax_type', String(length=255), nullable=False),
        Column('monthly_tax', Float, nullable=False),
        Column('annual_tax', Float, nullable=False),
        Column('cra', sa.Float, nullable=False),
        Column('taxable_income', Float, nullable=False),
        Column('statutory_deductions', Float, nullable=False),
        Column('pay_after_tax_deduction', Float, nullable=False),
        Column('net_pay_sum', sa.Float, nullable=False),
        Column('total_earnings_after_tax_and_other_deductions', Float, nullable=False),
    )


def downgrade() -> None:
    op.drop_table('payrollsummary')
