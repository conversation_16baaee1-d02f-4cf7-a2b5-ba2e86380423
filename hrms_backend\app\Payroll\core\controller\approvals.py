from flask import url_for, jsonify
from flask.views import <PERSON><PERSON>iew
from flask_smorest import Blueprint, abort
import traceback
from schemas import ApprovalSchema
from sqlalchemy.orm.exc import NoResultFound
from sqlalchemy.exc import SQLAlchemyError, IntegrityError
from core.middleware import roles_required
import core.utils.response_message as RESPONSEMESSAGE
from core.services.approvals import ApprovalsService
from core.utils.responseBuilder import ResponseBuilder
from core.services.employee import EmployeeService

blueprint = Blueprint("Approvals", __name__, description="Operations for Approvals")
    
@blueprint.route("/approvals/<id>")
class Approval(MethodView):
    @roles_required(['admin'])
    @blueprint.response(200, ApprovalSchema)
    def get(self, id):
        service = ApprovalsService()
        approval = service.getApprovals(id)
        if not approval:
            abort(401, message="Approval does not exist")
        approval_details = ApprovalSchema().dump(approval)
        return ResponseBuilder(data=approval_details, status_code=200).build()       
    
    @roles_required(['admin'])
    def delete(self, id):
        service = ApprovalsService()
        approval = service.getApprovals(id)
        if not approval:
            abort(404, message="Approval does not exist")
        service.deleteApprovals(id)
        return {"message" : "Approval deleted"}
    
    @roles_required(['admin'])
    @blueprint.arguments(ApprovalSchema)
    @blueprint.response(201, ApprovalSchema)
    def put(self, data, id):
        service = ApprovalsService()
        approval = service.getApprovals(id)
        if not approval:
            abort(404, message="Approval does not exist")
        try :
            new_approval = service.updateApprovals(id, data)
            return new_approval
        except SQLAlchemyError:
                abort(500, message="Error while updating Approval")
    
@blueprint.route("/approvals")
class ApprovalList(MethodView):    
    @roles_required(['admin'])
    @blueprint.response(200, ApprovalSchema)
    def get(self):
        approval_service = ApprovalsService()
        approval_list, total_approval = approval_service.fetchAll()
        department_schema = ApprovalSchema(many=True)
        approval_list = department_schema.dump(approval_list)
        return ResponseBuilder(data=approval_list, status_code=200, total=total_approval).build()
    
    @roles_required(['admin'])
    @blueprint.arguments(ApprovalSchema)
    @blueprint.response(200, ApprovalSchema)
    def post(self, data):
        service = ApprovalsService()
        existing_approval = service.getApprovals(data["employee_id"])
        existing_level = service.getApprovalLevel(data['level'])

        if existing_level is not None:
            abort(400, message=f"Approval level {data['level']} already exists.")

        if existing_approval is not None:
            abort(400, message="Approval already exists")

        try:        
            employee_service = EmployeeService()        
            employee_data = employee_service.getEmployeeById(data["employee_id"])
            if employee_data is not None:
                first_name = getattr(employee_data, "first_name", "") or ""
                last_name = getattr(employee_data, "last_name", "") or ""
                email = getattr(employee_data, "email", "") or ""
                phone = getattr(employee_data, "phone", "") or ""
                full_name = f'{first_name.strip()} {last_name.strip()}'.strip()                    
                approval_data = {
                    "employee_id": data.get("employee_id", ""),
                    "level": data.get("level", ""),
                    "role": "Approver",
                    "name": full_name,
                    "email": email.strip() if isinstance(email, str) else "",
                    "phone": phone.strip() if isinstance(phone, str) else "",
                }
                new_approval = service.createApprovals(approval_data)
                employee_service.updateEmployee(data["employee_id"], {'role' : 'Approver'})
            return new_approval
        except Exception as e:
            abort(500, message="An unexpected error occurred")
        
        
