
from datetime import datetime
from core.databases.database import db
from core.models.basemodel import ModelBase



class LoanRepaymentModel(ModelBase):
    __tablename__ = "loan_repayment"

    id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    payroll_id = db.Column(db.<PERSON><PERSON><PERSON>, db.<PERSON>ey((ModelBase.dbSchema() + '.payroll_history.id')), nullable=False)
    loan_id = db.Column(db.Integer, db.ForeignKey((ModelBase.dbSchema() + '.loan_request.id')), nullable=False)
    amount = db.Column(db.Float, nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
