"""Change taxID column to BIGINT

Revision ID: 77c698a62cdd
Revises: 66cbaafa3968
Create Date: 2025-03-26 11:45:18.697768

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '77c698a62cdd'
down_revision: Union[str, None] = '66cbaafa3968'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # Alter the taxID column to BIGINT
    op.alter_column(
        'employees',
        'taxID',
        existing_type=sa.Integer(),
        type_=sa.BigInteger(),
        existing_nullable=True
    )

def downgrade() -> None:
    # Revert the taxID column back to Integer
    op.alter_column(
        'employees',
        'taxID',
        existing_type=sa.BigInteger(),
        type_=sa.Integer(),
        existing_nullable=True
    )