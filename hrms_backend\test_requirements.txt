# Testing Requirements for HRMS Backend
# Core FastAPI and database dependencies
fastapi>=0.104.1
uvicorn[standard]>=0.24.0
sqlalchemy>=2.0.23
psycopg2-binary>=2.9.9
alembic>=1.13.0

# Authentication and security
python-jose[cryptography]>=3.3.0
passlib[bcrypt]>=1.7.4
python-multipart>=0.0.6

# HTTP client for testing
requests>=2.31.0
httpx>=0.25.2

# WebSocket support
websockets>=12.0

# Data validation and serialization
pydantic>=2.5.0
pydantic-settings>=2.1.0

# Date and time handling
python-dateutil>=2.8.2

# Environment variables
python-dotenv>=1.0.0

# Logging and monitoring
structlog>=23.2.0

# Testing frameworks (optional)
pytest>=7.4.3
pytest-asyncio>=0.21.1
pytest-mock>=3.12.0

# Development tools (optional)
black>=23.11.0
flake8>=6.1.0
mypy>=1.7.1

# Additional utilities
click>=8.1.7
rich>=13.7.0
typer>=0.9.0
