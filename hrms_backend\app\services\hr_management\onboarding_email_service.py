"""
Advanced Email Service for Employee Onboarding
Handles welcome emails, task notifications, and completion emails
"""

import smtplib
import logging
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from email.mime.base import MIMEBase
from email import encoders
from typing import List, Dict, Optional, Any
from datetime import datetime, date
from jinja2 import Template

from ...core.config import settings
from ...db.models.employee import Employee
from ...db.models.onboarding import OnboardingWorkflow, OnboardingTask

logger = logging.getLogger(__name__)


class OnboardingEmailService:
    """Advanced email service for onboarding workflows"""
    
    def __init__(self):
        self.smtp_host = getattr(settings, 'SMTP_HOST', 'smtp.gmail.com')
        self.smtp_port = getattr(settings, 'SMTP_PORT', 587)
        self.smtp_username = getattr(settings, 'SMTP_USERNAME', '')
        self.smtp_password = getattr(settings, 'SMTP_PASSWORD', '')
        self.from_email = getattr(settings, 'FROM_EMAIL', self.smtp_username)
        self.company_name = getattr(settings, 'COMPANY_NAME', 'AgnoConnect')
        
    async def send_welcome_email(
        self,
        employee: Employee,
        workflow: OnboardingWorkflow,
        login_credentials: Optional[Dict[str, str]] = None
    ) -> bool:
        """Send welcome email to new employee"""
        try:
            subject = f"Welcome to {self.company_name}! 🎉"
            
            # Create personalized welcome email
            html_content = self._create_welcome_email_template(
                employee, workflow, login_credentials
            )
            
            return await self._send_email(
                to_email=employee.email,
                subject=subject,
                html_content=html_content,
                email_type="welcome"
            )
            
        except Exception as e:
            logger.error(f"Error sending welcome email to {employee.email}: {e}")
            return False
    
    async def send_onboarding_start_notification(
        self,
        employee: Employee,
        workflow: OnboardingWorkflow,
        tasks: List[OnboardingTask]
    ) -> bool:
        """Send onboarding start notification with task list"""
        try:
            subject = f"Your Onboarding Journey Begins - {workflow.title}"
            
            html_content = self._create_onboarding_start_template(
                employee, workflow, tasks
            )
            
            return await self._send_email(
                to_email=employee.email,
                subject=subject,
                html_content=html_content,
                email_type="onboarding_start"
            )
            
        except Exception as e:
            logger.error(f"Error sending onboarding start email: {e}")
            return False
    
    async def send_task_reminder_email(
        self,
        employee: Employee,
        task: OnboardingTask,
        workflow: OnboardingWorkflow
    ) -> bool:
        """Send task reminder email"""
        try:
            subject = f"Reminder: {task.title} - Onboarding Task"
            
            html_content = self._create_task_reminder_template(
                employee, task, workflow
            )
            
            return await self._send_email(
                to_email=employee.email,
                subject=subject,
                html_content=html_content,
                email_type="task_reminder"
            )
            
        except Exception as e:
            logger.error(f"Error sending task reminder email: {e}")
            return False
    
    async def send_completion_email(
        self,
        employee: Employee,
        workflow: OnboardingWorkflow
    ) -> bool:
        """Send onboarding completion email"""
        try:
            subject = f"🎉 Congratulations! Onboarding Complete - Welcome to the Team!"
            
            html_content = self._create_completion_email_template(
                employee, workflow
            )
            
            return await self._send_email(
                to_email=employee.email,
                subject=subject,
                html_content=html_content,
                email_type="completion"
            )
            
        except Exception as e:
            logger.error(f"Error sending completion email: {e}")
            return False
    
    async def send_hr_notification(
        self,
        hr_email: str,
        employee: Employee,
        workflow: OnboardingWorkflow,
        notification_type: str
    ) -> bool:
        """Send notification to HR team"""
        try:
            subject_map = {
                "new_employee": f"New Employee Onboarding Started - {employee.full_name}",
                "task_overdue": f"Onboarding Task Overdue - {employee.full_name}",
                "completion": f"Onboarding Completed - {employee.full_name}",
                "assistance_needed": f"Onboarding Assistance Needed - {employee.full_name}"
            }
            
            subject = subject_map.get(notification_type, "Onboarding Notification")
            
            html_content = self._create_hr_notification_template(
                employee, workflow, notification_type
            )
            
            return await self._send_email(
                to_email=hr_email,
                subject=subject,
                html_content=html_content,
                email_type="hr_notification"
            )
            
        except Exception as e:
            logger.error(f"Error sending HR notification: {e}")
            return False
    
    def _create_welcome_email_template(
        self,
        employee: Employee,
        workflow: OnboardingWorkflow,
        login_credentials: Optional[Dict[str, str]] = None
    ) -> str:
        """Create welcome email HTML template"""
        
        credentials_section = ""
        if login_credentials:
            credentials_section = f"""
            <div style="background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;">
                <h3 style="color: #073763; margin-bottom: 15px;">🔐 Your Login Credentials</h3>
                <p><strong>Email:</strong> {login_credentials.get('email', employee.email)}</p>
                <p><strong>Temporary Password:</strong> {login_credentials.get('password', 'Please contact HR')}</p>
                <p style="color: #dc3545; font-size: 14px;">
                    <em>Please change your password after first login for security.</em>
                </p>
            </div>
            """
        
        template = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="utf-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Welcome to {self.company_name}</title>
        </head>
        <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;">
            
            <!-- Header -->
            <div style="background: linear-gradient(135deg, #073763 0%, #0B2A5A 100%); color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0;">
                <h1 style="margin: 0; font-size: 28px;">Welcome to {self.company_name}! 🎉</h1>
                <p style="margin: 10px 0 0 0; font-size: 16px; opacity: 0.9;">We're excited to have you on board!</p>
            </div>
            
            <!-- Content -->
            <div style="background: white; padding: 30px; border: 1px solid #e0e0e0; border-top: none;">
                <h2 style="color: #073763; margin-bottom: 20px;">Dear {employee.first_name},</h2>
                
                <p>Welcome to the {self.company_name} family! We are thrilled to have you join our team as a <strong>{employee.designation.title if employee.designation else 'Team Member'}</strong>.</p>
                
                <p>Your onboarding journey has been carefully designed to help you settle in smoothly and get up to speed with our company culture, processes, and your role.</p>
                
                {credentials_section}
                
                <div style="background-color: #e8f4fd; padding: 20px; border-radius: 8px; margin: 20px 0;">
                    <h3 style="color: #073763; margin-bottom: 15px;">📋 Your Onboarding Details</h3>
                    <p><strong>Start Date:</strong> {workflow.start_date.strftime('%B %d, %Y')}</p>
                    <p><strong>Expected Completion:</strong> {workflow.expected_completion_date.strftime('%B %d, %Y') if workflow.expected_completion_date else 'TBD'}</p>
                    <p><strong>Department:</strong> {employee.department.name if employee.department else 'TBD'}</p>
                    <p><strong>Manager:</strong> {employee.manager.full_name if employee.manager else 'TBD'}</p>
                </div>
                
                <div style="background-color: #fff3cd; padding: 20px; border-radius: 8px; margin: 20px 0;">
                    <h3 style="color: #856404; margin-bottom: 15px;">🚀 What's Next?</h3>
                    <ul style="margin: 0; padding-left: 20px;">
                        <li>Check your email regularly for onboarding tasks and updates</li>
                        <li>Complete your profile information in the HR system</li>
                        <li>Attend your scheduled orientation sessions</li>
                        <li>Meet with your assigned buddy/mentor</li>
                        <li>Don't hesitate to ask questions - we're here to help!</li>
                    </ul>
                </div>
                
                <div style="text-align: center; margin: 30px 0;">
                    <a href="http://localhost:5173" style="background-color: #F47C20; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; font-weight: bold; display: inline-block;">
                        Access HR Portal
                    </a>
                </div>
                
                <p>If you have any questions or need assistance, please don't hesitate to reach out to our HR team or your manager.</p>
                
                <p>Once again, welcome to {self.company_name}! We look forward to working with you and seeing all the great things you'll accomplish here.</p>
                
                <p style="margin-top: 30px;">
                    Best regards,<br>
                    <strong>The {self.company_name} HR Team</strong>
                </p>
            </div>
            
            <!-- Footer -->
            <div style="background-color: #f8f9fa; padding: 20px; text-align: center; border-radius: 0 0 10px 10px; border: 1px solid #e0e0e0; border-top: none;">
                <p style="margin: 0; font-size: 14px; color: #6c757d;">
                    This is an automated message from {self.company_name} HR System.<br>
                    Please do not reply to this email.
                </p>
            </div>
        </body>
        </html>
        """
        
        return template
    
    async def _send_email(
        self,
        to_email: str,
        subject: str,
        html_content: str,
        email_type: str = "general"
    ) -> bool:
        """Send email using SMTP"""
        try:
            if not self.smtp_username or not self.smtp_password:
                logger.warning("SMTP credentials not configured")
                return False
            
            # Create message
            msg = MIMEMultipart('alternative')
            msg['Subject'] = subject
            msg['From'] = f"{self.company_name} HR <{self.from_email}>"
            msg['To'] = to_email
            
            # Attach HTML content
            html_part = MIMEText(html_content, 'html')
            msg.attach(html_part)
            
            # Send email
            server = smtplib.SMTP(self.smtp_host, self.smtp_port)
            server.starttls()
            server.login(self.smtp_username, self.smtp_password)
            text = msg.as_string()
            server.sendmail(self.from_email, [to_email], text)
            server.quit()
            
            logger.info(f"Email sent successfully to {to_email} (type: {email_type})")
            return True
            
        except Exception as e:
            logger.error(f"Error sending email to {to_email}: {e}")
            return False

    def _create_onboarding_start_template(
        self,
        employee: Employee,
        workflow: OnboardingWorkflow,
        tasks: List[OnboardingTask]
    ) -> str:
        """Create onboarding start email template"""

        tasks_html = ""
        if tasks:
            tasks_html = "<ul style='margin: 0; padding-left: 20px;'>"
            for task in tasks[:5]:  # Show first 5 tasks
                tasks_html += f"<li style='margin-bottom: 8px;'><strong>{task.title}</strong>"
                if task.description:
                    tasks_html += f" - {task.description[:100]}{'...' if len(task.description) > 100 else ''}"
                tasks_html += "</li>"
            if len(tasks) > 5:
                tasks_html += f"<li style='color: #6c757d; font-style: italic;'>... and {len(tasks) - 5} more tasks</li>"
            tasks_html += "</ul>"

        return f"""
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="utf-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Your Onboarding Journey Begins</title>
        </head>
        <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;">

            <div style="background: linear-gradient(135deg, #073763 0%, #0B2A5A 100%); color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0;">
                <h1 style="margin: 0; font-size: 24px;">🚀 Your Onboarding Journey Begins!</h1>
                <p style="margin: 10px 0 0 0; font-size: 16px; opacity: 0.9;">{workflow.title}</p>
            </div>

            <div style="background: white; padding: 30px; border: 1px solid #e0e0e0; border-top: none;">
                <h2 style="color: #073763; margin-bottom: 20px;">Hello {employee.first_name}!</h2>

                <p>Your onboarding process has officially started! We've prepared a comprehensive journey to help you integrate smoothly into our team.</p>

                <div style="background-color: #e8f4fd; padding: 20px; border-radius: 8px; margin: 20px 0;">
                    <h3 style="color: #073763; margin-bottom: 15px;">📋 Your Upcoming Tasks</h3>
                    {tasks_html}
                </div>

                <div style="background-color: #d4edda; padding: 20px; border-radius: 8px; margin: 20px 0;">
                    <h3 style="color: #155724; margin-bottom: 15px;">💡 Tips for Success</h3>
                    <ul style="margin: 0; padding-left: 20px;">
                        <li>Complete tasks in the suggested order</li>
                        <li>Don't hesitate to ask questions</li>
                        <li>Take notes during training sessions</li>
                        <li>Connect with your colleagues</li>
                        <li>Provide feedback on your experience</li>
                    </ul>
                </div>

                <div style="text-align: center; margin: 30px 0;">
                    <a href="http://localhost:5173" style="background-color: #F47C20; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; font-weight: bold; display: inline-block;">
                        View Your Tasks
                    </a>
                </div>

                <p>We're here to support you every step of the way. Welcome to the team!</p>

                <p style="margin-top: 30px;">
                    Best regards,<br>
                    <strong>The {self.company_name} Team</strong>
                </p>
            </div>

            <div style="background-color: #f8f9fa; padding: 20px; text-align: center; border-radius: 0 0 10px 10px; border: 1px solid #e0e0e0; border-top: none;">
                <p style="margin: 0; font-size: 14px; color: #6c757d;">
                    This is an automated message from {self.company_name} HR System.
                </p>
            </div>
        </body>
        </html>
        """

    def _create_task_reminder_template(
        self,
        employee: Employee,
        task: OnboardingTask,
        workflow: OnboardingWorkflow
    ) -> str:
        """Create task reminder email template"""

        return f"""
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="utf-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Task Reminder</title>
        </head>
        <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;">

            <div style="background: linear-gradient(135deg, #F47C20 0%, #ff8c42 100%); color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0;">
                <h1 style="margin: 0; font-size: 24px;">⏰ Task Reminder</h1>
                <p style="margin: 10px 0 0 0; font-size: 16px; opacity: 0.9;">Don't forget to complete your onboarding task</p>
            </div>

            <div style="background: white; padding: 30px; border: 1px solid #e0e0e0; border-top: none;">
                <h2 style="color: #073763; margin-bottom: 20px;">Hi {employee.first_name},</h2>

                <p>This is a friendly reminder about your pending onboarding task:</p>

                <div style="background-color: #fff3cd; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #F47C20;">
                    <h3 style="color: #856404; margin-bottom: 15px; margin-top: 0;">📝 {task.title}</h3>
                    {f'<p style="margin-bottom: 10px;">{task.description}</p>' if task.description else ''}
                    {f'<p style="margin-bottom: 0;"><strong>Due Date:</strong> {task.due_date.strftime("%B %d, %Y")}</p>' if task.due_date else ''}
                </div>

                <div style="text-align: center; margin: 30px 0;">
                    <a href="http://localhost:5173" style="background-color: #F47C20; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; font-weight: bold; display: inline-block;">
                        Complete Task
                    </a>
                </div>

                <p>If you need any assistance with this task, please don't hesitate to reach out to your manager or HR team.</p>

                <p style="margin-top: 30px;">
                    Best regards,<br>
                    <strong>The {self.company_name} Team</strong>
                </p>
            </div>

            <div style="background-color: #f8f9fa; padding: 20px; text-align: center; border-radius: 0 0 10px 10px; border: 1px solid #e0e0e0; border-top: none;">
                <p style="margin: 0; font-size: 14px; color: #6c757d;">
                    This is an automated reminder from {self.company_name} HR System.
                </p>
            </div>
        </body>
        </html>
        """

    def _create_completion_email_template(
        self,
        employee: Employee,
        workflow: OnboardingWorkflow
    ) -> str:
        """Create completion email template"""

        return f"""
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="utf-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Onboarding Complete!</title>
        </head>
        <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;">

            <div style="background: linear-gradient(135deg, #28a745 0%, #20c997 100%); color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0;">
                <h1 style="margin: 0; font-size: 28px;">🎉 Congratulations!</h1>
                <p style="margin: 10px 0 0 0; font-size: 18px; opacity: 0.9;">You've completed your onboarding!</p>
            </div>

            <div style="background: white; padding: 30px; border: 1px solid #e0e0e0; border-top: none;">
                <h2 style="color: #073763; margin-bottom: 20px;">Dear {employee.first_name},</h2>

                <p>Congratulations on successfully completing your onboarding journey at {self.company_name}! 🎊</p>

                <p>You've shown great dedication and enthusiasm throughout the process, and we're excited to see you fully integrated into our team.</p>

                <div style="background-color: #d4edda; padding: 20px; border-radius: 8px; margin: 20px 0;">
                    <h3 style="color: #155724; margin-bottom: 15px;">✅ What You've Accomplished</h3>
                    <ul style="margin: 0; padding-left: 20px;">
                        <li>Completed all required onboarding tasks</li>
                        <li>Learned about our company culture and values</li>
                        <li>Set up your workspace and accounts</li>
                        <li>Met your team members and key contacts</li>
                        <li>Received necessary training and resources</li>
                    </ul>
                </div>

                <div style="background-color: #e8f4fd; padding: 20px; border-radius: 8px; margin: 20px 0;">
                    <h3 style="color: #073763; margin-bottom: 15px;">🚀 What's Next?</h3>
                    <ul style="margin: 0; padding-left: 20px;">
                        <li>Start working on your assigned projects</li>
                        <li>Continue building relationships with colleagues</li>
                        <li>Attend regular team meetings and updates</li>
                        <li>Participate in ongoing learning opportunities</li>
                        <li>Provide feedback on your onboarding experience</li>
                    </ul>
                </div>

                <div style="text-align: center; margin: 30px 0;">
                    <a href="http://localhost:5173" style="background-color: #F47C20; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; font-weight: bold; display: inline-block;">
                        Access Your Dashboard
                    </a>
                </div>

                <p>Remember, our doors are always open if you have questions or need support. We're here to help you succeed!</p>

                <p>Welcome to the {self.company_name} family! We're thrilled to have you on board and look forward to all the great things we'll accomplish together.</p>

                <p style="margin-top: 30px;">
                    Warmest congratulations,<br>
                    <strong>The {self.company_name} Team</strong>
                </p>
            </div>

            <div style="background-color: #f8f9fa; padding: 20px; text-align: center; border-radius: 0 0 10px 10px; border: 1px solid #e0e0e0; border-top: none;">
                <p style="margin: 0; font-size: 14px; color: #6c757d;">
                    This is an automated message from {self.company_name} HR System.
                </p>
            </div>
        </body>
        </html>
        """

    def _create_hr_notification_template(
        self,
        employee: Employee,
        workflow: OnboardingWorkflow,
        notification_type: str
    ) -> str:
        """Create HR notification email template"""

        content_map = {
            "new_employee": f"A new employee onboarding process has been started for {employee.full_name}.",
            "task_overdue": f"Some onboarding tasks are overdue for {employee.full_name}. Please follow up.",
            "completion": f"{employee.full_name} has successfully completed their onboarding process!",
            "assistance_needed": f"{employee.full_name} may need assistance with their onboarding process."
        }

        content = content_map.get(notification_type, "Onboarding notification")

        return f"""
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="utf-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>HR Notification</title>
        </head>
        <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;">

            <div style="background: linear-gradient(135deg, #073763 0%, #0B2A5A 100%); color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0;">
                <h1 style="margin: 0; font-size: 24px;">📋 HR Notification</h1>
                <p style="margin: 10px 0 0 0; font-size: 16px; opacity: 0.9;">Onboarding Update</p>
            </div>

            <div style="background: white; padding: 30px; border: 1px solid #e0e0e0; border-top: none;">
                <h2 style="color: #073763; margin-bottom: 20px;">HR Team,</h2>

                <p>{content}</p>

                <div style="background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;">
                    <h3 style="color: #073763; margin-bottom: 15px;">Employee Details</h3>
                    <p><strong>Name:</strong> {employee.full_name}</p>
                    <p><strong>Employee ID:</strong> {employee.employee_id}</p>
                    <p><strong>Email:</strong> {employee.email}</p>
                    <p><strong>Department:</strong> {employee.department.name if employee.department else 'TBD'}</p>
                    <p><strong>Start Date:</strong> {workflow.start_date.strftime('%B %d, %Y')}</p>
                    <p><strong>Progress:</strong> {workflow.progress_percentage}% complete</p>
                </div>

                <div style="text-align: center; margin: 30px 0;">
                    <a href="http://localhost:5173" style="background-color: #F47C20; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; font-weight: bold; display: inline-block;">
                        View Onboarding Details
                    </a>
                </div>

                <p style="margin-top: 30px;">
                    Best regards,<br>
                    <strong>{self.company_name} HR System</strong>
                </p>
            </div>

            <div style="background-color: #f8f9fa; padding: 20px; text-align: center; border-radius: 0 0 10px 10px; border: 1px solid #e0e0e0; border-top: none;">
                <p style="margin: 0; font-size: 14px; color: #6c757d;">
                    This is an automated notification from {self.company_name} HR System.
                </p>
            </div>
        </body>
        </html>
        """
