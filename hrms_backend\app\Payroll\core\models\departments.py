from core.databases.database import db
from core.models.basemodel import ModelBase
from sqlalchemy.orm import relationship
from core.models.employees import EmployeeModel
from datetime import datetime

class DepartmentModel(ModelBase):
    __tablename__ = "departments"

    id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    name = db.Column(db.String(45), unique=True, nullable=False)
    user_id = db.Column(db.Integer, db.ForeignKey((ModelBase.dbSchema() + '.users.id')), nullable=False) 
    owner = db.relationship('UserModel', backref='departments', lazy=True)
    timestamp = db.Column(db.DateTime, nullable=False, default=datetime.now())
    employees = db.relationship('EmployeeModel', backref='department', cascade='all, delete-orphan')