"""Create Prorate table

Revision ID: f102088bfe88
Revises: be257dc32d05
Create Date: 2024-10-24 17:02:21.439653

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy import Column, Integer, String, Float
from sqlalchemy import func, DateTime, ForeignKey
from sqlalchemy.dialects.postgresql import BOOLEAN


# revision identifiers, used by Alembic.
revision: str = 'f102088bfe88'
down_revision: Union[str, None] = 'be257dc32d05'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None



def upgrade() -> None:
    op.create_table(
        'prorate_salary',
        Column('id', Integer, primary_key=True, autoincrement=True),
        Column('first_name', String(length=45), nullable=True),
        Column('last_name', String(length=45), nullable=True),
        Column('email', String(length=100), nullable=True),
        Column('is_prorated', BOOLEAN, nullable=True, default=True),
        Column('gross_pay', Float, nullable=True),
        Column('monthly_tax', Float, nullable=True),
        Column('annual_tax', Float, nullable=True),
        Column('total_taxable_monthly_sum', Float, nullable=True),
        Column('total_taxable_annual_sum', Float, nullable=True),
        Column('total_non_taxable_monthly_sum', Float, nullable=True),
        Column('total_non_taxable_annual_sum', Float, nullable=True),
        Column('total_statutory_monthly_sum', Float, nullable=True),
        Column('total_statutory_annual_sum', Float, nullable=True),
        Column('total_other_deductions_monthly_sum', Float, nullable=True),
        Column('total_other_deductions_annual_sum', Float, nullable=True),
        Column('netpay', Float, nullable=True),
        Column('user_id', Integer, ForeignKey('users.id'), nullable=True),
        Column('employee_id', Integer, ForeignKey('employees.id')),
        Column('timestamp', DateTime, nullable=False, server_default=sa.func.now()),
    )


def downgrade() -> None:
    op.drop_table('prorate_salary')