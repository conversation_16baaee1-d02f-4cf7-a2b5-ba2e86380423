"""add email configuration table

Revision ID: 5821ba0d074e
Revises: 883eb9e88818
Create Date: 2025-05-26 15:30:05.778834

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '5821ba0d074e'
down_revision: Union[str, None] = '883eb9e88818'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    op.create_table('email_configurations',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('user_id', sa.Integer(), nullable=False),
        sa.Column('smtp_server', sa.String(length=255), nullable=False),
        sa.Column('smtp_port', sa.Integer(), nullable=False),
        sa.Column('username', sa.String(length=255), nullable=False),
        sa.Column('password', sa.String(length=255), nullable=False),
        sa.Column('use_ssl', sa.<PERSON>(), server_default='false', nullable=False),
        sa.Column('use_tls', sa.Boolean(), server_default='false', nullable=False),
        sa.Column('sender_email', sa.String(length=255), nullable=False),
        sa.Column('is_active', sa.Boolean(), server_default='true', nullable=False),
        sa.Column('created_at', sa.DateTime(), server_default=sa.text('now()'), nullable=False),
        sa.Column('updated_at', sa.DateTime(), server_default=sa.text('now()'), nullable=False),
        sa.ForeignKeyConstraint(['user_id'], ['users.id'], ondelete='CASCADE'),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_email_configurations_user_id'), 'email_configurations', ['user_id'], unique=False)


def downgrade():
    op.drop_index(op.f('ix_email_configurations_user_id'), table_name='email_configurations')
    op.drop_table('email_configurations')
