/**
 * Projects Management Page with RBAC Integration
 * Displays project kanban boards, project list, and project management with role-based access control
 */

import React, { useState } from 'react';
import { Plus, Filter, Search, Calendar, Users, MoreVertical, Eye, Edit } from 'lucide-react';
import { usePermissions } from '../hooks/usePermissions';
import { PermissionGate, ConditionalRender } from '../components/ProtectedRoute';

export default function Projects({ activeTab = 'kanban' }) {
  const permissions = usePermissions();
  const [selectedProject, setSelectedProject] = useState('all');
  const [searchTerm, setSearchTerm] = useState('');

  // Mock project data
  const projects = [
    {
      id: 1,
      name: 'HRMS Development',
      description: 'Building a comprehensive HR management system',
      status: 'in-progress',
      progress: 75,
      startDate: '2024-01-01',
      endDate: '2024-06-30',
      teamMembers: ['<PERSON>', '<PERSON>', '<PERSON>'],
      tasksTotal: 45,
      tasksCompleted: 34
    },
    {
      id: 2,
      name: 'Mobile App',
      description: 'Developing mobile application for employee self-service',
      status: 'planning',
      progress: 25,
      startDate: '2024-02-01',
      endDate: '2024-08-31',
      teamMembers: ['<PERSON>', 'Bob Wilson'],
      tasksTotal: 32,
      tasksCompleted: 8
    },
    {
      id: 3,
      name: 'Data Migration',
      description: 'Migrating legacy data to new system',
      status: 'completed',
      progress: 100,
      startDate: '2023-11-01',
      endDate: '2024-01-15',
      teamMembers: ['Charlie Davis', 'Diana Evans'],
      tasksTotal: 28,
      tasksCompleted: 28
    }
  ];

  // Mock kanban data
  const kanbanColumns = [
    {
      id: 'todo',
      title: 'To Do',
      tasks: [
        { id: 1, title: 'Design user interface', project: 'HRMS Development', assignee: 'John Doe', priority: 'high' },
        { id: 2, title: 'Setup database schema', project: 'HRMS Development', assignee: 'Jane Smith', priority: 'medium' },
        { id: 3, title: 'Create API endpoints', project: 'Mobile App', assignee: 'Alice Brown', priority: 'high' }
      ]
    },
    {
      id: 'in-progress',
      title: 'In Progress',
      tasks: [
        { id: 4, title: 'Implement authentication', project: 'HRMS Development', assignee: 'Mike Johnson', priority: 'high' },
        { id: 5, title: 'Design mobile screens', project: 'Mobile App', assignee: 'Bob Wilson', priority: 'medium' }
      ]
    },
    {
      id: 'review',
      title: 'Review',
      tasks: [
        { id: 6, title: 'Test user registration', project: 'HRMS Development', assignee: 'John Doe', priority: 'low' }
      ]
    },
    {
      id: 'done',
      title: 'Done',
      tasks: [
        { id: 7, title: 'Project setup', project: 'HRMS Development', assignee: 'Jane Smith', priority: 'medium' },
        { id: 8, title: 'Requirements gathering', project: 'Mobile App', assignee: 'Alice Brown', priority: 'high' }
      ]
    }
  ];

  const getStatusColor = (status) => {
    switch (status) {
      case 'completed': return 'text-green-600 bg-green-100';
      case 'in-progress': return 'text-blue-600 bg-blue-100';
      case 'planning': return 'text-yellow-600 bg-yellow-100';
      case 'on-hold': return 'text-red-600 bg-red-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const getPriorityColor = (priority) => {
    switch (priority) {
      case 'high': return 'border-l-red-500';
      case 'medium': return 'border-l-yellow-500';
      case 'low': return 'border-l-green-500';
      default: return 'border-l-gray-500';
    }
  };

  const renderKanbanBoard = () => (
    <div className="space-y-6">
      {/* Kanban Header */}
      <div className="flex justify-between items-center">
        <div className="flex space-x-4">
          <select 
            value={selectedProject} 
            onChange={(e) => setSelectedProject(e.target.value)}
            className="border border-gray-300 rounded-md px-3 py-2"
          >
            <option value="all">All Projects</option>
            {projects.map(project => (
              <option key={project.id} value={project.id}>{project.name}</option>
            ))}
          </select>
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={16} />
            <input
              type="text"
              placeholder="Search tasks..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 pr-4 py-2 border border-gray-300 rounded-md"
            />
          </div>
        </div>
        <PermissionGate permission="projectKanbanBoards">
          <button className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 flex items-center space-x-2">
            <Plus size={16} />
            <span>Add Task</span>
          </button>
        </PermissionGate>
      </div>

      {/* Kanban Board */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {kanbanColumns.map((column) => (
          <div key={column.id} className="bg-gray-50 rounded-lg p-4">
            <div className="flex justify-between items-center mb-4">
              <h3 className="font-medium text-gray-900">{column.title}</h3>
              <span className="bg-gray-200 text-gray-600 text-xs px-2 py-1 rounded-full">
                {column.tasks.length}
              </span>
            </div>
            <div className="space-y-3">
              {column.tasks.map((task) => (
                <div key={task.id} className={`bg-white rounded-lg p-3 shadow-sm border-l-4 ${getPriorityColor(task.priority)}`}>
                  <h4 className="text-sm font-medium text-gray-900 mb-2">{task.title}</h4>
                  <div className="text-xs text-gray-500 mb-2">{task.project}</div>
                  <div className="flex justify-between items-center">
                    <span className="text-xs text-gray-600">{task.assignee}</span>
                    <span className={`text-xs px-2 py-1 rounded ${
                      task.priority === 'high' ? 'bg-red-100 text-red-600' :
                      task.priority === 'medium' ? 'bg-yellow-100 text-yellow-600' :
                      'bg-green-100 text-green-600'
                    }`}>
                      {task.priority}
                    </span>
                  </div>
                </div>
              ))}
            </div>
          </div>
        ))}
      </div>
    </div>
  );

  const renderProjectList = () => (
    <div className="space-y-6">
      {/* Project Actions */}
      <div className="flex justify-between items-center">
        <div className="flex space-x-4">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={16} />
            <input
              type="text"
              placeholder="Search projects..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 pr-4 py-2 border border-gray-300 rounded-md"
            />
          </div>
          <select className="border border-gray-300 rounded-md px-3 py-2">
            <option value="all">All Status</option>
            <option value="planning">Planning</option>
            <option value="in-progress">In Progress</option>
            <option value="completed">Completed</option>
          </select>
        </div>
        <PermissionGate permission="projectKanbanBoards">
          <button className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 flex items-center space-x-2">
            <Plus size={16} />
            <span>New Project</span>
          </button>
        </PermissionGate>
      </div>

      {/* Projects Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {projects.map((project) => (
          <div key={project.id} className="bg-white rounded-lg shadow p-6">
            <div className="flex justify-between items-start mb-4">
              <h3 className="text-lg font-medium text-gray-900">{project.name}</h3>
              <button className="text-gray-400 hover:text-gray-600">
                <MoreVertical size={16} />
              </button>
            </div>
            
            <p className="text-gray-600 text-sm mb-4">{project.description}</p>
            
            <div className="space-y-3">
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-500">Status</span>
                <span className={`inline-flex px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(project.status)}`}>
                  {project.status.replace('-', ' ')}
                </span>
              </div>
              
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-500">Progress</span>
                <span className="text-sm font-medium">{project.progress}%</span>
              </div>
              
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div 
                  className="bg-blue-600 h-2 rounded-full" 
                  style={{ width: `${project.progress}%` }}
                ></div>
              </div>
              
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-500">Tasks</span>
                <span className="text-sm font-medium">{project.tasksCompleted}/{project.tasksTotal}</span>
              </div>
              
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-500">Team</span>
                <div className="flex -space-x-2">
                  {project.teamMembers.slice(0, 3).map((member, index) => (
                    <div key={index} className="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center text-white text-xs">
                      {member.split(' ').map(n => n[0]).join('')}
                    </div>
                  ))}
                  {project.teamMembers.length > 3 && (
                    <div className="w-6 h-6 bg-gray-500 rounded-full flex items-center justify-center text-white text-xs">
                      +{project.teamMembers.length - 3}
                    </div>
                  )}
                </div>
              </div>
              
              <div className="flex justify-between items-center pt-2">
                <span className="text-xs text-gray-500">{project.startDate} - {project.endDate}</span>
                <div className="flex space-x-2">
                  <button className="text-blue-600 hover:text-blue-900">
                    <Eye size={16} />
                  </button>
                  <button className="text-gray-600 hover:text-gray-900">
                    <Edit size={16} />
                  </button>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );

  const renderContent = () => {
    switch (activeTab) {
      case 'kanban':
        return renderKanbanBoard();
      case 'projects':
        return renderProjectList();
      default:
        return renderKanbanBoard();
    }
  };

  return (
    <div className="p-6">
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-gray-900">Project Management</h1>
        <p className="text-gray-600">Manage projects and track progress with kanban boards</p>
      </div>

      {renderContent()}
    </div>
  );
}
