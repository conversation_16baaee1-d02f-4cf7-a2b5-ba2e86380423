#!/usr/bin/env python3
"""
Check what enum values are available in the database
"""

import sys
import os

# Add the app directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'app'))

from sqlalchemy.orm import Session
from sqlalchemy import text
from app.db.session import SessionLocal

def check_enum_values():
    """Check what enum values are available"""
    db = SessionLocal()
    
    try:
        print("Checking enum values in database...")
        
        # Get enum values for role type
        result = db.execute(text("""
            SELECT enumlabel 
            FROM pg_enum 
            WHERE enumtypid = (
                SELECT oid 
                FROM pg_type 
                WHERE typname = 'role'
            )
            ORDER BY enumsortorder;
        """))
        
        role_values = result.fetchall()
        
        if role_values:
            print("\nAvailable role enum values:")
            for value in role_values:
                print(f"  - {value[0]}")
        else:
            print("No role enum found")
            
        # Check if there are any existing users
        result = db.execute(text("SELECT COUNT(*) FROM users"))
        user_count = result.scalar()
        print(f"\nExisting users count: {user_count}")
        
        if user_count > 0:
            result = db.execute(text("SELECT email, role FROM users LIMIT 5"))
            users = result.fetchall()
            print("\nExisting users:")
            for user in users:
                print(f"  {user[0]} - {user[1]}")
        
    except Exception as e:
        print(f"❌ Error checking enum values: {e}")
    finally:
        db.close()

if __name__ == "__main__":
    check_enum_values()
