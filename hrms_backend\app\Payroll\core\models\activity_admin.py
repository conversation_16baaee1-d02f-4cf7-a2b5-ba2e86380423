from core.databases.database import db
from core.models.basemodel import ModelBase
from sqlalchemy.orm import relationship
from core.models.employees import EmployeeModel
from datetime import datetime
from core.utils.helper import get_nigeria_time


class AdminActivityModel(ModelBase):
    __tablename__ = "admin_activities"

    id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    message = db.Column(db.String(100), nullable=True)
    user_id = db.Column(db.Integer, db.ForeignKey((ModelBase.dbSchema() + '.users.id')), nullable=False) 
    owner = db.relationship('UserModel', backref='admin_activities', lazy=True)
    created_at = db.Column(db.DateTime(timezone=True), nullable=False, default=get_nigeria_time)
    updated_at = db.Column(db.DateTime(timezone=True), nullable=False, default=get_nigeria_time, onupdate=get_nigeria_time)
