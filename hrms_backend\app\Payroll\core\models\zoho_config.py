from core.databases.database import db
from core.models.basemodel import ModelBase
from core.utils.helper import get_nigeria_time

class ZohoConfig(ModelBase):
    __tablename__ = "zoho_people_integration"

    id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    user_id = db.Column(db.Integer, db.ForeignKey((ModelBase.dbSchema() + '.users.id')), nullable=False) 
    authorization_employees_code = db.Column(db.String(250), nullable=False)
    client_id = db.Column(db.String(250), nullable=False)
    client_secret = db.Column(db.String(250), nullable=False)
    access_token= db.Column(db.String(500), nullable=False)
    refresh_token = db.Column(db.String(500), nullable=False)
    org_id = db.Column(db.String(100), nullable=True, index=True)
    is_enabled = db.Column(db.<PERSON><PERSON>, nullable=False, default=False)
    token_expires_at = db.Column(db.String(250), nullable=True)
    timestamp = db.Column(db.DateTime, nullable=False, default=get_nigeria_time)
    last_sync = db.Column(db.DateTime, nullable=True)
