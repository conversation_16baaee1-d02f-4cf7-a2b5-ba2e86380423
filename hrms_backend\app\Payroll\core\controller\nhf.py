from flask import url_for
from flask.views import MethodView
from flask_smorest import Blueprint, abort
from core.middleware import roles_required
from schemas import NhfSchema
from sqlalchemy.orm.exc import NoResultFound
from sqlalchemy.exc import SQLAlchemyError, IntegrityError

import core.utils.response_message as RESPONSEMESSAGE
from core.services.nhf import NhfService

blueprint = Blueprint("Nhf", __name__, description="Operations for Nhf")
    
@blueprint.route("/nhf/<id>")
class NhfList(MethodView):
    @roles_required(['admin'])
    @blueprint.response(200, NhfSchema)
    def get(self, id):
        service = NhfService()
        nhf = service.getNhf(id)
        if not nhf:
            abort(401, message="Nhf does not exist")
        return nhf    
    
    @roles_required(['admin'])
    def delete(self, id):
        service = NhfService()
        nhf = service.getNhf(id)
        if not nhf:
            abort(404, message="Nhf does not exist")
        service.deleteNhf(id)
        return {"message" : "Nhf deleted"}
    
    @roles_required(['admin'])
    @blueprint.arguments(NhfSchema)
    @blueprint.response(201, NhfSchema)
    def put(self, id, data):
        service = NhfService()
        nhf = service.getNhf(id)
        if not nhf:
            abort(404, message="Nhf does not exist")
        try :
            new_nhf = service.updateNhf(id, data)
            return new_nhf
        except SQLAlchemyError:
                abort(500, message="Error while updating Nhf")
    
@blueprint.route("/nhf")
class Nhf(MethodView):
    @roles_required(['admin'])
    @blueprint.arguments(NhfSchema)
    @blueprint.response(200, NhfSchema)
    def post(self, data):
        try:
            service = NhfService()
            nhf = service.getNhfByKey({"id": data['id']})
            if not nhf:
                new_nhf = service.createNhf(data)
            else:
                abort(400, message="Nhf already exist")
        except IntegrityError:
            abort(500, message="Error while creating Nhf")
        except SQLAlchemyError as e:
            print(e)
            abort(500, message="Error while creating Nhf")
        return new_nhf