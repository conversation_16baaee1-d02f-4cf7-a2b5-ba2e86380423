from pydantic import BaseModel, EmailStr, Field, validator
from typing import Optional, List
from uuid import UUID
from datetime import date, datetime
from enum import Enum


class EmployeeStatus(str, Enum):
    ACTIVE = "ACTIVE"
    INACTIVE = "INACTIVE"
    TERMINATED = "TERMINATED"
    ON_LEAVE = "ON_LEAVE"
    PROBATION = "PROBATION"


class MaritalStatus(str, Enum):
    SINGLE = "single"
    MARRIED = "married"
    DIVORCED = "divorced"
    WIDOWED = "widowed"


class Gender(str, Enum):
    MALE = "male"
    FEMALE = "female"
    OTHER = "other"
    PREFER_NOT_TO_SAY = "prefer_not_to_say"


class EmploymentType(str, Enum):
    FULL_TIME = "FULL_TIME"
    PART_TIME = "PART_TIME"
    CONTRACT = "CONTRACT"
    INTERN = "INTERN"
    CONSULTANT = "CONSULTANT"


class Role(str, Enum):
    ADMIN = "admin"
    HR = "hr"
    MANAGER = "manager"
    EMPLOYEE = "employee"
    SUPERVISOR = "supervisor"


# Base schemas
class EmployeeBase(BaseModel):
    first_name: str = Field(..., min_length=1, max_length=100)
    last_name: str = Field(..., min_length=1, max_length=100)
    middle_name: Optional[str] = Field(None, max_length=100)
    email: EmailStr
    phone: Optional[str] = Field(None, max_length=20)
    mobile_number: Optional[str] = Field(None, max_length=20)
    alternate_phone: Optional[str] = Field(None, max_length=20)

    # Personal information
    date_of_birth: Optional[date] = None
    gender: Optional[Gender] = None
    marital_status: Optional[MaritalStatus] = None
    nationality: Optional[str] = Field(None, max_length=100)
    blood_group: Optional[str] = Field(None, max_length=10)

    # Address information
    address_line1: Optional[str] = Field(None, max_length=255)
    address_line2: Optional[str] = Field(None, max_length=255)
    city: Optional[str] = Field(None, max_length=100)
    state: Optional[str] = Field(None, max_length=100)
    country: Optional[str] = Field(None, max_length=100)
    postal_code: Optional[str] = Field(None, max_length=20)

    # Emergency contact
    emergency_contact_name: Optional[str] = Field(None, max_length=200)
    emergency_contact_phone: Optional[str] = Field(None, max_length=20)
    emergency_contact_relationship: Optional[str] = Field(None, max_length=100)

    # Work information
    work_location: Optional[str] = Field(None, max_length=255)
    timezone: Optional[str] = Field("UTC", max_length=50)

    # Profile
    bio: Optional[str] = None
    skills: Optional[List[str]] = None


class EmployeeCreate(EmployeeBase):
    employee_id: str = Field(..., min_length=1, max_length=20)
    hire_date: date
    employment_type: EmploymentType = EmploymentType.FULL_TIME
    status: EmployeeStatus = EmployeeStatus.ACTIVE

    # Organizational information
    department_id: Optional[UUID] = None
    designation_id: Optional[UUID] = None
    manager_id: Optional[UUID] = None

    # System information
    user_id: Optional[UUID] = None  # Link to auth service user
    role: str = "employee"  # User role in the system


class EmployeeUpdate(BaseModel):
    first_name: Optional[str] = Field(None, min_length=1, max_length=100)
    last_name: Optional[str] = Field(None, min_length=1, max_length=100)
    middle_name: Optional[str] = Field(None, max_length=100)
    email: Optional[EmailStr] = None
    phone: Optional[str] = Field(None, max_length=20)
    alternate_phone: Optional[str] = Field(None, max_length=20)

    # Personal information
    date_of_birth: Optional[date] = None
    gender: Optional[Gender] = None
    marital_status: Optional[MaritalStatus] = None
    nationality: Optional[str] = Field(None, max_length=100)
    blood_group: Optional[str] = Field(None, max_length=10)

    # Address information
    address_line1: Optional[str] = Field(None, max_length=255)
    address_line2: Optional[str] = Field(None, max_length=255)
    city: Optional[str] = Field(None, max_length=100)
    state: Optional[str] = Field(None, max_length=100)
    country: Optional[str] = Field(None, max_length=100)
    postal_code: Optional[str] = Field(None, max_length=20)

    # Emergency contact
    emergency_contact_name: Optional[str] = Field(None, max_length=200)
    emergency_contact_phone: Optional[str] = Field(None, max_length=20)
    emergency_contact_relationship: Optional[str] = Field(None, max_length=100)

    # Employment information
    employment_type: Optional[EmploymentType] = None
    status: Optional[EmployeeStatus] = None
    termination_date: Optional[date] = None

    # Organizational information
    department_id: Optional[UUID] = None
    designation_id: Optional[UUID] = None
    manager_id: Optional[UUID] = None

    # Work information
    work_location: Optional[str] = Field(None, max_length=255)
    timezone: Optional[str] = Field(None, max_length=50)

    # Profile
    profile_picture_url: Optional[str] = Field(None, max_length=500)
    bio: Optional[str] = None
    skills: Optional[List[str]] = None

    # System information
    role: Optional[str] = None


class EmployeeResponse(EmployeeBase):
    id: UUID
    employee_id: str
    hire_date: date
    termination_date: Optional[date] = None
    employment_type: EmploymentType
    status: EmployeeStatus

    # Organizational information
    organization_id: UUID
    department_id: Optional[UUID] = None
    designation_id: Optional[UUID] = None
    manager_id: Optional[UUID] = None

    # Profile
    profile_picture_url: Optional[str] = None

    # System information
    user_id: Optional[UUID] = None
    role: str

    # Timestamps
    created_at: datetime
    updated_at: datetime
    is_active: bool

    # Computed fields
    full_name: Optional[str] = None
    display_name: Optional[str] = None

    class Config:
        from_attributes = True


class EmployeeListResponse(BaseModel):
    employees: List[EmployeeResponse]
    total: int
    skip: int
    limit: int


# Department schemas
class DepartmentBase(BaseModel):
    name: str = Field(..., min_length=1, max_length=200)
    code: str = Field(..., min_length=1, max_length=20)
    description: Optional[str] = None


class DepartmentCreate(DepartmentBase):
    parent_department_id: Optional[UUID] = None
    head_id: Optional[UUID] = None


class DepartmentUpdate(BaseModel):
    name: Optional[str] = Field(None, min_length=1, max_length=200)
    code: Optional[str] = Field(None, min_length=1, max_length=20)
    description: Optional[str] = None
    parent_department_id: Optional[UUID] = None
    head_id: Optional[UUID] = None


class DepartmentResponse(DepartmentBase):
    id: UUID
    organization_id: UUID
    parent_department_id: Optional[UUID] = None
    head_id: Optional[UUID] = None
    created_at: datetime
    updated_at: datetime
    is_active: bool

    class Config:
        from_attributes = True


# Designation schemas
class DesignationBase(BaseModel):
    title: str = Field(..., min_length=1, max_length=200)
    code: str = Field(..., min_length=1, max_length=20)
    description: Optional[str] = None
    level: Optional[int] = Field(None, ge=1)


class DesignationCreate(DesignationBase):
    department_id: Optional[UUID] = None


class DesignationUpdate(BaseModel):
    title: Optional[str] = Field(None, min_length=1, max_length=200)
    code: Optional[str] = Field(None, min_length=1, max_length=20)
    description: Optional[str] = None
    level: Optional[int] = Field(None, ge=1)
    department_id: Optional[UUID] = None


class DesignationResponse(DesignationBase):
    id: UUID
    organization_id: UUID
    department_id: Optional[UUID] = None
    created_at: datetime
    updated_at: datetime
    is_active: bool

    class Config:
        from_attributes = True
