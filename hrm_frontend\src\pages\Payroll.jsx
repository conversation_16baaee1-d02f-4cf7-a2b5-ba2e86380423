/**
 * Payroll Management Page with RBAC Integration
 * Displays payslips, salary information, and payroll management with role-based access control
 */

import React, { useState } from 'react';
import { DollarSign, Download, Eye, Calendar, Filter, Plus, CreditCard, TrendingUp } from 'lucide-react';
import { usePermissions } from '../hooks/usePermissions';
import { PermissionGate, ConditionalRender } from '../components/ProtectedRoute';

export default function Payroll({ activeTab = 'my-payslips' }) {
  const permissions = usePermissions();
  const [selectedYear, setSelectedYear] = useState('2024');
  const [selectedMonth, setSelectedMonth] = useState('all');

  // Mock payroll data
  const payslips = [
    {
      id: 1,
      month: 'January 2024',
      grossSalary: 75000,
      netSalary: 62500,
      deductions: 12500,
      status: 'paid',
      payDate: '2024-01-31'
    },
    {
      id: 2,
      month: 'December 2023',
      grossSalary: 75000,
      netSalary: 62500,
      deductions: 12500,
      status: 'paid',
      payDate: '2023-12-31'
    },
    {
      id: 3,
      month: 'November 2023',
      grossSalary: 75000,
      netSalary: 62500,
      deductions: 12500,
      status: 'paid',
      payDate: '2023-11-30'
    }
  ];

  const salaryBreakdown = {
    basicSalary: 50000,
    allowances: {
      housing: 15000,
      transport: 5000,
      medical: 3000,
      meal: 2000
    },
    deductions: {
      tax: 8500,
      pension: 3000,
      nhf: 1000
    }
  };

  const yearToDateSummary = {
    totalEarnings: 900000,
    totalDeductions: 150000,
    netPay: 750000,
    taxPaid: 102000
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-NG', {
      style: 'currency',
      currency: 'NGN'
    }).format(amount);
  };

  const renderMyPayslips = () => (
    <div className="space-y-6">
      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <DollarSign className="h-8 w-8 text-green-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Current Salary</p>
              <p className="text-2xl font-bold text-gray-900">{formatCurrency(salaryBreakdown.basicSalary)}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <TrendingUp className="h-8 w-8 text-blue-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">YTD Earnings</p>
              <p className="text-2xl font-bold text-gray-900">{formatCurrency(yearToDateSummary.totalEarnings)}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <CreditCard className="h-8 w-8 text-purple-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Net Pay YTD</p>
              <p className="text-2xl font-bold text-gray-900">{formatCurrency(yearToDateSummary.netPay)}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <Calendar className="h-8 w-8 text-orange-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Tax Paid YTD</p>
              <p className="text-2xl font-bold text-gray-900">{formatCurrency(yearToDateSummary.taxPaid)}</p>
            </div>
          </div>
        </div>
      </div>

      {/* Filters */}
      <div className="flex justify-between items-center">
        <div className="flex space-x-4">
          <select 
            value={selectedYear} 
            onChange={(e) => setSelectedYear(e.target.value)}
            className="border border-gray-300 rounded-md px-3 py-2"
          >
            <option value="2024">2024</option>
            <option value="2023">2023</option>
            <option value="2022">2022</option>
          </select>
          <select 
            value={selectedMonth} 
            onChange={(e) => setSelectedMonth(e.target.value)}
            className="border border-gray-300 rounded-md px-3 py-2"
          >
            <option value="all">All Months</option>
            <option value="01">January</option>
            <option value="02">February</option>
            <option value="03">March</option>
            <option value="04">April</option>
            <option value="05">May</option>
            <option value="06">June</option>
            <option value="07">July</option>
            <option value="08">August</option>
            <option value="09">September</option>
            <option value="10">October</option>
            <option value="11">November</option>
            <option value="12">December</option>
          </select>
        </div>
      </div>

      {/* Payslips Table */}
      <div className="bg-white rounded-lg shadow overflow-hidden">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Period</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Gross Salary</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Deductions</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Net Salary</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {payslips.map((payslip) => (
              <tr key={payslip.id}>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm font-medium text-gray-900">{payslip.month}</div>
                  <div className="text-sm text-gray-500">Paid: {payslip.payDate}</div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  {formatCurrency(payslip.grossSalary)}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  {formatCurrency(payslip.deductions)}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                  {formatCurrency(payslip.netSalary)}
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
                    {payslip.status}
                  </span>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                  <div className="flex space-x-2">
                    <button className="text-blue-600 hover:text-blue-900">
                      <Eye size={16} />
                    </button>
                    <button className="text-green-600 hover:text-green-900">
                      <Download size={16} />
                    </button>
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );

  const renderSalaryBreakdown = () => (
    <div className="space-y-6">
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Earnings */}
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Earnings</h3>
          <div className="space-y-3">
            <div className="flex justify-between">
              <span className="text-sm text-gray-600">Basic Salary</span>
              <span className="text-sm font-medium">{formatCurrency(salaryBreakdown.basicSalary)}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm text-gray-600">Housing Allowance</span>
              <span className="text-sm font-medium">{formatCurrency(salaryBreakdown.allowances.housing)}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm text-gray-600">Transport Allowance</span>
              <span className="text-sm font-medium">{formatCurrency(salaryBreakdown.allowances.transport)}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm text-gray-600">Medical Allowance</span>
              <span className="text-sm font-medium">{formatCurrency(salaryBreakdown.allowances.medical)}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm text-gray-600">Meal Allowance</span>
              <span className="text-sm font-medium">{formatCurrency(salaryBreakdown.allowances.meal)}</span>
            </div>
            <hr />
            <div className="flex justify-between font-medium">
              <span>Total Earnings</span>
              <span>{formatCurrency(salaryBreakdown.basicSalary + Object.values(salaryBreakdown.allowances).reduce((a, b) => a + b, 0))}</span>
            </div>
          </div>
        </div>

        {/* Deductions */}
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Deductions</h3>
          <div className="space-y-3">
            <div className="flex justify-between">
              <span className="text-sm text-gray-600">Income Tax</span>
              <span className="text-sm font-medium">{formatCurrency(salaryBreakdown.deductions.tax)}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm text-gray-600">Pension (8%)</span>
              <span className="text-sm font-medium">{formatCurrency(salaryBreakdown.deductions.pension)}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm text-gray-600">NHF (2.5%)</span>
              <span className="text-sm font-medium">{formatCurrency(salaryBreakdown.deductions.nhf)}</span>
            </div>
            <hr />
            <div className="flex justify-between font-medium">
              <span>Total Deductions</span>
              <span>{formatCurrency(Object.values(salaryBreakdown.deductions).reduce((a, b) => a + b, 0))}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  const renderPayrollManagement = () => (
    <PermissionGate permission="payrollManagement">
      <div className="space-y-6">
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Payroll Management</h3>
          <p className="text-gray-600">Manage payroll processing and employee salaries...</p>
        </div>
      </div>
    </PermissionGate>
  );

  const renderContent = () => {
    switch (activeTab) {
      case 'my-payslips':
        return renderMyPayslips();
      case 'salary-breakdown':
        return renderSalaryBreakdown();
      case 'payroll-management':
        return renderPayrollManagement();
      default:
        return renderMyPayslips();
    }
  };

  return (
    <div className="p-6">
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-gray-900">Payroll Management</h1>
        <p className="text-gray-600">View your payslips and salary information</p>
      </div>

      {renderContent()}
    </div>
  );
}
