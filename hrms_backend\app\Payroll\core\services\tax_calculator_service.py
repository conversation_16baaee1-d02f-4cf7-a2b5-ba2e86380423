from core.packages.tax_calculator.factory import TaxCalculatorFactory

class TaxCalculatorService:
    def __init__(self, country, tax_type="PAYE"):
        self.calculator = TaxCalculatorFactory.create_calculator(country,tax_type)

    def calculate_annual_tax(self, annual_earnings, annual_pen, annual_nhf):
        return self.calculator.calculate_annual_tax(annual_earnings, annual_pen, annual_nhf)

    def calculate_monthly_tax(self, annual_tax):
        return self.calculator.calculate_monthly_tax(annual_tax)
