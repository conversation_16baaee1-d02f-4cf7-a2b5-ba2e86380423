# Agnoshin HRMS Backend

A comprehensive Human Resource Management System (HRMS) backend built with FastAPI, featuring modular architecture, real-time updates, and enterprise-grade security.

## 🚀 Features

### ✅ Core HR Functions
- **Employee Management**: Complete CRUD operations with profile management
- **Department & Designation Management**: Organizational structure management
- **Onboarding Workflows**: Automated employee onboarding processes

### ✅ Attendance & Time Management
- **Attendance Tracking**: Clock-in/out with geolocation support
- **Shift Management**: Flexible shift scheduling with timezone support
- **Time Tracker**: Real-time time tracking with project integration
- **Timesheet Management**: Automated timesheet generation and approval

### ✅ Leave Management
- **Leave Policies**: Configurable leave types and policies
- **Leave Requests**: Automated approval workflows
- **Leave Balance**: Real-time balance tracking and accrual
- **Leave Calendar**: Company-wide holiday management

### ✅ Project & Task Management
- **Project Management**: Complete project lifecycle management
- **Kanban Boards**: Trello-like boards with drag-drop functionality
- **Task Dependencies**: Complex task relationships and dependencies
- **Real-time Updates**: WebSocket-powered live updates

### ✅ Ticketing System
- **Support Tickets**: Employee query management
- **Auto-assignment**: Intelligent ticket routing
- **SLA Management**: Service level agreement tracking
- **Resolution Tracking**: Complete ticket lifecycle

### ✅ Delegation & Approvals
- **Task Delegation**: Temporary responsibility delegation
- **Approval Workflows**: Configurable approval chains
- **Audit Trails**: Complete delegation history
- **Auto-revert**: Automatic delegation expiry

### ✅ Payroll Management
- **Salary Structures**: Flexible compensation management
- **Payroll Processing**: Automated payroll calculations
- **Tax Management**: Configurable tax calculations
- **Payslip Generation**: Automated document generation

### ✅ Performance Management
- **Goal Setting**: SMART goal management
- **Performance Reviews**: 360-degree feedback system
- **Competency Framework**: Skills and competency tracking

### 🆕 Enhanced Security & Authentication
- **Multi-Factor Authentication (2FA)**: TOTP-based 2FA with QR code setup
- **AES-256 Encryption**: Field-level encryption for sensitive data
- **Advanced Security Middleware**: Rate limiting, IP filtering, request validation
- **Comprehensive Audit Logging**: Complete audit trails for all system activities

### 🆕 GDPR Compliance
- **Data Subject Rights**: Right to access, rectification, erasure, portability
- **Automated Data Retention**: Configurable retention policies with auto-cleanup
- **Consent Management**: Granular consent tracking for data processing
- **Privacy Impact Assessments**: Automated PIA generation

### 🆕 Recruitment Management
- **Job Posting Management**: Create and manage job postings with templates
- **Candidate Tracking**: Complete candidate lifecycle management
- **Application Processing**: Automated application screening and routing
- **Interview Scheduling**: Integrated interview management with feedback
- **Offer Management**: Digital offer letters with e-signature support

### 🆕 Learning Management System (LMS)
- **Course Creation**: Rich course content with multimedia support
- **Enrollment Management**: Automated enrollment with prerequisites
- **Progress Tracking**: Real-time learning progress monitoring
- **Assessments**: Interactive quizzes with auto-grading
- **Certifications**: Digital certificates with verification

### 🆕 Onboarding/Offboarding Workflows
- **Workflow Templates**: Customizable onboarding/offboarding templates
- **Task Automation**: Automated task assignment and tracking
- **Document Management**: Digital document collection and verification
- **Progress Monitoring**: Real-time workflow progress tracking

### 🆕 Advanced Reporting & Analytics
- **Custom Report Builder**: Drag-and-drop report creation
- **Interactive Dashboards**: Real-time dashboards with customizable widgets
- **Scheduled Reports**: Automated report generation and email delivery
- **KPI Tracking**: Key performance indicator monitoring
- **Data Export**: Multiple format support (PDF, Excel, CSV, JSON)

### 🆕 Enhanced Case Management
- **Workflow Automation**: Intelligent case routing and escalation
- **SLA Management**: Service level agreement tracking and alerts
- **Knowledge Base Integration**: AI-powered solution suggestions
- **Multi-channel Support**: Email, chat, and portal integration
- **Development Planning**: Career development tracking

### ✅ Employee Engagement
- **Surveys**: Anonymous and identified surveys
- **eNPS Tracking**: Employee Net Promoter Score
- **Feedback System**: 360-degree feedback
- **Engagement Analytics**: Comprehensive reporting

## 🏗️ Architecture

### Technology Stack
- **Backend Framework**: FastAPI (Python 3.10+)
- **Database**: PostgreSQL with SQLAlchemy ORM
- **Authentication**: JWT with integration to auth microservice
- **Authorization**: Casbin RBAC with role hierarchy
- **Background Tasks**: Celery with Redis
- **Real-time Updates**: WebSockets
- **Caching**: Redis
- **File Storage**: Local storage with S3 support

### Project Structure
```
hrms_backend/
├── app/
│   ├── api/                    # API endpoints
│   │   ├── employee.py
│   │   ├── attendance.py
│   │   ├── leave.py
│   │   ├── project.py
│   │   ├── kanban.py
│   │   ├── ticket.py
│   │   ├── delegation.py
│   │   ├── payroll.py
│   │   ├── performance.py
│   │   └── engagement.py
│   ├── core/                   # Core functionality
│   │   ├── config.py          # Configuration
│   │   ├── security.py        # Authentication & JWT
│   │   ├── rbac.py           # Role-based access control
│   │   ├── websocket_manager.py # WebSocket management
│   │   └── celery_app.py     # Celery configuration
│   ├── db/                     # Database layer
│   │   ├── models/            # SQLAlchemy models
│   │   ├── base.py           # Base model
│   │   └── session.py        # Database session
│   ├── schemas/               # Pydantic schemas
│   ├── services/              # Business logic
│   ├── workers/               # Celery tasks
│   ├── utils/                 # Utility functions
│   ├── sockets/              # WebSocket handlers
│   └── main.py               # FastAPI application
├── alembic/                   # Database migrations
├── tests/                     # Test suite
├── requirements.txt
├── .env.example
└── README.md
```

## 🔐 Security & RBAC

### Role Hierarchy
```
SUPER_ADMIN
    └── ADMIN
        └── HR
            └── MANAGER
                └── EMPLOYEE
```

### Permission Matrix
- **SUPER_ADMIN**: Full system access
- **ADMIN**: All operations except super admin functions
- **HR**: HR operations, employee management, reports
- **MANAGER**: Team management, approvals, project management
- **EMPLOYEE**: Self-service operations, basic project access

### Security Features
- JWT-based authentication with refresh tokens
- Casbin RBAC with fine-grained permissions
- Organization-level data isolation
- Audit trails for all operations
- Rate limiting and request validation

## 🚀 Getting Started

### Prerequisites
- Python 3.10+
- PostgreSQL 12+
- Redis 6+
- Auth Service (running on port 8081)

### Installation

1. **Clone the repository**
```bash
git clone <repository-url>
cd hrms_backend
```

2. **Create virtual environment**
```bash
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
```

3. **Install dependencies**
```bash
pip install -r requirements.txt
```

4. **Configure environment**
```bash
cp .env.example .env
# Edit .env with your configuration
```

5. **Set up database**
```bash
# Create database
createdb agnoshin_hrms

# Run migrations
alembic upgrade head
```

6. **Start Redis**
```bash
redis-server
```

7. **Start Celery worker** (in separate terminal)
```bash
celery -A app.core.celery_app worker --loglevel=info
```

8. **Start Celery beat** (in separate terminal)
```bash
celery -A app.core.celery_app beat --loglevel=info
```

9. **Start the application**
```bash
uvicorn app.main:app --host 0.0.0.0 --port 8085 --reload
```

### API Documentation
- **Swagger UI**: http://localhost:8085/docs
- **ReDoc**: http://localhost:8085/redoc

## 📊 Real-time Features

### WebSocket Endpoints
- **Connection**: `ws://localhost:8085/ws/{user_id}?room={room_id}`
- **Notifications**: Real-time notifications for all modules
- **Kanban Updates**: Live board updates
- **Attendance Updates**: Real-time attendance status
- **Task Updates**: Live task and project updates

### Supported Events
- `attendance_update`: Attendance status changes
- `leave_status_change`: Leave request updates
- `task_assignment`: New task assignments
- `kanban_update`: Board and card updates
- `ticket_update`: Support ticket updates
- `delegation_request`: Delegation notifications

## 🔄 Background Tasks

### Scheduled Tasks
- **Daily**: Auto-checkout, leave balance updates
- **Weekly**: Timesheet reminders
- **Monthly**: Payroll processing, report generation
- **Periodic**: SLA breach checks, delegation expiry

### Task Queues
- `attendance`: Attendance-related tasks
- `leave`: Leave management tasks
- `payroll`: Payroll processing tasks
- `notifications`: Email and push notifications
- `reports`: Report generation tasks
- `general`: General background tasks

## 🧪 Testing

```bash
# Run all tests
pytest

# Run with coverage
pytest --cov=app

# Run specific test file
pytest tests/test_employee.py
```

## 📈 Monitoring & Logging

### Health Checks
- **Application**: `GET /health`
- **WebSocket Stats**: `GET /ws/stats`

### Logging
- Structured logging with correlation IDs
- Error tracking and alerting
- Performance monitoring
- Audit trail logging

## 🚀 Deployment

### Docker Deployment
```bash
# Build image
docker build -t agnoshin-hrms-backend .

# Run container
docker run -p 8085:8085 agnoshin-hrms-backend
```

### Production Considerations
- Use environment-specific configuration
- Set up proper logging and monitoring
- Configure SSL/TLS termination
- Set up database connection pooling
- Configure Redis clustering for high availability
- Set up Celery worker scaling

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Ensure all tests pass
6. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

For support and questions:
- Create an issue in the repository
- Contact the development team
- Check the documentation at `/docs`

---

**Agnoshin HRMS Backend** - Empowering organizations with comprehensive HR management.

A comprehensive Human Resource Management System backend built with FastAPI.

## Features

- **Employee Management**: Complete employee lifecycle management
- **Attendance Tracking**: Real-time attendance monitoring with WebSocket support
- **Leave Management**: Leave requests, approvals, and tracking
- **Shift Management**: Flexible shift scheduling and management
- **Project Management**: Project tracking and task management
- **Kanban Board**: Visual project management with real-time updates
- **Ticket System**: Issue tracking and resolution
- **Delegation**: Task and responsibility delegation
- **Timesheet**: Time tracking and reporting
- **Payroll**: Salary calculation and payroll management
- **Performance**: Employee performance evaluation
- **Engagement**: Employee engagement tracking

## Architecture

- **FastAPI**: Modern, fast web framework for building APIs
- **SQLAlchemy**: SQL toolkit and Object-Relational Mapping
- **Alembic**: Database migration tool
- **Celery**: Distributed task queue for background jobs
- **Redis**: In-memory data structure store for caching and message broker
- **Casbin**: Authorization library for RBAC
- **WebSocket**: Real-time communication for live updates
- **Pydantic**: Data validation using Python type annotations

## Project Structure

```
hrms_backend/
├── app/
│   ├── core/                     # Core utilities & configs
│   ├── db/                       # Database configuration and models
│   ├── api/                      # API routers grouped by feature
│   ├── schemas/                  # Pydantic request/response models
│   ├── services/                 # Business logic layer
│   ├── workers/                  # Celery background tasks
│   ├── sockets/                  # WebSocket endpoints
│   ├── casbin/                   # RBAC configuration
│   └── utils/                    # Utility functions
├── tests/                        # Unit and integration tests
├── alembic/                      # Database migrations
├── requirements.txt
└── README.md
```

## Getting Started

1. Install dependencies:
   ```bash
   pip install -r requirements.txt
   ```

2. Set up environment variables
3. Run database migrations
4. Start the FastAPI server
5. Start Celery workers
6. Start Redis server

## Development

This project follows a modular architecture with clear separation of concerns:

- **Models**: Database models organized by feature
- **Schemas**: Pydantic models for request/response validation
- **Services**: Business logic implementation
- **API**: FastAPI routers for HTTP endpoints
- **WebSockets**: Real-time communication endpoints
- **Workers**: Background task processing

## License

[Add your license here]
