from core.databases.database import db
from core.models.basemodel import ModelBase
from datetime import datetime

class ApprovalSettingsModel(ModelBase):
    __tablename__ = "approval_settings"

    id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    number_of_approval = db.Column(db.Integer, nullable=False)
    user_id = db.Column(db.Integer, db.<PERSON>ey((ModelBase.dbSchema() + '.users.id')), nullable=False) 
    status = db.Column(db.<PERSON>, default=False, nullable=True)
    timestamp = db.Column(db.DateTime, nullable=False, default=datetime.now())
    
      
    