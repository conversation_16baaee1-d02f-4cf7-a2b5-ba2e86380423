"""create more salary component column

Revision ID: be257dc32d05
Revises: ce5b9b635b2c
Create Date: 2024-10-09 10:55:27.124967

"""
from typing import Sequence, Union

from alembic import op
from sqlalchemy import func, TIMESTAMP, Foreign<PERSON>ey
from sqlalchemy import Column, Integer, Float


# revision identifiers, used by Alembic.
revision: str = 'be257dc32d05'
down_revision: Union[str, None] = 'ce5b9b635b2c'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    op.add_column('salary_templates', Column('monthly_tax', Float, nullable=True)),
    op.add_column('salary_templates', Column('annual_tax', Float, nullable=True)),
    op.add_column('salary_templates', Column('total_taxable_monthly_sum', Float, nullable=True)),
    op.add_column('salary_templates', Column('total_taxable_annual_sum', Float, nullable=True)),
    op.add_column('salary_templates', Column('total_non_taxable_monthly_sum', Float, nullable=True)),
    op.add_column('salary_templates', Column('total_non_taxable_annual_sum', Float, nullable=True)),
    op.add_column('salary_templates', Column('total_statutory_monthly_sum', Float, nullable=True)),
    op.add_column('salary_templates', Column('total_statutory_annual_sum', Float, nullable=True)),
    op.add_column('salary_templates', Column('total_other_deductions_monthly_sum', Float, nullable=True)),
    op.add_column('salary_templates', Column('total_other_deductions_annual_sum', Float, nullable=True)),
    op.add_column('salary_templates', Column('netpay', Float, nullable=True)),


def downgrade() -> None:
    op.drop_column('salary_templates', 'monthly_tax')
    op.drop_column('salary_templates', 'annual_tax')
    op.drop_column('salary_templates', 'total_taxable_monthly_sum')
    op.drop_column('salary_templates', 'total_taxable_annual_sum')
    op.drop_column('salary_templates', 'total_non_taxable_monthly_sum')
    op.drop_column('salary_templates', 'total_non_taxable_annual_sum')
    op.drop_column('salary_templates', 'total_statutory_monthly_sum')
    op.drop_column('salary_templates', 'total_statutory_annual_sum')
    op.drop_column('salary_templates', 'total_other_deductions_monthly_sum')
    op.drop_column('salary_templates', 'total_other_deductions_annual_sum')
    op.drop_column('salary_templates', 'netpay')
