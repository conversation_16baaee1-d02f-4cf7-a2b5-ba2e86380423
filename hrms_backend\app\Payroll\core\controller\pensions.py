from flask import url_for
from flask.views import MethodView
from flask_smorest import Blueprint, abort
from core.middleware import roles_required
from schemas import PensionSchema
from sqlalchemy.orm.exc import NoResultFound
from sqlalchemy.exc import SQLAlchemyError, IntegrityError
import core.utils.response_message as RESPONSEMESSAGE
from core.services.pensions import PensionsService

blueprint = Blueprint("Pension", __name__, description="Operations for Pension")
    
@blueprint.route("/pensions/<id>")
class PensionList(MethodView):
    @roles_required(['admin'])
    @blueprint.response(200, PensionSchema)
    def get(self, id):
        service = PensionsService()
        pension = service.getPensions(id)
        if not pension:
            abort(401, message="Pension does not exist")
        return pension    
    
    @roles_required(['admin'])
    def delete(self, id):
        service = PensionsService()
        pension = service.getPensions(id)
        if not pension:
            abort(404, message="Pension does not exist")
        service.deletePensions(id)
        return {"message" : "Pension deleted"}
    
    @roles_required(['admin'])
    @blueprint.arguments(PensionSchema)
    @blueprint.response(201, PensionSchema)
    def put(self, id, data):
        service = PensionsService()
        pension = service.getPensions(id)
        if not pension:
            abort(404, message="Pension does not exist")
        try :
            new_pension = service.updatePensions(id, data)
            return new_pension
        except SQLAlchemyError:
                abort(500, message="Error while updating Pension")
    
@blueprint.route("/pension")
class Pension(MethodView):
    @roles_required(['admin'])
    @blueprint.arguments(PensionSchema)
    @blueprint.response(200, PensionSchema)
    def post(self, data):
        try:
            service = PensionsService()
            pension = service.getPensionsByKey({"id": data['id']})
            if not pension:
                new_pension = service.createPensions(data)
            else:
                abort(400, message="Pension already exist")
        except IntegrityError:
            abort(500, message="Error while creating Pension")
        except SQLAlchemyError as e:
            print(e)
            abort(500, message="Error while creating Pension")
        return new_pension