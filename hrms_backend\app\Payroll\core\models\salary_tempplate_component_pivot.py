from core.databases.database import db
from core.models.basemodel import ModelBase
from datetime import datetime

class SalaryTemplateComponentsPivotModel(ModelBase):
    __tablename__ = "salary_template_components_pivot"

    id = db.Column(db.Integer, primary_key=True)
    salary_template_id = db.Column(db.<PERSON><PERSON>ger, db.<PERSON>ey((ModelBase.dbSchema() + '.salary_templates.id')), nullable=False) 
    salary_component_id = db.Column(db.Integer, db.ForeignKey((ModelBase.dbSchema() + '.salary_components.id')), nullable=False) 

    salary_template = db.relationship('SalaryTemplatesModel', back_populates='salary_template_components')
    salary_component = db.relationship('SalaryComponentsModel', back_populates='salary_template_components')
