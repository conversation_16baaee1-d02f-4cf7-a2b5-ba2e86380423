"""create bank table

Revision ID: 5f58cde4eca9
Revises: 47c47ba6783c
Create Date: 2024-04-20 07:06:29.876456

"""
from typing import Sequence, Union

from alembic import op
from sqlalchemy import func, TIMESTAMP, ForeignKey
from sqlalchemy import <PERSON><PERSON>n, Integer, String

# revision identifiers, used by Alembic.
revision: str = '5f58cde4eca9'
down_revision: Union[str, None] = '47c47ba6783c'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    op.create_table(
        'banks',
        Column('id', Integer, primary_key=True),
        <PERSON>umn('name', String(45), nullable=True),
        <PERSON>umn('sort_code', String(45), nullable=True),
        <PERSON>umn('user_id', Integer, ForeignKey("users.id")),
        <PERSON><PERSON><PERSON>("timestamp", TIMESTAMP, server_default=func.now()),
    )


def downgrade() -> None:
    op.drop_table("banks")