from pydantic import BaseModel, <PERSON>, validator
from typing import Optional, List, Dict, Any
from uuid import UUID
from datetime import datetime, date
from decimal import Decimal
from enum import Enum


class PayrollStatus(str, Enum):
    DRAFT = "draft"
    PROCESSING = "processing"
    PROCESSED = "processed"
    APPROVED = "approved"
    PAID = "paid"
    CANCELLED = "cancelled"


class ComponentType(str, Enum):
    EARNING = "earning"
    DEDUCTION = "deduction"
    BENEFIT = "benefit"
    TAX = "tax"
    REIMBURSEMENT = "reimbursement"


class PayrollFrequency(str, Enum):
    WEEKLY = "weekly"
    BIWEEKLY = "biweekly"
    MONTHLY = "monthly"
    QUARTERLY = "quarterly"
    ANNUALLY = "annually"


# Salary Structure Schemas
class SalaryStructureBase(BaseModel):
    name: str = Field(..., min_length=1, max_length=200)
    description: Optional[str] = None
    frequency: PayrollFrequency = PayrollFrequency.MONTHLY
    currency: str = Field("USD", max_length=3)
    is_default: bool = False


class SalaryStructureCreate(SalaryStructureBase):
    department_id: Optional[UUID] = None
    designation_id: Optional[UUID] = None


class SalaryStructureUpdate(BaseModel):
    name: Optional[str] = Field(None, min_length=1, max_length=200)
    description: Optional[str] = None
    frequency: Optional[PayrollFrequency] = None
    currency: Optional[str] = Field(None, max_length=3)
    is_default: Optional[bool] = None
    department_id: Optional[UUID] = None
    designation_id: Optional[UUID] = None


class SalaryStructureResponse(SalaryStructureBase):
    id: UUID
    organization_id: UUID
    department_id: Optional[UUID] = None
    designation_id: Optional[UUID] = None
    created_at: datetime
    updated_at: datetime
    is_active: bool

    class Config:
        from_attributes = True


# Payroll Component Schemas
class PayrollComponentBase(BaseModel):
    name: str = Field(..., min_length=1, max_length=200)
    code: str = Field(..., min_length=1, max_length=50)
    component_type: ComponentType
    description: Optional[str] = None
    is_taxable: bool = True
    is_statutory: bool = False
    calculation_formula: Optional[str] = None
    percentage_of_basic: Optional[Decimal] = Field(None, ge=0, le=100)
    fixed_amount: Optional[Decimal] = Field(None, ge=0)
    max_limit: Optional[Decimal] = Field(None, ge=0)
    min_limit: Optional[Decimal] = Field(None, ge=0)


class PayrollComponentCreate(PayrollComponentBase):
    depends_on_components: Optional[List[UUID]] = None


class PayrollComponentUpdate(BaseModel):
    name: Optional[str] = Field(None, min_length=1, max_length=200)
    description: Optional[str] = None
    is_taxable: Optional[bool] = None
    is_statutory: Optional[bool] = None
    calculation_formula: Optional[str] = None
    percentage_of_basic: Optional[Decimal] = Field(None, ge=0, le=100)
    fixed_amount: Optional[Decimal] = Field(None, ge=0)
    max_limit: Optional[Decimal] = Field(None, ge=0)
    min_limit: Optional[Decimal] = Field(None, ge=0)
    depends_on_components: Optional[List[UUID]] = None


class PayrollComponentResponse(PayrollComponentBase):
    id: UUID
    organization_id: UUID
    depends_on_components: Optional[List[UUID]] = None
    created_at: datetime
    updated_at: datetime
    is_active: bool

    class Config:
        from_attributes = True


# Employee Salary Schemas
class EmployeeSalaryBase(BaseModel):
    basic_salary: Decimal = Field(..., ge=0)
    effective_date: date
    currency: str = Field("USD", max_length=3)
    is_active: bool = True


class EmployeeSalaryCreate(EmployeeSalaryBase):
    employee_id: UUID
    salary_structure_id: UUID
    component_values: Optional[Dict[str, Decimal]] = None


class EmployeeSalaryUpdate(BaseModel):
    basic_salary: Optional[Decimal] = Field(None, ge=0)
    effective_date: Optional[date] = None
    currency: Optional[str] = Field(None, max_length=3)
    is_active: Optional[bool] = None
    component_values: Optional[Dict[str, Decimal]] = None


class EmployeeSalaryResponse(EmployeeSalaryBase):
    id: UUID
    employee_id: UUID
    salary_structure_id: UUID
    component_values: Optional[Dict[str, Decimal]] = None
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


# Payroll Record Schemas
class PayrollRecordBase(BaseModel):
    pay_period_start: date
    pay_period_end: date
    pay_date: date
    currency: str = Field("USD", max_length=3)

    @validator('pay_period_end')
    def end_date_must_be_after_start_date(cls, v, values):
        if 'pay_period_start' in values and v <= values['pay_period_start']:
            raise ValueError('Pay period end must be after start date')
        return v


class PayrollRecordCreate(PayrollRecordBase):
    employee_id: UUID
    salary_structure_id: UUID
    basic_salary: Decimal = Field(..., ge=0)
    working_days: int = Field(..., ge=0)
    present_days: int = Field(..., ge=0)
    overtime_hours: Optional[Decimal] = Field(None, ge=0)
    overtime_rate: Optional[Decimal] = Field(None, ge=0)


class PayrollRecordUpdate(BaseModel):
    pay_date: Optional[date] = None
    working_days: Optional[int] = Field(None, ge=0)
    present_days: Optional[int] = Field(None, ge=0)
    overtime_hours: Optional[Decimal] = Field(None, ge=0)
    overtime_rate: Optional[Decimal] = Field(None, ge=0)
    status: Optional[PayrollStatus] = None
    approved_by: Optional[UUID] = None
    approved_at: Optional[datetime] = None
    paid_by: Optional[UUID] = None
    paid_at: Optional[datetime] = None
    payment_reference: Optional[str] = Field(None, max_length=200)
    notes: Optional[str] = None


class PayrollRecordResponse(PayrollRecordBase):
    id: UUID
    employee_id: UUID
    salary_structure_id: UUID
    basic_salary: Decimal
    working_days: int
    present_days: int
    overtime_hours: Optional[Decimal] = None
    overtime_rate: Optional[Decimal] = None
    gross_salary: Decimal
    total_deductions: Decimal
    net_salary: Decimal
    tax_amount: Decimal
    status: PayrollStatus
    component_breakdown: Optional[Dict[str, Decimal]] = None
    approved_by: Optional[UUID] = None
    approved_at: Optional[datetime] = None
    paid_by: Optional[UUID] = None
    paid_at: Optional[datetime] = None
    payment_reference: Optional[str] = None
    notes: Optional[str] = None
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class PayrollRecordListResponse(BaseModel):
    records: List[PayrollRecordResponse]
    total: int
    skip: int
    limit: int


# Payroll Processing Schemas
class PayrollProcessingRequest(BaseModel):
    employee_ids: Optional[List[UUID]] = None  # If None, process all employees
    pay_period_start: date
    pay_period_end: date
    pay_date: date
    include_overtime: bool = True
    include_bonuses: bool = True
    dry_run: bool = False


class PayrollProcessingResponse(BaseModel):
    batch_id: UUID
    total_employees: int
    processed_count: int
    failed_count: int
    total_gross: Decimal
    total_deductions: Decimal
    total_net: Decimal
    processing_status: str
    errors: Optional[List[Dict[str, Any]]] = None


# Payroll Approval Schemas
class PayrollApprovalRequest(BaseModel):
    payroll_record_ids: List[UUID]
    approved: bool
    comments: Optional[str] = Field(None, max_length=1000)


class PayrollApprovalResponse(BaseModel):
    payroll_record_id: UUID
    approved: bool
    approved_by: UUID
    approved_at: datetime
    comments: Optional[str] = None


# Payslip Schema
class PayslipResponse(BaseModel):
    payroll_record: PayrollRecordResponse
    employee_details: Dict[str, Any]
    company_details: Dict[str, Any]
    earnings: List[Dict[str, Any]]
    deductions: List[Dict[str, Any]]
    summary: Dict[str, Decimal]
    generated_at: datetime


# Tax Configuration Schemas
class TaxSlabBase(BaseModel):
    min_amount: Decimal = Field(..., ge=0)
    max_amount: Optional[Decimal] = Field(None, ge=0)
    tax_rate: Decimal = Field(..., ge=0, le=100)

    @validator('max_amount')
    def max_amount_must_be_greater_than_min(cls, v, values):
        if v is not None and 'min_amount' in values and v <= values['min_amount']:
            raise ValueError('Max amount must be greater than min amount')
        return v


class TaxSlabCreate(TaxSlabBase):
    pass


class TaxSlabResponse(TaxSlabBase):
    id: UUID
    organization_id: UUID
    created_at: datetime
    updated_at: datetime
    is_active: bool

    class Config:
        from_attributes = True


# Payroll Reports
class PayrollSummaryReport(BaseModel):
    period_start: date
    period_end: date
    total_employees: int
    total_gross_salary: Decimal
    total_deductions: Decimal
    total_net_salary: Decimal
    total_tax: Decimal
    department_wise_summary: List[Dict[str, Any]]
    component_wise_summary: List[Dict[str, Any]]


class EmployeePayrollSummary(BaseModel):
    employee_id: UUID
    employee_name: str
    total_gross: Decimal
    total_deductions: Decimal
    total_net: Decimal
    payroll_records_count: int
    last_pay_date: Optional[date] = None


# Bulk Operations
class BulkSalaryUpdate(BaseModel):
    employee_ids: List[UUID]
    percentage_increase: Optional[Decimal] = Field(None, ge=0)
    fixed_increase: Optional[Decimal] = Field(None, ge=0)
    effective_date: date
    reason: str = Field(..., min_length=1, max_length=500)
