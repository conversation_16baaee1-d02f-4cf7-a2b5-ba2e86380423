#!/usr/bin/env python3
"""
Create test data for HRMS system
"""

import sys
import os
from uuid import uuid4
from datetime import datetime, date

# Add the app directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'app'))

from sqlalchemy.orm import Session
from app.db.session import SessionLocal, engine
from app.db.models.employee import Employee, Department, Designation
from app.db.models.attendance import AttendancePolicy
from app.db.models.leave import LeavePolicy

def create_test_data():
    """Create test data for the HRMS system"""
    db = SessionLocal()

    try:
        print("Creating test data...")

        # Check if data already exists
        existing_dept = db.query(Department).first()
        if existing_dept:
            print("✅ Test data already exists!")

            # Show existing employees
            employees = db.query(Employee).all()
            print("\nExisting Test Users:")
            for emp in employees:
                print(f"  - {emp.first_name} {emp.last_name} ({emp.email}) - Role: {emp.role}")

            return

        # Create test organization ID
        org_id = uuid4()
        
        # Create departments
        departments = [
            Department(
                id=uuid4(),
                name="Engineering",
                code="ENG",
                description="Software Development Team",
                organization_id=org_id,
                is_active=True
            ),
            Department(
                id=uuid4(),
                name="Human Resources",
                code="HR",
                description="HR Department",
                organization_id=org_id,
                is_active=True
            ),
            Department(
                id=uuid4(),
                name="Sales",
                code="SALES",
                description="Sales and Marketing",
                organization_id=org_id,
                is_active=True
            )
        ]
        
        for dept in departments:
            db.add(dept)
        
        db.commit()
        print("✅ Departments created")
        
        # Create designations
        designations = [
            Designation(
                id=uuid4(),
                title="Software Engineer",
                code="SE",
                description="Software Development",
                organization_id=org_id,
                is_active=True
            ),
            Designation(
                id=uuid4(),
                title="HR Manager",
                code="HRM",
                description="Human Resources Management",
                organization_id=org_id,
                is_active=True
            ),
            Designation(
                id=uuid4(),
                title="Sales Executive",
                code="SX",
                description="Sales and Customer Relations",
                organization_id=org_id,
                is_active=True
            )
        ]
        
        for designation in designations:
            db.add(designation)
        
        db.commit()
        print("✅ Designations created")
        
        # Create test employees
        employees = [
            Employee(
                id=uuid4(),
                employee_id="EMP001",
                first_name="John",
                last_name="Doe",
                email="<EMAIL>",
                phone="+1234567890",
                department_id=departments[0].id,
                designation_id=designations[0].id,

                hire_date=date(2023, 1, 15),
                organization_id=org_id,
                is_active=True,
                role="employee"
            ),
            Employee(
                id=uuid4(),
                employee_id="EMP002",
                first_name="Jane",
                last_name="Smith",
                email="<EMAIL>",
                phone="+1234567891",
                department_id=departments[1].id,
                designation_id=designations[1].id,

                hire_date=date(2023, 2, 1),
                organization_id=org_id,
                is_active=True,
                role="hr"
            ),
            Employee(
                id=uuid4(),
                employee_id="EMP003",
                first_name="Bob",
                last_name="Johnson",
                email="<EMAIL>",
                phone="+1234567892",
                department_id=departments[0].id,
                designation_id=designations[0].id,

                hire_date=date(2023, 3, 10),
                organization_id=org_id,
                is_active=True,
                role="manager"
            ),
            Employee(
                id=uuid4(),
                employee_id="EMP004",
                first_name="Alice",
                last_name="Wilson",
                email="<EMAIL>",
                phone="+1234567893",
                department_id=departments[2].id,
                designation_id=designations[2].id,

                hire_date=date(2023, 1, 20),
                organization_id=org_id,
                is_active=True,
                role="admin"
            )
        ]
        
        for employee in employees:
            db.add(employee)
        
        db.commit()
        print("✅ Test employees created")
        
        # Create attendance policy
        attendance_policy = AttendancePolicy(
            id=uuid4(),
            organization_id=org_id,
            name="Default Attendance Policy",
            working_hours_per_day=8.0,
            working_days_per_week=5,
            grace_period_minutes=15,
            half_day_threshold_hours=4.0,
            overtime_threshold_hours=8.0,
            auto_clock_out_enabled=True,
            auto_clock_out_time="18:00",
            location_tracking_enabled=False,
            is_active=True
        )
        
        db.add(attendance_policy)
        db.commit()
        print("✅ Attendance policy created")
        
        # Create leave policies
        leave_policies = [
            LeavePolicy(
                id=uuid4(),
                organization_id=org_id,
                name="Annual Leave",
                leave_type="ANNUAL",
                days_per_year=25,
                max_consecutive_days=10,
                carry_forward_enabled=True,
                max_carry_forward_days=5,
                is_active=True
            ),
            LeavePolicy(
                id=uuid4(),
                organization_id=org_id,
                name="Sick Leave",
                leave_type="SICK",
                days_per_year=12,
                max_consecutive_days=7,
                carry_forward_enabled=False,
                is_active=True
            )
        ]
        
        for policy in leave_policies:
            db.add(policy)
        
        db.commit()
        print("✅ Leave policies created")
        
        print("\n🎉 Test data created successfully!")
        print("\nTest Users:")
        for emp in employees:
            print(f"  - {emp.first_name} {emp.last_name} ({emp.email}) - Role: {emp.role}")
        
        print(f"\nOrganization ID: {org_id}")
        
    except Exception as e:
        print(f"❌ Error creating test data: {e}")
        db.rollback()
        raise
    finally:
        db.close()

if __name__ == "__main__":
    create_test_data()
