"""
Test Onboarding System
Test the complete onboarding workflow with email notifications
"""

import asyncio
import sys
import os
from datetime import datetime, date, timedelta
import uuid

# Add the app directory to the Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'app'))

from app.db.session import <PERSON><PERSON>ocal
from app.db.models.employee import Employee
from app.db.models.onboarding import OnboardingWorkflow, WorkflowTemplate, WorkflowStatus
from app.services.hr_management.onboarding_service import OnboardingService
from app.services.hr_management.onboarding_email_service import OnboardingEmailService
from app.core.security import CurrentUser

async def test_onboarding_system():
    """Test the complete onboarding system"""
    
    db = SessionLocal()
    onboarding_service = OnboardingService()
    email_service = OnboardingEmailService()
    
    try:
        print("🧪 Testing Onboarding System")
        print("=" * 50)
        
        # Get a test employee
        employee = db.query(Employee).filter(Employee.employee_id == "EMP001").first()
        if not employee:
            print("❌ Test employee not found. Please run create_test_employees.py first")
            return
        
        print(f"✅ Found test employee: {employee.full_name} ({employee.email})")
        
        # Get the default template
        template = db.query(WorkflowTemplate).filter(
            WorkflowTemplate.is_default == True,
            WorkflowTemplate.workflow_type == "onboarding"
        ).first()
        
        if not template:
            print("❌ Default onboarding template not found")
            return
        
        print(f"✅ Found default template: {template.name}")
        
        # Create a mock current user (HR)
        hr_employee = db.query(Employee).filter(Employee.employee_id == "HR001").first()
        if not hr_employee:
            print("❌ HR employee not found")
            return
        
        current_user = CurrentUser(
            user_id=str(hr_employee.user_id),
            email=hr_employee.email,
            role="hr",
            organization_id=str(hr_employee.id)
        )
        
        print(f"✅ Using HR user: {hr_employee.full_name}")
        
        # Check if onboarding workflow already exists
        existing_workflow = db.query(OnboardingWorkflow).filter(
            OnboardingWorkflow.employee_id == employee.id,
            OnboardingWorkflow.status.in_([WorkflowStatus.NOT_STARTED, WorkflowStatus.IN_PROGRESS])
        ).first()
        
        if existing_workflow:
            print(f"✅ Found existing workflow: {existing_workflow.title}")
            workflow = existing_workflow
        else:
            # Create onboarding workflow data
            from app.schemas.onboarding import OnboardingWorkflowCreate
            
            workflow_data = OnboardingWorkflowCreate(
                employee_id=employee.id,
                template_id=template.id,
                title=f"Onboarding - {employee.full_name}",
                description=f"Complete onboarding process for {employee.full_name} starting {date.today()}",
                start_date=date.today(),
                expected_completion_date=date.today() + timedelta(days=90),
                assigned_to_id=hr_employee.id,
                buddy_id=None
            )
            
            print("📝 Creating onboarding workflow...")
            workflow = await onboarding_service.create_onboarding_workflow(
                db=db,
                workflow_data=workflow_data,
                current_user=current_user
            )
            print(f"✅ Created workflow: {workflow.title}")
        
        # Test email notifications
        print("\n📧 Testing Email Notifications")
        print("-" * 30)
        
        # Test welcome email
        print("Sending welcome email...")
        welcome_result = await email_service.send_welcome_email(
            employee=employee,
            workflow=workflow,
            login_credentials={
                "email": employee.email,
                "password": "TempPassword123!"
            }
        )
        print(f"Welcome email: {'✅ Sent' if welcome_result else '❌ Failed'}")
        
        # Test onboarding start notification
        print("Sending onboarding start notification...")
        from app.db.models.onboarding import OnboardingTask
        tasks = db.query(OnboardingTask).filter(
            OnboardingTask.workflow_id == workflow.id
        ).order_by(OnboardingTask.order_index).limit(5).all()
        
        start_result = await email_service.send_onboarding_start_notification(
            employee=employee,
            workflow=workflow,
            tasks=tasks
        )
        print(f"Start notification: {'✅ Sent' if start_result else '❌ Failed'}")
        
        # Test HR notification
        print("Sending HR notification...")
        hr_result = await email_service.send_hr_notification(
            hr_email=hr_employee.email,
            employee=employee,
            workflow=workflow,
            notification_type="new_employee"
        )
        print(f"HR notification: {'✅ Sent' if hr_result else '❌ Failed'}")
        
        # Test task reminder (if tasks exist)
        if tasks:
            print("Sending task reminder...")
            task_result = await email_service.send_task_reminder_email(
                employee=employee,
                task=tasks[0],
                workflow=workflow
            )
            print(f"Task reminder: {'✅ Sent' if task_result else '❌ Failed'}")
        
        # Test workflow completion
        print("\n🎯 Testing Workflow Completion")
        print("-" * 30)
        
        if workflow.status != WorkflowStatus.COMPLETED:
            print("Completing onboarding workflow...")
            completed_workflow = await onboarding_service.complete_onboarding_workflow(
                db=db,
                workflow_id=workflow.id,
                current_user=current_user
            )
            print(f"✅ Workflow completed: {completed_workflow.status}")
            
            # Test completion email
            print("Sending completion email...")
            completion_result = await email_service.send_completion_email(
                employee=employee,
                workflow=completed_workflow
            )
            print(f"Completion email: {'✅ Sent' if completion_result else '❌ Failed'}")
        else:
            print("✅ Workflow already completed")
        
        # Summary
        print("\n📊 Test Summary")
        print("=" * 50)
        print(f"Employee: {employee.full_name}")
        print(f"Workflow: {workflow.title}")
        print(f"Status: {workflow.status}")
        print(f"Progress: {workflow.progress_percentage}%")
        print(f"Tasks: {workflow.completed_tasks}/{workflow.total_tasks}")
        print(f"Start Date: {workflow.start_date}")
        print(f"Expected Completion: {workflow.expected_completion_date}")
        
        if workflow.actual_completion_date:
            print(f"Actual Completion: {workflow.actual_completion_date}")
        
        print("\n🎉 Onboarding system test completed successfully!")
        print("\nNext Steps:")
        print("1. Check your email for onboarding notifications")
        print("2. Open the frontend at http://localhost:5173")
        print("3. Navigate to the Onboarding page")
        print("4. View the created workflow and test the interface")
        
    except Exception as e:
        print(f"❌ Error testing onboarding system: {e}")
        import traceback
        traceback.print_exc()
    finally:
        db.close()

if __name__ == "__main__":
    asyncio.run(test_onboarding_system())
