from core.models.designations import DesignationModel
from core.databases.database import db
from core.repositories.user import UserRepository
from core.models.employees import EmployeeModel
from sqlalchemy.orm import joinedload
from sqlalchemy import func
class DesignationsRepository:
    def __init__(self) -> None:
        pass

    @classmethod
    def createDesignations(self, name):
        designations = DesignationModel(
            name=name,
            user_id=UserRepository.authUserId()
        )
        db.session.add(designations)
        db.session.commit()
        return designations
    
    @classmethod
    def get_or_create_designation(self, name):
        designation = DesignationModel.query.filter_by(name=name, user_id=UserRepository.authUserId()).first()
        if not designation:
            designation = self.createDesignations(name)
        return designation.id

    @classmethod
    def fetchAll(self):
        return (
            db.session.query(
                DesignationModel,
                func.count(EmployeeModel.id).label("employee_count")
            )
            .filter(DesignationModel.user_id == UserRepository().authUserId())
            .outerjoin(EmployeeModel, DesignationModel.id == EmployeeModel.designation_id)
            .group_by(DesignationModel.id)
            .order_by(DesignationModel.timestamp.desc())
            .all()
        )


    @classmethod
    def getDesignations(self, id):
        return DesignationModel.query.filter(DesignationModel.id == id).first()
    
    @classmethod
    def getDesignationsByKeys(self, kwargs):
        return DesignationModel.query.filter_by(user_id=UserRepository().authUserId(), **kwargs).all()

    @classmethod
    def updateDesignations(self, id, **kwargs):
        designations = DesignationModel.query.filter_by(id=id).first()
        if designations:
            for key, value in kwargs.items():
                setattr(designations, key, value)
            db.session.commit()
            return designations
        else:
            return None

    @classmethod
    def deleteDesignations(self, id):
        DesignationModel.query.filter(DesignationModel.id == id).delete()
        db.session.commit()
        return