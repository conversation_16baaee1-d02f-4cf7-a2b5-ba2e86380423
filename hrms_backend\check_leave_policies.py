#!/usr/bin/env python3
"""
Check leave_policies table structure
"""

import sys
import os

# Add the app directory to Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'app'))

from sqlalchemy import text
from app.db.session import engine

def check_leave_policies():
    """Check leave_policies table structure"""
    try:
        with engine.connect() as conn:
            print('=== LEAVE_POLICIES TABLE COLUMNS ===')
            result = conn.execute(text("""
                SELECT column_name, data_type FROM information_schema.columns 
                WHERE table_name = 'leave_policies' AND table_schema = 'public'
                ORDER BY ordinal_position
            """))
            for row in result.fetchall():
                print(f'  {row[0]} ({row[1]})')
                
    except Exception as e:
        print(f"Error checking leave_policies: {e}")

if __name__ == "__main__":
    check_leave_policies()
