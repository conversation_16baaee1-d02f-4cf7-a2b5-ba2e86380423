from core.models.payment_gateway import PaymentGatewayModel
from core.databases.database import db
from core.repositories.user import UserRepository
from schemas import PaymentGatewaySchema

class PaymentGatewayRepository:

    def __init__(self) -> None:
       pass

    
    @classmethod
    def createPayGateway(self, payment_gateway_name):

        payment_gateway_data = PaymentGatewayModel(
            payment_gateway_name=payment_gateway_name,
            user_id=UserRepository().authUserId()
        )
        db.session.add(payment_gateway_data)
        db.session.commit()
         
        payment_gateway_schema = PaymentGatewaySchema()

        return payment_gateway_schema.dump(payment_gateway_data)
    
    @classmethod
    def get_all_payment_gateway(self):
        return PaymentGatewayModel.query.order_by(PaymentGatewayModel.created_at.desc()).all()
