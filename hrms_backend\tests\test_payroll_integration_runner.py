#!/usr/bin/env python3
"""
Payroll Integration Test Runner
Validates the integrated payroll system functionality
"""

import asyncio
import sys
import os
from datetime import date, datetime, timedelta
from decimal import Decimal

# Add the app directory to the Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'app'))

def test_tax_calculator():
    """Test the tax calculator functionality"""
    print("🧮 Testing Tax Calculator...")
    
    try:
        from app.services.payroll_service import TaxCalculatorService
        
        # Test Nigeria PAYE calculation
        calculator = TaxCalculatorService(country="Nigeria", tax_type="PAYE")
        
        # Test case 1: Low income
        annual_tax = calculator.calculate_annual_tax(300000, 24000, 0)
        monthly_tax = calculator.calculate_monthly_tax(annual_tax)
        
        print(f"  ✅ Low income test: Annual tax = ₦{annual_tax:,.2f}, Monthly tax = ₦{monthly_tax:,.2f}")
        
        # Test case 2: Medium income
        annual_tax = calculator.calculate_annual_tax(1000000, 80000, 0)
        monthly_tax = calculator.calculate_monthly_tax(annual_tax)
        
        print(f"  ✅ Medium income test: Annual tax = ₦{annual_tax:,.2f}, Monthly tax = ₦{monthly_tax:,.2f}")
        
        # Test case 3: High income
        annual_tax = calculator.calculate_annual_tax(5000000, 400000, 0)
        monthly_tax = calculator.calculate_monthly_tax(annual_tax)
        
        print(f"  ✅ High income test: Annual tax = ₦{annual_tax:,.2f}, Monthly tax = ₦{monthly_tax:,.2f}")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Tax calculator test failed: {e}")
        return False

def test_payroll_service():
    """Test the payroll service functionality"""
    print("💼 Testing Payroll Service...")
    
    try:
        from app.services.payroll_service import PayrollService
        
        service = PayrollService()
        
        # Test component calculation - fixed amount
        component_fixed = {
            "name": "Housing Allowance",
            "type": "earning",
            "amount": 50000,
            "calculation_method": "fixed",
            "is_taxable": True
        }
        
        amount = service._calculate_component_amount(
            component_fixed,
            Decimal("200000"),
            Decimal("150000")
        )
        
        assert amount == Decimal("50000"), f"Expected 50000, got {amount}"
        print(f"  ✅ Fixed component calculation: ₦{amount:,.2f}")
        
        # Test component calculation - percentage
        component_percentage = {
            "name": "Pension",
            "type": "deduction", 
            "percentage": 8,
            "calculation_method": "percentage",
            "base": "basic"
        }
        
        amount = service._calculate_component_amount(
            component_percentage,
            Decimal("200000"),
            Decimal("150000")
        )
        
        assert amount == Decimal("12000"), f"Expected 12000 (8% of 150000), got {amount}"
        print(f"  ✅ Percentage component calculation: ₦{amount:,.2f}")
        
        # Test data validation
        valid_data = {
            "employee_id": "EMP001",
            "pay_period_start": "2024-01-01",
            "pay_period_end": "2024-01-31",
            "basic_salary": 150000
        }
        
        validation = service.validatePayrollData(valid_data)
        assert validation["isValid"] == True, "Valid data should pass validation"
        print(f"  ✅ Data validation: {validation['isValid']}")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Payroll service test failed: {e}")
        return False

def test_payroll_calculations():
    """Test payroll calculation scenarios"""
    print("📊 Testing Payroll Calculations...")
    
    try:
        # Mock employee data
        employee_data = {
            "id": "EMP001",
            "name": "John Doe",
            "basic_salary": 150000,
            "allowances": {
                "housing": 50000,
                "transport": 25000,
                "meal": 15000
            },
            "deductions": {
                "pension": 0.08,  # 8% of basic
                "nhf": 0.025,     # 2.5% of basic
                "tax": 0.15       # Simplified 15%
            }
        }
        
        # Calculate gross salary
        gross_salary = employee_data["basic_salary"] + sum(employee_data["allowances"].values())
        print(f"  ✅ Gross salary calculation: ₦{gross_salary:,.2f}")
        
        # Calculate deductions
        pension = employee_data["basic_salary"] * employee_data["deductions"]["pension"]
        nhf = employee_data["basic_salary"] * employee_data["deductions"]["nhf"]
        tax = gross_salary * employee_data["deductions"]["tax"]
        
        total_deductions = pension + nhf + tax
        print(f"  ✅ Total deductions: ₦{total_deductions:,.2f}")
        print(f"    - Pension: ₦{pension:,.2f}")
        print(f"    - NHF: ₦{nhf:,.2f}")
        print(f"    - Tax: ₦{tax:,.2f}")
        
        # Calculate net salary
        net_salary = gross_salary - total_deductions
        print(f"  ✅ Net salary: ₦{net_salary:,.2f}")
        
        # Validate calculations
        assert gross_salary > 0, "Gross salary should be positive"
        assert total_deductions > 0, "Total deductions should be positive"
        assert net_salary > 0, "Net salary should be positive"
        assert net_salary < gross_salary, "Net salary should be less than gross"
        
        return True
        
    except Exception as e:
        print(f"  ❌ Payroll calculations test failed: {e}")
        return False

def test_payment_integration():
    """Test payment integration functionality"""
    print("💳 Testing Payment Integration...")
    
    try:
        # Test payment data preparation
        net_salary = Decimal("125000.00")
        employee_email = "<EMAIL>"
        reference = f"PAY_EMP001_{datetime.now().strftime('%Y%m%d%H%M%S')}"
        
        # Prepare Paystack payment data
        payment_data = {
            "amount": int(float(net_salary) * 100),  # Convert to kobo
            "email": employee_email,
            "reference": reference,
            "currency": "NGN",
            "metadata": {
                "employee_id": "EMP001",
                "pay_period": "2024-01"
            }
        }
        
        print(f"  ✅ Payment data prepared:")
        print(f"    - Amount: ₦{net_salary:,.2f} ({payment_data['amount']} kobo)")
        print(f"    - Reference: {reference}")
        print(f"    - Email: {employee_email}")
        
        # Test webhook data processing
        webhook_data = {
            "event": "charge.success",
            "data": {
                "reference": reference,
                "amount": payment_data["amount"],
                "currency": "NGN",
                "status": "success"
            }
        }
        
        # Validate webhook processing
        assert webhook_data["event"] == "charge.success", "Event should be charge.success"
        assert webhook_data["data"]["reference"] == reference, "Reference should match"
        assert webhook_data["data"]["amount"] == payment_data["amount"], "Amount should match"
        
        print(f"  ✅ Webhook processing validated")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Payment integration test failed: {e}")
        return False

def test_reporting_functionality():
    """Test reporting and analytics functionality"""
    print("📈 Testing Reporting Functionality...")
    
    try:
        # Mock payroll data for reporting
        mock_payroll_data = [
            {
                "employee_id": "EMP001",
                "department": "Engineering",
                "gross_salary": 200000,
                "net_salary": 160000,
                "total_deductions": 40000,
                "total_taxes": 25000
            },
            {
                "employee_id": "EMP002", 
                "department": "Sales",
                "gross_salary": 150000,
                "net_salary": 125000,
                "total_deductions": 25000,
                "total_taxes": 15000
            },
            {
                "employee_id": "EMP003",
                "department": "Engineering", 
                "gross_salary": 180000,
                "net_salary": 145000,
                "total_deductions": 35000,
                "total_taxes": 22000
            }
        ]
        
        # Calculate summary statistics
        total_employees = len(mock_payroll_data)
        total_gross = sum(record["gross_salary"] for record in mock_payroll_data)
        total_net = sum(record["net_salary"] for record in mock_payroll_data)
        total_deductions = sum(record["total_deductions"] for record in mock_payroll_data)
        total_taxes = sum(record["total_taxes"] for record in mock_payroll_data)
        
        avg_gross = total_gross / total_employees
        avg_net = total_net / total_employees
        
        print(f"  ✅ Summary Statistics:")
        print(f"    - Total Employees: {total_employees}")
        print(f"    - Total Gross: ₦{total_gross:,.2f}")
        print(f"    - Total Net: ₦{total_net:,.2f}")
        print(f"    - Total Deductions: ₦{total_deductions:,.2f}")
        print(f"    - Total Taxes: ₦{total_taxes:,.2f}")
        print(f"    - Average Gross: ₦{avg_gross:,.2f}")
        print(f"    - Average Net: ₦{avg_net:,.2f}")
        
        # Department breakdown
        department_breakdown = {}
        for record in mock_payroll_data:
            dept = record["department"]
            if dept not in department_breakdown:
                department_breakdown[dept] = {
                    "count": 0,
                    "total_gross": 0,
                    "total_net": 0
                }
            
            department_breakdown[dept]["count"] += 1
            department_breakdown[dept]["total_gross"] += record["gross_salary"]
            department_breakdown[dept]["total_net"] += record["net_salary"]
        
        print(f"  ✅ Department Breakdown:")
        for dept, data in department_breakdown.items():
            print(f"    - {dept}: {data['count']} employees, ₦{data['total_gross']:,.2f} gross")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Reporting functionality test failed: {e}")
        return False

def test_api_endpoints():
    """Test API endpoint availability"""
    print("🌐 Testing API Endpoints...")

    try:
        # Test that the API modules can be imported
        from app.api.payroll import router
        from app.services.payroll_service import PayrollService

        print(f"  ✅ Payroll API router imported successfully")
        print(f"  ✅ Payroll service imported successfully")

        # Test service instantiation
        service = PayrollService()
        assert service is not None, "Service should be instantiable"

        print(f"  ✅ Payroll service instantiated successfully")

        return True

    except Exception as e:
        print(f"  ❌ API endpoints test failed: {e}")
        return False

def test_advanced_payroll_integration():
    """Test advanced payroll folder integration"""
    print("🔧 Testing Advanced Payroll Integration...")

    try:
        from app.services.payroll_service import PAYROLL_SERVICES_AVAILABLE

        if PAYROLL_SERVICES_AVAILABLE:
            print(f"  ✅ Advanced Payroll services are available")

            # Test individual service imports
            try:
                from app.services.payroll_service import (
                    PayrollSalaryTemplatesService,
                    PayrollBenefitsService,
                    PayrollPaystackService,
                    ComponentProcessor,
                    LoanRequestService,
                    ApprovalSettingsService,
                    ProrateSalariesService,
                    WorkingDaysService,
                    PayrollTaxCalculatorService,
                    PayslipService,
                    EmailService,
                    NHFService,
                    PensionsService
                )
                print(f"  ✅ All advanced services imported successfully")

                # Test service instantiation
                services_to_test = [
                    ("Salary Templates", PayrollSalaryTemplatesService),
                    ("Benefits", PayrollBenefitsService),
                    ("Loan Request", LoanRequestService),
                    ("Approval Settings", ApprovalSettingsService),
                    ("Prorate Salaries", ProrateSalariesService),
                    ("Working Days", WorkingDaysService),
                    ("NHF", NHFService),
                    ("Pensions", PensionsService),
                    ("Email", EmailService),
                    ("Payslip", PayslipService)
                ]

                for service_name, service_class in services_to_test:
                    try:
                        service_instance = service_class()
                        print(f"  ✅ {service_name} service instantiated")
                    except Exception as e:
                        print(f"  ⚠️  {service_name} service instantiation failed: {e}")

            except ImportError as e:
                print(f"  ⚠️  Some advanced services not available: {e}")

        else:
            print(f"  ⚠️  Advanced Payroll services not available - using fallback implementations")

        return True

    except Exception as e:
        print(f"  ❌ Advanced payroll integration test failed: {e}")
        return False

def test_paystack_integration():
    """Test Paystack integration functionality"""
    print("💳 Testing Paystack Integration...")

    try:
        from app.services.payroll_service import PAYROLL_SERVICES_AVAILABLE

        if PAYROLL_SERVICES_AVAILABLE:
            try:
                from app.services.payroll_service import PayrollPaystackService

                # Test Paystack service instantiation
                paystack_service = PayrollPaystackService(
                    payroll_id="test_payroll_123",
                    payment_gatwway="paystack",
                    net_pay=150000,
                    account_number="**********",
                    sort_code="123456"
                )

                print(f"  ✅ Paystack service instantiated successfully")

                # Test encryption/decryption (mock)
                try:
                    secret_key = paystack_service.decrypt_key()
                    print(f"  ✅ Key decryption method available")
                except:
                    print(f"  ⚠️  Key decryption not configured (expected in test)")

                # Test bank verification methods
                print(f"  ✅ Paystack integration methods available")

            except ImportError as e:
                print(f"  ⚠️  Paystack service not available: {e}")
        else:
            print(f"  ⚠️  Using fallback payment processing")

        return True

    except Exception as e:
        print(f"  ❌ Paystack integration test failed: {e}")
        return False

def test_salary_templates_integration():
    """Test salary templates integration"""
    print("📋 Testing Salary Templates Integration...")

    try:
        from app.services.payroll_service import PayrollService

        service = PayrollService()

        # Test template data structure
        template_data = {
            "template_name": "Test Template",
            "description": "Test salary template",
            "employment_type": "Full-time",
            "employee_type": "Regular",
            "level": "Junior",
            "salary_type": "monthly",
            "country": "Nigeria",
            "currency": "NGN",
            "work_schedule": "Standard",
            "hours_worked": 40,
            "tax_type": "PAYE",
            "gross_pay": 200000,
            "basic_salary": 150000,
            "is_active": True
        }

        print(f"  ✅ Template data structure validated")

        # Test validation
        validation = service.validatePayrollData({
            "employee_id": "EMP001",
            "pay_period_start": "2024-01-01",
            "pay_period_end": "2024-01-31",
            "basic_salary": 150000
        })

        assert validation["isValid"] == True, "Template validation should pass"
        print(f"  ✅ Template validation working")

        return True

    except Exception as e:
        print(f"  ❌ Salary templates integration test failed: {e}")
        return False

def test_benefits_integration():
    """Test benefits integration"""
    print("🎁 Testing Benefits Integration...")

    try:
        # Test benefit data structure
        benefit_data = {
            "name": "Health Insurance",
            "description": "Comprehensive health coverage",
            "amount": 25000,
            "percentage": None,
            "is_taxable": False,
            "is_active": True
        }

        print(f"  ✅ Benefit data structure validated")

        # Test benefit calculation logic
        benefit_amount = benefit_data.get("amount", 0)
        benefit_percentage = benefit_data.get("percentage", 0)

        if benefit_amount:
            calculated_benefit = benefit_amount
        elif benefit_percentage:
            calculated_benefit = 150000 * (benefit_percentage / 100)  # 150k base salary
        else:
            calculated_benefit = 0

        assert calculated_benefit > 0, "Benefit calculation should work"
        print(f"  ✅ Benefit calculation: ₦{calculated_benefit:,.2f}")

        return True

    except Exception as e:
        print(f"  ❌ Benefits integration test failed: {e}")
        return False

def test_loan_management_integration():
    """Test loan management integration"""
    print("🏦 Testing Loan Management Integration...")

    try:
        # Test loan data structure
        loan_data = {
            "employee_id": "EMP001",
            "loan_amount": 500000,
            "monthly_deduction": 50000,
            "loan_duration": 10,
            "interest_rate": 5.0,
            "status": "pending",
            "reason": "Personal loan for emergency"
        }

        print(f"  ✅ Loan data structure validated")

        # Test loan calculation
        principal = loan_data["loan_amount"]
        monthly_payment = loan_data["monthly_deduction"]
        duration = loan_data["loan_duration"]

        total_payment = monthly_payment * duration
        total_interest = total_payment - principal

        print(f"  ✅ Loan calculation:")
        print(f"    - Principal: ₦{principal:,.2f}")
        print(f"    - Monthly Payment: ₦{monthly_payment:,.2f}")
        print(f"    - Total Interest: ₦{total_interest:,.2f}")

        return True

    except Exception as e:
        print(f"  ❌ Loan management integration test failed: {e}")
        return False

def test_working_days_integration():
    """Test working days calculation integration"""
    print("📅 Testing Working Days Integration...")

    try:
        from datetime import date, timedelta

        # Test working days calculation
        start_date = date(2024, 1, 1)
        end_date = date(2024, 1, 31)

        total_days = (end_date - start_date).days + 1
        working_days = 0
        current_date = start_date

        while current_date <= end_date:
            if current_date.weekday() < 5:  # Monday = 0, Sunday = 6
                working_days += 1
            current_date += timedelta(days=1)

        weekend_days = total_days - working_days

        print(f"  ✅ Working days calculation for January 2024:")
        print(f"    - Total days: {total_days}")
        print(f"    - Working days: {working_days}")
        print(f"    - Weekend days: {weekend_days}")

        assert working_days > 0, "Should have working days"
        assert weekend_days > 0, "Should have weekend days"

        return True

    except Exception as e:
        print(f"  ❌ Working days integration test failed: {e}")
        return False

def main():
    """Run all payroll integration tests"""
    print("🚀 Starting Payroll Integration Tests")
    print("=" * 50)
    
    tests = [
        ("Tax Calculator", test_tax_calculator),
        ("Payroll Service", test_payroll_service),
        ("Payroll Calculations", test_payroll_calculations),
        ("Payment Integration", test_payment_integration),
        ("Reporting Functionality", test_reporting_functionality),
        ("API Endpoints", test_api_endpoints),
        ("Advanced Payroll Integration", test_advanced_payroll_integration),
        ("Paystack Integration", test_paystack_integration),
        ("Salary Templates Integration", test_salary_templates_integration),
        ("Benefits Integration", test_benefits_integration),
        ("Loan Management Integration", test_loan_management_integration),
        ("Working Days Integration", test_working_days_integration)
    ]
    
    passed = 0
    failed = 0
    
    for test_name, test_func in tests:
        print(f"\n📋 Running {test_name} tests...")
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} tests PASSED")
            else:
                failed += 1
                print(f"❌ {test_name} tests FAILED")
        except Exception as e:
            failed += 1
            print(f"❌ {test_name} tests FAILED with exception: {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 Test Results Summary:")
    print(f"  ✅ Passed: {passed}")
    print(f"  ❌ Failed: {failed}")
    print(f"  📈 Success Rate: {(passed / (passed + failed) * 100):.1f}%")
    
    if failed == 0:
        print("\n🎉 All payroll integration tests PASSED!")
        return 0
    else:
        print(f"\n⚠️  {failed} test(s) FAILED. Please review the errors above.")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
