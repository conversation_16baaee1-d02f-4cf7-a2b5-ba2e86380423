"""
Shift Service for business logic
"""

from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, func
from typing import Optional, List, Dict
from uuid import UUID
from datetime import datetime, date, time
from fastapi import HTTPException, status
import logging

from ...db.models.employee import Employee
from ...db.models.shift import Shift, ShiftAssignment, ShiftSwapRequest as ShiftSwapModel, ShiftPattern, ShiftPatternAssignment
from ...db.models.attendance import AttendanceRecord
from ...db.models.leave import LeaveRequest
from ...schemas.shift import (
    ShiftCreate, ShiftUpdate, ShiftResponse, ShiftListResponse,
    ShiftAssignmentCreate, ShiftAssignmentUpdate, ShiftAssignmentResponse,
    ShiftSwapRequest, ShiftSwapResponse, ShiftScheduleResponse, ShiftStatus
)
from ...schemas.leave import LeaveStatus
from ...core.security import CurrentUser
from ...core.websocket_manager import notification_manager

logger = logging.getLogger(__name__)

class ShiftService:
    """Shift service for business logic"""

    async def get_shifts(
        self,
        db: Session,
        current_user: CurrentUser,
        skip: int = 0,
        limit: int = 20,
        search: Optional[str] = None,
        department_id: Optional[UUID] = None
    ) -> ShiftListResponse:
        """Get all shifts with filtering"""
        try:
            query = db.query(Shift).filter(
                Shift.organization_id == current_user.organization_id,
                Shift.is_active == True
            )

            # Apply filters
            if search:
                query = query.filter(
                    or_(
                        Shift.name.ilike(f"%{search}%"),
                        Shift.code.ilike(f"%{search}%"),
                        Shift.description.ilike(f"%{search}%")
                    )
                )

            if department_id:
                query = query.filter(Shift.department_id == department_id)

            total = query.count()
            shifts = query.offset(skip).limit(limit).all()

            shift_responses = []
            for shift in shifts:
                # Get current assignments count
                assignments_count = db.query(ShiftAssignment).filter(
                    ShiftAssignment.shift_id == shift.id,
                    ShiftAssignment.is_active == True,
                    or_(
                        ShiftAssignment.end_date.is_(None),
                        ShiftAssignment.end_date >= date.today()
                    )
                ).count()

                shift_response = ShiftResponse.from_orm(shift)
                shift_response.current_assignments = assignments_count
                shift_responses.append(shift_response)

            total_pages = (total + limit - 1) // limit
            current_page = (skip // limit) + 1

            return ShiftListResponse(
                shifts=shift_responses,
                total=total,
                page=current_page,
                per_page=limit,
                pages=total_pages
            )

        except Exception as e:
            logger.error(f"Error getting shifts: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Error retrieving shifts"
            )

    async def create_shift(
        self,
        db: Session,
        shift_data: ShiftCreate,
        current_user: CurrentUser
    ) -> ShiftResponse:
        """Create a new shift"""
        try:
            # Validate shift timing
            await self._validate_shift_timing(shift_data)

            # Generate unique shift code
            shift_code = await self._generate_shift_code(db, shift_data.name, current_user.organization_id)

            # Calculate total hours
            total_hours = await self._calculate_shift_hours(shift_data.start_time, shift_data.end_time, shift_data.is_overnight)

            # Create shift
            shift = Shift(
                name=shift_data.name,
                code=shift_code,
                description=shift_data.description,
                organization_id=current_user.organization_id,
                start_time=shift_data.start_time,
                end_time=shift_data.end_time,
                shift_type=shift_data.shift_type,
                total_hours=total_hours,
                break_duration=shift_data.break_duration,
                is_overnight=shift_data.is_overnight,
                max_employees=shift_data.max_employees,
                department_id=shift_data.department_id,
                working_days=getattr(shift_data, 'working_days', [1, 2, 3, 4, 5]),  # Default Mon-Fri
                status=ShiftStatus.ACTIVE,
                is_active=True,
                created_by=current_user.user_id
            )

            db.add(shift)
            db.commit()
            db.refresh(shift)

            logger.info(f"Shift created: {shift.id} by {current_user.user_id}")
            return ShiftResponse.from_orm(shift)

        except HTTPException:
            raise
        except Exception as e:
            db.rollback()
            logger.error(f"Error creating shift: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Error creating shift"
            )

    async def get_shift(
        self,
        db: Session,
        shift_id: UUID,
        current_user: CurrentUser
    ) -> ShiftResponse:
        """Get shift by ID"""
        # Mock implementation
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Shift not found"
        )

    async def update_shift(
        self,
        db: Session,
        shift_id: UUID,
        shift_data: ShiftUpdate,
        current_user: CurrentUser
    ) -> ShiftResponse:
        """Update shift"""
        # Mock implementation
        raise HTTPException(
            status_code=status.HTTP_501_NOT_IMPLEMENTED,
            detail="Shift update not implemented yet"
        )

    async def delete_shift(
        self,
        db: Session,
        shift_id: UUID,
        current_user: CurrentUser
    ):
        """Delete shift"""
        # Mock implementation
        raise HTTPException(
            status_code=status.HTTP_501_NOT_IMPLEMENTED,
            detail="Shift deletion not implemented yet"
        )

    async def get_shift_assignments(
        self,
        db: Session,
        current_user: CurrentUser,
        employee_id: Optional[UUID] = None,
        shift_id: Optional[UUID] = None,
        start_date: Optional[date] = None,
        end_date: Optional[date] = None
    ) -> List[ShiftAssignmentResponse]:
        """Get shift assignments with filtering"""
        try:
            query = db.query(ShiftAssignment).join(Employee).join(Shift).filter(
                Employee.organization_id == current_user.organization_id,
                ShiftAssignment.is_active == True
            )

            # Apply filters
            if employee_id:
                query = query.filter(ShiftAssignment.employee_id == employee_id)

            if shift_id:
                query = query.filter(ShiftAssignment.shift_id == shift_id)

            if start_date:
                query = query.filter(
                    or_(
                        ShiftAssignment.end_date.is_(None),
                        ShiftAssignment.end_date >= start_date
                    )
                )

            if end_date:
                query = query.filter(ShiftAssignment.start_date <= end_date)

            assignments = query.all()

            assignment_responses = []
            for assignment in assignments:
                assignment_response = ShiftAssignmentResponse.from_orm(assignment)
                assignment_response.employee_name = f"{assignment.employee.first_name} {assignment.employee.last_name}"
                assignment_response.shift_name = assignment.shift.name
                assignment_responses.append(assignment_response)

            return assignment_responses

        except Exception as e:
            logger.error(f"Error getting shift assignments: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Error retrieving shift assignments"
            )

    async def create_shift_assignment(
        self,
        db: Session,
        assignment_data: ShiftAssignmentCreate,
        current_user: CurrentUser
    ) -> ShiftAssignmentResponse:
        """Create shift assignment"""
        try:
            # Validate employee exists and belongs to organization
            employee = db.query(Employee).filter(
                Employee.id == assignment_data.employee_id,
                Employee.organization_id == current_user.organization_id,
                Employee.is_active == True
            ).first()

            if not employee:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Employee not found"
                )

            # Validate shift exists and belongs to organization
            shift = db.query(Shift).filter(
                Shift.id == assignment_data.shift_id,
                Shift.organization_id == current_user.organization_id,
                Shift.is_active == True
            ).first()

            if not shift:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Shift not found"
                )

            # Check for conflicts
            await self._validate_shift_assignment(db, assignment_data, current_user)

            # Check shift capacity
            if shift.max_employees:
                current_assignments = db.query(ShiftAssignment).filter(
                    ShiftAssignment.shift_id == assignment_data.shift_id,
                    ShiftAssignment.assigned_date == assignment_data.assigned_date,
                    ShiftAssignment.is_active == True
                ).count()

                if current_assignments >= shift.max_employees:
                    raise HTTPException(
                        status_code=status.HTTP_400_BAD_REQUEST,
                        detail=f"Shift capacity exceeded. Maximum {shift.max_employees} employees allowed."
                    )

            # Create assignment
            assignment = ShiftAssignment(
                employee_id=assignment_data.employee_id,
                shift_id=assignment_data.shift_id,
                assigned_date=assignment_data.assigned_date,
                is_mandatory=assignment_data.is_mandatory,
                start_date=assignment_data.assigned_date,
                end_date=assignment_data.assigned_date,  # Single day assignment
                is_active=True,
                created_by=current_user.user_id
            )

            db.add(assignment)
            db.commit()
            db.refresh(assignment)

            # Send notification to employee
            await notification_manager.notify_shift_assignment(
                str(assignment_data.employee_id),
                {
                    "assignment_id": str(assignment.id),
                    "shift_name": shift.name,
                    "assigned_date": assignment_data.assigned_date.isoformat(),
                    "start_time": shift.start_time.strftime("%H:%M"),
                    "end_time": shift.end_time.strftime("%H:%M"),
                    "is_mandatory": assignment_data.is_mandatory
                }
            )

            logger.info(f"Shift assignment created: {assignment.id} for employee {assignment_data.employee_id}")

            assignment_response = ShiftAssignmentResponse.from_orm(assignment)
            assignment_response.employee_name = f"{employee.first_name} {employee.last_name}"
            assignment_response.shift_name = shift.name

            return assignment_response

        except HTTPException:
            raise
        except Exception as e:
            db.rollback()
            logger.error(f"Error creating shift assignment: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Error creating shift assignment"
            )

    async def get_employee_schedule(
        self,
        db: Session,
        employee_id: UUID,
        current_user: CurrentUser,
        start_date: Optional[date] = None,
        end_date: Optional[date] = None
    ) -> ShiftScheduleResponse:
        """Get employee's shift schedule"""
        # Mock implementation
        return ShiftScheduleResponse(
            employee_id=employee_id,
            assignments=[],
            total_hours=0.0,
            week_start=start_date or date.today(),
            week_end=end_date or date.today()
        )

    async def request_shift_swap(
        self,
        db: Session,
        swap_request: ShiftSwapRequest,
        current_user: CurrentUser
    ) -> ShiftSwapResponse:
        """Request shift swap"""
        # Mock implementation
        raise HTTPException(
            status_code=status.HTTP_501_NOT_IMPLEMENTED,
            detail="Shift swap not implemented yet"
        )

    async def _validate_shift_timing(self, shift_data: ShiftCreate):
        """Validate shift timing"""
        if shift_data.start_time == shift_data.end_time:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Start time and end time cannot be the same"
            )

        # For non-overnight shifts, end time should be after start time
        if not shift_data.is_overnight and shift_data.end_time <= shift_data.start_time:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="End time must be after start time for non-overnight shifts"
            )

        # For overnight shifts, validate the logic
        if shift_data.is_overnight and shift_data.end_time >= shift_data.start_time:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="For overnight shifts, end time should be before start time (next day)"
            )

    async def _generate_shift_code(self, db: Session, shift_name: str, organization_id: UUID) -> str:
        """Generate unique shift code"""
        # Create base code from shift name
        base_code = ''.join(word[:3].upper() for word in shift_name.split()[:2])

        # Find existing codes with same base
        existing_codes = db.query(Shift.code).filter(
            Shift.organization_id == organization_id,
            Shift.code.like(f"{base_code}%")
        ).all()

        existing_codes = [code[0] for code in existing_codes]

        # Generate unique code
        counter = 1
        new_code = base_code
        while new_code in existing_codes:
            new_code = f"{base_code}{counter:02d}"
            counter += 1

        return new_code

    async def _calculate_shift_hours(self, start_time: time, end_time: time, is_overnight: bool) -> int:
        """Calculate total shift hours in minutes"""
        start_minutes = start_time.hour * 60 + start_time.minute
        end_minutes = end_time.hour * 60 + end_time.minute

        if is_overnight:
            # For overnight shifts, add 24 hours to end time
            total_minutes = (24 * 60) - start_minutes + end_minutes
        else:
            total_minutes = end_minutes - start_minutes

        return total_minutes

    async def _validate_shift_assignment(
        self,
        db: Session,
        assignment_data: ShiftAssignmentCreate,
        current_user: CurrentUser
    ):
        """Validate shift assignment for conflicts"""
        # Check for existing assignment on the same date
        existing_assignment = db.query(ShiftAssignment).filter(
            ShiftAssignment.employee_id == assignment_data.employee_id,
            ShiftAssignment.assigned_date == assignment_data.assigned_date,
            ShiftAssignment.is_active == True
        ).first()

        if existing_assignment:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Employee already has a shift assignment on this date"
            )

        # Check for leave requests on the same date
        leave_request = db.query(LeaveRequest).filter(
            LeaveRequest.employee_id == assignment_data.employee_id,
            LeaveRequest.start_date <= assignment_data.assigned_date,
            LeaveRequest.end_date >= assignment_data.assigned_date,
            LeaveRequest.status == LeaveStatus.APPROVED
        ).first()

        if leave_request:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Employee has approved leave on this date"
            )

    async def get_staffing_forecast(
        self,
        db: Session,
        current_user: CurrentUser,
        start_date: date,
        end_date: date,
        department_id: Optional[UUID] = None
    ) -> Dict:
        """Get staffing forecast for WFM integration"""
        try:
            forecast_data = []
            current_date = start_date

            while current_date <= end_date:
                # Get shift assignments for the date
                query = db.query(ShiftAssignment).join(Employee).join(Shift).filter(
                    Employee.organization_id == current_user.organization_id,
                    ShiftAssignment.assigned_date == current_date,
                    ShiftAssignment.is_active == True
                )

                if department_id:
                    query = query.filter(Employee.department_id == department_id)

                assignments = query.all()

                # Group by shift
                shift_data = {}
                for assignment in assignments:
                    shift_id = str(assignment.shift_id)
                    if shift_id not in shift_data:
                        shift_data[shift_id] = {
                            "shift_name": assignment.shift.name,
                            "start_time": assignment.shift.start_time.strftime("%H:%M"),
                            "end_time": assignment.shift.end_time.strftime("%H:%M"),
                            "assigned_count": 0,
                            "max_capacity": assignment.shift.max_employees,
                            "employees": []
                        }

                    shift_data[shift_id]["assigned_count"] += 1
                    shift_data[shift_id]["employees"].append({
                        "employee_id": str(assignment.employee_id),
                        "employee_name": f"{assignment.employee.first_name} {assignment.employee.last_name}",
                        "is_mandatory": assignment.is_mandatory
                    })

                # Calculate staffing metrics
                total_assigned = sum(shift["assigned_count"] for shift in shift_data.values())
                total_capacity = sum(shift["max_capacity"] or 0 for shift in shift_data.values())

                forecast_data.append({
                    "date": current_date.isoformat(),
                    "total_assigned": total_assigned,
                    "total_capacity": total_capacity,
                    "utilization_rate": (total_assigned / total_capacity * 100) if total_capacity > 0 else 0,
                    "shifts": list(shift_data.values())
                })

                current_date += timedelta(days=1)

            return {
                "period": {
                    "start_date": start_date.isoformat(),
                    "end_date": end_date.isoformat()
                },
                "department_id": str(department_id) if department_id else None,
                "forecast": forecast_data,
                "summary": {
                    "total_days": len(forecast_data),
                    "avg_utilization": sum(day["utilization_rate"] for day in forecast_data) / len(forecast_data) if forecast_data else 0,
                    "understaffed_days": len([day for day in forecast_data if day["utilization_rate"] < 70]),
                    "overstaffed_days": len([day for day in forecast_data if day["utilization_rate"] > 100])
                }
            }

        except Exception as e:
            logger.error(f"Error getting staffing forecast: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Error retrieving staffing forecast"
            )

    async def validate_leave_impact_on_staffing(
        self,
        db: Session,
        employee_id: UUID,
        leave_start_date: date,
        leave_end_date: date,
        current_user: CurrentUser
    ) -> Dict:
        """Validate impact of leave request on shift staffing"""
        try:
            impact_analysis = {
                "can_approve": True,
                "staffing_issues": [],
                "affected_shifts": [],
                "recommendations": []
            }

            current_date = leave_start_date
            while current_date <= leave_end_date:
                # Get employee's shift assignments for this date
                assignments = db.query(ShiftAssignment).join(Shift).filter(
                    ShiftAssignment.employee_id == employee_id,
                    ShiftAssignment.assigned_date == current_date,
                    ShiftAssignment.is_active == True
                ).all()

                for assignment in assignments:
                    shift = assignment.shift

                    # Count other employees assigned to same shift on same date
                    other_assignments = db.query(ShiftAssignment).filter(
                        ShiftAssignment.shift_id == shift.id,
                        ShiftAssignment.assigned_date == current_date,
                        ShiftAssignment.employee_id != employee_id,
                        ShiftAssignment.is_active == True
                    ).count()

                    # Calculate minimum required staffing (e.g., 70% of max capacity)
                    min_required = max(1, int((shift.max_employees or 1) * 0.7))

                    if other_assignments < min_required:
                        impact_analysis["can_approve"] = False
                        impact_analysis["staffing_issues"].append({
                            "date": current_date.isoformat(),
                            "shift_name": shift.name,
                            "current_staff": other_assignments,
                            "minimum_required": min_required,
                            "shortage": min_required - other_assignments
                        })

                        # Find potential replacements
                        replacements = await self._find_potential_replacements(
                            db, shift.id, current_date, employee_id, current_user
                        )

                        if replacements:
                            impact_analysis["recommendations"].append({
                                "date": current_date.isoformat(),
                                "shift_name": shift.name,
                                "action": "assign_replacement",
                                "potential_replacements": replacements
                            })
                        else:
                            impact_analysis["recommendations"].append({
                                "date": current_date.isoformat(),
                                "shift_name": shift.name,
                                "action": "defer_leave",
                                "reason": "No suitable replacements available"
                            })

                    impact_analysis["affected_shifts"].append({
                        "date": current_date.isoformat(),
                        "shift_name": shift.name,
                        "shift_id": str(shift.id),
                        "remaining_staff": other_assignments,
                        "is_critical": other_assignments < min_required
                    })

                current_date += timedelta(days=1)

            return impact_analysis

        except Exception as e:
            logger.error(f"Error validating leave impact: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Error validating leave impact on staffing"
            )

    async def _find_potential_replacements(
        self,
        db: Session,
        shift_id: UUID,
        target_date: date,
        excluded_employee_id: UUID,
        current_user: CurrentUser
    ) -> List[Dict]:
        """Find potential replacement employees for a shift"""
        try:
            # Get shift details
            shift = db.query(Shift).filter(Shift.id == shift_id).first()
            if not shift:
                return []

            # Find employees who:
            # 1. Are in the same department (if shift is department-specific)
            # 2. Don't have assignments on that date
            # 3. Don't have approved leave on that date
            # 4. Have worked this shift type before (optional)

            query = db.query(Employee).filter(
                Employee.organization_id == current_user.organization_id,
                Employee.is_active == True,
                Employee.id != excluded_employee_id
            )

            if shift.department_id:
                query = query.filter(Employee.department_id == shift.department_id)

            potential_employees = query.all()

            replacements = []
            for employee in potential_employees:
                # Check if employee has assignment on target date
                existing_assignment = db.query(ShiftAssignment).filter(
                    ShiftAssignment.employee_id == employee.id,
                    ShiftAssignment.assigned_date == target_date,
                    ShiftAssignment.is_active == True
                ).first()

                if existing_assignment:
                    continue

                # Check if employee has approved leave on target date
                leave_conflict = db.query(LeaveRequest).filter(
                    LeaveRequest.employee_id == employee.id,
                    LeaveRequest.start_date <= target_date,
                    LeaveRequest.end_date >= target_date,
                    LeaveRequest.status == LeaveStatus.APPROVED
                ).first()

                if leave_conflict:
                    continue

                # Calculate compatibility score based on past assignments
                past_assignments = db.query(ShiftAssignment).filter(
                    ShiftAssignment.employee_id == employee.id,
                    ShiftAssignment.shift_id == shift_id,
                    ShiftAssignment.assigned_date < target_date
                ).count()

                compatibility_score = min(100, past_assignments * 10)  # Max 100%

                replacements.append({
                    "employee_id": str(employee.id),
                    "employee_name": f"{employee.first_name} {employee.last_name}",
                    "employee_code": employee.employee_id,
                    "department": employee.department.name if employee.department else None,
                    "compatibility_score": compatibility_score,
                    "past_assignments": past_assignments
                })

            # Sort by compatibility score (descending)
            replacements.sort(key=lambda x: x["compatibility_score"], reverse=True)

            return replacements[:5]  # Return top 5 candidates

        except Exception as e:
            logger.error(f"Error finding replacements: {e}")
            return []

    async def generate_optimal_roster(
        self,
        db: Session,
        start_date: date,
        end_date: date,
        department_id: Optional[UUID],
        current_user: CurrentUser
    ) -> Dict:
        """Generate optimal shift roster using WFM algorithms"""
        try:
            # This is a simplified optimization algorithm
            # In a real WFM system, this would use complex algorithms like:
            # - Linear programming
            # - Genetic algorithms
            # - Machine learning models

            roster_data = []
            current_date = start_date

            while current_date <= end_date:
                # Get available employees for the date
                available_employees = await self._get_available_employees(
                    db, current_date, department_id, current_user
                )

                # Get required shifts for the date
                required_shifts = await self._get_required_shifts(
                    db, current_date, department_id, current_user
                )

                # Optimize assignments using simple algorithm
                daily_assignments = await self._optimize_daily_assignments(
                    available_employees, required_shifts, current_date
                )

                roster_data.append({
                    "date": current_date.isoformat(),
                    "assignments": daily_assignments,
                    "coverage_score": self._calculate_coverage_score(daily_assignments, required_shifts)
                })

                current_date += timedelta(days=1)

            return {
                "period": {
                    "start_date": start_date.isoformat(),
                    "end_date": end_date.isoformat()
                },
                "department_id": str(department_id) if department_id else None,
                "roster": roster_data,
                "optimization_summary": {
                    "total_days": len(roster_data),
                    "average_coverage": sum(day["coverage_score"] for day in roster_data) / len(roster_data) if roster_data else 0,
                    "optimization_algorithm": "simple_greedy",
                    "generated_at": datetime.utcnow().isoformat()
                }
            }

        except Exception as e:
            logger.error(f"Error generating optimal roster: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Error generating optimal roster"
            )

    async def _get_available_employees(
        self,
        db: Session,
        target_date: date,
        department_id: Optional[UUID],
        current_user: CurrentUser
    ) -> List[Dict]:
        """Get available employees for a specific date"""
        query = db.query(Employee).filter(
            Employee.organization_id == current_user.organization_id,
            Employee.is_active == True
        )

        if department_id:
            query = query.filter(Employee.department_id == department_id)

        employees = query.all()
        available = []

        for employee in employees:
            # Check if employee has leave on this date
            has_leave = db.query(LeaveRequest).filter(
                LeaveRequest.employee_id == employee.id,
                LeaveRequest.start_date <= target_date,
                LeaveRequest.end_date >= target_date,
                LeaveRequest.status == LeaveStatus.APPROVED
            ).first()

            if not has_leave:
                # Check existing assignments
                existing_assignment = db.query(ShiftAssignment).filter(
                    ShiftAssignment.employee_id == employee.id,
                    ShiftAssignment.assigned_date == target_date,
                    ShiftAssignment.is_active == True
                ).first()

                available.append({
                    "employee_id": str(employee.id),
                    "employee_name": f"{employee.first_name} {employee.last_name}",
                    "department_id": str(employee.department_id) if employee.department_id else None,
                    "has_existing_assignment": existing_assignment is not None,
                    "existing_shift_id": str(existing_assignment.shift_id) if existing_assignment else None
                })

        return available

    async def _get_required_shifts(
        self,
        db: Session,
        target_date: date,
        department_id: Optional[UUID],
        current_user: CurrentUser
    ) -> List[Dict]:
        """Get required shifts for a specific date"""
        # Get day of week (0=Monday, 6=Sunday)
        day_of_week = target_date.weekday()

        query = db.query(Shift).filter(
            Shift.organization_id == current_user.organization_id,
            Shift.is_active == True,
            Shift.working_days.contains([day_of_week])  # Check if shift operates on this day
        )

        if department_id:
            query = query.filter(Shift.department_id == department_id)

        shifts = query.all()
        required = []

        for shift in shifts:
            # Get current assignments for this shift on this date
            current_assignments = db.query(ShiftAssignment).filter(
                ShiftAssignment.shift_id == shift.id,
                ShiftAssignment.assigned_date == target_date,
                ShiftAssignment.is_active == True
            ).count()

            required.append({
                "shift_id": str(shift.id),
                "shift_name": shift.name,
                "start_time": shift.start_time.strftime("%H:%M"),
                "end_time": shift.end_time.strftime("%H:%M"),
                "max_employees": shift.max_employees or 1,
                "current_assignments": current_assignments,
                "required_assignments": max(1, int((shift.max_employees or 1) * 0.8)),  # 80% capacity target
                "priority": self._calculate_shift_priority(shift, target_date)
            })

        return required

    def _calculate_shift_priority(self, shift: Shift, target_date: date) -> int:
        """Calculate shift priority for optimization (1-10, 10 being highest)"""
        priority = 5  # Base priority

        # Increase priority for critical shifts
        if shift.shift_type.value in ["night", "emergency"]:
            priority += 2

        # Increase priority for mandatory shifts
        if getattr(shift, 'is_mandatory', False):
            priority += 1

        # Weekend adjustments
        if target_date.weekday() >= 5:  # Weekend
            priority += 1

        return min(10, priority)

    async def _optimize_daily_assignments(
        self,
        available_employees: List[Dict],
        required_shifts: List[Dict],
        target_date: date
    ) -> List[Dict]:
        """Optimize shift assignments for a single day"""
        assignments = []

        # Sort shifts by priority (highest first)
        required_shifts.sort(key=lambda x: x["priority"], reverse=True)

        # Simple greedy algorithm for assignment
        for shift in required_shifts:
            shift_assignments = []
            needed = shift["required_assignments"] - shift["current_assignments"]

            if needed <= 0:
                continue

            # Find best employees for this shift
            candidates = [emp for emp in available_employees if not emp["has_existing_assignment"]]

            # Score candidates based on various factors
            for candidate in candidates:
                candidate["score"] = self._calculate_employee_score(candidate, shift, target_date)

            # Sort by score (highest first)
            candidates.sort(key=lambda x: x["score"], reverse=True)

            # Assign top candidates
            assigned_count = 0
            for candidate in candidates:
                if assigned_count >= needed:
                    break

                shift_assignments.append({
                    "employee_id": candidate["employee_id"],
                    "employee_name": candidate["employee_name"],
                    "shift_id": shift["shift_id"],
                    "shift_name": shift["shift_name"],
                    "assignment_score": candidate["score"]
                })

                # Mark employee as assigned
                candidate["has_existing_assignment"] = True
                assigned_count += 1

            if shift_assignments:
                assignments.extend(shift_assignments)

        return assignments

    def _calculate_employee_score(self, employee: Dict, shift: Dict, target_date: date) -> float:
        """Calculate employee suitability score for a shift"""
        score = 50.0  # Base score

        # Add randomization for fairness
        import random
        score += random.uniform(-5, 5)

        # In a real system, this would consider:
        # - Employee skills and certifications
        # - Past performance on similar shifts
        # - Preference settings
        # - Work-life balance factors
        # - Overtime considerations
        # - Training requirements

        return score

    def _calculate_coverage_score(self, assignments: List[Dict], required_shifts: List[Dict]) -> float:
        """Calculate coverage score for the day (0-100)"""
        if not required_shifts:
            return 100.0

        total_coverage = 0
        for shift in required_shifts:
            shift_assignments = [a for a in assignments if a["shift_id"] == shift["shift_id"]]
            coverage = min(100, (len(shift_assignments) / shift["required_assignments"]) * 100)
            total_coverage += coverage

        return total_coverage / len(required_shifts)

    async def sync_with_external_wfm(
        self,
        db: Session,
        current_user: CurrentUser,
        wfm_system: str = "default"
    ) -> Dict:
        """Sync shift data with external WFM system"""
        try:
            # This is a placeholder for external WFM integration
            # In a real implementation, this would:
            # 1. Connect to external WFM API
            # 2. Sync employee data
            # 3. Sync shift definitions
            # 4. Sync assignments
            # 5. Handle conflicts and updates

            sync_result = {
                "status": "success",
                "wfm_system": wfm_system,
                "sync_timestamp": datetime.utcnow().isoformat(),
                "synced_entities": {
                    "employees": 0,
                    "shifts": 0,
                    "assignments": 0
                },
                "conflicts": [],
                "errors": []
            }

            # Get organization data for sync
            employees_count = db.query(Employee).filter(
                Employee.organization_id == current_user.organization_id,
                Employee.is_active == True
            ).count()

            shifts_count = db.query(Shift).filter(
                Shift.organization_id == current_user.organization_id,
                Shift.is_active == True
            ).count()

            assignments_count = db.query(ShiftAssignment).join(Employee).filter(
                Employee.organization_id == current_user.organization_id,
                ShiftAssignment.is_active == True
            ).count()

            sync_result["synced_entities"] = {
                "employees": employees_count,
                "shifts": shifts_count,
                "assignments": assignments_count
            }

            logger.info(f"WFM sync completed for organization {current_user.organization_id}")
            return sync_result

        except Exception as e:
            logger.error(f"Error syncing with external WFM: {e}")
            return {
                "status": "error",
                "wfm_system": wfm_system,
                "sync_timestamp": datetime.utcnow().isoformat(),
                "error_message": str(e)
            }

    async def get_shift_swap_requests(
        self,
        db: Session,
        current_user: CurrentUser,
        status: Optional[str] = None
    ) -> List[ShiftSwapResponse]:
        """Get shift swap requests"""
        # Mock implementation
        return []

    async def approve_shift_swap(
        self,
        db: Session,
        swap_id: UUID,
        current_user: CurrentUser
    ):
        """Approve shift swap"""
        # Mock implementation
        raise HTTPException(
            status_code=status.HTTP_501_NOT_IMPLEMENTED,
            detail="Shift swap approval not implemented yet"
        )

    async def reject_shift_swap(
        self,
        db: Session,
        swap_id: UUID,
        current_user: CurrentUser,
        reason: Optional[str] = None
    ):
        """Reject shift swap"""
        # Mock implementation
        raise HTTPException(
            status_code=status.HTTP_501_NOT_IMPLEMENTED,
            detail="Shift swap rejection not implemented yet"
        )
