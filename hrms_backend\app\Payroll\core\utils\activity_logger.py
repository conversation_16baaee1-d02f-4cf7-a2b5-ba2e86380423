from core.services.activity_admin import AdminActivityService
from sqlalchemy.exc import SQLAlchemyError

class ActivityLogger:
    @staticmethod
    def log_admin_activity(message: str, user_id: int = None):
        try:
            service = AdminActivityService()
            return service.logActivity(message, user_id=user_id)
        except SQLAlchemyError as e:
            print(f"[Activity Log Error] Failed to log admin activity: {str(e)}")
            return None
