import pytest
import asyncio
from datetime import date, datetime, timedelta
from decimal import Decimal
from uuid import uuid4
from sqlalchemy.orm import Session
from fastapi.testclient import TestClient

from app.main import app
from app.db.session import get_db
from app.db.models.employee import Employee, Department, Designation
from app.db.models.payroll import (
    PayrollRecord, SalaryStructure, PayrollComponentDefinition,
    PayrollComponent, TaxConfiguration, PayrollStatus, ComponentType
)
from app.services.payroll_service import PayrollService, TaxCalculatorService
from app.core.security import CurrentUser

# Test client
client = TestClient(app)

class TestPayrollIntegration:
    """Comprehensive tests for payroll integration"""
    
    @pytest.fixture
    def db_session(self):
        """Mock database session"""
        # In a real test, you would set up a test database
        pass
    
    @pytest.fixture
    def mock_current_user(self):
        """Mock current user for testing"""
        return CurrentUser(
            user_id=str(uuid4()),
            email="<EMAIL>",
            organization_id=str(uuid4()),
            role="hr",
            permissions=["payroll:read", "payroll:create", "payroll:update", "payroll:process"]
        )
    
    @pytest.fixture
    def sample_employee(self):
        """Sample employee for testing"""
        return Employee(
            id=uuid4(),
            employee_id="EMP001",
            first_name="John",
            last_name="Doe",
            email="<EMAIL>",
            phone="+1234567890",
            hire_date=date.today() - timedelta(days=365),
            is_active=True,
            country="Nigeria",
            tax_type="PAYE"
        )
    
    @pytest.fixture
    def sample_salary_structure(self, sample_employee):
        """Sample salary structure for testing"""
        return SalaryStructure(
            id=uuid4(),
            employee_id=sample_employee.id,
            name="Standard Salary Structure",
            basic_salary=Decimal("100000.00"),
            gross_salary=Decimal("150000.00"),
            effective_from=date.today(),
            is_active=True,
            salary_components=[
                {
                    "name": "Basic Salary",
                    "type": "earning",
                    "amount": 100000,
                    "calculation_method": "fixed",
                    "is_taxable": True
                },
                {
                    "name": "Housing Allowance",
                    "type": "earning",
                    "amount": 30000,
                    "calculation_method": "fixed",
                    "is_taxable": True
                },
                {
                    "name": "Transport Allowance",
                    "type": "earning",
                    "amount": 20000,
                    "calculation_method": "fixed",
                    "is_taxable": False
                },
                {
                    "name": "Pension",
                    "type": "deduction",
                    "percentage": 8,
                    "calculation_method": "percentage",
                    "base": "basic",
                    "is_taxable": False
                }
            ]
        )

class TestTaxCalculatorService:
    """Test the enhanced tax calculator service"""
    
    def test_nigeria_paye_calculation(self):
        """Test Nigeria PAYE tax calculation"""
        calculator = TaxCalculatorService(country="Nigeria", tax_type="PAYE")
        
        # Test case 1: Low income (below first band)
        annual_tax = calculator.calculate_annual_tax(200000, 16000, 0)
        monthly_tax = calculator.calculate_monthly_tax(annual_tax)
        
        # Expected: (200000 - 16000) * 0.07 = 12880
        assert abs(annual_tax - 12880) < 1, f"Expected ~12880, got {annual_tax}"
        assert abs(monthly_tax - (12880/12)) < 1, f"Expected ~{12880/12}, got {monthly_tax}"
        
        # Test case 2: Higher income (multiple bands)
        annual_tax = calculator.calculate_annual_tax(2000000, 160000, 0)
        monthly_tax = calculator.calculate_monthly_tax(annual_tax)
        
        # Should be calculated across multiple tax bands
        assert annual_tax > 0, "Tax should be calculated for higher income"
        assert monthly_tax == annual_tax / 12, "Monthly tax should be annual tax divided by 12"
    
    def test_tax_calculator_fallback(self):
        """Test tax calculator fallback when advanced calculator is not available"""
        calculator = TaxCalculatorService(country="Nigeria", tax_type="PAYE")
        
        # Force fallback by setting calculator to None
        calculator.calculator = None
        
        annual_tax = calculator.calculate_annual_tax(1000000, 80000, 0)
        assert annual_tax > 0, "Fallback calculation should work"

class TestPayrollService:
    """Test the enhanced payroll service"""
    
    @pytest.fixture
    def payroll_service(self):
        return PayrollService()
    
    def test_enhanced_payroll_calculation(self, payroll_service, mock_current_user, sample_employee, sample_salary_structure):
        """Test enhanced payroll calculation with tax integration"""
        # This would be an async test in a real implementation
        # For now, we'll test the calculation logic
        
        # Mock data
        pay_period_start = date(2024, 1, 1)
        pay_period_end = date(2024, 1, 31)
        
        # Test component calculation
        component = {
            "name": "Housing Allowance",
            "type": "earning",
            "amount": 30000,
            "calculation_method": "fixed",
            "is_taxable": True
        }
        
        amount = payroll_service._calculate_component_amount(
            component, 
            Decimal("150000"), 
            Decimal("100000")
        )
        
        assert amount == Decimal("30000"), f"Expected 30000, got {amount}"
        
        # Test percentage calculation
        component_percentage = {
            "name": "Pension",
            "type": "deduction",
            "percentage": 8,
            "calculation_method": "percentage",
            "base": "basic"
        }
        
        amount = payroll_service._calculate_component_amount(
            component_percentage,
            Decimal("150000"),
            Decimal("100000")
        )
        
        assert amount == Decimal("8000"), f"Expected 8000 (8% of 100000), got {amount}"

class TestPayrollAPI:
    """Test payroll API endpoints"""
    
    def test_tax_calculation_endpoint(self):
        """Test the tax calculation API endpoint"""
        response = client.post(
            "/api/payroll/tax/calculate",
            json={
                "annual_earnings": 1000000,
                "annual_pension": 80000,
                "annual_nhf": 0,
                "country": "Nigeria",
                "tax_type": "PAYE"
            },
            headers={"Authorization": "Bearer mock_token"}
        )
        
        # In a real test, you would mock the authentication
        # For now, we expect a 401 due to missing auth
        assert response.status_code in [200, 401], "Endpoint should exist"
    
    def test_payroll_records_endpoint(self):
        """Test getting payroll records"""
        response = client.get(
            "/api/payroll/records",
            headers={"Authorization": "Bearer mock_token"}
        )
        
        # Expect 401 due to missing auth in test
        assert response.status_code in [200, 401], "Endpoint should exist"
    
    def test_salary_templates_endpoint(self):
        """Test salary templates endpoint"""
        response = client.get(
            "/api/payroll/salary-templates",
            headers={"Authorization": "Bearer mock_token"}
        )
        
        assert response.status_code in [200, 401], "Endpoint should exist"

class TestPayrollReports:
    """Test payroll reporting functionality"""
    
    def test_payroll_summary_calculation(self):
        """Test payroll summary calculations"""
        # Mock payroll records
        mock_records = [
            {
                "id": str(uuid4()),
                "employee_id": str(uuid4()),
                "gross_salary": 150000,
                "net_salary": 120000,
                "total_deductions": 30000,
                "total_taxes": 15000,
                "status": "paid"
            },
            {
                "id": str(uuid4()),
                "employee_id": str(uuid4()),
                "gross_salary": 200000,
                "net_salary": 160000,
                "total_deductions": 40000,
                "total_taxes": 20000,
                "status": "paid"
            }
        ]
        
        # Calculate summary
        total_gross = sum(record["gross_salary"] for record in mock_records)
        total_net = sum(record["net_salary"] for record in mock_records)
        total_deductions = sum(record["total_deductions"] for record in mock_records)
        
        assert total_gross == 350000, f"Expected 350000, got {total_gross}"
        assert total_net == 280000, f"Expected 280000, got {total_net}"
        assert total_deductions == 70000, f"Expected 70000, got {total_deductions}"
        
        # Calculate averages
        avg_gross = total_gross / len(mock_records)
        assert avg_gross == 175000, f"Expected 175000, got {avg_gross}"

class TestPaymentIntegration:
    """Test payment gateway integration"""
    
    def test_payment_data_preparation(self):
        """Test payment data preparation for Paystack"""
        # Mock payroll record
        net_salary = Decimal("120000.00")
        employee_email = "<EMAIL>"
        reference = "PAY_123_20241201120000"
        
        # Prepare payment data (as would be done in the service)
        payment_data = {
            "amount": int(float(net_salary) * 100),  # Convert to kobo
            "email": employee_email,
            "reference": reference,
            "currency": "NGN"
        }
        
        assert payment_data["amount"] == 12000000, "Amount should be in kobo"
        assert payment_data["email"] == employee_email, "Email should match"
        assert payment_data["currency"] == "NGN", "Currency should be NGN"
    
    def test_webhook_data_processing(self):
        """Test webhook data processing"""
        # Mock webhook data
        webhook_data = {
            "event": "charge.success",
            "data": {
                "reference": "PAY_123_20241201120000",
                "amount": 12000000,
                "currency": "NGN",
                "status": "success"
            }
        }
        
        # Extract and validate data
        event = webhook_data.get("event")
        reference = webhook_data.get("data", {}).get("reference")
        amount = webhook_data.get("data", {}).get("amount")
        
        assert event == "charge.success", "Event should be charge.success"
        assert reference == "PAY_123_20241201120000", "Reference should match"
        assert amount == 12000000, "Amount should be in kobo"

class TestDataValidation:
    """Test data validation and error handling"""
    
    def test_payroll_data_validation(self):
        """Test payroll data validation"""
        from app.services.payrollService import PayrollService
        
        # Valid data
        valid_data = {
            "employee_id": str(uuid4()),
            "pay_period_start": "2024-01-01",
            "pay_period_end": "2024-01-31",
            "basic_salary": 100000
        }
        
        validation_result = PayrollService().validatePayrollData(valid_data)
        assert validation_result["isValid"] == True, "Valid data should pass validation"
        
        # Invalid data - missing employee_id
        invalid_data = {
            "pay_period_start": "2024-01-01",
            "pay_period_end": "2024-01-31",
            "basic_salary": 100000
        }
        
        validation_result = PayrollService().validatePayrollData(invalid_data)
        assert validation_result["isValid"] == False, "Invalid data should fail validation"
        assert "Employee ID is required" in validation_result["errors"], "Should have specific error message"

# Integration test scenarios
class TestEndToEndScenarios:
    """End-to-end integration test scenarios"""
    
    def test_complete_payroll_cycle(self):
        """Test complete payroll processing cycle"""
        # This would test the entire flow:
        # 1. Create employee with salary structure
        # 2. Process payroll
        # 3. Calculate taxes
        # 4. Generate payslip
        # 5. Approve payroll
        # 6. Initiate payment
        # 7. Handle payment confirmation
        
        # For now, we'll test the flow conceptually
        steps = [
            "create_employee",
            "setup_salary_structure", 
            "process_payroll",
            "calculate_taxes",
            "generate_payslip",
            "approve_payroll",
            "initiate_payment",
            "confirm_payment"
        ]
        
        # Each step should be testable independently
        for step in steps:
            assert step is not None, f"Step {step} should be defined"
    
    def test_bulk_payroll_processing(self):
        """Test bulk payroll processing for multiple employees"""
        # Mock multiple employees
        employee_count = 10
        mock_employees = [
            {
                "id": str(uuid4()),
                "employee_id": f"EMP{i:03d}",
                "basic_salary": 100000 + (i * 10000)
            }
            for i in range(1, employee_count + 1)
        ]
        
        # Test bulk processing logic
        total_payroll = sum(emp["basic_salary"] for emp in mock_employees)
        assert total_payroll > 0, "Total payroll should be calculated"
        assert len(mock_employees) == employee_count, "Should process all employees"

if __name__ == "__main__":
    # Run tests
    pytest.main([__file__, "-v"])
