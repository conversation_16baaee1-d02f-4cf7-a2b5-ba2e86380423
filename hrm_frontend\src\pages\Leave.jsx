/**
 * Enhanced Leave Management Page with Professional UI
 * Features: Dashboard, calendar view, application forms, analytics
 */

import React, { useState, useEffect } from 'react';
import { Calendar, Clock, Plus, Filter, Download, Eye, Edit, CheckCircle, XCircle, AlertCircle, BarChart3, Users } from 'lucide-react';
import { usePermissions } from '../hooks/usePermissions';
import { PermissionGate, ConditionalRender } from '../components/ProtectedRoute';
import apiService from '../services/api';
import LeaveDashboard from '../components/leave/LeaveDashboard';
import LeaveCalendar from '../components/leave/LeaveCalendar';
import LeaveApplicationForm from '../components/leave/LeaveApplicationForm';
import LeavePolicyManager from '../components/leave/LeavePolicyManager';
import LeaveBalanceTracker from '../components/leave/LeaveBalanceTracker';
import LeaveApprovalPanel from '../components/leave/LeaveApprovalPanel';

export default function Leave({ activeTab = 'dashboard' }) {
  const permissions = usePermissions();
  const [selectedLeaveType, setSelectedLeaveType] = useState('all');
  const [selectedStatus, setSelectedStatus] = useState('all');
  const [leaveRequests, setLeaveRequests] = useState([]);
  const [leaveBalance, setLeaveBalance] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [showApplicationForm, setShowApplicationForm] = useState(false);
  const [currentTab, setCurrentTab] = useState(activeTab);

  // Load data from API
  useEffect(() => {
    const loadLeaveData = async () => {
      try {
        setLoading(true);
        setError(null);

        // Load leave requests and balance in parallel
        const [requestsResponse, balanceResponse] = await Promise.all([
          apiService.getLeaveRequests(),
          apiService.getMyLeaveBalance()
        ]);

        setLeaveRequests(requestsResponse.data || []);
        setLeaveBalance(balanceResponse.data || []);
      } catch (err) {
        console.error('Failed to load leave data:', err);
        setError('Failed to load leave data. Please try again.');

        // Fallback to mock data
        setLeaveRequests([
          {
            id: 1,
            type: 'Annual Leave',
            startDate: '2024-01-15',
            endDate: '2024-01-19',
            days: 5,
            status: 'approved',
            reason: 'Family vacation',
            appliedDate: '2024-01-01',
            approver: 'John Manager'
          },
          {
            id: 2,
            type: 'Sick Leave',
            startDate: '2024-01-22',
            endDate: '2024-01-22',
            days: 1,
            status: 'pending',
            reason: 'Medical appointment',
            appliedDate: '2024-01-20',
            approver: 'John Manager'
          }
        ]);

        setLeaveBalance([
          { type: 'Annual Leave', total: 21, used: 5, remaining: 16 },
          { type: 'Sick Leave', total: 10, used: 1, remaining: 9 },
          { type: 'Personal Leave', total: 5, used: 0, remaining: 5 },
          { type: 'Maternity Leave', total: 90, used: 0, remaining: 90 }
        ]);
      } finally {
        setLoading(false);
      }
    };

    loadLeaveData();
  }, []);

  const getStatusColor = (status) => {
    switch (status) {
      case 'approved': return 'text-green-600 bg-green-100';
      case 'pending': return 'text-yellow-600 bg-yellow-100';
      case 'rejected': return 'text-red-600 bg-red-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'approved': return <CheckCircle size={16} />;
      case 'pending': return <AlertCircle size={16} />;
      case 'rejected': return <XCircle size={16} />;
      default: return <Clock size={16} />;
    }
  };

  const renderMyLeaves = () => {
    if (loading) {
      return (
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
        </div>
      );
    }

    if (error) {
      return (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex">
            <AlertCircle className="h-5 w-5 text-red-400" />
            <div className="ml-3">
              <h3 className="text-sm font-medium text-red-800">Error</h3>
              <p className="text-sm text-red-700 mt-1">{error}</p>
            </div>
          </div>
        </div>
      );
    }

    return (
    <div className="space-y-6">
      {/* Leave Balance Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {leaveBalance.map((balance, index) => (
          <div key={index} className="bg-white rounded-lg shadow p-6">
            <h3 className="text-sm font-medium text-gray-500 mb-2">{balance.type}</h3>
            <div className="flex items-center justify-between">
              <div>
                <p className="text-2xl font-bold text-gray-900">{balance.remaining}</p>
                <p className="text-sm text-gray-500">Remaining</p>
              </div>
              <div className="text-right">
                <p className="text-sm text-gray-600">{balance.used} used</p>
                <p className="text-sm text-gray-500">of {balance.total}</p>
              </div>
            </div>
            <div className="mt-3 bg-gray-200 rounded-full h-2">
              <div 
                className="bg-blue-600 h-2 rounded-full" 
                style={{ width: `${(balance.used / balance.total) * 100}%` }}
              ></div>
            </div>
          </div>
        ))}
      </div>

      {/* Actions */}
      <div className="flex justify-between items-center">
        <div className="flex space-x-4">
          <select 
            value={selectedLeaveType} 
            onChange={(e) => setSelectedLeaveType(e.target.value)}
            className="border border-gray-300 rounded-md px-3 py-2"
          >
            <option value="all">All Leave Types</option>
            <option value="annual">Annual Leave</option>
            <option value="sick">Sick Leave</option>
            <option value="personal">Personal Leave</option>
          </select>
          <select 
            value={selectedStatus} 
            onChange={(e) => setSelectedStatus(e.target.value)}
            className="border border-gray-300 rounded-md px-3 py-2"
          >
            <option value="all">All Status</option>
            <option value="pending">Pending</option>
            <option value="approved">Approved</option>
            <option value="rejected">Rejected</option>
          </select>
        </div>
        <PermissionGate permission="leaveRequestsSelf">
          <button className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 flex items-center space-x-2">
            <Plus size={16} />
            <span>Apply Leave</span>
          </button>
        </PermissionGate>
      </div>

      {/* Leave Requests Table */}
      <div className="bg-white rounded-lg shadow overflow-hidden">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Leave Type</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Duration</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Applied Date</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {leaveRequests.map((request) => (
              <tr key={request.id}>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div>
                    <div className="text-sm font-medium text-gray-900">{request.type}</div>
                    <div className="text-sm text-gray-500">{request.reason}</div>
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm text-gray-900">{request.startDate} to {request.endDate}</div>
                  <div className="text-sm text-gray-500">{request.days} day(s)</div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <span className={`inline-flex items-center space-x-1 px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(request.status)}`}>
                    {getStatusIcon(request.status)}
                    <span className="capitalize">{request.status}</span>
                  </span>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  {request.appliedDate}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                  <div className="flex space-x-2">
                    <button className="text-blue-600 hover:text-blue-900">
                      <Eye size={16} />
                    </button>
                    {request.status === 'pending' && (
                      <button className="text-gray-600 hover:text-gray-900">
                        <Edit size={16} />
                      </button>
                    )}
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
    );
  };

  const renderLeaveManagement = () => (
    <PermissionGate permission="leaveManagement">
      <div className="space-y-6">
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Team Leave Requests</h3>
          <p className="text-gray-600">Manage and approve team leave requests...</p>
        </div>
      </div>
    </PermissionGate>
  );

  const renderLeavePolicies = () => (
    <div className="space-y-6">
      <div className="bg-white rounded-lg shadow p-6">
        <h3 className="text-lg font-medium text-gray-900 mb-4">Leave Policies</h3>
        <p className="text-gray-600">View company leave policies and guidelines...</p>
      </div>
    </div>
  );

  const handleLeaveApplication = async (formData) => {
    try {
      console.log('🔄 Leave Page - Handling leave application:', formData);
      console.log('🔄 Leave Page - Duration type received:', formData.duration_type);
      await apiService.createLeaveRequest(formData);
      await loadLeaveData(); // Refresh data
      setShowApplicationForm(false);
    } catch (error) {
      console.error('Error submitting leave application:', error);
      throw error;
    }
  };

  const renderTabNavigation = () => (
    <div className="border-b border-gray-200 mb-6">
      <nav className="-mb-px flex space-x-8">
        {[
          { id: 'dashboard', name: 'Dashboard', icon: BarChart3 },
          { id: 'my-leaves', name: 'My Leaves', icon: Calendar },
          { id: 'balance', name: 'Balance', icon: Clock },
          { id: 'calendar', name: 'Calendar', icon: Calendar },
          { id: 'team-leaves', name: 'Team Leaves', icon: Users, permission: 'leaveManagement' },
          { id: 'approvals', name: 'Approvals', icon: CheckCircle, permission: 'leaveManagement' },
          { id: 'policies', name: 'Policies', icon: Eye, permission: 'leaveManagement' }
        ].map((tab) => {
          if (tab.permission && !permissions.hasPermission(tab.permission)) return null;

          const Icon = tab.icon;
          return (
            <button
              key={tab.id}
              onClick={() => setCurrentTab(tab.id)}
              className={`flex items-center py-2 px-1 border-b-2 font-medium text-sm ${
                currentTab === tab.id
                  ? 'border-agno-primary text-agno-primary'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              <Icon size={16} className="mr-2" />
              {tab.name}
            </button>
          );
        })}
      </nav>
    </div>
  );

  const renderContent = () => {
    switch (currentTab) {
      case 'dashboard':
        return <LeaveDashboard />;
      case 'my-leaves':
        return renderMyLeaves();
      case 'balance':
        return <LeaveBalanceTracker />;
      case 'calendar':
        return <LeaveCalendar onApplyLeave={() => setShowApplicationForm(true)} />;
      case 'team-leaves':
        return renderLeaveManagement();
      case 'approvals':
        return <LeaveApprovalPanel />;
      case 'policies':
        return <LeavePolicyManager />;
      default:
        return <LeaveDashboard />;
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {renderTabNavigation()}
        {renderContent()}

        {/* Leave Application Form Modal */}
        {showApplicationForm && (
          <LeaveApplicationForm
            onClose={() => setShowApplicationForm(false)}
            onSubmit={handleLeaveApplication}
          />
        )}
      </div>
    </div>
  );
}
