"""
Dashboard Service for Leave Management
"""

from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, func, extract
from typing import Optional, List, Dict
from uuid import UUID
from datetime import datetime, date, timedelta
from fastapi import HTTPException, status
import logging
from calendar import monthrange

from ...db.models.leave import LeaveRequest, LeavePolicy, LeaveBalance, LeaveCalendar, LeaveEncashment
from ...db.models.employee import Employee, Department
from ...db.models.attendance import AttendanceRecord
from ...schemas.leave import LeaveStatus
from ...core.security import CurrentUser

logger = logging.getLogger(__name__)


class DashboardService:
    """Dashboard service for role-based leave management dashboards"""

    async def get_employee_dashboard(
        self,
        db: Session,
        current_user: CurrentUser
    ) -> Dict:
        """Get employee dashboard data"""
        try:
            employee_id = UUID(current_user.user_id)
            current_year = date.today().year
            
            # Get leave balances
            balances = db.query(LeaveBalance).join(LeavePolicy).filter(
                LeaveBalance.employee_id == employee_id,
                LeaveBalance.year == current_year
            ).all()
            
            leave_balances = [
                {
                    "leave_type": balance.leave_policy.leave_type,
                    "policy_name": balance.leave_policy.name,
                    "total_entitlement": float(balance.leave_policy.annual_entitlement),
                    "available_balance": float(balance.available_balance),
                    "used_balance": float(balance.used_balance),
                    "pending_balance": float(balance.pending_balance),
                    "carried_forward": float(balance.carried_forward)
                }
                for balance in balances
            ]
            
            # Get recent leave requests
            recent_requests = db.query(LeaveRequest).filter(
                LeaveRequest.employee_id == employee_id
            ).order_by(LeaveRequest.applied_at.desc()).limit(5).all()
            
            leave_requests = [
                {
                    "id": str(req.id),
                    "start_date": req.start_date.isoformat(),
                    "end_date": req.end_date.isoformat(),
                    "total_days": float(req.total_days),
                    "status": req.status.value,
                    "leave_type": req.leave_policy.leave_type if req.leave_policy else "Unknown",
                    "applied_at": req.applied_at.isoformat()
                }
                for req in recent_requests
            ]
            
            # Get upcoming holidays
            upcoming_holidays = db.query(LeaveCalendar).filter(
                LeaveCalendar.organization_id == current_user.organization_id,
                LeaveCalendar.date >= date.today(),
                LeaveCalendar.date <= date.today() + timedelta(days=90)
            ).order_by(LeaveCalendar.date).limit(10).all()
            
            holidays = [
                {
                    "date": holiday.date.isoformat(),
                    "name": holiday.name,
                    "type": holiday.holiday_type,
                    "is_optional": holiday.is_optional
                }
                for holiday in upcoming_holidays
            ]
            
            # Get attendance summary for current month
            month_start = date.today().replace(day=1)
            attendance_summary = await self._get_attendance_summary(
                db, employee_id, month_start, date.today()
            )
            
            return {
                "employee_id": str(employee_id),
                "dashboard_type": "employee",
                "leave_balances": leave_balances,
                "recent_requests": leave_requests,
                "upcoming_holidays": holidays,
                "attendance_summary": attendance_summary,
                "quick_stats": {
                    "total_available_days": sum(float(b.available_balance) for b in balances),
                    "pending_requests": len([r for r in recent_requests if r.status == LeaveStatus.PENDING]),
                    "approved_requests_this_year": len([r for r in recent_requests if r.status == LeaveStatus.APPROVED])
                }
            }
            
        except Exception as e:
            logger.error(f"Error getting employee dashboard: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Error retrieving employee dashboard"
            )

    async def get_manager_dashboard(
        self,
        db: Session,
        current_user: CurrentUser
    ) -> Dict:
        """Get manager dashboard data"""
        try:
            manager_id = UUID(current_user.user_id)
            
            # Get team members
            team_members = db.query(Employee).filter(
                Employee.manager_id == manager_id,
                Employee.organization_id == current_user.organization_id,
                Employee.is_active == True
            ).all()
            
            team_member_ids = [member.id for member in team_members]
            
            # Get pending leave requests for team
            pending_requests = db.query(LeaveRequest).filter(
                LeaveRequest.employee_id.in_(team_member_ids),
                LeaveRequest.status == LeaveStatus.PENDING
            ).all()
            
            pending_leave_requests = [
                {
                    "id": str(req.id),
                    "employee_name": f"{req.employee.first_name} {req.employee.last_name}",
                    "employee_id": req.employee.employee_id,
                    "start_date": req.start_date.isoformat(),
                    "end_date": req.end_date.isoformat(),
                    "total_days": float(req.total_days),
                    "leave_type": req.leave_policy.leave_type if req.leave_policy else "Unknown",
                    "reason": req.reason,
                    "applied_at": req.applied_at.isoformat()
                }
                for req in pending_requests
            ]
            
            # Get team leave calendar for next 30 days
            team_calendar = await self._get_team_leave_calendar(
                db, team_member_ids, date.today(), date.today() + timedelta(days=30)
            )
            
            # Get team attendance summary
            team_attendance = await self._get_team_attendance_summary(
                db, team_member_ids, current_user
            )
            
            # Get staffing impact analysis
            staffing_analysis = await self._get_staffing_analysis(
                db, team_member_ids, current_user
            )
            
            return {
                "manager_id": str(manager_id),
                "dashboard_type": "manager",
                "team_size": len(team_members),
                "pending_requests": pending_leave_requests,
                "team_calendar": team_calendar,
                "team_attendance": team_attendance,
                "staffing_analysis": staffing_analysis,
                "quick_stats": {
                    "pending_approvals": len(pending_requests),
                    "team_on_leave_today": len([cal for cal in team_calendar if cal["date"] == date.today().isoformat() and cal["type"] == "leave"]),
                    "upcoming_leaves": len([cal for cal in team_calendar if cal["type"] == "leave"])
                }
            }
            
        except Exception as e:
            logger.error(f"Error getting manager dashboard: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Error retrieving manager dashboard"
            )

    async def get_hr_dashboard(
        self,
        db: Session,
        current_user: CurrentUser
    ) -> Dict:
        """Get HR dashboard data"""
        try:
            # Get organization-wide statistics
            org_stats = await self._get_organization_stats(db, current_user)
            
            # Get all pending requests
            all_pending = db.query(LeaveRequest).join(Employee).filter(
                Employee.organization_id == current_user.organization_id,
                LeaveRequest.status == LeaveStatus.PENDING
            ).all()
            
            # Get leave trends
            leave_trends = await self._get_leave_trends(db, current_user)
            
            # Get policy utilization
            policy_utilization = await self._get_policy_utilization(db, current_user)
            
            # Get upcoming expirations
            upcoming_expirations = await self._get_upcoming_expirations(db, current_user)
            
            # Get encashment requests
            encashment_requests = db.query(LeaveEncashment).join(Employee).filter(
                Employee.organization_id == current_user.organization_id,
                LeaveEncashment.status == LeaveStatus.PENDING
            ).all()
            
            return {
                "dashboard_type": "hr",
                "organization_stats": org_stats,
                "pending_requests_count": len(all_pending),
                "pending_encashments_count": len(encashment_requests),
                "leave_trends": leave_trends,
                "policy_utilization": policy_utilization,
                "upcoming_expirations": upcoming_expirations,
                "quick_stats": {
                    "total_employees": org_stats.get("total_employees", 0),
                    "on_leave_today": org_stats.get("on_leave_today", 0),
                    "pending_approvals": len(all_pending),
                    "leaves_expiring_soon": len(upcoming_expirations)
                }
            }
            
        except Exception as e:
            logger.error(f"Error getting HR dashboard: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Error retrieving HR dashboard"
            )

    async def _get_attendance_summary(
        self,
        db: Session,
        employee_id: UUID,
        start_date: date,
        end_date: date
    ) -> Dict:
        """Get attendance summary for employee"""
        try:
            records = db.query(AttendanceRecord).filter(
                AttendanceRecord.employee_id == employee_id,
                AttendanceRecord.date >= start_date,
                AttendanceRecord.date <= end_date
            ).all()
            
            total_days = (end_date - start_date).days + 1
            present_days = len([r for r in records if r.status == "present"])
            total_hours = sum(float(r.total_hours_worked or 0) for r in records)
            overtime_hours = sum(float(r.overtime_hours or 0) for r in records)
            
            return {
                "period": {
                    "start_date": start_date.isoformat(),
                    "end_date": end_date.isoformat()
                },
                "total_days": total_days,
                "present_days": present_days,
                "absent_days": total_days - present_days,
                "total_hours_worked": total_hours,
                "overtime_hours": overtime_hours,
                "attendance_percentage": round((present_days / total_days) * 100, 2) if total_days > 0 else 0
            }
            
        except Exception as e:
            logger.error(f"Error getting attendance summary: {e}")
            return {}

    async def _get_team_leave_calendar(
        self,
        db: Session,
        team_member_ids: List[UUID],
        start_date: date,
        end_date: date
    ) -> List[Dict]:
        """Get team leave calendar"""
        try:
            leave_requests = db.query(LeaveRequest).filter(
                LeaveRequest.employee_id.in_(team_member_ids),
                LeaveRequest.status == LeaveStatus.APPROVED,
                LeaveRequest.start_date <= end_date,
                LeaveRequest.end_date >= start_date
            ).all()
            
            calendar_events = []
            for req in leave_requests:
                current_date = max(req.start_date, start_date)
                end_req_date = min(req.end_date, end_date)
                
                while current_date <= end_req_date:
                    calendar_events.append({
                        "date": current_date.isoformat(),
                        "employee_name": f"{req.employee.first_name} {req.employee.last_name}",
                        "employee_id": req.employee.employee_id,
                        "leave_type": req.leave_policy.leave_type if req.leave_policy else "Unknown",
                        "type": "leave",
                        "duration": req.duration_type.value if req.duration_type else "full_day"
                    })
                    current_date += timedelta(days=1)
            
            return sorted(calendar_events, key=lambda x: x["date"])
            
        except Exception as e:
            logger.error(f"Error getting team leave calendar: {e}")
            return []

    async def _get_organization_stats(
        self,
        db: Session,
        current_user: CurrentUser
    ) -> Dict:
        """Get organization-wide statistics"""
        try:
            total_employees = db.query(Employee).filter(
                Employee.organization_id == current_user.organization_id,
                Employee.is_active == True
            ).count()
            
            # Employees on leave today
            today = date.today()
            on_leave_today = db.query(LeaveRequest).join(Employee).filter(
                Employee.organization_id == current_user.organization_id,
                LeaveRequest.status == LeaveStatus.APPROVED,
                LeaveRequest.start_date <= today,
                LeaveRequest.end_date >= today
            ).count()
            
            return {
                "total_employees": total_employees,
                "on_leave_today": on_leave_today,
                "available_today": total_employees - on_leave_today
            }
            
        except Exception as e:
            logger.error(f"Error getting organization stats: {e}")
            return {}

    async def _get_leave_trends(
        self,
        db: Session,
        current_user: CurrentUser
    ) -> List[Dict]:
        """Get leave trends by month"""
        try:
            # Get leave requests for current year by month
            current_year = date.today().year
            trends = []
            
            for month in range(1, 13):
                month_requests = db.query(LeaveRequest).join(Employee).filter(
                    Employee.organization_id == current_user.organization_id,
                    extract('year', LeaveRequest.start_date) == current_year,
                    extract('month', LeaveRequest.start_date) == month,
                    LeaveRequest.status == LeaveStatus.APPROVED
                ).count()
                
                trends.append({
                    "month": month,
                    "month_name": date(current_year, month, 1).strftime("%B"),
                    "leave_requests": month_requests
                })
            
            return trends
            
        except Exception as e:
            logger.error(f"Error getting leave trends: {e}")
            return []

    async def _get_policy_utilization(
        self,
        db: Session,
        current_user: CurrentUser
    ) -> List[Dict]:
        """Get leave policy utilization"""
        try:
            policies = db.query(LeavePolicy).filter(
                LeavePolicy.organization_id == current_user.organization_id,
                LeavePolicy.is_active == True
            ).all()
            
            utilization = []
            for policy in policies:
                total_entitlement = db.query(func.sum(LeaveBalance.accrued_balance)).filter(
                    LeaveBalance.leave_policy_id == policy.id,
                    LeaveBalance.year == date.today().year
                ).scalar() or 0
                
                total_used = db.query(func.sum(LeaveBalance.used_balance)).filter(
                    LeaveBalance.leave_policy_id == policy.id,
                    LeaveBalance.year == date.today().year
                ).scalar() or 0
                
                utilization_rate = (float(total_used) / float(total_entitlement)) * 100 if total_entitlement > 0 else 0
                
                utilization.append({
                    "policy_name": policy.name,
                    "leave_type": policy.leave_type,
                    "total_entitlement": float(total_entitlement),
                    "total_used": float(total_used),
                    "utilization_rate": round(utilization_rate, 2)
                })
            
            return utilization
            
        except Exception as e:
            logger.error(f"Error getting policy utilization: {e}")
            return []

    async def _get_upcoming_expirations(
        self,
        db: Session,
        current_user: CurrentUser
    ) -> List[Dict]:
        """Get upcoming leave expirations"""
        try:
            # Get balances that will expire at year end
            year_end = date(date.today().year, 12, 31)
            
            balances = db.query(LeaveBalance).join(LeavePolicy).join(Employee).filter(
                Employee.organization_id == current_user.organization_id,
                LeaveBalance.year == date.today().year,
                LeaveBalance.available_balance > 0,
                or_(
                    LeavePolicy.max_carry_forward.is_(None),
                    LeaveBalance.available_balance > LeavePolicy.max_carry_forward
                )
            ).all()
            
            expirations = []
            for balance in balances:
                expiring_days = float(balance.available_balance)
                if balance.leave_policy.max_carry_forward:
                    expiring_days = max(0, float(balance.available_balance) - float(balance.leave_policy.max_carry_forward))
                
                if expiring_days > 0:
                    expirations.append({
                        "employee_name": f"{balance.employee.first_name} {balance.employee.last_name}",
                        "employee_id": balance.employee.employee_id,
                        "leave_type": balance.leave_policy.leave_type,
                        "expiring_days": expiring_days,
                        "expiry_date": year_end.isoformat()
                    })
            
            return expirations
            
        except Exception as e:
            logger.error(f"Error getting upcoming expirations: {e}")
            return []


# Create service instance
dashboard_service = DashboardService()
