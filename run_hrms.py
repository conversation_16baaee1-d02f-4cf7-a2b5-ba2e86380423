#!/usr/bin/env python3
"""
HRMS Unified Application Launcher
Combines both frontend and backend into a single application
"""

import os
import sys
import subprocess
import threading
import time
import signal
import webbrowser
from pathlib import Path

class HRMSLauncher:
    def __init__(self):
        self.backend_process = None
        self.frontend_process = None
        self.base_dir = Path(__file__).parent
        self.backend_dir = self.base_dir / "hrms_backend"
        self.frontend_dir = self.base_dir / "hrm_frontend"
        
    def check_dependencies(self):
        """Check if all required dependencies are available"""
        print("🔍 Checking dependencies...")
        
        # Check Python
        try:
            python_version = sys.version_info
            if python_version.major < 3 or (python_version.major == 3 and python_version.minor < 8):
                print("❌ Python 3.8+ is required")
                return False
            print(f"✅ Python {python_version.major}.{python_version.minor}.{python_version.micro}")
        except Exception as e:
            print(f"❌ Python check failed: {e}")
            return False
        
        # Check Node.js
        try:
            result = subprocess.run(['node', '--version'], capture_output=True, text=True)
            if result.returncode == 0:
                print(f"✅ Node.js {result.stdout.strip()}")
            else:
                print("❌ Node.js is not installed")
                return False
        except FileNotFoundError:
            print("❌ Node.js is not installed")
            return False
        
        # Check npm
        try:
            # Try different npm commands for different platforms
            npm_commands = ['npm', 'npm.cmd']
            npm_found = False

            for npm_cmd in npm_commands:
                try:
                    result = subprocess.run([npm_cmd, '--version'], capture_output=True, text=True)
                    if result.returncode == 0:
                        print(f"✅ npm {result.stdout.strip()}")
                        npm_found = True
                        break
                except FileNotFoundError:
                    continue

            if not npm_found:
                print("❌ npm is not available")
                return False
        except Exception as e:
            print(f"❌ npm check failed: {e}")
            return False
        
        # Check directories
        if not self.backend_dir.exists():
            print(f"❌ Backend directory not found: {self.backend_dir}")
            return False
        print(f"✅ Backend directory: {self.backend_dir}")
        
        if not self.frontend_dir.exists():
            print(f"❌ Frontend directory not found: {self.frontend_dir}")
            return False
        print(f"✅ Frontend directory: {self.frontend_dir}")
        
        return True
    
    def install_dependencies(self):
        """Install Python and Node.js dependencies"""
        print("\n📦 Installing dependencies...")
        
        # Install Python dependencies
        print("Installing Python dependencies...")
        try:
            subprocess.run([
                sys.executable, '-m', 'pip', 'install', '-r', 'requirements.txt'
            ], cwd=self.backend_dir, check=True)
            print("✅ Python dependencies installed")
        except subprocess.CalledProcessError as e:
            print(f"❌ Failed to install Python dependencies: {e}")
            return False
        
        # Install Node.js dependencies
        print("Installing Node.js dependencies...")
        try:
            # Try different npm commands for different platforms
            npm_commands = ['npm', 'npm.cmd']
            npm_success = False

            for npm_cmd in npm_commands:
                try:
                    subprocess.run([npm_cmd, 'install'], cwd=self.frontend_dir, check=True)
                    npm_success = True
                    break
                except (subprocess.CalledProcessError, FileNotFoundError):
                    continue

            if npm_success:
                print("✅ Node.js dependencies installed")
            else:
                print("❌ Failed to install Node.js dependencies")
                return False
        except Exception as e:
            print(f"❌ Failed to install Node.js dependencies: {e}")
            return False
        
        return True
    
    def start_backend(self):
        """Start the FastAPI backend server"""
        print("🚀 Starting backend server...")
        try:
            self.backend_process = subprocess.Popen([
                sys.executable, '-m', 'uvicorn', 'app.main:app',
                '--host', '0.0.0.0',
                '--port', '8085',
                '--reload'
            ], cwd=self.backend_dir)
            
            # Wait a moment for the server to start
            time.sleep(3)
            
            if self.backend_process.poll() is None:
                print("✅ Backend server started on http://localhost:8085")
                return True
            else:
                print("❌ Backend server failed to start")
                return False
        except Exception as e:
            print(f"❌ Failed to start backend: {e}")
            return False
    
    def start_frontend(self):
        """Start the React frontend development server"""
        print("🎨 Starting frontend server...")
        try:
            # Try different npm commands for different platforms
            npm_commands = ['npm', 'npm.cmd']
            frontend_started = False

            for npm_cmd in npm_commands:
                try:
                    self.frontend_process = subprocess.Popen([
                        npm_cmd, 'run', 'dev'
                    ], cwd=self.frontend_dir)
                    frontend_started = True
                    break
                except FileNotFoundError:
                    continue

            if not frontend_started:
                print("❌ Could not start frontend with any npm command")
                return False
            
            # Wait a moment for the server to start
            time.sleep(5)
            
            if self.frontend_process.poll() is None:
                print("✅ Frontend server started on http://localhost:5173")
                return True
            else:
                print("❌ Frontend server failed to start")
                return False
        except Exception as e:
            print(f"❌ Failed to start frontend: {e}")
            return False
    
    def wait_for_servers(self):
        """Wait for both servers to be ready"""
        print("\n⏳ Waiting for servers to be ready...")
        
        # Check backend health
        backend_ready = False
        for i in range(30):  # Wait up to 30 seconds
            try:
                import requests
                response = requests.get("http://localhost:8085/health", timeout=2)
                if response.status_code == 200:
                    backend_ready = True
                    break
            except:
                pass
            time.sleep(1)
        
        if backend_ready:
            print("✅ Backend server is ready")
        else:
            print("⚠️  Backend server may not be fully ready")
        
        # Check frontend (just wait a bit more)
        time.sleep(3)
        print("✅ Frontend server should be ready")
    
    def open_browser(self):
        """Open the application in the default browser"""
        print("\n🌐 Opening application in browser...")
        try:
            webbrowser.open("http://localhost:5173")
            print("✅ Browser opened")
        except Exception as e:
            print(f"⚠️  Could not open browser automatically: {e}")
            print("Please manually open: http://localhost:5173")
    
    def cleanup(self):
        """Clean up processes"""
        print("\n🧹 Cleaning up...")
        
        if self.backend_process:
            try:
                self.backend_process.terminate()
                self.backend_process.wait(timeout=5)
                print("✅ Backend server stopped")
            except:
                try:
                    self.backend_process.kill()
                    print("✅ Backend server force stopped")
                except:
                    print("⚠️  Could not stop backend server")
        
        if self.frontend_process:
            try:
                self.frontend_process.terminate()
                self.frontend_process.wait(timeout=5)
                print("✅ Frontend server stopped")
            except:
                try:
                    self.frontend_process.kill()
                    print("✅ Frontend server force stopped")
                except:
                    print("⚠️  Could not stop frontend server")
    
    def run(self, install_deps=False):
        """Run the complete HRMS application"""
        print("🏢 HRMS - Human Resource Management System")
        print("=" * 50)
        
        # Check dependencies
        if not self.check_dependencies():
            print("\n❌ Dependency check failed. Please install required dependencies.")
            return False
        
        # Install dependencies if requested
        if install_deps:
            if not self.install_dependencies():
                print("\n❌ Dependency installation failed.")
                return False
        
        try:
            # Start backend
            if not self.start_backend():
                return False
            
            # Start frontend
            if not self.start_frontend():
                self.cleanup()
                return False
            
            # Wait for servers to be ready
            self.wait_for_servers()
            
            # Open browser
            self.open_browser()
            
            # Show status
            print("\n🎉 HRMS Application is running!")
            print("=" * 50)
            print("📊 Backend API: http://localhost:8085")
            print("🎨 Frontend UI: http://localhost:5173")
            print("📚 API Docs: http://localhost:8085/docs")
            print("\n💡 Features Available:")
            print("   • Employee Management")
            print("   • Quick Onboarding")
            print("   • Attendance Tracking")
            print("   • Leave Management")
            print("   • Payroll Processing")
            print("   • Project Management")
            print("   • Ticket Management")
            print("   • Performance Reviews")
            print("   • Reports & Analytics")
            print("\n🔐 Default Login Credentials:")
            print("   Admin: <EMAIL> / password123")
            print("   HR: <EMAIL> / password123")
            print("   Manager: <EMAIL> / password123")
            print("\n⚠️  Press Ctrl+C to stop the application")
            
            # Keep running until interrupted
            try:
                while True:
                    time.sleep(1)
                    # Check if processes are still running
                    if self.backend_process.poll() is not None:
                        print("\n❌ Backend server stopped unexpectedly")
                        break
                    if self.frontend_process.poll() is not None:
                        print("\n❌ Frontend server stopped unexpectedly")
                        break
            except KeyboardInterrupt:
                print("\n\n🛑 Shutting down HRMS application...")
            
        except Exception as e:
            print(f"\n❌ Error running application: {e}")
        finally:
            self.cleanup()
        
        print("\n👋 HRMS application stopped. Thank you for using our system!")
        return True

def main():
    """Main entry point"""
    import argparse
    
    parser = argparse.ArgumentParser(description="HRMS Unified Application Launcher")
    parser.add_argument("--install", action="store_true", help="Install dependencies before running")
    parser.add_argument("--check", action="store_true", help="Only check dependencies")
    
    args = parser.parse_args()
    
    launcher = HRMSLauncher()
    
    if args.check:
        launcher.check_dependencies()
        return
    
    launcher.run(install_deps=args.install)

if __name__ == "__main__":
    main()
