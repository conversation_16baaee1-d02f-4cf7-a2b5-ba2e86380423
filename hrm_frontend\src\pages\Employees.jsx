/**
 * Employees Page with RBAC Integration
 * Displays employee directory with role-based access control
 */

import React, { useState, useEffect } from 'react';
import { Search, Filter, Plus, Edit, Eye, MoreVertical } from 'lucide-react';
import { usePermissions } from '../hooks/usePermissions';
import { PermissionGate, ConditionalRender } from '../components/ProtectedRoute';
import apiService from '../services/api';

export default function Employees({ activeTab = 'directory' }) {
  const permissions = usePermissions();
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedDepartment, setSelectedDepartment] = useState('all');
  const [employees, setEmployees] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Fetch employees from API
  useEffect(() => {
    const fetchEmployees = async () => {
      try {
        setLoading(true);
        setError(null);
        const response = await apiService.getEmployees({
          search: searchTerm,
          department: selectedDepartment !== 'all' ? selectedDepartment : undefined
        });

        // Handle the response format from backend
        if (response && response.employees) {
          setEmployees(response.employees);
        } else if (Array.isArray(response)) {
          setEmployees(response);
        } else {
          setEmployees([]);
        }
      } catch (err) {
        console.error('Error fetching employees:', err);
        setError('Failed to load employees. Please try again.');
        setEmployees([]);
      } finally {
        setLoading(false);
      }
    };

    fetchEmployees();
  }, [searchTerm, selectedDepartment]);

  const departments = ['Engineering', 'Marketing', 'HR', 'Sales', 'Finance'];

  const filteredEmployees = employees.filter(employee => {
    const fullName = `${employee.first_name || ''} ${employee.last_name || ''}`.trim();
    const matchesSearch = fullName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         (employee.email || '').toLowerCase().includes(searchTerm.toLowerCase()) ||
                         (employee.employee_id || '').toLowerCase().includes(searchTerm.toLowerCase());
    const matchesDepartment = selectedDepartment === 'all' ||
                             (employee.department && employee.department.toLowerCase() === selectedDepartment.toLowerCase());
    return matchesSearch && matchesDepartment;
  });

  const renderDirectoryTab = () => (
    <div className="space-y-6">
      {/* Header with search and filters */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Employee Directory</h1>
          <p className="text-gray-600">Manage and view employee information</p>
        </div>
        
        <PermissionGate permission="employeeDirectory">
          <button className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center gap-2">
            <Plus size={16} />
            Add Employee
          </button>
        </PermissionGate>
      </div>

      {/* Search and filters */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={16} />
          <input
            type="text"
            placeholder="Search employees..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
        </div>
        
        <select
          value={selectedDepartment}
          onChange={(e) => setSelectedDepartment(e.target.value)}
          className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
        >
          <option value="all">All Departments</option>
          {departments.map(dept => (
            <option key={dept} value={dept}>{dept}</option>
          ))}
        </select>
      </div>

      {/* Loading state */}
      {loading && (
        <div className="text-center py-12">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="text-gray-500 mt-4">Loading employees...</p>
        </div>
      )}

      {/* Error state */}
      {error && (
        <div className="text-center py-12">
          <div className="bg-red-50 border border-red-200 rounded-lg p-4 max-w-md mx-auto">
            <p className="text-red-600">{error}</p>
            <button
              onClick={() => window.location.reload()}
              className="mt-2 text-red-600 hover:text-red-800 underline"
            >
              Try again
            </button>
          </div>
        </div>
      )}

      {/* Employee grid */}
      {!loading && !error && (
        <>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredEmployees.map(employee => (
              <EmployeeCard key={employee.id} employee={employee} />
            ))}
          </div>

          {filteredEmployees.length === 0 && employees.length > 0 && (
            <div className="text-center py-12">
              <p className="text-gray-500">No employees found matching your criteria.</p>
            </div>
          )}

          {employees.length === 0 && (
            <div className="text-center py-12">
              <p className="text-gray-500">No employees found. Add some employees to get started.</p>
            </div>
          )}
        </>
      )}
    </div>
  );

  const renderProfilesTab = () => (
    <PermissionGate permission="profileManagement">
      <div className="space-y-6">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Profile Management</h1>
          <p className="text-gray-600">Manage employee profiles and information</p>
        </div>
        
        <ConditionalRender
          permission="profileManagement"
          fullAccess={
            <div className="bg-white rounded-lg shadow p-6">
              <h3 className="text-lg font-semibold mb-4">Full Profile Management</h3>
              <p className="text-gray-600">You have full access to manage all employee profiles.</p>
            </div>
          }
          teamAccess={
            <div className="bg-white rounded-lg shadow p-6">
              <h3 className="text-lg font-semibold mb-4">Team Profile Management</h3>
              <p className="text-gray-600">You can manage profiles for your team members only.</p>
            </div>
          }
        />
      </div>
    </PermissionGate>
  );

  const renderOnboardingTab = () => (
    <PermissionGate permission="employeeDirectory">
      <div className="space-y-6">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Employee Onboarding</h1>
          <p className="text-gray-600">Manage new employee onboarding process</p>
        </div>
        
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-semibold mb-4">Onboarding Pipeline</h3>
          <p className="text-gray-600">Track and manage the onboarding process for new hires.</p>
        </div>
      </div>
    </PermissionGate>
  );

  // Render content based on active tab
  const renderContent = () => {
    switch (activeTab) {
      case 'profiles':
        return renderProfilesTab();
      case 'onboarding':
        return renderOnboardingTab();
      case 'directory':
      default:
        return renderDirectoryTab();
    }
  };

  return (
    <div className="p-6">
      {renderContent()}
    </div>
  );
}

// Employee card component
function EmployeeCard({ employee }) {
  const permissions = usePermissions();
  const [showActions, setShowActions] = useState(false);

  // Handle different data structures from backend
  const fullName = `${employee.first_name || ''} ${employee.last_name || ''}`.trim() || employee.name || 'Unknown';
  const initials = fullName.split(' ').map(n => n[0]).join('').toUpperCase();
  const position = employee.position || employee.designation || 'N/A';
  const department = employee.department || 'N/A';
  const status = employee.is_active ? 'active' : 'inactive';

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow">
      <div className="flex items-start justify-between">
        <div className="flex items-center space-x-4">
          <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
            <span className="text-blue-600 font-semibold">
              {initials}
            </span>
          </div>
          <div>
            <h3 className="font-semibold text-gray-900">{fullName}</h3>
            <p className="text-sm text-gray-600">{position}</p>
            <p className="text-sm text-gray-500">{department}</p>
            {employee.employee_id && (
              <p className="text-xs text-gray-400">ID: {employee.employee_id}</p>
            )}
          </div>
        </div>
        
        <div className="relative">
          <button
            onClick={() => setShowActions(!showActions)}
            className="text-gray-400 hover:text-gray-600"
          >
            <MoreVertical size={16} />
          </button>
          
          {showActions && (
            <div className="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg z-10 border">
              <div className="py-1">
                <button className="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 w-full">
                  <Eye size={14} className="mr-2" />
                  View Profile
                </button>
                
                <PermissionGate permission="profileManagement" hideOnDenied={true}>
                  <button className="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 w-full">
                    <Edit size={14} className="mr-2" />
                    Edit Profile
                  </button>
                </PermissionGate>
              </div>
            </div>
          )}
        </div>
      </div>
      
      <div className="mt-4 pt-4 border-t border-gray-100">
        <div className="flex items-center justify-between text-sm">
          <span className="text-gray-500">Status</span>
          <span className={`px-2 py-1 rounded-full text-xs ${
            status === 'active'
              ? 'bg-green-100 text-green-800'
              : 'bg-gray-100 text-gray-800'
          }`}>
            {status}
          </span>
        </div>
        {employee.email && (
          <div className="flex items-center justify-between text-sm mt-2">
            <span className="text-gray-500">Email</span>
            <span className="text-gray-700 text-xs">{employee.email}</span>
          </div>
        )}
      </div>
    </div>
  );
}
