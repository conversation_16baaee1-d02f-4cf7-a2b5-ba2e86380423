from core.models.prorate_salary import ProrateSalaryModel
from core.repositories.prorate_salary import ProrateSalaryRepository
import db
from sqlalchemy import extract


class ProrateSalaryService:
    def __init__(self) -> None:
        self.repository = ProrateSalaryRepository()
        
    def create_prorate_salary(self, kwargs):
        """
        Create a new prorate salary record using the provided keyword arguments.
        
        This should include the employee_id to tie the record to the specific employee.
        """
        # You can also add additional logic here if needed
        return self.repository.create_prorate_salary(**kwargs)

    # def create_prorate_salary(self, kwargs):
    #     # Create a new prorate salary record using the provided keyword arguments
    #     return self.repository.create_prorate_salary(**kwargs)
    
    
    # def get_prorate_salary_by_id(employee_id):
    #     # Query for the prorated salary based on the employee_id
    #     return ProrateSalaryModel.query.filter_by(employee_id=employee_id).first()
    
    def get_prorate_salary_by_id(self, employee_id):
        """
        Get prorated salary details for a specific employee.
        
        :param employee_id: The ID of the employee.
        :return: ProrateSalaryModel instance or None if not found.
        """
        return self.repository.get_by_employee_id(employee_id)

    def get_all_prorate_salaries(self):
        # Fetch all prorate salary records and count them
        prorated_salaries = self.repository.fetch_all()  # Make sure this returns a list
        total_count = len(prorated_salaries)  # Get the count of records
        return prorated_salaries, total_count  # Return both
    
    def get_prorate_salaries_by_user(self, user_id):
        # Query to filter prorated salaries by user_id
        prorated_salaries = ProrateSalaryModel.query.filter_by(user_id=user_id).all()
        total_count = len(prorated_salaries)

        return prorated_salaries, total_count
    
        
    def get_most_recent_prorate_salary_by_employee_id(self, employee_id):
        """
        Retrieve the most recent prorated salary record for a given employee ID
        using the 'timestamp' field.
        """
        return self.repository.get_most_recent_by_employee_id(employee_id)
        
    # def get_latest_prorate_salary_for_month(self, employee_id, month, year):
    #     """
    #     Get the most recent prorate salary record for a specific employee in the given month and year.
        
    #     :param employee_id: ID of the employee.
    #     :param month: Month for which to fetch the prorated salary.
    #     :param year: Year for which to fetch the prorated salary.
    #     :return: The most recent ProrateSalaryModel instance or None if not found.
    #     """
    #     return self.repository.get_latest_by_employee_month_year(employee_id, month, year)
    
    def get_latest_prorate_salary_for_month(self, employee_id, month, year):
        """
        Get the most recent prorate salary record for an employee for a specific month and year.
        
        :param employee_id: ID of the employee.
        :param month: Month for which to fetch the prorated salary.
        :param year: Year for which to fetch the prorated salary.
        :return: The most recent ProrateSalaryModel instance or None if not found.
        """
        return (
            ProrateSalaryModel.query
            .filter(
                ProrateSalaryModel.employee_id == employee_id,
                extract('month', ProrateSalaryModel.timestamp) == month,
                extract('year', ProrateSalaryModel.timestamp) == year
            )
            .order_by(ProrateSalaryModel.timestamp.desc())
            .first()
        )

    