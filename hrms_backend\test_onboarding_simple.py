"""
Simple Onboarding System Test
Test the onboarding email service directly
"""

import asyncio
import sys
import os
from datetime import datetime, date, timedelta

# Add the app directory to the Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'app'))

from app.services.hr_management.onboarding_email_service import OnboardingEmailService

# Mock employee class for testing
class MockEmployee:
    def __init__(self):
        self.id = "test-employee-id"
        self.employee_id = "EMP001"
        self.first_name = "<PERSON>"
        self.last_name = "<PERSON>e"
        self.email = "<EMAIL>"
        self.department = None
        self.designation = None
        self.manager = None
    
    @property
    def full_name(self):
        return f"{self.first_name} {self.last_name}"

# Mock workflow class for testing
class MockWorkflow:
    def __init__(self):
        self.id = "test-workflow-id"
        self.title = "Software Developer Onboarding"
        self.description = "Complete onboarding process for new software developer"
        self.status = "IN_PROGRESS"
        self.start_date = date.today()
        self.expected_completion_date = date.today() + timedelta(days=90)
        self.progress_percentage = 25
        self.assigned_to_id = "hr-manager-id"

# Mock task class for testing
class MockTask:
    def __init__(self, title, description):
        self.id = "test-task-id"
        self.title = title
        self.description = description
        self.due_date = date.today() + timedelta(days=7)

async def test_email_service():
    """Test the onboarding email service"""
    
    print("📧 Testing Onboarding Email Service")
    print("=" * 50)
    
    # Create email service
    email_service = OnboardingEmailService()
    
    # Create mock objects
    employee = MockEmployee()
    workflow = MockWorkflow()
    
    print(f"Employee: {employee.full_name} ({employee.email})")
    print(f"Workflow: {workflow.title}")
    print()
    
    # Test welcome email
    print("1. Testing Welcome Email...")
    try:
        login_credentials = {
            "email": employee.email,
            "password": "TempPassword123!"
        }
        
        result = await email_service.send_welcome_email(
            employee=employee,
            workflow=workflow,
            login_credentials=login_credentials
        )
        
        print(f"   Welcome Email: {'✅ Sent successfully' if result else '❌ Failed to send'}")
        
    except Exception as e:
        print(f"   Welcome Email: ❌ Error - {e}")
    
    # Test onboarding start notification
    print("\n2. Testing Onboarding Start Notification...")
    try:
        tasks = [
            MockTask("Complete IT Setup", "Set up laptop and accounts"),
            MockTask("HR Documentation", "Fill out employment forms"),
            MockTask("Team Introduction", "Meet your new team members"),
            MockTask("Security Training", "Complete mandatory security training"),
            MockTask("Role-specific Training", "Learn job-specific skills")
        ]
        
        result = await email_service.send_onboarding_start_notification(
            employee=employee,
            workflow=workflow,
            tasks=tasks
        )
        
        print(f"   Start Notification: {'✅ Sent successfully' if result else '❌ Failed to send'}")
        
    except Exception as e:
        print(f"   Start Notification: ❌ Error - {e}")
    
    # Test task reminder
    print("\n3. Testing Task Reminder Email...")
    try:
        task = MockTask("Complete Security Training", "Finish the mandatory cybersecurity awareness training")
        
        result = await email_service.send_task_reminder_email(
            employee=employee,
            task=task,
            workflow=workflow
        )
        
        print(f"   Task Reminder: {'✅ Sent successfully' if result else '❌ Failed to send'}")
        
    except Exception as e:
        print(f"   Task Reminder: ❌ Error - {e}")
    
    # Test completion email
    print("\n4. Testing Completion Email...")
    try:
        # Update workflow status for completion test
        workflow.status = "COMPLETED"
        workflow.progress_percentage = 100
        
        result = await email_service.send_completion_email(
            employee=employee,
            workflow=workflow
        )
        
        print(f"   Completion Email: {'✅ Sent successfully' if result else '❌ Failed to send'}")
        
    except Exception as e:
        print(f"   Completion Email: ❌ Error - {e}")
    
    # Test HR notification
    print("\n5. Testing HR Notification...")
    try:
        result = await email_service.send_hr_notification(
            hr_email="<EMAIL>",
            employee=employee,
            workflow=workflow,
            notification_type="new_employee"
        )
        
        print(f"   HR Notification: {'✅ Sent successfully' if result else '❌ Failed to send'}")
        
    except Exception as e:
        print(f"   HR Notification: ❌ Error - {e}")
    
    # Summary
    print("\n📊 Email Service Test Summary")
    print("=" * 50)
    print("✅ Email service testing completed!")
    print()
    print("📝 Email Templates Generated:")
    print("   • Welcome email with login credentials")
    print("   • Onboarding start notification with task list")
    print("   • Task reminder with due date")
    print("   • Completion congratulations email")
    print("   • HR notification for new employee")
    print()
    print("🎨 Email Features:")
    print("   • Professional HTML templates")
    print("   • AgnoConnect branding and colors")
    print("   • Responsive design")
    print("   • Personalized content")
    print("   • Call-to-action buttons")
    print("   • Security and compliance information")
    print()
    print("⚙️  Configuration:")
    print("   • SMTP server: Gmail (smtp.gmail.com)")
    print("   • Port: 587 (TLS)")
    print("   • Authentication: Required")
    print("   • HTML email support: Yes")
    print()
    print("🔧 Next Steps:")
    print("   1. Configure SMTP credentials in .env file:")
    print("      SMTP_USERNAME=<EMAIL>")
    print("      SMTP_PASSWORD=your-app-password")
    print("   2. Test with real email addresses")
    print("   3. Integrate with frontend onboarding workflow")
    print("   4. Set up automated task reminders")
    print()
    print("🌐 Frontend Integration:")
    print("   • Open http://localhost:5173")
    print("   • Navigate to Onboarding page")
    print("   • Create new onboarding workflows")
    print("   • Monitor email notifications")

async def test_api_endpoints():
    """Test onboarding API endpoints"""
    
    print("\n🔗 Testing API Endpoints")
    print("=" * 50)
    
    import requests
    
    base_url = "http://localhost:8085/api"
    
    # Test login first
    try:
        login_response = requests.post(f"{base_url}/auth/login", json={
            "email": "<EMAIL>",
            "password": "password123"
        })
        
        if login_response.status_code == 200:
            token = login_response.json()["access_token"]
            headers = {"Authorization": f"Bearer {token}"}
            
            print("✅ Authentication successful")
            
            # Test onboarding endpoints
            endpoints = [
                "/onboarding/templates",
                "/onboarding/onboarding",
                "/employees/"
            ]
            
            for endpoint in endpoints:
                try:
                    response = requests.get(f"{base_url}{endpoint}", headers=headers)
                    status = "✅ Working" if response.status_code == 200 else f"❌ {response.status_code}"
                    print(f"   {endpoint}: {status}")
                except Exception as e:
                    print(f"   {endpoint}: ❌ Error - {e}")
        else:
            print("❌ Authentication failed")
            
    except Exception as e:
        print(f"❌ API test error: {e}")

if __name__ == "__main__":
    asyncio.run(test_email_service())
    asyncio.run(test_api_endpoints())
