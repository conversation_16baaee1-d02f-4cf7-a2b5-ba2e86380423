from flask import url_for, jsonify
from flask.views import <PERSON>View
from flask_smorest import Blueprint, abort
from core.middleware import roles_required
from schemas import LicenseSchema
from sqlalchemy.orm.exc import NoResultFound
from sqlalchemy.exc import SQLAlchemyError, IntegrityError
import core.utils.response_message as RESPONSEMESSAGE
from core.services.licenses import LicensesService

blueprint = Blueprint("Licenses", __name__, description="Operations for Licenses")
    
@blueprint.route("/licenses/<id>")
class Licences(MethodView):
    @roles_required(['admin'])
    @blueprint.response(200, LicenseSchema)
    def get(self, id):
        service = LicensesService()
        license = service.getLicenses(id)
        if not license:
            abort(401, message="License does not exist")
        return license    
    
    @roles_required(['admin'])
    def delete(self, id):
        service = LicensesService()
        license = service.getLicenses(id)
        if not license:
            abort(404, message="License does not exist")
        service.deleteLicenses(id)
        return {"message" : "License deleted"}
    
    @roles_required(['admin'])
    @blueprint.arguments(LicenseSchema)
    @blueprint.response(201, LicenseSchema)
    def put(self, data, id):
        service = LicensesService()
        license = service.getLicenses(id)
        if not license:
            abort(404, message="Licenses does not exist")
        try :
            new_license = service.updateLicenses(id, data)
            return new_license
        except SQLAlchemyError:
                abort(500, message="Error while updating License")
    
@blueprint.route("/licenses")
class LicencesList(MethodView):
    @roles_required(['admin'])
    @blueprint.response(200, LicenseSchema)
    def get(self):
        service = LicensesService()
        license_list, total_license = service.fetchAll()
        dservice_schema = LicenseSchema(many=True)
        license_list = dservice_schema.dump(license_list)

        return jsonify({
            "total": total_license,
            "data": license_list,
        })
    

    @roles_required(['admin'])
    @blueprint.arguments(LicenseSchema)
    @blueprint.response(200, LicenseSchema)
    def post(self, data):
        try:
            service = LicensesService()
            new_license = service.createLicenses(data)
        except IntegrityError:
            abort(500, message="Error while creating License")
        except SQLAlchemyError as e:
            print(e)
            abort(500, message="Error while creating License")
        return new_license