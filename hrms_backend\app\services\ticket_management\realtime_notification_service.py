import json
import logging
from typing import Dict, List, Optional, Any, Set
from datetime import datetime
from sqlalchemy.orm import Session
from fastapi import WebSocket, WebSocketDisconnect
import asyncio
from enum import Enum

from ...db.models.ticket import Ticket, TicketStatus, TicketPriority
from ...db.models.employee import Employee
from ...core.security import CurrentUser
from ...core.websocket_manager import ConnectionManager

logger = logging.getLogger(__name__)


class NotificationType(Enum):
    TICKET_CREATED = "ticket_created"
    TICKET_UPDATED = "ticket_updated"
    TICKET_ASSIGNED = "ticket_assigned"
    TICKET_RESOLVED = "ticket_resolved"
    TICKET_ESCALATED = "ticket_escalated"
    COMMENT_ADDED = "comment_added"
    SLA_BREACH = "sla_breach"
    PRIORITY_CHANGED = "priority_changed"
    STATUS_CHANGED = "status_changed"


class RealtimeNotificationService:
    """Real-time notification service using WebSockets"""

    def __init__(self):
        self.websocket_manager = ConnectionManager()
        self.active_connections: Dict[str, Set[WebSocket]] = {}
        self.user_subscriptions: Dict[str, Dict[str, Any]] = {}
        
        # Notification preferences by role
        self.role_notifications = {
            "SUPER_ADMIN": [
                NotificationType.TICKET_CREATED,
                NotificationType.TICKET_ESCALATED,
                NotificationType.SLA_BREACH,
                NotificationType.PRIORITY_CHANGED
            ],
            "ADMIN": [
                NotificationType.TICKET_CREATED,
                NotificationType.TICKET_ESCALATED,
                NotificationType.SLA_BREACH,
                NotificationType.PRIORITY_CHANGED
            ],
            "HR": [
                NotificationType.TICKET_CREATED,
                NotificationType.TICKET_ASSIGNED,
                NotificationType.TICKET_UPDATED,
                NotificationType.COMMENT_ADDED
            ],
            "MANAGER": [
                NotificationType.TICKET_ASSIGNED,
                NotificationType.TICKET_ESCALATED,
                NotificationType.TICKET_UPDATED,
                NotificationType.STATUS_CHANGED
            ],
            "EMPLOYEE": [
                NotificationType.TICKET_UPDATED,
                NotificationType.TICKET_RESOLVED,
                NotificationType.COMMENT_ADDED,
                NotificationType.STATUS_CHANGED
            ]
        }

    async def connect_user(
        self,
        websocket: WebSocket,
        user_id: str,
        organization_id: str,
        role: str
    ):
        """Connect user to real-time notifications"""
        try:
            await websocket.accept()
            
            # Add to active connections
            if user_id not in self.active_connections:
                self.active_connections[user_id] = set()
            self.active_connections[user_id].add(websocket)
            
            # Set up user subscriptions
            self.user_subscriptions[user_id] = {
                "organization_id": organization_id,
                "role": role,
                "connected_at": datetime.utcnow(),
                "notification_types": self.role_notifications.get(role, [])
            }
            
            # Send connection confirmation
            await self._send_to_user(user_id, {
                "type": "connection_established",
                "message": "Real-time notifications connected",
                "timestamp": datetime.utcnow().isoformat()
            })
            
            logger.info(f"User {user_id} connected to real-time notifications")

        except Exception as e:
            logger.error(f"Error connecting user {user_id}: {e}")

    async def disconnect_user(self, websocket: WebSocket, user_id: str):
        """Disconnect user from real-time notifications"""
        try:
            if user_id in self.active_connections:
                self.active_connections[user_id].discard(websocket)
                
                # Remove user if no active connections
                if not self.active_connections[user_id]:
                    del self.active_connections[user_id]
                    if user_id in self.user_subscriptions:
                        del self.user_subscriptions[user_id]
            
            logger.info(f"User {user_id} disconnected from real-time notifications")

        except Exception as e:
            logger.error(f"Error disconnecting user {user_id}: {e}")

    async def notify_ticket_created(
        self,
        db: Session,
        ticket: Ticket,
        created_by: CurrentUser
    ):
        """Send real-time notification for ticket creation"""
        try:
            notification_data = {
                "type": NotificationType.TICKET_CREATED.value,
                "ticket_id": str(ticket.id),
                "ticket_number": ticket.ticket_number,
                "title": ticket.title,
                "priority": ticket.priority.value,
                "ticket_type": ticket.ticket_type.value,
                "created_by": f"{created_by.first_name} {created_by.last_name}",
                "created_at": ticket.created_at.isoformat(),
                "organization_id": str(ticket.organization_id)
            }

            # Notify relevant users
            await self._notify_by_role(
                notification_data,
                [NotificationType.TICKET_CREATED],
                str(ticket.organization_id)
            )

            # Notify assigned agent if auto-assigned
            if ticket.assigned_to:
                await self._notify_specific_user(
                    str(ticket.assigned_to),
                    {
                        **notification_data,
                        "type": NotificationType.TICKET_ASSIGNED.value,
                        "message": f"New ticket assigned to you: {ticket.ticket_number}"
                    }
                )

        except Exception as e:
            logger.error(f"Error sending ticket creation notification: {e}")

    async def notify_ticket_updated(
        self,
        db: Session,
        ticket: Ticket,
        updated_by: CurrentUser,
        changes: Dict[str, Any]
    ):
        """Send real-time notification for ticket updates"""
        try:
            notification_data = {
                "type": NotificationType.TICKET_UPDATED.value,
                "ticket_id": str(ticket.id),
                "ticket_number": ticket.ticket_number,
                "title": ticket.title,
                "changes": changes,
                "updated_by": f"{updated_by.first_name} {updated_by.last_name}",
                "updated_at": datetime.utcnow().isoformat(),
                "organization_id": str(ticket.organization_id)
            }

            # Notify ticket requester
            await self._notify_specific_user(
                str(ticket.requester_id),
                {
                    **notification_data,
                    "message": f"Your ticket {ticket.ticket_number} has been updated"
                }
            )

            # Notify assigned agent
            if ticket.assigned_to and str(ticket.assigned_to) != updated_by.user_id:
                await self._notify_specific_user(
                    str(ticket.assigned_to),
                    {
                        **notification_data,
                        "message": f"Ticket {ticket.ticket_number} has been updated"
                    }
                )

            # Check for specific change types
            if "status" in changes:
                await self._handle_status_change_notification(ticket, updated_by, changes)
            
            if "priority" in changes:
                await self._handle_priority_change_notification(ticket, updated_by, changes)

        except Exception as e:
            logger.error(f"Error sending ticket update notification: {e}")

    async def notify_ticket_assigned(
        self,
        db: Session,
        ticket: Ticket,
        assigned_to: str,
        assigned_by: CurrentUser
    ):
        """Send real-time notification for ticket assignment"""
        try:
            # Get assignee details
            assignee = db.query(Employee).filter(Employee.id == assigned_to).first()
            if not assignee:
                return

            notification_data = {
                "type": NotificationType.TICKET_ASSIGNED.value,
                "ticket_id": str(ticket.id),
                "ticket_number": ticket.ticket_number,
                "title": ticket.title,
                "priority": ticket.priority.value,
                "assigned_by": f"{assigned_by.first_name} {assigned_by.last_name}",
                "assigned_at": datetime.utcnow().isoformat(),
                "organization_id": str(ticket.organization_id)
            }

            # Notify the assignee
            await self._notify_specific_user(
                assigned_to,
                {
                    **notification_data,
                    "message": f"Ticket {ticket.ticket_number} has been assigned to you"
                }
            )

            # Notify the requester
            await self._notify_specific_user(
                str(ticket.requester_id),
                {
                    **notification_data,
                    "message": f"Your ticket {ticket.ticket_number} has been assigned to {assignee.first_name} {assignee.last_name}"
                }
            )

        except Exception as e:
            logger.error(f"Error sending ticket assignment notification: {e}")

    async def notify_ticket_escalated(
        self,
        db: Session,
        ticket: Ticket,
        escalated_to: str,
        escalated_by: CurrentUser,
        reason: str
    ):
        """Send real-time notification for ticket escalation"""
        try:
            notification_data = {
                "type": NotificationType.TICKET_ESCALATED.value,
                "ticket_id": str(ticket.id),
                "ticket_number": ticket.ticket_number,
                "title": ticket.title,
                "priority": ticket.priority.value,
                "reason": reason,
                "escalated_by": f"{escalated_by.first_name} {escalated_by.last_name}",
                "escalated_at": datetime.utcnow().isoformat(),
                "organization_id": str(ticket.organization_id)
            }

            # Notify escalation target
            await self._notify_specific_user(
                escalated_to,
                {
                    **notification_data,
                    "message": f"Ticket {ticket.ticket_number} has been escalated to you",
                    "urgent": True
                }
            )

            # Notify admins and managers
            await self._notify_by_role(
                {
                    **notification_data,
                    "message": f"Ticket {ticket.ticket_number} has been escalated"
                },
                [NotificationType.TICKET_ESCALATED],
                str(ticket.organization_id)
            )

        except Exception as e:
            logger.error(f"Error sending ticket escalation notification: {e}")

    async def notify_sla_breach(
        self,
        db: Session,
        ticket: Ticket,
        breach_type: str = "resolution"
    ):
        """Send real-time notification for SLA breach"""
        try:
            notification_data = {
                "type": NotificationType.SLA_BREACH.value,
                "ticket_id": str(ticket.id),
                "ticket_number": ticket.ticket_number,
                "title": ticket.title,
                "priority": ticket.priority.value,
                "breach_type": breach_type,
                "due_date": ticket.due_date.isoformat() if ticket.due_date else None,
                "breached_at": datetime.utcnow().isoformat(),
                "organization_id": str(ticket.organization_id),
                "urgent": True
            }

            # Notify assigned agent
            if ticket.assigned_to:
                await self._notify_specific_user(
                    str(ticket.assigned_to),
                    {
                        **notification_data,
                        "message": f"SLA breach: Ticket {ticket.ticket_number} is overdue"
                    }
                )

            # Notify admins and managers
            await self._notify_by_role(
                {
                    **notification_data,
                    "message": f"SLA breach detected for ticket {ticket.ticket_number}"
                },
                [NotificationType.SLA_BREACH],
                str(ticket.organization_id)
            )

        except Exception as e:
            logger.error(f"Error sending SLA breach notification: {e}")

    async def notify_comment_added(
        self,
        db: Session,
        ticket: Ticket,
        comment_author: CurrentUser,
        comment_content: str,
        is_internal: bool = False
    ):
        """Send real-time notification for new comments"""
        try:
            notification_data = {
                "type": NotificationType.COMMENT_ADDED.value,
                "ticket_id": str(ticket.id),
                "ticket_number": ticket.ticket_number,
                "comment_author": f"{comment_author.first_name} {comment_author.last_name}",
                "comment_preview": comment_content[:100] + "..." if len(comment_content) > 100 else comment_content,
                "is_internal": is_internal,
                "commented_at": datetime.utcnow().isoformat(),
                "organization_id": str(ticket.organization_id)
            }

            # Don't notify for internal comments to external users
            if not is_internal:
                # Notify ticket requester
                if str(ticket.requester_id) != comment_author.user_id:
                    await self._notify_specific_user(
                        str(ticket.requester_id),
                        {
                            **notification_data,
                            "message": f"New comment on your ticket {ticket.ticket_number}"
                        }
                    )

            # Notify assigned agent
            if ticket.assigned_to and str(ticket.assigned_to) != comment_author.user_id:
                await self._notify_specific_user(
                    str(ticket.assigned_to),
                    {
                        **notification_data,
                        "message": f"New comment on ticket {ticket.ticket_number}"
                    }
                )

        except Exception as e:
            logger.error(f"Error sending comment notification: {e}")

    async def get_user_notifications(
        self,
        user_id: str,
        limit: int = 50
    ) -> List[Dict[str, Any]]:
        """Get recent notifications for a user"""
        try:
            # This would typically fetch from a notifications table
            # For now, return empty list as placeholder
            return []

        except Exception as e:
            logger.error(f"Error getting user notifications: {e}")
            return []

    async def mark_notification_read(
        self,
        user_id: str,
        notification_id: str
    ) -> bool:
        """Mark a notification as read"""
        try:
            # This would update the notification status in database
            # For now, return True as placeholder
            return True

        except Exception as e:
            logger.error(f"Error marking notification as read: {e}")
            return False

    # Helper methods
    async def _send_to_user(self, user_id: str, data: Dict[str, Any]):
        """Send data to specific user's WebSocket connections"""
        try:
            if user_id in self.active_connections:
                message = json.dumps(data)
                disconnected_sockets = set()
                
                for websocket in self.active_connections[user_id]:
                    try:
                        await websocket.send_text(message)
                    except WebSocketDisconnect:
                        disconnected_sockets.add(websocket)
                    except Exception as e:
                        logger.error(f"Error sending to websocket: {e}")
                        disconnected_sockets.add(websocket)
                
                # Clean up disconnected sockets
                for websocket in disconnected_sockets:
                    self.active_connections[user_id].discard(websocket)

        except Exception as e:
            logger.error(f"Error sending to user {user_id}: {e}")

    async def _notify_specific_user(self, user_id: str, notification_data: Dict[str, Any]):
        """Send notification to a specific user"""
        try:
            if user_id in self.user_subscriptions:
                user_sub = self.user_subscriptions[user_id]
                notification_type = NotificationType(notification_data["type"])
                
                # Check if user should receive this notification type
                if notification_type in user_sub.get("notification_types", []):
                    await self._send_to_user(user_id, notification_data)

        except Exception as e:
            logger.error(f"Error notifying specific user {user_id}: {e}")

    async def _notify_by_role(
        self,
        notification_data: Dict[str, Any],
        notification_types: List[NotificationType],
        organization_id: str
    ):
        """Send notification to users by role"""
        try:
            for user_id, subscription in self.user_subscriptions.items():
                if subscription["organization_id"] == organization_id:
                    user_notification_types = subscription.get("notification_types", [])
                    
                    # Check if user should receive any of these notification types
                    if any(nt in user_notification_types for nt in notification_types):
                        await self._send_to_user(user_id, notification_data)

        except Exception as e:
            logger.error(f"Error notifying by role: {e}")

    async def _handle_status_change_notification(
        self,
        ticket: Ticket,
        updated_by: CurrentUser,
        changes: Dict[str, Any]
    ):
        """Handle specific notification for status changes"""
        try:
            old_status = changes.get("status", {}).get("old")
            new_status = changes.get("status", {}).get("new")
            
            if new_status == TicketStatus.RESOLVED.value:
                await self._notify_specific_user(
                    str(ticket.requester_id),
                    {
                        "type": NotificationType.TICKET_RESOLVED.value,
                        "ticket_id": str(ticket.id),
                        "ticket_number": ticket.ticket_number,
                        "message": f"Your ticket {ticket.ticket_number} has been resolved",
                        "resolved_by": f"{updated_by.first_name} {updated_by.last_name}",
                        "resolved_at": datetime.utcnow().isoformat()
                    }
                )

        except Exception as e:
            logger.error(f"Error handling status change notification: {e}")

    async def _handle_priority_change_notification(
        self,
        ticket: Ticket,
        updated_by: CurrentUser,
        changes: Dict[str, Any]
    ):
        """Handle specific notification for priority changes"""
        try:
            old_priority = changes.get("priority", {}).get("old")
            new_priority = changes.get("priority", {}).get("new")
            
            # Notify for priority increases
            if (old_priority in ["low", "medium"] and 
                new_priority in ["high", "urgent", "critical"]):
                
                notification_data = {
                    "type": NotificationType.PRIORITY_CHANGED.value,
                    "ticket_id": str(ticket.id),
                    "ticket_number": ticket.ticket_number,
                    "old_priority": old_priority,
                    "new_priority": new_priority,
                    "changed_by": f"{updated_by.first_name} {updated_by.last_name}",
                    "message": f"Ticket {ticket.ticket_number} priority increased to {new_priority}",
                    "urgent": new_priority in ["urgent", "critical"]
                }
                
                # Notify assigned agent
                if ticket.assigned_to:
                    await self._notify_specific_user(str(ticket.assigned_to), notification_data)
                
                # Notify managers for urgent/critical tickets
                if new_priority in ["urgent", "critical"]:
                    await self._notify_by_role(
                        notification_data,
                        [NotificationType.PRIORITY_CHANGED],
                        str(ticket.organization_id)
                    )

        except Exception as e:
            logger.error(f"Error handling priority change notification: {e}")
