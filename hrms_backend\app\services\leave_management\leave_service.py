from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, func, extract
from typing import Optional, List
from uuid import UUID
from datetime import datetime, date, timedelta
from decimal import Decimal
from fastapi import HTTPException, status
import logging
from dateutil.relativedelta import relativedelta

from ...db.models.leave import (
    LeaveRequest, LeavePolicy, LeaveBalance, LeaveCalendar, LeaveEncashment
)
from ...db.models.employee import Employee, EmployeeStatus
from typing import List
from ...schemas.leave import (
    LeaveRequestCreate, LeaveRequestUpdate, LeaveRequestResponse,
    LeaveRequestListResponse, LeavePolicyCreate, LeavePolicyUpdate,
    LeavePolicyResponse, LeaveBalanceResponse, LeaveApprovalRequest,
    LeaveCalendarCreate, LeaveCalendarUpdate, LeaveCalendarResponse,
    LeaveEncashmentCreate, LeaveEncashmentResponse, LeaveStatus,
    LeaveDuration, LeaveType
)
from ...core.security import CurrentUser
from ...core.websocket_manager import notification_manager
from ..hr_management.notification_service import NotificationService

logger = logging.getLogger(__name__)


class LeaveService:
    """Leave service for business logic"""

    def __init__(self):
        self.notification_service = NotificationService()

    async def create_leave_request(
        self,
        db: Session,
        employee_id: UUID,
        leave_data: LeaveRequestCreate,
        current_user: CurrentUser
    ) -> LeaveRequestResponse:
        """Create new leave request"""
        try:
            # Verify employee exists and belongs to organization
            # First try to find by employee ID, then by user ID
            employee = db.query(Employee).filter(
                Employee.id == employee_id,
                Employee.organization_id == current_user.organization_id,
                Employee.is_active == True
            ).first()

            # If not found by employee ID, try by user ID
            if not employee:
                employee = db.query(Employee).filter(
                    Employee.user_id == employee_id,
                    Employee.organization_id == current_user.organization_id,
                    Employee.is_active == True
                ).first()

            if not employee:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Employee not found"
                )

            # Verify leave policy exists
            leave_policy = db.query(LeavePolicy).filter(
                LeavePolicy.id == leave_data.leave_policy_id,
                LeavePolicy.organization_id == current_user.organization_id,
                LeavePolicy.is_active == True
            ).first()

            if not leave_policy:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Leave policy not found"
                )

            # Calculate total days
            total_days = await self._calculate_leave_days(
                leave_data.start_date,
                leave_data.end_date,
                leave_data.duration_type,
                db,
                current_user.organization_id
            )

            # Validate leave request
            await self._validate_leave_request(
                db, employee.id, leave_policy, leave_data, total_days, current_user
            )

            # Check leave balance
            await self._check_leave_balance(
                db, employee.id, leave_policy.id, total_days, current_user
            )

            # Create leave request
            leave_request = LeaveRequest(
                employee_id=employee.id,
                leave_policy_id=leave_data.leave_policy_id,
                start_date=leave_data.start_date,
                end_date=leave_data.end_date,
                total_days=total_days,
                duration_type=leave_data.duration_type,
                reason=leave_data.reason,
                contact_number=leave_data.contact_number,
                emergency_contact=leave_data.emergency_contact,
                handover_notes=leave_data.handover_notes,
                handover_to=leave_data.handover_to,
                attachment_urls=leave_data.attachment_urls or [],
                status=LeaveStatus.PENDING,
                applied_at=datetime.utcnow()
            )

            db.add(leave_request)
            db.commit()
            db.refresh(leave_request)

            # Update leave balance (pending)
            await self._update_leave_balance_pending(
                db, employee.id, leave_policy.id, total_days, current_user
            )

            # Temporarily disable auto-approval for testing
            # auto_decision = await self._check_auto_decision(db, leave_request, leave_policy, employee, current_user)
            auto_decision = None
            if auto_decision:
                leave_request.status = auto_decision["status"]
                leave_request.approved_by = auto_decision.get("approved_by")
                leave_request.approved_at = datetime.utcnow()
                leave_request.rejection_reason = auto_decision.get("reason")

                if auto_decision["status"] == LeaveStatus.APPROVED:
                    await self._finalize_leave_balance(
                        db, employee.id, leave_policy.id, total_days, current_user
                    )
                else:
                    await self._revert_pending_balance(
                        db, employee_id, leave_policy.id, total_days, current_user
                    )

                db.commit()
                db.refresh(leave_request)

                # Send notification for auto-decision
                await self._notify_leave_request(leave_request, auto_decision["action"], current_user)
            else:
                # Send notification to manager/HR for manual approval
                await self._notify_leave_request(leave_request, "created", current_user)

                # Check for auto-escalation
                await self._schedule_auto_escalation(db, leave_request, current_user)

            logger.info(f"Leave request created: {leave_request.id} for employee {employee_id}")
            return LeaveRequestResponse.from_orm(leave_request)

        except HTTPException:
            raise
        except Exception as e:
            db.rollback()
            logger.error(f"Error creating leave request: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Error creating leave request"
            )

    async def get_leave_requests(
        self,
        db: Session,
        employee_id: Optional[UUID] = None,
        status: Optional[LeaveStatus] = None,
        start_date: Optional[date] = None,
        end_date: Optional[date] = None,
        skip: int = 0,
        limit: int = 20,
        current_user: CurrentUser = None
    ) -> LeaveRequestListResponse:
        """Get leave requests with filtering and enhanced data"""
        try:
            query = db.query(LeaveRequest).join(Employee, LeaveRequest.employee_id == Employee.id).filter(
                Employee.organization_id == current_user.organization_id
            )

            # Apply role-based filtering
            if current_user.role == "Employee":
                # Employees can only see their own requests
                query = query.filter(LeaveRequest.employee_id == current_user.user_id)
            elif current_user.role == "Manager":
                # Managers can see their team's requests
                team_employee_ids = await self._get_team_employee_ids(db, current_user.user_id, current_user)
                query = query.filter(LeaveRequest.employee_id.in_(team_employee_ids))

            # Apply filters
            if employee_id:
                query = query.filter(LeaveRequest.employee_id == employee_id)

            if status:
                query = query.filter(LeaveRequest.status == status)

            if start_date:
                query = query.filter(LeaveRequest.start_date >= start_date)

            if end_date:
                query = query.filter(LeaveRequest.end_date <= end_date)

            # Get total count
            total = query.count()

            # Apply pagination and ordering
            requests = query.order_by(
                LeaveRequest.applied_at.desc()
            ).offset(skip).limit(limit).all()

            # Enhance requests with additional data
            enhanced_requests = []
            for req in requests:
                req_data = LeaveRequestResponse.from_orm(req)

                # Add employee details
                employee = db.query(Employee).filter(Employee.id == req.employee_id).first()
                if employee:
                    req_data.employee_name = f"{employee.first_name} {employee.last_name}"
                    req_data.employee_email = employee.email
                    req_data.department = employee.department

                # Add leave policy details
                policy = db.query(LeavePolicy).filter(LeavePolicy.id == req.leave_policy_id).first()
                if policy:
                    req_data.leave_type_name = policy.name
                    req_data.leave_type = policy.leave_type

                # Add approver details if approved/rejected
                if req.approved_by:
                    approver = db.query(Employee).filter(Employee.id == req.approved_by).first()
                    if approver:
                        req_data.approved_by_name = f"{approver.first_name} {approver.last_name}"

                enhanced_requests.append(req_data)

            return LeaveRequestListResponse(
                requests=enhanced_requests,
                total=total,
                skip=skip,
                limit=limit
            )

        except Exception as e:
            logger.error(f"Error getting leave requests: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Error retrieving leave requests"
            )

    async def _get_team_employee_ids(self, db: Session, manager_id: UUID, current_user: CurrentUser) -> List[UUID]:
        """Get employee IDs for a manager's team"""
        try:
            # Get employees who report to this manager
            team_employees = db.query(Employee).filter(
                Employee.manager_id == manager_id,
                Employee.organization_id == current_user.organization_id,
                Employee.is_active == True
            ).all()

            # Include the manager's own ID
            employee_ids = [manager_id]
            employee_ids.extend([emp.id for emp in team_employees])

            return employee_ids
        except Exception as e:
            logger.error(f"Error getting team employee IDs: {e}")
            return [manager_id]  # Fallback to just manager's ID

    async def get_leave_dashboard(self, db: Session, current_user: CurrentUser) -> dict:
        """Get comprehensive leave dashboard data"""
        try:
            dashboard_data = {
                "summary": {},
                "recent_requests": [],
                "pending_approvals": [],
                "upcoming_leaves": [],
                "team_calendar": [],
                "analytics": {}
            }

            # Get user's leave summary
            if current_user.role in ["Employee", "Manager"]:
                user_balance = await self.get_leave_balance(db, current_user.user_id, None, current_user)
                dashboard_data["summary"]["my_balance"] = user_balance

            # Get recent requests
            recent_requests = await self.get_leave_requests(
                db, employee_id=current_user.user_id if current_user.role == "Employee" else None,
                skip=0, limit=5, current_user=current_user
            )
            dashboard_data["recent_requests"] = recent_requests.requests

            # Get pending approvals (for managers and HR)
            if current_user.role in ["Manager", "HR", "Admin", "SuperAdmin"]:
                pending_requests = await self.get_leave_requests(
                    db, status=LeaveStatus.PENDING, skip=0, limit=10, current_user=current_user
                )
                dashboard_data["pending_approvals"] = pending_requests.requests

            # Get upcoming leaves
            upcoming_leaves = await self._get_upcoming_leaves(db, current_user)
            dashboard_data["upcoming_leaves"] = upcoming_leaves

            # Get team calendar (for managers)
            if current_user.role in ["Manager", "HR", "Admin", "SuperAdmin"]:
                team_calendar = await self._get_team_leave_calendar(db, current_user)
                dashboard_data["team_calendar"] = team_calendar

            # Get analytics
            analytics = await self._get_leave_analytics(db, current_user)
            dashboard_data["analytics"] = analytics

            return dashboard_data

        except Exception as e:
            logger.error(f"Error getting leave dashboard: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Error retrieving leave dashboard"
            )

    async def _get_upcoming_leaves(self, db: Session, current_user: CurrentUser) -> List[dict]:
        """Get upcoming approved leaves"""
        try:
            today = date.today()
            upcoming_date = today + timedelta(days=30)  # Next 30 days

            query = db.query(LeaveRequest).join(Employee).filter(
                Employee.organization_id == current_user.organization_id,
                LeaveRequest.status == LeaveStatus.APPROVED,
                LeaveRequest.start_date >= today,
                LeaveRequest.start_date <= upcoming_date
            )

            # Apply role-based filtering
            if current_user.role == "Employee":
                query = query.filter(LeaveRequest.employee_id == current_user.user_id)
            elif current_user.role == "Manager":
                team_employee_ids = await self._get_team_employee_ids(db, current_user.user_id, current_user)
                query = query.filter(LeaveRequest.employee_id.in_(team_employee_ids))

            upcoming_leaves = query.order_by(LeaveRequest.start_date).all()

            result = []
            for leave in upcoming_leaves:
                employee = db.query(Employee).filter(Employee.id == leave.employee_id).first()
                policy = db.query(LeavePolicy).filter(LeavePolicy.id == leave.leave_policy_id).first()

                result.append({
                    "id": str(leave.id),
                    "employee_name": f"{employee.first_name} {employee.last_name}" if employee else "Unknown",
                    "employee_id": str(leave.employee_id),
                    "leave_type": policy.leave_type if policy else "Unknown",
                    "start_date": leave.start_date.isoformat(),
                    "end_date": leave.end_date.isoformat(),
                    "total_days": float(leave.total_days),
                    "reason": leave.reason[:100] + "..." if len(leave.reason) > 100 else leave.reason
                })

            return result

        except Exception as e:
            logger.error(f"Error getting upcoming leaves: {e}")
            return []

    async def _get_team_leave_calendar(self, db: Session, current_user: CurrentUser) -> List[dict]:
        """Get team leave calendar for the current month"""
        try:
            today = date.today()
            start_of_month = today.replace(day=1)
            if today.month == 12:
                end_of_month = date(today.year + 1, 1, 1) - timedelta(days=1)
            else:
                end_of_month = date(today.year, today.month + 1, 1) - timedelta(days=1)

            query = db.query(LeaveRequest).join(Employee).filter(
                Employee.organization_id == current_user.organization_id,
                LeaveRequest.status == LeaveStatus.APPROVED,
                LeaveRequest.start_date <= end_of_month,
                LeaveRequest.end_date >= start_of_month
            )

            # Apply role-based filtering
            if current_user.role == "Manager":
                team_employee_ids = await self._get_team_employee_ids(db, current_user.user_id, current_user)
                query = query.filter(LeaveRequest.employee_id.in_(team_employee_ids))

            calendar_leaves = query.all()

            result = []
            for leave in calendar_leaves:
                employee = db.query(Employee).filter(Employee.id == leave.employee_id).first()
                policy = db.query(LeavePolicy).filter(LeavePolicy.id == leave.leave_policy_id).first()

                result.append({
                    "id": str(leave.id),
                    "title": f"{employee.first_name} {employee.last_name} - {policy.leave_type}" if employee and policy else "Leave",
                    "start": leave.start_date.isoformat(),
                    "end": (leave.end_date + timedelta(days=1)).isoformat(),  # Full day event
                    "employee_name": f"{employee.first_name} {employee.last_name}" if employee else "Unknown",
                    "leave_type": policy.leave_type if policy else "Unknown",
                    "total_days": float(leave.total_days),
                    "color": self._get_leave_type_color(policy.leave_type if policy else "unknown")
                })

            return result

        except Exception as e:
            logger.error(f"Error getting team leave calendar: {e}")
            return []

    def _get_leave_type_color(self, leave_type: str) -> str:
        """Get color for leave type in calendar"""
        color_map = {
            "annual": "#3B82F6",      # Blue
            "sick": "#EF4444",        # Red
            "maternity": "#EC4899",   # Pink
            "paternity": "#8B5CF6",   # Purple
            "personal": "#10B981",    # Green
            "emergency": "#F59E0B",   # Amber
            "bereavement": "#6B7280", # Gray
            "study": "#06B6D4",       # Cyan
            "sabbatical": "#8B5CF6",  # Purple
            "unpaid": "#9CA3AF"       # Light Gray
        }
        return color_map.get(leave_type.lower(), "#6B7280")

    async def _get_leave_analytics(self, db: Session, current_user: CurrentUser) -> dict:
        """Get leave analytics and statistics"""
        try:
            analytics = {
                "current_year_stats": {},
                "monthly_trends": [],
                "leave_type_distribution": [],
                "approval_stats": {},
                "team_utilization": []
            }

            current_year = date.today().year

            # Base query for organization
            base_query = db.query(LeaveRequest).join(Employee).filter(
                Employee.organization_id == current_user.organization_id,
                extract('year', LeaveRequest.start_date) == current_year
            )

            # Apply role-based filtering
            if current_user.role == "Employee":
                base_query = base_query.filter(LeaveRequest.employee_id == current_user.user_id)
            elif current_user.role == "Manager":
                team_employee_ids = await self._get_team_employee_ids(db, current_user.user_id, current_user)
                base_query = base_query.filter(LeaveRequest.employee_id.in_(team_employee_ids))

            # Current year stats
            total_requests = base_query.count()
            approved_requests = base_query.filter(LeaveRequest.status == LeaveStatus.APPROVED).count()
            pending_requests = base_query.filter(LeaveRequest.status == LeaveStatus.PENDING).count()
            rejected_requests = base_query.filter(LeaveRequest.status == LeaveStatus.REJECTED).count()

            analytics["current_year_stats"] = {
                "total_requests": total_requests,
                "approved_requests": approved_requests,
                "pending_requests": pending_requests,
                "rejected_requests": rejected_requests,
                "approval_rate": round((approved_requests / total_requests * 100) if total_requests > 0 else 0, 1)
            }

            # Monthly trends (last 12 months)
            monthly_data = []
            for i in range(12):
                month_date = date.today().replace(day=1) - relativedelta(months=i)
                month_requests = base_query.filter(
                    extract('year', LeaveRequest.start_date) == month_date.year,
                    extract('month', LeaveRequest.start_date) == month_date.month
                ).count()

                monthly_data.append({
                    "month": month_date.strftime("%b %Y"),
                    "requests": month_requests
                })

            analytics["monthly_trends"] = list(reversed(monthly_data))

            # Leave type distribution
            leave_type_stats = db.query(
                LeavePolicy.leave_type,
                func.count(LeaveRequest.id).label('count'),
                func.sum(LeaveRequest.total_days).label('total_days')
            ).join(LeaveRequest).join(Employee).filter(
                Employee.organization_id == current_user.organization_id,
                extract('year', LeaveRequest.start_date) == current_year,
                LeaveRequest.status == LeaveStatus.APPROVED
            ).group_by(LeavePolicy.leave_type).all()

            analytics["leave_type_distribution"] = [
                {
                    "leave_type": stat.leave_type,
                    "count": stat.count,
                    "total_days": float(stat.total_days or 0)
                }
                for stat in leave_type_stats
            ]

            # Approval stats (for managers and above)
            if current_user.role in ["Manager", "HR", "Admin", "SuperAdmin"]:
                avg_approval_time = db.query(
                    func.avg(
                        func.extract('epoch', LeaveRequest.approved_at - LeaveRequest.applied_at) / 3600
                    ).label('avg_hours')
                ).filter(
                    LeaveRequest.status.in_([LeaveStatus.APPROVED, LeaveStatus.REJECTED]),
                    LeaveRequest.approved_at.isnot(None)
                ).scalar()

                analytics["approval_stats"] = {
                    "avg_approval_time_hours": round(float(avg_approval_time or 0), 1),
                    "pending_count": pending_requests
                }

            return analytics

        except Exception as e:
            logger.error(f"Error getting leave analytics: {e}")
            return {
                "current_year_stats": {},
                "monthly_trends": [],
                "leave_type_distribution": [],
                "approval_stats": {},
                "team_utilization": []
            }

    async def send_leave_notification(
        self,
        db: Session,
        leave_request: LeaveRequest,
        notification_type: str,
        recipient_id: UUID,
        current_user: CurrentUser
    ):
        """Send leave-related notifications"""
        try:
            # Get employee and recipient details
            employee = db.query(Employee).filter(Employee.id == leave_request.employee_id).first()
            recipient = db.query(Employee).filter(Employee.id == recipient_id).first()

            if not employee or not recipient:
                return

            # Prepare notification data
            notification_data = {
                "type": notification_type,
                "employee_name": f"{employee.first_name} {employee.last_name}",
                "leave_type": leave_request.leave_policy.leave_type if leave_request.leave_policy else "Leave",
                "start_date": leave_request.start_date.strftime("%B %d, %Y"),
                "end_date": leave_request.end_date.strftime("%B %d, %Y"),
                "total_days": float(leave_request.total_days),
                "reason": leave_request.reason[:100] + "..." if len(leave_request.reason) > 100 else leave_request.reason
            }

            # Send notification via WebSocket
            await notification_manager.send_notification(
                user_id=str(recipient_id),
                notification_type=notification_type,
                data=notification_data
            )

            # Send email notification
            await self._send_email_notification(
                recipient_email=recipient.email,
                notification_type=notification_type,
                data=notification_data
            )

            logger.info(f"Notification sent: {notification_type} to {recipient_id} for leave request {leave_request.id}")

        except Exception as e:
            logger.error(f"Error sending leave notification: {e}")

    async def _send_email_notification(self, recipient_email: str, notification_type: str, data: dict):
        """Send email notification for leave events"""
        try:
            # Email templates for different notification types
            templates = {
                "leave_submitted": {
                    "subject": f"Leave Request Submitted - {data['employee_name']}",
                    "body": f"""
                    A new leave request has been submitted for your approval.

                    Employee: {data['employee_name']}
                    Leave Type: {data['leave_type']}
                    Duration: {data['start_date']} to {data['end_date']} ({data['total_days']} days)
                    Reason: {data['reason']}

                    Please review and approve/reject this request in the HRMS system.
                    """
                },
                "leave_approved": {
                    "subject": f"Leave Request Approved - {data['leave_type']}",
                    "body": f"""
                    Your leave request has been approved!

                    Leave Type: {data['leave_type']}
                    Duration: {data['start_date']} to {data['end_date']} ({data['total_days']} days)

                    Your leave has been added to the calendar. Have a great time off!
                    """
                },
                "leave_rejected": {
                    "subject": f"Leave Request Update - {data['leave_type']}",
                    "body": f"""
                    Your leave request has been reviewed.

                    Leave Type: {data['leave_type']}
                    Duration: {data['start_date']} to {data['end_date']} ({data['total_days']} days)
                    Status: Not Approved

                    Please contact your manager for more details.
                    """
                }
            }

            template = templates.get(notification_type)
            if template:
                # Here you would integrate with your email service
                # For now, we'll just log the email content
                logger.info(f"Email notification: {template['subject']} to {recipient_email}")

        except Exception as e:
            logger.error(f"Error sending email notification: {e}")

    async def get_leave_workflow_status(
        self,
        db: Session,
        leave_request_id: UUID,
        current_user: CurrentUser
    ) -> dict:
        """Get detailed workflow status for a leave request"""
        try:
            leave_request = db.query(LeaveRequest).join(Employee).filter(
                LeaveRequest.id == leave_request_id,
                Employee.organization_id == current_user.organization_id
            ).first()

            if not leave_request:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Leave request not found"
                )

            # Check if user has permission to view this request
            if (current_user.role == "Employee" and
                str(leave_request.employee_id) != current_user.user_id):
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="Access denied"
                )

            # Get employee and approver details
            employee = db.query(Employee).filter(Employee.id == leave_request.employee_id).first()
            approver = None
            if leave_request.approved_by:
                approver = db.query(Employee).filter(Employee.id == leave_request.approved_by).first()

            # Build workflow status
            workflow_status = {
                "request_id": str(leave_request.id),
                "employee": {
                    "id": str(employee.id),
                    "name": f"{employee.first_name} {employee.last_name}",
                    "email": employee.email,
                    "department": employee.department
                },
                "leave_details": {
                    "type": leave_request.leave_policy.leave_type if leave_request.leave_policy else "Unknown",
                    "start_date": leave_request.start_date.isoformat(),
                    "end_date": leave_request.end_date.isoformat(),
                    "total_days": float(leave_request.total_days),
                    "duration_type": leave_request.duration_type,
                    "reason": leave_request.reason
                },
                "status": {
                    "current": leave_request.status,
                    "applied_at": leave_request.applied_at.isoformat(),
                    "approved_at": leave_request.approved_at.isoformat() if leave_request.approved_at else None,
                    "approved_by": {
                        "id": str(approver.id),
                        "name": f"{approver.first_name} {approver.last_name}"
                    } if approver else None,
                    "rejection_reason": leave_request.rejection_reason
                },
                "workflow_steps": [
                    {
                        "step": "submitted",
                        "title": "Application Submitted",
                        "status": "completed",
                        "timestamp": leave_request.applied_at.isoformat(),
                        "actor": f"{employee.first_name} {employee.last_name}"
                    },
                    {
                        "step": "review",
                        "title": "Under Review",
                        "status": "completed" if leave_request.status in ["approved", "rejected"] else "current",
                        "timestamp": leave_request.approved_at.isoformat() if leave_request.approved_at else None,
                        "actor": f"{approver.first_name} {approver.last_name}" if approver else "Pending"
                    }
                ]
            }

            # Add final step based on status
            if leave_request.status == "approved":
                workflow_status["workflow_steps"].append({
                    "step": "approved",
                    "title": "Approved",
                    "status": "completed",
                    "timestamp": leave_request.approved_at.isoformat(),
                    "actor": f"{approver.first_name} {approver.last_name}" if approver else "System"
                })
            elif leave_request.status == "rejected":
                workflow_status["workflow_steps"].append({
                    "step": "rejected",
                    "title": "Rejected",
                    "status": "rejected",
                    "timestamp": leave_request.approved_at.isoformat(),
                    "actor": f"{approver.first_name} {approver.last_name}" if approver else "System"
                })

            return workflow_status

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error getting leave workflow status: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Error retrieving workflow status"
            )

    async def calculate_automated_leave_days(
        self,
        start_date: date,
        end_date: date,
        duration_type: str,
        db: Session,
        organization_id: UUID,
        employee_id: UUID = None
    ) -> dict:
        """Calculate leave days with advanced logic including weekends, holidays, and working patterns"""
        try:
            total_calendar_days = (end_date - start_date).days + 1
            working_days = 0
            weekend_days = 0
            holiday_days = 0
            excluded_days = []

            # Get organization holidays
            holidays = db.query(LeaveCalendar).filter(
                LeaveCalendar.organization_id == organization_id,
                LeaveCalendar.date >= start_date,
                LeaveCalendar.date <= end_date
            ).all()

            holiday_dates = {holiday.date for holiday in holidays}

            # Get employee's working pattern if available
            working_pattern = None
            if employee_id:
                employee = db.query(Employee).filter(Employee.id == employee_id).first()
                if employee and hasattr(employee, 'working_pattern'):
                    working_pattern = employee.working_pattern

            # Default working pattern (Monday-Friday)
            default_working_days = {0, 1, 2, 3, 4}  # Monday=0, Sunday=6
            working_days_pattern = working_pattern.working_days if working_pattern else default_working_days

            # Calculate day by day
            current_date = start_date
            while current_date <= end_date:
                day_of_week = current_date.weekday()

                if current_date in holiday_dates:
                    holiday_days += 1
                    excluded_days.append({
                        'date': current_date.isoformat(),
                        'type': 'holiday',
                        'name': next((h.name for h in holidays if h.date == current_date), 'Holiday')
                    })
                elif day_of_week not in working_days_pattern:
                    weekend_days += 1
                    excluded_days.append({
                        'date': current_date.isoformat(),
                        'type': 'weekend',
                        'name': current_date.strftime('%A')
                    })
                else:
                    working_days += 1

                current_date += timedelta(days=1)

            # Apply duration type multiplier
            duration_multiplier = {
                'full_day': 1.0,
                'half_day_morning': 0.5,
                'half_day_afternoon': 0.5,
                'hourly': 0.125  # Assuming 8-hour workday
            }.get(duration_type, 1.0)

            calculated_leave_days = working_days * duration_multiplier

            return {
                'total_calendar_days': total_calendar_days,
                'working_days': working_days,
                'weekend_days': weekend_days,
                'holiday_days': holiday_days,
                'calculated_leave_days': calculated_leave_days,
                'duration_type': duration_type,
                'duration_multiplier': duration_multiplier,
                'excluded_days': excluded_days,
                'calculation_breakdown': {
                    'start_date': start_date.isoformat(),
                    'end_date': end_date.isoformat(),
                    'working_pattern': list(working_days_pattern),
                    'holidays_excluded': len(holiday_dates),
                    'weekends_excluded': weekend_days
                }
            }

        except Exception as e:
            logger.error(f"Error calculating automated leave days: {e}")
            # Fallback to simple calculation
            simple_days = (end_date - start_date).days + 1
            return {
                'total_calendar_days': simple_days,
                'working_days': simple_days,
                'weekend_days': 0,
                'holiday_days': 0,
                'calculated_leave_days': simple_days,
                'duration_type': duration_type,
                'duration_multiplier': 1.0,
                'excluded_days': [],
                'calculation_breakdown': {
                    'start_date': start_date.isoformat(),
                    'end_date': end_date.isoformat(),
                    'error': 'Fallback calculation used'
                }
            }

    async def check_leave_conflicts(
        self,
        db: Session,
        employee_id: UUID,
        start_date: date,
        end_date: date,
        current_user: CurrentUser,
        exclude_request_id: UUID = None
    ) -> dict:
        """Check for leave conflicts and team impact"""
        try:
            conflicts = {
                'has_conflicts': False,
                'overlapping_requests': [],
                'team_impact': {},
                'recommendations': []
            }

            # Check for overlapping leave requests
            overlap_query = db.query(LeaveRequest).filter(
                LeaveRequest.employee_id == employee_id,
                LeaveRequest.status.in_(['pending', 'approved']),
                or_(
                    and_(LeaveRequest.start_date <= start_date, LeaveRequest.end_date >= start_date),
                    and_(LeaveRequest.start_date <= end_date, LeaveRequest.end_date >= end_date),
                    and_(LeaveRequest.start_date >= start_date, LeaveRequest.end_date <= end_date)
                )
            )

            if exclude_request_id:
                overlap_query = overlap_query.filter(LeaveRequest.id != exclude_request_id)

            overlapping_requests = overlap_query.all()

            if overlapping_requests:
                conflicts['has_conflicts'] = True
                conflicts['overlapping_requests'] = [
                    {
                        'id': str(req.id),
                        'start_date': req.start_date.isoformat(),
                        'end_date': req.end_date.isoformat(),
                        'status': req.status,
                        'leave_type': req.leave_policy.leave_type if req.leave_policy else 'Unknown'
                    }
                    for req in overlapping_requests
                ]

            # Check team impact (simplified)
            employee = db.query(Employee).filter(Employee.id == employee_id).first()
            if employee and employee.department:
                # Get team members in same department
                team_members = db.query(Employee).filter(
                    Employee.department == employee.department,
                    Employee.organization_id == current_user.organization_id,
                    Employee.id != employee_id,
                    Employee.is_active == True
                ).count()

                # Get team members on leave during the same period
                team_on_leave = db.query(LeaveRequest).join(Employee).filter(
                    Employee.department == employee.department,
                    Employee.organization_id == current_user.organization_id,
                    Employee.id != employee_id,
                    LeaveRequest.status == 'approved',
                    or_(
                        and_(LeaveRequest.start_date <= start_date, LeaveRequest.end_date >= start_date),
                        and_(LeaveRequest.start_date <= end_date, LeaveRequest.end_date >= end_date),
                        and_(LeaveRequest.start_date >= start_date, LeaveRequest.end_date <= end_date)
                    )
                ).count()

                conflicts['team_impact'] = {
                    'department': employee.department,
                    'total_team_members': team_members,
                    'members_on_leave': team_on_leave,
                    'availability_percentage': ((team_members - team_on_leave) / max(team_members, 1)) * 100 if team_members > 0 else 100
                }

                # Add recommendations based on team impact
                if team_on_leave > 0:
                    conflicts['recommendations'].append(
                        f"{team_on_leave} team member(s) will be on leave during this period"
                    )

                if conflicts['team_impact']['availability_percentage'] < 50:
                    conflicts['recommendations'].append(
                        "Consider rescheduling as team availability will be below 50%"
                    )

            return conflicts

        except Exception as e:
            logger.error(f"Error checking leave conflicts: {e}")
            return {
                'has_conflicts': False,
                'overlapping_requests': [],
                'team_impact': {},
                'recommendations': [],
                'error': str(e)
            }

    async def approve_leave_request(
        self,
        db: Session,
        leave_request_id: UUID,
        approved: bool,
        comments: Optional[str],
        current_user: CurrentUser
    ) -> LeaveRequestResponse:
        """Approve or reject leave request"""
        try:
            # Get leave request
            leave_request = db.query(LeaveRequest).join(Employee).filter(
                LeaveRequest.id == leave_request_id,
                Employee.organization_id == current_user.organization_id
            ).first()

            if not leave_request:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Leave request not found"
                )

            if leave_request.status != LeaveStatus.PENDING:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Leave request is not pending approval"
                )

            # Update leave request
            leave_request.status = LeaveStatus.APPROVED if approved else LeaveStatus.REJECTED
            leave_request.approved_by = current_user.user_id
            leave_request.approved_at = datetime.utcnow()
            leave_request.rejection_reason = comments if not approved else None

            # Update leave balance
            if approved:
                await self._finalize_leave_balance(
                    db, leave_request.employee_id, leave_request.leave_policy_id,
                    leave_request.total_days, current_user
                )
            else:
                # Revert pending balance
                await self._revert_pending_balance(
                    db, leave_request.employee_id, leave_request.leave_policy_id,
                    leave_request.total_days, current_user
                )

            db.commit()
            db.refresh(leave_request)

            # Send notification
            await self._notify_leave_request(
                leave_request, "approved" if approved else "rejected", current_user
            )

            logger.info(f"Leave request {leave_request_id} {'approved' if approved else 'rejected'}")
            return LeaveRequestResponse.from_orm(leave_request)

        except HTTPException:
            raise
        except Exception as e:
            db.rollback()
            logger.error(f"Error approving leave request: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Error processing leave approval"
            )

    async def get_leave_balance(
        self,
        db: Session,
        employee_id: UUID,
        year: Optional[int] = None,
        current_user: CurrentUser = None
    ) -> List[LeaveBalanceResponse]:
        """Get leave balance for employee"""
        try:
            if not year:
                year = date.today().year

            # First try to find employee by ID, then by user_id
            employee = db.query(Employee).filter(
                Employee.id == employee_id,
                Employee.organization_id == current_user.organization_id,
                Employee.is_active == True
            ).first()

            if not employee:
                employee = db.query(Employee).filter(
                    Employee.user_id == employee_id,
                    Employee.organization_id == current_user.organization_id,
                    Employee.is_active == True
                ).first()

            if not employee:
                return []

            balances = db.query(LeaveBalance).join(Employee).filter(
                LeaveBalance.employee_id == employee.id,
                LeaveBalance.year == year,
                Employee.organization_id == current_user.organization_id
            ).all()

            return [LeaveBalanceResponse.from_orm(balance) for balance in balances]

        except Exception as e:
            logger.error(f"Error getting leave balance: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Error retrieving leave balance"
            )

    # Helper methods
    async def _calculate_leave_days(
        self,
        start_date: date,
        end_date: date,
        duration_type: LeaveDuration,
        db: Session,
        organization_id: UUID
    ) -> Decimal:
        """Calculate total leave days excluding holidays"""
        total_days = (end_date - start_date).days + 1

        # Get holidays in the date range
        holidays = db.query(LeaveCalendar).filter(
            LeaveCalendar.organization_id == organization_id,
            LeaveCalendar.date >= start_date,
            LeaveCalendar.date <= end_date
        ).all()

        holiday_dates = {holiday.date for holiday in holidays}

        # Calculate working days
        working_days = 0
        current_date = start_date

        while current_date <= end_date:
            # Skip weekends (Saturday=5, Sunday=6)
            if current_date.weekday() < 5 and current_date not in holiday_dates:
                working_days += 1
            current_date += timedelta(days=1)

        # Apply duration type
        if duration_type == LeaveDuration.HALF_DAY_MORNING or duration_type == LeaveDuration.HALF_DAY_AFTERNOON:
            return Decimal(working_days) / 2
        elif duration_type == LeaveDuration.HOURLY:
            # For hourly, return as is (will be handled separately)
            return Decimal(working_days)
        else:
            return Decimal(working_days)

    async def _validate_leave_request(
        self,
        db: Session,
        employee_id: UUID,
        leave_policy: LeavePolicy,
        leave_data: LeaveRequestCreate,
        total_days: Decimal,
        current_user: CurrentUser
    ):
        """Validate leave request against policy"""
        # Get employee details for validation
        employee = db.query(Employee).filter(Employee.id == employee_id).first()
        if not employee:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Employee not found"
            )

        # Check gender-based eligibility (e.g., Maternity leave for females only)
        if leave_policy.applicable_genders:
            if employee.gender and employee.gender.value not in leave_policy.applicable_genders:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"This leave type is not applicable for {employee.gender.value} employees"
                )

        # Check employment type eligibility
        if leave_policy.applicable_employment_types:
            if employee.employment_type and employee.employment_type.value not in leave_policy.applicable_employment_types:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"This leave type is not applicable for {employee.employment_type.value} employees"
                )

        # Check probation period restrictions
        if not leave_policy.available_during_probation and employee.status == EmployeeStatus.PROBATION:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="This leave type is not available during probation period"
            )

        # Check notice period restrictions
        if employee.status == EmployeeStatus.NOTICE_PERIOD:
            # Only allow sick leave or unpaid leave during notice period
            if leave_policy.leave_type not in ["SICK", "LOP"]:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Only sick leave or unpaid leave allowed during notice period"
                )

        # Check minimum notice period
        days_until_start = (leave_data.start_date - date.today()).days
        if days_until_start < leave_policy.min_notice_days:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Minimum notice period is {leave_policy.min_notice_days} days"
            )

        # Check maximum consecutive days
        if leave_policy.max_consecutive_days and total_days > leave_policy.max_consecutive_days:
            # Check if manager override is allowed
            if current_user.role.upper() not in ["MANAGER", "HR", "ADMIN"]:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"Maximum consecutive days allowed is {leave_policy.max_consecutive_days}. Manager override required."
                )

        # Check minimum application days
        if total_days < leave_policy.min_application_days:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Minimum application period is {leave_policy.min_application_days} days"
            )

        # Check for overlapping leave requests
        overlapping = db.query(LeaveRequest).filter(
            LeaveRequest.employee_id == employee_id,
            LeaveRequest.status.in_([LeaveStatus.PENDING, LeaveStatus.APPROVED]),
            or_(
                and_(
                    LeaveRequest.start_date <= leave_data.start_date,
                    LeaveRequest.end_date >= leave_data.start_date
                ),
                and_(
                    LeaveRequest.start_date <= leave_data.end_date,
                    LeaveRequest.end_date >= leave_data.end_date
                ),
                and_(
                    LeaveRequest.start_date >= leave_data.start_date,
                    LeaveRequest.end_date <= leave_data.end_date
                )
            )
        ).first()

        if overlapping:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Leave request overlaps with existing leave"
            )

        # Check documentation requirements
        if leave_policy.requires_documentation and total_days >= (leave_policy.documentation_threshold or 1):
            if not leave_data.attachment_urls:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"Documentation required for leave requests of {leave_policy.documentation_threshold or 1} days or more"
                )

    async def _check_leave_balance(
        self,
        db: Session,
        employee_id: UUID,
        leave_policy_id: UUID,
        requested_days: Decimal,
        current_user: CurrentUser
    ):
        """Check if employee has sufficient leave balance"""
        year = date.today().year

        balance = db.query(LeaveBalance).filter(
            LeaveBalance.employee_id == employee_id,
            LeaveBalance.leave_policy_id == leave_policy_id,
            LeaveBalance.year == year
        ).first()

        if not balance:
            # Create balance if doesn't exist
            balance = await self._create_leave_balance(
                db, employee_id, leave_policy_id, year, current_user
            )

        if balance.available_balance < requested_days:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Insufficient leave balance. Available: {balance.available_balance}, Requested: {requested_days}"
            )

    async def _update_leave_balance_pending(
        self,
        db: Session,
        employee_id: UUID,
        leave_policy_id: UUID,
        days: Decimal,
        current_user: CurrentUser
    ):
        """Update pending balance when leave is requested"""
        year = date.today().year

        balance = db.query(LeaveBalance).filter(
            LeaveBalance.employee_id == employee_id,
            LeaveBalance.leave_policy_id == leave_policy_id,
            LeaveBalance.year == year
        ).first()

        if balance:
            balance.pending_balance += days
            balance.available_balance -= days

    async def _finalize_leave_balance(
        self,
        db: Session,
        employee_id: UUID,
        leave_policy_id: UUID,
        days: Decimal,
        current_user: CurrentUser
    ):
        """Finalize leave balance when leave is approved"""
        year = date.today().year

        balance = db.query(LeaveBalance).filter(
            LeaveBalance.employee_id == employee_id,
            LeaveBalance.leave_policy_id == leave_policy_id,
            LeaveBalance.year == year
        ).first()

        if balance:
            balance.pending_balance -= days
            balance.used_balance += days

    async def _revert_pending_balance(
        self,
        db: Session,
        employee_id: UUID,
        leave_policy_id: UUID,
        days: Decimal,
        current_user: CurrentUser
    ):
        """Revert pending balance when leave is rejected"""
        year = date.today().year

        balance = db.query(LeaveBalance).filter(
            LeaveBalance.employee_id == employee_id,
            LeaveBalance.leave_policy_id == leave_policy_id,
            LeaveBalance.year == year
        ).first()

        if balance:
            balance.pending_balance -= days
            balance.available_balance += days

    async def _notify_leave_request(
        self,
        leave_request: LeaveRequest,
        action: str,
        current_user: CurrentUser
    ):
        """Send notifications for leave request actions"""
        try:
            # Send WebSocket notification
            await notification_manager.notify_leave_status_change(
                str(leave_request.employee_id),
                {
                    "leave_request_id": str(leave_request.id),
                    "action": action,
                    "status": leave_request.status,
                    "start_date": leave_request.start_date.isoformat(),
                    "end_date": leave_request.end_date.isoformat(),
                    "total_days": float(leave_request.total_days)
                }
            )

            # Send email notifications
            await self._send_email_notifications(leave_request, action, current_user)

        except Exception as e:
            logger.error(f"Error sending leave notification: {e}")

    async def _send_email_notifications(
        self,
        leave_request: LeaveRequest,
        action: str,
        current_user: CurrentUser
    ):
        """Send email notifications for leave request actions"""
        try:
            # Get employee details from the leave_request relationship
            employee = leave_request.employee
            if not employee:
                logger.error(f"Employee not found for leave request {leave_request.id}")
                return

            # Prepare email data
            employee_name = f"{employee.first_name} {employee.last_name}"
            leave_details = {
                "employee_name": employee_name,
                "leave_type": leave_request.leave_policy.leave_type if leave_request.leave_policy else "Leave",
                "start_date": leave_request.start_date.strftime("%B %d, %Y"),
                "end_date": leave_request.end_date.strftime("%B %d, %Y"),
                "total_days": float(leave_request.total_days),
                "reason": leave_request.reason,
                "status": leave_request.status
            }

            # Determine email recipients and notification type
            email_recipients = []
            notification_type = "created"

            if action == "created":
                # Send to employee (confirmation), manager (approval request), and HR
                email_recipients.append(employee.email)  # Employee confirmation

                # Find manager - for now, we'll send to specific emails as requested
                email_recipients.extend([
                    "<EMAIL>",  # Manager
                    "<EMAIL>"        # HR/System admin
                ])
                notification_type = "created"

            elif action == "approved":
                email_recipients.append(employee.email)
                notification_type = "approved"

            elif action == "rejected":
                email_recipients.append(employee.email)
                notification_type = "rejected"

            # Send email notifications
            if email_recipients:
                await self.notification_service.send_leave_request_notification(
                    to_emails=email_recipients,
                    employee_name=employee_name,
                    leave_details=leave_details,
                    notification_type=notification_type
                )
                logger.info(f"Email notifications sent to: {', '.join(email_recipients)}")

        except Exception as e:
            logger.error(f"Error sending email notifications: {e}")

    async def _create_leave_balance(
        self,
        db: Session,
        employee_id: UUID,
        leave_policy_id: UUID,
        year: int,
        current_user: CurrentUser
    ) -> LeaveBalance:
        """Create initial leave balance for employee"""
        leave_policy = db.query(LeavePolicy).filter(
            LeavePolicy.id == leave_policy_id
        ).first()

        if not leave_policy:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Leave policy not found"
            )

        balance = LeaveBalance(
            employee_id=employee_id,
            leave_policy_id=leave_policy_id,
            year=year,
            opening_balance=Decimal(0),
            accrued_balance=leave_policy.annual_entitlement,
            used_balance=Decimal(0),
            pending_balance=Decimal(0),
            carried_forward=Decimal(0),
            available_balance=leave_policy.annual_entitlement
        )

        db.add(balance)
        db.commit()
        db.refresh(balance)

        return balance

    async def _check_auto_decision(
        self,
        db: Session,
        leave_request: LeaveRequest,
        leave_policy: LeavePolicy,
        employee: Employee,
        current_user: CurrentUser
    ) -> Optional[dict]:
        """Check if leave request can be auto-approved or auto-rejected"""
        try:
            # Auto-approve conditions
            if leave_policy.auto_approve_threshold and leave_request.total_days <= leave_policy.auto_approve_threshold:
                # Check if sufficient leave balance
                balance = db.query(LeaveBalance).filter(
                    LeaveBalance.employee_id == employee.id,
                    LeaveBalance.leave_policy_id == leave_policy.id,
                    LeaveBalance.year == date.today().year
                ).first()

                if balance and balance.available_balance >= leave_request.total_days:
                    # Check staffing levels (simplified check)
                    if await self._check_staffing_levels(db, leave_request, current_user):
                        return {
                            "status": LeaveStatus.APPROVED,
                            "action": "auto_approved",
                            "approved_by": None,  # System approval
                            "reason": "Auto-approved based on policy threshold"
                        }

            # Auto-reject conditions
            # Check if insufficient balance
            balance = db.query(LeaveBalance).filter(
                LeaveBalance.employee_id == employee.id,
                LeaveBalance.leave_policy_id == leave_policy.id,
                LeaveBalance.year == date.today().year
            ).first()

            if not balance or balance.available_balance < leave_request.total_days:
                return {
                    "status": LeaveStatus.REJECTED,
                    "action": "auto_rejected",
                    "reason": "Insufficient leave balance"
                }

            # Check blackout dates (if implemented)
            if await self._is_blackout_period(db, leave_request, current_user):
                return {
                    "status": LeaveStatus.REJECTED,
                    "action": "auto_rejected",
                    "reason": "Leave requested during blackout period"
                }

            return None  # No auto-decision, requires manual approval

        except Exception as e:
            logger.error(f"Error checking auto-decision: {e}")
            return None

    async def _check_staffing_levels(
        self,
        db: Session,
        leave_request: LeaveRequest,
        current_user: CurrentUser
    ) -> bool:
        """Check if approving leave maintains minimum staffing levels"""
        try:
            # Use the shift service for comprehensive staffing validation
            from ..shift_management.shift_service import shift_service

            impact_analysis = await shift_service.validate_leave_impact_on_staffing(
                db, leave_request.employee_id, leave_request.start_date,
                leave_request.end_date, current_user
            )

            # If there are critical staffing issues, don't auto-approve
            if not impact_analysis.get("can_approve", True):
                logger.warning(f"Leave request {leave_request.id} blocked due to staffing issues")
                return False

            # Check if there are any critical shifts affected
            critical_shifts = [
                shift for shift in impact_analysis.get("affected_shifts", [])
                if shift.get("is_critical", False)
            ]

            if critical_shifts:
                logger.warning(f"Leave request {leave_request.id} affects critical shifts")
                return False

            return True

        except Exception as e:
            logger.error(f"Error checking staffing levels with shift integration: {e}")
            # Fallback to simple department-based check
            return await self._simple_staffing_check(db, leave_request, current_user)

    async def _simple_staffing_check(
        self,
        db: Session,
        leave_request: LeaveRequest,
        current_user: CurrentUser
    ) -> bool:
        """Simple fallback staffing check"""
        try:
            # Get employee's department
            employee = db.query(Employee).filter(Employee.id == leave_request.employee_id).first()
            if not employee or not employee.department_id:
                return True  # Default to allow if no department info

            # Count employees in same department on leave during requested period
            overlapping_leaves = db.query(LeaveRequest).join(Employee).filter(
                Employee.department_id == employee.department_id,
                LeaveRequest.status == LeaveStatus.APPROVED,
                or_(
                    and_(
                        LeaveRequest.start_date <= leave_request.start_date,
                        LeaveRequest.end_date >= leave_request.start_date
                    ),
                    and_(
                        LeaveRequest.start_date <= leave_request.end_date,
                        LeaveRequest.end_date >= leave_request.end_date
                    )
                )
            ).count()

            # Get total employees in department
            total_employees = db.query(Employee).filter(
                Employee.department_id == employee.department_id,
                Employee.is_active == True
            ).count()

            # Simple rule: maintain at least 70% staffing
            min_required = max(1, int(total_employees * 0.7))
            available_staff = total_employees - overlapping_leaves - 1  # -1 for current request

            return available_staff >= min_required

        except Exception as e:
            logger.error(f"Error in simple staffing check: {e}")
            return True  # Default to allow on error

    async def _is_blackout_period(
        self,
        db: Session,
        leave_request: LeaveRequest,
        current_user: CurrentUser
    ) -> bool:
        """Check if leave request falls during blackout period"""
        try:
            # This is a placeholder - implement based on your blackout period rules
            # For example, check against a blackout_periods table or configuration
            return False

        except Exception as e:
            logger.error(f"Error checking blackout period: {e}")
            return False

    async def _schedule_auto_escalation(
        self,
        db: Session,
        leave_request: LeaveRequest,
        current_user: CurrentUser
    ):
        """Schedule auto-escalation for pending leave requests"""
        try:
            # Get employee details to determine escalation path
            employee = db.query(Employee).filter(Employee.id == leave_request.employee_id).first()
            if not employee:
                return

            # Determine escalation timeline based on employee role
            escalation_hours = 24  # Default 24 hours
            if employee.role == "manager":
                escalation_hours = 12  # Managers get faster escalation

            # Schedule escalation task (using Celery or similar)
            try:
                from ...workers.leave_tasks import escalate_pending_leave_request
                escalate_pending_leave_request.apply_async(
                    args=[str(leave_request.id)],
                    countdown=escalation_hours * 3600  # Convert to seconds
                )
            except ImportError:
                logger.warning("Celery task not available for auto-escalation")

            logger.info(f"Scheduled auto-escalation for leave request {leave_request.id} in {escalation_hours} hours")

        except Exception as e:
            logger.error(f"Error scheduling auto-escalation: {e}")

    async def create_leave_encashment(
        self,
        db: Session,
        employee_id: UUID,
        encashment_data: LeaveEncashmentCreate,
        current_user: CurrentUser
    ) -> LeaveEncashmentResponse:
        """Create leave encashment request"""
        try:
            # Verify employee exists and belongs to organization
            employee = db.query(Employee).filter(
                Employee.id == employee_id,
                Employee.organization_id == current_user.organization_id,
                Employee.is_active == True
            ).first()

            if not employee:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Employee not found"
                )

            # Verify leave policy exists
            leave_policy = db.query(LeavePolicy).filter(
                LeavePolicy.id == encashment_data.leave_policy_id,
                LeavePolicy.organization_id == current_user.organization_id,
                LeavePolicy.is_active == True
            ).first()

            if not leave_policy:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Leave policy not found"
                )

            # Check if leave type allows encashment
            if not leave_policy.allow_encashment:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="This leave type does not allow encashment"
                )

            # Get current leave balance
            balance = db.query(LeaveBalance).filter(
                LeaveBalance.employee_id == employee_id,
                LeaveBalance.leave_policy_id == encashment_data.leave_policy_id,
                LeaveBalance.year == date.today().year
            ).first()

            if not balance:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Leave balance not found"
                )

            # Validate encashment eligibility
            await self._validate_encashment_eligibility(
                db, employee, leave_policy, balance, encashment_data, current_user
            )

            # Calculate encashment amount
            rate_per_day, total_amount = await self._calculate_encashment_amount(
                db, employee, leave_policy, encashment_data.days_to_encash, current_user
            )

            # Create encashment request
            encashment = LeaveEncashment(
                employee_id=employee_id,
                leave_policy_id=encashment_data.leave_policy_id,
                days_to_encash=encashment_data.days_to_encash,
                rate_per_day=rate_per_day,
                total_amount=total_amount,
                reason=encashment_data.reason,
                status=LeaveStatus.PENDING,
                requested_at=datetime.utcnow()
            )

            db.add(encashment)
            db.commit()
            db.refresh(encashment)

            # Send notification to HR
            await self._notify_encashment_request(encashment, "created", current_user)

            logger.info(f"Leave encashment request created: {encashment.id} for employee {employee_id}")
            return LeaveEncashmentResponse.from_orm(encashment)

        except HTTPException:
            raise
        except Exception as e:
            db.rollback()
            logger.error(f"Error creating leave encashment: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Error creating leave encashment request"
            )

    async def _validate_encashment_eligibility(
        self,
        db: Session,
        employee: Employee,
        leave_policy: LeavePolicy,
        balance: LeaveBalance,
        encashment_data: LeaveEncashmentCreate,
        current_user: CurrentUser
    ):
        """Validate if employee is eligible for leave encashment"""
        # Check minimum balance requirement
        min_balance_required = getattr(leave_policy, 'min_balance_for_encashment', 5)
        remaining_balance = balance.available_balance - encashment_data.days_to_encash

        if remaining_balance < min_balance_required:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Minimum {min_balance_required} days must remain after encashment"
            )

        # Check maximum encashment limit
        max_encashment_days = getattr(leave_policy, 'max_encashment_days_per_year', 10)
        if encashment_data.days_to_encash > max_encashment_days:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Maximum {max_encashment_days} days can be encashed per year"
            )

        # Check if employee has already encashed this year
        current_year = date.today().year
        existing_encashments = db.query(LeaveEncashment).filter(
            LeaveEncashment.employee_id == employee.id,
            LeaveEncashment.leave_policy_id == leave_policy.id,
            func.extract('year', LeaveEncashment.requested_at) == current_year,
            LeaveEncashment.status.in_([LeaveStatus.APPROVED, LeaveStatus.PENDING])
        ).all()

        total_encashed_days = sum(enc.days_to_encash for enc in existing_encashments)
        if total_encashed_days + encashment_data.days_to_encash > max_encashment_days:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Total encashment for the year would exceed {max_encashment_days} days limit"
            )

        # Check frequency limit (e.g., once per quarter)
        encashment_frequency = getattr(leave_policy, 'encashment_frequency_months', 3)
        recent_encashments = db.query(LeaveEncashment).filter(
            LeaveEncashment.employee_id == employee.id,
            LeaveEncashment.leave_policy_id == leave_policy.id,
            LeaveEncashment.requested_at >= datetime.utcnow() - timedelta(days=encashment_frequency * 30),
            LeaveEncashment.status == LeaveStatus.APPROVED
        ).first()

        if recent_encashments:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Encashment allowed only once every {encashment_frequency} months"
            )

    async def _calculate_encashment_amount(
        self,
        db: Session,
        employee: Employee,
        leave_policy: LeavePolicy,
        days_to_encash: Decimal,
        current_user: CurrentUser
    ) -> tuple[Decimal, Decimal]:
        """Calculate encashment rate and total amount"""
        try:
            # Get employee's basic salary (this would typically come from payroll integration)
            # For now, using a default calculation
            basic_salary = getattr(employee, 'basic_salary', Decimal('30000'))  # Default salary

            # Calculate daily rate (basic salary / 30 days)
            daily_rate = basic_salary / 30

            # Apply encashment rate (typically 100% of basic salary)
            encashment_rate = getattr(leave_policy, 'encashment_rate_percentage', 100) / 100
            rate_per_day = daily_rate * Decimal(str(encashment_rate))

            # Calculate total amount
            total_amount = rate_per_day * days_to_encash

            # Apply any tax deductions (simplified)
            tax_rate = Decimal('0.1')  # 10% tax
            total_amount_after_tax = total_amount * (1 - tax_rate)

            return rate_per_day, total_amount_after_tax

        except Exception as e:
            logger.error(f"Error calculating encashment amount: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Error calculating encashment amount"
            )

    async def _notify_encashment_request(
        self,
        encashment: LeaveEncashment,
        action: str,
        current_user: CurrentUser
    ):
        """Send notifications for encashment request actions"""
        try:
            await notification_manager.notify_encashment_status_change(
                str(encashment.employee_id),
                {
                    "encashment_id": str(encashment.id),
                    "action": action,
                    "status": encashment.status,
                    "days_to_encash": float(encashment.days_to_encash),
                    "total_amount": float(encashment.total_amount)
                }
            )
        except Exception as e:
            logger.error(f"Error sending encashment notification: {e}")

    async def get_leave_encashments(
        self,
        db: Session,
        employee_id: Optional[UUID] = None,
        status: Optional[LeaveStatus] = None,
        skip: int = 0,
        limit: int = 20,
        current_user: CurrentUser = None
    ) -> List[LeaveEncashmentResponse]:
        """Get leave encashment requests with filtering"""
        try:
            query = db.query(LeaveEncashment).join(Employee).filter(
                Employee.organization_id == current_user.organization_id
            )

            if employee_id:
                query = query.filter(LeaveEncashment.employee_id == employee_id)

            if status:
                query = query.filter(LeaveEncashment.status == status)

            # Apply role-based filtering
            if current_user.role.upper() not in ["ADMIN", "HR"]:
                # Non-admin users can only see their own encashments
                query = query.filter(LeaveEncashment.employee_id == current_user.user_id)

            encashments = query.offset(skip).limit(limit).all()
            return [LeaveEncashmentResponse.from_orm(enc) for enc in encashments]

        except Exception as e:
            logger.error(f"Error getting leave encashments: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Error retrieving leave encashments"
            )

    async def approve_leave_encashment(
        self,
        db: Session,
        encashment_id: UUID,
        approved: bool,
        comments: Optional[str],
        current_user: CurrentUser
    ) -> LeaveEncashmentResponse:
        """Approve or reject leave encashment request"""
        try:
            # Get encashment request
            encashment = db.query(LeaveEncashment).join(Employee).filter(
                LeaveEncashment.id == encashment_id,
                Employee.organization_id == current_user.organization_id
            ).first()

            if not encashment:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Encashment request not found"
                )

            if encashment.status != LeaveStatus.PENDING:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Encashment request is not pending approval"
                )

            # Update encashment request
            encashment.status = LeaveStatus.APPROVED if approved else LeaveStatus.REJECTED
            encashment.approved_by = current_user.user_id
            encashment.approved_at = datetime.utcnow()

            if approved:
                # Update leave balance - reduce available balance
                balance = db.query(LeaveBalance).filter(
                    LeaveBalance.employee_id == encashment.employee_id,
                    LeaveBalance.leave_policy_id == encashment.leave_policy_id,
                    LeaveBalance.year == date.today().year
                ).first()

                if balance:
                    balance.available_balance -= encashment.days_to_encash
                    balance.used_balance += encashment.days_to_encash

                # Create payroll integration record (placeholder)
                await self._integrate_with_payroll(encashment, current_user)

            db.commit()
            db.refresh(encashment)

            # Send notification
            await self._notify_encashment_request(
                encashment, "approved" if approved else "rejected", current_user
            )

            logger.info(f"Leave encashment {encashment_id} {'approved' if approved else 'rejected'}")
            return LeaveEncashmentResponse.from_orm(encashment)

        except HTTPException:
            raise
        except Exception as e:
            db.rollback()
            logger.error(f"Error approving leave encashment: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Error processing encashment approval"
            )

    async def _integrate_with_payroll(
        self,
        encashment: LeaveEncashment,
        current_user: CurrentUser
    ):
        """Integrate approved encashment with payroll system"""
        try:
            # This is a placeholder for payroll integration
            # In a real system, this would:
            # 1. Create a payroll entry for the encashment amount
            # 2. Apply tax calculations
            # 3. Generate payment reference
            # 4. Update employee's payslip for the month

            # For now, just generate a payment reference
            payment_ref = f"ENC-{encashment.id.hex[:8].upper()}-{datetime.now().strftime('%Y%m')}"
            encashment.payment_reference = payment_ref
            encashment.processed_by = current_user.user_id
            encashment.processed_at = datetime.utcnow()

            logger.info(f"Encashment {encashment.id} integrated with payroll: {payment_ref}")

        except Exception as e:
            logger.error(f"Error integrating with payroll: {e}")
            # Don't raise exception as this is supplementary functionality

    # Leave Policy Management Methods
    async def get_leave_policies(
        self,
        db: Session,
        current_user: CurrentUser,
        skip: int = 0,
        limit: int = 100,
        leave_type: Optional[str] = None,
        is_active: Optional[bool] = True
    ) -> List[LeavePolicyResponse]:
        """Get leave policies for the organization"""
        try:
            query = db.query(LeavePolicy).filter(
                LeavePolicy.organization_id == current_user.organization_id
            )

            if leave_type:
                query = query.filter(LeavePolicy.leave_type == leave_type)

            if is_active is not None:
                query = query.filter(LeavePolicy.is_active == is_active)

            policies = query.offset(skip).limit(limit).all()

            return [LeavePolicyResponse.from_orm(policy) for policy in policies]

        except Exception as e:
            logger.error(f"Error getting leave policies: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Error retrieving leave policies"
            )

    async def create_leave_policy(
        self,
        db: Session,
        policy_data: LeavePolicyCreate,
        current_user: CurrentUser
    ) -> LeavePolicyResponse:
        """Create new leave policy"""
        try:
            # Check if policy with same name and type already exists
            existing_policy = db.query(LeavePolicy).filter(
                LeavePolicy.organization_id == current_user.organization_id,
                LeavePolicy.name == policy_data.name,
                LeavePolicy.leave_type == policy_data.leave_type,
                LeavePolicy.is_active == True
            ).first()

            if existing_policy:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Leave policy with this name and type already exists"
                )

            # Create new policy
            policy = LeavePolicy(
                organization_id=current_user.organization_id,
                **policy_data.dict()
            )

            db.add(policy)
            db.commit()
            db.refresh(policy)

            logger.info(f"Created leave policy {policy.id} by user {current_user.user_id}")
            return LeavePolicyResponse.from_orm(policy)

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error creating leave policy: {e}")
            db.rollback()
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Error creating leave policy"
            )

    async def get_leave_policy(
        self,
        db: Session,
        policy_id: UUID,
        current_user: CurrentUser
    ) -> LeavePolicyResponse:
        """Get specific leave policy"""
        try:
            policy = db.query(LeavePolicy).filter(
                LeavePolicy.id == policy_id,
                LeavePolicy.organization_id == current_user.organization_id
            ).first()

            if not policy:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Leave policy not found"
                )

            return LeavePolicyResponse.from_orm(policy)

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error getting leave policy {policy_id}: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Error retrieving leave policy"
            )

    async def update_leave_policy(
        self,
        db: Session,
        policy_id: UUID,
        policy_data: LeavePolicyUpdate,
        current_user: CurrentUser
    ) -> LeavePolicyResponse:
        """Update leave policy"""
        try:
            policy = db.query(LeavePolicy).filter(
                LeavePolicy.id == policy_id,
                LeavePolicy.organization_id == current_user.organization_id
            ).first()

            if not policy:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Leave policy not found"
                )

            # Update policy fields
            update_data = policy_data.dict(exclude_unset=True)
            for field, value in update_data.items():
                setattr(policy, field, value)

            db.commit()
            db.refresh(policy)

            logger.info(f"Updated leave policy {policy_id} by user {current_user.user_id}")
            return LeavePolicyResponse.from_orm(policy)

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error updating leave policy {policy_id}: {e}")
            db.rollback()
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Error updating leave policy"
            )

    async def delete_leave_policy(
        self,
        db: Session,
        policy_id: UUID,
        current_user: CurrentUser
    ) -> dict:
        """Delete leave policy (soft delete)"""
        try:
            policy = db.query(LeavePolicy).filter(
                LeavePolicy.id == policy_id,
                LeavePolicy.organization_id == current_user.organization_id
            ).first()

            if not policy:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Leave policy not found"
                )

            # Check if policy is being used by any leave requests
            active_requests = db.query(LeaveRequest).filter(
                LeaveRequest.leave_policy_id == policy_id,
                LeaveRequest.status.in_([LeaveStatus.PENDING, LeaveStatus.APPROVED])
            ).count()

            if active_requests > 0:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Cannot delete policy with active leave requests"
                )

            # Soft delete
            policy.is_active = False
            db.commit()

            logger.info(f"Deleted leave policy {policy_id} by user {current_user.user_id}")
            return {"message": "Leave policy deleted successfully"}

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error deleting leave policy {policy_id}: {e}")
            db.rollback()
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Error deleting leave policy"
            )
