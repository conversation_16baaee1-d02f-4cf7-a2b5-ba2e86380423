from core.databases.database import db
from core.models.basemodel import ModelBase
from sqlalchemy.orm import relationship
from datetime import datetime

class BankModel(ModelBase):
    __tablename__ = "banks"

    id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    name = db.Column(db.String(45), nullable=False)
    sort_code = db.Column(db.String(45), nullable=False)
    user_id = db.Column(db.Integer, db.<PERSON>ey((ModelBase.dbSchema() + '.users.id')), nullable=False) 
    timestamp = db.Column(db.DateTime, nullable=False, default=datetime.now()) 
