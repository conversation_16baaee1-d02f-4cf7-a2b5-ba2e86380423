from flask.views import MethodView
from flask_smorest import Blueprint, abort
from sqlalchemy.exc import SQLAlchemyError
from core.middleware import roles_required
from schemas import EmployeeActivitySchema
from core.services.activity_employee import EmployeeActivityService
from core.utils.responseBuilder import ResponseBuilder

blueprint = Blueprint("EmployeeActivity", __name__, description="Operations for Employee Activity Logs")

@blueprint.route("/employee-activities/<id>")
class EmployeeActivity(MethodView):
    @roles_required(['employee'])
    @blueprint.response(200, EmployeeActivitySchema)
    def get(self, id):
        service = EmployeeActivityService()
        activity = service.getActivityById(id)
        if not activity:
            abort(404, message="Employee activity not found")
        return ResponseBuilder(data=EmployeeActivitySchema().dump(activity), status_code=200).build()

    @roles_required(['employee'])
    def delete(self, id):
        service = EmployeeActivityService()
        activity = service.getActivityById(id)
        if not activity:
            abort(404, message="Employee activity not found")
        service.deleteActivity(id)
        return {"message": "Employee activity deleted successfully"}

    @roles_required(['employee'])
    @blueprint.arguments(EmployeeActivitySchema)
    @blueprint.response(201, EmployeeActivitySchema)
    def put(self, data, id):
        service = EmployeeActivityService()
        activity = service.getActivityById(id)
        if not activity:
            abort(404, message="Employee activity not found")
        try:
            updated = service.repository.updateActivity(id, **data)
            return updated
        except SQLAlchemyError:
            abort(500, message="Error updating employee activity")

@blueprint.route("/employee-activities")
class EmployeeActivityList(MethodView):
    @roles_required(['employee'])
    @blueprint.response(200, EmployeeActivitySchema)
    def get(self):
        service = EmployeeActivityService()
        # Optional: could filter by employee_id if needed
        activities, total = service.fetchAllActivitiesForEmployee(employee_id=None)
        return ResponseBuilder(data=EmployeeActivitySchema(many=True).dump(activities), status_code=200, total=total).build()

    @roles_required(['employee'])
    @blueprint.arguments(EmployeeActivitySchema)
    @blueprint.response(201, EmployeeActivitySchema)
    def post(self, data):
        try:
            service = EmployeeActivityService()
            created = service.logActivity(data.get("employee_id"), data.get("message"))
            return created
        except SQLAlchemyError:
            abort(500, message="Failed to log employee activity")
