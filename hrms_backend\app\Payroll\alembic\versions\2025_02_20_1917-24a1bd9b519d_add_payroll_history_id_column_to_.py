"""Add payroll_history_id  column to transaction_history

Revision ID: 24a1bd9b519d
Revises: 35704f6d5691
Create Date: 2025-02-20 19:17:39.326522

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy import Column, Integer, String, Float
from sqlalchemy import func, DateTime, ForeignKey
from sqlalchemy.dialects.postgresql import BOOLEAN


# revision identifiers, used by Alembic.
revision: str = '24a1bd9b519d'
down_revision: Union[str, None] = '35704f6d5691'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    op.add_column("transaction_history", sa.Column('payroll_history_id', Integer, nullable=True))


def downgrade() -> None:
    op.drop_column("transaction_history","payroll_history_id")



