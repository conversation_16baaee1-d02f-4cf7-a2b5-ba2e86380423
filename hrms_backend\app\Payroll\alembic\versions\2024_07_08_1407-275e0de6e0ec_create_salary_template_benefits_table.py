"""Create salary template benefits table

Revision ID: 275e0de6e0ec
Revises: 06ceeda520ed
Create Date: 2024-07-08 14:07:28.392406

"""
from typing import Sequence, Union

from alembic import op
from sqlalchemy import func, TIMESTAMP, ForeignKey
from sqlalchemy import <PERSON>umn, Integer, String

# revision identifiers, used by Alembic.
revision: str = '275e0de6e0ec'
down_revision: Union[str, None] = '06ceeda520ed'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None

def upgrade() -> None:
    op.create_table(
        'salary_template_benefits_pivot',
        Column('id', Integer, primary_key=True),
        <PERSON>umn('salary_template_id', Integer, ForeignKey('salary_templates.id')),
        Column('benefits_id', Integer, ForeignKey('benefits.id'))
    )


def downgrade() -> None:
    op.drop_table("salary_template_benefits_pivot")
