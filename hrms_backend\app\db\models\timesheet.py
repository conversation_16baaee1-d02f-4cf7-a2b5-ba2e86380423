from sqlalchemy import Column, String, DateTime, Date, Foreign<PERSON>ey, Boolean, Text, Numeric, Integer, Enum
from sqlalchemy.dialects.postgresql import UUID, JSONB
from sqlalchemy.orm import relationship
from enum import Enum as PyEnum
from datetime import datetime, date
from typing import Optional

from ..base import BaseModel, AuditMixin


class TimesheetStatus(PyEnum):
    DRAFT = "draft"
    SUBMITTED = "submitted"
    APPROVED = "approved"
    REJECTED = "rejected"
    LOCKED = "locked"


class EntryType(PyEnum):
    REGULAR = "regular"
    OVERTIME = "overtime"
    BREAK = "break"
    MEETING = "meeting"
    TRAINING = "training"
    TRAVEL = "travel"


class BillableType(PyEnum):
    BILLABLE = "billable"
    NON_BILLABLE = "non_billable"
    INTERNAL = "internal"


class TimesheetEntry(BaseModel, AuditMixin):
    """Individual timesheet entry"""
    __tablename__ = "timesheet_entries"

    employee_id = Column(UUID(as_uuid=True), ForeignKey("employees.id"), nullable=False, index=True)
    date = Column(Date, nullable=False, index=True)

    # Time tracking
    start_time = Column(DateTime(timezone=True), nullable=True)
    end_time = Column(DateTime(timezone=True), nullable=True)
    duration_hours = Column(Numeric(5, 2), nullable=False)  # Manual or calculated

    # Project and task association
    project_id = Column(UUID(as_uuid=True), ForeignKey("projects.id"), nullable=True)
    task_id = Column(UUID(as_uuid=True), ForeignKey("tasks.id"), nullable=True)

    # Entry details
    description = Column(Text, nullable=False)
    entry_type = Column(Enum(EntryType), nullable=False, default=EntryType.REGULAR)
    billable_type = Column(Enum(BillableType), nullable=False, default=BillableType.BILLABLE)

    # Billing information
    hourly_rate = Column(Numeric(8, 2), nullable=True)
    billable_amount = Column(Numeric(10, 2), nullable=True)

    # Approval
    status = Column(Enum(TimesheetStatus), nullable=False, default=TimesheetStatus.DRAFT)
    approved_by = Column(UUID(as_uuid=True), ForeignKey("employees.id"), nullable=True)
    approved_at = Column(DateTime(timezone=True), nullable=True)
    rejection_reason = Column(Text, nullable=True)

    # Location and device info
    location = Column(String(255), nullable=True)
    ip_address = Column(String(45), nullable=True)
    device_info = Column(JSONB, nullable=True)

    # Timer tracking
    timer_started_at = Column(DateTime(timezone=True), nullable=True)
    is_timer_running = Column(Boolean, default=False)

    # Relationships
    employee = relationship("Employee", back_populates="timesheet_entries", foreign_keys=[employee_id])
    project = relationship("Project", back_populates="timesheet_entries")
    task = relationship("Task", back_populates="timesheet_entries")
    approver = relationship("Employee", foreign_keys=[approved_by])


class TimesheetPeriod(BaseModel):
    """Timesheet period configuration"""
    __tablename__ = "timesheet_periods"

    organization_id = Column(UUID(as_uuid=True), nullable=False, index=True)
    name = Column(String(200), nullable=False)

    # Period configuration
    start_date = Column(Date, nullable=False)
    end_date = Column(Date, nullable=False)

    # Submission settings
    submission_deadline = Column(Date, nullable=True)
    auto_submit = Column(Boolean, default=False)

    # Approval settings
    approval_deadline = Column(Date, nullable=True)
    auto_approve = Column(Boolean, default=False)

    # Status
    is_locked = Column(Boolean, default=False)
    locked_at = Column(DateTime(timezone=True), nullable=True)
    locked_by = Column(UUID(as_uuid=True), ForeignKey("employees.id"), nullable=True)

    # Relationships
    locker = relationship("Employee", foreign_keys=[locked_by])


class TimesheetTemplate(BaseModel):
    """Timesheet template for recurring entries"""
    __tablename__ = "timesheet_templates"

    employee_id = Column(UUID(as_uuid=True), ForeignKey("employees.id"), nullable=False)
    name = Column(String(200), nullable=False)

    # Template configuration
    project_id = Column(UUID(as_uuid=True), ForeignKey("projects.id"), nullable=True)
    task_id = Column(UUID(as_uuid=True), ForeignKey("tasks.id"), nullable=True)
    description = Column(Text, nullable=False)
    default_hours = Column(Numeric(5, 2), nullable=True)
    entry_type = Column(Enum(EntryType), nullable=False, default=EntryType.REGULAR)
    billable_type = Column(Enum(BillableType), nullable=False, default=BillableType.BILLABLE)

    # Recurrence settings
    is_recurring = Column(Boolean, default=False)
    recurrence_pattern = Column(JSONB, nullable=True)  # Days of week, frequency, etc.

    # Relationships
    employee = relationship("Employee")
    project = relationship("Project")
    task = relationship("Task")


class TimeTracker(BaseModel):
    """Real-time time tracking sessions"""
    __tablename__ = "time_trackers"

    employee_id = Column(UUID(as_uuid=True), ForeignKey("employees.id"), nullable=False, index=True)

    # Session details
    session_start = Column(DateTime(timezone=True), nullable=False)
    session_end = Column(DateTime(timezone=True), nullable=True)
    is_active = Column(Boolean, default=True)

    # Associated work
    project_id = Column(UUID(as_uuid=True), ForeignKey("projects.id"), nullable=True)
    task_id = Column(UUID(as_uuid=True), ForeignKey("tasks.id"), nullable=True)
    description = Column(Text, nullable=True)

    # Tracking metadata
    total_duration = Column(Integer, nullable=True)  # in seconds
    break_duration = Column(Integer, nullable=True, default=0)  # in seconds

    # Device and location
    device_info = Column(JSONB, nullable=True)
    location = Column(String(255), nullable=True)
    ip_address = Column(String(45), nullable=True)

    # Screenshots (optional)
    screenshot_urls = Column(JSONB, nullable=True)  # Array of screenshot URLs
    screenshot_interval = Column(Integer, nullable=True, default=300)  # seconds

    # Relationships
    employee = relationship("Employee")
    project = relationship("Project")
    task = relationship("Task")


class TimesheetApproval(BaseModel, AuditMixin):
    """Timesheet approval workflow"""
    __tablename__ = "timesheet_approvals"

    employee_id = Column(UUID(as_uuid=True), ForeignKey("employees.id"), nullable=False)
    period_start = Column(Date, nullable=False)
    period_end = Column(Date, nullable=False)

    # Submission
    submitted_at = Column(DateTime(timezone=True), nullable=True)
    total_hours = Column(Numeric(6, 2), nullable=False)
    total_billable_hours = Column(Numeric(6, 2), nullable=False)

    # Approval
    status = Column(Enum(TimesheetStatus), nullable=False, default=TimesheetStatus.DRAFT)
    approved_by = Column(UUID(as_uuid=True), ForeignKey("employees.id"), nullable=True)
    approved_at = Column(DateTime(timezone=True), nullable=True)
    rejection_reason = Column(Text, nullable=True)

    # Comments
    employee_comments = Column(Text, nullable=True)
    approver_comments = Column(Text, nullable=True)

    # Relationships
    employee = relationship("Employee", foreign_keys=[employee_id])
    approver = relationship("Employee", foreign_keys=[approved_by])
