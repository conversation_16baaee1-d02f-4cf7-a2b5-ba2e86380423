#!/usr/bin/env python3
"""
Comprehensive API and Workflow Testing Suite
Tests all API endpoints and business workflows in the HRMS backend
"""

import sys
import os
import json
import logging
import asyncio
import requests
import time
from datetime import datetime, timedelta
from uuid import uuid4
from typing import Dict, List, Any, Optional

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class ComprehensiveAPITester:
    """Comprehensive API and workflow testing class"""

    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url
        self.session = requests.Session()
        self.test_results = []
        self.test_data = {}
        self.auth_token = None
        self.user_credentials = {}

    def log_test(self, test_name: str, success: bool, message: str = "", details: Any = None):
        """Log test results"""
        result = {
            "test_name": test_name,
            "success": success,
            "message": message,
            "details": details,
            "timestamp": datetime.utcnow().isoformat()
        }
        self.test_results.append(result)

        status = "✅ PASS" if success else "❌ FAIL"
        logger.info(f"{status} - {test_name}: {message}")

    def test_server_health(self) -> bool:
        """Test server health and availability"""
        try:
            response = self.session.get(f"{self.base_url}/health", timeout=10)
            if response.status_code == 200:
                health_data = response.json()
                self.log_test("Server Health", True,
                             f"Server is healthy",
                             health_data)
                return True
            else:
                self.log_test("Server Health", False,
                             f"Health check failed with status {response.status_code}")
                return False
        except requests.exceptions.RequestException as e:
            self.log_test("Server Health", False, f"Cannot connect to server: {str(e)}")
            return False

    def test_api_documentation(self) -> bool:
        """Test API documentation endpoints"""
        try:
            # Test OpenAPI docs
            response = self.session.get(f"{self.base_url}/docs", timeout=10)
            if response.status_code == 200:
                self.log_test("API Documentation", True, "OpenAPI docs accessible")
            else:
                self.log_test("API Documentation", False,
                             f"Docs returned status {response.status_code}")
                return False

            # Test OpenAPI JSON schema
            response = self.session.get(f"{self.base_url}/openapi.json", timeout=10)
            if response.status_code == 200:
                openapi_spec = response.json()
                endpoints_count = len(openapi_spec.get("paths", {}))
                self.log_test("OpenAPI Schema", True,
                             f"Found {endpoints_count} API endpoints",
                             {"endpoints": endpoints_count, "version": openapi_spec.get("info", {}).get("version")})
                return True
            else:
                self.log_test("OpenAPI Schema", False,
                             f"OpenAPI JSON returned status {response.status_code}")
                return False

        except Exception as e:
            self.log_test("API Documentation", False, f"Error: {str(e)}")
            return False

    def test_authentication_workflow(self) -> bool:
        """Test complete authentication workflow"""
        try:
            # Test user registration
            registration_data = {
                "email": "<EMAIL>",
                "password": "TestPassword123!",
                "first_name": "API",
                "last_name": "Test",
                "role": "EMPLOYEE"
            }

            response = self.session.post(f"{self.base_url}/api/auth/register",
                                       json=registration_data, timeout=10)

            if response.status_code in [200, 201]:
                reg_result = response.json()
                self.log_test("User Registration", True,
                             "User registered successfully",
                             {"user_id": reg_result.get("id")})
                self.test_data["user_id"] = reg_result.get("id")
            elif response.status_code == 409:
                self.log_test("User Registration", True,
                             "User already exists (expected for repeated tests)")
            else:
                self.log_test("User Registration", False,
                             f"Registration failed with status {response.status_code}",
                             response.text)

            # Test user login
            login_data = {
                "username": registration_data["email"],
                "password": registration_data["password"]
            }

            response = self.session.post(f"{self.base_url}/api/auth/login",
                                       json=login_data, timeout=10)

            if response.status_code == 200:
                login_result = response.json()
                self.auth_token = login_result.get("access_token")
                if self.auth_token:
                    self.session.headers.update({"Authorization": f"Bearer {self.auth_token}"})
                    self.log_test("User Login", True,
                                 "Login successful, token obtained",
                                 {"token_type": login_result.get("token_type")})
                    self.user_credentials = registration_data
                    return True
                else:
                    self.log_test("User Login", False, "No access token in response")
                    return False
            else:
                self.log_test("User Login", False,
                             f"Login failed with status {response.status_code}",
                             response.text)
                return False

        except Exception as e:
            self.log_test("Authentication Workflow", False, f"Error: {str(e)}")
            return False

    def test_user_management_apis(self) -> bool:
        """Test user management API endpoints"""
        try:
            # Test get current user profile
            response = self.session.get(f"{self.base_url}/api/users/me", timeout=10)
            if response.status_code == 200:
                user_profile = response.json()
                self.log_test("Get User Profile", True,
                             "User profile retrieved successfully",
                             {"email": user_profile.get("email")})
                self.test_data["current_user"] = user_profile
            else:
                self.log_test("Get User Profile", False,
                             f"Failed with status {response.status_code}")

            # Test update user profile
            update_data = {
                "first_name": "Updated API",
                "last_name": "Test User"
            }

            response = self.session.put(f"{self.base_url}/api/users/me",
                                      json=update_data, timeout=10)
            if response.status_code == 200:
                self.log_test("Update User Profile", True, "Profile updated successfully")
            else:
                self.log_test("Update User Profile", False,
                             f"Failed with status {response.status_code}")

            # Test get all users (admin endpoint)
            response = self.session.get(f"{self.base_url}/api/users/", timeout=10)
            if response.status_code in [200, 403]:  # 403 expected for non-admin users
                if response.status_code == 200:
                    users_list = response.json()
                    self.log_test("List Users", True,
                                 f"Retrieved {len(users_list)} users")
                else:
                    self.log_test("List Users", True,
                                 "Access denied (expected for non-admin)")
            else:
                self.log_test("List Users", False,
                             f"Failed with status {response.status_code}")

            return True

        except Exception as e:
            self.log_test("User Management APIs", False, f"Error: {str(e)}")
            return False

    def test_employee_management_apis(self) -> bool:
        """Test employee management API endpoints"""
        try:
            # Test create employee
            employee_data = {
                "first_name": "Test",
                "last_name": "Employee",
                "email": "<EMAIL>",
                "department": "IT",
                "position": "Software Developer",
                "hire_date": datetime.utcnow().isoformat()
            }

            response = self.session.post(f"{self.base_url}/api/employees/",
                                       json=employee_data, timeout=10)
            if response.status_code in [200, 201]:
                employee_result = response.json()
                self.log_test("Create Employee", True,
                             "Employee created successfully",
                             {"employee_id": employee_result.get("id")})
                self.test_data["employee_id"] = employee_result.get("id")
            else:
                self.log_test("Create Employee", False,
                             f"Failed with status {response.status_code}",
                             response.text)

            # Test get employees list
            response = self.session.get(f"{self.base_url}/api/employees/", timeout=10)
            if response.status_code == 200:
                employees_list = response.json()
                self.log_test("List Employees", True,
                             f"Retrieved {len(employees_list)} employees")
            else:
                self.log_test("List Employees", False,
                             f"Failed with status {response.status_code}")

            # Test get specific employee
            if self.test_data.get("employee_id"):
                response = self.session.get(f"{self.base_url}/api/employees/{self.test_data['employee_id']}",
                                          timeout=10)
                if response.status_code == 200:
                    employee_detail = response.json()
                    self.log_test("Get Employee Detail", True,
                                 "Employee details retrieved successfully")
                else:
                    self.log_test("Get Employee Detail", False,
                                 f"Failed with status {response.status_code}")

            # Test update employee
            if self.test_data.get("employee_id"):
                update_data = {
                    "position": "Senior Software Developer",
                    "department": "Engineering"
                }
                response = self.session.put(f"{self.base_url}/api/employees/{self.test_data['employee_id']}",
                                          json=update_data, timeout=10)
                if response.status_code == 200:
                    self.log_test("Update Employee", True, "Employee updated successfully")
                else:
                    self.log_test("Update Employee", False,
                                 f"Failed with status {response.status_code}")

            return True

        except Exception as e:
            self.log_test("Employee Management APIs", False, f"Error: {str(e)}")
            return False

    def test_ticket_management_workflow(self) -> bool:
        """Test complete ticket management workflow"""
        try:
            # Test create ticket
            ticket_data = {
                "title": "API Test Ticket",
                "description": "This is a test ticket created via API testing",
                "ticket_type": "IT_SUPPORT",
                "priority": "MEDIUM",
                "category": "Software",
                "contact_method": "web"
            }

            response = self.session.post(f"{self.base_url}/api/tickets/",
                                       json=ticket_data, timeout=10)
            if response.status_code in [200, 201]:
                ticket_result = response.json()
                self.log_test("Create Ticket", True,
                             "Ticket created successfully",
                             {"ticket_id": ticket_result.get("id"),
                              "ticket_number": ticket_result.get("ticket_number")})
                self.test_data["ticket_id"] = ticket_result.get("id")
                self.test_data["ticket_number"] = ticket_result.get("ticket_number")
            else:
                self.log_test("Create Ticket", False,
                             f"Failed with status {response.status_code}",
                             response.text)

            # Test get tickets list
            response = self.session.get(f"{self.base_url}/api/tickets/", timeout=10)
            if response.status_code == 200:
                tickets_list = response.json()
                self.log_test("List Tickets", True,
                             f"Retrieved {len(tickets_list)} tickets")
            else:
                self.log_test("List Tickets", False,
                             f"Failed with status {response.status_code}")

            # Test get specific ticket
            if self.test_data.get("ticket_id"):
                response = self.session.get(f"{self.base_url}/api/tickets/{self.test_data['ticket_id']}",
                                          timeout=10)
                if response.status_code == 200:
                    ticket_detail = response.json()
                    self.log_test("Get Ticket Detail", True,
                                 "Ticket details retrieved successfully")
                else:
                    self.log_test("Get Ticket Detail", False,
                                 f"Failed with status {response.status_code}")

            # Test update ticket status
            if self.test_data.get("ticket_id"):
                update_data = {
                    "status": "IN_PROGRESS",
                    "priority": "HIGH"
                }
                response = self.session.put(f"{self.base_url}/api/tickets/{self.test_data['ticket_id']}",
                                          json=update_data, timeout=10)
                if response.status_code == 200:
                    self.log_test("Update Ticket", True, "Ticket updated successfully")
                else:
                    self.log_test("Update Ticket", False,
                                 f"Failed with status {response.status_code}")

            # Test add ticket comment
            if self.test_data.get("ticket_id"):
                comment_data = {
                    "content": "This is a test comment added via API",
                    "is_internal": False
                }
                response = self.session.post(f"{self.base_url}/api/tickets/{self.test_data['ticket_id']}/comments",
                                           json=comment_data, timeout=10)
                if response.status_code in [200, 201]:
                    self.log_test("Add Ticket Comment", True, "Comment added successfully")
                else:
                    self.log_test("Add Ticket Comment", False,
                                 f"Failed with status {response.status_code}")

            # Test get ticket comments
            if self.test_data.get("ticket_id"):
                response = self.session.get(f"{self.base_url}/api/tickets/{self.test_data['ticket_id']}/comments",
                                          timeout=10)
                if response.status_code == 200:
                    comments_list = response.json()
                    self.log_test("Get Ticket Comments", True,
                                 f"Retrieved {len(comments_list)} comments")
                else:
                    self.log_test("Get Ticket Comments", False,
                                 f"Failed with status {response.status_code}")

            return True

        except Exception as e:
            self.log_test("Ticket Management Workflow", False, f"Error: {str(e)}")
            return False

    def test_leave_management_workflow(self) -> bool:
        """Test leave management workflow"""
        try:
            # Test create leave request
            leave_data = {
                "leave_type": "ANNUAL",
                "start_date": (datetime.utcnow() + timedelta(days=7)).isoformat(),
                "end_date": (datetime.utcnow() + timedelta(days=9)).isoformat(),
                "duration": "FULL_DAY",
                "reason": "API testing leave request"
            }

            response = self.session.post(f"{self.base_url}/api/leave/requests",
                                       json=leave_data, timeout=10)
            if response.status_code in [200, 201]:
                leave_result = response.json()
                self.log_test("Create Leave Request", True,
                             "Leave request created successfully",
                             {"leave_id": leave_result.get("id")})
                self.test_data["leave_id"] = leave_result.get("id")
            else:
                self.log_test("Create Leave Request", False,
                             f"Failed with status {response.status_code}",
                             response.text)

            # Test get leave requests
            response = self.session.get(f"{self.base_url}/api/leave/requests", timeout=10)
            if response.status_code == 200:
                leave_list = response.json()
                self.log_test("List Leave Requests", True,
                             f"Retrieved {len(leave_list)} leave requests")
            else:
                self.log_test("List Leave Requests", False,
                             f"Failed with status {response.status_code}")

            # Test get leave balance
            response = self.session.get(f"{self.base_url}/api/leave/balance", timeout=10)
            if response.status_code == 200:
                balance_data = response.json()
                self.log_test("Get Leave Balance", True,
                             "Leave balance retrieved successfully",
                             balance_data)
            else:
                self.log_test("Get Leave Balance", False,
                             f"Failed with status {response.status_code}")

            return True

        except Exception as e:
            self.log_test("Leave Management Workflow", False, f"Error: {str(e)}")
            return False

    def test_attendance_management_apis(self) -> bool:
        """Test attendance management APIs"""
        try:
            # Test clock in
            clock_in_data = {
                "timestamp": datetime.utcnow().isoformat(),
                "location": "Office",
                "notes": "API test clock in"
            }

            response = self.session.post(f"{self.base_url}/api/attendance/clock-in",
                                       json=clock_in_data, timeout=10)
            if response.status_code in [200, 201]:
                clock_result = response.json()
                self.log_test("Clock In", True,
                             "Clock in successful",
                             {"attendance_id": clock_result.get("id")})
                self.test_data["attendance_id"] = clock_result.get("id")
            else:
                self.log_test("Clock In", False,
                             f"Failed with status {response.status_code}",
                             response.text)

            # Test get attendance records
            response = self.session.get(f"{self.base_url}/api/attendance/records", timeout=10)
            if response.status_code == 200:
                attendance_list = response.json()
                self.log_test("Get Attendance Records", True,
                             f"Retrieved {len(attendance_list)} attendance records")
            else:
                self.log_test("Get Attendance Records", False,
                             f"Failed with status {response.status_code}")

            # Test clock out (after a short delay)
            time.sleep(2)
            clock_out_data = {
                "timestamp": datetime.utcnow().isoformat(),
                "notes": "API test clock out"
            }

            response = self.session.post(f"{self.base_url}/api/attendance/clock-out",
                                       json=clock_out_data, timeout=10)
            if response.status_code in [200, 201]:
                self.log_test("Clock Out", True, "Clock out successful")
            else:
                self.log_test("Clock Out", False,
                             f"Failed with status {response.status_code}")

            return True

        except Exception as e:
            self.log_test("Attendance Management APIs", False, f"Error: {str(e)}")
            return False

    def test_project_management_apis(self) -> bool:
        """Test project management APIs"""
        try:
            # Test create project
            project_data = {
                "name": "API Test Project",
                "description": "Project created for API testing",
                "start_date": datetime.utcnow().isoformat(),
                "end_date": (datetime.utcnow() + timedelta(days=30)).isoformat(),
                "priority": "MEDIUM",
                "status": "PLANNING"
            }

            response = self.session.post(f"{self.base_url}/api/projects/",
                                       json=project_data, timeout=10)
            if response.status_code in [200, 201]:
                project_result = response.json()
                self.log_test("Create Project", True,
                             "Project created successfully",
                             {"project_id": project_result.get("id")})
                self.test_data["project_id"] = project_result.get("id")
            else:
                self.log_test("Create Project", False,
                             f"Failed with status {response.status_code}",
                             response.text)

            # Test get projects list
            response = self.session.get(f"{self.base_url}/api/projects/", timeout=10)
            if response.status_code == 200:
                projects_list = response.json()
                self.log_test("List Projects", True,
                             f"Retrieved {len(projects_list)} projects")
            else:
                self.log_test("List Projects", False,
                             f"Failed with status {response.status_code}")

            # Test create kanban board
            if self.test_data.get("project_id"):
                board_data = {
                    "name": "API Test Board",
                    "description": "Kanban board for API testing",
                    "project_id": self.test_data["project_id"],
                    "board_type": "PROJECT"
                }

                response = self.session.post(f"{self.base_url}/api/kanban/boards",
                                           json=board_data, timeout=10)
                if response.status_code in [200, 201]:
                    board_result = response.json()
                    self.log_test("Create Kanban Board", True,
                                 "Kanban board created successfully",
                                 {"board_id": board_result.get("id")})
                    self.test_data["board_id"] = board_result.get("id")
                else:
                    self.log_test("Create Kanban Board", False,
                                 f"Failed with status {response.status_code}")

            return True

        except Exception as e:
            self.log_test("Project Management APIs", False, f"Error: {str(e)}")
            return False

    def test_ai_powered_features(self) -> bool:
        """Test AI-powered features and smart routing"""
        try:
            # Test AI ticket categorization
            if self.test_data.get("ticket_id"):
                response = self.session.post(f"{self.base_url}/api/ai/categorize-ticket/{self.test_data['ticket_id']}",
                                           timeout=15)
                if response.status_code == 200:
                    ai_result = response.json()
                    self.log_test("AI Ticket Categorization", True,
                                 "AI categorization successful",
                                 ai_result)
                else:
                    self.log_test("AI Ticket Categorization", False,
                                 f"Failed with status {response.status_code}")

            # Test sentiment analysis
            sentiment_data = {
                "text": "I am very frustrated with this issue and need urgent help!"
            }

            response = self.session.post(f"{self.base_url}/api/ai/sentiment-analysis",
                                       json=sentiment_data, timeout=15)
            if response.status_code == 200:
                sentiment_result = response.json()
                self.log_test("AI Sentiment Analysis", True,
                             "Sentiment analysis successful",
                             sentiment_result)
            else:
                self.log_test("AI Sentiment Analysis", False,
                             f"Failed with status {response.status_code}")

            # Test smart routing
            if self.test_data.get("ticket_id"):
                response = self.session.post(f"{self.base_url}/api/ai/smart-route/{self.test_data['ticket_id']}",
                                           timeout=15)
                if response.status_code == 200:
                    routing_result = response.json()
                    self.log_test("AI Smart Routing", True,
                                 "Smart routing successful",
                                 routing_result)
                else:
                    self.log_test("AI Smart Routing", False,
                                 f"Failed with status {response.status_code}")

            return True

        except Exception as e:
            self.log_test("AI Powered Features", False, f"Error: {str(e)}")
            return False

    def test_notification_system(self) -> bool:
        """Test notification system"""
        try:
            # Test get notifications
            response = self.session.get(f"{self.base_url}/api/notifications/", timeout=10)
            if response.status_code == 200:
                notifications_list = response.json()
                self.log_test("Get Notifications", True,
                             f"Retrieved {len(notifications_list)} notifications")
            else:
                self.log_test("Get Notifications", False,
                             f"Failed with status {response.status_code}")

            # Test mark notification as read
            if self.test_data.get("ticket_id"):
                # Create a test notification first
                notification_data = {
                    "title": "API Test Notification",
                    "message": "This is a test notification created via API",
                    "type": "info"
                }

                response = self.session.post(f"{self.base_url}/api/notifications/",
                                           json=notification_data, timeout=10)
                if response.status_code in [200, 201]:
                    notification_result = response.json()
                    notification_id = notification_result.get("id")

                    # Mark as read
                    response = self.session.put(f"{self.base_url}/api/notifications/{notification_id}/read",
                                              timeout=10)
                    if response.status_code == 200:
                        self.log_test("Mark Notification Read", True,
                                     "Notification marked as read")
                    else:
                        self.log_test("Mark Notification Read", False,
                                     f"Failed with status {response.status_code}")

            return True

        except Exception as e:
            self.log_test("Notification System", False, f"Error: {str(e)}")
            return False

    def test_reporting_apis(self) -> bool:
        """Test reporting and analytics APIs"""
        try:
            # Test dashboard statistics
            response = self.session.get(f"{self.base_url}/api/reports/dashboard", timeout=10)
            if response.status_code == 200:
                dashboard_data = response.json()
                self.log_test("Dashboard Statistics", True,
                             "Dashboard data retrieved successfully",
                             dashboard_data)
            else:
                self.log_test("Dashboard Statistics", False,
                             f"Failed with status {response.status_code}")

            # Test ticket analytics
            response = self.session.get(f"{self.base_url}/api/reports/tickets/analytics", timeout=10)
            if response.status_code == 200:
                analytics_data = response.json()
                self.log_test("Ticket Analytics", True,
                             "Ticket analytics retrieved successfully",
                             analytics_data)
            else:
                self.log_test("Ticket Analytics", False,
                             f"Failed with status {response.status_code}")

            # Test attendance report
            response = self.session.get(f"{self.base_url}/api/reports/attendance", timeout=10)
            if response.status_code == 200:
                attendance_report = response.json()
                self.log_test("Attendance Report", True,
                             "Attendance report retrieved successfully")
            else:
                self.log_test("Attendance Report", False,
                             f"Failed with status {response.status_code}")

            return True

        except Exception as e:
            self.log_test("Reporting APIs", False, f"Error: {str(e)}")
            return False

    def test_admin_apis(self) -> bool:
        """Test admin-specific APIs"""
        try:
            # Test system settings
            response = self.session.get(f"{self.base_url}/api/admin/settings", timeout=10)
            if response.status_code in [200, 403]:  # 403 expected for non-admin users
                if response.status_code == 200:
                    settings_data = response.json()
                    self.log_test("System Settings", True,
                                 "System settings retrieved successfully")
                else:
                    self.log_test("System Settings", True,
                                 "Access denied (expected for non-admin)")
            else:
                self.log_test("System Settings", False,
                             f"Failed with status {response.status_code}")

            # Test audit logs
            response = self.session.get(f"{self.base_url}/api/admin/audit-logs", timeout=10)
            if response.status_code in [200, 403]:
                if response.status_code == 200:
                    audit_logs = response.json()
                    self.log_test("Audit Logs", True,
                                 f"Retrieved {len(audit_logs)} audit logs")
                else:
                    self.log_test("Audit Logs", True,
                                 "Access denied (expected for non-admin)")
            else:
                self.log_test("Audit Logs", False,
                             f"Failed with status {response.status_code}")

            return True

        except Exception as e:
            self.log_test("Admin APIs", False, f"Error: {str(e)}")
            return False

    def test_websocket_connections(self) -> bool:
        """Test WebSocket connections for real-time features"""
        try:
            # Test WebSocket endpoint availability
            import websocket

            ws_url = self.base_url.replace("http://", "ws://") + "/ws"

            def on_message(ws, message):
                self.log_test("WebSocket Message", True, f"Received: {message}")

            def on_error(ws, error):
                self.log_test("WebSocket Error", False, f"Error: {error}")

            def on_close(ws, close_status_code, close_msg):
                self.log_test("WebSocket Close", True, "Connection closed")

            def on_open(ws):
                self.log_test("WebSocket Connection", True, "WebSocket connected successfully")
                # Send a test message
                ws.send(json.dumps({"type": "ping", "data": "test"}))
                # Close after a short delay
                import threading
                def close_connection():
                    time.sleep(2)
                    ws.close()
                threading.Thread(target=close_connection).start()

            try:
                ws = websocket.WebSocketApp(ws_url,
                                          on_open=on_open,
                                          on_message=on_message,
                                          on_error=on_error,
                                          on_close=on_close)
                ws.run_forever(timeout=5)
            except Exception as e:
                self.log_test("WebSocket Connection", False, f"WebSocket test failed: {str(e)}")

            return True

        except ImportError:
            self.log_test("WebSocket Test", False, "websocket-client not installed")
            return False
        except Exception as e:
            self.log_test("WebSocket Connections", False, f"Error: {str(e)}")
            return False

    def cleanup_test_data(self) -> bool:
        """Clean up test data created during testing"""
        try:
            cleanup_success = True

            # Delete test ticket
            if self.test_data.get("ticket_id"):
                response = self.session.delete(f"{self.base_url}/api/tickets/{self.test_data['ticket_id']}",
                                             timeout=10)
                if response.status_code in [200, 204, 404]:
                    self.log_test("Cleanup Ticket", True, "Test ticket deleted")
                else:
                    self.log_test("Cleanup Ticket", False, f"Failed with status {response.status_code}")
                    cleanup_success = False

            # Delete test employee
            if self.test_data.get("employee_id"):
                response = self.session.delete(f"{self.base_url}/api/employees/{self.test_data['employee_id']}",
                                             timeout=10)
                if response.status_code in [200, 204, 404]:
                    self.log_test("Cleanup Employee", True, "Test employee deleted")
                else:
                    self.log_test("Cleanup Employee", False, f"Failed with status {response.status_code}")
                    cleanup_success = False

            # Delete test project
            if self.test_data.get("project_id"):
                response = self.session.delete(f"{self.base_url}/api/projects/{self.test_data['project_id']}",
                                             timeout=10)
                if response.status_code in [200, 204, 404]:
                    self.log_test("Cleanup Project", True, "Test project deleted")
                else:
                    self.log_test("Cleanup Project", False, f"Failed with status {response.status_code}")
                    cleanup_success = False

            return cleanup_success

        except Exception as e:
            self.log_test("Cleanup Test Data", False, f"Error: {str(e)}")
            return False

    def generate_comprehensive_report(self) -> dict:
        """Generate comprehensive API testing report"""
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results if result['success'])
        failed_tests = total_tests - passed_tests

        # Categorize tests
        categories = {
            "Authentication": [],
            "User Management": [],
            "Employee Management": [],
            "Ticket Management": [],
            "Leave Management": [],
            "Attendance Management": [],
            "Project Management": [],
            "AI Features": [],
            "Notifications": [],
            "Reporting": [],
            "Admin": [],
            "WebSocket": [],
            "Cleanup": [],
            "Infrastructure": []
        }

        for result in self.test_results:
            test_name = result['test_name']
            if any(keyword in test_name.lower() for keyword in ['auth', 'login', 'register']):
                categories["Authentication"].append(result)
            elif 'user' in test_name.lower():
                categories["User Management"].append(result)
            elif 'employee' in test_name.lower():
                categories["Employee Management"].append(result)
            elif 'ticket' in test_name.lower():
                categories["Ticket Management"].append(result)
            elif 'leave' in test_name.lower():
                categories["Leave Management"].append(result)
            elif 'attendance' in test_name.lower():
                categories["Attendance Management"].append(result)
            elif 'project' in test_name.lower() or 'kanban' in test_name.lower():
                categories["Project Management"].append(result)
            elif 'ai' in test_name.lower() or 'sentiment' in test_name.lower():
                categories["AI Features"].append(result)
            elif 'notification' in test_name.lower():
                categories["Notifications"].append(result)
            elif 'report' in test_name.lower() or 'dashboard' in test_name.lower() or 'analytics' in test_name.lower():
                categories["Reporting"].append(result)
            elif 'admin' in test_name.lower() or 'audit' in test_name.lower():
                categories["Admin"].append(result)
            elif 'websocket' in test_name.lower():
                categories["WebSocket"].append(result)
            elif 'cleanup' in test_name.lower():
                categories["Cleanup"].append(result)
            else:
                categories["Infrastructure"].append(result)

        report = {
            "test_summary": {
                "total_tests": total_tests,
                "passed_tests": passed_tests,
                "failed_tests": failed_tests,
                "success_rate": round((passed_tests / total_tests) * 100, 2) if total_tests > 0 else 0,
                "test_duration": "N/A",  # Could be calculated if we track start/end times
                "timestamp": datetime.utcnow().isoformat()
            },
            "api_coverage": {
                "authentication_apis": len(categories["Authentication"]),
                "user_management_apis": len(categories["User Management"]),
                "employee_management_apis": len(categories["Employee Management"]),
                "ticket_management_apis": len(categories["Ticket Management"]),
                "leave_management_apis": len(categories["Leave Management"]),
                "attendance_apis": len(categories["Attendance Management"]),
                "project_management_apis": len(categories["Project Management"]),
                "ai_powered_apis": len(categories["AI Features"]),
                "notification_apis": len(categories["Notifications"]),
                "reporting_apis": len(categories["Reporting"]),
                "admin_apis": len(categories["Admin"]),
                "websocket_apis": len(categories["WebSocket"])
            },
            "workflow_verification": {
                "user_registration_login": any(r['success'] for r in categories["Authentication"]),
                "ticket_lifecycle": any(r['success'] for r in categories["Ticket Management"]),
                "employee_onboarding": any(r['success'] for r in categories["Employee Management"]),
                "leave_request_process": any(r['success'] for r in categories["Leave Management"]),
                "attendance_tracking": any(r['success'] for r in categories["Attendance Management"]),
                "project_creation": any(r['success'] for r in categories["Project Management"]),
                "ai_enhancement": any(r['success'] for r in categories["AI Features"]),
                "real_time_notifications": any(r['success'] for r in categories["Notifications"])
            },
            "test_categories": categories,
            "test_details": self.test_results,
            "test_data_created": self.test_data
        }

        return report


def main():
    """Main API testing execution"""
    print("🚀 COMPREHENSIVE API AND WORKFLOW TESTING")
    print("=" * 80)
    print(f"Target Server: http://localhost:8000")
    print(f"Test Start Time: {datetime.utcnow().isoformat()}")
    print("=" * 80)

    tester = ComprehensiveAPITester()

    # Execute comprehensive API tests
    test_suites = [
        ("Server Health Check", tester.test_server_health),
        ("API Documentation", tester.test_api_documentation),
        ("Authentication Workflow", tester.test_authentication_workflow),
        ("User Management APIs", tester.test_user_management_apis),
        ("Employee Management APIs", tester.test_employee_management_apis),
        ("Ticket Management Workflow", tester.test_ticket_management_workflow),
        ("Leave Management Workflow", tester.test_leave_management_workflow),
        ("Attendance Management APIs", tester.test_attendance_management_apis),
        ("Project Management APIs", tester.test_project_management_apis),
        ("AI Powered Features", tester.test_ai_powered_features),
        ("Notification System", tester.test_notification_system),
        ("Reporting APIs", tester.test_reporting_apis),
        ("Admin APIs", tester.test_admin_apis),
        ("WebSocket Connections", tester.test_websocket_connections),
        ("Cleanup Test Data", tester.cleanup_test_data)
    ]

    for suite_name, test_func in test_suites:
        print(f"\n🔍 Testing: {suite_name}")
        try:
            test_func()
        except Exception as e:
            tester.log_test(suite_name, False, f"Unexpected error: {str(e)}")

    # Generate comprehensive report
    report = tester.generate_comprehensive_report()

    # Save report
    with open('comprehensive_api_test_report.json', 'w') as f:
        json.dump(report, f, indent=2)

    # Display results
    print("\n" + "=" * 80)
    print("📊 COMPREHENSIVE API TESTING RESULTS")
    print("=" * 80)
    print(f"Total API Tests: {report['test_summary']['total_tests']}")
    print(f"Tests Passed: {report['test_summary']['passed_tests']}")
    print(f"Tests Failed: {report['test_summary']['failed_tests']}")
    print(f"Overall Success Rate: {report['test_summary']['success_rate']}%")

    # Show API coverage
    print(f"\n📋 API COVERAGE:")
    for api_type, count in report['api_coverage'].items():
        print(f"  • {api_type.replace('_', ' ').title()}: {count} tests")

    # Show workflow verification
    print(f"\n🔄 WORKFLOW VERIFICATION:")
    for workflow, status in report['workflow_verification'].items():
        status_icon = "✅" if status else "❌"
        print(f"  {status_icon} {workflow.replace('_', ' ').title()}")

    # Show failed tests
    if report['test_summary']['failed_tests'] > 0:
        print(f"\n❌ FAILED TESTS ({report['test_summary']['failed_tests']}):")
        for result in report['test_details']:
            if not result['success']:
                print(f"  • {result['test_name']}: {result['message']}")

    # Final verdict
    success_rate = report['test_summary']['success_rate']
    print(f"\n🎯 FINAL API TESTING VERDICT:")

    if success_rate >= 95:
        print("🎉 OUTSTANDING! All APIs and workflows are fully functional!")
        print("✅ Ready for production deployment")
    elif success_rate >= 85:
        print("🎉 EXCELLENT! APIs and workflows are working well!")
        print("⚠️ Minor issues detected, but core functionality is solid")
    elif success_rate >= 70:
        print("✅ GOOD! Most APIs are functional")
        print("🔧 Some features need attention before production")
    elif success_rate >= 50:
        print("⚠️ PARTIAL! Core APIs working, significant issues detected")
        print("🚨 Requires immediate attention")
    else:
        print("❌ CRITICAL! Major API failures detected")
        print("🚨 System not ready for production")

    print(f"\n📄 Detailed report saved to: comprehensive_api_test_report.json")
    print("=" * 80)

    return success_rate >= 70


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)