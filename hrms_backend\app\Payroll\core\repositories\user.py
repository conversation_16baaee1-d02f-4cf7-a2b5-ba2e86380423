from core.models.user import UserModel
from core.databases.database import db
from flask_jwt_extended import get_jwt_identity, get_current_user, get_jwt
from core.models.employees import EmployeeModel

class UserRepository:
    @classmethod
    def authUserId(self):
        return get_jwt_identity()
    
    @classmethod
    def fetchUsers(self, offset: int, size: int = 50):
        users = UserModel.query.order_by(UserModel.created_at.desc()).offset(offset=offset).limit(limit=size).all()
        return users

    def authUser(self):
        jwt_data = get_jwt()
        user_id = get_jwt_identity()

        if jwt_data.get("is_admin"):
            return UserModel.query.get(user_id)
            # return UserModel.query.filter_by(id=self.authUserId()).first()
        elif jwt_data.get("is_employee"):
            return EmployeeModel.query.get(user_id)
        return None
    
    def isAdmin():
        return get_jwt().get("is_admin", False)

    def isEmployee():
        return get_jwt().get("is_employee", False)
    
    def authUserType():
        jwt_data = get_jwt()
        if jwt_data.get("is_admin"):
            return "admin"
        elif jwt_data.get("is_employee"):
            return "employee"
        return "unknown"

    def updateLoggedInUser(self, kwargs):
        user = UserModel.query.filter_by(id=self.authUserId()).first()
        if user:
            for key, value in kwargs.items():
                setattr(user, key, value)
            db.session.commit()
            return user
        else:
            return None