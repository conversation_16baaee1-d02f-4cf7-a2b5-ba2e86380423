from core.repositories.benefits import BenefitsRepository

class BenefitsService:
    def __init__(self) -> None:
        self.repository = BenefitsRepository()

    def createBenefits(self, Kwargs):
        return self.repository.createBenefits(**Kwargs)
    
    def fetchAll(self):
        benefits = self.repository.fetchAll()
        total_benefits = len(benefits)
        return benefits, total_benefits
    
    def getBenefits(self, id):
        return self.repository.getBenefits(id)
    
    def updateBenefits(self, id, Kwargs):
        return self.repository.updateBenefits(id, **Kwargs)
    
    def getBenefitsByKey(self, Kwarg):
        return self.repository.getBenefitsByKeys(Kwarg)
    
    def deleteBenefits(self, id):
        return self.repository.deleteBenefits(id)
    
            
    def assignBenefitToEmployee(self, employee_id, benefit_id):
        """
        Assign a benefit to an employee if not already assigned.
        """
        existing_benefit = self.repository.getAttachedEmployeeBenefit(employee_id, benefit_id)
        if not existing_benefit:
            self.repository.create(employee_id, benefit_id)