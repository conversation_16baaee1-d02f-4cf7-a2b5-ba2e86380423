"""create approval_settings table

Revision ID: 5382ee23b696
Revises: 4d6aef792dcd
Create Date: 2024-04-03 15:04:03.625039

"""
from typing import Sequence, Union

from alembic import op
from sqlalchemy import func, TIMESTAMP, Foreign<PERSON>ey
from sqlalchemy import <PERSON>umn, Integer, String

# revision identifiers, used by Alembic.
revision: str = '5382ee23b696'
down_revision: Union[str, None] = '4d6aef792dcd'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    op.create_table(
        'approval_settings',
        Column('id', Integer, primary_key=True),
        <PERSON>umn('number_of_approval', Integer, nullable=False),
        <PERSON><PERSON>n("user_id", Integer, ForeignKey("users.id")),
        <PERSON>umn("timestamp", TIMESTAMP, server_default=func.now()),
    )


def downgrade() -> None:
    op.drop_table("approval_settings")
