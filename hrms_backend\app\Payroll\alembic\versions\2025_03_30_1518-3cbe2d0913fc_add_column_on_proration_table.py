"""Add column on  proration table

Revision ID: 3cbe2d0913fc
Revises: ee88a82c70c1
Create Date: 2025-03-30 15:18:55.805360

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '3cbe2d0913fc'
down_revision: Union[str, None] = 'ee88a82c70c1'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    op.add_column('prorate_salary', sa.Column('tax_overtime', sa.<PERSON>(), nullable=True, server_default=sa.text("false")))
    op.add_column('prorate_salary', sa.Column('overtime_hours', sa.Float(), nullable=True, server_default="0.0"))
    op.add_column('prorate_salary', sa.Column('overtime_rate', sa.Float(), nullable=True, server_default="0.0"))
    op.add_column('prorate_salary', sa.Column('overtime_amount', sa.Float(), nullable=True, server_default="0.0"))
    op.add_column('prorate_salary', sa.Column('salary_arrears', sa.Float(), nullable=True, server_default="0.0"))
    op.add_column('prorate_salary', sa.Column('surcharge', sa.Float(), nullable=True, server_default="0.0"))

def downgrade() -> None:
    op.drop_column('prorate_salary', 'tax_overtime')
    op.drop_column('prorate_salary', 'overtime_hours')
    op.drop_column('prorate_salary', 'overtime_rate')
    op.drop_column('prorate_salary', 'overtime_amount')
    op.drop_column('prorate_salary', 'salary_arrears')
    op.drop_column('prorate_salary', 'surcharge')

