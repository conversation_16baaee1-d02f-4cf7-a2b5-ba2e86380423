"""create salary_templates table

Revision ID: 3e39e8ce74f0
Revises: 0c4a93e5a071
Create Date: 2024-04-03 15:03:57.271792

"""
from typing import Sequence, Union

from alembic import op
from sqlalchemy import func, TIMESTAMP, ForeignKey
from sqlalchemy import Column, Float, Integer, String, Text

# revision identifiers, used by Alembic.
revision: str = '3e39e8ce74f0'
down_revision: Union[str, None] = '0c4a93e5a071'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None

def upgrade() -> None:
     op.create_table(
        'salary_templates',
        Column('id', Integer, primary_key=True),
        Column('template_name', String(100), nullable=True),
        Column('employment_type', String(45), nullable=True),
        Column('description', Text, nullable=True),
        Column('level', String(100), nullable=True),
        Column('salary_type', String(45), nullable=True),
        Column('employee_type', String(45), nullable=True),
        Column('country', String(45), nullable=True),
        Column('currency', String(45), nullable=True),
        Column('rate', String(45), nullable=True),
        Column('tax_type', String(45), nullable=True),
        Column('work_schedule', String(45), nullable=True),
        Column('hours_worked', String(45), nullable=True),
        Column('work_duration', String(45), nullable=True),
        Column('gross_pay', Float, nullable=True),
        Column('user_id', Integer, ForeignKey("users.id")),
        Column("timestamp", TIMESTAMP, server_default=func.now()),
    )


def downgrade() -> None:
    op.drop_table("salary_templates")