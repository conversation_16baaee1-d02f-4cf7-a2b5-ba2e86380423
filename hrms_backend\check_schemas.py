#!/usr/bin/env python3
"""
Check table schemas for leave_requests and attendance_records
"""

import sys
import os

# Add the app directory to Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'app'))

from sqlalchemy import text
from app.db.session import engine

def check_schemas():
    """Check table schemas"""
    try:
        with engine.connect() as conn:
            print('=== LEAVE_REQUESTS TABLE COLUMNS ===')
            result = conn.execute(text("""
                SELECT column_name, data_type FROM information_schema.columns 
                WHERE table_name = 'leave_requests' AND table_schema = 'public'
                ORDER BY ordinal_position
            """))
            leave_columns = result.fetchall()
            for row in leave_columns:
                print(f'  {row[0]} ({row[1]})')
            
            print('\n=== ATTENDANCE_RECORDS TABLE COLUMNS ===')
            result = conn.execute(text("""
                SELECT column_name, data_type FROM information_schema.columns 
                WHERE table_name = 'attendance_records' AND table_schema = 'public'
                ORDER BY ordinal_position
            """))
            attendance_columns = result.fetchall()
            for row in attendance_columns:
                print(f'  {row[0]} ({row[1]})')
                
    except Exception as e:
        print(f"Error checking schemas: {e}")

if __name__ == "__main__":
    check_schemas()
