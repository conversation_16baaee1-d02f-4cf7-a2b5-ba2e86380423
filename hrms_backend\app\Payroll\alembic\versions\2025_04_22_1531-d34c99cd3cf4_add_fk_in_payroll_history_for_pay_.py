"""add FK in payroll_history for pay_schedle_id

Revision ID: d34c99cd3cf4
Revises: 6e4dc2f646f4
Create Date: 2025-04-22 15:31:34.176455

"""
from typing import Sequence, Union
from sqlalchemy import Column, Integer, String, Float
from alembic import op
import sqlalchemy as sa
from sqlalchemy import func, DateTime, ForeignKey
from sqlalchemy.dialects.postgresql import BOOLEAN
from sqlalchemy import func, TIMESTAMP, ForeignKey


# revision identifiers, used by Alembic.
revision: str = 'd34c99cd3cf4'
down_revision: Union[str, None] = '6e4dc2f646f4'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # Add the column first
    op.add_column('payroll_history', sa.Column('pay_schedle_id', sa.Integer, nullable=True))

    # Then create the FK constraint with ON DELETE SET NULL
    op.create_foreign_key(
        'fk_payroll_history_pay_schedle_id_pay_schedules',
        'payroll_history', 'pay_schedules',
        ['pay_schedle_id'], ['id'],
        ondelete='SET NULL'
    )


def downgrade() -> None:
    # Drop the foreign key constraint first
    op.drop_constraint(
        'fk_payroll_history_pay_schedle_id_pay_schedules',
        'payroll_history',
        type_='foreignkey'
    )

    # Then drop the column
    op.drop_column('payroll_history', 'pay_schedle_id')
