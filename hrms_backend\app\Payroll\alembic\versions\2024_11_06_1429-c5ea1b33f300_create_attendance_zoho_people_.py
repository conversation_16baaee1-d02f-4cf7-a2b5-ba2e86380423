"""Create attendance_zoho_people_integration table

Revision ID: c5ea1b33f300
Revises: b654da68485a
Create Date: 2024-11-06 14:29:04.876749

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy import func, TIMESTAMP, ForeignKey
from sqlalchemy import Column, Integer, String


# revision identifiers, used by Alembic.
revision: str = 'c5ea1b33f300'
down_revision: Union[str, None] = 'b654da68485a'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    op.create_table(
        'attendance_zoho_people_integration',
        Column('id', Integer, primary_key=True),
        Column('first_name', String(45), nullable=True),
        Column('last_name', String(45), nullable=True),
        Column('email', String(45), nullable=False),
        Column('gender', String(45), nullable=False),
        Column('organization_profile', String(45), nullable=False),
        Column('employee_status', String(45), nullable=False),
        Column('employee_type', String(45), nullable=False),
        Column('employee_id', String(45), nullable=False),
        Column('bank_code', String(45), nullable=False),
        Column('bank_name', String(45), nullable=False),
        Column('account_no', String(45), nullable=False),
        Column('department', String(45), nullable=False),
        Column('designation', String(45), nullable=False),
        Column('salary_templates', String(45), nullable=False),
        Column('mobile_no', String(45), nullable=False),
        Column('tax_type', String(45), nullable=False),
        Column('business_unit', String(45), nullable=False),
        Column('employee_type', String(45), nullable=False),
        Column('division', String(45), nullable=False),
        Column('location', String(45), nullable=False),
        Column('gross_pay', String(45), nullable=False),
        Column('nhf_no', String(45), nullable=False),
        Column('nhf_mortgage_bank', String(45), nullable=False),
        Column('pension_pfa', String(45), nullable=False),
        Column('pfa_no', String(45), nullable=False),
        Column('currency', String(45), nullable=False),
        Column('annual_leave_days', String(45), nullable=False),
        Column('unpaid_leave_days', String(45), nullable=False),
        Column('sick_leave_days', String(45), nullable=False),
        Column('maternity_paternity_leave_days', String(45), nullable=False),
        Column('casual_leave_days', String(45), nullable=False),
        Column('compassionate_leave_days', String(45), nullable=False),
        Column('total_working_days', String(45), nullable=False),
        Column('total_present_days', String(45), nullable=False),
        Column('total_absent_days', String(45), nullable=False),
        Column("timestamp", TIMESTAMP, server_default=func.now()),
        
    )

def downgrade() -> None:
    op.drop_table("attendance_zoho_people_integration")
