from pydantic import BaseModel, Field, validator
from typing import Optional, List, Dict, Any, Union
from datetime import datetime, date
from uuid import UUID
from enum import Enum


class ReportType(str, Enum):
    """Report type enumeration"""
    EMPLOYEE_REPORT = "employee_report"
    ATTENDANCE_REPORT = "attendance_report"
    LEAVE_REPORT = "leave_report"
    PAYROLL_REPORT = "payroll_report"
    PERFORMANCE_REPORT = "performance_report"
    RECRUITMENT_REPORT = "recruitment_report"
    TRAINING_REPORT = "training_report"
    COMPLIANCE_REPORT = "compliance_report"
    CUSTOM_REPORT = "custom_report"


class ReportFormat(str, Enum):
    """Report format enumeration"""
    PDF = "pdf"
    EXCEL = "excel"
    CSV = "csv"
    JSON = "json"


class ReportFrequency(str, Enum):
    """Report frequency enumeration"""
    DAILY = "daily"
    WEEKLY = "weekly"
    MONTHLY = "monthly"
    QUARTERLY = "quarterly"
    YEARLY = "yearly"
    ON_DEMAND = "on_demand"


class ChartType(str, Enum):
    """Chart type enumeration"""
    BAR = "bar"
    LINE = "line"
    PIE = "pie"
    DOUGHNUT = "doughnut"
    AREA = "area"
    SCATTER = "scatter"
    TABLE = "table"


class ReportTemplateCreate(BaseModel):
    """Report template creation schema"""
    name: str = Field(..., max_length=200, description="Template name")
    description: Optional[str] = Field(None, description="Template description")
    report_type: ReportType = Field(..., description="Type of report")
    category: Optional[str] = Field(None, max_length=100, description="Report category")
    
    # Data source configuration
    data_sources: List[str] = Field(..., description="Data source tables/views")
    filters: Optional[Dict[str, Any]] = Field(None, description="Default filters")
    columns: List[Dict[str, Any]] = Field(..., description="Report columns configuration")
    
    # Visualization
    charts: Optional[List[Dict[str, Any]]] = Field(None, description="Chart configurations")
    layout: Optional[Dict[str, Any]] = Field(None, description="Report layout configuration")
    
    # Access control
    roles_allowed: List[str] = Field(..., description="Roles allowed to access this report")
    departments_allowed: Optional[List[UUID]] = Field(None, description="Departments allowed")
    
    # Scheduling
    is_scheduled: bool = Field(default=False, description="Is this a scheduled report")
    frequency: Optional[ReportFrequency] = Field(None, description="Report frequency")
    schedule_config: Optional[Dict[str, Any]] = Field(None, description="Schedule configuration")
    
    # Output settings
    default_format: ReportFormat = Field(default=ReportFormat.PDF, description="Default output format")
    auto_email: bool = Field(default=False, description="Auto-email report")
    email_recipients: Optional[List[str]] = Field(None, description="Email recipients")


class ReportTemplateUpdate(BaseModel):
    """Report template update schema"""
    name: Optional[str] = Field(None, max_length=200)
    description: Optional[str] = Field(None)
    category: Optional[str] = Field(None, max_length=100)
    data_sources: Optional[List[str]] = Field(None)
    filters: Optional[Dict[str, Any]] = Field(None)
    columns: Optional[List[Dict[str, Any]]] = Field(None)
    charts: Optional[List[Dict[str, Any]]] = Field(None)
    layout: Optional[Dict[str, Any]] = Field(None)
    roles_allowed: Optional[List[str]] = Field(None)
    departments_allowed: Optional[List[UUID]] = Field(None)
    is_scheduled: Optional[bool] = Field(None)
    frequency: Optional[ReportFrequency] = Field(None)
    schedule_config: Optional[Dict[str, Any]] = Field(None)
    default_format: Optional[ReportFormat] = Field(None)
    auto_email: Optional[bool] = Field(None)
    email_recipients: Optional[List[str]] = Field(None)


class ReportTemplateResponse(BaseModel):
    """Report template response schema"""
    id: UUID
    name: str
    description: Optional[str]
    report_type: ReportType
    category: Optional[str]
    data_sources: List[str]
    filters: Optional[Dict[str, Any]]
    columns: List[Dict[str, Any]]
    charts: Optional[List[Dict[str, Any]]]
    layout: Optional[Dict[str, Any]]
    roles_allowed: List[str]
    departments_allowed: Optional[List[UUID]]
    is_scheduled: bool
    frequency: Optional[ReportFrequency]
    schedule_config: Optional[Dict[str, Any]]
    default_format: ReportFormat
    auto_email: bool
    email_recipients: Optional[List[str]]
    is_active: bool
    usage_count: int
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class ReportGenerationRequest(BaseModel):
    """Report generation request schema"""
    template_id: UUID = Field(..., description="Report template ID")
    parameters: Optional[Dict[str, Any]] = Field(None, description="Report parameters")
    filters: Optional[Dict[str, Any]] = Field(None, description="Additional filters")
    date_range: Optional[Dict[str, date]] = Field(None, description="Date range filter")
    format: ReportFormat = Field(default=ReportFormat.PDF, description="Output format")
    email_recipients: Optional[List[str]] = Field(None, description="Email recipients")
    include_charts: bool = Field(default=True, description="Include charts in report")


class ReportResponse(BaseModel):
    """Report response schema"""
    id: UUID
    template_id: UUID
    name: str
    report_type: ReportType
    format: ReportFormat
    status: str  # generating, completed, failed
    file_url: Optional[str]
    file_size: Optional[int]
    generated_at: datetime
    generated_by: UUID
    parameters: Optional[Dict[str, Any]]
    filters: Optional[Dict[str, Any]]
    error_message: Optional[str]
    expires_at: Optional[datetime]

    class Config:
        from_attributes = True


class DashboardCreate(BaseModel):
    """Dashboard creation schema"""
    name: str = Field(..., max_length=200, description="Dashboard name")
    description: Optional[str] = Field(None, description="Dashboard description")
    category: Optional[str] = Field(None, max_length=100, description="Dashboard category")
    
    # Layout and widgets
    layout: Dict[str, Any] = Field(..., description="Dashboard layout configuration")
    widgets: List[Dict[str, Any]] = Field(..., description="Dashboard widgets")
    
    # Access control
    is_public: bool = Field(default=False, description="Is dashboard public")
    roles_allowed: List[str] = Field(..., description="Roles allowed to access")
    departments_allowed: Optional[List[UUID]] = Field(None, description="Departments allowed")
    
    # Refresh settings
    auto_refresh: bool = Field(default=False, description="Auto-refresh dashboard")
    refresh_interval: Optional[int] = Field(None, description="Refresh interval in minutes")


class DashboardResponse(BaseModel):
    """Dashboard response schema"""
    id: UUID
    name: str
    description: Optional[str]
    category: Optional[str]
    layout: Dict[str, Any]
    widgets: List[Dict[str, Any]]
    is_public: bool
    roles_allowed: List[str]
    departments_allowed: Optional[List[UUID]]
    auto_refresh: bool
    refresh_interval: Optional[int]
    is_active: bool
    view_count: int
    created_by: UUID
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class AnalyticsQuery(BaseModel):
    """Analytics query schema"""
    metric: str = Field(..., description="Metric to analyze")
    dimensions: List[str] = Field(..., description="Dimensions to group by")
    filters: Optional[Dict[str, Any]] = Field(None, description="Query filters")
    date_range: Optional[Dict[str, date]] = Field(None, description="Date range")
    aggregation: str = Field(default="count", description="Aggregation function")
    limit: Optional[int] = Field(None, ge=1, le=1000, description="Result limit")
    sort_by: Optional[str] = Field(None, description="Sort field")
    sort_order: str = Field(default="desc", description="Sort order")


class AnalyticsResult(BaseModel):
    """Analytics result schema"""
    metric: str
    dimensions: List[str]
    data: List[Dict[str, Any]]
    total_records: int
    aggregation: str
    filters_applied: Optional[Dict[str, Any]]
    generated_at: datetime
    execution_time_ms: float

    class Config:
        from_attributes = True


class KPIDefinition(BaseModel):
    """KPI definition schema"""
    name: str = Field(..., max_length=200, description="KPI name")
    description: Optional[str] = Field(None, description="KPI description")
    category: str = Field(..., max_length=100, description="KPI category")
    
    # Calculation
    calculation_method: str = Field(..., description="Calculation method")
    data_source: str = Field(..., description="Data source")
    formula: str = Field(..., description="Calculation formula")
    
    # Targets and thresholds
    target_value: Optional[float] = Field(None, description="Target value")
    warning_threshold: Optional[float] = Field(None, description="Warning threshold")
    critical_threshold: Optional[float] = Field(None, description="Critical threshold")
    
    # Display settings
    unit: Optional[str] = Field(None, description="Unit of measurement")
    format_type: str = Field(default="number", description="Display format")
    chart_type: ChartType = Field(default=ChartType.LINE, description="Chart type")
    
    # Update frequency
    update_frequency: str = Field(default="daily", description="Update frequency")
    is_active: bool = Field(default=True, description="Is KPI active")


class KPIValue(BaseModel):
    """KPI value schema"""
    kpi_id: UUID
    value: float
    target_value: Optional[float]
    period_start: date
    period_end: date
    status: str  # on_target, warning, critical
    trend: Optional[str]  # up, down, stable
    change_percentage: Optional[float]
    calculated_at: datetime

    class Config:
        from_attributes = True


class ReportSchedule(BaseModel):
    """Report schedule schema"""
    template_id: UUID = Field(..., description="Report template ID")
    name: str = Field(..., max_length=200, description="Schedule name")
    frequency: ReportFrequency = Field(..., description="Schedule frequency")
    
    # Schedule configuration
    schedule_config: Dict[str, Any] = Field(..., description="Schedule configuration")
    timezone: str = Field(default="UTC", description="Timezone")
    
    # Recipients
    email_recipients: List[str] = Field(..., description="Email recipients")
    
    # Parameters
    parameters: Optional[Dict[str, Any]] = Field(None, description="Report parameters")
    filters: Optional[Dict[str, Any]] = Field(None, description="Report filters")
    format: ReportFormat = Field(default=ReportFormat.PDF, description="Output format")
    
    # Status
    is_active: bool = Field(default=True, description="Is schedule active")
    next_run: Optional[datetime] = Field(None, description="Next scheduled run")


class ReportListResponse(BaseModel):
    """Report list response"""
    items: List[ReportResponse]
    total: int
    page: int
    size: int
    pages: int


class DashboardListResponse(BaseModel):
    """Dashboard list response"""
    items: List[DashboardResponse]
    total: int
    page: int
    size: int
    pages: int


class ReportTemplateListResponse(BaseModel):
    """Report template list response"""
    items: List[ReportTemplateResponse]
    total: int
    page: int
    size: int
    pages: int
