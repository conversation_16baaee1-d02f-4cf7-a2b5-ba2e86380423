from core.models.benefits import BenefitsModel
from core.databases.database import db
from core.models.employee_benefit_pivot import EmployeeBenefitsPivotModel
from core.models.salary_template_benefit_pivot import SalaryTemplateBenefitsPivotModel
from core.repositories.user import UserRepository
from sqlalchemy.exc import SQLAlchemyError

class BenefitsRepository:
    def __init__(self) -> None:
        pass

    @classmethod
    def createBenefits(self, benefit_name, cycle, duration, payslip_name, component_type, calculation_type, amount, value, preview_payslip):
        try:
            benefits = BenefitsModel(
                benefit_name=benefit_name,
                payslip_name=payslip_name,
                component_type=component_type,
                calculation_type=calculation_type,
                amount=amount,
                value=value,
                duration=duration,
                cycle=cycle,
                preview_payslip=preview_payslip,
                user_id=UserRepository.authUserId()
            )
            db.session.add(benefits)
            db.session.commit()
            return benefits
        except SQLAlchemyError as e:
            # db.session.rollback() 
            print(f"Database Error: {str(e)}") 
            raise e  
        except Exception as e:
            print(f"Unexpected Error: {str(e)}")  
            raise e

    @classmethod
    def fetchAll(self):
        return BenefitsModel.query.filter_by(user_id=UserRepository().authUserId()).order_by(BenefitsModel.timestamp.desc()).all()

    
    @classmethod
    def getBenefits(self, id):
        return BenefitsModel.query.filter(BenefitsModel.id == id).first()
    
    @classmethod
    def getBenefitsByKeys(self, kwargs):
        return BenefitsModel.query.filter_by(user_id=UserRepository().authUserId(), **kwargs).all()

    @classmethod
    def updateBenefits(self, id, **kwargs):
        benefits = BenefitsModel.query.filter_by(id=id).first()
        if benefits:
            for key, value in kwargs.items():
                setattr(benefits, key, value)
            db.session.commit()
            return benefits
        else:
            return None

    @classmethod
    def deleteBenefits(self, id):
        BenefitsModel.query.filter(BenefitsModel.id == id).delete()
        db.session.commit()
        return

    @classmethod
    def getAttachedEmployeeBenefit(cls, employee_id, benefits_id):
        """
        Check if a benefit is already assigned to an employee.
        """
        return EmployeeBenefitsPivotModel.query.filter_by(employee_id=employee_id, benefits_id=benefits_id).first()

    @classmethod
    def assign(cls, employee_id, benefits_id):
        """
        Assign a benefit to an employee.
        """
        try:
            benefit = EmployeeBenefitsPivotModel(
                employee_id=employee_id,
                benefits_id=benefits_id
            )
            db.session.add(benefit)
            db.session.commit()
            return benefit
        except SQLAlchemyError as e:
            print(f"Database Error: {str(e)}")
            db.session.rollback()
            raise e