from typing import Optional, List, Dict, Any, Tuple
from sqlalchemy.orm import Session, joinedload
from sqlalchemy import and_, or_, func, desc, asc, text
from fastapi import HTTPException, status
from uuid import UUID
from datetime import datetime, date, timedelta
import logging
import json
import hashlib
import pandas as pd
from io import BytesIO
import asyncio

from ...db.models.reporting import (
    ReportTemplate, Report, Dashboard, KPI, KPIValue, ReportSchedule,
    AnalyticsQuery, ReportType, ReportFormat, ReportStatus, ChartType
)
from ...db.models.employee import Employee
from ...schemas.reporting import (
    ReportTemplateCreate, ReportTemplateUpdate, ReportGenerationRequest,
    DashboardCreate, AnalyticsQuery as AnalyticsQuerySchema, KPIDefinition
)
from ...core.security import CurrentUser
from ...core.audit_logger import AuditLogger

logger = logging.getLogger(__name__)


class ReportingService:
    """Advanced reporting and analytics service"""

    # Report Template Methods
    async def create_report_template(
        self,
        db: Session,
        template_data: ReportTemplateCreate,
        current_user: CurrentUser
    ) -> ReportTemplate:
        """Create a new report template"""
        try:
            template = ReportTemplate(
                **template_data.dict(),
                organization_id=current_user.organization_id
            )

            db.add(template)
            db.commit()
            db.refresh(template)

            # Log audit event
            AuditLogger.log_action(
                db=db,
                action="CREATE",
                resource_type="report_templates",
                resource_id=str(template.id),
                user=current_user,
                new_values=template_data.dict()
            )

            return template

        except Exception as e:
            logger.error(f"Error creating report template: {e}")
            db.rollback()
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to create report template"
            )

    async def generate_report(
        self,
        db: Session,
        request: ReportGenerationRequest,
        current_user: CurrentUser
    ) -> Report:
        """Generate a report from template"""
        try:
            # Get template
            template = db.query(ReportTemplate).filter(
                ReportTemplate.id == request.template_id,
                ReportTemplate.is_active == True
            ).first()

            if not template:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Report template not found"
                )

            # Check permissions
            if not await self._check_report_permissions(template, current_user):
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="Insufficient permissions to generate this report"
                )

            # Create report record
            report = Report(
                template_id=request.template_id,
                name=template.name,
                report_type=template.report_type,
                format=request.format,
                generated_by=current_user.user_id,
                parameters=request.parameters,
                filters=request.filters,
                date_range=request.date_range,
                organization_id=current_user.organization_id,
                status=ReportStatus.GENERATING
            )

            db.add(report)
            db.commit()
            db.refresh(report)

            # Generate report asynchronously
            asyncio.create_task(self._generate_report_async(db, report, template, request))

            # Update template usage
            template.usage_count += 1
            template.last_generated = datetime.utcnow()
            db.commit()

            return report

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error generating report: {e}")
            db.rollback()
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to generate report"
            )

    async def _generate_report_async(
        self,
        db: Session,
        report: Report,
        template: ReportTemplate,
        request: ReportGenerationRequest
    ):
        """Generate report asynchronously"""
        try:
            start_time = datetime.utcnow()

            # Build and execute query
            data = await self._execute_report_query(db, template, request)

            # Generate report file
            file_info = await self._create_report_file(data, template, request)

            # Update report record
            report.status = ReportStatus.COMPLETED
            report.generated_at = datetime.utcnow()
            report.file_url = file_info["url"]
            report.file_name = file_info["name"]
            report.file_size = file_info["size"]
            report.file_hash = file_info["hash"]
            report.generation_time_ms = int((datetime.utcnow() - start_time).total_seconds() * 1000)
            report.record_count = len(data) if isinstance(data, list) else 0
            report.expires_at = datetime.utcnow() + timedelta(days=30)  # 30 days retention

            db.commit()

            # Send email if requested
            if request.email_recipients:
                await self._send_report_email(report, request.email_recipients)

        except Exception as e:
            logger.error(f"Error in async report generation: {e}")
            report.status = ReportStatus.FAILED
            report.error_message = str(e)
            report.retry_count += 1
            db.commit()

    async def _execute_report_query(
        self,
        db: Session,
        template: ReportTemplate,
        request: ReportGenerationRequest
    ) -> List[Dict[str, Any]]:
        """Execute report query and return data"""
        try:
            # Build SQL query based on template configuration
            query = await self._build_report_query(template, request)
            
            # Execute query
            result = db.execute(text(query))
            
            # Convert to list of dictionaries
            columns = result.keys()
            data = [dict(zip(columns, row)) for row in result.fetchall()]
            
            return data

        except Exception as e:
            logger.error(f"Error executing report query: {e}")
            raise

    async def _build_report_query(
        self,
        template: ReportTemplate,
        request: ReportGenerationRequest
    ) -> str:
        """Build SQL query from template configuration"""
        try:
            # Get base tables
            data_sources = template.data_sources
            columns = template.columns
            
            # Build SELECT clause
            select_columns = []
            for col in columns:
                if col.get("visible", True):
                    column_expr = col.get("expression", col["name"])
                    alias = col.get("alias", col["name"])
                    select_columns.append(f"{column_expr} AS {alias}")
            
            select_clause = "SELECT " + ", ".join(select_columns)
            
            # Build FROM clause
            from_clause = f"FROM {data_sources[0]}"
            
            # Add JOINs if multiple data sources
            for i, source in enumerate(data_sources[1:], 1):
                # This would need proper JOIN configuration in template
                from_clause += f" LEFT JOIN {source} ON {source}.id = {data_sources[0]}.{source}_id"
            
            # Build WHERE clause
            where_conditions = []
            
            # Add template filters
            if template.filters:
                for filter_key, filter_value in template.filters.items():
                    where_conditions.append(f"{filter_key} = '{filter_value}'")
            
            # Add request filters
            if request.filters:
                for filter_key, filter_value in request.filters.items():
                    where_conditions.append(f"{filter_key} = '{filter_value}'")
            
            # Add date range filter
            if request.date_range:
                start_date = request.date_range.get("start_date")
                end_date = request.date_range.get("end_date")
                date_column = request.date_range.get("column", "created_at")
                
                if start_date:
                    where_conditions.append(f"{date_column} >= '{start_date}'")
                if end_date:
                    where_conditions.append(f"{date_column} <= '{end_date}'")
            
            where_clause = ""
            if where_conditions:
                where_clause = "WHERE " + " AND ".join(where_conditions)
            
            # Build ORDER BY clause
            order_clause = ""
            if template.layout and template.layout.get("sort"):
                sort_config = template.layout["sort"]
                order_clause = f"ORDER BY {sort_config['column']} {sort_config.get('direction', 'ASC')}"
            
            # Combine all clauses
            query = f"{select_clause} {from_clause} {where_clause} {order_clause}"
            
            return query

        except Exception as e:
            logger.error(f"Error building report query: {e}")
            raise

    async def _create_report_file(
        self,
        data: List[Dict[str, Any]],
        template: ReportTemplate,
        request: ReportGenerationRequest
    ) -> Dict[str, Any]:
        """Create report file in requested format"""
        try:
            if request.format == ReportFormat.CSV:
                return await self._create_csv_report(data, template)
            elif request.format == ReportFormat.EXCEL:
                return await self._create_excel_report(data, template, request.include_charts)
            elif request.format == ReportFormat.PDF:
                return await self._create_pdf_report(data, template, request.include_charts)
            elif request.format == ReportFormat.JSON:
                return await self._create_json_report(data, template)
            else:
                raise ValueError(f"Unsupported report format: {request.format}")

        except Exception as e:
            logger.error(f"Error creating report file: {e}")
            raise

    async def _create_csv_report(self, data: List[Dict[str, Any]], template: ReportTemplate) -> Dict[str, Any]:
        """Create CSV report"""
        try:
            df = pd.DataFrame(data)
            
            # Generate filename
            timestamp = datetime.utcnow().strftime("%Y%m%d_%H%M%S")
            filename = f"{template.name}_{timestamp}.csv"
            
            # Create CSV content
            csv_buffer = BytesIO()
            df.to_csv(csv_buffer, index=False)
            csv_content = csv_buffer.getvalue()
            
            # Calculate hash
            file_hash = hashlib.sha256(csv_content).hexdigest()
            
            # Save file (this would integrate with your file storage system)
            file_url = await self._save_report_file(filename, csv_content)
            
            return {
                "url": file_url,
                "name": filename,
                "size": len(csv_content),
                "hash": file_hash
            }

        except Exception as e:
            logger.error(f"Error creating CSV report: {e}")
            raise

    async def _create_excel_report(
        self, data: List[Dict[str, Any]], template: ReportTemplate, include_charts: bool
    ) -> Dict[str, Any]:
        """Create Excel report with optional charts"""
        try:
            df = pd.DataFrame(data)
            
            # Generate filename
            timestamp = datetime.utcnow().strftime("%Y%m%d_%H%M%S")
            filename = f"{template.name}_{timestamp}.xlsx"
            
            # Create Excel content
            excel_buffer = BytesIO()
            with pd.ExcelWriter(excel_buffer, engine='openpyxl') as writer:
                df.to_excel(writer, sheet_name='Data', index=False)
                
                # Add charts if requested and configured
                if include_charts and template.charts:
                    await self._add_excel_charts(writer, df, template.charts)
            
            excel_content = excel_buffer.getvalue()
            
            # Calculate hash
            file_hash = hashlib.sha256(excel_content).hexdigest()
            
            # Save file
            file_url = await self._save_report_file(filename, excel_content)
            
            return {
                "url": file_url,
                "name": filename,
                "size": len(excel_content),
                "hash": file_hash
            }

        except Exception as e:
            logger.error(f"Error creating Excel report: {e}")
            raise

    async def _create_pdf_report(
        self, data: List[Dict[str, Any]], template: ReportTemplate, include_charts: bool
    ) -> Dict[str, Any]:
        """Create PDF report"""
        try:
            # This would use a PDF generation library like ReportLab
            # For now, return a placeholder
            timestamp = datetime.utcnow().strftime("%Y%m%d_%H%M%S")
            filename = f"{template.name}_{timestamp}.pdf"
            
            # Generate PDF content (placeholder)
            pdf_content = b"PDF content placeholder"
            
            # Calculate hash
            file_hash = hashlib.sha256(pdf_content).hexdigest()
            
            # Save file
            file_url = await self._save_report_file(filename, pdf_content)
            
            return {
                "url": file_url,
                "name": filename,
                "size": len(pdf_content),
                "hash": file_hash
            }

        except Exception as e:
            logger.error(f"Error creating PDF report: {e}")
            raise

    async def _create_json_report(self, data: List[Dict[str, Any]], template: ReportTemplate) -> Dict[str, Any]:
        """Create JSON report"""
        try:
            # Generate filename
            timestamp = datetime.utcnow().strftime("%Y%m%d_%H%M%S")
            filename = f"{template.name}_{timestamp}.json"
            
            # Create JSON content
            json_content = json.dumps(data, indent=2, default=str).encode('utf-8')
            
            # Calculate hash
            file_hash = hashlib.sha256(json_content).hexdigest()
            
            # Save file
            file_url = await self._save_report_file(filename, json_content)
            
            return {
                "url": file_url,
                "name": filename,
                "size": len(json_content),
                "hash": file_hash
            }

        except Exception as e:
            logger.error(f"Error creating JSON report: {e}")
            raise

    async def _save_report_file(self, filename: str, content: bytes) -> str:
        """Save report file to storage"""
        # This would integrate with your file storage system (S3, local storage, etc.)
        # For now, return a placeholder URL
        return f"/reports/{filename}"

    async def _check_report_permissions(self, template: ReportTemplate, current_user: CurrentUser) -> bool:
        """Check if user has permission to generate report"""
        try:
            # Check role permissions
            if current_user.role not in template.roles_allowed:
                return False
            
            # Check department permissions if specified
            if template.departments_allowed and current_user.department_id:
                if str(current_user.department_id) not in template.departments_allowed:
                    return False
            
            return True

        except Exception as e:
            logger.error(f"Error checking report permissions: {e}")
            return False

    async def _send_report_email(self, report: Report, recipients: List[str]):
        """Send report via email"""
        try:
            # This would integrate with your email service
            logger.info(f"Sending report {report.id} to {recipients}")
        except Exception as e:
            logger.error(f"Error sending report email: {e}")

    async def _add_excel_charts(self, writer, df: pd.DataFrame, chart_configs: List[Dict[str, Any]]):
        """Add charts to Excel report"""
        try:
            # This would use openpyxl to add charts
            pass
        except Exception as e:
            logger.error(f"Error adding Excel charts: {e}")

    # Analytics Methods
    async def execute_analytics_query(
        self,
        db: Session,
        query: AnalyticsQuerySchema,
        current_user: CurrentUser
    ) -> Dict[str, Any]:
        """Execute analytics query"""
        try:
            # Create query hash for caching
            query_dict = query.dict()
            query_hash = hashlib.sha256(json.dumps(query_dict, sort_keys=True).encode()).hexdigest()
            
            # Check cache
            cached_query = db.query(AnalyticsQuery).filter(
                AnalyticsQuery.query_hash == query_hash,
                AnalyticsQuery.cache_expires_at > datetime.utcnow()
            ).first()
            
            if cached_query:
                cached_query.hit_count += 1
                db.commit()
                return {
                    "metric": cached_query.metric,
                    "dimensions": cached_query.dimensions,
                    "data": cached_query.result_data,
                    "total_records": cached_query.total_records,
                    "aggregation": query.aggregation,
                    "filters_applied": query.filters,
                    "generated_at": cached_query.cached_at,
                    "execution_time_ms": cached_query.execution_time_ms,
                    "cached": True
                }
            
            # Execute new query
            start_time = datetime.utcnow()
            result_data = await self._execute_analytics_query(db, query)
            execution_time = int((datetime.utcnow() - start_time).total_seconds() * 1000)
            
            # Cache result
            analytics_query = AnalyticsQuery(
                query_hash=query_hash,
                metric=query.metric,
                dimensions=query.dimensions,
                filters=query.filters,
                aggregation=query.aggregation,
                result_data=result_data,
                total_records=len(result_data) if isinstance(result_data, list) else 0,
                execution_time_ms=execution_time,
                cached_at=datetime.utcnow(),
                cache_expires_at=datetime.utcnow() + timedelta(hours=1),  # 1 hour cache
                created_by=current_user.user_id,
                organization_id=current_user.organization_id
            )
            
            db.add(analytics_query)
            db.commit()
            
            return {
                "metric": query.metric,
                "dimensions": query.dimensions,
                "data": result_data,
                "total_records": len(result_data) if isinstance(result_data, list) else 0,
                "aggregation": query.aggregation,
                "filters_applied": query.filters,
                "generated_at": datetime.utcnow(),
                "execution_time_ms": execution_time,
                "cached": False
            }

        except Exception as e:
            logger.error(f"Error executing analytics query: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to execute analytics query"
            )

    async def _execute_analytics_query(self, db: Session, query: AnalyticsQuerySchema) -> List[Dict[str, Any]]:
        """Execute the actual analytics query"""
        try:
            # Build analytics SQL query
            sql_query = await self._build_analytics_query(query)
            
            # Execute query
            result = db.execute(text(sql_query))
            
            # Convert to list of dictionaries
            columns = result.keys()
            data = [dict(zip(columns, row)) for row in result.fetchall()]
            
            return data

        except Exception as e:
            logger.error(f"Error executing analytics SQL: {e}")
            raise

    async def _build_analytics_query(self, query: AnalyticsQuerySchema) -> str:
        """Build SQL query for analytics"""
        try:
            # This is a simplified example - in practice, you'd have more sophisticated query building
            metric_table = self._get_metric_table(query.metric)
            
            # Build SELECT clause
            select_parts = []
            for dimension in query.dimensions:
                select_parts.append(dimension)
            
            # Add aggregation
            if query.aggregation == "count":
                select_parts.append("COUNT(*) as value")
            elif query.aggregation == "sum":
                select_parts.append(f"SUM({query.metric}) as value")
            elif query.aggregation == "avg":
                select_parts.append(f"AVG({query.metric}) as value")
            
            select_clause = "SELECT " + ", ".join(select_parts)
            
            # Build FROM clause
            from_clause = f"FROM {metric_table}"
            
            # Build WHERE clause
            where_conditions = []
            if query.filters:
                for filter_key, filter_value in query.filters.items():
                    where_conditions.append(f"{filter_key} = '{filter_value}'")
            
            where_clause = ""
            if where_conditions:
                where_clause = "WHERE " + " AND ".join(where_conditions)
            
            # Build GROUP BY clause
            group_by_clause = ""
            if query.dimensions:
                group_by_clause = "GROUP BY " + ", ".join(query.dimensions)
            
            # Build ORDER BY clause
            order_clause = ""
            if query.sort_by:
                order_clause = f"ORDER BY {query.sort_by} {query.sort_order.upper()}"
            
            # Build LIMIT clause
            limit_clause = ""
            if query.limit:
                limit_clause = f"LIMIT {query.limit}"
            
            # Combine all clauses
            sql_query = f"{select_clause} {from_clause} {where_clause} {group_by_clause} {order_clause} {limit_clause}"
            
            return sql_query

        except Exception as e:
            logger.error(f"Error building analytics query: {e}")
            raise

    def _get_metric_table(self, metric: str) -> str:
        """Get the appropriate table for a metric"""
        metric_tables = {
            "employees": "employees",
            "attendance": "attendance_records",
            "leave": "leave_requests",
            "performance": "performance_reviews",
            "recruitment": "job_applications",
            "training": "course_enrollments"
        }
        
        return metric_tables.get(metric, "employees")
