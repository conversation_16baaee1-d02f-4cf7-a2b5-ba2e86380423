/**
 * Time Tracker Component with RBAC Integration
 * Provides real-time timer and check-in/out functionality
 */

import React, { useState, useEffect } from 'react';
import { Play, Pause, Clock, MapPin, Calendar } from 'lucide-react';
import { usePermissions } from '../hooks/usePermissions';
import { PermissionGate } from './ProtectedRoute';

export default function TimeTracker({ className = '' }) {
  const permissions = usePermissions();
  const [isCheckedIn, setIsCheckedIn] = useState(false);
  const [workTimer, setWorkTimer] = useState(0);
  const [checkInTime, setCheckInTime] = useState(null);
  const [checkOutTime, setCheckOutTime] = useState(null);
  const [notes, setNotes] = useState('');
  const [location, setLocation] = useState('Office');

  // Real-time timer effect
  useEffect(() => {
    let interval;
    if (isCheckedIn) {
      interval = setInterval(() => {
        setWorkTimer(prev => prev + 1);
      }, 1000);
    }
    return () => clearInterval(interval);
  }, [isCheckedIn]);

  // Format timer display
  const formatTimer = (seconds) => {
    const hrs = String(Math.floor(seconds / 3600)).padStart(2, '0');
    const mins = String(Math.floor((seconds % 3600) / 60)).padStart(2, '0');
    const secs = String(seconds % 60).padStart(2, '0');
    return `${hrs}:${mins}:${secs}`;
  };

  // Get current time string
  const getCurrentTime = () => {
    return new Date().toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit',
      hour12: true
    });
  };

  // Handle check-in
  const handleCheckIn = () => {
    const currentTime = getCurrentTime();
    setIsCheckedIn(true);
    setCheckInTime(currentTime);
    setWorkTimer(0);
    console.log('Checked in at:', currentTime);
  };

  // Handle check-out
  const handleCheckOut = () => {
    const currentTime = getCurrentTime();
    setIsCheckedIn(false);
    setCheckOutTime(currentTime);
    console.log('Checked out at:', currentTime, 'Total time:', formatTimer(workTimer));
  };

  return (
    <PermissionGate permission="timeTracker">
      <div className={`bg-white rounded-lg shadow-lg p-6 ${className}`}>
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center gap-3">
            <Clock className="agno-text-orange" size={24} />
            <h3 className="text-lg font-semibold text-gray-900">Time Tracker</h3>
          </div>
          
          <div className="flex items-center gap-2 text-sm text-gray-600">
            <Calendar size={16} />
            <span>{new Date().toLocaleDateString()}</span>
          </div>
        </div>

        {/* Current Status */}
        <div className="mb-6">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center gap-2">
              <div className={`w-3 h-3 rounded-full ${isCheckedIn ? 'bg-green-500' : 'bg-gray-400'}`}></div>
              <span className="font-medium text-gray-900">
                {isCheckedIn ? 'Currently Working' : 'Not Checked In'}
              </span>
            </div>
            
            <div className="text-2xl font-bold agno-text-orange">
              {formatTimer(workTimer)}
            </div>
          </div>

          {/* Work Schedule Info */}
          <div className="bg-gray-50 rounded-lg p-4 mb-4">
            <div className="flex items-center justify-between text-sm">
              <span className="text-gray-600">Today's Schedule:</span>
              <span className="font-medium text-gray-900">General [ 9:00 AM - 6:00 PM ]</span>
            </div>
          </div>
        </div>

        {/* Check-in/Check-out Times */}
        {(checkInTime || checkOutTime) && (
          <div className="grid grid-cols-2 gap-4 mb-6">
            <div className="text-center">
              <div className="text-sm text-gray-600 mb-1">Check In</div>
              <div className="font-semibold text-green-600">
                {checkInTime || '--:--'}
              </div>
            </div>
            <div className="text-center">
              <div className="text-sm text-gray-600 mb-1">Check Out</div>
              <div className="font-semibold text-red-600">
                {checkOutTime || '--:--'}
              </div>
            </div>
          </div>
        )}

        {/* Location Selection */}
        <div className="mb-6">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            <MapPin size={16} className="inline mr-1" />
            Work Location
          </label>
          <select
            value={location}
            onChange={(e) => setLocation(e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-accent-500"
          >
            <option value="Office">Office</option>
            <option value="Home">Work from Home</option>
            <option value="Client Site">Client Site</option>
            <option value="Other">Other</option>
          </select>
        </div>

        {/* Notes */}
        <div className="mb-6">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Notes (Optional)
          </label>
          <textarea
            value={notes}
            onChange={(e) => setNotes(e.target.value)}
            placeholder="Add notes about your work session..."
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-accent-500 resize-none"
            rows={3}
          />
        </div>

        {/* Action Button */}
        <button
          onClick={isCheckedIn ? handleCheckOut : handleCheckIn}
          className={`w-full py-3 px-4 rounded-lg font-medium flex items-center justify-center gap-2 transition-colors ${
            isCheckedIn
              ? 'bg-red-600 text-white hover:bg-red-700'
              : 'agno-bg-orange text-white hover:bg-accent-600'
          }`}
        >
          {isCheckedIn ? (
            <>
              <Pause size={20} />
              Check Out
            </>
          ) : (
            <>
              <Play size={20} />
              Check In
            </>
          )}
        </button>

        {/* Quick Stats */}
        <div className="mt-6 pt-6 border-t border-gray-200">
          <div className="grid grid-cols-3 gap-4 text-center">
            <div>
              <div className="text-sm text-gray-600">This Week</div>
              <div className="font-semibold text-gray-900">42:30</div>
            </div>
            <div>
              <div className="text-sm text-gray-600">This Month</div>
              <div className="font-semibold text-gray-900">168:45</div>
            </div>
            <div>
              <div className="text-sm text-gray-600">Overtime</div>
              <div className="font-semibold text-orange-600">8:15</div>
            </div>
          </div>
        </div>
      </div>
    </PermissionGate>
  );
}

// Compact version for dashboard/header use
export function CompactTimeTracker({ className = '' }) {
  const [isCheckedIn, setIsCheckedIn] = useState(false);
  const [workTimer, setWorkTimer] = useState(0);

  useEffect(() => {
    let interval;
    if (isCheckedIn) {
      interval = setInterval(() => {
        setWorkTimer(prev => prev + 1);
      }, 1000);
    }
    return () => clearInterval(interval);
  }, [isCheckedIn]);

  const formatTimer = (seconds) => {
    const hrs = String(Math.floor(seconds / 3600)).padStart(2, '0');
    const mins = String(Math.floor((seconds % 3600) / 60)).padStart(2, '0');
    const secs = String(seconds % 60).padStart(2, '0');
    return `${hrs}:${mins}:${secs}`;
  };

  return (
    <PermissionGate permission="timeTracker">
      <div className={`flex items-center gap-3 ${className}`}>
        <div className="flex items-center gap-2">
          <div className={`w-2 h-2 rounded-full ${isCheckedIn ? 'bg-green-500' : 'bg-gray-400'}`}></div>
          <span className="text-sm font-medium text-gray-700">
            {formatTimer(workTimer)}
          </span>
        </div>
        
        <button
          onClick={() => setIsCheckedIn(!isCheckedIn)}
          className={`px-3 py-1 rounded text-sm font-medium ${
            isCheckedIn
              ? 'bg-red-100 text-red-700 hover:bg-red-200'
              : 'bg-green-100 text-green-700 hover:bg-green-200'
          }`}
        >
          {isCheckedIn ? 'Check Out' : 'Check In'}
        </button>
      </div>
    </PermissionGate>
  );
}
