from core.databases.database import db
from core.models.basemodel import ModelBase
from datetime import datetime

class EmployeeBenefitsPivotModel(ModelBase):
    __tablename__ = "employee_benefits_pivot"

    id = db.Column(db.Integer, primary_key=True)
    employee_id = db.Column(db.<PERSON><PERSON><PERSON>, db.<PERSON>((ModelBase.dbSchema() + '.employees.id')), nullable=False) 
    benefits_id = db.Column(db.Integer, db.ForeignKey((ModelBase.dbSchema() + '.benefits.id')), nullable=False) 

    employee = db.relationship('EmployeeModel', back_populates='employee_benefits')
    benefit = db.relationship('BenefitsModel', back_populates='employee_benefits')
