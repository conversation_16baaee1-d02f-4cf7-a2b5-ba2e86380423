from flask import url_for, jsonify,request,redirect,session,redirect,url_for,jsonify,render_template,flash
from flask.views import MethodView
from flask_smorest import Blueprint, abort
from core.middleware import roles_required
from core.services.paystack_integration import PaystackIntegrationService
from core.models.paystack_credential import Paystack_IntegrationModel
from core.databases.database import db
from ..repositories.user import UserRepository
from core.services.paystack_service import PaystackService

blueprint = Blueprint("paystack", __name__, description="Operations for paystack")

@blueprint.route("/paystack")
class Paystack(MethodView):
    def __init__(self):
       pass
       

    @roles_required(['admin'])
    def post(self):
        try:
            # Get authenticated user
            user = UserRepository().authUser()
            user_id = user.id

            if not user_id:
                return jsonify({"error": "Authentication is required"}), 401

            # Check if integration already exists
            paystack_integration = Paystack_IntegrationModel.query.filter_by(user_id=user_id).first()

            if paystack_integration:
                # Integration exists; return as read-only
                return jsonify({
                    "paystack_key": paystack_integration.paystack_key,
                    "message": "Paystack integration data exists. Fields are read-only.",
                    "readonly": True,
                }), 200

            # Integration does not exist; proceed to create one
            data = request.get_json()
            if not data:
                return jsonify({"error": "Invalid or missing request data"}), 400

            paystack_key = data.get('paystack_key')
            if not paystack_key:
                return jsonify({"error": "Paystack key is required"}), 400

            # Encrypt the Paystack key
            encrypted_key = PaystackService().encrypt_service.encrypt(paystack_key)

            # Use the service to create a new integration
            new_integration = PaystackIntegrationService().createIntegration(
                user_id=user_id,
                paystack_key=encrypted_key,
            )

            return jsonify({
                "message": "Paystack integration data stored successfully",
                "integration_id": new_integration.id,
                "user_id": new_integration.user_id,
                "paystack_key": new_integration.paystack_key,
            }), 201

        except Exception as e:
            # Log the error
            # current_app.logger.error(f"Error saving Paystack integration: {str(e)}")
            return jsonify({"error": "An internal server error occurred"}), 500
 
 
@blueprint.route("/paystack/<id>")
class Paystack(MethodView):
     
    @roles_required(['admin'])
    def delete(self,id):

        existing_integration = Paystack_IntegrationModel.query.filter_by(user_id=id).first()

        if not existing_integration:
            return jsonify({"error": "No existing Paystack Credentials found for the user"}), 404

        try:
            # Delete the Zoho integration record
            db.session.delete(existing_integration)
            db.session.commit()

            # Return a success response
            return jsonify({"message": "Paystack Credentials data deleted successfully"}), 200

        except Exception as e:
            print(f"Error deleting Paystack: {str(e)}")
            return jsonify({"error": "Failed to delete integration data"}), 500       

@blueprint.route("/paystack/bank")
class PaystackBalance(MethodView):


    def __init__(self):
        pass
      
    
    @roles_required(['admin'])
    def get(self):
        """
        Fetch the list of banks from Paystack using the authenticated user's credentials.
        """
        try:
            # Get the authenticated user
            user = UserRepository().authUser()
            user_id = user.id

            # Fetch Paystack integration data for the user
            paystack_integration = Paystack_IntegrationModel.query.filter_by(user_id=user_id).first()

            if not paystack_integration:
                return jsonify({"error": "No Paystack integration found for the user"}), 404

            # Decrypt the Paystack secret key
            encrypted_key = paystack_integration.paystack_key
            secret_key = PaystackService().encrypt_service.decrypt(encrypted_key)

            # Fetch the bank list using the service
            response_data, status_code = PaystackService().get_banks(secret_key)

            return jsonify(response_data), status_code

        except Exception as e:
            # Log the error and return a generic error message
            print(f"Error fetching banks: {str(e)}")
            return jsonify({"error": "An internal server error occurred"}), 500
@blueprint.route("/paystack/balance")
class PaystackBalance(MethodView):

    def __init__(self):
      
       pass
      
    @roles_required(['admin'])
    def get(self):
      
        try:
            # Get the authenticated user
            user = UserRepository().authUser()
            user_id = user.id

            # Fetch Paystack integration data for the user
            paystack_integration = Paystack_IntegrationModel.query.filter_by(user_id=user_id).first()

            if not paystack_integration:
                return jsonify({"error": "No Paystack integration found for the user"}), 404

            # Decrypt the Paystack secret key
            encrypted_key = paystack_integration.paystack_key
            secret_key = PaystackService().encrypt_service.decrypt(encrypted_key)

            # Fetch the balance using the service
            response_data, status_code =PaystackService().get_balance(secret_key)

            return jsonify(response_data), status_code

        except Exception as e:
            # Log the error and return a generic error message
            print(f"Error fetching balance: {str(e)}")
            return jsonify({"error": "An internal server error occurred"}), 500
      
        
 