# payroll-BE
a new stand alone project for the payroll application

### Requirement
- Python3
- Postgres Database

### Installation
- Click on <PERSON> at the top right corner 
- Clone your forked repository 
- cd into the cloned folded | <code>cd payroll-BE</code>

### How to run app
Run the following commands
```
pip install -r requirements.txt

python -m virtualenv venv

source ./venv/bin/activate

```
- To start the application run 
```
python flask run
```
- To start the application run in a background mode
```
python flask run --debug
```

### To run the application using Docker, follow these steps:
update your .env with postgres credentials  
```
POSTGRES_USER=postgres
POSTGRES_PASSWORD=1
POSTGRES_DB=postgres
```

Build the container   
```
docker-compose up --build
```

Stop and remove the container   
```
docker-compose down
```

### Accessing Logs in the docker container
```
docker exec -it <your_flask_container_name> /bin/sh
cat /app/logs/access.log
cat /app/logs/error.log
```

### Run Container in Interactive Mode for Debugging
```
docker run -it payroll-be-app bash
```

For more docker commands Visit [docker commands](https://docs.docker.com/reference/cli/docker/compose/#subcommands).

### Database Migration
Alembic provides for the creation, management, and invocation of change management scripts for a relational database, using SQLAlchemy as the underlying engine.

Revision files are created in `alembic/versions`.

### Migration commands
- To create new database migration:
   - `alembic revision -m "Create user table"`
- To run migration
  - `alembic upgrade head`
- To reverse migration
  - `alembic downgrade base`
- To update esisting database migration: eg update password field on empoyee table
  - `alembic revision -m "Add password to employee table"`


### Alembic Doc
Visit [alembic documentation](https://alembic.sqlalchemy.org/en/latest/tutorial.html).


### For contribution
- Add your files
```
git add .
```
- Commit the files with an explanatory message
```
git commit -m"message of the commit"
```
- Pull from remote to update your local, this is very necessary step to avoid comflit
```
git pull
```
- checkout a feature existing branch
```
git checkout [branch-name]
```
- push to the branch
```
git checkout [branch-name]
```

# Tax Calculator Package

This package provides tax calculation logic for multiple countries [Nigeria and Ghana] using the strategy pattern with a centralized factory.

## API Endpoint to validate tax calculation
Route: POST /api/calculate-tax
### Body
```
{
  "country": "Nigeria",
  "tax_type": "PAYE",
  "annual_earnings": 2400000,
  "annual_pen": 153600,
  "annual_nhf": 240000
}
```
###
```
{
  "country": "Nigeria",
  "tax_type": "PAYE",
  "annual_tax": 161560.0,
  "monthly_tax": 13463.33
}
```

---
## Structure
```
core/
├── services/
│   └── tax_calculator_service.py
├── packages/
│   └── tax_calculator/
│       ├── factory.py
│       ├── nigeria_tax.py
│       └── ghana_tax.py
```

## TaxCalculatorService
```
  calculate_annual_tax(annual_earnings, pension, nhf)
```
```
  calculate_monthly_tax(annual_tax)
```
```
  Internally uses TaxCalculatorFactory to select the appropriate calculator based on country and tax type.
```

## NigeriaTaxCalculator
### Supports:
- PAYE — standard progressive income tax with CRA and tax bands.

- WHT — flat 7.5%

- MinTax — 1% minimum tax

- NA — tax exempt

## GhanaTaxCalculator
```
on going...
```