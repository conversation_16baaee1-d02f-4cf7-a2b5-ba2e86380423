from flask import url_for, jsonify
from flask.views import <PERSON><PERSON>iew
from flask_smorest import Blueprint, abort
from core.middleware import roles_required
from schemas import AnnouncementSchema, UpdateAnnouncementSchema
from sqlalchemy.orm.exc import NoResultFound
from sqlalchemy.exc import SQLAlchemyError, IntegrityError
from core.utils.responseBuilder import ResponseBuilder
import core.utils.response_message as RESPONSEMESSAGE
from core.services.announcements import AnnouncementService

blueprint = Blueprint("Announcement", __name__, description="Operations for Announcement")
    
@blueprint.route("/announcement/<id>")
class AnnouncementList(MethodView):
    @roles_required(['admin', 'employee'])
    @blueprint.response(200, AnnouncementSchema)
    def get(self, id):
        service = AnnouncementService()
        announcement = service.getAnnouncements(id)
        if not announcement:
            abort(401, message="Announcement does not exist")
        announcement_details = AnnouncementSchema().dump(announcement)
        return ResponseBuilder(data=announcement_details, status_code=200).build()    
    
    @roles_required(['admin'])
    def delete(self, id):
        service = AnnouncementService()
        announcement = service.getAnnouncements(id)
        if not announcement:
            abort(404, message="Announcement does not exist")
        service.deleteAnnouncements(id)
        return {"message" : "Announcement deleted"}
    
    @roles_required(['admin'])
    @blueprint.arguments(UpdateAnnouncementSchema)
    @blueprint.response(201, AnnouncementSchema)
    def put(self, data, id):
        service = AnnouncementService()
        announcement = service.getAnnouncements(id)
        if not announcement:
            abort(404, message="Announcement does not exist")
        try :
            new_announcement = service.updateAnnouncements(id, data)
            return new_announcement
        except SQLAlchemyError:
                abort(500, message="Error while updating announcement")
    
@blueprint.route("/announcement")
class Annoucement(MethodView):
    @roles_required(['admin','employee'])
    @blueprint.response(200, AnnouncementSchema)
    def get(self):
        announcement_service = AnnouncementService()
        announcement_list, total_announcements = announcement_service.fetchAll()
        announcement_schema = AnnouncementSchema(many=True)
        announcement_list = announcement_schema.dump(announcement_list)
        return ResponseBuilder(data=announcement_list, status_code=200, total=total_announcements).build()
    
    @roles_required(['admin'])
    @blueprint.arguments(AnnouncementSchema)
    @blueprint.response(200, AnnouncementSchema)
    def post(self, data):
        try:
            service = AnnouncementService()
            new_announcement = service.createAnnouncements(data)
            print(f'{service}')
        except IntegrityError as e:
            abort(500, message="Error while creating announcement")
        except SQLAlchemyError as e:
            abort(500, message="Error while creating announcement")
        return new_announcement