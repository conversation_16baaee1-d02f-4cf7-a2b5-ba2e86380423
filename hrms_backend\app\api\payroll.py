from fastapi import APIRouter, HTTPException, status, Depends, Query
from sqlalchemy.orm import Session
from typing import List, Optional
from uuid import UUID
from datetime import date

from ..core.security import get_current_user, require_permission, CurrentUser
from ..core.rbac import Permissions
from ..db.session import get_db
from ..schemas.payroll import (
    PayrollRecordCreate, PayrollRecordUpdate, PayrollRecordResponse,
    PayrollRecordListResponse, SalaryStructureCreate, SalaryStructureUpdate,
    SalaryStructureResponse, PayrollComponentCreate, PayrollComponentUpdate,
    PayrollComponentResponse, EmployeeSalaryCreate, EmployeeSalaryUpdate,
    EmployeeSalaryResponse, PayrollProcessingRequest, PayrollProcessingResponse,
    PayrollApprovalRequest, PayslipResponse, PayrollSummaryReport,
    BulkSalaryUpdate, PayrollStatus, ComponentType
)
from ..services.hr_management.payroll_service import PayrollService

router = APIRouter()
payroll_service = PayrollService()

# Root endpoint
@router.get("/")
async def get_payroll_overview(
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.PAYROLL_READ))
):
    """Get payroll overview"""
    try:
        return {
            "message": "Payroll management system",
            "endpoints": [
                "/records - Get payroll records",
                "/process - Process payroll",
                "/salary-structures - Get salary structures",
                "/components - Get payroll components"
            ]
        }
    except Exception as e:
        return {"error": str(e)}

# Payroll Processing endpoints
@router.post("/process", response_model=PayrollProcessingResponse)
async def process_payroll(
    processing_request: PayrollProcessingRequest,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.PAYROLL_PROCESS))
):
    """Process payroll for employees"""
    return await payroll_service.process_payroll(db, processing_request, current_user)


@router.get("/records", response_model=PayrollRecordListResponse)
async def get_payroll_records(
    employee_id: Optional[UUID] = Query(None),
    status: Optional[PayrollStatus] = Query(None),
    pay_period_start: Optional[date] = Query(None),
    pay_period_end: Optional[date] = Query(None),
    skip: int = Query(0, ge=0),
    limit: int = Query(20, ge=1, le=100),
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.PAYROLL_READ))
):
    """Get payroll records with filtering"""
    return await payroll_service.get_payroll_records(
        db=db,
        employee_id=employee_id,
        status=status,
        pay_period_start=pay_period_start,
        pay_period_end=pay_period_end,
        skip=skip,
        limit=limit,
        current_user=current_user
    )


@router.get("/records/{record_id}", response_model=PayrollRecordResponse)
async def get_payroll_record(
    record_id: UUID,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.PAYROLL_READ))
):
    """Get payroll record by ID"""
    record = await payroll_service.get_payroll_record_by_id(db, record_id, current_user)
    if not record:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Payroll record not found"
        )
    return record


@router.put("/records/{record_id}/approve", response_model=PayrollRecordResponse)
async def approve_payroll_record(
    record_id: UUID,
    approved: bool = True,
    comments: Optional[str] = None,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.PAYROLL_PROCESS))
):
    """Approve payroll record"""
    record = await payroll_service.approve_payroll_record(
        db, record_id, approved, comments, current_user
    )
    if not record:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Payroll record not found"
        )
    return record


@router.get("/records/{record_id}/payslip", response_model=PayslipResponse)
async def get_payslip(
    record_id: UUID,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(get_current_user)
):
    """Generate payslip for payroll record"""
    payslip = await payroll_service.generate_payslip(db, record_id, current_user)
    if not payslip:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Payroll record not found or access denied"
        )
    return payslip


# Salary Structure endpoints
@router.get("/salary-structures", response_model=List[SalaryStructureResponse])
async def get_salary_structures(
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.PAYROLL_READ))
):
    """Get salary structures"""
    return await payroll_service.get_salary_structures(db, current_user)


@router.post("/salary-structures", response_model=SalaryStructureResponse)
async def create_salary_structure(
    structure_data: SalaryStructureCreate,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.PAYROLL_CREATE))
):
    """Create salary structure"""
    return await payroll_service.create_salary_structure(db, structure_data, current_user)


# Payroll Components endpoints
@router.get("/components", response_model=List[PayrollComponentResponse])
async def get_payroll_components(
    component_type: Optional[ComponentType] = Query(None),
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.PAYROLL_READ))
):
    """Get payroll components"""
    return await payroll_service.get_payroll_components(db, component_type, current_user)


@router.post("/components", response_model=PayrollComponentResponse)
async def create_payroll_component(
    component_data: PayrollComponentCreate,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.PAYROLL_CREATE))
):
    """Create payroll component"""
    return await payroll_service.create_payroll_component(db, component_data, current_user)


# Employee Salary endpoints
@router.get("/employee-salaries/{employee_id}", response_model=List[EmployeeSalaryResponse])
async def get_employee_salaries(
    employee_id: UUID,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.PAYROLL_READ))
):
    """Get employee salary history"""
    return await payroll_service.get_employee_salaries(db, employee_id, current_user)


@router.post("/employee-salaries", response_model=EmployeeSalaryResponse)
async def create_employee_salary(
    salary_data: EmployeeSalaryCreate,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.PAYROLL_CREATE))
):
    """Create employee salary"""
    return await payroll_service.create_employee_salary(db, salary_data, current_user)


# My payroll endpoints (for employees)
@router.get("/my/records", response_model=PayrollRecordListResponse)
async def get_my_payroll_records(
    skip: int = Query(0, ge=0),
    limit: int = Query(20, ge=1, le=100),
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(get_current_user)
):
    """Get my payroll records"""
    return await payroll_service.get_payroll_records(
        db=db,
        employee_id=UUID(current_user.user_id),
        skip=skip,
        limit=limit,
        current_user=current_user
    )


@router.get("/my/salary", response_model=EmployeeSalaryResponse)
async def get_my_current_salary(
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(get_current_user)
):
    """Get my current salary"""
    salary = await payroll_service.get_current_employee_salary(
        db, UUID(current_user.user_id), current_user
    )
    if not salary:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Salary information not found"
        )
    return salary


# Enhanced Payroll Endpoints
@router.post("/calculate-enhanced/{employee_id}")
async def calculate_enhanced_payroll(
    employee_id: UUID,
    pay_period_start: date = Query(...),
    pay_period_end: date = Query(...),
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.PAYROLL_READ))
):
    """Calculate enhanced payroll with advanced tax calculations"""
    return await payroll_service.calculate_enhanced_payroll(
        db=db,
        employee_id=employee_id,
        pay_period_start=pay_period_start,
        pay_period_end=pay_period_end,
        current_user=current_user
    )


@router.get("/payslip/{payroll_record_id}/detailed")
async def get_detailed_payslip(
    payroll_record_id: UUID,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(get_current_user)
):
    """Get detailed payslip with all components"""
    return await payroll_service.generate_payslip(
        db=db,
        payroll_record_id=payroll_record_id,
        current_user=current_user
    )


@router.post("/tax/calculate")
async def calculate_tax(
    annual_earnings: float,
    annual_pension: float = 0,
    annual_nhf: float = 0,
    country: str = "Nigeria",
    tax_type: str = "PAYE",
    current_user: CurrentUser = Depends(get_current_user)
):
    """Calculate tax using advanced tax calculator"""
    from ..services.hr_management.payroll_service import TaxCalculatorService

    tax_calculator = TaxCalculatorService(country=country, tax_type=tax_type)
    annual_tax = tax_calculator.calculate_annual_tax(annual_earnings, annual_pension, annual_nhf)
    monthly_tax = tax_calculator.calculate_monthly_tax(annual_tax)

    return {
        "country": country,
        "tax_type": tax_type,
        "annual_earnings": annual_earnings,
        "annual_pension": annual_pension,
        "annual_nhf": annual_nhf,
        "annual_tax": annual_tax,
        "monthly_tax": monthly_tax
    }


@router.get("/salary-templates")
async def get_salary_templates(
    skip: int = Query(0, ge=0),
    limit: int = Query(20, ge=1, le=100),
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.PAYROLL_READ))
):
    """Get available salary templates"""
    return await payroll_service.get_salary_templates(
        db=db,
        skip=skip,
        limit=limit,
        current_user=current_user
    )


@router.post("/salary-templates")
async def create_salary_template(
    template_data: dict,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.PAYROLL_CREATE))
):
    """Create new salary template"""
    return await payroll_service.create_salary_template(
        db=db,
        template_data=template_data,
        current_user=current_user
    )


@router.get("/salary-templates/{template_id}")
async def get_salary_template(
    template_id: UUID,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.PAYROLL_READ))
):
    """Get salary template by ID"""
    return await payroll_service.get_salary_template_by_id(
        db=db,
        template_id=template_id,
        current_user=current_user
    )


@router.put("/salary-templates/{template_id}")
async def update_salary_template(
    template_id: UUID,
    template_data: dict,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.PAYROLL_UPDATE))
):
    """Update salary template"""
    return await payroll_service.update_salary_template(
        db=db,
        template_id=template_id,
        template_data=template_data,
        current_user=current_user
    )


@router.delete("/salary-templates/{template_id}")
async def delete_salary_template(
    template_id: UUID,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.PAYROLL_DELETE))
):
    """Delete salary template"""
    return await payroll_service.delete_salary_template(
        db=db,
        template_id=template_id,
        current_user=current_user
    )


@router.post("/bulk-salary-update")
async def bulk_salary_update(
    updates: BulkSalaryUpdate,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.PAYROLL_UPDATE))
):
    """Bulk update employee salaries"""
    return await payroll_service.bulk_update_salaries(db, updates, current_user)


@router.get("/reports/summary")
async def get_payroll_summary_report(
    start_date: date = Query(...),
    end_date: date = Query(...),
    department_id: Optional[UUID] = Query(None),
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.PAYROLL_READ))
):
    """Get payroll summary report"""
    return await payroll_service.generate_payroll_summary_report(
        db=db,
        start_date=start_date,
        end_date=end_date,
        department_id=department_id,
        current_user=current_user
    )


# Payroll History Endpoints
@router.get("/history/{employee_id}")
async def get_employee_payroll_history(
    employee_id: UUID,
    skip: int = Query(0, ge=0),
    limit: int = Query(20, ge=1, le=100),
    start_date: Optional[date] = Query(None),
    end_date: Optional[date] = Query(None),
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.PAYROLL_READ))
):
    """Get employee payroll history"""
    return await payroll_service.get_employee_payroll_history(
        db=db,
        employee_id=employee_id,
        skip=skip,
        limit=limit,
        start_date=start_date,
        end_date=end_date,
        current_user=current_user
    )


@router.get("/my/history")
async def get_my_payroll_history(
    skip: int = Query(0, ge=0),
    limit: int = Query(20, ge=1, le=100),
    start_date: Optional[date] = Query(None),
    end_date: Optional[date] = Query(None),
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(get_current_user)
):
    """Get my payroll history"""
    return await payroll_service.get_employee_payroll_history(
        db=db,
        employee_id=UUID(current_user.user_id),
        skip=skip,
        limit=limit,
        start_date=start_date,
        end_date=end_date,
        current_user=current_user
    )


# Payment Integration Endpoints
@router.post("/payment/initiate/{payroll_record_id}")
async def initiate_payment(
    payroll_record_id: UUID,
    payment_method: str = "bank_transfer",
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.PAYROLL_PROCESS))
):
    """Initiate payment for payroll record"""
    return await payroll_service.initiate_payment(
        db=db,
        payroll_record_id=payroll_record_id,
        payment_method=payment_method,
        current_user=current_user
    )


@router.get("/payment/status/{transaction_id}")
async def get_payment_status(
    transaction_id: str,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.PAYROLL_READ))
):
    """Get payment status"""
    return await payroll_service.get_payment_status(
        db=db,
        transaction_id=transaction_id,
        current_user=current_user
    )


@router.post("/payment/webhook")
async def payment_webhook(
    webhook_data: dict,
    db: Session = Depends(get_db)
):
    """Handle payment gateway webhooks"""
    return await payroll_service.handle_payment_webhook(
        db=db,
        webhook_data=webhook_data
    )


# Component Management Endpoints
@router.get("/components")
async def get_payroll_components(
    component_type: Optional[ComponentType] = Query(None),
    skip: int = Query(0, ge=0),
    limit: int = Query(20, ge=1, le=100),
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.PAYROLL_READ))
):
    """Get payroll component definitions"""
    return await payroll_service.get_payroll_components(
        db=db,
        component_type=component_type,
        skip=skip,
        limit=limit,
        current_user=current_user
    )


@router.post("/components")
async def create_payroll_component(
    component_data: PayrollComponentCreate,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.PAYROLL_CREATE))
):
    """Create new payroll component definition"""
    return await payroll_service.create_payroll_component(
        db=db,
        component_data=component_data,
        current_user=current_user
    )


# Advanced Reporting Endpoints
@router.get("/reports/tax")
async def get_tax_report(
    start_date: date = Query(...),
    end_date: date = Query(...),
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.PAYROLL_READ))
):
    """Generate tax compliance report"""
    return await payroll_service.generate_tax_report(
        db=db,
        start_date=start_date,
        end_date=end_date,
        current_user=current_user
    )


@router.get("/reports/department-analysis")
async def get_department_analysis(
    start_date: date = Query(...),
    end_date: date = Query(...),
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.PAYROLL_READ))
):
    """Generate department-wise payroll analysis"""
    return await payroll_service.generate_department_analysis(
        db=db,
        start_date=start_date,
        end_date=end_date,
        current_user=current_user
    )


@router.get("/analytics/dashboard")
async def get_payroll_analytics_dashboard(
    period: str = Query("current_month", regex="^(current_month|last_month|current_quarter|current_year)$"),
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.PAYROLL_READ))
):
    """Get payroll analytics dashboard data"""

    # Calculate date range based on period
    today = date.today()

    if period == "current_month":
        start_date = date(today.year, today.month, 1)
        end_date = date(today.year, today.month + 1, 1) - timedelta(days=1) if today.month < 12 else date(today.year, 12, 31)
    elif period == "last_month":
        if today.month == 1:
            start_date = date(today.year - 1, 12, 1)
            end_date = date(today.year - 1, 12, 31)
        else:
            start_date = date(today.year, today.month - 1, 1)
            end_date = date(today.year, today.month, 1) - timedelta(days=1)
    elif period == "current_quarter":
        quarter = (today.month - 1) // 3 + 1
        start_date = date(today.year, (quarter - 1) * 3 + 1, 1)
        end_date = date(today.year, quarter * 3 + 1, 1) - timedelta(days=1) if quarter < 4 else date(today.year, 12, 31)
    else:  # current_year
        start_date = date(today.year, 1, 1)
        end_date = date(today.year, 12, 31)

    # Get comprehensive analytics
    summary_report = await payroll_service.generate_payroll_summary_report(
        db=db,
        start_date=start_date,
        end_date=end_date,
        department_id=None,
        current_user=current_user
    )

    tax_report = await payroll_service.generate_tax_report(
        db=db,
        start_date=start_date,
        end_date=end_date,
        current_user=current_user
    )

    department_analysis = await payroll_service.generate_department_analysis(
        db=db,
        start_date=start_date,
        end_date=end_date,
        current_user=current_user
    )

    return {
        "period": period,
        "date_range": {
            "start_date": start_date.isoformat(),
            "end_date": end_date.isoformat()
        },
        "summary": summary_report["summary"],
        "department_breakdown": summary_report["department_breakdown"],
        "tax_summary": tax_report["tax_summary"],
        "department_analysis": department_analysis["cost_distribution"],
        "trends": {
            "payroll_growth": "5.2%",  # Mock data - would calculate from historical data
            "average_salary_change": "3.1%",
            "tax_efficiency": "92.5%"
        },
        "alerts": [
            {
                "type": "warning",
                "message": f"{summary_report['summary']['total_employees']} employees processed this period"
            },
            {
                "type": "info",
                "message": f"Tax rate: {tax_report['tax_summary']['average_tax_rate']:.1f}%"
            }
        ]
    }


@router.get("/export/payroll-data")
async def export_payroll_data(
    start_date: date = Query(...),
    end_date: date = Query(...),
    format: str = Query("csv", regex="^(csv|excel|pdf)$"),
    include_sensitive: bool = Query(False),
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.PAYROLL_READ))
):
    """Export payroll data in various formats"""

    # Get payroll records
    payroll_records = await payroll_service.get_payroll_records(
        db=db,
        pay_period_start=start_date,
        pay_period_end=end_date,
        skip=0,
        limit=10000,  # Large limit for export
        current_user=current_user
    )

    # Prepare export data
    export_data = []
    for record in payroll_records.get("records", []):
        row = {
            "Employee ID": record.get("employee", {}).get("employee_id"),
            "Employee Name": record.get("employee", {}).get("full_name"),
            "Department": record.get("employee", {}).get("department"),
            "Pay Period Start": record.get("pay_period_start"),
            "Pay Period End": record.get("pay_period_end"),
            "Gross Salary": record.get("gross_salary"),
            "Total Deductions": record.get("total_deductions"),
            "Net Salary": record.get("net_salary"),
            "Status": record.get("status")
        }

        if include_sensitive:
            row.update({
                "Tax Amount": record.get("total_taxes"),
                "Benefits": record.get("total_benefits"),
                "Payment Reference": record.get("payment_reference")
            })

        export_data.append(row)

    return {
        "format": format,
        "record_count": len(export_data),
        "export_url": f"/api/payroll/download/{format}/payroll_export_{start_date}_{end_date}.{format}",
        "generated_at": datetime.utcnow().isoformat(),
        "data_preview": export_data[:5] if export_data else [],
        "message": f"Export prepared with {len(export_data)} records"
    }


# ============ ADVANCED PAYROLL FOLDER INTEGRATION ENDPOINTS ============

# Advanced Salary Templates
@router.get("/advanced/salary-templates")
async def get_advanced_salary_templates(
    skip: int = Query(0, ge=0),
    limit: int = Query(20, ge=1, le=100),
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.PAYROLL_READ))
):
    """Get advanced salary templates from Payroll folder"""
    return await payroll_service.get_advanced_salary_templates(
        db=db,
        skip=skip,
        limit=limit,
        current_user=current_user
    )


@router.post("/advanced/salary-templates")
async def create_advanced_salary_template(
    template_data: dict,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.PAYROLL_CREATE))
):
    """Create advanced salary template using Payroll folder service"""
    return await payroll_service.create_advanced_salary_template(
        db=db,
        template_data=template_data,
        current_user=current_user
    )


# Advanced Benefits Management
@router.get("/advanced/benefits")
async def get_advanced_benefits(
    skip: int = Query(0, ge=0),
    limit: int = Query(20, ge=1, le=100),
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.PAYROLL_READ))
):
    """Get benefits using advanced Payroll folder service"""
    return await payroll_service.get_advanced_benefits(
        db=db,
        skip=skip,
        limit=limit,
        current_user=current_user
    )


# Loan Management
@router.get("/loans")
async def get_loan_requests(
    skip: int = Query(0, ge=0),
    limit: int = Query(20, ge=1, le=100),
    status: Optional[str] = Query(None),
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.PAYROLL_READ))
):
    """Get loan requests"""
    return await payroll_service.get_loan_requests(
        db=db,
        skip=skip,
        limit=limit,
        status=status,
        current_user=current_user
    )


@router.post("/loans")
async def create_loan_request(
    loan_data: dict,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(get_current_user)
):
    """Create loan request"""
    return await payroll_service.create_loan_request(
        db=db,
        loan_data=loan_data,
        current_user=current_user
    )


@router.put("/loans/{loan_id}/approve")
async def approve_loan_request(
    loan_id: str,
    approval_data: dict,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.PAYROLL_PROCESS))
):
    """Approve or reject loan request"""
    return await payroll_service.approve_loan_request(
        db=db,
        loan_id=loan_id,
        approval_data=approval_data,
        current_user=current_user
    )


# Advanced Payroll Processing
@router.post("/advanced/process")
async def process_advanced_payroll(
    processing_data: dict,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.PAYROLL_PROCESS))
):
    """Process payroll using advanced Payroll folder service"""
    payroll_ids = processing_data.get("payroll_ids", [])
    payment_gateway_id = processing_data.get("payment_gateway_id")

    return await payroll_service.process_advanced_payroll(
        db=db,
        payroll_ids=payroll_ids,
        payment_gateway_id=payment_gateway_id,
        current_user=current_user
    )


# Proration Calculations
@router.post("/proration/calculate")
async def calculate_proration(
    proration_data: dict,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.PAYROLL_READ))
):
    """Calculate prorated salary"""
    employee_id = UUID(proration_data.get("employee_id"))
    start_date = date.fromisoformat(proration_data.get("start_date"))
    end_date = date.fromisoformat(proration_data.get("end_date"))

    return await payroll_service.calculate_proration(
        db=db,
        employee_id=employee_id,
        start_date=start_date,
        end_date=end_date,
        current_user=current_user
    )


# Advanced Payslip Generation
@router.get("/payslip/{payroll_record_id}/advanced")
async def get_advanced_payslip(
    payroll_record_id: UUID,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(get_current_user)
):
    """Generate advanced payslip using Payroll folder service"""
    return await payroll_service.generate_advanced_payslip(
        db=db,
        payroll_record_id=payroll_record_id,
        current_user=current_user
    )


@router.post("/payslip/{payroll_record_id}/email")
async def send_payslip_email(
    payroll_record_id: UUID,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.PAYROLL_READ))
):
    """Send payslip via email"""
    return await payroll_service.send_payslip_email(
        db=db,
        payroll_record_id=payroll_record_id,
        current_user=current_user
    )


# NHF Management
@router.get("/nhf")
async def get_nhf_calculations(
    employee_id: Optional[UUID] = Query(None),
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.PAYROLL_READ))
):
    """Get NHF calculations"""
    return await payroll_service.get_nhf_calculations(
        db=db,
        employee_id=employee_id,
        current_user=current_user
    )


# Pension Management
@router.get("/pensions")
async def get_pension_calculations(
    employee_id: Optional[UUID] = Query(None),
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.PAYROLL_READ))
):
    """Get pension calculations"""
    return await payroll_service.get_pension_calculations(
        db=db,
        employee_id=employee_id,
        current_user=current_user
    )


# Working Days Management
@router.get("/working-days")
async def get_working_days(
    start_date: date = Query(...),
    end_date: date = Query(...),
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.PAYROLL_READ))
):
    """Get working days for a period"""
    try:
        from app.services.hr_management.payroll_service import PAYROLL_SERVICES_AVAILABLE, WorkingDaysService

        if not PAYROLL_SERVICES_AVAILABLE:
            # Calculate basic working days (excluding weekends)
            total_days = (end_date - start_date).days + 1
            working_days = 0
            current_date = start_date

            while current_date <= end_date:
                if current_date.weekday() < 5:  # Monday = 0, Sunday = 6
                    working_days += 1
                current_date += timedelta(days=1)

            return {
                "start_date": start_date.isoformat(),
                "end_date": end_date.isoformat(),
                "total_days": total_days,
                "working_days": working_days,
                "weekend_days": total_days - working_days,
                "holidays": []
            }

        working_days_service = WorkingDaysService()
        working_days_data = working_days_service.get_working_days_for_period(start_date, end_date)

        return {
            "start_date": start_date.isoformat(),
            "end_date": end_date.isoformat(),
            "working_days": working_days_data.get("working_days"),
            "total_days": working_days_data.get("total_days"),
            "holidays": working_days_data.get("holidays", []),
            "weekend_days": working_days_data.get("weekend_days")
        }

    except Exception as e:
        return {"error": f"Failed to get working days: {str(e)}"}


# Transaction History
@router.get("/transactions")
async def get_transaction_history(
    skip: int = Query(0, ge=0),
    limit: int = Query(20, ge=1, le=100),
    employee_id: Optional[UUID] = Query(None),
    status: Optional[str] = Query(None),
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.PAYROLL_READ))
):
    """Get transaction history"""
    try:
        from app.services.hr_management.payroll_service import PAYROLL_SERVICES_AVAILABLE, PayrollTransactionHistoryService

        if not PAYROLL_SERVICES_AVAILABLE:
            return {"transactions": [], "total": 0, "skip": skip, "limit": limit}

        transaction_service = PayrollTransactionHistoryService()

        filters = {}
        if employee_id:
            filters["employee_id"] = str(employee_id)
        if status:
            filters["status"] = status

        transactions = transaction_service.get_transactions(filters, skip, limit)

        return {
            "transactions": [
                {
                    "id": str(txn.id),
                    "employee_id": str(txn.employee_id),
                    "payroll_id": str(txn.payroll_id) if txn.payroll_id else None,
                    "transaction_id": txn.transaction_id,
                    "amount": float(txn.amount),
                    "status": txn.status,
                    "gateway": txn.gateway,
                    "reference": txn.reference,
                    "created_at": txn.created_at.isoformat() if txn.created_at else None
                }
                for txn in transactions
            ],
            "total": len(transactions),
            "skip": skip,
            "limit": limit
        }

    except Exception as e:
        return {"error": f"Failed to get transaction history: {str(e)}"}


# Approval Workflows
@router.get("/approvals/settings")
async def get_approval_settings(
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.PAYROLL_READ))
):
    """Get approval settings"""
    try:
        from app.services.hr_management.payroll_service import PAYROLL_SERVICES_AVAILABLE, ApprovalSettingsService

        if not PAYROLL_SERVICES_AVAILABLE:
            return {"approval_settings": [], "message": "Approval service not available"}

        approval_service = ApprovalSettingsService()
        settings = approval_service.get_approval_settings()

        return {
            "approval_settings": [
                {
                    "id": str(setting.id),
                    "approval_type": setting.approval_type,
                    "required_approvers": setting.required_approvers,
                    "approval_order": setting.approval_order,
                    "is_active": setting.is_active
                }
                for setting in settings
            ]
        }

    except Exception as e:
        return {"error": f"Failed to get approval settings: {str(e)}"}


@router.post("/approvals/validate")
async def validate_payroll_approvals(
    validation_data: dict,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.PAYROLL_PROCESS))
):
    """Validate payroll approval requirements"""
    try:
        from app.services.hr_management.payroll_service import PAYROLL_SERVICES_AVAILABLE, ApprovalSettingsService

        if not PAYROLL_SERVICES_AVAILABLE:
            return {"valid": True, "message": "Approval validation not available"}

        payroll_ids = validation_data.get("payroll_ids", [])
        approval_service = ApprovalSettingsService()

        validation_result = approval_service.validate_payroll_approval_requirements(payroll_ids)

        return {
            "valid": validation_result.get("valid", True),
            "message": validation_result.get("message", "Validation successful"),
            "required_approvals": validation_result.get("required_approvals", []),
            "missing_approvals": validation_result.get("missing_approvals", [])
        }

    except Exception as e:
        return {"error": f"Failed to validate approvals: {str(e)}"}


# Advanced Tax Calculations
@router.post("/tax/advanced-calculate")
async def calculate_advanced_tax(
    tax_data: dict,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(get_current_user)
):
    """Calculate tax using advanced Payroll folder tax calculator"""
    try:
        from app.services.hr_management.payroll_service import PAYROLL_SERVICES_AVAILABLE, PayrollTaxCalculatorService

        if not PAYROLL_SERVICES_AVAILABLE:
            # Fallback to basic tax calculation
            return await calculate_tax(
                annual_earnings=tax_data.get("annual_earnings", 0),
                annual_pension=tax_data.get("annual_pension", 0),
                annual_nhf=tax_data.get("annual_nhf", 0),
                country=tax_data.get("country", "Nigeria"),
                tax_type=tax_data.get("tax_type", "PAYE"),
                current_user=current_user
            )

        country = tax_data.get("country", "Nigeria")
        tax_calculator = PayrollTaxCalculatorService(country)

        tax_result = tax_calculator.calculate_comprehensive_tax(
            annual_earnings=tax_data.get("annual_earnings"),
            annual_pension=tax_data.get("annual_pension", 0),
            annual_nhf=tax_data.get("annual_nhf", 0),
            other_deductions=tax_data.get("other_deductions", 0),
            tax_type=tax_data.get("tax_type", "PAYE")
        )

        return {
            "country": country,
            "tax_type": tax_data.get("tax_type", "PAYE"),
            "annual_earnings": tax_data.get("annual_earnings"),
            "annual_pension": tax_data.get("annual_pension", 0),
            "annual_nhf": tax_data.get("annual_nhf", 0),
            "taxable_income": tax_result.get("taxable_income"),
            "annual_tax": tax_result.get("annual_tax"),
            "monthly_tax": tax_result.get("monthly_tax"),
            "tax_bands": tax_result.get("tax_bands", []),
            "effective_tax_rate": tax_result.get("effective_tax_rate"),
            "marginal_tax_rate": tax_result.get("marginal_tax_rate")
        }

    except Exception as e:
        return {"error": f"Failed to calculate advanced tax: {str(e)}"}


# Component Processing
@router.post("/components/process")
async def process_salary_components(
    component_data: dict,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.PAYROLL_READ))
):
    """Process salary components using advanced Payroll folder service"""
    try:
        from app.services.hr_management.payroll_service import PAYROLL_SERVICES_AVAILABLE, ComponentProcessor

        if not PAYROLL_SERVICES_AVAILABLE:
            return {"error": "Component processing service not available"}

        processor = ComponentProcessor(
            employee_id=component_data.get("employee_id"),
            gross_pay=component_data.get("gross_pay"),
            overtime_amount=component_data.get("overtime_amount", 0),
            is_prorated=component_data.get("is_prorated", False),
            salary_benefit=component_data.get("benefits", []),
            salary_component=component_data.get("components", [])
        )

        processed_result = processor.generate_salary_response()

        return {
            "employee_id": component_data.get("employee_id"),
            "gross_pay": component_data.get("gross_pay"),
            "processed_components": processed_result.get("components", []),
            "total_earnings": processed_result.get("total_earnings"),
            "total_deductions": processed_result.get("total_deductions"),
            "total_benefits": processed_result.get("total_benefits"),
            "net_pay": processed_result.get("net_pay"),
            "tax_calculations": processed_result.get("tax_details")
        }

    except Exception as e:
        return {"error": f"Failed to process components: {str(e)}"}


# ============ ENHANCED PAYSTACK INTEGRATION ============

@router.get("/paystack/banks")
async def get_paystack_banks(
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.PAYROLL_READ))
):
    """Get list of banks from Paystack"""
    try:
        from app.services.hr_management.payroll_service import PAYROLL_SERVICES_AVAILABLE, PayrollPaystackService

        if not PAYROLL_SERVICES_AVAILABLE:
            return {"error": "Paystack service not available"}

        paystack_service = PayrollPaystackService()
        secret_key = paystack_service.decrypt_key()

        if secret_key == "No Paystack key":
            return {"error": "Paystack key not configured"}

        banks_data, status_code = paystack_service.get_banks(secret_key)

        return {
            "banks": banks_data.get("data", []),
            "status": banks_data.get("status", False),
            "message": banks_data.get("message", "Banks retrieved successfully")
        }

    except Exception as e:
        return {"error": f"Failed to get banks: {str(e)}"}


@router.get("/paystack/balance")
async def get_paystack_balance(
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.PAYROLL_READ))
):
    """Get Paystack account balance"""
    try:
        from app.services.hr_management.payroll_service import PAYROLL_SERVICES_AVAILABLE, PayrollPaystackService

        if not PAYROLL_SERVICES_AVAILABLE:
            return {"error": "Paystack service not available"}

        paystack_service = PayrollPaystackService()
        secret_key = paystack_service.decrypt_key()

        if secret_key == "No Paystack key":
            return {"error": "Paystack key not configured"}

        balance_data, status_code = paystack_service.get_balance(secret_key)

        return {
            "balance": balance_data.get("data", {}),
            "status": balance_data.get("status", False),
            "message": balance_data.get("message", "Balance retrieved successfully")
        }

    except Exception as e:
        return {"error": f"Failed to get balance: {str(e)}"}


@router.post("/paystack/verify-account")
async def verify_bank_account(
    account_data: dict,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.PAYROLL_READ))
):
    """Verify bank account details using NUBAN"""
    try:
        from app.services.hr_management.payroll_service import PAYROLL_SERVICES_AVAILABLE, PayrollPaystackService

        if not PAYROLL_SERVICES_AVAILABLE:
            return {"error": "Paystack service not available"}

        account_number = account_data.get("account_number")
        bank_code = account_data.get("bank_code")

        if not account_number or not bank_code:
            return {"error": "Account number and bank code are required"}

        paystack_service = PayrollPaystackService(
            account_number=account_number,
            sort_code=bank_code
        )

        account_name = paystack_service.nuban_check()

        return {
            "account_name": account_name,
            "account_number": account_number,
            "bank_code": bank_code,
            "verified": bool(account_name),
            "message": "Account verified successfully" if account_name else "Account verification failed"
        }

    except Exception as e:
        return {"error": f"Failed to verify account: {str(e)}"}


@router.post("/paystack/create-recipient")
async def create_transfer_recipient(
    recipient_data: dict,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.PAYROLL_PROCESS))
):
    """Create transfer recipient for Paystack"""
    try:
        from app.services.hr_management.payroll_service import PAYROLL_SERVICES_AVAILABLE, PayrollPaystackService

        if not PAYROLL_SERVICES_AVAILABLE:
            return {"error": "Paystack service not available"}

        paystack_service = PayrollPaystackService(
            account_number=recipient_data.get("account_number"),
            sort_code=recipient_data.get("bank_code")
        )

        secret_key = paystack_service.decrypt_key()
        if secret_key == "No Paystack key":
            return {"error": "Paystack key not configured"}

        transfer_recipient_data = {
            "name": recipient_data.get("name"),
            "account_number": recipient_data.get("account_number"),
            "bank_code": recipient_data.get("bank_code"),
            "currency": recipient_data.get("currency", "NGN")
        }

        response_data, status_code = paystack_service.create_transfer_recipient(
            secret_key,
            transfer_recipient_data
        )

        return {
            "recipient": response_data.get("data", {}),
            "status": response_data.get("status", False),
            "message": response_data.get("message", "Recipient created successfully")
        }

    except Exception as e:
        return {"error": f"Failed to create recipient: {str(e)}"}


@router.post("/paystack/initiate-transfer")
async def initiate_paystack_transfer(
    transfer_data: dict,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.PAYROLL_PROCESS))
):
    """Initiate transfer using Paystack"""
    try:
        from app.services.hr_management.payroll_service import PAYROLL_SERVICES_AVAILABLE, PayrollPaystackService

        if not PAYROLL_SERVICES_AVAILABLE:
            return {"error": "Paystack service not available"}

        paystack_service = PayrollPaystackService(
            payroll_id=transfer_data.get("payroll_id"),
            payment_gatwway="paystack",
            net_pay=transfer_data.get("amount"),
            account_number=transfer_data.get("account_number"),
            sort_code=transfer_data.get("bank_code")
        )

        # Use the complete disburse_payment method
        transfer_result = paystack_service.disburse_payment()

        return {
            "transfer_result": transfer_result,
            "status": transfer_result.get("status"),
            "message": transfer_result.get("message"),
            "verified_details": {
                "bank_name": transfer_result.get("verified_bank_name"),
                "account_name": transfer_result.get("verified_account_name"),
                "account_number": transfer_result.get("verified_account_number"),
                "bank_code": transfer_result.get("verified_bank_sort_code")
            },
            "transaction_date": transfer_result.get("transaction_date")
        }

    except Exception as e:
        return {"error": f"Failed to initiate transfer: {str(e)}"}


@router.post("/paystack/webhook")
async def handle_paystack_webhook(
    webhook_data: dict,
    db: Session = Depends(get_db)
):
    """Handle Paystack webhooks"""
    try:
        # Process webhook using the enhanced service
        result = await payroll_service.handle_payment_webhook(db, webhook_data)

        return {
            "status": "success",
            "message": "Webhook processed successfully",
            "result": result
        }

    except Exception as e:
        logger.error(f"Failed to process webhook: {e}")
        return {"error": f"Failed to process webhook: {str(e)}"}


# ============ ENHANCED WORKING DAYS AND CALENDAR ============

@router.post("/working-days/calculate")
async def calculate_working_days_advanced(
    calculation_data: dict,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.PAYROLL_READ))
):
    """Calculate working days with advanced features"""
    try:
        from app.services.hr_management.payroll_service import PAYROLL_SERVICES_AVAILABLE, WorkingDaysService

        start_date = date.fromisoformat(calculation_data.get("start_date"))
        end_date = date.fromisoformat(calculation_data.get("end_date"))

        if not PAYROLL_SERVICES_AVAILABLE:
            # Basic calculation
            total_days = (end_date - start_date).days + 1
            working_days = 0
            current_date = start_date

            while current_date <= end_date:
                if current_date.weekday() < 5:  # Monday = 0, Sunday = 6
                    working_days += 1
                current_date += timedelta(days=1)

            return {
                "start_date": start_date.isoformat(),
                "end_date": end_date.isoformat(),
                "total_days": total_days,
                "working_days": working_days,
                "weekend_days": total_days - working_days,
                "holidays": [],
                "calculation_method": "basic"
            }

        working_days_service = WorkingDaysService()
        working_days_data = working_days_service.get_working_days_for_period(start_date, end_date)

        return {
            "start_date": start_date.isoformat(),
            "end_date": end_date.isoformat(),
            "working_days": working_days_data.get("working_days"),
            "total_days": working_days_data.get("total_days"),
            "holidays": working_days_data.get("holidays", []),
            "weekend_days": working_days_data.get("weekend_days"),
            "calculation_method": "advanced"
        }

    except Exception as e:
        return {"error": f"Failed to calculate working days: {str(e)}"}
