from fastapi import APIRouter, HTTPException, status, Depends, Query
from sqlalchemy.orm import Session
from typing import List, Optional
from uuid import UUID

from ..core.security import get_current_user, require_permission, CurrentUser
from ..core.rbac import Permissions
from ..db.session import get_db
from ..schemas.performance import (
    PerformanceReviewCreate, PerformanceReviewUpdate, PerformanceReviewResponse,
    PerformanceReviewListResponse, GoalCreate, GoalUpdate, GoalResponse,
    GoalListResponse, ReviewTemplateCreate, ReviewTemplateUpdate,
    ReviewTemplateResponse, DevelopmentPlanCreate, DevelopmentPlanUpdate,
    DevelopmentPlanResponse, PerformanceAnalytics, PerformanceDashboard,
    BulkReviewCreate, BulkGoalCreate, ReviewStatus, ReviewType,
    GoalStatus, GoalType
)
from ..services.hr_management.performance_service import PerformanceService

router = APIRouter()
performance_service = PerformanceService()

# Root endpoint
@router.get("/")
async def get_performance_overview(
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.PERFORMANCE_READ))
):
    """Get performance overview"""
    try:
        return {
            "message": "Performance management system",
            "endpoints": [
                "/dashboard - Get performance dashboard",
                "/reviews - Get performance reviews",
                "/goals - Get goals",
                "/templates - Get review templates"
            ]
        }
    except Exception as e:
        return {"error": str(e)}

# Dashboard endpoint
@router.get("/dashboard", response_model=PerformanceDashboard)
async def get_performance_dashboard(
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.PERFORMANCE_READ))
):
    """Get performance dashboard"""
    return await performance_service.get_performance_dashboard(db, current_user)


# Performance Review endpoints
@router.get("/reviews", response_model=PerformanceReviewListResponse)
async def get_performance_reviews(
    employee_id: Optional[UUID] = Query(None),
    reviewer_id: Optional[UUID] = Query(None),
    status: Optional[ReviewStatus] = Query(None),
    review_type: Optional[ReviewType] = Query(None),
    skip: int = Query(0, ge=0),
    limit: int = Query(20, ge=1, le=100),
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.PERFORMANCE_READ))
):
    """Get performance reviews with filtering"""
    return await performance_service.get_performance_reviews(
        db=db,
        employee_id=employee_id,
        reviewer_id=reviewer_id,
        status=status,
        review_type=review_type,
        skip=skip,
        limit=limit,
        current_user=current_user
    )


@router.post("/reviews", response_model=PerformanceReviewResponse)
async def create_performance_review(
    review_data: PerformanceReviewCreate,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.PERFORMANCE_CREATE))
):
    """Create new performance review"""
    return await performance_service.create_performance_review(db, review_data, current_user)


@router.get("/reviews/{review_id}", response_model=PerformanceReviewResponse)
async def get_performance_review(
    review_id: UUID,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.PERFORMANCE_READ))
):
    """Get performance review by ID"""
    review = await performance_service.get_performance_review_by_id(db, review_id, current_user)
    if not review:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Performance review not found"
        )
    return review


@router.put("/reviews/{review_id}", response_model=PerformanceReviewResponse)
async def update_performance_review(
    review_id: UUID,
    review_data: PerformanceReviewUpdate,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.PERFORMANCE_UPDATE))
):
    """Update performance review"""
    review = await performance_service.update_performance_review(db, review_id, review_data, current_user)
    if not review:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Performance review not found"
        )
    return review


# Goal endpoints
@router.get("/goals", response_model=GoalListResponse)
async def get_goals(
    employee_id: Optional[UUID] = Query(None),
    goal_type: Optional[GoalType] = Query(None),
    status: Optional[GoalStatus] = Query(None),
    skip: int = Query(0, ge=0),
    limit: int = Query(20, ge=1, le=100),
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.PERFORMANCE_READ))
):
    """Get goals with filtering"""
    return await performance_service.get_goals(
        db=db,
        employee_id=employee_id,
        goal_type=goal_type,
        status=status,
        skip=skip,
        limit=limit,
        current_user=current_user
    )


@router.post("/goals", response_model=GoalResponse)
async def create_goal(
    goal_data: GoalCreate,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.PERFORMANCE_CREATE))
):
    """Create new goal"""
    return await performance_service.create_goal(db, goal_data, current_user)


@router.get("/goals/{goal_id}", response_model=GoalResponse)
async def get_goal(
    goal_id: UUID,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.PERFORMANCE_READ))
):
    """Get goal by ID"""
    goal = await performance_service.get_goal_by_id(db, goal_id, current_user)
    if not goal:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Goal not found"
        )
    return goal


@router.put("/goals/{goal_id}", response_model=GoalResponse)
async def update_goal(
    goal_id: UUID,
    goal_data: GoalUpdate,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.PERFORMANCE_UPDATE))
):
    """Update goal"""
    goal = await performance_service.update_goal(db, goal_id, goal_data, current_user)
    if not goal:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Goal not found"
        )
    return goal


# Analytics endpoints
@router.get("/analytics/{employee_id}", response_model=PerformanceAnalytics)
async def get_employee_performance_analytics(
    employee_id: UUID,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.PERFORMANCE_READ))
):
    """Get performance analytics for employee"""
    analytics = await performance_service.get_employee_performance_analytics(
        db, employee_id, current_user
    )
    if not analytics:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Employee not found or no performance data available"
        )
    return analytics


# My performance endpoints (for employees)
@router.get("/my/reviews", response_model=PerformanceReviewListResponse)
async def get_my_performance_reviews(
    skip: int = Query(0, ge=0),
    limit: int = Query(20, ge=1, le=100),
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(get_current_user)
):
    """Get my performance reviews"""
    return await performance_service.get_performance_reviews(
        db=db,
        employee_id=UUID(current_user.user_id),
        skip=skip,
        limit=limit,
        current_user=current_user
    )


@router.get("/my/goals", response_model=GoalListResponse)
async def get_my_goals(
    status: Optional[GoalStatus] = Query(None),
    skip: int = Query(0, ge=0),
    limit: int = Query(20, ge=1, le=100),
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(get_current_user)
):
    """Get my goals"""
    return await performance_service.get_goals(
        db=db,
        employee_id=UUID(current_user.user_id),
        status=status,
        skip=skip,
        limit=limit,
        current_user=current_user
    )


@router.get("/my/analytics", response_model=PerformanceAnalytics)
async def get_my_performance_analytics(
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(get_current_user)
):
    """Get my performance analytics"""
    analytics = await performance_service.get_employee_performance_analytics(
        db, UUID(current_user.user_id), current_user
    )
    if not analytics:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="No performance data available"
        )
    return analytics
