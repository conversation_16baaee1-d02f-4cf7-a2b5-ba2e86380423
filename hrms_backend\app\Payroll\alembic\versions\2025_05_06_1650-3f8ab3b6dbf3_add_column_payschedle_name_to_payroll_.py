"""add column payschedle_name to payroll_history table

Revision ID: 3f8ab3b6dbf3
Revises: d3e353900775
Create Date: 2025-05-06 16:50:14.233934

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '3f8ab3b6dbf3'
down_revision: Union[str, None] = 'd3e353900775'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None



def upgrade() -> None:
    op.add_column("payroll_history", sa.Column('payschedle_name', sa.String(length=150), nullable=True))


def downgrade() -> None:
    op.drop_column("payroll_history", "payschedle_name")

