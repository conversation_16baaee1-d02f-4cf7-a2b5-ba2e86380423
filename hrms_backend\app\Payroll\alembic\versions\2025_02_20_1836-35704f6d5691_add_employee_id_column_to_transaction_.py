"""Add employee_id column to transaction_history

Revision ID: 35704f6d5691
Revises: eaadf73de156
Create Date: 2025-02-20 18:36:39.572095

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy import Column, Integer, String, Float
from sqlalchemy import func, DateTime, ForeignKey
from sqlalchemy.dialects.postgresql import BOOLEAN


# revision identifiers, used by Alembic.
revision: str = '35704f6d5691'
down_revision: Union[str, None] = 'eaadf73de156'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    op.add_column("transaction_history", sa.Column('employee_id', Integer, nullable=True))


def downgrade() -> None:
    op.drop_column("transaction_history","employee_id")

