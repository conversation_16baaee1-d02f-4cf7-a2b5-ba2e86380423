/**
 * Authentication Context for RBAC System
 * Provides global state management for user authentication and role-based access control
 * 
 * Features:
 * - User authentication state management
 * - Role-based permission checking
 * - Token management and validation
 * - Automatic role extraction from JWT tokens
 * - Persistent authentication state
 */

import React, { createContext, useContext, useReducer, useEffect } from 'react';
import { ROLES, isValidRole } from '../services/permissions';
import apiService from '../services/api';

// Initial authentication state
const initialState = {
  isAuthenticated: false,
  user: null,
  role: null,
  token: null,
  loading: true,
  error: null
};

// Authentication action types
const AUTH_ACTIONS = {
  LOGIN_START: 'LOGIN_START',
  LOGIN_SUCCESS: 'LOGIN_SUCCESS',
  LOGIN_FAILURE: 'LOGIN_FAILURE',
  LOGOUT: 'LOGOUT',
  SET_LOADING: 'SET_LOADING',
  CLEAR_ERROR: 'CLEAR_ERROR',
  UPDATE_USER: 'UPDATE_USER'
};

// Authentication reducer
function authReducer(state, action) {
  switch (action.type) {
    case AUTH_ACTIONS.LOGIN_START:
      return {
        ...state,
        loading: true,
        error: null
      };

    case AUTH_ACTIONS.LOGIN_SUCCESS:
      return {
        ...state,
        isAuthenticated: true,
        user: action.payload.user,
        role: action.payload.role,
        token: action.payload.token,
        loading: false,
        error: null
      };

    case AUTH_ACTIONS.LOGIN_FAILURE:
      return {
        ...state,
        isAuthenticated: false,
        user: null,
        role: null,
        token: null,
        loading: false,
        error: action.payload.error
      };

    case AUTH_ACTIONS.LOGOUT:
      return {
        ...initialState,
        loading: false
      };

    case AUTH_ACTIONS.SET_LOADING:
      return {
        ...state,
        loading: action.payload
      };

    case AUTH_ACTIONS.CLEAR_ERROR:
      return {
        ...state,
        error: null
      };

    case AUTH_ACTIONS.UPDATE_USER:
      return {
        ...state,
        user: { ...state.user, ...action.payload }
      };

    default:
      return state;
  }
}

// Create authentication context
const AuthContext = createContext();

// Authentication provider component
export function AuthProvider({ children }) {
  const [state, dispatch] = useReducer(authReducer, initialState);

  // Initialize authentication state from localStorage on app start
  useEffect(() => {
    initializeAuth();
  }, []);

  /**
   * Initialize authentication from stored token
   */
  const initializeAuth = async () => {
    try {
      const token = localStorage.getItem('hrm_auth_token');
      const userData = localStorage.getItem('hrm_user_data');

      if (token && userData) {
        const user = JSON.parse(userData);
        
        // Validate token and user data
        if (user.role && isValidRole(user.role)) {
          dispatch({
            type: AUTH_ACTIONS.LOGIN_SUCCESS,
            payload: {
              user,
              role: user.role,
              token
            }
          });
        } else {
          // Invalid data, clear storage
          clearAuthStorage();
          dispatch({ type: AUTH_ACTIONS.SET_LOADING, payload: false });
        }
      } else {
        dispatch({ type: AUTH_ACTIONS.SET_LOADING, payload: false });
      }
    } catch (error) {
      console.error('Error initializing auth:', error);
      clearAuthStorage();
      dispatch({ type: AUTH_ACTIONS.SET_LOADING, payload: false });
    }
  };

  /**
   * Login function
   * @param {Object} credentials - User credentials
   * @returns {Promise<boolean>} - Success status
   */
  const login = async (credentials) => {
    dispatch({ type: AUTH_ACTIONS.LOGIN_START });

    try {
      // Call the real backend API
      const response = await apiService.login(credentials);

      if (response && response.access_token) {
        const { access_token, user } = response;

        // Store authentication data
        localStorage.setItem('hrm_auth_token', access_token);
        localStorage.setItem('hrm_user_data', JSON.stringify(user));

        dispatch({
          type: AUTH_ACTIONS.LOGIN_SUCCESS,
          payload: {
            user,
            role: user.role,
            token: access_token
          }
        });

        return true;
      } else {
        dispatch({
          type: AUTH_ACTIONS.LOGIN_FAILURE,
          payload: { error: 'Invalid response from server' }
        });
        return false;
      }
    } catch (error) {
      console.error('Login error:', error);
      const errorMessage = error.response?.data?.detail || error.message || 'Login failed. Please try again.';
      dispatch({
        type: AUTH_ACTIONS.LOGIN_FAILURE,
        payload: { error: errorMessage }
      });
      return false;
    }
  };

  /**
   * Logout function
   */
  const logout = () => {
    clearAuthStorage();
    dispatch({ type: AUTH_ACTIONS.LOGOUT });
  };

  /**
   * Update user information
   * @param {Object} updates - User data updates
   */
  const updateUser = (updates) => {
    const updatedUser = { ...state.user, ...updates };
    localStorage.setItem('hrm_user_data', JSON.stringify(updatedUser));
    dispatch({
      type: AUTH_ACTIONS.UPDATE_USER,
      payload: updates
    });
  };

  /**
   * Clear error state
   */
  const clearError = () => {
    dispatch({ type: AUTH_ACTIONS.CLEAR_ERROR });
  };

  /**
   * Clear authentication storage
   */
  const clearAuthStorage = () => {
    localStorage.removeItem('hrm_auth_token');
    localStorage.removeItem('hrm_user_data');
  };

  /**
   * Simulate login for demo purposes
   * In production, replace with actual API call
   */
  const simulateLogin = async (credentials) => {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 1000));

    // Demo users for testing different roles
    const demoUsers = {
      '<EMAIL>': {
        id: 1,
        email: '<EMAIL>',
        name: 'Super Admin',
        role: ROLES.SUPER_ADMIN,
        department: 'IT',
        avatar: '/images/avatars/super-admin.jpg'
      },
      '<EMAIL>': {
        id: 2,
        email: '<EMAIL>',
        name: 'System Admin',
        role: ROLES.ADMIN,
        department: 'IT',
        avatar: '/images/avatars/admin.jpg'
      },
      '<EMAIL>': {
        id: 3,
        email: '<EMAIL>',
        name: 'HR Manager',
        role: ROLES.HR,
        department: 'Human Resources',
        avatar: '/images/avatars/hr.jpg'
      },
      '<EMAIL>': {
        id: 4,
        email: '<EMAIL>',
        name: 'Team Manager',
        role: ROLES.MANAGER,
        department: 'Engineering',
        avatar: '/images/avatars/manager.jpg'
      },
      '<EMAIL>': {
        id: 5,
        email: '<EMAIL>',
        name: 'John Employee',
        role: ROLES.EMPLOYEE,
        department: 'Engineering',
        avatar: '/images/avatars/employee.jpg'
      }
    };

    const user = demoUsers[credentials.email];
    
    if (user && credentials.password === 'password123') {
      return {
        success: true,
        data: {
          user,
          token: `mock_jwt_token_${user.id}_${Date.now()}`
        }
      };
    }

    return {
      success: false,
      error: 'Invalid email or password'
    };
  };

  // Context value
  const value = {
    // State
    ...state,
    
    // Actions
    login,
    logout,
    updateUser,
    clearError,
    
    // Utility functions
    isAuthenticated: state.isAuthenticated,
    userRole: state.role,
    userName: state.user?.name,
    userEmail: state.user?.email,
    userId: state.user?.id
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
}

// Custom hook to use authentication context
export function useAuth() {
  const context = useContext(AuthContext);
  
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  
  return context;
}

// Export action types for external use
export { AUTH_ACTIONS };
