from core.repositories.licenses import LicensesRepository

class LicensesService:
    def __init__(self) -> None:
        self.repository = LicensesRepository()

    def createLicenses(self, Kwargs):
        return self.repository.createLicenses(**Kwargs)
    
    def getLicenses(self, id):
        return self.repository.getLicenses(id)
    
    def fetchAll(self):
        licenses = self.repository.fetchAll()
        total_licenses = len(licenses)
        return licenses, total_licenses
    
    def updateLicenses(self, id, Kwargs):
        return self.repository.updateLicenses(id, **Kwargs)
    
    def getLicensesByKey(self, Kwarg):
        return self.repository.getLicensesByKeys(Kwarg)
    
    def deleteLicenses(self, id):
        return self.repository.deleteLicenses(id)