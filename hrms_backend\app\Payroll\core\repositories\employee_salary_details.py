from core.models.employee_salary_details import EmployeeSalDetailsModel
from core.databases.database import db

class EmployeeSalDetRepository:
    def __init__(self) -> None:
        pass

    @classmethod
    def createEmployeeSalDet(self, employee_id, template_id, amount, salary_type ):
        employeeSalaryDetails = EmployeeSalDetailsModel(
            employee_id = employee_id
            template_id = template_id
            amount = amount
            salary_type = salary_type
        )
        db.session.add(employeeSalaryDetails)
        db.session.commit()
        return employeeSalaryDetails

    @classmethod
    def getEmployeeSalDetails(self, id):
        return EmployeeSalDetailsModel.query.filter(EmployeeSalDetailsModel.id == id).first()
    
    @classmethod
    def getEmployeeSalDetByKeys(self, kwargs):
        return EmployeeSalDetailsModel.query.filter_by(**kwargs).all()
       

    @classmethod
    def updateEmployeeSalDets(self, id, **kwargs):
        employeesalDets = EmployeeSalDetailsModel.query.filter_by(id=id).first()
        if employeesalDets:
            for key, value in kwargs.items():
                setattr(employeesalDets, key, value)
            db.session.commit()
            return employeesalDets
        else:
            return None

    @classmethod
    def deleteEmployeeSalDets(self, id):
        return EmployeeSalDetailsModel.query.filter(EmployeeSalDetailsModel.id == id).delete()
        