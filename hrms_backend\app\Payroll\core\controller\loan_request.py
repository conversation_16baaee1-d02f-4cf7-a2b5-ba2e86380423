from flask import url_for, jsonify
from flask.views import <PERSON><PERSON>ie<PERSON>
from flask_jwt_extended import get_jwt_identity
from flask_smorest import Blueprint, abort
from core.middleware import roles_required
from core.models.employees import EmployeeModel
from core.repositories.employee import EmployeeRepository
from core.repositories.user import UserRepository
from schemas import LoanApprovalSchema, LoanRequestSchema, UpdateLoanRequestSchema
from sqlalchemy.orm.exc import NoResultFound
from sqlalchemy.exc import SQLAlchemyError, IntegrityError
import core.utils.response_message as RESPONSEMESSAGE
from core.services.loan_request import LoanRequestService
from core.utils.responseBuilder import ResponseBuilder

blueprint = Blueprint("Loan Request", __name__, description="Operations for Loan Request")
    
@blueprint.route("/loan_request")
class LoanRequestList(MethodView):
    @roles_required(['admin', 'employee'])
    @blueprint.response(200, LoanRequestSchema(many=True))
    def get(self):
     try:
         service = LoanRequestService()
         loan_request = service.getLoanRequests()
         if not loan_request:
             abort(401, message="Loan Request does not exist")
         loan_request_details = LoanRequestSchema(many=True).dump(loan_request)
         return ResponseBuilder(data=loan_request_details, status_code=200).build()
     except Exception as e:
            abort(400, message=str(e))  

    @roles_required(['employee'])
    @blueprint.response(200, description="Loan deleted successfully")
    def delete(self):
        print("🔍 DELETE /loan_request endpoint hit")
        try:
            employee = UserRepository().authUser()
            if not employee or not isinstance(employee, EmployeeModel):
                abort(401, message="Unauthorized or invalid employee")
            service = LoanRequestService()
            deleted_loan = service.deleteEmployeePendingLoan(employee.id)
            if not deleted_loan:
               abort(404, message="No pending loan found to delete")
            return {"message": "Loan request successfully deleted"}
        except Exception as e:
            abort(400, message=str(e))

    
    @roles_required(['employee'])
    @blueprint.arguments(UpdateLoanRequestSchema)
    @blueprint.response(201, LoanRequestSchema)
    def put(self, data):
        try:
            employee = UserRepository().authUser()
            if not employee or not isinstance(employee, EmployeeModel):
                abort(401, message="Unauthorized or invalid employee")

            service = LoanRequestService()
            updated_loan = service.updateEmployeePendingLoan(employee.id, data)

            if not updated_loan:
                abort(404, message="No pending loan found to update")

            return updated_loan

        except Exception as e:
            abort(400, message=str(e))
    
@blueprint.route("/loan_requests")
class LoanRequest(MethodView):
    @roles_required(['admin', 'employee'])
    @blueprint.response(200, LoanRequestSchema)
    def get(self):
        service = LoanRequestService()
        loan_request_list, total_requests = service.fetchAll()
        schema = LoanRequestSchema(many=True)
        loan_request_list = schema.dump(loan_request_list)
        return ResponseBuilder(data=loan_request_list, status_code=200, total=total_requests).build()
    

    @roles_required(['employee'])
    @blueprint.arguments(LoanRequestSchema)
    @blueprint.response(200, LoanRequestSchema)
    def post(self, data):
       try:
          employee = UserRepository().authUser()
          if not employee:
            abort(401, message="Unauthorized or not an employee")

          data["employee_id"] = employee.id

          service = LoanRequestService()
          new_request = service.createLoanRequest(data)

       except IntegrityError as e:
        print("IntegrityError:", str(e))
        abort(500, message="Error while creating loan request")
       except SQLAlchemyError as e:
        print("SQLAlchemyError:", str(e))
        abort(500, message="Database error while creating loan request")
       except Exception as e:
        print("Unexpected error:", str(e))
        abort(500, message="Unexpected error occurred")

       return new_request
    
@blueprint.route("/loan_requests/<int:loan_id>")
class LoanApprovalController(MethodView):
    @roles_required(["admin"])  # Only admins can access this
    @blueprint.arguments(LoanApprovalSchema)
    @blueprint.response(200, LoanRequestSchema)
    def put(self, data, loan_id):
        try:
            service = LoanRequestService()
            updated_loan = service.updateLoanApprovalStatus(loan_id, data)
            if not updated_loan:
                abort(404, message="Loan Request not found")
            return updated_loan
        except Exception as e:
            abort(400, message=str(e))

