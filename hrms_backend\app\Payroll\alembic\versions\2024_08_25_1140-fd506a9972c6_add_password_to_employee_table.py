"""Add password to employee table

Revision ID: fd506a9972c6
Revises: 787ae1705a2b
Create Date: 2024-08-25 11:40:09.905539

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'fd506a9972c6'
down_revision: Union[str, None] = '787ae1705a2b'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    op.add_column('employees', sa.Column('password_hash', sa.String(length=255), nullable=True))

def downgrade() -> None:
    op.drop_column('employees', 'password_hash')
