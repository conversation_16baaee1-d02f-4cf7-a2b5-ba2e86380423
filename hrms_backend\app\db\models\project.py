from sqlalchemy import Column, String, DateTime, Date, Foreign<PERSON>ey, Boolean, Text, Numeric, Integer, Enum
from sqlalchemy.dialects.postgresql import UUID, JSONB
from sqlalchemy.orm import relationship
from enum import Enum as PyEnum
from datetime import datetime, date
from typing import Optional

from ..base import BaseModel, AuditMixin


class ProjectStatus(PyEnum):
    PLANNING = "planning"
    ACTIVE = "active"
    ON_HOLD = "on_hold"
    COMPLETED = "completed"
    CANCELLED = "cancelled"
    ARCHIVED = "archived"


class ProjectPriority(PyEnum):
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class TaskStatus(PyEnum):
    TODO = "todo"
    IN_PROGRESS = "in_progress"
    IN_REVIEW = "in_review"
    TESTING = "testing"
    DONE = "done"
    BLOCKED = "blocked"
    CANCELLED = "cancelled"


class TaskPriority(PyEnum):
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    URGENT = "urgent"


class Project(BaseModel, AuditMixin):
    """Project model"""
    __tablename__ = "projects"

    # Basic information
    name = Column(String(200), nullable=False)
    code = Column(String(50), unique=True, nullable=False, index=True)
    description = Column(Text, nullable=True)
    organization_id = Column(UUID(as_uuid=True), nullable=False, index=True)

    # Project details
    status = Column(Enum(ProjectStatus), nullable=False, default=ProjectStatus.PLANNING)
    priority = Column(Enum(ProjectPriority), nullable=False, default=ProjectPriority.MEDIUM)

    # Timeline
    start_date = Column(Date, nullable=True)
    end_date = Column(Date, nullable=True)
    estimated_hours = Column(Numeric(8, 2), nullable=True)
    actual_hours = Column(Numeric(8, 2), nullable=True, default=0)

    # Budget
    budget = Column(Numeric(12, 2), nullable=True)
    actual_cost = Column(Numeric(12, 2), nullable=True, default=0)

    # Client information
    client_name = Column(String(200), nullable=True)
    client_contact = Column(String(255), nullable=True)

    # Project management
    project_manager_id = Column(UUID(as_uuid=True), ForeignKey("employees.id"), nullable=True)
    department_id = Column(UUID(as_uuid=True), ForeignKey("departments.id"), nullable=True)

    # Settings
    is_billable = Column(Boolean, default=True)
    hourly_rate = Column(Numeric(8, 2), nullable=True)

    # Progress tracking
    progress_percentage = Column(Numeric(5, 2), nullable=True, default=0)

    # Relationships
    project_manager = relationship("Employee", foreign_keys=[project_manager_id])
    department = relationship("Department")
    assignments = relationship("ProjectAssignment", back_populates="project")
    tasks = relationship("Task", back_populates="project")
    timesheet_entries = relationship("TimesheetEntry", back_populates="project")


class ProjectAssignment(BaseModel, AuditMixin):
    """Project team member assignment"""
    __tablename__ = "project_assignments"

    project_id = Column(UUID(as_uuid=True), ForeignKey("projects.id"), nullable=False, index=True)
    employee_id = Column(UUID(as_uuid=True), ForeignKey("employees.id"), nullable=False, index=True)

    # Assignment details
    role = Column(String(100), nullable=True)  # Developer, Designer, QA, etc.
    start_date = Column(Date, nullable=False)
    end_date = Column(Date, nullable=True)

    # Allocation
    allocation_percentage = Column(Numeric(5, 2), nullable=False, default=100)  # % of time allocated
    hourly_rate = Column(Numeric(8, 2), nullable=True)

    # Permissions
    can_edit_tasks = Column(Boolean, default=False)
    can_view_budget = Column(Boolean, default=False)
    can_approve_timesheets = Column(Boolean, default=False)

    # Status
    is_lead = Column(Boolean, default=False)

    # Relationships
    project = relationship("Project", back_populates="assignments")
    employee = relationship("Employee", back_populates="project_assignments")


class Task(BaseModel, AuditMixin):
    """Task model"""
    __tablename__ = "tasks"

    # Basic information
    title = Column(String(200), nullable=False)
    description = Column(Text, nullable=True)
    task_number = Column(String(50), nullable=True, index=True)

    # Project association
    project_id = Column(UUID(as_uuid=True), ForeignKey("projects.id"), nullable=False, index=True)

    # Task hierarchy
    parent_task_id = Column(UUID(as_uuid=True), ForeignKey("tasks.id"), nullable=True)

    # Status and priority
    status = Column(Enum(TaskStatus), nullable=False, default=TaskStatus.TODO)
    priority = Column(Enum(TaskPriority), nullable=False, default=TaskPriority.MEDIUM)

    # Assignment
    assignee_id = Column(UUID(as_uuid=True), ForeignKey("employees.id"), nullable=True)
    reporter_id = Column(UUID(as_uuid=True), ForeignKey("employees.id"), nullable=True)

    # Timeline
    start_date = Column(Date, nullable=True)
    due_date = Column(Date, nullable=True)
    estimated_hours = Column(Numeric(6, 2), nullable=True)
    actual_hours = Column(Numeric(6, 2), nullable=True, default=0)

    # Progress
    progress_percentage = Column(Numeric(5, 2), nullable=True, default=0)

    # Labels and tags
    labels = Column(JSONB, nullable=True)  # Array of labels
    tags = Column(JSONB, nullable=True)  # Array of tags

    # Attachments
    attachment_urls = Column(JSONB, nullable=True)  # Array of file URLs

    # Relationships
    project = relationship("Project", back_populates="tasks")
    assignee = relationship("Employee", foreign_keys=[assignee_id], back_populates="assigned_tasks")
    reporter = relationship("Employee", foreign_keys=[reporter_id], back_populates="reported_tasks")
    parent_task = relationship("Task", remote_side="Task.id", back_populates="subtasks")
    subtasks = relationship("Task", back_populates="parent_task")
    dependencies = relationship("TaskDependency", foreign_keys="TaskDependency.task_id", back_populates="task")
    dependents = relationship("TaskDependency", foreign_keys="TaskDependency.depends_on_task_id", back_populates="depends_on_task")
    comments = relationship("TaskComment", back_populates="task")
    timesheet_entries = relationship("TimesheetEntry", back_populates="task")


class TaskDependency(BaseModel):
    """Task dependency model"""
    __tablename__ = "task_dependencies"

    task_id = Column(UUID(as_uuid=True), ForeignKey("tasks.id"), nullable=False)
    depends_on_task_id = Column(UUID(as_uuid=True), ForeignKey("tasks.id"), nullable=False)
    dependency_type = Column(String(50), nullable=False, default="finish_to_start")  # finish_to_start, start_to_start, etc.

    # Relationships
    task = relationship("Task", foreign_keys=[task_id], back_populates="dependencies")
    depends_on_task = relationship("Task", foreign_keys=[depends_on_task_id], back_populates="dependents")


class TaskComment(BaseModel, AuditMixin):
    """Task comment model"""
    __tablename__ = "task_comments"

    task_id = Column(UUID(as_uuid=True), ForeignKey("tasks.id"), nullable=False, index=True)
    author_id = Column(UUID(as_uuid=True), ForeignKey("employees.id"), nullable=False)

    content = Column(Text, nullable=False)
    attachment_urls = Column(JSONB, nullable=True)  # Array of file URLs

    # Threading
    parent_comment_id = Column(UUID(as_uuid=True), ForeignKey("task_comments.id"), nullable=True)

    # Relationships
    task = relationship("Task", back_populates="comments")
    author = relationship("Employee")
    parent_comment = relationship("TaskComment", remote_side="TaskComment.id", back_populates="replies")
    replies = relationship("TaskComment", back_populates="parent_comment")


class TaskAssignment(BaseModel, AuditMixin):
    """Task assignment tracking"""
    __tablename__ = "task_assignments"

    task_id = Column(UUID(as_uuid=True), ForeignKey("tasks.id"), nullable=False)
    assignee_id = Column(UUID(as_uuid=True), ForeignKey("employees.id"), nullable=False)
    assigned_by = Column(UUID(as_uuid=True), ForeignKey("employees.id"), nullable=False)

    assigned_at = Column(DateTime(timezone=True), nullable=False, default=datetime.utcnow)
    unassigned_at = Column(DateTime(timezone=True), nullable=True)

    # Relationships
    task = relationship("Task", foreign_keys=[task_id])
    assignee = relationship("Employee", foreign_keys=[assignee_id], back_populates="task_assignments")
    assigner = relationship("Employee", foreign_keys=[assigned_by])


class ProjectMilestone(BaseModel, AuditMixin):
    """Project milestone model"""
    __tablename__ = "project_milestones"

    project_id = Column(UUID(as_uuid=True), ForeignKey("projects.id"), nullable=False)
    name = Column(String(200), nullable=False)
    description = Column(Text, nullable=True)

    # Timeline
    due_date = Column(Date, nullable=False)
    completed_date = Column(Date, nullable=True)

    # Status
    is_completed = Column(Boolean, default=False)

    # Dependencies
    depends_on_tasks = Column(JSONB, nullable=True)  # Array of task IDs

    # Relationships
    project = relationship("Project")


class ProjectTemplate(BaseModel):
    """Project template for creating new projects"""
    __tablename__ = "project_templates"

    name = Column(String(200), nullable=False)
    description = Column(Text, nullable=True)
    organization_id = Column(UUID(as_uuid=True), nullable=False, index=True)

    # Template configuration
    default_duration_days = Column(Integer, nullable=True)
    task_templates = Column(JSONB, nullable=True)  # Array of task template configs
    milestone_templates = Column(JSONB, nullable=True)  # Array of milestone configs

    # Settings
    is_public = Column(Boolean, default=False)
    category = Column(String(100), nullable=True)
