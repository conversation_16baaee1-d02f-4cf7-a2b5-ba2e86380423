from core.models.licenses import LicensesModel
from core.databases.database import db
from core.repositories.user import UserRepository

class LicensesRepository:
    def __init__(self) -> None:
        pass

    @classmethod
    def createLicenses(self, number_of_license, license_used, approved_by, date_approved):
        licenses = LicensesModel(
            number_of_license=number_of_license,
            license_used=license_used,
            approved_by=approved_by,
            date_approved=date_approved,
            user_id=UserRepository.authUserId()
        )
        db.session.add(licenses)
        db.session.commit()
        return licenses

    @classmethod
    def getLicenses(self, id):
        return LicensesModel.query.filter(LicensesModel.id == id).first()
    
    @classmethod
    def fetchAll(self):
        return LicensesModel.query.filter_by(user_id=UserRepository().authUserId()).order_by(LicensesModel.timestamp.desc()).all()

    @classmethod
    def getLicensesByKeys(self, kwargs):
        return LicensesModel.query.filter_by(**kwargs).all()

    @classmethod
    def updateLicenses(self, id, **kwargs):
        licenses = LicensesModel.query.filter_by(id=id).first()
        if licenses:
            for key, value in kwargs.items():
                setattr(licenses, key, value)
            db.session.commit()
            return licenses
        else:
            return None

    @classmethod
    def deleteLicenses(self, id):
        LicensesModel.query.filter(LicensesModel.id == id).delete()
        db.session.commit()
        return