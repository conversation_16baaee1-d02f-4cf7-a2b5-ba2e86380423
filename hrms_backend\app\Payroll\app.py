import os
from datetime import timed<PERSON>ta
from flask import Flask, jsonify
from flask_smorest import Api
from flask_jwt_extended import J<PERSON>TManager
from core.controller.user import blueprint as UserBluePrint
from core.controller.organisation import blueprint as OrganisationBluePrint
from core.controller.employee import blueprint as EmployeeBluePrint
from core.controller.departments import blueprint as DepartmentBluePrint
from core.controller.designations import blueprint as DesignationBluePrint
from core.controller.banks import blueprint as BankBluePrint
from core.controller.salary_components import blueprint as SalaryComponentBluePrint
from core.controller.salary_templates import blueprint as SalaryTemplateBluePrint
from core.controller.benefits import blueprint as BenefitBluePrint
from core.controller.activity_admin import blueprint as AdminActivityBluePrint
from core.controller.activity_employee import blueprint as EmployeeActivityBluePrint
from core.controller.licenses import blueprint as LicenseBluePrint
from core.controller.approvals import blueprint as ApprovalBluePrint
from core.controller.approval_settings import blueprint as ApprovalSettingsBluePrint
from core.controller.operator import blueprint as OperatorBluePrint
from core.controller.pay_schedule import blueprint as PayScheduleBluePrint
from core.controller.announcements import blueprint as AnnouncementBlueprint
from core.controller.loan_request import blueprint as LoanRequestBlueprint
from core.controller.zoho_config import blueprint as ZohoConfig
from core.controller.zoho_attendance import blueprint as  zoho_attendance_blueprint
from core.controller.paystack_integration import blueprint as Paystack_blueprint
from core.controller.paystack_getCredential import blueprint  as Paystack_credential_blueprint
from core.controller.payslip import blueprint  as Payslip_blueprint
from core.controller.remark import blueprint as RemarkBlueprint
from core.controller.employee_payslip import blueprint as employee_payslip
from core.controller.dashboard import blueprint as DashboardBlueprint
from core.controller.transaction_history import blueprint as TransactionBP
from core.controller.approval_history import blueprint as ApprovalHisotryBluePrint
from flask_cors import CORS
from core.controller.employee_bulk_register import blueprint as employee_bulk_register
from core.controller.email_config import blueprint as email_blueprint
# from core.controller.pensions import blueprint as PensionBluePrint
# from core.controller.setting_integrations import blueprint as SettingIntegrationBluePrint
from core.controller.payroll import blueprint as PayrollBlueprint
from core.controller.proration import blueprint as ProrationBlueprint
from core.controller.tax import blueprint as TaxBlueprint
# from core.controller.payroll_details import blueprint as PayrollDetailBluePrint
# from core.controller.payroll_processings import blueprint as PayrollProcessingBluePrint
# from core.controller.working_days import blueprint as WorkingDayBluePrint
# from core.controller.tax_regulations import blueprint as TaxRegulationBluePrint
from flask_cors import CORS
from core.databases.database import db
import core.models
from blacklist import BLACKLIST
from core.config.database import DBConfig
from datetime import timedelta

from flask import Flask
from flask_mail import Mail
from config import Config

# from core.services.celery_worker import celery


app = Flask(__name__)

cors = CORS(app)
@app.route("/")
def index():
    return jsonify({"message": "Avetium payroll server is live"})


jwt = JWTManager()
jwt.init_app(app)





app.config.from_object('config.Config')
mail = Mail(app)
app.config['MAIL_DEBUG'] = True  # Enable debug mode for email





if remote_origin:
    origins.append(remote_origin)
CORS(app, origins=origins, supports_credentials=True)

# Set token expiration times
app.config['JWT_ACCESS_TOKEN_EXPIRES'] = timedelta(minutes=5000)
# app.config['JWT_ACCESS_TOKEN_EXPIRES'] = timedelta(seconds=10)
app.config['JWT_REFRESH_TOKEN_EXPIRES'] = timedelta(days=30)

#if token is in blacklist
@jwt.token_in_blocklist_loader
def check_if_token_in_blacklist(jwt_header,jwt_payload):
    return jwt_payload["jti"] in BLACKLIST

#revoked token
@jwt.revoked_token_loader
def revoked_token_loader(jwt_header,jwt_payload):
    return (jsonify(
        {"message":"The token has been revoked", 
         "error":"token_revoked",
         "status_code":401
         }
        ), 401)   

@jwt.expired_token_loader
def expired_token_callback(jwt_header, jwt_payload):
    return (jsonify({"message":"the provided toke is expired", "error":"token_expired", "status_code":401}), 401)

@jwt.invalid_token_loader
def invalid_token_loader_callback(error):
    return (jsonify({"message":"Signature verification failed", "error":error, "status_code":401}), 401)

@jwt.unauthorized_loader
def missing_token_callback(error):
    return (jsonify(
        {"message":"Request does not contain an access token", 
         "error":"authorization_required",
         "status_code":401
         }
        ), 401)

@jwt.needs_fresh_token_loader
def token_not_fresh_callback(jwt_header, jwt_payload):
    return (jsonify(
        {"message":"The token is not fresh", 
         "error":"refresh_token_required",
         "status_code":401
         }
        ), 401)

db.init_app(app)

api = Api(app)

with app.app_context():
    db.create_all()

api.register_blueprint(UserBluePrint)
api.register_blueprint(OrganisationBluePrint)
api.register_blueprint(EmployeeBluePrint)
api.register_blueprint(DepartmentBluePrint)
api.register_blueprint(DesignationBluePrint)
api.register_blueprint(BankBluePrint)
api.register_blueprint(SalaryComponentBluePrint)
api.register_blueprint(SalaryTemplateBluePrint)
api.register_blueprint(ApprovalBluePrint)
api.register_blueprint(BenefitBluePrint)
api.register_blueprint(EmployeeActivityBluePrint)
api.register_blueprint(AdminActivityBluePrint)
api.register_blueprint(LicenseBluePrint)
api.register_blueprint(ApprovalSettingsBluePrint)
api.register_blueprint(OperatorBluePrint)
api.register_blueprint(PayScheduleBluePrint)
api.register_blueprint(AnnouncementBlueprint)
api.register_blueprint(LoanRequestBlueprint)
api.register_blueprint(ZohoConfig)
api.register_blueprint(zoho_attendance_blueprint)
api.register_blueprint(Paystack_blueprint)
api.register_blueprint(Paystack_credential_blueprint)
api.register_blueprint(Payslip_blueprint)
api.register_blueprint(RemarkBlueprint)
api.register_blueprint(employee_payslip)
api.register_blueprint(DashboardBlueprint)
api.register_blueprint(ApprovalHisotryBluePrint)
api.register_blueprint(email_blueprint)

# api.register_blueprint(PensionBluePrint)
# api.register_blueprint(PayrollDetailBluePrint)
# api.register_blueprint(PayrollProcessingBluePrint)
# api.register_blueprint(SettingIntegrationBluePrint)
# api.register_blueprint(WorkingDayBluePrint)
# api.register_blueprint(TaxRegulationBluePrint)
api.register_blueprint(PayrollBlueprint)
api.register_blueprint(ProrationBlueprint)
api.register_blueprint(TransactionBP)
api.register_blueprint(TaxBlueprint)
api.register_blueprint(employee_bulk_register)