from fastapi import APIRouter, HTTPException, status, Depends, Query
from sqlalchemy.orm import Session
from typing import List, Optional
from uuid import UUID
from datetime import date

from ..core.security import get_current_user, require_permission, CurrentUser
from ..core.rbac import Permissions
from ..db.session import get_db
from ..schemas.leave import (
    LeaveRequestCreate, LeaveRequestUpdate, LeaveRequestResponse,
    LeaveRequestListResponse, LeavePolicyCreate, LeavePolicyUpdate,
    LeavePolicyResponse, LeaveBalanceResponse, LeaveApprovalRequest,
    LeaveCalendarCreate, LeaveCalendarUpdate, LeaveCalendarResponse,
    LeaveEncashmentCreate, LeaveEncashmentResponse, LeaveStatus
)
from ..services.leave_management.leave_service import LeaveService

router = APIRouter()
leave_service = LeaveService()

# Root endpoint
@router.get("/")
async def get_leave_overview(
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.LEAVE_READ))
):
    """Get leave overview"""
    try:
        # Return basic leave overview
        return {
            "message": "Leave management system",
            "endpoints": [
                "/requests - Get leave requests",
                "/policies - Get leave policies",
                "/balance/{employee_id} - Get leave balance",
                "/calendar - Get leave calendar",
                "/dashboard - Get leave dashboard"
            ]
        }
    except Exception as e:
        return {"error": str(e)}


# Dashboard endpoint
@router.get("/dashboard")
async def get_leave_dashboard(
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.LEAVE_READ))
):
    """Get comprehensive leave dashboard"""
    return await leave_service.get_leave_dashboard(db, current_user)


# Workflow status endpoint
@router.get("/requests/{leave_request_id}/status")
async def get_leave_workflow_status(
    leave_request_id: UUID,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.LEAVE_READ))
):
    """Get detailed workflow status for a leave request"""
    return await leave_service.get_leave_workflow_status(db, leave_request_id, current_user)


# Leave calculation endpoint
@router.post("/calculate-days")
async def calculate_leave_days(
    calculation_data: dict,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.LEAVE_READ))
):
    """Calculate leave days with advanced logic"""
    try:
        start_date = date.fromisoformat(calculation_data['start_date'])
        end_date = date.fromisoformat(calculation_data['end_date'])
        duration_type = calculation_data.get('duration_type', 'full_day')
        employee_id = calculation_data.get('employee_id')

        result = await leave_service.calculate_automated_leave_days(
            start_date=start_date,
            end_date=end_date,
            duration_type=duration_type,
            db=db,
            organization_id=current_user.organization_id,
            employee_id=UUID(employee_id) if employee_id else None
        )

        return result
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Error calculating leave days: {str(e)}"
        )


# Leave conflicts check endpoint
@router.post("/check-conflicts")
async def check_leave_conflicts(
    conflict_data: dict,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.LEAVE_READ))
):
    """Check for leave conflicts and team impact"""
    try:
        employee_id = UUID(conflict_data['employee_id'])
        start_date = date.fromisoformat(conflict_data['start_date'])
        end_date = date.fromisoformat(conflict_data['end_date'])
        exclude_request_id = conflict_data.get('exclude_request_id')

        result = await leave_service.check_leave_conflicts(
            db=db,
            employee_id=employee_id,
            start_date=start_date,
            end_date=end_date,
            current_user=current_user,
            exclude_request_id=UUID(exclude_request_id) if exclude_request_id else None
        )

        return result
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Error checking conflicts: {str(e)}"
        )

# Leave Request endpoints
@router.get("/requests", response_model=LeaveRequestListResponse)
async def get_leave_requests(
    employee_id: Optional[UUID] = Query(None),
    status: Optional[LeaveStatus] = Query(None),
    start_date: Optional[date] = Query(None),
    end_date: Optional[date] = Query(None),
    skip: int = Query(0, ge=0),
    limit: int = Query(20, ge=1, le=100),
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.LEAVE_READ))
):
    """Get leave requests with filtering"""
    return await leave_service.get_leave_requests(
        db=db,
        employee_id=employee_id,
        status=status,
        start_date=start_date,
        end_date=end_date,
        skip=skip,
        limit=limit,
        current_user=current_user
    )


@router.post("/requests/{employee_id}", response_model=LeaveRequestResponse)
async def create_leave_request(
    employee_id: UUID,
    leave_data: LeaveRequestCreate,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.LEAVE_CREATE))
):
    """Create new leave request"""
    return await leave_service.create_leave_request(db, employee_id, leave_data, current_user)


@router.get("/requests/{request_id}", response_model=LeaveRequestResponse)
async def get_leave_request(
    request_id: UUID,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.LEAVE_READ))
):
    """Get specific leave request"""
    requests = await leave_service.get_leave_requests(
        db=db,
        skip=0,
        limit=1,
        current_user=current_user
    )

    # Find the specific request
    request = next((r for r in requests.requests if r.id == request_id), None)
    if not request:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Leave request not found"
        )

    return request


@router.put("/requests/{request_id}/approve", response_model=LeaveRequestResponse)
async def approve_leave_request(
    request_id: UUID,
    approved: bool = True,
    comments: Optional[str] = None,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.LEAVE_APPROVE))
):
    """Approve or reject leave request"""
    return await leave_service.approve_leave_request(
        db, request_id, approved, comments, current_user
    )


@router.put("/requests/{request_id}/reject", response_model=LeaveRequestResponse)
async def reject_leave_request(
    request_id: UUID,
    comments: Optional[str] = None,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.LEAVE_APPROVE))
):
    """Reject leave request"""
    return await leave_service.approve_leave_request(
        db, request_id, False, comments, current_user
    )


# Leave Policy endpoints
@router.get("/policies", response_model=List[LeavePolicyResponse])
async def get_leave_policies(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=100),
    leave_type: Optional[str] = Query(None),
    is_active: Optional[bool] = Query(True),
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(get_current_user)
):
    """Get leave policies for the organization"""
    return await leave_service.get_leave_policies(
        db=db,
        current_user=current_user,
        skip=skip,
        limit=limit,
        leave_type=leave_type,
        is_active=is_active
    )


@router.post("/policies", response_model=LeavePolicyResponse)
async def create_leave_policy(
    policy_data: LeavePolicyCreate,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.LEAVE_ADMIN))
):
    """Create new leave policy"""
    return await leave_service.create_leave_policy(db, policy_data, current_user)


@router.get("/policies/{policy_id}", response_model=LeavePolicyResponse)
async def get_leave_policy(
    policy_id: UUID,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.LEAVE_READ))
):
    """Get specific leave policy"""
    return await leave_service.get_leave_policy(db, policy_id, current_user)


@router.put("/policies/{policy_id}", response_model=LeavePolicyResponse)
async def update_leave_policy(
    policy_id: UUID,
    policy_data: LeavePolicyUpdate,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.LEAVE_ADMIN))
):
    """Update leave policy"""
    return await leave_service.update_leave_policy(db, policy_id, policy_data, current_user)


@router.delete("/policies/{policy_id}")
async def delete_leave_policy(
    policy_id: UUID,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.LEAVE_ADMIN))
):
    """Delete leave policy"""
    return await leave_service.delete_leave_policy(db, policy_id, current_user)


# Leave Balance endpoints
@router.get("/balance/{employee_id}", response_model=List[LeaveBalanceResponse])
async def get_leave_balance(
    employee_id: UUID,
    year: Optional[int] = Query(None),
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(get_current_user)
):
    """Get leave balance for employee"""
    # Allow employees to view their own balance
    if current_user.user_id != str(employee_id):
        # Check if user has permission to view other balances
        current_user = Depends(require_permission(Permissions.LEAVE_READ))(current_user)

    return await leave_service.get_leave_balance(db, employee_id, year, current_user)


# My Leave endpoints (for employees)
@router.get("/my/requests", response_model=LeaveRequestListResponse)
async def get_my_leave_requests(
    status: Optional[LeaveStatus] = Query(None),
    start_date: Optional[date] = Query(None),
    end_date: Optional[date] = Query(None),
    skip: int = Query(0, ge=0),
    limit: int = Query(20, ge=1, le=100),
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(get_current_user)
):
    """Get my leave requests"""
    return await leave_service.get_leave_requests(
        db=db,
        employee_id=UUID(current_user.user_id),
        status=status,
        start_date=start_date,
        end_date=end_date,
        skip=skip,
        limit=limit,
        current_user=current_user
    )


@router.post("/my/requests", response_model=LeaveRequestResponse)
async def create_my_leave_request(
    leave_data: LeaveRequestCreate,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(get_current_user)
):
    """Create my leave request"""
    return await leave_service.create_leave_request(
        db, UUID(current_user.user_id), leave_data, current_user
    )


@router.get("/my/balance", response_model=List[LeaveBalanceResponse])
async def get_my_leave_balance(
    year: Optional[int] = Query(None),
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(get_current_user)
):
    """Get my leave balance"""
    return await leave_service.get_leave_balance(
        db, UUID(current_user.user_id), year, current_user
    )


# Leave Encashment endpoints
@router.post("/encashment/{employee_id}", response_model=LeaveEncashmentResponse)
async def create_leave_encashment(
    employee_id: UUID,
    encashment_data: LeaveEncashmentCreate,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.LEAVE_CREATE))
):
    """Create leave encashment request"""
    return await leave_service.create_leave_encashment(db, employee_id, encashment_data, current_user)


@router.get("/encashment", response_model=List[LeaveEncashmentResponse])
async def get_leave_encashments(
    employee_id: Optional[UUID] = Query(None),
    status: Optional[LeaveStatus] = Query(None),
    skip: int = Query(0, ge=0),
    limit: int = Query(20, ge=1, le=100),
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.LEAVE_READ))
):
    """Get leave encashment requests"""
    return await leave_service.get_leave_encashments(
        db=db,
        employee_id=employee_id,
        status=status,
        skip=skip,
        limit=limit,
        current_user=current_user
    )


@router.put("/encashment/{encashment_id}/approve", response_model=LeaveEncashmentResponse)
async def approve_leave_encashment(
    encashment_id: UUID,
    approved: bool = True,
    comments: Optional[str] = None,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.LEAVE_APPROVE))
):
    """Approve or reject leave encashment request"""
    return await leave_service.approve_leave_encashment(
        db, encashment_id, approved, comments, current_user
    )


@router.post("/my/encashment", response_model=LeaveEncashmentResponse)
async def create_my_leave_encashment(
    encashment_data: LeaveEncashmentCreate,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(get_current_user)
):
    """Create my leave encashment request"""
    return await leave_service.create_leave_encashment(
        db, UUID(current_user.user_id), encashment_data, current_user
    )


@router.get("/my/encashment", response_model=List[LeaveEncashmentResponse])
async def get_my_leave_encashments(
    status: Optional[LeaveStatus] = Query(None),
    skip: int = Query(0, ge=0),
    limit: int = Query(20, ge=1, le=100),
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(get_current_user)
):
    """Get my leave encashment requests"""
    return await leave_service.get_leave_encashments(
        db=db,
        employee_id=UUID(current_user.user_id),
        status=status,
        skip=skip,
        limit=limit,
        current_user=current_user
    )
