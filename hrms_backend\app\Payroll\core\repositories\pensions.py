from core.models.pensions import PensionsModel
from core.databases.database import db

class PensionsRepository:
    def __init__(self) -> None:
        pass

    @classmethod
    def createPensions(self, pension_name, pension_fund_custodian, pfc_account_number, address, city, state, country, zip_code):
        pensions = PensionsModel(
            pension_name = pension_name,
            pension_fund_custodian = pension_fund_custodian,
            pfc_account_number = pfc_account_number,
            address = address,
            city = city,
            state = state,
            country = country,
            zip_code = zip_code
        )
        db.session.add(pensions)
        db.session.commit()
        return pensions

    @classmethod
    def getPensions(self, id):
        return PensionsModel.query.filter(PensionsModel.id == id).first()
    
    @classmethod
    def getPensionsByKeys(self, kwargs):
        return PensionsModel.query.filter_by(**kwargs).all()

    @classmethod
    def updatePensions(self, id, **kwargs):
        pensions = PensionsModel.query.filter_by(id=id).first()
        if pensions:
            for key, value in kwargs.items():
                setattr(pensions, key, value)
            db.session.commit()
            return pensions
        else:
            return None

    @classmethod
    def deletePensions(self, id):
        return PensionsModel.query.filter(PensionsModel.id == id).delete()
        