from core.services.employee import EmployeeService
from core.services.payroll_history import PayrollHistoryService
from core.utils.responseBuilder import ResponseBuilder
from core.services.component_processor import ComponentProcessor
from schemas import EmployeeSchema, PayrollHistorySchema
from flask_smorest import Blueprint, abort


class PaySlipAttachementService:
    def __init__(self):
        pass
    
    def verify_payroll_ids(self, payroll_ids):
        """
        Verifies if payroll IDs exist in the database.
        :param payroll_ids: List of payroll IDs to verify
        :return: List of verified payroll IDs
        """
        verified_ids = []
        for payroll_id in payroll_ids:
            # Simulate checking database (Replace with actual DB query)
            if payroll_id > 0:  # Assume valid IDs are positive integers
                verified_ids.append(payroll_id)
            else:
                print(f"Invalid payroll ID: {payroll_id}")

        return verified_ids
        
    def generate_payslip_dynamic_attachment(self, employee_payroll_id):
            # service = EmployeeService()
            # employee_service = service.getEmployeeById(id)

            payroll_service = PayrollHistoryService().get_payroll_history_by_id(employee_payroll_id)
           

            # if not employee_service:
            #     abort(401, message="Employee payslip does not exist")

            if not payroll_service:
                abort(401, message="Employee payslip does not exist")

            emp_ps = PayrollHistorySchema().dump(payroll_service)
          
            # emp_ps = EmployeeSchema().dump(employee_service)
            # employee_id, employee_gross, employee_template, employee_components = emp_ps["id"], emp_ps["gross_pay"], emp_ps["template"], emp_ps["employee_components"]
            employee_id = emp_ps["employee_id"]
            employee_gross = emp_ps["gross_pay"]
            prorated_gross = emp_ps["prorated_gross"] or 0.0
            employee_template = emp_ps["employee"]["template"]
            employee_benefit = emp_ps["employee"]["employee_benefits"]
            employee_components = emp_ps["employee"]["employee_components"]
            # Extract employee data
            employee = emp_ps.get("employee", {})

            # Process salary components
            process_payslip = ComponentProcessor(
                employee_id=employee_id,
                employee_gross=employee_gross,
                prorated_gross=prorated_gross,
                salary_benefit=employee_benefit,
                salary_component=employee_components
            ).generate_salary_response()

            # ✅ Include `emp_ps` in the payslip response
            process_payslip["employee"] = emp_ps
            # print(f"Employee PaySlip Dynamic:: {process_payslip}")
            return process_payslip
         
 