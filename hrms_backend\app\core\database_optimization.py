"""
Database optimization utilities for performance improvements
"""

import logging
from sqlalchemy import text, Index
from sqlalchemy.orm import Session
from typing import List, Dict, Any

from ..db.session import engine

logger = logging.getLogger(__name__)


class DatabaseOptimizer:
    """Database optimization utilities"""
    
    @staticmethod
    def create_performance_indexes():
        """Create indexes for better query performance"""
        indexes = [
            # Employee indexes
            "CREATE INDEX IF NOT EXISTS idx_employees_organization_id ON employees(organization_id);",
            "CREATE INDEX IF NOT EXISTS idx_employees_department_id ON employees(department_id);",
            "CREATE INDEX IF NOT EXISTS idx_employees_status ON employees(status);",
            "CREATE INDEX IF NOT EXISTS idx_employees_email ON employees(email);",
            "CREATE INDEX IF NOT EXISTS idx_employees_employee_id ON employees(employee_id);",
            "CREATE INDEX IF NOT EXISTS idx_employees_search ON employees USING gin(to_tsvector('english', first_name || ' ' || last_name || ' ' || email));",
            
            # Attendance indexes
            "CREATE INDEX IF NOT EXISTS idx_attendance_records_employee_id ON attendance_records(employee_id);",
            "CREATE INDEX IF NOT EXISTS idx_attendance_records_date ON attendance_records(date);",
            "CREATE INDEX IF NOT EXISTS idx_attendance_records_employee_date ON attendance_records(employee_id, date);",
            "CREATE INDEX IF NOT EXISTS idx_attendance_logs_employee_id ON attendance_logs(employee_id);",
            "CREATE INDEX IF NOT EXISTS idx_attendance_logs_timestamp ON attendance_logs(timestamp);",
            
            # Timesheet indexes
            "CREATE INDEX IF NOT EXISTS idx_timesheet_entries_employee_id ON timesheet_entries(employee_id);",
            "CREATE INDEX IF NOT EXISTS idx_timesheet_entries_start_time ON timesheet_entries(start_time);",
            "CREATE INDEX IF NOT EXISTS idx_timesheet_entries_project_id ON timesheet_entries(project_id);",
            "CREATE INDEX IF NOT EXISTS idx_timesheet_entries_billable ON timesheet_entries(billable);",
            "CREATE INDEX IF NOT EXISTS idx_timesheet_entries_employee_date ON timesheet_entries(employee_id, start_time);",
            
            # Project indexes
            "CREATE INDEX IF NOT EXISTS idx_projects_organization_id ON projects(organization_id);",
            "CREATE INDEX IF NOT EXISTS idx_projects_status ON projects(status);",
            "CREATE INDEX IF NOT EXISTS idx_projects_owner_id ON projects(owner_id);",
            
            # Kanban indexes
            "CREATE INDEX IF NOT EXISTS idx_kanban_boards_organization_id ON kanban_boards(organization_id);",
            "CREATE INDEX IF NOT EXISTS idx_kanban_boards_owner_id ON kanban_boards(owner_id);",
            "CREATE INDEX IF NOT EXISTS idx_kanban_cards_board_id ON kanban_cards(board_id);",
            "CREATE INDEX IF NOT EXISTS idx_kanban_cards_column_id ON kanban_cards(column_id);",
            
            # Ticket indexes
            "CREATE INDEX IF NOT EXISTS idx_tickets_organization_id ON tickets(organization_id);",
            "CREATE INDEX IF NOT EXISTS idx_tickets_assigned_to ON tickets(assigned_to);",
            "CREATE INDEX IF NOT EXISTS idx_tickets_status ON tickets(status);",
            "CREATE INDEX IF NOT EXISTS idx_tickets_priority ON tickets(priority);",
            "CREATE INDEX IF NOT EXISTS idx_tickets_created_at ON tickets(created_at);",
            
            # Leave indexes
            "CREATE INDEX IF NOT EXISTS idx_leave_requests_employee_id ON leave_requests(employee_id);",
            "CREATE INDEX IF NOT EXISTS idx_leave_requests_start_date ON leave_requests(start_date);",
            "CREATE INDEX IF NOT EXISTS idx_leave_requests_status ON leave_requests(status);",
            
            # General audit indexes
            "CREATE INDEX IF NOT EXISTS idx_created_at_general ON employees(created_at);",
            "CREATE INDEX IF NOT EXISTS idx_updated_at_general ON employees(updated_at);",
            "CREATE INDEX IF NOT EXISTS idx_is_active_general ON employees(is_active);",
        ]
        
        try:
            with engine.connect() as conn:
                for index_sql in indexes:
                    try:
                        conn.execute(text(index_sql))
                        logger.info(f"✅ Created index: {index_sql.split('idx_')[1].split(' ')[0] if 'idx_' in index_sql else 'unknown'}")
                    except Exception as e:
                        logger.warning(f"⚠️ Index creation failed: {e}")
                
                conn.commit()
                logger.info("✅ Database indexes optimization completed")
                
        except Exception as e:
            logger.error(f"❌ Database optimization failed: {e}")
            raise
    
    @staticmethod
    def analyze_query_performance() -> Dict[str, Any]:
        """Analyze database query performance"""
        try:
            with engine.connect() as conn:
                # Get slow queries (if pg_stat_statements is enabled)
                slow_queries_sql = """
                SELECT 
                    query,
                    calls,
                    total_time,
                    mean_time,
                    rows
                FROM pg_stat_statements 
                WHERE mean_time > 100  -- queries taking more than 100ms on average
                ORDER BY mean_time DESC 
                LIMIT 10;
                """
                
                # Get table sizes
                table_sizes_sql = """
                SELECT 
                    schemaname,
                    tablename,
                    pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size,
                    pg_total_relation_size(schemaname||'.'||tablename) as size_bytes
                FROM pg_tables 
                WHERE schemaname = 'public'
                ORDER BY size_bytes DESC;
                """
                
                # Get index usage
                index_usage_sql = """
                SELECT 
                    schemaname,
                    tablename,
                    indexname,
                    idx_scan,
                    idx_tup_read,
                    idx_tup_fetch
                FROM pg_stat_user_indexes
                ORDER BY idx_scan DESC;
                """
                
                result = {
                    "timestamp": "now()",
                    "table_sizes": [],
                    "index_usage": [],
                    "slow_queries": []
                }
                
                try:
                    # Get table sizes
                    table_result = conn.execute(text(table_sizes_sql))
                    result["table_sizes"] = [dict(row) for row in table_result]
                except Exception as e:
                    logger.warning(f"Could not get table sizes: {e}")
                
                try:
                    # Get index usage
                    index_result = conn.execute(text(index_usage_sql))
                    result["index_usage"] = [dict(row) for row in index_result]
                except Exception as e:
                    logger.warning(f"Could not get index usage: {e}")
                
                try:
                    # Get slow queries (might not be available)
                    slow_result = conn.execute(text(slow_queries_sql))
                    result["slow_queries"] = [dict(row) for row in slow_result]
                except Exception as e:
                    logger.warning(f"Could not get slow queries (pg_stat_statements not enabled?): {e}")
                
                return result
                
        except Exception as e:
            logger.error(f"Performance analysis failed: {e}")
            return {"error": str(e)}
    
    @staticmethod
    def optimize_database_settings():
        """Apply database optimization settings"""
        optimization_queries = [
            # Enable query planning optimizations
            "SET random_page_cost = 1.1;",  # For SSD storage
            "SET effective_cache_size = '1GB';",  # Adjust based on available RAM
            "SET shared_buffers = '256MB';",  # Adjust based on available RAM
            "SET work_mem = '4MB';",  # For sorting and hash operations
            "SET maintenance_work_mem = '64MB';",  # For maintenance operations
            
            # Enable statistics collection
            "SET track_activities = on;",
            "SET track_counts = on;",
            "SET track_io_timing = on;",
            "SET track_functions = 'all';",
        ]
        
        try:
            with engine.connect() as conn:
                for query in optimization_queries:
                    try:
                        conn.execute(text(query))
                        logger.info(f"✅ Applied setting: {query}")
                    except Exception as e:
                        logger.warning(f"⚠️ Could not apply setting {query}: {e}")
                
                logger.info("✅ Database settings optimization completed")
                
        except Exception as e:
            logger.error(f"❌ Database settings optimization failed: {e}")
    
    @staticmethod
    def vacuum_analyze_tables():
        """Run VACUUM ANALYZE on all tables for better query planning"""
        try:
            with engine.connect() as conn:
                # Get all user tables
                tables_sql = """
                SELECT tablename 
                FROM pg_tables 
                WHERE schemaname = 'public';
                """
                
                result = conn.execute(text(tables_sql))
                tables = [row[0] for row in result]
                
                for table in tables:
                    try:
                        # Use autocommit for VACUUM
                        conn.execute(text(f"VACUUM ANALYZE {table};"))
                        logger.info(f"✅ Vacuumed and analyzed table: {table}")
                    except Exception as e:
                        logger.warning(f"⚠️ Could not vacuum table {table}: {e}")
                
                logger.info("✅ Database vacuum and analyze completed")
                
        except Exception as e:
            logger.error(f"❌ Database vacuum failed: {e}")


def run_database_optimization():
    """Run complete database optimization"""
    logger.info("🚀 Starting database optimization...")
    
    optimizer = DatabaseOptimizer()
    
    # Create performance indexes
    optimizer.create_performance_indexes()
    
    # Optimize database settings
    optimizer.optimize_database_settings()
    
    # Run vacuum analyze
    optimizer.vacuum_analyze_tables()
    
    # Analyze performance
    performance_report = optimizer.analyze_query_performance()
    logger.info(f"📊 Performance analysis completed: {len(performance_report.get('table_sizes', []))} tables analyzed")
    
    logger.info("✅ Database optimization completed!")
    return performance_report


if __name__ == "__main__":
    run_database_optimization()
