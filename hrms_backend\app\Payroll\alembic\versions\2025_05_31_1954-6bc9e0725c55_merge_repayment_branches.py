"""merge repayment branches

Revision ID: 6bc9e0725c55
Revises: 5821ba0d074e, 8fc940a36883
Create Date: 2025-05-31 19:54:35.743822

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '6bc9e0725c55'
down_revision: Union[str, None] = ('5821ba0d074e', '8fc940a36883')
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    pass


def downgrade() -> None:
    pass
