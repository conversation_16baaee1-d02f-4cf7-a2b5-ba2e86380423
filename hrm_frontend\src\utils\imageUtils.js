// Helper function to handle image paths
export function getImagePath(imageName) {
  // For development
  if (import.meta.env.DEV) {
    return `/images/${imageName}`;
  }
  // For production
  return `/images/${imageName}`;
}

// Helper function to create image with fallback
export function createImageWithFallback(src, alt, className = "", fallbackContent = null) {
  const img = new Image();
  img.src = src;
  
  return new Promise((resolve) => {
    img.onload = () => {
      resolve({
        error: false,
        element: <img src={src || "/placeholder.svg"} alt={alt} className={className} />
      });
    };
    
    img.onerror = () => {
      resolve({
        error: true,
        element: fallbackContent || (
          <div className={`flex items-center justify-center bg-gray-200 ${className}`}>
            <span className="text-gray-500">{alt || "Image"}</span>
          </div>
        )
      });
    };
  });
}