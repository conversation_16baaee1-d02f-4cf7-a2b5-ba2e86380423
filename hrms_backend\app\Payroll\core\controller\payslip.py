from flask import url_for, jsonify, request
from flask.views import MethodView
from flask_smorest import Blueprint, abort
from core.middleware import roles_required
from schemas import EmployeeSchema, PayrollHistorySchema, EmployeePayslip
from sqlalchemy.orm.exc import NoResultFound
from sqlalchemy.exc import SQLAlchemyError, IntegrityError
import core.utils.response_message as RESPONSEMESSAGE
from core.services.employee import EmployeeService
from core.utils.responseBuilder import ResponseBuilder
from core.services.component_processor import ComponentProcessor
from core.services.payroll_history import PayrollHistoryService
from datetime import datetime

blueprint = Blueprint("Payslip", __name__, description="Operations for payslip")
    
@blueprint.route("/employee_payslip/<id>")
class Payslip(MethodView):
    @roles_required(['admin','employee'])
    @blueprint.response(200, EmployeeSchema)
    def get(self, id):
        # payroll_id = request.args.get("payroll") 
        employee_data = PayrollHistoryService().get_payroll_hostory_id(id)
        
        if employee_data is None:
            abort(404, message="No record found")

        employee_payroll = PayrollHistorySchema().dump(employee_data)

        prorated_gross = employee_payroll.get("prorated_gross")
        prorated_status = employee_payroll.get("is_prorated")
        prorated_tax = employee_payroll.get("prorated_monthly_tax") 
        prorated_net = employee_payroll.get("prorated_net") 
        net_pay = employee_payroll.get("netpay") 

        employee_id, employee_gross, employee_benefits, employee_components = employee_payroll["employee"]["id"], employee_payroll["gross_pay"], employee_payroll["employee"]["employee_benefits"], employee_payroll["employee"]["employee_components"]
        
        process_payslip = ComponentProcessor(employee_id, employee_gross, prorated_gross, prorated_status, employee_benefits, employee_components).generate_salary_response()
        process_payslip["employee"] = employee_payroll["employee"]
        process_payslip["prorated_status"] = prorated_status
        process_payslip["prorated_tax"] = prorated_tax
        process_payslip["prorated_net"] = prorated_net
        process_payslip["net_pay"] = net_pay
        process_payslip["id"] = employee_payroll["id"]
        return ResponseBuilder(data=process_payslip, status_code=200).build()   
        
@blueprint.route("/payslip")
class PayslipList(MethodView):
    @roles_required(['employee'])
    @blueprint.response(200, EmployeePayslip)
    def get(self):
        employee_record = PayrollHistoryService().get_payroll_history_by_employee_id()
        payroll_data = EmployeePayslip(many=True).dump(employee_record)
        print(payroll_data)

        # payslip_record= []
        # for data in payroll_data:
        #     prorated_gross = data.get("prorated_gross") #0.0
        #     prorated_status = data.get("is_prorated") #False
        #     prorated_tax = data.get("prorated_monthly_tax") 
        #     prorated_net = data.get("prorated_net") 
        #     created_at = datetime.fromisoformat(data.get("created_at")) 
        #     period = created_at.strftime("%b %Y")

        #     employee_id, employee_gross, employee_benefits, employee_components = data["employee"]["id"], data["employee"]["gross_pay"], data["employee"]["employee_benefits"], data["employee"]["employee_components"]
            
        #     process_payslip = ComponentProcessor(employee_id, employee_gross, prorated_gross, prorated_status, employee_benefits, employee_components).generate_salary_response()
        #     process_payslip["employee"] = data["employee"]
        #     process_payslip["prorated_status"] = prorated_status
        #     process_payslip["prorated_tax"] = prorated_tax
        #     process_payslip["prorated_net"] = prorated_net
        #     process_payslip["id"] = data["id"]
        #     process_payslip["gross_pay"] = data.get("gross_pay") 
        #     process_payslip["period"] = period
        #     payslip_record.append(process_payslip)

        payslip_record = []
        for data in payroll_data:
            prorated_gross = data.get("prorated_gross") 
            prorated_status = data.get("is_prorated") 
            created_at = datetime.fromisoformat(data.get("created_at")) 
            period = created_at.strftime("%b %Y")

            payslip_record.append({
                "id": data["id"],
                "gross_pay": prorated_gross if prorated_status else data.get("gross_pay"),
                "period": period,
                "payschedle_name": data['payschedle_name'],
            })

        return ResponseBuilder(payslip_record, 200, None, None, "Employee payslip").build()   


