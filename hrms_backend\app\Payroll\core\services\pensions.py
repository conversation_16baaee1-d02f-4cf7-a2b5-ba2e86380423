from core.repositories.pensions import PensionsRepository

class PensionsService:
    def __init__(self) -> None:
        self.repository = PensionsRepository()

    def createPensions(self, Kwargs):
        # print(Kwargs)
        return self.repository.createPensions(**Kwargs)
    
    def getPensions(self, id):
        return self.repository.getPensions(id)
    
    def updatePensions(self, id, **Kwargs):
        return self.repository.updatePensions(id, **Kwargs)
    
    def getPensionsByKey(self, Kwarg):
        return self.repository.getPensionsByKeys(Kwarg)
    
    def deletePensions(self, id):
        return self.repository.deletePensions(id)