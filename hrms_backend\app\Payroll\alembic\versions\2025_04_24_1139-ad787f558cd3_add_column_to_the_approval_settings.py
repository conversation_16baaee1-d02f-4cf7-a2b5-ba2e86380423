"""add column to the approval settings

Revision ID: ad787f558cd3
Revises: d34c99cd3cf4
Create Date: 2025-04-24 11:39:54.931531

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects.postgresql import BOOLEAN
from sqlalchemy import func, TIMESTAMP, ForeignKey

# revision identifiers, used by Alembic.
revision: str = 'ad787f558cd3'
down_revision: Union[str, None] = 'd34c99cd3cf4'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    op.add_column('approval_settings', sa.Column('status', BOOLEAN, nullable=True, default=False)),


def downgrade() -> None:
    op.drop_column('approval_settings', 'status')
