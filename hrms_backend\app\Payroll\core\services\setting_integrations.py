from core.repositories.setting_integrations import SettingIntegrationsRepository

class SettingIntegrationsService:
    def __init__(self) -> None:
        self.repository = SettingIntegrationsRepository()

    def createSettingIntegrations(self, Kwargs):
        # print(Kwargs)
        return self.repository.createSettingIntegrations(**Kwargs)
    
    def getSettingIntegrations(self, id):
        return self.repository.getSettingIntegrations(id)
    
    def updateSettingIntegrations(self, id, **Kwargs):
        return self.repository.updateSettingIntegrations(id, **Kwargs)
    
    def getSettingIntegrationsByKey(self, Kwarg):
        return self.repository.getSettingIntegrationsByKeys(Kwarg)
    
    def deleteSettingIntegrations(self, id):
        return self.repository.deleteSettingIntegrations(id)py