"""
Shift Pattern Service for advanced roster management
"""

from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, func
from typing import Optional, List, Dict
from uuid import UUID
from datetime import datetime, date, timedelta, time
from fastapi import HTTPException, status
import logging

from ...db.models.shift import ShiftPattern, ShiftPatternAssignment, Shift, ShiftAssignment
from ...db.models.employee import Employee
from ...schemas.shift import (
    ShiftPatternCreate, ShiftPatternUpdate, ShiftPatternResponse,
    ShiftPatternAssignmentCreate, ShiftPatternAssignmentResponse
)
from ...core.security import CurrentUser
from ...core.websocket_manager import notification_manager

logger = logging.getLogger(__name__)


class ShiftPatternService:
    """Service for managing shift patterns and rotations"""

    async def create_shift_pattern(
        self,
        db: Session,
        pattern_data: ShiftPatternCreate,
        current_user: CurrentUser
    ) -> ShiftPatternResponse:
        """Create a new shift pattern"""
        try:
            # Validate pattern configuration
            await self._validate_pattern_config(pattern_data)

            pattern = ShiftPattern(
                name=pattern_data.name,
                description=pattern_data.description,
                organization_id=current_user.organization_id,
                pattern_type=pattern_data.pattern_type,
                rotation_days=pattern_data.rotation_days,
                shifts_config=pattern_data.shifts_config,
                working_days=pattern_data.working_days,
                rest_days=pattern_data.rest_days,
                is_active=True,
                created_by=current_user.user_id
            )

            db.add(pattern)
            db.commit()
            db.refresh(pattern)

            logger.info(f"Shift pattern created: {pattern.id}")
            return ShiftPatternResponse.from_orm(pattern)

        except HTTPException:
            raise
        except Exception as e:
            db.rollback()
            logger.error(f"Error creating shift pattern: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Error creating shift pattern"
            )

    async def assign_pattern_to_employee(
        self,
        db: Session,
        assignment_data: ShiftPatternAssignmentCreate,
        current_user: CurrentUser
    ) -> ShiftPatternAssignmentResponse:
        """Assign shift pattern to employee"""
        try:
            # Validate employee and pattern
            employee = db.query(Employee).filter(
                Employee.id == assignment_data.employee_id,
                Employee.organization_id == current_user.organization_id
            ).first()

            if not employee:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Employee not found"
                )

            pattern = db.query(ShiftPattern).filter(
                ShiftPattern.id == assignment_data.pattern_id,
                ShiftPattern.organization_id == current_user.organization_id
            ).first()

            if not pattern:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Shift pattern not found"
                )

            # Check for overlapping assignments
            existing = db.query(ShiftPatternAssignment).filter(
                ShiftPatternAssignment.employee_id == assignment_data.employee_id,
                ShiftPatternAssignment.start_date <= assignment_data.end_date,
                or_(
                    ShiftPatternAssignment.end_date.is_(None),
                    ShiftPatternAssignment.end_date >= assignment_data.start_date
                ),
                ShiftPatternAssignment.is_active == True
            ).first()

            if existing:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Employee already has an active pattern assignment for this period"
                )

            assignment = ShiftPatternAssignment(
                employee_id=assignment_data.employee_id,
                pattern_id=assignment_data.pattern_id,
                start_date=assignment_data.start_date,
                end_date=assignment_data.end_date,
                cycle_start_date=assignment_data.cycle_start_date or assignment_data.start_date,
                is_active=True,
                created_by=current_user.user_id
            )

            db.add(assignment)
            db.commit()
            db.refresh(assignment)

            # Generate shift assignments based on pattern
            await self._generate_pattern_assignments(db, assignment, current_user)

            logger.info(f"Pattern assignment created: {assignment.id}")
            return ShiftPatternAssignmentResponse.from_orm(assignment)

        except HTTPException:
            raise
        except Exception as e:
            db.rollback()
            logger.error(f"Error assigning pattern: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Error assigning shift pattern"
            )

    async def _generate_pattern_assignments(
        self,
        db: Session,
        pattern_assignment: ShiftPatternAssignment,
        current_user: CurrentUser
    ):
        """Generate individual shift assignments based on pattern"""
        try:
            pattern = pattern_assignment.pattern
            current_date = pattern_assignment.start_date
            end_date = pattern_assignment.end_date or (current_date + timedelta(days=365))  # Default 1 year
            
            cycle_day = 0
            
            while current_date <= end_date:
                # Determine if this is a working day based on pattern
                if await self._is_working_day(pattern, cycle_day, current_date):
                    # Get shift for this day
                    shift_config = await self._get_shift_for_day(pattern, cycle_day, current_date)
                    
                    if shift_config:
                        # Find or create the shift
                        shift = await self._get_or_create_shift(db, shift_config, current_user)
                        
                        # Create shift assignment
                        shift_assignment = ShiftAssignment(
                            employee_id=pattern_assignment.employee_id,
                            shift_id=shift.id,
                            assigned_date=current_date,
                            is_mandatory=True,
                            pattern_assignment_id=pattern_assignment.id,
                            start_date=current_date,
                            end_date=current_date,
                            is_active=True,
                            created_by=current_user.user_id
                        )
                        
                        db.add(shift_assignment)
                
                current_date += timedelta(days=1)
                cycle_day = (cycle_day + 1) % pattern.rotation_days
            
            db.commit()
            logger.info(f"Generated pattern assignments for {pattern_assignment.id}")
            
        except Exception as e:
            logger.error(f"Error generating pattern assignments: {e}")
            raise

    async def _is_working_day(self, pattern: ShiftPattern, cycle_day: int, date: date) -> bool:
        """Check if a day is a working day based on pattern"""
        # Check if it's a rest day in the cycle
        if cycle_day in (pattern.rest_days or []):
            return False
        
        # Check day of week
        weekday = date.weekday()  # 0=Monday, 6=Sunday
        if weekday not in (pattern.working_days or [0, 1, 2, 3, 4]):  # Default Mon-Fri
            return False
        
        return True

    async def _get_shift_for_day(self, pattern: ShiftPattern, cycle_day: int, date: date) -> Optional[Dict]:
        """Get shift configuration for a specific day in the pattern"""
        shifts_config = pattern.shifts_config or {}
        
        if pattern.pattern_type == "fixed":
            # Same shift every working day
            return shifts_config.get("default")
        
        elif pattern.pattern_type == "rotating":
            # Different shifts based on cycle day
            shift_key = str(cycle_day % len(shifts_config))
            return shifts_config.get(shift_key)
        
        elif pattern.pattern_type == "weekly":
            # Different shifts based on day of week
            weekday = str(date.weekday())
            return shifts_config.get(weekday)
        
        return None

    async def _get_or_create_shift(self, db: Session, shift_config: Dict, current_user: CurrentUser) -> Shift:
        """Get existing shift or create new one based on configuration"""
        # Try to find existing shift with same configuration
        existing_shift = db.query(Shift).filter(
            Shift.organization_id == current_user.organization_id,
            Shift.name == shift_config.get("name"),
            Shift.start_time == time.fromisoformat(shift_config.get("start_time")),
            Shift.end_time == time.fromisoformat(shift_config.get("end_time")),
            Shift.is_active == True
        ).first()
        
        if existing_shift:
            return existing_shift
        
        # Create new shift
        from .shift_service import shift_service
        from ...schemas.shift import ShiftCreate, ShiftType
        
        shift_data = ShiftCreate(
            name=shift_config.get("name"),
            description=f"Auto-generated from pattern: {shift_config.get('name')}",
            start_time=time.fromisoformat(shift_config.get("start_time")),
            end_time=time.fromisoformat(shift_config.get("end_time")),
            shift_type=ShiftType(shift_config.get("type", "regular")),
            is_overnight=shift_config.get("is_overnight", False),
            break_duration=shift_config.get("break_duration", 60),
            max_employees=shift_config.get("max_employees", 10)
        )
        
        shift_response = await shift_service.create_shift(db, shift_data, current_user)
        return db.query(Shift).filter(Shift.id == shift_response.id).first()

    async def _validate_pattern_config(self, pattern_data: ShiftPatternCreate):
        """Validate shift pattern configuration"""
        if not pattern_data.shifts_config:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Shifts configuration is required"
            )
        
        if pattern_data.rotation_days <= 0:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Rotation days must be greater than 0"
            )
        
        # Validate shift configurations
        for shift_key, shift_config in pattern_data.shifts_config.items():
            if not all(key in shift_config for key in ["name", "start_time", "end_time"]):
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"Invalid shift configuration for {shift_key}"
                )

    async def get_employee_pattern_schedule(
        self,
        db: Session,
        employee_id: UUID,
        start_date: date,
        end_date: date,
        current_user: CurrentUser
    ) -> Dict:
        """Get employee's schedule based on assigned patterns"""
        try:
            # Get active pattern assignments for employee
            assignments = db.query(ShiftPatternAssignment).filter(
                ShiftPatternAssignment.employee_id == employee_id,
                ShiftPatternAssignment.start_date <= end_date,
                or_(
                    ShiftPatternAssignment.end_date.is_(None),
                    ShiftPatternAssignment.end_date >= start_date
                ),
                ShiftPatternAssignment.is_active == True
            ).all()
            
            schedule = []
            current_date = start_date
            
            while current_date <= end_date:
                day_schedule = {
                    "date": current_date.isoformat(),
                    "shifts": [],
                    "is_working_day": False,
                    "pattern_info": None
                }
                
                # Find applicable pattern for this date
                for assignment in assignments:
                    if (assignment.start_date <= current_date and 
                        (assignment.end_date is None or assignment.end_date >= current_date)):
                        
                        pattern = assignment.pattern
                        days_since_cycle_start = (current_date - assignment.cycle_start_date).days
                        cycle_day = days_since_cycle_start % pattern.rotation_days
                        
                        if await self._is_working_day(pattern, cycle_day, current_date):
                            shift_config = await self._get_shift_for_day(pattern, cycle_day, current_date)
                            
                            if shift_config:
                                day_schedule["shifts"].append(shift_config)
                                day_schedule["is_working_day"] = True
                                day_schedule["pattern_info"] = {
                                    "pattern_name": pattern.name,
                                    "cycle_day": cycle_day,
                                    "pattern_type": pattern.pattern_type
                                }
                        break
                
                schedule.append(day_schedule)
                current_date += timedelta(days=1)
            
            return {
                "employee_id": str(employee_id),
                "period": {
                    "start_date": start_date.isoformat(),
                    "end_date": end_date.isoformat()
                },
                "schedule": schedule,
                "summary": {
                    "total_days": len(schedule),
                    "working_days": len([day for day in schedule if day["is_working_day"]]),
                    "rest_days": len([day for day in schedule if not day["is_working_day"]])
                }
            }
            
        except Exception as e:
            logger.error(f"Error getting employee pattern schedule: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Error retrieving employee schedule"
            )


# Create service instance
shift_pattern_service = ShiftPatternService()
