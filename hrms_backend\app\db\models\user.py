from sqlalchemy import Column, String, Boolean, DateTime, Text, Integer, ForeignKey, Enum as SQLEnum
from sqlalchemy.dialects.postgresql import UUID, JSONB
from sqlalchemy.orm import relationship, foreign
from uuid import uuid4
from datetime import datetime
import enum

from ..base import BaseModel, AuditMixin


class UserStatus(str, enum.Enum):
    """User status enumeration"""
    ACTIVE = "active"
    INACTIVE = "inactive"
    SUSPENDED = "suspended"
    PENDING = "pending"


class User(BaseModel, AuditMixin):
    """User model for authentication and authorization"""
    __tablename__ = "users"

    # Basic Information
    username = Column(String(100), unique=True, nullable=False, index=True)
    email = Column(String(255), unique=True, nullable=False, index=True)
    password_hash = Column(String(255), nullable=False)
    
    # Personal Information
    first_name = Column(String(100), nullable=False)
    last_name = Column(String(100), nullable=False)
    phone = Column(String(20), nullable=True)
    
    # Status and Security
    status = Column(SQLEnum(UserStatus), nullable=False, default=UserStatus.ACTIVE)
    is_active = Column(Boolean, default=True, nullable=False)
    is_verified = Column(Boolean, default=False, nullable=False)
    email_verified = Column(Boolean, default=False, nullable=False)
    phone_verified = Column(Boolean, default=False, nullable=False)
    
    # 2FA Settings
    two_fa_enabled = Column(Boolean, default=False, nullable=False)
    two_fa_secret = Column(String(255), nullable=True)
    backup_codes = Column(JSONB, nullable=True)  # Array of backup codes
    
    # Login Information
    last_login = Column(DateTime, nullable=True)
    login_attempts = Column(Integer, default=0, nullable=False)
    locked_until = Column(DateTime, nullable=True)
    password_changed_at = Column(DateTime, nullable=True)
    
    # Role and Organization
    role_id = Column(UUID(as_uuid=True), ForeignKey("roles.id"), nullable=False)
    organization_id = Column(UUID(as_uuid=True), nullable=True, index=True)
    
    # Profile
    profile_picture_url = Column(String(500), nullable=True)
    timezone = Column(String(50), nullable=True, default="UTC")
    language = Column(String(10), nullable=True, default="en")
    
    # Preferences
    preferences = Column(JSONB, nullable=True)  # User preferences as JSON
    
    # Relationships
    role = relationship("Role", back_populates="users")
    employee = relationship("Employee", back_populates="user", uselist=False, primaryjoin="User.id == foreign(Employee.user_id)")
    audit_logs = relationship("AuditLog", back_populates="user")
    refresh_tokens = relationship("RefreshToken", back_populates="user")

    def __repr__(self):
        return f"<User(username='{self.username}', email='{self.email}')>"


class Role(BaseModel, AuditMixin):
    """Role model for RBAC"""
    __tablename__ = "roles"

    # Basic Information
    name = Column(String(100), unique=True, nullable=False, index=True)
    display_name = Column(String(100), nullable=False)
    description = Column(Text, nullable=True)
    
    # Role Configuration
    permissions = Column(JSONB, nullable=False)  # Array of permissions
    is_system_role = Column(Boolean, default=False, nullable=False)  # System vs custom roles
    is_active = Column(Boolean, default=True, nullable=False)
    
    # Hierarchy
    parent_role_id = Column(UUID(as_uuid=True), ForeignKey("roles.id"), nullable=True)
    level = Column(Integer, nullable=False, default=0)  # Role hierarchy level
    
    # Organization
    organization_id = Column(UUID(as_uuid=True), nullable=True, index=True)
    
    # Relationships
    users = relationship("User", back_populates="role")
    parent_role = relationship("Role", remote_side="Role.id", back_populates="child_roles")
    child_roles = relationship("Role", back_populates="parent_role")

    def __repr__(self):
        return f"<Role(name='{self.name}', display_name='{self.display_name}')>"


class RefreshToken(BaseModel):
    """Refresh token model for JWT token management"""
    __tablename__ = "refresh_tokens"

    # Token Information
    token = Column(String(500), unique=True, nullable=False, index=True)
    user_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=False)
    
    # Expiration and Status
    expires_at = Column(DateTime, nullable=False)
    is_revoked = Column(Boolean, default=False, nullable=False)
    revoked_at = Column(DateTime, nullable=True)
    
    # Device and Session Information
    device_info = Column(JSONB, nullable=True)  # Device fingerprint, user agent, etc.
    ip_address = Column(String(45), nullable=True)  # IPv4 or IPv6
    session_id = Column(String(255), nullable=True)
    
    # Relationships
    user = relationship("User", back_populates="refresh_tokens")

    def __repr__(self):
        return f"<RefreshToken(user_id='{self.user_id}', expires_at='{self.expires_at}')>"


class AuditLog(BaseModel):
    """Audit log model for tracking all user actions"""
    __tablename__ = "audit_logs"

    # Action Information
    action = Column(String(100), nullable=False, index=True)  # CREATE, UPDATE, DELETE, LOGIN, etc.
    resource_type = Column(String(100), nullable=False, index=True)  # employees, leave_requests, etc.
    resource_id = Column(String(255), nullable=True, index=True)  # ID of the affected resource
    
    # User Information
    user_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=True)
    user_email = Column(String(255), nullable=True)  # Backup in case user is deleted
    user_role = Column(String(100), nullable=True)
    
    # Request Information
    ip_address = Column(String(45), nullable=True)
    user_agent = Column(Text, nullable=True)
    request_method = Column(String(10), nullable=True)  # GET, POST, PUT, DELETE
    request_url = Column(Text, nullable=True)
    
    # Change Information
    old_values = Column(JSONB, nullable=True)  # Previous values before change
    new_values = Column(JSONB, nullable=True)  # New values after change
    changes = Column(JSONB, nullable=True)  # Summary of what changed
    
    # Additional Context
    organization_id = Column(UUID(as_uuid=True), nullable=True, index=True)
    session_id = Column(String(255), nullable=True)
    correlation_id = Column(String(255), nullable=True)  # For tracking related actions
    
    # Status and Result
    status = Column(String(20), nullable=False, default="SUCCESS")  # SUCCESS, FAILED, ERROR
    error_message = Column(Text, nullable=True)
    
    # Relationships
    user = relationship("User", back_populates="audit_logs")

    def __repr__(self):
        return f"<AuditLog(action='{self.action}', resource_type='{self.resource_type}', user_id='{self.user_id}')>"
