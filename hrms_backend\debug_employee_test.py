#!/usr/bin/env python3
"""
Debug script to test employee endpoints directly
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from sqlalchemy.orm import Session
from app.db.session import get_db
from app.db.models.employee import Employee, Department, Designation
from app.schemas.employee import EmployeeCreate
from app.core.security import CurrentUser
import uuid
from datetime import datetime

def test_employee_query():
    """Test basic employee query without eager loading"""
    print("🔍 Testing Employee Query...")
    
    # Get database session
    db = next(get_db())
    
    try:
        # Test 1: Basic employee query
        print("\n1. Testing basic employee query...")
        employees = db.query(Employee).filter(Employee.is_active == True).limit(5).all()
        print(f"   Found {len(employees)} active employees")
        
        for emp in employees:
            print(f"   - {emp.first_name} {emp.last_name} ({emp.employee_id}) - Role: {emp.role}")
        
        # Test 2: Query with organization filter
        print("\n2. Testing with organization filter...")
        org_id = "fd3d7ca7-bad7-44c6-84a8-e74fc40bba0e"
        org_employees = db.query(Employee).filter(
            Employee.is_active == True,
            Employee.organization_id == org_id
        ).limit(5).all()
        print(f"   Found {len(org_employees)} employees in organization {org_id}")
        
        # Test 3: Test department and designation relationships
        print("\n3. Testing relationships...")
        for emp in org_employees[:2]:  # Test first 2 employees
            print(f"   Employee: {emp.first_name} {emp.last_name}")
            try:
                if emp.department_id:
                    dept = db.query(Department).filter(Department.id == emp.department_id).first()
                    print(f"     Department: {dept.name if dept else 'Not found'}")
                else:
                    print(f"     Department: None")
                    
                if emp.designation_id:
                    desig = db.query(Designation).filter(Designation.id == emp.designation_id).first()
                    print(f"     Designation: {desig.title if desig else 'Not found'}")
                else:
                    print(f"     Designation: None")
            except Exception as e:
                print(f"     Error accessing relationships: {e}")
        
        # Test 4: Test employee creation data structure
        print("\n4. Testing employee creation structure...")
        test_employee_data = {
            "employee_id": "TEST999",
            "first_name": "Debug",
            "last_name": "Test",
            "email": "<EMAIL>",
            "phone": "+1234567890",
            "role": "employee",
            "hire_date": "2024-01-01"
        }
        
        print(f"   Test employee data: {test_employee_data}")
        
        # Check if this employee already exists
        existing = db.query(Employee).filter(
            Employee.employee_id == test_employee_data["employee_id"],
            Employee.organization_id == org_id
        ).first()
        
        if existing:
            print(f"   Employee {test_employee_data['employee_id']} already exists")
        else:
            print(f"   Employee {test_employee_data['employee_id']} does not exist - ready for creation")
        
        print("\n✅ Employee query tests completed successfully!")
        return True
        
    except Exception as e:
        print(f"\n❌ Error during employee query test: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        db.close()

async def test_employee_service_methods():
    """Test the actual employee service methods"""
    print("\n🔧 Testing Employee Service Methods...")

    from app.services.employee_service import EmployeeService

    # Create a mock current user
    current_user = CurrentUser(
        user_id=uuid.uuid4(),
        email="<EMAIL>",
        role="ADMIN",
        organization_id=uuid.UUID("fd3d7ca7-bad7-44c6-84a8-e74fc40bba0e")
    )

    employee_service = EmployeeService()
    db = next(get_db())

    try:
        # Test get_employees method
        print("\n1. Testing get_employees method...")
        result = await employee_service.get_employees(
            db=db,
            skip=0,
            limit=5,
            current_user=current_user
        )
        print(f"   Result type: {type(result)}")
        print(f"   Total employees: {result.total}")
        print(f"   Employees returned: {len(result.employees)}")

        # Test create_employee method
        print("\n2. Testing create_employee method...")
        test_data = EmployeeCreate(
            employee_id="DEBUG001",
            first_name="Debug",
            last_name="User",
            email="<EMAIL>",
            phone="+1234567890",
            role="employee",
            hire_date=datetime.now().date()
        )

        # Check if employee exists first
        existing = db.query(Employee).filter(
            Employee.employee_id == test_data.employee_id,
            Employee.organization_id == current_user.organization_id
        ).first()

        if existing:
            print(f"   Employee {test_data.employee_id} already exists, skipping creation")
        else:
            created_employee = await employee_service.create_employee(
                db=db,
                employee_data=test_data,
                current_user=current_user
            )
            print(f"   Created employee: {created_employee.first_name} {created_employee.last_name}")

        print("\n✅ Employee service tests completed successfully!")
        return True

    except Exception as e:
        print(f"\n❌ Error during employee service test: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        db.close()

if __name__ == "__main__":
    print("🚀 Starting Employee Debug Tests")
    print("=" * 50)
    
    # Test 1: Basic queries
    query_success = test_employee_query()
    
    # Test 2: Service methods (only if queries work)
    if query_success:
        import asyncio
        service_success = asyncio.run(test_employee_service_methods())
    else:
        service_success = False
    
    print("\n" + "=" * 50)
    print("📊 FINAL RESULTS:")
    print(f"   Query Tests: {'✅ PASS' if query_success else '❌ FAIL'}")
    print(f"   Service Tests: {'✅ PASS' if service_success else '❌ FAIL'}")
    
    if query_success and service_success:
        print("\n🎉 All tests passed! Employee endpoints should work.")
    else:
        print("\n⚠️  Some tests failed. Check the errors above.")
