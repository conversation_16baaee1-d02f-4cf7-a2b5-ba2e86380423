from fastapi import APIRouter, HTTPException, status, Depends, Query
from sqlalchemy.orm import Session
from typing import List, Optional, Dict, Any
from uuid import UUID
from pydantic import BaseModel, EmailStr
from datetime import date

from ..core.security import get_current_user, require_permission, CurrentUser
from ..core.rbac import Permissions
from ..db.session import get_db
from ..schemas.onboarding import (
    WorkflowTemplateCreate, WorkflowTemplateResponse,
    OnboardingWorkflowCreate, OnboardingWorkflowUpdate, OnboardingWorkflowResponse,
    OnboardingWorkflowListResponse, OffboardingWorkflowCreate, OffboardingWorkflowUpdate,
    OffboardingWorkflowResponse, OffboardingWorkflowListResponse,
    OnboardingTaskCreate, OnboardingTaskUpdate, OnboardingTaskResponse,
    OffboardingTaskCreate, OffboardingTaskUpdate, OffboardingTaskResponse,
    TaskListResponse, DocumentCreate, DocumentUpdate, DocumentResponse,
    DocumentListResponse
)
from ..services.hr_management.onboarding_service import OnboardingService
from ..db.models.onboarding import WorkflowStatus, TaskStatus, DocumentStatus

router = APIRouter()
onboarding_service = OnboardingService()

# Quick Onboarding Schema
class QuickOnboardingRequest(BaseModel):
    """Schema for quick onboarding request"""
    employee_name: str
    employee_email: EmailStr
    department_id: Optional[UUID] = None
    designation_id: Optional[UUID] = None
    start_date: Optional[date] = None

class QuickOnboardingResponse(BaseModel):
    """Schema for quick onboarding response"""
    success: bool
    employee: Dict[str, Any]
    workflow: Dict[str, Any]
    credentials: Dict[str, str]
    message: str

# Quick Onboarding Endpoint
@router.post("/quick-onboard", response_model=QuickOnboardingResponse)
async def quick_onboard_employee(
    request: QuickOnboardingRequest,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.ONBOARDING_CREATE))
):
    """
    Quick onboarding: Create employee and start onboarding workflow from minimal info
    HR/Admin can simply enter name and email to start the complete onboarding process
    """
    return await onboarding_service.quick_onboard_employee(
        db=db,
        employee_name=request.employee_name,
        employee_email=request.employee_email,
        department_id=request.department_id,
        designation_id=request.designation_id,
        start_date=request.start_date,
        current_user=current_user
    )

# Workflow Template Endpoints
@router.post("/templates", response_model=WorkflowTemplateResponse)
async def create_workflow_template(
    template_data: WorkflowTemplateCreate,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.ONBOARDING_CREATE))
):
    """Create a workflow template"""
    return await onboarding_service.create_workflow_template(db, template_data, current_user)


@router.get("/templates", response_model=List[WorkflowTemplateResponse])
async def get_workflow_templates(
    workflow_type: Optional[str] = Query(None),
    is_active: bool = Query(True),
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.ONBOARDING_READ))
):
    """Get workflow templates"""
    from ..db.models.onboarding import WorkflowTemplate
    
    query = db.query(WorkflowTemplate)
    
    # Apply organization filter
    if current_user.organization_id:
        query = query.filter(WorkflowTemplate.organization_id == current_user.organization_id)
    
    # Apply filters
    if workflow_type:
        query = query.filter(WorkflowTemplate.workflow_type == workflow_type)
    
    if is_active is not None:
        query = query.filter(WorkflowTemplate.is_active == is_active)
    
    templates = query.all()
    return templates


# Onboarding Workflow Endpoints
@router.post("/onboarding", response_model=OnboardingWorkflowResponse)
async def create_onboarding_workflow(
    workflow_data: OnboardingWorkflowCreate,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.ONBOARDING_CREATE))
):
    """Create an onboarding workflow"""
    return await onboarding_service.create_onboarding_workflow(db, workflow_data, current_user)


@router.get("/onboarding", response_model=OnboardingWorkflowListResponse)
async def get_onboarding_workflows(
    skip: int = Query(0, ge=0),
    limit: int = Query(20, ge=1, le=100),
    status: Optional[WorkflowStatus] = Query(None),
    employee_id: Optional[UUID] = Query(None),
    assigned_to_id: Optional[UUID] = Query(None),
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.ONBOARDING_READ))
):
    """Get onboarding workflows with filtering and pagination"""
    workflows, total = await onboarding_service.get_onboarding_workflows(
        db=db,
        skip=skip,
        limit=limit,
        status=status,
        employee_id=employee_id,
        assigned_to_id=assigned_to_id,
        current_user=current_user
    )
    
    return OnboardingWorkflowListResponse(
        items=workflows,
        total=total,
        page=skip // limit + 1,
        size=limit,
        pages=(total + limit - 1) // limit
    )


@router.get("/onboarding/{workflow_id}", response_model=OnboardingWorkflowResponse)
async def get_onboarding_workflow(
    workflow_id: UUID,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.ONBOARDING_READ))
):
    """Get a specific onboarding workflow"""
    return await onboarding_service.get_onboarding_workflow(db, workflow_id)


@router.put("/onboarding/{workflow_id}", response_model=OnboardingWorkflowResponse)
async def update_onboarding_workflow(
    workflow_id: UUID,
    workflow_data: OnboardingWorkflowUpdate,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.ONBOARDING_UPDATE))
):
    """Update an onboarding workflow"""
    return await onboarding_service.update_onboarding_workflow(db, workflow_id, workflow_data, current_user)


@router.post("/onboarding/{workflow_id}/complete", response_model=OnboardingWorkflowResponse)
async def complete_onboarding_workflow(
    workflow_id: UUID,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.ONBOARDING_UPDATE))
):
    """Complete an onboarding workflow and send completion emails"""
    return await onboarding_service.complete_onboarding_workflow(db, workflow_id, current_user)


# Offboarding Workflow Endpoints
@router.post("/offboarding", response_model=OffboardingWorkflowResponse)
async def create_offboarding_workflow(
    workflow_data: OffboardingWorkflowCreate,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.ONBOARDING_CREATE))
):
    """Create an offboarding workflow"""
    return await onboarding_service.create_offboarding_workflow(db, workflow_data, current_user)


@router.get("/offboarding", response_model=OffboardingWorkflowListResponse)
async def get_offboarding_workflows(
    skip: int = Query(0, ge=0),
    limit: int = Query(20, ge=1, le=100),
    status: Optional[WorkflowStatus] = Query(None),
    employee_id: Optional[UUID] = Query(None),
    assigned_to_id: Optional[UUID] = Query(None),
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.ONBOARDING_READ))
):
    """Get offboarding workflows with filtering and pagination"""
    from ..db.models.onboarding import OffboardingWorkflow
    
    query = db.query(OffboardingWorkflow)
    
    # Apply organization filter
    if current_user.organization_id:
        query = query.filter(OffboardingWorkflow.organization_id == current_user.organization_id)
    
    # Apply filters
    if status:
        query = query.filter(OffboardingWorkflow.status == status)
    
    if employee_id:
        query = query.filter(OffboardingWorkflow.employee_id == employee_id)
    
    if assigned_to_id:
        query = query.filter(OffboardingWorkflow.assigned_to_id == assigned_to_id)
    
    # Get total count
    total = query.count()
    
    # Apply pagination
    workflows = query.offset(skip).limit(limit).all()
    
    return OffboardingWorkflowListResponse(
        items=workflows,
        total=total,
        page=skip // limit + 1,
        size=limit,
        pages=(total + limit - 1) // limit
    )


# Task Endpoints
@router.post("/onboarding/{workflow_id}/tasks", response_model=OnboardingTaskResponse)
async def create_onboarding_task(
    workflow_id: UUID,
    task_data: OnboardingTaskCreate,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.ONBOARDING_CREATE))
):
    """Create an onboarding task"""
    # Ensure the workflow_id in the URL matches the one in the data
    task_data.workflow_id = workflow_id
    return await onboarding_service.create_onboarding_task(db, task_data, current_user)


@router.get("/onboarding/{workflow_id}/tasks", response_model=List[OnboardingTaskResponse])
async def get_onboarding_tasks(
    workflow_id: UUID,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.ONBOARDING_READ))
):
    """Get tasks for an onboarding workflow"""
    from ..db.models.onboarding import OnboardingTask
    
    tasks = db.query(OnboardingTask).filter(
        OnboardingTask.workflow_id == workflow_id
    ).order_by(OnboardingTask.order_index).all()
    
    return tasks


@router.put("/tasks/{task_id}/status")
async def update_task_status(
    task_id: UUID,
    status: TaskStatus,
    completion_notes: Optional[str] = None,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.ONBOARDING_UPDATE))
):
    """Update task status"""
    return await onboarding_service.update_task_status(
        db, task_id, status, current_user, completion_notes
    )


# Document Endpoints
@router.post("/workflows/{workflow_id}/documents", response_model=DocumentResponse)
async def create_document(
    workflow_id: UUID,
    document_data: DocumentCreate,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.ONBOARDING_CREATE))
):
    """Create a document for a workflow"""
    from ..db.models.onboarding import OnboardingDocument
    
    # Ensure the workflow_id in the URL matches the one in the data
    document_data.workflow_id = workflow_id
    
    document = OnboardingDocument(
        **document_data.dict(),
        organization_id=current_user.organization_id,
        status=DocumentStatus.PENDING
    )
    
    db.add(document)
    db.commit()
    db.refresh(document)
    
    return document


@router.get("/workflows/{workflow_id}/documents", response_model=List[DocumentResponse])
async def get_workflow_documents(
    workflow_id: UUID,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.ONBOARDING_READ))
):
    """Get documents for a workflow"""
    from ..db.models.onboarding import OnboardingDocument, OffboardingDocument
    
    # Try onboarding documents first
    documents = db.query(OnboardingDocument).filter(
        OnboardingDocument.workflow_id == workflow_id
    ).all()
    
    # If no onboarding documents, try offboarding documents
    if not documents:
        documents = db.query(OffboardingDocument).filter(
            OffboardingDocument.workflow_id == workflow_id
        ).all()
    
    return documents


@router.put("/documents/{document_id}", response_model=DocumentResponse)
async def update_document(
    document_id: UUID,
    document_data: DocumentUpdate,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.ONBOARDING_UPDATE))
):
    """Update a document"""
    from ..db.models.onboarding import OnboardingDocument, OffboardingDocument
    
    # Try to find the document in onboarding documents
    document = db.query(OnboardingDocument).filter(
        OnboardingDocument.id == document_id
    ).first()
    
    # If not found, try offboarding documents
    if not document:
        document = db.query(OffboardingDocument).filter(
            OffboardingDocument.id == document_id
        ).first()
    
    if not document:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Document not found"
        )
    
    # Update fields
    update_data = document_data.dict(exclude_unset=True)
    for field, value in update_data.items():
        setattr(document, field, value)
    
    # Update review information
    if document_data.status in [DocumentStatus.APPROVED, DocumentStatus.REJECTED]:
        document.reviewed_by_id = current_user.user_id
        document.reviewed_date = datetime.utcnow()
    
    db.commit()
    db.refresh(document)
    
    return document


@router.post("/documents/{document_id}/submit")
async def submit_document(
    document_id: UUID,
    file_url: str,
    file_name: str,
    file_size: Optional[int] = None,
    file_type: Optional[str] = None,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.ONBOARDING_UPDATE))
):
    """Submit a document"""
    from ..db.models.onboarding import OnboardingDocument, OffboardingDocument
    
    # Try to find the document
    document = db.query(OnboardingDocument).filter(
        OnboardingDocument.id == document_id
    ).first()
    
    if not document:
        document = db.query(OffboardingDocument).filter(
            OffboardingDocument.id == document_id
        ).first()
    
    if not document:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Document not found"
        )
    
    # Update document with file information
    document.file_url = file_url
    document.file_name = file_name
    document.file_size = file_size
    document.file_type = file_type
    document.status = DocumentStatus.SUBMITTED
    document.submitted_date = datetime.utcnow()
    
    db.commit()
    db.refresh(document)
    
    return {"message": "Document submitted successfully"}
