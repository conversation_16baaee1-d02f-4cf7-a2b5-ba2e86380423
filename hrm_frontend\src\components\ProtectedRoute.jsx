/**
 * Protected Route Components for RBAC System
 * Provides route-level access control based on user roles and permissions
 * 
 * Components:
 * - ProtectedRoute: Main route protection wrapper
 * - PermissionGate: Component-level permission checking
 * - RoleGate: Role-based access control wrapper
 * - ConditionalRender: Conditional rendering based on permissions
 */

import React from 'react';
import { useAuth } from '../contexts/AuthContext';
import { usePermissions, usePermission, useRoleAccess } from '../hooks/usePermissions';

/**
 * Main protected route component
 * Wraps content and only renders if user has required permissions
 * 
 * @param {Object} props
 * @param {React.ReactNode} props.children - Content to protect
 * @param {string} props.permission - Required permission
 * @param {string|string[]} props.roles - Required role(s)
 * @param {Object} props.context - Additional context for permission checking
 * @param {React.ReactNode} props.fallback - Component to render when access denied
 * @param {boolean} props.redirectToDashboard - Whether to redirect to dashboard on access denied
 */
export function ProtectedRoute({ 
  children, 
  permission, 
  roles, 
  context = {}, 
  fallback = null,
  redirectToDashboard = false 
}) {
  const { isAuthenticated, loading } = useAuth();
  const permissions = usePermissions();
  const hasRoleAccess = useRoleAccess(roles);

  // Show loading state while authentication is being checked
  if (loading) {
    return <LoadingSpinner />;
  }

  // Redirect to login if not authenticated
  if (!isAuthenticated) {
    return <LoginRequired />;
  }

  // Check role-based access if roles are specified
  if (roles && !hasRoleAccess) {
    return fallback || <AccessDenied redirectToDashboard={redirectToDashboard} />;
  }

  // Check permission-based access if permission is specified
  if (permission) {
    const canAccess = permissions.canAccessResource(permission, context);
    if (!canAccess) {
      return fallback || <AccessDenied redirectToDashboard={redirectToDashboard} />;
    }
  }

  return children;
}

/**
 * Permission gate component for fine-grained access control
 * Can be used to wrap any component or content that needs permission checking
 * 
 * @param {Object} props
 * @param {React.ReactNode} props.children - Content to protect
 * @param {string} props.permission - Required permission
 * @param {Object} props.context - Additional context for permission checking
 * @param {React.ReactNode} props.fallback - Component to render when access denied
 * @param {boolean} props.hideOnDenied - Whether to hide content completely when access denied
 */
export function PermissionGate({ 
  children, 
  permission, 
  context = {}, 
  fallback = null,
  hideOnDenied = false 
}) {
  const { hasAccess } = usePermission(permission, context);

  if (!hasAccess) {
    if (hideOnDenied) {
      return null;
    }
    return fallback || <div className="text-gray-500 text-sm">Access restricted</div>;
  }

  return children;
}

/**
 * Role gate component for role-based access control
 * 
 * @param {Object} props
 * @param {React.ReactNode} props.children - Content to protect
 * @param {string|string[]} props.roles - Required role(s)
 * @param {React.ReactNode} props.fallback - Component to render when access denied
 * @param {boolean} props.hideOnDenied - Whether to hide content completely when access denied
 */
export function RoleGate({ 
  children, 
  roles, 
  fallback = null,
  hideOnDenied = false 
}) {
  const hasAccess = useRoleAccess(roles);

  if (!hasAccess) {
    if (hideOnDenied) {
      return null;
    }
    return fallback || <div className="text-gray-500 text-sm">Access restricted</div>;
  }

  return children;
}

/**
 * Conditional render component based on permissions
 * Useful for showing different content based on permission levels
 * 
 * @param {Object} props
 * @param {string} props.permission - Permission to check
 * @param {Object} props.context - Additional context for permission checking
 * @param {React.ReactNode} props.fullAccess - Content for full access
 * @param {React.ReactNode} props.teamAccess - Content for team-only access
 * @param {React.ReactNode} props.assignedAccess - Content for assigned-only access
 * @param {React.ReactNode} props.selfAccess - Content for self-only access
 * @param {React.ReactNode} props.noAccess - Content when no access
 */
export function ConditionalRender({ 
  permission, 
  context = {},
  fullAccess = null,
  teamAccess = null,
  assignedAccess = null,
  selfAccess = null,
  noAccess = null 
}) {
  const { permissionType, hasAccess } = usePermission(permission, context);

  if (!hasAccess) {
    return noAccess;
  }

  switch (permissionType) {
    case 'full':
      return fullAccess || teamAccess || assignedAccess || selfAccess;
    case 'team_only':
      return teamAccess || assignedAccess || selfAccess;
    case 'assigned_only':
      return assignedAccess || selfAccess;
    case 'self_only':
      return selfAccess;
    default:
      return noAccess;
  }
}

/**
 * Loading spinner component
 */
function LoadingSpinner() {
  return (
    <div className="flex items-center justify-center min-h-screen bg-slate-100">
      <div className="flex flex-col items-center space-y-4">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
        <p className="text-gray-600">Loading...</p>
      </div>
    </div>
  );
}

/**
 * Login required component
 */
function LoginRequired() {
  return (
    <div className="flex items-center justify-center min-h-screen bg-slate-100">
      <div className="bg-white rounded-lg shadow-lg p-8 max-w-md w-full mx-4">
        <div className="text-center">
          <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <svg className="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
            </svg>
          </div>
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Authentication Required</h2>
          <p className="text-gray-600 mb-6">Please log in to access this page.</p>
          <LoginForm />
        </div>
      </div>
    </div>
  );
}

/**
 * Access denied component
 */
function AccessDenied({ redirectToDashboard = false }) {
  const handleRedirect = () => {
    if (redirectToDashboard) {
      window.location.href = '/dashboard';
    }
  };

  return (
    <div className="flex items-center justify-center min-h-screen bg-slate-100">
      <div className="bg-white rounded-lg shadow-lg p-8 max-w-md w-full mx-4">
        <div className="text-center">
          <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <svg className="w-8 h-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M18.364 18.364A9 9 0 005.636 5.636m12.728 12.728L5.636 5.636m12.728 12.728L18.364 5.636M5.636 18.364l12.728-12.728" />
            </svg>
          </div>
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Access Denied</h2>
          <p className="text-gray-600 mb-6">You don't have permission to access this resource.</p>
          {redirectToDashboard && (
            <button
              onClick={handleRedirect}
              className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors"
            >
              Go to Dashboard
            </button>
          )}
        </div>
      </div>
    </div>
  );
}

/**
 * Simple login form component
 */
function LoginForm() {
  const { login, loading, error } = useAuth();
  const [credentials, setCredentials] = React.useState({
    email: '',
    password: ''
  });

  const handleSubmit = async (e) => {
    e.preventDefault();
    await login(credentials);
  };

  const handleChange = (e) => {
    setCredentials({
      ...credentials,
      [e.target.name]: e.target.value
    });
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      {error && (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded">
          {error}
        </div>
      )}
      
      <div>
        <input
          type="email"
          name="email"
          placeholder="Email"
          value={credentials.email}
          onChange={handleChange}
          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          required
        />
      </div>
      
      <div>
        <input
          type="password"
          name="password"
          placeholder="Password"
          value={credentials.password}
          onChange={handleChange}
          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          required
        />
      </div>
      
      <button
        type="submit"
        disabled={loading}
        className="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
      >
        {loading ? 'Signing in...' : 'Sign In'}
      </button>
      
      <div className="text-sm text-gray-600 mt-4">
        <p>Demo accounts:</p>
        <p><EMAIL> / password123</p>
        <p><EMAIL> / password123</p>
        <p><EMAIL> / password123</p>
        <p><EMAIL> / password123</p>
        <p><EMAIL> / password123</p>
      </div>
    </form>
  );
}
