from core.models.approval_settings import ApprovalSettingsModel
from core.models.approvals import ApprovalModel
from core.models.approval_history import ApprovalHistoryModel
from core.utils.email import send_smtp_email


class ApprovalFlowHandler:
    def __init__(self, name, user_id, pay_schedule_id, db_session):
        # print(f"\n=== [ApprovalFlowHandler INIT] Triggered for PaySchedule: {name} ===")
        self.name = name
        self.user_id = user_id
        self.pay_schedule_id = pay_schedule_id
        self.db = db_session

        self.start_approval_flow()

    def start_approval_flow(self):
        # print(f"\n[Flow Start] Looking up the latest ApprovalSettings for user_id={self.user_id}")
        settings = (
            self.db.query(ApprovalSettingsModel)
            .filter_by(user_id=self.user_id)
            .order_by(ApprovalSettingsModel.timestamp.desc())
            .first()
        )

        if not settings:
            # print("❌ No approval settings found.")
            return
        # print(f"✅ Found approval settings: status={settings.status}, number_of_approval={settings.number_of_approval}")

        if not settings.status or settings.number_of_approval <= 0:
            # print("⏹️ Approval flow inactive or no approvers configured.")
            return

        # print("\n[Query] Fetching approvers from ApprovalModel...")
        approvers = (
            self.db.query(ApprovalModel)
            .filter_by(user_id=self.user_id)
            .order_by(ApprovalModel.level)
            .limit(settings.number_of_approval)
            .all()
        )

        # print(f"🧑‍💼 Found {len(approvers)} approvers:")
        for approver in approvers:
            # print(f"  - Level {approver.level}, Employee ID: {approver.employee_id}, Email: {approver.email}")
            pass

        # print("\n[Update] Attaching pay_schedule_id and name to each approver...")
        for approver in approvers:
            # print(f"  ↳ Updating approver (ID: {approver.id}) with PaySchedule ID: {self.pay_schedule_id} and name: {self.name}")
            approver.pay_schedules_id = self.pay_schedule_id
            approver.name = self.name
            self.db.add(approver)

        self.db.commit()
        # print("✅ Approvers updated and committed.")

        # print("\n[Search] Looking for first level approver (level == 1)...")
        first = next((a for a in approvers if int(a.level) == 1), None)

        if first and first.email:
            # print(f"📧 Sending approval request to first approver (Email: {first.email})")

            send_smtp_email(
                to=first.email,
                subject="Pay-Schedle Approval Request",
                body=f"You have a new approval task for pay schedule: {self.name}, Kindly login to you employee account and approve so pay roll for the month can be processed, Thank you."
            )

            # print("📝 Logging approval request to ApprovalHistoryModel...")
            history = ApprovalHistoryModel(
                reason="Initial approval request",
                status="pending",
                approval_id=first.id,
                pay_schedules_id=self.pay_schedule_id,
                employee_id=first.employee_id
            )
            self.db.add(history)
            self.db.commit()
            # print("✅ Approval history logged.")

