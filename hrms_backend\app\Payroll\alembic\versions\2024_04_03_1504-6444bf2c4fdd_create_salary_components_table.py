"""create salary_components table

Revision ID: 6444bf2c4fdd
Revises: 76231ea93797
Create Date: 2024-04-03 15:04:16.779448

"""
from typing import Sequence, Union

from alembic import op
from sqlalchemy import func, TIMESTAMP, ForeignKey
from sqlalchemy import Column, Integer, String, Float

# revision identifiers, used by Alembic.
revision: str = '6444bf2c4fdd'
down_revision: Union[str, None] = '76231ea93797'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    op.create_table(
        'salary_components',
        Column('id', Integer, primary_key=True),
        Column('component_name', String(100), nullable=False),
        <PERSON>umn('payslip_name', String(100), nullable=False),
        Column('component_type',String(100), nullable=False),
        Column('calculation_type', String(100), nullable=False),
        Column('cycle', String(45), nullable=True),
        Column('duration', String(45), nullable=True),
        Column('amount', Float, nullable=True),
        Column('value', Float, nullable=True),
        Column('user_id', Integer, ForeignKey("users.id")),
        Column("timestamp", TIMESTAMP, server_default=func.now()),
    )


def downgrade() -> None:
     op.drop_table("salary_components")
