"""Create announcements table

Revision ID: 06ceeda520ed
Revises: 219f6e68a90a
Create Date: 2024-06-10 09:20:03.748774

"""
from typing import Sequence, Union

from alembic import op
from sqlalchemy import func, TIMESTAMP, ForeignKey, DateTime
from sqlalchemy import Column, Integer, String, Text

# revision identifiers, used by Alembic.
revision: str = '06ceeda520ed'
down_revision: Union[str, None] = '219f6e68a90a'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    op.create_table(
        'announcements',
        Column('id', Integer, primary_key=True),
        <PERSON>umn('title', String(45), nullable=False),
        <PERSON>umn("timestamp", TIMESTAMP, server_default=func.now()),
        Column('sent_to', String(45), nullable=False),
        <PERSON>umn('description', Text, nullable=False),
        <PERSON>umn('status', String(45), nullable=False),
        <PERSON>umn("date", DateTime, nullable=True, server_default=func.now()),
        <PERSON>umn('user_id', Integer, ForeignKey("users.id")),
    )


def downgrade() -> None:
    op.drop_table("announcements")
