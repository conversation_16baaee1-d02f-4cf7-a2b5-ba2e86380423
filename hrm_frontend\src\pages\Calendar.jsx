/**
 * Enhanced Calendar Page with FullCalendar Integration and RBAC
 * Displays attendance events and calendar functionality with role-based access control
 */

import React, { useState } from 'react';
import FullCalendar from '@fullcalendar/react';
import dayGridPlugin from '@fullcalendar/daygrid';
import interactionPlugin from '@fullcalendar/interaction';
import { Calendar as CalendarIcon, Plus, Filter, Download, Users, Clock } from 'lucide-react';
import { usePermissions } from '../hooks/usePermissions';
import { PermissionGate, ConditionalRender } from '../components/ProtectedRoute';
import '../styles/calendar.css';

export default function Calendar() {
  const permissions = usePermissions();
  const [selectedDate, setSelectedDate] = useState(null);
  const [showEventModal, setShowEventModal] = useState(false);
  const [viewType, setViewType] = useState('attendance'); // 'attendance', 'events', 'all'

  // Attendance events data
  const attendanceEvents = [
    {
      id: 'att-1',
      title: 'Absent',
      date: '2025-06-02',
      display: 'block',
      backgroundColor: '#ef4444',
      borderColor: '#ef4444',
      textColor: 'white',
      extendedProps: {
        type: 'attendance',
        status: 'absent'
      }
    },
    {
      id: 'att-2',
      title: 'Absent',
      date: '2025-06-03',
      display: 'block',
      backgroundColor: '#ef4444',
      borderColor: '#ef4444',
      textColor: 'white',
      extendedProps: {
        type: 'attendance',
        status: 'absent'
      }
    },
    {
      id: 'att-3',
      title: 'Present\n10:19 Hrs',
      date: '2025-06-04',
      display: 'block',
      backgroundColor: '#22c55e',
      borderColor: '#22c55e',
      textColor: 'white',
      extendedProps: {
        type: 'attendance',
        status: 'present',
        hours: '10:19'
      }
    },
    {
      id: 'att-4',
      title: 'Present\n9:45 Hrs',
      date: '2025-06-05',
      display: 'block',
      backgroundColor: '#22c55e',
      borderColor: '#22c55e',
      textColor: 'white',
      extendedProps: {
        type: 'attendance',
        status: 'present',
        hours: '9:45'
      }
    },
    {
      id: 'att-5',
      title: 'Late\n8:30 Hrs',
      date: '2025-06-06',
      display: 'block',
      backgroundColor: '#f59e0b',
      borderColor: '#f59e0b',
      textColor: 'white',
      extendedProps: {
        type: 'attendance',
        status: 'late',
        hours: '8:30'
      }
    }
  ];

  // Regular calendar events
  const calendarEvents = [
    {
      id: 'evt-1',
      title: 'Team Meeting',
      date: '2025-06-10',
      backgroundColor: '#3b82f6',
      borderColor: '#3b82f6',
      extendedProps: {
        type: 'meeting'
      }
    },
    {
      id: 'evt-2',
      title: 'Project Deadline',
      date: '2025-06-15',
      backgroundColor: '#8b5cf6',
      borderColor: '#8b5cf6',
      extendedProps: {
        type: 'deadline'
      }
    }
  ];

  // Filter events based on view type
  const getFilteredEvents = () => {
    switch (viewType) {
      case 'attendance':
        return attendanceEvents;
      case 'events':
        return calendarEvents;
      case 'all':
      default:
        return [...attendanceEvents, ...calendarEvents];
    }
  };

  const handleDateClick = (arg) => {
    setSelectedDate(arg.date);
    setShowEventModal(true);
  };

  const handleEventClick = (clickInfo) => {
    const event = clickInfo.event;
    alert(`Event: ${event.title}\nDate: ${event.start.toDateString()}`);
  };

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 flex items-center gap-2">
            <CalendarIcon size={28} className="agno-text-orange" />
            AgnoConnect Calendar
          </h1>
          <p className="text-gray-600">View attendance, events, and schedule</p>
        </div>

        <div className="flex items-center gap-4">
          {/* View Type Filter */}
          <div className="flex bg-gray-100 rounded-lg p-1">
            <button
              onClick={() => setViewType('all')}
              className={`px-3 py-1 rounded text-sm font-medium transition-colors ${
                viewType === 'all' ? 'bg-white agno-text-orange shadow-sm' : 'text-gray-600'
              }`}
            >
              All
            </button>
            <button
              onClick={() => setViewType('attendance')}
              className={`px-3 py-1 rounded text-sm font-medium transition-colors ${
                viewType === 'attendance' ? 'bg-white agno-text-orange shadow-sm' : 'text-gray-600'
              }`}
            >
              Attendance
            </button>
            <button
              onClick={() => setViewType('events')}
              className={`px-3 py-1 rounded text-sm font-medium transition-colors ${
                viewType === 'events' ? 'bg-white agno-text-orange shadow-sm' : 'text-gray-600'
              }`}
            >
              Events
            </button>
          </div>

          <PermissionGate permission="calendarEventManagement">
            <button className="agno-bg-orange text-white px-4 py-2 rounded-lg hover:bg-accent-600 flex items-center gap-2">
              <Plus size={16} />
              Add Event
            </button>
          </PermissionGate>

          <button className="bg-gray-100 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-200 flex items-center gap-2">
            <Download size={16} />
            Export
          </button>
        </div>
      </div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <StatCard
          title="Present Days"
          value="18"
          subtitle="This month"
          icon={<Clock className="text-green-500" size={24} />}
          color="green"
        />
        <StatCard
          title="Absent Days"
          value="2"
          subtitle="This month"
          icon={<Clock className="text-red-500" size={24} />}
          color="red"
        />
        <StatCard
          title="Late Days"
          value="3"
          subtitle="This month"
          icon={<Clock className="text-yellow-500" size={24} />}
          color="yellow"
        />
        <StatCard
          title="Total Events"
          value="12"
          subtitle="This month"
          icon={<CalendarIcon className="text-blue-500" size={24} />}
          color="blue"
        />
      </div>

      {/* Calendar Component */}
      <div className="bg-white rounded-lg shadow-lg p-6">
        <FullCalendar
          plugins={[dayGridPlugin, interactionPlugin]}
          initialView="dayGridMonth"
          headerToolbar={{
            left: 'title',
            center: '',
            right: 'prev,next today',
          }}
          events={getFilteredEvents()}
          dateClick={handleDateClick}
          eventClick={handleEventClick}
          fixedWeekCount={false}
          dayMaxEventRows={3}
          height="auto"
          eventDisplay="block"
          displayEventTime={false}
          eventClassNames={(arg) => {
            const type = arg.event.extendedProps.type;
            return [`event-${type}`];
          }}
        />
      </div>

      {/* Legend */}
      <div className="bg-white rounded-lg shadow p-4">
        <h3 className="text-lg font-semibold mb-4">Legend</h3>
        <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
          <LegendItem color="bg-green-500" label="Present" />
          <LegendItem color="bg-red-500" label="Absent" />
          <LegendItem color="bg-yellow-500" label="Late" />
          <LegendItem color="bg-blue-500" label="Meeting" />
          <LegendItem color="bg-purple-500" label="Deadline" />
        </div>
      </div>

      {/* Event Modal */}
      {showEventModal && (
        <EventModal
          date={selectedDate}
          onClose={() => setShowEventModal(false)}
          permissions={permissions}
        />
      )}

      {/* Custom CSS for calendar styling */}
      <style>{`
        .fc-event.event-attendance {
          border-radius: 4px !important;
          font-size: 12px !important;
          padding: 2px 4px !important;
        }
        .fc-event.event-meeting {
          border-radius: 4px !important;
        }
        .fc-event.event-deadline {
          border-radius: 4px !important;
        }
        .fc-daygrid-event {
          margin: 1px 0 !important;
        }
        .fc-event-title {
          white-space: pre-line !important;
        }
      `}</style>
    </div>
  );
}

// Stat card component
function StatCard({ title, value, subtitle, icon, color }) {
  const colorClasses = {
    green: 'border-green-200 bg-green-50',
    yellow: 'border-yellow-200 bg-yellow-50',
    red: 'border-red-200 bg-red-50',
    blue: 'border-blue-200 bg-blue-50'
  };

  return (
    <div className={`bg-white rounded-lg border-2 ${colorClasses[color]} p-6`}>
      <div className="flex items-center justify-between">
        <div>
          <p className="text-sm font-medium text-gray-600">{title}</p>
          <p className="text-2xl font-bold text-gray-900">{value}</p>
          <p className="text-sm text-gray-500">{subtitle}</p>
        </div>
        <div className="flex-shrink-0">
          {icon}
        </div>
      </div>
    </div>
  );
}

// Legend item component
function LegendItem({ color, label }) {
  return (
    <div className="flex items-center gap-2">
      <div className={`w-4 h-4 rounded ${color}`}></div>
      <span className="text-sm text-gray-700">{label}</span>
    </div>
  );
}

// Event modal component
function EventModal({ date, onClose, permissions }) {
  const [eventTitle, setEventTitle] = useState('');
  const [eventType, setEventType] = useState('meeting');

  const handleSubmit = (e) => {
    e.preventDefault();
    // Handle event creation logic here
    console.log('Creating event:', { title: eventTitle, date, type: eventType });
    onClose();
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-md">
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-lg font-semibold">Add Event</h3>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            ×
          </button>
        </div>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Date
            </label>
            <input
              type="text"
              value={date ? date.toDateString() : ''}
              disabled
              className="w-full px-3 py-2 border border-gray-300 rounded-lg bg-gray-50"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Event Title
            </label>
            <input
              type="text"
              value={eventTitle}
              onChange={(e) => setEventTitle(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
              placeholder="Enter event title"
              required
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Event Type
            </label>
            <select
              value={eventType}
              onChange={(e) => setEventType(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
            >
              <option value="meeting">Meeting</option>
              <option value="deadline">Deadline</option>
              <option value="holiday">Holiday</option>
              <option value="training">Training</option>
            </select>
          </div>

          <div className="flex gap-3 pt-4">
            <button
              type="button"
              onClick={onClose}
              className="flex-1 px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50"
            >
              Cancel
            </button>
            <button
              type="submit"
              className="flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
            >
              Add Event
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}
