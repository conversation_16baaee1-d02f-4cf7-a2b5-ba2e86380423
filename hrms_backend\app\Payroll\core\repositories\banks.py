from core.models.bank import BankModel
from core.databases.database import db
from core.repositories.user import UserRepository

class BanksRepository:
    def __init__(self) -> None:
        pass

    @classmethod
    def createBanks(self, name, sort_code):
        banks = BankModel(
            name=name,
            sort_code=sort_code,
            user_id=UserRepository.authUserId()
        )
        db.session.add(banks)
        db.session.commit()
        return banks

    @classmethod
    def fetchAll(self):
        return BankModel.query.filter_by(user_id=UserRepository().authUserId()).order_by(BankModel.timestamp.desc()).all()
    
    @classmethod
    def getBanks(self, id):
        return BankModel.query.filter(BankModel.id == id).first()
    
    @classmethod
    def getBanksByKeys(self, kwargs):
        return BankModel.query.filter_by(user_id=UserRepository().authUserId(), **kwargs).all()

    @classmethod
    def updateBanks(self, id, **kwargs):
        banks = BankModel.query.filter_by(id=id).first()
        if banks:
            for key, value in kwargs.items():
                setattr(banks, key, value)
            db.session.commit()
            return banks
        else:
            return None

    @classmethod
    def deleteBanks(self, id):
        BankModel.query.filter(BankModel.id == id).delete()
        db.session.commit()
        return
        