from core.repositories.organisation import OrganisationRepository
import datetime

class OrganisationService:
    def __init__(self) -> None:
        self.repository = OrganisationRepository()

    def createOrganisation(self, Kwargs):
        return self.repository.createOrganisation(**Kwargs)
    
    def getOrganisation(self, slug):
        return self.repository.getOrganisationBySlug(slug)
    
    def getOrganisationById(self, id):
        return self.repository.getOrganisationById(id)
    
    def getAllOrganisation(self):
        organisaations = self.repository.fetchAllOrganisation()
        total_organisation = len(organisaations)
        return [
            {
                "id": organisation.id,
                "organisation_name": organisation.organisation_name,
                "email": organisation.email,
                "address": organisation.address,
                "country": organisation.country,
                "city": organisation.city,
                "logoUrl": organisation.logoUrl,
                "industry": organisation.industry,
                "phone": organisation.phone,
                "slug": organisation.slug,
                "state": organisation.state,
                "type": organisation.type,
                "zipcode": organisation.zipcode,
                "timestamp": organisation.timestamp.strftime("%Y-%m-%d"),
                "employee_count": employee_count
            }
            for organisation, employee_count in organisaations
        ], total_organisation
    
    def updateOrganisation(self, id, data_org, logo_file=None):
        return self.repository.updateOrganisation(id, logo_file=logo_file, **data_org)
    
    def getOrganisationByKey(self, Kwarg):
        return self.repository.getOrganisationByKeys(Kwarg)
    
    def deleteOrganisation(self, id):
        return self.repository.deleteOrganisation(id)
    
    def slug(self, Kwarg):
        return self.generate_slug(Kwarg)

    def generate_slug(self, Kwarg):
        slug = Kwarg.lower()
        return slug[:3] + datetime.datetime.now().strftime('%Y%m%d%H%M%S')