/**
 * Top Banner Component for HRM Dashboard
 * Displays welcome message and quick stats with RBAC integration
 */

import React from 'react';
import { Clock, Users, Calendar, TrendingUp } from 'lucide-react';
import { usePermissions } from '../hooks/usePermissions';
import { PermissionGate } from './ProtectedRoute';

export default function TopBanner({ user = { name: 'User', role: 'Employee' } }) {
  const permissions = usePermissions();
  
  const getCurrentGreeting = () => {
    const hour = new Date().getHours();
    if (hour < 12) return 'Good Morning';
    if (hour < 17) return 'Good Afternoon';
    return 'Good Evening';
  };

  const getCurrentDate = () => {
    return new Date().toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  return (
    <div className="relative agno-gradient text-white rounded-lg shadow-agno-lg overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-10">
        <div className="absolute inset-0" style={{
          backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`
        }}></div>
      </div>

      <div className="relative p-6">
        <div className="flex justify-between items-start">
          {/* Welcome Section */}
          <div className="flex-1">
            <h1 className="text-2xl font-bold mb-2">
              {getCurrentGreeting()}, {user.name}!
            </h1>
            <p className="text-white/80 mb-4">
              {getCurrentDate()}
            </p>
            <p className="text-white/70 text-sm">
              Welcome to your AgnoConnect HRM Dashboard. Have a productive day!
            </p>
          </div>

          {/* Quick Stats */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 ml-6">
            <PermissionGate permission="attendanceTrackerSelf">
              <QuickStat
                icon={<Clock size={20} />}
                label="Today"
                value="8:30"
                subtitle="Hours"
              />
            </PermissionGate>
            
            <PermissionGate permission="employeeDirectory">
              <QuickStat
                icon={<Users size={20} />}
                label="Team"
                value="24"
                subtitle="Members"
              />
            </PermissionGate>
            
            <PermissionGate permission="calendarEvents">
              <QuickStat
                icon={<Calendar size={20} />}
                label="Events"
                value="3"
                subtitle="Today"
              />
            </PermissionGate>
            
            <PermissionGate permission="performanceReviewsSelf">
              <QuickStat
                icon={<TrendingUp size={20} />}
                label="Goals"
                value="85%"
                subtitle="Complete"
              />
            </PermissionGate>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex gap-3 mt-6">
          <PermissionGate permission="timeTracker">
            <ActionButton
              label="Quick Check-in"
              variant="primary"
            />
          </PermissionGate>
          
          <PermissionGate permission="leaveRequestsSelf">
            <ActionButton
              label="Request Leave"
              variant="secondary"
            />
          </PermissionGate>
          
          <PermissionGate permission="calendarEventManagement">
            <ActionButton
              label="Schedule Meeting"
              variant="secondary"
            />
          </PermissionGate>
        </div>
      </div>
    </div>
  );
}

// Quick Stat Component
function QuickStat({ icon, label, value, subtitle }) {
  return (
    <div className="bg-white bg-opacity-10 rounded-lg p-3 text-center backdrop-blur-sm">
      <div className="flex justify-center mb-2 text-white/80">
        {icon}
      </div>
      <div className="text-lg font-bold">{value}</div>
      <div className="text-xs text-white/70">{label}</div>
      <div className="text-xs text-white/60">{subtitle}</div>
    </div>
  );
}

// Action Button Component
function ActionButton({ label, variant = 'secondary', onClick }) {
  const baseClasses = "px-4 py-2 rounded-lg font-medium transition-colors text-sm";
  const variantClasses = {
    primary: "agno-bg-orange text-white hover:bg-accent-600",
    secondary: "bg-white bg-opacity-20 text-white hover:bg-opacity-30 border border-white/30"
  };

  return (
    <button
      onClick={onClick}
      className={`${baseClasses} ${variantClasses[variant]}`}
    >
      {label}
    </button>
  );
}

// Compact version for smaller spaces
export function CompactTopBanner({ user = { name: 'User' } }) {
  const getCurrentGreeting = () => {
    const hour = new Date().getHours();
    if (hour < 12) return 'Good Morning';
    if (hour < 17) return 'Good Afternoon';
    return 'Good Evening';
  };

  return (
    <div className="agno-gradient text-white p-4 rounded-lg">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-lg font-semibold">
            {getCurrentGreeting()}, {user.name}!
          </h2>
          <p className="text-white/70 text-sm">
            {new Date().toLocaleDateString('en-US', { 
              weekday: 'long', 
              month: 'short', 
              day: 'numeric' 
            })}
          </p>
        </div>
        
        <div className="flex gap-2">
          <PermissionGate permission="timeTracker">
            <ActionButton
              label="Check-in"
              variant="primary"
            />
          </PermissionGate>
        </div>
      </div>
    </div>
  );
}
