#!/usr/bin/env python3
"""
Check complete leave_policies table schema
"""

import sys
import os

# Add the app directory to Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'app'))

from sqlalchemy import text
from app.db.session import engine

def check_complete_leave_schema():
    """Check complete leave_policies table schema"""
    try:
        with engine.connect() as conn:
            print('=== COMPLETE LEAVE_POLICIES TABLE SCHEMA ===')
            result = conn.execute(text("""
                SELECT column_name, data_type, is_nullable, column_default 
                FROM information_schema.columns 
                WHERE table_name = 'leave_policies' AND table_schema = 'public'
                ORDER BY ordinal_position
            """))
            
            columns = result.fetchall()
            for row in columns:
                nullable = 'NULL' if row[2] == 'YES' else 'NOT NULL'
                default = f' DEFAULT {row[3]}' if row[3] else ''
                print(f'  {row[0]} ({row[1]}) {nullable}{default}')
            
            print(f'\nTotal columns: {len(columns)}')
            
            # Check which columns are NOT NULL
            not_null_columns = [row[0] for row in columns if row[2] == 'NO']
            print(f'\nNOT NULL columns ({len(not_null_columns)}):')
            for col in not_null_columns:
                print(f'  - {col}')
                
    except Exception as e:
        print(f"Error checking schema: {e}")

if __name__ == "__main__":
    check_complete_leave_schema()
