"""add date_approved to approvl history table

Revision ID: 0a8137e3ef70
Revises: 2b4137d3e25b
Create Date: 2025-04-29 14:49:22.745235

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy import func, TIMESTAMP, ForeignKey


# revision identifiers, used by Alembic.
revision: str = '0a8137e3ef70'
down_revision: Union[str, None] = '2b4137d3e25b'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    op.add_column('approval_histories', sa.Column('date_approved', TIMESTAMP, nullable=True))


def downgrade() -> None:
    op.drop_column('approval_histories', 'date_approved')

