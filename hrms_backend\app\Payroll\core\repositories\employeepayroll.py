from core.models.employee_payrolls import EmployeePayrollModel
from core.databases.database import db

class EmployeePayrollRepository:
    def __init__(self) -> None:
        pass

    @classmethod
    def createEmployeePayroll(self, gross_pay, net_pay, annual_tax, total_earnings, total_deductions,employee_id, cost_company ):
        employeePayroll = EmployeePayrollModel(
            gross_pay = gross_pay
            net_pay = net_pay
            annual_tax = annual_tax
            total_earnings = total_earnings
            total_deductions = total_deductions
            employee_id = employee_id
            cost_company = cost_company
        )
        db.session.add(employeePayroll)
        db.session.commit()
        return employeePayroll

    @classmethod
    def getEmployeePayroll(self, id):
        return EmployeePayrollModel.query.filter(EmployeeSalDetailsModel.id == id).first()
    
    @classmethod
    def getEmployeePayrollByKeys(self, kwargs):
        return EmployeePayrollModel.query.filter_by(**kwargs).all()
       

    @classmethod
    def updateEmployeePayroll(self, id, **kwargs):
        employeePayroll = EmployeePayrollModel.query.filter_by(id=id).first()
        if employeePayroll:
            for key, value in kwargs.items():
                setattr(employeePayroll, key, value)
            db.session.commit()
            return employeePayroll
        else:
            return None

    @classmethod
    def deleteEmployeePayroll(self, id):
        return EmployeePayrollModel.query.filter(EmployeeSalDetailsModel.id == id).delete()
        