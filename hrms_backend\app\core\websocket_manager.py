import json
import asyncio
from typing import Dict, List, Set, Optional, Any
from fastapi import WebSocket, WebSocketDisconnect
from datetime import datetime
import logging

logger = logging.getLogger(__name__)


class ConnectionManager:
    """WebSocket connection manager for real-time features"""

    def __init__(self):
        # Store active connections by user_id
        self.active_connections: Dict[str, Set[WebSocket]] = {}
        # Store connections by room/channel
        self.rooms: Dict[str, Set[WebSocket]] = {}
        # Store user info for each connection
        self.connection_users: Dict[WebSocket, str] = {}
        # Store connection metadata
        self.connection_metadata: Dict[WebSocket, Dict[str, Any]] = {}

    async def connect(self, websocket: WebSocket, user_id: str, room: Optional[str] = None):
        """Accept a new WebSocket connection"""
        await websocket.accept()

        # Add to user connections
        if user_id not in self.active_connections:
            self.active_connections[user_id] = set()
        self.active_connections[user_id].add(websocket)

        # Store user info for this connection
        self.connection_users[websocket] = user_id

        # Add to room if specified
        if room:
            if room not in self.rooms:
                self.rooms[room] = set()
            self.rooms[room].add(websocket)

        # Store metadata
        self.connection_metadata[websocket] = {
            "user_id": user_id,
            "room": room,
            "connected_at": datetime.utcnow(),
            "last_ping": datetime.utcnow()
        }

        logger.info(f"User {user_id} connected to WebSocket" + (f" in room {room}" if room else ""))

    def disconnect(self, websocket: WebSocket):
        """Remove a WebSocket connection"""
        if websocket in self.connection_users:
            user_id = self.connection_users[websocket]

            # Remove from user connections
            if user_id in self.active_connections:
                self.active_connections[user_id].discard(websocket)
                if not self.active_connections[user_id]:
                    del self.active_connections[user_id]

            # Remove from rooms
            for room_connections in self.rooms.values():
                room_connections.discard(websocket)

            # Clean up metadata
            room = self.connection_metadata.get(websocket, {}).get("room")
            del self.connection_users[websocket]
            del self.connection_metadata[websocket]

            logger.info(f"User {user_id} disconnected from WebSocket" + (f" in room {room}" if room else ""))

    async def send_personal_message(self, message: dict, user_id: str):
        """Send message to a specific user"""
        if user_id in self.active_connections:
            message_str = json.dumps(message)
            disconnected = []

            for websocket in self.active_connections[user_id].copy():
                try:
                    await websocket.send_text(message_str)
                except Exception as e:
                    logger.error(f"Error sending message to user {user_id}: {e}")
                    disconnected.append(websocket)

            # Clean up disconnected websockets
            for websocket in disconnected:
                self.disconnect(websocket)

    async def send_room_message(self, message: dict, room: str):
        """Send message to all users in a room"""
        if room in self.rooms:
            message_str = json.dumps(message)
            disconnected = []

            for websocket in self.rooms[room].copy():
                try:
                    await websocket.send_text(message_str)
                except Exception as e:
                    user_id = self.connection_users.get(websocket, "unknown")
                    logger.error(f"Error sending message to user {user_id} in room {room}: {e}")
                    disconnected.append(websocket)

            # Clean up disconnected websockets
            for websocket in disconnected:
                self.disconnect(websocket)

    async def broadcast_message(self, message: dict):
        """Broadcast message to all connected users"""
        message_str = json.dumps(message)
        disconnected = []

        for websocket in self.connection_users.keys():
            try:
                await websocket.send_text(message_str)
            except Exception as e:
                user_id = self.connection_users.get(websocket, "unknown")
                logger.error(f"Error broadcasting message to user {user_id}: {e}")
                disconnected.append(websocket)

        # Clean up disconnected websockets
        for websocket in disconnected:
            self.disconnect(websocket)

    def get_user_connections(self, user_id: str) -> Set[WebSocket]:
        """Get all connections for a user"""
        return self.active_connections.get(user_id, set())

    def get_room_connections(self, room: str) -> Set[WebSocket]:
        """Get all connections in a room"""
        return self.rooms.get(room, set())

    def get_connected_users(self) -> List[str]:
        """Get list of all connected user IDs"""
        return list(self.active_connections.keys())

    def get_room_users(self, room: str) -> List[str]:
        """Get list of user IDs in a room"""
        if room not in self.rooms:
            return []

        users = []
        for websocket in self.rooms[room]:
            if websocket in self.connection_users:
                users.append(self.connection_users[websocket])
        return list(set(users))  # Remove duplicates

    async def ping_all_connections(self):
        """Send ping to all connections to keep them alive"""
        ping_message = {
            "type": "ping",
            "timestamp": datetime.utcnow().isoformat()
        }
        await self.broadcast_message(ping_message)

    def get_connection_stats(self) -> dict:
        """Get connection statistics"""
        return {
            "total_connections": len(self.connection_users),
            "total_users": len(self.active_connections),
            "total_rooms": len(self.rooms),
            "rooms": {room: len(connections) for room, connections in self.rooms.items()}
        }


# Global connection manager instance
connection_manager = ConnectionManager()


class NotificationManager:
    """Manager for sending real-time notifications"""

    def __init__(self, connection_manager: ConnectionManager):
        self.connection_manager = connection_manager

    async def notify_user(self, user_id: str, notification_type: str, data: dict):
        """Send notification to a specific user"""
        message = {
            "type": "notification",
            "notification_type": notification_type,
            "data": data,
            "timestamp": datetime.utcnow().isoformat()
        }
        await self.connection_manager.send_personal_message(message, user_id)

    async def notify_room(self, room: str, notification_type: str, data: dict):
        """Send notification to all users in a room"""
        message = {
            "type": "notification",
            "notification_type": notification_type,
            "data": data,
            "timestamp": datetime.utcnow().isoformat()
        }
        await self.connection_manager.send_room_message(message, room)

    async def notify_attendance_update(self, user_id: str, attendance_data: dict):
        """Notify about attendance updates"""
        await self.notify_user(user_id, "attendance_update", attendance_data)

    async def notify_leave_status_change(self, user_id: str, leave_data: dict):
        """Notify about leave status changes"""
        await self.notify_user(user_id, "leave_status_change", leave_data)

    async def notify_task_assignment(self, user_id: str, task_data: dict):
        """Notify about task assignments"""
        await self.notify_user(user_id, "task_assignment", task_data)

    async def notify_kanban_update(self, board_id: str, update_data: dict):
        """Notify about Kanban board updates"""
        room = f"kanban_board_{board_id}"
        await self.notify_room(room, "kanban_update", update_data)

    async def notify_ticket_update(self, user_id: str, ticket_data: dict):
        """Notify about ticket updates"""
        await self.notify_user(user_id, "ticket_update", ticket_data)

    async def notify_delegation_request(self, user_id: str, delegation_data: dict):
        """Notify about delegation requests"""
        await self.notify_user(user_id, "delegation_request", delegation_data)

    # Enhanced Project Management Notifications
    async def notify_project_update(self, project_id: str, update_data: dict):
        """Notify about project updates"""
        room = f"project_{project_id}"
        await self.notify_room(room, "project_update", update_data)

    async def notify_project_assignment(self, user_id: str, project_data: dict):
        """Notify about project assignments"""
        await self.notify_user(user_id, "project_assignment", project_data)

    async def notify_task_status_change(self, task_id: str, status_data: dict):
        """Notify about task status changes"""
        room = f"task_{task_id}"
        await self.notify_room(room, "task_status_change", status_data)

    async def notify_card_assignment(self, user_id: str, card_data: dict):
        """Notify about kanban card assignments"""
        await self.notify_user(user_id, "card_assignment", card_data)

    async def notify_card_move(self, board_id: str, move_data: dict):
        """Notify about kanban card movements"""
        room = f"kanban_board_{board_id}"
        await self.notify_room(room, "card_move", move_data)

    async def notify_due_date_reminder(self, user_id: str, reminder_data: dict):
        """Notify about due date reminders"""
        await self.notify_user(user_id, "due_date_reminder", reminder_data)

    async def notify_overdue_alert(self, user_id: str, overdue_data: dict):
        """Notify about overdue items"""
        await self.notify_user(user_id, "overdue_alert", overdue_data)

    async def notify_project_milestone(self, project_id: str, milestone_data: dict):
        """Notify about project milestone updates"""
        room = f"project_{project_id}"
        await self.notify_room(room, "milestone_update", milestone_data)

    async def notify_team_activity(self, team_id: str, activity_data: dict):
        """Notify team about activity updates"""
        room = f"team_{team_id}"
        await self.notify_room(room, "team_activity", activity_data)

    async def notify_dashboard_update(self, user_id: str, dashboard_data: dict):
        """Notify about dashboard data updates"""
        await self.notify_user(user_id, "dashboard_update", dashboard_data)

    async def broadcast_project_activity(self, organization_id: str, activity_data: dict):
        """Broadcast project activity to organization"""
        room = f"org_{organization_id}_projects"
        await self.notify_room(room, "project_activity", activity_data)


# Global notification manager instance
notification_manager = NotificationManager(connection_manager)
