/**
 * Professional Leave Calendar Component
 * Features: Monthly/weekly views, leave visualization, team calendar
 */

import React, { useState, useEffect } from 'react';
import {
  ChevronLeft,
  ChevronRight,
  Calendar as CalendarIcon,
  Users,
  Filter,
  Download,
  Eye,
  Plus
} from 'lucide-react';
import apiService from '../../services/api';

const LeaveCalendar = ({ onApplyLeave }) => {
  const [currentDate, setCurrentDate] = useState(new Date());
  const [view, setView] = useState('month'); // month, week
  const [leaveData, setLeaveData] = useState([]);
  const [loading, setLoading] = useState(true);
  const [selectedFilters, setSelectedFilters] = useState({
    leaveTypes: [],
    employees: [],
    status: 'approved'
  });

  useEffect(() => {
    loadLeaveData();
  }, [currentDate, selectedFilters]);

  const loadLeaveData = async () => {
    try {
      setLoading(true);
      const dashboardData = await apiService.get('/leave/dashboard');
      setLeaveData(dashboardData.team_calendar || []);
    } catch (error) {
      console.error('Error loading leave data:', error);
    } finally {
      setLoading(false);
    }
  };

  const getDaysInMonth = (date) => {
    return new Date(date.getFullYear(), date.getMonth() + 1, 0).getDate();
  };

  const getFirstDayOfMonth = (date) => {
    return new Date(date.getFullYear(), date.getMonth(), 1).getDay();
  };

  const navigateMonth = (direction) => {
    setCurrentDate(prev => {
      const newDate = new Date(prev);
      newDate.setMonth(prev.getMonth() + direction);
      return newDate;
    });
  };

  const navigateWeek = (direction) => {
    setCurrentDate(prev => {
      const newDate = new Date(prev);
      newDate.setDate(prev.getDate() + (direction * 7));
      return newDate;
    });
  };

  const getWeekDates = (date) => {
    const week = [];
    const startOfWeek = new Date(date);
    const day = startOfWeek.getDay();
    const diff = startOfWeek.getDate() - day;
    startOfWeek.setDate(diff);

    for (let i = 0; i < 7; i++) {
      const day = new Date(startOfWeek);
      day.setDate(startOfWeek.getDate() + i);
      week.push(day);
    }
    return week;
  };

  const getLeavesForDate = (date) => {
    const dateStr = date.toISOString().split('T')[0];
    return leaveData.filter(leave => {
      const startDate = new Date(leave.start);
      const endDate = new Date(leave.end);
      const checkDate = new Date(dateStr);
      return checkDate >= startDate && checkDate < endDate;
    });
  };

  const formatDate = (date) => {
    return date.toLocaleDateString('en-US', { 
      weekday: 'long', 
      year: 'numeric', 
      month: 'long', 
      day: 'numeric' 
    });
  };

  const renderMonthView = () => {
    const daysInMonth = getDaysInMonth(currentDate);
    const firstDay = getFirstDayOfMonth(currentDate);
    const days = [];

    // Empty cells for days before the first day of the month
    for (let i = 0; i < firstDay; i++) {
      days.push(<div key={`empty-${i}`} className="h-32 border border-gray-200"></div>);
    }

    // Days of the month
    for (let day = 1; day <= daysInMonth; day++) {
      const date = new Date(currentDate.getFullYear(), currentDate.getMonth(), day);
      const leaves = getLeavesForDate(date);
      const isToday = date.toDateString() === new Date().toDateString();
      const isWeekend = date.getDay() === 0 || date.getDay() === 6;

      days.push(
        <div
          key={day}
          className={`h-32 border border-gray-200 p-2 ${
            isToday ? 'bg-blue-50 border-blue-300' : 
            isWeekend ? 'bg-gray-50' : 'bg-white'
          } hover:bg-gray-50 transition-colors`}
        >
          <div className={`text-sm font-medium mb-2 ${
            isToday ? 'text-blue-600' : 
            isWeekend ? 'text-gray-400' : 'text-gray-900'
          }`}>
            {day}
          </div>
          
          <div className="space-y-1">
            {leaves.slice(0, 3).map((leave, index) => (
              <div
                key={index}
                className="text-xs px-2 py-1 rounded text-white truncate"
                style={{ backgroundColor: leave.color }}
                title={`${leave.employee_name} - ${leave.leave_type}`}
              >
                {leave.employee_name}
              </div>
            ))}
            {leaves.length > 3 && (
              <div className="text-xs text-gray-500 px-2">
                +{leaves.length - 3} more
              </div>
            )}
          </div>
        </div>
      );
    }

    return (
      <div className="grid grid-cols-7 gap-0">
        {/* Day headers */}
        {['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'].map(day => (
          <div key={day} className="h-12 border border-gray-200 bg-gray-100 flex items-center justify-center font-medium text-gray-700">
            {day}
          </div>
        ))}
        {days}
      </div>
    );
  };

  const renderWeekView = () => {
    const weekDates = getWeekDates(currentDate);
    
    return (
      <div className="space-y-4">
        {/* Week header */}
        <div className="grid grid-cols-8 gap-4">
          <div className="font-medium text-gray-700">Time</div>
          {weekDates.map((date, index) => (
            <div key={index} className="text-center">
              <div className="font-medium text-gray-900">
                {date.toLocaleDateString('en-US', { weekday: 'short' })}
              </div>
              <div className={`text-sm ${
                date.toDateString() === new Date().toDateString() 
                  ? 'text-blue-600 font-medium' 
                  : 'text-gray-500'
              }`}>
                {date.getDate()}
              </div>
            </div>
          ))}
        </div>

        {/* Week grid */}
        <div className="grid grid-cols-8 gap-4 min-h-96">
          <div className="space-y-4">
            {['9 AM', '12 PM', '3 PM', '6 PM'].map(time => (
              <div key={time} className="h-20 flex items-center text-sm text-gray-500">
                {time}
              </div>
            ))}
          </div>
          
          {weekDates.map((date, index) => (
            <div key={index} className="space-y-4">
              {[1, 2, 3, 4].map(slot => {
                const leaves = getLeavesForDate(date);
                const leave = leaves[slot - 1];
                
                return (
                  <div key={slot} className="h-20 border border-gray-200 rounded p-2">
                    {leave && (
                      <div
                        className="h-full rounded text-white text-xs p-2 flex flex-col justify-center"
                        style={{ backgroundColor: leave.color }}
                      >
                        <div className="font-medium truncate">{leave.employee_name}</div>
                        <div className="truncate">{leave.leave_type}</div>
                      </div>
                    )}
                  </div>
                );
              })}
            </div>
          ))}
        </div>
      </div>
    );
  };

  return (
    <div className="bg-white rounded-xl shadow-sm border border-gray-200">
      {/* Header */}
      <div className="p-6 border-b border-gray-200">
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <div className="flex items-center space-x-4">
            <h2 className="text-xl font-semibold text-gray-900">Leave Calendar</h2>
            
            {/* View Toggle */}
            <div className="flex bg-gray-100 rounded-lg p-1">
              <button
                onClick={() => setView('month')}
                className={`px-3 py-1 text-sm font-medium rounded-md transition-colors ${
                  view === 'month' 
                    ? 'bg-white text-gray-900 shadow-sm' 
                    : 'text-gray-600 hover:text-gray-900'
                }`}
              >
                Month
              </button>
              <button
                onClick={() => setView('week')}
                className={`px-3 py-1 text-sm font-medium rounded-md transition-colors ${
                  view === 'week' 
                    ? 'bg-white text-gray-900 shadow-sm' 
                    : 'text-gray-600 hover:text-gray-900'
                }`}
              >
                Week
              </button>
            </div>
          </div>

          <div className="flex items-center space-x-3">
            <button
              onClick={onApplyLeave}
              className="flex items-center px-4 py-2 agno-bg-primary text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              <Plus size={16} className="mr-2" />
              Apply Leave
            </button>
            
            <button className="flex items-center px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50">
              <Filter size={16} className="mr-2" />
              Filter
            </button>
            
            <button className="flex items-center px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50">
              <Download size={16} className="mr-2" />
              Export
            </button>
          </div>
        </div>

        {/* Navigation */}
        <div className="flex items-center justify-between mt-6">
          <button
            onClick={() => view === 'month' ? navigateMonth(-1) : navigateWeek(-1)}
            className="flex items-center px-3 py-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors"
          >
            <ChevronLeft size={20} />
          </button>

          <h3 className="text-lg font-semibold text-gray-900">
            {view === 'month' 
              ? currentDate.toLocaleDateString('en-US', { month: 'long', year: 'numeric' })
              : `Week of ${getWeekDates(currentDate)[0].toLocaleDateString('en-US', { month: 'short', day: 'numeric' })}`
            }
          </h3>

          <button
            onClick={() => view === 'month' ? navigateMonth(1) : navigateWeek(1)}
            className="flex items-center px-3 py-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors"
          >
            <ChevronRight size={20} />
          </button>
        </div>
      </div>

      {/* Calendar Content */}
      <div className="p-6">
        {loading ? (
          <div className="flex items-center justify-center h-64">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-agno-primary"></div>
          </div>
        ) : (
          <>
            {view === 'month' ? renderMonthView() : renderWeekView()}
          </>
        )}
      </div>

      {/* Legend */}
      <div className="px-6 pb-6">
        <div className="flex items-center space-x-6 text-sm">
          <div className="flex items-center space-x-2">
            <div className="w-3 h-3 bg-blue-500 rounded"></div>
            <span className="text-gray-600">Annual Leave</span>
          </div>
          <div className="flex items-center space-x-2">
            <div className="w-3 h-3 bg-red-500 rounded"></div>
            <span className="text-gray-600">Sick Leave</span>
          </div>
          <div className="flex items-center space-x-2">
            <div className="w-3 h-3 bg-green-500 rounded"></div>
            <span className="text-gray-600">Personal Leave</span>
          </div>
          <div className="flex items-center space-x-2">
            <div className="w-3 h-3 bg-purple-500 rounded"></div>
            <span className="text-gray-600">Other</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default LeaveCalendar;
