/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {
      colors: {
        // AgnoConnect Brand Colors
        agno: {
          'primary-dark': '#073763',    // Primary Dark Blue
          'primary': '#0B2A5A',         // Light Blue (Header/Footer)
          'orange': '#F47C20',          // Bright Orange
          'gray-medium': '#6E7C8E',     // Medium Gray
          'gray-dark': '#2E2E2E',       // Dark Gray/Black
        },
        // Semantic color mappings for easier use
        primary: {
          50: '#f0f7ff',
          100: '#e0efff',
          200: '#b9dfff',
          300: '#7cc8ff',
          400: '#36b0ff',
          500: '#0B2A5A',  // Light Blue
          600: '#073763',  // Primary Dark Blue
          700: '#052a4d',
          800: '#042240',
          900: '#031b35',
        },
        accent: {
          50: '#fff7ed',
          100: '#ffedd5',
          200: '#fed7aa',
          300: '#fdba74',
          400: '#fb923c',
          500: '#F47C20',  // Bright Orange
          600: '#ea580c',
          700: '#c2410c',
          800: '#9a3412',
          900: '#7c2d12',
        },
        gray: {
          50: '#f9fafb',
          100: '#f3f4f6',
          200: '#e5e7eb',
          300: '#d1d5db',
          400: '#9ca3af',
          500: '#6E7C8E',  // Medium Gray
          600: '#4b5563',
          700: '#374151',
          800: '#2E2E2E',  // Dark Gray
          900: '#111827',
        },
        // Add standard colors for gradients
        blue: {
          50: '#eff6ff',
          100: '#dbeafe',
          200: '#bfdbfe',
          300: '#93c5fd',
          400: '#60a5fa',
          500: '#3b82f6',
          600: '#2563eb',
          700: '#1d4ed8',
          800: '#1e40af',
          900: '#1e3a8a',
        },
        green: {
          50: '#f0fdf4',
          100: '#dcfce7',
          200: '#bbf7d0',
          300: '#86efac',
          400: '#4ade80',
          500: '#22c55e',
          600: '#16a34a',
          700: '#15803d',
          800: '#166534',
          900: '#14532d',
        },
        red: {
          50: '#fef2f2',
          100: '#fee2e2',
          200: '#fecaca',
          300: '#fca5a5',
          400: '#f87171',
          500: '#ef4444',
          600: '#dc2626',
          700: '#b91c1c',
          800: '#991b1b',
          900: '#7f1d1d',
        }
      },
      fontFamily: {
        sans: ['Inter', 'system-ui', 'sans-serif'],
      },
      boxShadow: {
        'agno': '0 4px 6px -1px rgba(7, 55, 99, 0.1), 0 2px 4px -1px rgba(7, 55, 99, 0.06)',
        'agno-lg': '0 10px 15px -3px rgba(7, 55, 99, 0.1), 0 4px 6px -2px rgba(7, 55, 99, 0.05)',
      },
      animation: {
        'fade-in': 'fadeIn 0.5s ease-in-out',
        'slide-up': 'slideUp 0.3s ease-out',
        'pulse-orange': 'pulseOrange 2s cubic-bezier(0.4, 0, 0.6, 1) infinite',
      },
      keyframes: {
        fadeIn: {
          '0%': { opacity: '0' },
          '100%': { opacity: '1' },
        },
        slideUp: {
          '0%': { transform: 'translateY(10px)', opacity: '0' },
          '100%': { transform: 'translateY(0)', opacity: '1' },
        },
        pulseOrange: {
          '0%, 100%': { 
            boxShadow: '0 0 0 0 rgba(244, 124, 32, 0.7)' 
          },
          '70%': { 
            boxShadow: '0 0 0 10px rgba(244, 124, 32, 0)' 
          },
        },
      },
      backgroundImage: {
        'agno-gradient': 'linear-gradient(135deg, #073763 0%, #0B2A5A 100%)',
        'agno-gradient-reverse': 'linear-gradient(135deg, #0B2A5A 0%, #073763 100%)',
        'orange-gradient': 'linear-gradient(135deg, #F47C20 0%, #ea580c 100%)',
      }
    },
  },
  plugins: [],
}
