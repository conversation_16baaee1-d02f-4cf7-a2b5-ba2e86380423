from calendar import month
from core.controller.employee import PayrollHistory
from core.models.payroll_history import PayrollHistoryModel
from core.models.organisation import OrganisationModel
from core.repositories.user import UserRepository
from core.databases.database import db



class DashboardRepository:

   @classmethod
   def totalNetPay(cls):
        return db.session.query(
            db.func.sum(PayrollHistoryModel.netpay)
        ).filter(
            PayrollHistoryModel.user_id == UserRepository().authUserId(),
            PayrollHistoryModel.is_processed == True
        ).scalar() or 0

   @classmethod
   def totalMonthlyPay(cls):
            return db.session.query(
            db.func.sum(PayrollHistoryModel.gross_pay)
        ).filter(
            PayrollHistoryModel.user_id == UserRepository().authUserId(),
            PayrollHistoryModel.is_processed == True
        ).scalar() or 0

   @classmethod
   def totalMonthlyTax(cls):
        return db.session.query(
            db.func.sum(PayrollHistoryModel.monthly_tax)
        ).filter(
            PayrollHistoryModel.user_id == UserRepository().authUserId(),
            PayrollHistoryModel.is_processed == True
        ).scalar() or 0

   @classmethod
   def totalStatutoryDeduction(cls):
        return db.session.query(
            db.func.sum(PayrollHistoryModel.total_statutory_monthly_sum)
        ).filter(
            PayrollHistoryModel.user_id == UserRepository().authUserId(),
            PayrollHistoryModel.is_processed == True
        ).scalar() or 0

   @classmethod
   def totalOrganizations(cls):
       return db.session.query(db.func.count(db.distinct(OrganisationModel.id))) \
        .filter(OrganisationModel.user_id == UserRepository().authUserId()) \
        .scalar() or 0
    
   @classmethod
   def get_payroll_summary(cls, year: int, month: int = None):
        """
        Fetch payroll summary data.
        - If `month` is provided, return a single record for that month.
        - If only `year` is provided, return a list of records for each month in that year.
        """
        query = db.session.query(
            db.func.extract('month', db.cast(PayrollHistoryModel.transaction_date, db.Date)).label("month"),
            db.func.sum(PayrollHistoryModel.gross_pay).label("total_gross_pay"),
            db.func.sum(PayrollHistoryModel.netpay).label("total_net_pay"),
            db.func.sum(PayrollHistoryModel.total_other_deductions_monthly_sum).label("total_other_deductions")
        ).filter(
            PayrollHistoryModel.user_id == UserRepository().authUserId(),
            db.func.extract('year', db.cast(PayrollHistoryModel.transaction_date, db.Date)) == year
        )

        # If a specific month is provided, return only one record for that month
        if month:
            query = query.filter(
                db.func.extract('month', db.cast(PayrollHistoryModel.transaction_date, db.Date)) == month
            )
            payroll_data = query.group_by("month").first()

            if not payroll_data:
                return {"error": f"No payroll data available for {month}/{year}"}

            return {
                "year": year,
                "month": month,
                "total_gross_pay": payroll_data.total_gross_pay or 0,
                "total_net_pay": payroll_data.total_net_pay or 0,
                "total_other_deductions": payroll_data.total_other_deductions or 0
            }

        # If only year is provided, return a list of records (grouped by month)
        payroll_data = query.group_by("month").order_by("month").all()

        if not payroll_data:
            return {"error": f"No payroll data available for the year {year}"}

        return {
            "year": year,
            "records": [
                {
                    "month": int(data.month),
                    "total_gross_pay": data.total_gross_pay or 0,
                    "total_net_pay": data.total_net_pay or 0,
                    "total_other_deductions": data.total_other_deductions or 0
                }
                for data in payroll_data
            ]
        }