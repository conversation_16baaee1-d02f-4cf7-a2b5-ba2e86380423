#!/usr/bin/env python3
"""
Core Database Testing Script
Tests essential database functionality and data storage
"""

import sys
import os
import json
import logging
from datetime import datetime
from uuid import uuid4

# Add the app directory to Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'app'))

from sqlalchemy.orm import Session
from app.db.session import SessionLocal, test_connection, create_tables
from app.core.config import settings

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def test_database_connection():
    """Test database connection"""
    try:
        result = test_connection()
        logger.info(f"✅ Database connection: {result}")
        return result
    except Exception as e:
        logger.error(f"❌ Database connection failed: {e}")
        return False


def test_table_creation():
    """Test table creation"""
    try:
        create_tables()
        logger.info("✅ Tables created successfully")
        return True
    except Exception as e:
        logger.error(f"❌ Table creation failed: {e}")
        return False


def test_basic_crud():
    """Test basic CRUD operations"""
    try:
        from app.db.models.user import User
        from app.db.models.employee import Organization, Employee, Department
        from app.db.models.ticket import Ticket, TicketComment, TicketType, TicketPriority, TicketStatus
        
        db = SessionLocal()
        
        # Create test organization
        org = Organization(
            name="Test Organization",
            domain="test.com",
            description="Test organization for database testing"
        )
        db.add(org)
        db.commit()
        db.refresh(org)
        logger.info(f"✅ Created organization: {org.id}")
        
        # Create test user
        user = User(
            username="testuser",
            email="<EMAIL>",
            password_hash="hashed_password",
            first_name="Test",
            last_name="User",
            organization_id=org.id
        )
        db.add(user)
        db.commit()
        db.refresh(user)
        logger.info(f"✅ Created user: {user.id}")
        
        # Create test department
        dept = Department(
            name="IT Department",
            code="IT",
            description="Information Technology Department",
            organization_id=org.id
        )
        db.add(dept)
        db.commit()
        db.refresh(dept)
        logger.info(f"✅ Created department: {dept.id}")
        
        # Create test employee
        employee = Employee(
            employee_id="EMP001",
            first_name="John",
            last_name="Doe",
            email="<EMAIL>",
            phone="+1234567890",
            hire_date=datetime.now().date(),
            organization_id=org.id,
            department_id=dept.id,
            user_id=user.id
        )
        db.add(employee)
        db.commit()
        db.refresh(employee)
        logger.info(f"✅ Created employee: {employee.id}")
        
        # Create test ticket
        ticket = Ticket(
            ticket_number="TKT-000001",
            title="Test Ticket",
            description="This is a test ticket for database testing",
            ticket_type=TicketType.IT_SUPPORT,
            priority=TicketPriority.MEDIUM,
            status=TicketStatus.OPEN,
            requester_id=employee.id,
            organization_id=org.id,
            contact_method="web",
            contact_details="<EMAIL>"
        )
        db.add(ticket)
        db.commit()
        db.refresh(ticket)
        logger.info(f"✅ Created ticket: {ticket.id}")
        
        # Create test comment
        comment = TicketComment(
            ticket_id=ticket.id,
            user_id=user.id,
            content="This is a test comment",
            is_internal=False
        )
        db.add(comment)
        db.commit()
        db.refresh(comment)
        logger.info(f"✅ Created comment: {comment.id}")
        
        # Test AI metadata storage
        ticket.ai_metadata = {
            "predicted_type": "it_support",
            "confidence_score": 0.85,
            "sentiment_analysis": {
                "sentiment": "neutral",
                "urgency_score": 0.6
            },
            "suggested_tags": ["it", "support", "test"]
        }
        db.commit()
        logger.info("✅ Stored AI metadata in ticket")
        
        # Test data retrieval
        retrieved_ticket = db.query(Ticket).filter(Ticket.id == ticket.id).first()
        if retrieved_ticket and retrieved_ticket.ai_metadata:
            logger.info(f"✅ Retrieved AI metadata: {retrieved_ticket.ai_metadata}")
        
        # Test relationships
        ticket_with_comments = db.query(Ticket).filter(Ticket.id == ticket.id).first()
        comments_count = db.query(TicketComment).filter(TicketComment.ticket_id == ticket.id).count()
        logger.info(f"✅ Ticket has {comments_count} comments")
        
        # Clean up
        db.delete(comment)
        db.delete(ticket)
        db.delete(employee)
        db.delete(dept)
        db.delete(user)
        db.delete(org)
        db.commit()
        db.close()
        
        logger.info("✅ CRUD operations and cleanup successful")
        return True
        
    except Exception as e:
        logger.error(f"❌ CRUD operations failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_data_integrity():
    """Test data integrity constraints"""
    try:
        from app.db.models.ticket import Ticket, TicketType, TicketPriority, TicketStatus
        
        db = SessionLocal()
        
        # Test unique constraint
        try:
            ticket1 = Ticket(
                ticket_number="TKT-UNIQUE",
                title="First Ticket",
                description="First ticket",
                ticket_type=TicketType.IT_SUPPORT,
                priority=TicketPriority.LOW,
                status=TicketStatus.OPEN,
                requester_id=uuid4(),
                organization_id=uuid4(),
                contact_method="web"
            )
            db.add(ticket1)
            db.commit()
            
            # Try to create duplicate
            ticket2 = Ticket(
                ticket_number="TKT-UNIQUE",  # Same ticket number
                title="Second Ticket",
                description="Second ticket",
                ticket_type=TicketType.IT_SUPPORT,
                priority=TicketPriority.LOW,
                status=TicketStatus.OPEN,
                requester_id=uuid4(),
                organization_id=uuid4(),
                contact_method="web"
            )
            db.add(ticket2)
            db.commit()
            
            logger.error("❌ Unique constraint not working")
            return False
            
        except Exception:
            db.rollback()
            logger.info("✅ Unique constraint working correctly")
        
        db.close()
        return True
        
    except Exception as e:
        logger.error(f"❌ Data integrity test failed: {e}")
        return False


def test_performance():
    """Test basic performance"""
    try:
        from app.db.models.ticket import Ticket
        
        db = SessionLocal()
        
        # Test query performance
        start_time = datetime.utcnow()
        tickets = db.query(Ticket).limit(100).all()
        end_time = datetime.utcnow()
        
        query_time = (end_time - start_time).total_seconds()
        logger.info(f"✅ Query performance: {len(tickets)} tickets in {query_time:.3f} seconds")
        
        db.close()
        return True
        
    except Exception as e:
        logger.error(f"❌ Performance test failed: {e}")
        return False


def main():
    """Main test function"""
    print("🚀 Running Core Database Tests")
    print("=" * 50)
    print(f"Database: {settings.database_url}")
    print("=" * 50)
    
    tests = [
        ("Database Connection", test_database_connection),
        ("Table Creation", test_table_creation),
        ("Basic CRUD Operations", test_basic_crud),
        ("Data Integrity", test_data_integrity),
        ("Performance", test_performance)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n🔍 Testing: {test_name}")
        try:
            result = test_func()
            results.append(result)
        except Exception as e:
            logger.error(f"❌ {test_name} failed with exception: {e}")
            results.append(False)
    
    # Summary
    passed = sum(results)
    total = len(results)
    
    print(f"\n📊 Test Summary:")
    print(f"Passed: {passed}/{total}")
    print(f"Success Rate: {(passed/total)*100:.1f}%")
    
    # Generate report
    report = {
        "test_summary": {
            "total_tests": total,
            "passed_tests": passed,
            "failed_tests": total - passed,
            "success_rate": round((passed/total)*100, 2)
        },
        "database_info": {
            "url": settings.database_url,
            "host": settings.DB_HOST,
            "port": settings.DB_PORT,
            "database": settings.DB_NAME
        },
        "timestamp": datetime.utcnow().isoformat()
    }
    
    with open('core_test_report.json', 'w') as f:
        json.dump(report, f, indent=2)
    
    if passed == total:
        print("🎉 All core tests passed!")
        print("✅ Database is working correctly and storing values properly!")
        return True
    else:
        print("⚠️ Some tests failed!")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
