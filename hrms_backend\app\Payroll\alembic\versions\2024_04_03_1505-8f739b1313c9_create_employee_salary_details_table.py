"""create employee_salary_details table

Revision ID: 8f739b1313c9
Revises: a59bd156bbf8
Create Date: 2024-04-03 15:05:16.050690

"""
from typing import Sequence, Union

from alembic import op
from sqlalchemy import func, TIMESTAMP, Foreign<PERSON>ey
from sqlalchemy import Column, Integer, String, Float


# revision identifiers, used by Alembic.
revision: str = '8f739b1313c9'
down_revision: Union[str, None] = 'a59bd156bbf8'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    op.create_table(
        'employee_salary_details',
        <PERSON>umn('id', Integer, primary_key=True),
        <PERSON><PERSON><PERSON>("employee_id", Integer, ForeignKey("employees.id")),
        <PERSON><PERSON><PERSON>("template_id", Integer, ForeignKey("salary_templates.id")),
        <PERSON><PERSON><PERSON>("amount", Float, nullable=False),
        <PERSON><PERSON><PERSON>('salary_type', String(100), nullable=False),
        <PERSON><PERSON><PERSON>("timestamp", TIMESTAMP, server_default=func.now()),
       
    )

def downgrade() -> None:
    op.drop_table("employee_salary_details")

