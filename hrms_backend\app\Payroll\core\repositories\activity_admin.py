from core.models.activity_admin import AdminActivityModel
from core.databases.database import db
from core.repositories.user import UserRepository
from sqlalchemy.exc import SQLAlchemyError

class AdminActivityRepository:
    def __init__(self) -> None:
        pass

    @classmethod
    def log_activity(cls, message, user_id=None):
        try:
            if user_id is None:
                from core.repositories.user import UserRepository
                user_id = UserRepository.authUserId()

            activity = AdminActivityModel(
                message=message,
                user_id=user_id
            )
            db.session.add(activity)
            db.session.commit()
            return activity
        except SQLAlchemyError as e:
            print(f"Database Error: {str(e)}")
            db.session.rollback()
            raise e
        except Exception as e:
            print(f"Unexpected Error: {str(e)}")
            raise e

    @classmethod
    def fetch_all(cls):
        return AdminActivityModel.query.filter_by(user_id=UserRepository.authUserId()).order_by(AdminActivityModel.created_at.desc()).all()

    @classmethod
    def fetch_by_id(cls, id):
        return AdminActivityModel.query.filter_by(id=id, user_id=UserRepository.authUserId()).first()

    @classmethod
    def delete_activity(cls, id):
        AdminActivityModel.query.filter_by(id=id, user_id=UserRepository.authUserId()).delete()
        db.session.commit()
