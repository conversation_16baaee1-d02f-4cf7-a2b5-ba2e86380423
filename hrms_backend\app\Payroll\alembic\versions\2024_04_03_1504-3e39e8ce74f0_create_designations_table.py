"""create designations table

Revision ID: 0c4a93e5a071
Revises: 6444bf2c4fdd
Create Date: 2024-04-03 15:04:37.590940

"""
from typing import Sequence, Union

from alembic import op
from sqlalchemy import func, TIMESTAMP, Foreign<PERSON>ey
from sqlalchemy import <PERSON>umn, Integer, String

# revision identifiers, used by Alembic.
revision: str = "0c4a93e5a071"
down_revision: Union[str, None] = '6444bf2c4fdd'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    op.create_table(
        'designations',
        Column('id', Integer, primary_key=True),
        <PERSON>umn('name', String(45), nullable=False),
        <PERSON>umn('user_id', Integer, Foreign<PERSON>ey("users.id")),
        <PERSON>umn("timestamp", TIMESTAMP, server_default=func.now()),
    )


def downgrade() -> None:
    op.drop_table("designations")


