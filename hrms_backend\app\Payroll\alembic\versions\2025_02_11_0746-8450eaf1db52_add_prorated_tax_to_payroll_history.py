"""Add prorated tax to payroll history

Revision ID: 8450eaf1db52
Revises: 316245418208
Create Date: 2025-02-11 07:46:22.060893

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '8450eaf1db52'
down_revision: Union[str, None] = '316245418208'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    op.add_column("payroll_history", sa.Column("prorated_monthly_tax", sa.Float, nullable=True))
    op.add_column("payroll_history", sa.Column("prorated_annual_tax", sa.Float, nullable=True))


def downgrade() -> None:
    op.drop_column("payroll_history", "prorated_monthly_tax")
    op.drop_column("payroll_history", "prorated_annual_tax")
