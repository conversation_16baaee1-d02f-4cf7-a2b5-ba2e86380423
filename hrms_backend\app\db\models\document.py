from sqlalchemy import Column, String, DateTime, Foreign<PERSON>ey, <PERSON>olean, Text, Integer, Enum
from sqlalchemy.dialects.postgresql import UUID, JSONB
from sqlalchemy.orm import relationship
from enum import Enum as PyEnum
from datetime import datetime
from typing import Optional

from ..base import BaseModel, AuditMixin


class DocumentType(PyEnum):
    LEAVE_POLICY = "leave_policy"
    HOLIDAY_LIST = "holiday_list"
    EMPLOYEE_HANDBOOK = "employee_handbook"
    FORM_TEMPLATE = "form_template"
    CERTIFICATE = "certificate"
    ATTACHMENT = "attachment"
    OTHER = "other"


class DocumentStatus(PyEnum):
    DRAFT = "draft"
    ACTIVE = "active"
    ARCHIVED = "archived"
    DELETED = "deleted"


class Document(BaseModel, AuditMixin):
    """Document management model"""
    __tablename__ = "documents"

    # Basic Information
    name = Column(String(255), nullable=False)
    description = Column(Text, nullable=True)
    document_type = Column(Enum(DocumentType), nullable=False)
    status = Column(Enum(DocumentStatus), nullable=False, default=DocumentStatus.DRAFT)
    
    # File Information
    file_name = Column(String(255), nullable=False)
    file_path = Column(String(500), nullable=False)
    file_size = Column(Integer, nullable=False)  # in bytes
    mime_type = Column(String(100), nullable=False)
    file_hash = Column(String(64), nullable=True)  # SHA-256 hash for integrity
    
    # Version Control
    version = Column(String(20), nullable=False, default="1.0")
    parent_document_id = Column(UUID(as_uuid=True), ForeignKey("documents.id"), nullable=True)
    is_latest_version = Column(Boolean, default=True)
    
    # Organization and Access
    organization_id = Column(UUID(as_uuid=True), nullable=False, index=True)
    uploaded_by = Column(UUID(as_uuid=True), nullable=False)  # User ID
    
    # Metadata
    tags = Column(JSONB, nullable=True)  # Array of tags
    metadata = Column(JSONB, nullable=True)  # Additional metadata
    
    # Access Control
    is_public = Column(Boolean, default=False)
    allowed_roles = Column(JSONB, nullable=True)  # Array of roles that can access
    allowed_departments = Column(JSONB, nullable=True)  # Array of department IDs
    
    # Expiry and Validity
    valid_from = Column(DateTime(timezone=True), nullable=True)
    valid_until = Column(DateTime(timezone=True), nullable=True)
    
    # Relationships
    parent_document = relationship("Document", remote_side="Document.id", back_populates="versions")
    versions = relationship("Document", back_populates="parent_document")
    
    # Unique constraint for organization and file path
    __table_args__ = (
        {"schema": None},
    )

    @property
    def file_size_mb(self) -> float:
        """Get file size in MB"""
        return round(self.file_size / (1024 * 1024), 2)

    @property
    def is_expired(self) -> bool:
        """Check if document is expired"""
        if self.valid_until:
            return datetime.utcnow() > self.valid_until.replace(tzinfo=None)
        return False


class DocumentAccess(BaseModel):
    """Document access log"""
    __tablename__ = "document_access_logs"

    document_id = Column(UUID(as_uuid=True), ForeignKey("documents.id"), nullable=False)
    user_id = Column(UUID(as_uuid=True), nullable=False)
    access_type = Column(String(20), nullable=False)  # view, download, edit
    ip_address = Column(String(45), nullable=True)
    user_agent = Column(Text, nullable=True)
    accessed_at = Column(DateTime(timezone=True), nullable=False, default=datetime.utcnow)
    
    # Relationships
    document = relationship("Document")


class DocumentShare(BaseModel):
    """Document sharing configuration"""
    __tablename__ = "document_shares"

    document_id = Column(UUID(as_uuid=True), ForeignKey("documents.id"), nullable=False)
    shared_by = Column(UUID(as_uuid=True), nullable=False)
    shared_with = Column(UUID(as_uuid=True), nullable=True)  # Specific user
    shared_with_role = Column(String(50), nullable=True)  # Role-based sharing
    shared_with_department = Column(UUID(as_uuid=True), nullable=True)  # Department-based sharing
    
    # Permissions
    can_view = Column(Boolean, default=True)
    can_download = Column(Boolean, default=False)
    can_edit = Column(Boolean, default=False)
    can_share = Column(Boolean, default=False)
    
    # Validity
    expires_at = Column(DateTime(timezone=True), nullable=True)
    is_active = Column(Boolean, default=True)
    
    # Relationships
    document = relationship("Document")


class DocumentTemplate(BaseModel, AuditMixin):
    """Document templates for forms and policies"""
    __tablename__ = "document_templates"

    name = Column(String(255), nullable=False)
    description = Column(Text, nullable=True)
    template_type = Column(String(50), nullable=False)  # leave_form, policy_template, etc.
    
    # Template Content
    content = Column(Text, nullable=False)  # HTML or markdown content
    variables = Column(JSONB, nullable=True)  # Template variables
    
    # Organization
    organization_id = Column(UUID(as_uuid=True), nullable=False, index=True)
    is_default = Column(Boolean, default=False)
    is_active = Column(Boolean, default=True)
    
    # Version
    version = Column(String(20), nullable=False, default="1.0")
