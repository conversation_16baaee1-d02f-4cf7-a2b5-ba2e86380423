from sqlalchemy import Column, String, DateTime, Date, Time, ForeignKey, Boolean, Text, Numeric, Integer
from sqlalchemy.dialects.postgresql import UUID, JSONB
from sqlalchemy.orm import relationship
from enum import Enum as PyEnum
from datetime import datetime, date, time
from typing import Optional

from ..base import BaseModel, AuditMixin


class AttendanceStatus(PyEnum):
    PRESENT = "present"
    ABSENT = "absent"
    LATE = "late"
    HALF_DAY = "half_day"
    WORK_FROM_HOME = "work_from_home"
    ON_LEAVE = "on_leave"
    HOLIDAY = "holiday"


class CheckType(PyEnum):
    CHECK_IN = "check_in"
    CHECK_OUT = "check_out"
    BREAK_START = "break_start"
    BREAK_END = "break_end"


class AttendanceRecord(BaseModel, AuditMixin):
    """Daily attendance record for employees"""
    __tablename__ = "attendance_records"

    employee_id = Column(UUID(as_uuid=True), ForeignKey("employees.id"), nullable=False, index=True)
    date = Column(Date, nullable=False, index=True)

    # Check-in/out times
    check_in_time = Column(DateTime(timezone=True), nullable=True)
    check_out_time = Column(DateTime(timezone=True), nullable=True)

    # Break times
    break_start_time = Column(DateTime(timezone=True), nullable=True)
    break_end_time = Column(DateTime(timezone=True), nullable=True)
    total_break_duration = Column(Integer, nullable=True)  # in minutes

    # Calculated fields
    total_hours_worked = Column(Numeric(5, 2), nullable=True)  # decimal hours
    overtime_hours = Column(Numeric(5, 2), nullable=True, default=0)

    # Status and location
    status = Column(String(50), nullable=False, default=AttendanceStatus.PRESENT.value)
    work_location = Column(String(255), nullable=True)
    is_remote = Column(Boolean, default=False)

    # Geolocation (optional)
    check_in_latitude = Column(Numeric(10, 8), nullable=True)
    check_in_longitude = Column(Numeric(11, 8), nullable=True)
    check_out_latitude = Column(Numeric(10, 8), nullable=True)
    check_out_longitude = Column(Numeric(11, 8), nullable=True)

    # Shift information
    shift_id = Column(UUID(as_uuid=True), ForeignKey("shifts.id"), nullable=True)
    expected_check_in = Column(DateTime(timezone=True), nullable=True)
    expected_check_out = Column(DateTime(timezone=True), nullable=True)

    # Approval and notes
    is_approved = Column(Boolean, default=False)
    approved_by = Column(UUID(as_uuid=True), ForeignKey("employees.id"), nullable=True)
    approved_at = Column(DateTime(timezone=True), nullable=True)

    # Relationships
    employee = relationship("Employee", back_populates="attendance_records", foreign_keys=[employee_id])
    shift = relationship("Shift", back_populates="attendance_records")
    approver = relationship("Employee", foreign_keys=[approved_by])

    # Unique constraint on employee and date
    __table_args__ = (
        {"schema": None},
    )


class AttendanceLog(BaseModel):
    """Individual check-in/out log entries"""
    __tablename__ = "attendance_logs"

    employee_id = Column(UUID(as_uuid=True), ForeignKey("employees.id"), nullable=False, index=True)
    attendance_record_id = Column(UUID(as_uuid=True), ForeignKey("attendance_records.id"), nullable=True)

    timestamp = Column(DateTime(timezone=True), nullable=False)
    check_type = Column(String(20), nullable=False)  # check_in, check_out, break_start, break_end

    # Location data
    latitude = Column(Numeric(10, 8), nullable=True)
    longitude = Column(Numeric(11, 8), nullable=True)
    location_name = Column(String(255), nullable=True)

    # Device/IP information
    ip_address = Column(String(45), nullable=True)
    user_agent = Column(Text, nullable=True)
    device_info = Column(JSONB, nullable=True)

    # Photo/verification
    photo_url = Column(String(500), nullable=True)

    # Relationships
    employee = relationship("Employee")
    attendance_record = relationship("AttendanceRecord")


class AttendancePolicy(BaseModel):
    """Attendance policy configuration"""
    __tablename__ = "attendance_policies"

    name = Column(String(200), nullable=False)
    organization_id = Column(UUID(as_uuid=True), nullable=False, index=True)

    # Working hours
    standard_hours_per_day = Column(Numeric(4, 2), nullable=False, default=8.0)
    standard_hours_per_week = Column(Numeric(4, 2), nullable=False, default=40.0)

    # Grace periods
    late_grace_period = Column(Integer, nullable=False, default=15)  # minutes
    early_departure_grace_period = Column(Integer, nullable=False, default=15)  # minutes

    # Break settings
    break_duration = Column(Integer, nullable=False, default=60)  # minutes
    max_break_duration = Column(Integer, nullable=False, default=120)  # minutes

    # Overtime settings
    overtime_threshold = Column(Numeric(4, 2), nullable=False, default=8.0)  # hours
    overtime_multiplier = Column(Numeric(3, 2), nullable=False, default=1.5)

    # Geofencing
    enable_geofencing = Column(Boolean, default=False)
    office_latitude = Column(Numeric(10, 8), nullable=True)
    office_longitude = Column(Numeric(11, 8), nullable=True)
    geofence_radius = Column(Integer, nullable=True, default=100)  # meters

    # Photo verification
    require_photo_checkin = Column(Boolean, default=False)
    require_photo_checkout = Column(Boolean, default=False)

    # Auto clock-out
    auto_clock_out_enabled = Column(Boolean, default=True)
    auto_clock_out_time = Column(Time, nullable=True)

    is_default = Column(Boolean, default=False)
