from core.repositories.announcements import AnnouncementsRepository

class AnnouncementService:
    def __init__(self) -> None:
        self.repository = AnnouncementsRepository()

    def createAnnouncements(self, Kwargs):
        return self.repository.createAnnouncements(**Kwargs)
    
    def fetchAll(self):
        announcements = self.repository.fetchAll()
        total_announcements = len(announcements)
        return announcements, total_announcements

    def getAnnouncements(self, id):
        return self.repository.getAnnouncements(id)
    
    def updateAnnouncements(self, id, Kwargs):
        return self.repository.updateAnnouncements(id, **Kwargs)
    
    def getAnnouncementsByKey(self, Kwarg):
        return self.repository.getAnnouncementsByKeys(Kwarg)
    
    def deleteAnnouncements(self, id):
        return self.repository.deleteAnnouncements(id)