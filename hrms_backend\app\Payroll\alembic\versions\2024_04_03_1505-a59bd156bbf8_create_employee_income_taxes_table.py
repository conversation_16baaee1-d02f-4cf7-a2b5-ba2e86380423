"""create employee_income_taxes table

Revision ID: a59bd156bbf8
Revises: ee6fd56b6289
Create Date: 2024-04-03 15:05:10.120603

"""
from typing import Sequence, Union

from alembic import op
from sqlalchemy import func, TIMESTAMP, Foreign<PERSON>ey
from sqlalchemy import Column, Integer, String, Float

# revision identifiers, used by Alembic.
revision: str = 'a59bd156bbf8'
down_revision: Union[str, None] = 'ee6fd56b6289'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    op.create_table(
        'employee_income_taxes',
        <PERSON>umn('id', Integer, primary_key=True),
        <PERSON><PERSON><PERSON>('employee_id', Integer, ForeignKey("employees.id")),
        <PERSON><PERSON><PERSON>('regulation_id', Integer, ForeignKey("tax_regulations.id")),
        <PERSON><PERSON><PERSON>('taxable_income', Integer, nullable=False),
        <PERSON><PERSON><PERSON>('tax_amount', Float, nullable=False),
        <PERSON><PERSON><PERSON>('organisation_id', Integer, ForeignKey("employees.id")),
        <PERSON><PERSON><PERSON>("timestamp", TIMESTAMP, server_default=func.now()),
    )


def downgrade() -> None:
     op.drop_table("employee_income_taxes")
