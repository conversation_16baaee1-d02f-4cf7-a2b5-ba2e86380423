"""Add preview_payslip to benefits table

Revision ID: 4854bff2583d
Revises: 6451ff91ffc2
Create Date: 2025-01-28 05:17:33.336793

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '4854bff2583d'
down_revision: Union[str, None] = '6451ff91ffc2'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    op.add_column("benefits", sa.Column("preview_payslip", sa.<PERSON>, server_default=sa.sql.expression.false()))


def downgrade() -> None:
    op.drop_column("benefits","preview_payslip")
