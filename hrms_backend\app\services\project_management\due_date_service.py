from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, func
from typing import Optional, List, Dict, Any
from uuid import UUID
from datetime import datetime, date, timedelta
from fastapi import HTTPException, status
import logging

from ...db.models.project import Project, Task
from ...db.models.kanban import KanbanBoard, KanbanCard, KanbanColumn
from ...db.models.employee import Employee
from ...schemas.project import ProjectStatus, TaskStatus
from ...core.security import CurrentUser
from ...core.websocket_manager import notification_manager

logger = logging.getLogger(__name__)


class DueDateService:
    """Service for managing due dates and overdue notifications"""

    async def get_overdue_items(
        self,
        db: Session,
        current_user: CurrentUser,
        item_type: Optional[str] = None
    ) -> Dict[str, Any]:
        """Get all overdue items for the current user"""
        try:
            today = date.today()
            now = datetime.utcnow()
            
            overdue_data = {}

            if not item_type or item_type == "projects":
                overdue_data["projects"] = await self._get_overdue_projects(db, current_user, today)

            if not item_type or item_type == "tasks":
                overdue_data["tasks"] = await self._get_overdue_tasks(db, current_user, today)

            if not item_type or item_type == "cards":
                overdue_data["cards"] = await self._get_overdue_cards(db, current_user, now)

            # Calculate summary
            total_overdue = sum(len(items) for items in overdue_data.values())
            
            return {
                "total_overdue": total_overdue,
                "overdue_items": overdue_data,
                "checked_at": datetime.utcnow().isoformat()
            }

        except Exception as e:
            logger.error(f"Error getting overdue items: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Error retrieving overdue items"
            )

    async def _get_overdue_projects(
        self,
        db: Session,
        current_user: CurrentUser,
        today: date
    ) -> List[Dict[str, Any]]:
        """Get overdue projects"""
        
        query = db.query(Project).filter(
            Project.organization_id == current_user.organization_id,
            Project.end_date < today,
            Project.status.in_([ProjectStatus.PLANNING, ProjectStatus.IN_PROGRESS]),
            Project.is_active == True
        )

        # Role-based filtering
        if current_user.role.upper() == "MANAGER":
            query = query.filter(
                or_(
                    Project.project_manager_id == current_user.user_id,
                    Project.id.in_(
                        db.query(ProjectAssignment.project_id).filter(
                            ProjectAssignment.employee_id == current_user.user_id
                        )
                    )
                )
            )
        elif current_user.role.upper() == "EMPLOYEE":
            query = query.filter(
                Project.id.in_(
                    db.query(ProjectAssignment.project_id).filter(
                        ProjectAssignment.employee_id == current_user.user_id
                    )
                )
            )

        projects = query.all()

        return [
            {
                "id": str(project.id),
                "name": project.name,
                "description": project.description,
                "due_date": project.end_date.isoformat(),
                "days_overdue": (today - project.end_date).days,
                "status": project.status.value,
                "progress": float(project.progress_percentage or 0),
                "priority": "high" if (today - project.end_date).days > 7 else "medium",
                "project_manager": project.project_manager.first_name + " " + project.project_manager.last_name if project.project_manager else None
            }
            for project in projects
        ]

    async def _get_overdue_tasks(
        self,
        db: Session,
        current_user: CurrentUser,
        today: date
    ) -> List[Dict[str, Any]]:
        """Get overdue tasks"""
        
        query = db.query(Task).join(Project).filter(
            Project.organization_id == current_user.organization_id,
            Task.due_date < today,
            Task.status.in_([TaskStatus.TODO, TaskStatus.IN_PROGRESS])
        )

        # Role-based filtering
        if current_user.role.upper() == "MANAGER":
            query = query.filter(
                or_(
                    Task.assignee_id == current_user.user_id,
                    Task.reporter_id == current_user.user_id,
                    Project.project_manager_id == current_user.user_id
                )
            )
        elif current_user.role.upper() == "EMPLOYEE":
            query = query.filter(
                or_(
                    Task.assignee_id == current_user.user_id,
                    Task.reporter_id == current_user.user_id
                )
            )

        tasks = query.all()

        return [
            {
                "id": str(task.id),
                "title": task.title,
                "description": task.description,
                "due_date": task.due_date.isoformat(),
                "days_overdue": (today - task.due_date).days,
                "status": task.status.value,
                "priority": task.priority.value if task.priority else "medium",
                "project_name": task.project.name if task.project else None,
                "project_id": str(task.project.id) if task.project else None,
                "assignee": task.assignee.first_name + " " + task.assignee.last_name if task.assignee else None,
                "estimated_hours": float(task.estimated_hours) if task.estimated_hours else None
            }
            for task in tasks
        ]

    async def _get_overdue_cards(
        self,
        db: Session,
        current_user: CurrentUser,
        now: datetime
    ) -> List[Dict[str, Any]]:
        """Get overdue kanban cards"""
        
        query = db.query(KanbanCard).join(KanbanColumn).join(KanbanBoard).filter(
            KanbanBoard.organization_id == current_user.organization_id,
            KanbanCard.due_date < now,
            KanbanCard.completed_date.is_(None)
        )

        # Role-based filtering for kanban cards
        if current_user.role.upper() == "MANAGER":
            # Managers see cards from boards they own or are members of
            query = query.filter(
                or_(
                    KanbanBoard.owner_id == current_user.user_id,
                    KanbanBoard.id.in_(
                        db.query(KanbanBoardMember.board_id).filter(
                            KanbanBoardMember.employee_id == current_user.user_id
                        )
                    )
                )
            )
        elif current_user.role.upper() == "EMPLOYEE":
            # Employees see only cards assigned to them or from boards they're members of
            query = query.filter(
                or_(
                    KanbanCard.assignee_id == current_user.user_id,
                    KanbanBoard.id.in_(
                        db.query(KanbanBoardMember.board_id).filter(
                            KanbanBoardMember.employee_id == current_user.user_id
                        )
                    )
                )
            )

        cards = query.all()

        return [
            {
                "id": str(card.id),
                "title": card.title,
                "description": card.description,
                "due_date": card.due_date.isoformat(),
                "days_overdue": (now.date() - card.due_date.date()).days,
                "priority": card.priority.value if card.priority else "medium",
                "board_name": card.column.board.name if card.column and card.column.board else None,
                "board_id": str(card.column.board.id) if card.column and card.column.board else None,
                "column_name": card.column.name if card.column else None,
                "assignee": card.assignee.first_name + " " + card.assignee.last_name if card.assignee else None,
                "story_points": card.story_points,
                "estimated_hours": card.estimated_hours
            }
            for card in cards
        ]

    async def get_upcoming_due_dates(
        self,
        db: Session,
        current_user: CurrentUser,
        days_ahead: int = 7
    ) -> Dict[str, Any]:
        """Get items due in the next N days"""
        try:
            today = date.today()
            future_date = today + timedelta(days=days_ahead)
            future_datetime = datetime.utcnow() + timedelta(days=days_ahead)

            upcoming_data = {}

            # Upcoming project deadlines
            upcoming_projects = db.query(Project).filter(
                Project.organization_id == current_user.organization_id,
                Project.end_date >= today,
                Project.end_date <= future_date,
                Project.status.in_([ProjectStatus.PLANNING, ProjectStatus.IN_PROGRESS]),
                Project.is_active == True
            ).all()

            # Upcoming task deadlines
            upcoming_tasks = db.query(Task).join(Project).filter(
                Project.organization_id == current_user.organization_id,
                Task.due_date >= today,
                Task.due_date <= future_date,
                Task.status.in_([TaskStatus.TODO, TaskStatus.IN_PROGRESS])
            ).all()

            # Upcoming card deadlines
            upcoming_cards = db.query(KanbanCard).join(KanbanColumn).join(KanbanBoard).filter(
                KanbanBoard.organization_id == current_user.organization_id,
                KanbanCard.due_date >= datetime.utcnow(),
                KanbanCard.due_date <= future_datetime,
                KanbanCard.completed_date.is_(None)
            ).all()

            return {
                "days_ahead": days_ahead,
                "period": {
                    "start_date": today.isoformat(),
                    "end_date": future_date.isoformat()
                },
                "upcoming_projects": len(upcoming_projects),
                "upcoming_tasks": len(upcoming_tasks),
                "upcoming_cards": len(upcoming_cards),
                "total_upcoming": len(upcoming_projects) + len(upcoming_tasks) + len(upcoming_cards)
            }

        except Exception as e:
            logger.error(f"Error getting upcoming due dates: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Error retrieving upcoming due dates"
            )

    async def send_due_date_reminders(
        self,
        db: Session,
        organization_id: str,
        days_ahead: int = 1
    ) -> Dict[str, Any]:
        """Send due date reminders to users"""
        try:
            today = date.today()
            reminder_date = today + timedelta(days=days_ahead)
            reminder_datetime = datetime.utcnow() + timedelta(days=days_ahead)

            reminders_sent = 0

            # Find tasks due soon
            upcoming_tasks = db.query(Task).join(Project).filter(
                Project.organization_id == organization_id,
                Task.due_date == reminder_date,
                Task.status.in_([TaskStatus.TODO, TaskStatus.IN_PROGRESS]),
                Task.assignee_id.isnot(None)
            ).all()

            for task in upcoming_tasks:
                reminder_data = {
                    "type": "task",
                    "id": str(task.id),
                    "title": task.title,
                    "due_date": task.due_date.isoformat(),
                    "days_until_due": days_ahead,
                    "project_name": task.project.name if task.project else None
                }
                
                await notification_manager.notify_due_date_reminder(
                    str(task.assignee_id),
                    reminder_data
                )
                reminders_sent += 1

            # Find cards due soon
            upcoming_cards = db.query(KanbanCard).join(KanbanColumn).join(KanbanBoard).filter(
                KanbanBoard.organization_id == organization_id,
                func.date(KanbanCard.due_date) == reminder_date,
                KanbanCard.completed_date.is_(None),
                KanbanCard.assignee_id.isnot(None)
            ).all()

            for card in upcoming_cards:
                reminder_data = {
                    "type": "card",
                    "id": str(card.id),
                    "title": card.title,
                    "due_date": card.due_date.isoformat(),
                    "days_until_due": days_ahead,
                    "board_name": card.column.board.name if card.column and card.column.board else None
                }
                
                await notification_manager.notify_due_date_reminder(
                    str(card.assignee_id),
                    reminder_data
                )
                reminders_sent += 1

            return {
                "reminders_sent": reminders_sent,
                "reminder_date": reminder_date.isoformat(),
                "days_ahead": days_ahead
            }

        except Exception as e:
            logger.error(f"Error sending due date reminders: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Error sending due date reminders"
            )
