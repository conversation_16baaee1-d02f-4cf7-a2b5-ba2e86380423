/* Calendar Component Styles */

/* FullCalendar custom styling */
.fc {
  font-family: inherit;
}

.fc-toolbar {
  margin-bottom: 1rem;
}

.fc-toolbar-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #1f2937;
}

.fc-button {
  background-color: #F47C20;
  border-color: #F47C20;
  color: white;
  border-radius: 0.375rem;
  padding: 0.5rem 1rem;
  font-weight: 500;
}

.fc-button:hover {
  background-color: #ea580c;
  border-color: #ea580c;
}

.fc-button:focus {
  box-shadow: 0 0 0 3px rgba(244, 124, 32, 0.1);
}

.fc-button-active {
  background-color: #c2410c;
  border-color: #c2410c;
}

.fc-daygrid-day {
  border-color: #e5e7eb;
}

.fc-daygrid-day-top {
  padding: 0.5rem;
}

.fc-daygrid-day-number {
  color: #374151;
  font-weight: 500;
}

.fc-day-today {
  background-color: #eff6ff;
}

.fc-day-today .fc-daygrid-day-number {
  color: #F47C20;
  font-weight: 600;
}

/* Event styling */
.fc-event {
  border-radius: 0.25rem;
  border: none;
  font-size: 0.75rem;
  font-weight: 500;
  margin: 1px 0;
  padding: 2px 4px;
}

.fc-event-title {
  white-space: pre-line;
  line-height: 1.2;
}

/* Attendance events */
.fc-event.event-attendance {
  border-radius: 0.25rem;
}

/* Meeting events */
.fc-event.event-meeting {
  background-color: #0B2A5A;
  border-color: #0B2A5A;
}

/* Deadline events */
.fc-event.event-deadline {
  background-color: #073763;
  border-color: #073763;
}

/* Present status */
.fc-event.present {
  background-color: #10b981;
  border-color: #10b981;
}

/* Absent status */
.fc-event.absent {
  background-color: #ef4444;
  border-color: #ef4444;
}

/* Late status */
.fc-event.late {
  background-color: #f59e0b;
  border-color: #f59e0b;
}

/* Weekend styling */
.fc-day-sat,
.fc-day-sun {
  background-color: #f9fafb;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .fc-toolbar {
    flex-direction: column;
    gap: 0.5rem;
  }
  
  .fc-toolbar-chunk {
    display: flex;
    justify-content: center;
  }
  
  .fc-button {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
  }
  
  .fc-event {
    font-size: 0.625rem;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .fc {
    color: #f9fafb;
  }
  
  .fc-toolbar-title {
    color: #f9fafb;
  }
  
  .fc-daygrid-day {
    border-color: #374151;
  }
  
  .fc-daygrid-day-number {
    color: #d1d5db;
  }
  
  .fc-day-today {
    background-color: #1e3a8a;
  }
  
  .fc-day-sat,
  .fc-day-sun {
    background-color: #111827;
  }
}
