/**
 * RBAC (Role-Based Access Control) Service
 * Centralized permission management for HRM Frontend Application
 * 
 * This service provides:
 * - Role definitions and hierarchies
 * - Permission matrix based on business requirements
 * - Utility functions for permission checking
 * - Scalable structure for future role/permission expansion
 */

// Available roles in the system (ordered by hierarchy)
export const ROLES = {
  SUPER_ADMIN: 'super_admin',
  ADMIN: 'admin',
  HR: 'hr',
  MANAGER: 'manager',
  EMPLOYEE: 'employee'
};

// Role hierarchy for inheritance (higher roles inherit lower role permissions)
export const ROLE_HIERARCHY = {
  [ROLES.SUPER_ADMIN]: 5,
  [ROLES.ADMIN]: 4,
  [ROLES.HR]: 3,
  [ROLES.MANAGER]: 2,
  [ROLES.EMPLOYEE]: 1
};

// Permission types for granular access control
export const PERMISSION_TYPES = {
  FULL: 'full',           // Complete access
  TEAM_ONLY: 'team_only', // Access limited to team members
  ASSIGNED_ONLY: 'assigned_only', // Access limited to assigned items
  SELF_ONLY: 'self_only', // Access limited to own data
  NONE: 'none'            // No access
};

// Comprehensive permission matrix based on requirements
export const PERMISSION_MATRIX = {
  // Dashboard & Overview
  dashboard: {
    [ROLES.SUPER_ADMIN]: PERMISSION_TYPES.FULL,
    [ROLES.ADMIN]: PERMISSION_TYPES.FULL,
    [ROLES.HR]: PERMISSION_TYPES.FULL,
    [ROLES.MANAGER]: PERMISSION_TYPES.FULL,
    [ROLES.EMPLOYEE]: PERMISSION_TYPES.FULL
  },

  // Employee Management
  employeeDirectory: {
    [ROLES.SUPER_ADMIN]: PERMISSION_TYPES.FULL,
    [ROLES.ADMIN]: PERMISSION_TYPES.FULL,
    [ROLES.HR]: PERMISSION_TYPES.FULL,
    [ROLES.MANAGER]: PERMISSION_TYPES.TEAM_ONLY,
    [ROLES.EMPLOYEE]: PERMISSION_TYPES.NONE
  },

  // Profile Management
  profileSelf: {
    [ROLES.SUPER_ADMIN]: PERMISSION_TYPES.FULL,
    [ROLES.ADMIN]: PERMISSION_TYPES.FULL,
    [ROLES.HR]: PERMISSION_TYPES.FULL,
    [ROLES.MANAGER]: PERMISSION_TYPES.FULL,
    [ROLES.EMPLOYEE]: PERMISSION_TYPES.FULL
  },

  profileManagement: {
    [ROLES.SUPER_ADMIN]: PERMISSION_TYPES.FULL,
    [ROLES.ADMIN]: PERMISSION_TYPES.FULL,
    [ROLES.HR]: PERMISSION_TYPES.FULL,
    [ROLES.MANAGER]: PERMISSION_TYPES.TEAM_ONLY,
    [ROLES.EMPLOYEE]: PERMISSION_TYPES.NONE
  },

  // Onboarding Management
  onboardingRead: {
    [ROLES.SUPER_ADMIN]: PERMISSION_TYPES.FULL,
    [ROLES.ADMIN]: PERMISSION_TYPES.FULL,
    [ROLES.HR]: PERMISSION_TYPES.FULL,
    [ROLES.MANAGER]: PERMISSION_TYPES.TEAM_ONLY,
    [ROLES.EMPLOYEE]: PERMISSION_TYPES.NONE
  },

  onboardingCreate: {
    [ROLES.SUPER_ADMIN]: PERMISSION_TYPES.FULL,
    [ROLES.ADMIN]: PERMISSION_TYPES.FULL,
    [ROLES.HR]: PERMISSION_TYPES.FULL,
    [ROLES.MANAGER]: PERMISSION_TYPES.NONE,
    [ROLES.EMPLOYEE]: PERMISSION_TYPES.NONE
  },

  onboardingUpdate: {
    [ROLES.SUPER_ADMIN]: PERMISSION_TYPES.FULL,
    [ROLES.ADMIN]: PERMISSION_TYPES.FULL,
    [ROLES.HR]: PERMISSION_TYPES.FULL,
    [ROLES.MANAGER]: PERMISSION_TYPES.NONE,
    [ROLES.EMPLOYEE]: PERMISSION_TYPES.NONE
  },

  // Attendance Management
  attendanceTrackerSelf: {
    [ROLES.SUPER_ADMIN]: PERMISSION_TYPES.FULL,
    [ROLES.ADMIN]: PERMISSION_TYPES.FULL,
    [ROLES.HR]: PERMISSION_TYPES.FULL,
    [ROLES.MANAGER]: PERMISSION_TYPES.FULL,
    [ROLES.EMPLOYEE]: PERMISSION_TYPES.FULL
  },

  attendanceManagement: {
    [ROLES.SUPER_ADMIN]: PERMISSION_TYPES.FULL,
    [ROLES.ADMIN]: PERMISSION_TYPES.FULL,
    [ROLES.HR]: PERMISSION_TYPES.FULL,
    [ROLES.MANAGER]: PERMISSION_TYPES.TEAM_ONLY,
    [ROLES.EMPLOYEE]: PERMISSION_TYPES.NONE
  },

  // Leave Management
  leaveRequestsSelf: {
    [ROLES.SUPER_ADMIN]: PERMISSION_TYPES.FULL,
    [ROLES.ADMIN]: PERMISSION_TYPES.FULL,
    [ROLES.HR]: PERMISSION_TYPES.FULL,
    [ROLES.MANAGER]: PERMISSION_TYPES.FULL,
    [ROLES.EMPLOYEE]: PERMISSION_TYPES.FULL
  },

  leaveManagement: {
    [ROLES.SUPER_ADMIN]: PERMISSION_TYPES.FULL,
    [ROLES.ADMIN]: PERMISSION_TYPES.FULL,
    [ROLES.HR]: PERMISSION_TYPES.FULL,
    [ROLES.MANAGER]: PERMISSION_TYPES.TEAM_ONLY,
    [ROLES.EMPLOYEE]: PERMISSION_TYPES.NONE
  },

  // Shift Scheduling
  shiftScheduling: {
    [ROLES.SUPER_ADMIN]: PERMISSION_TYPES.FULL,
    [ROLES.ADMIN]: PERMISSION_TYPES.FULL,
    [ROLES.HR]: PERMISSION_TYPES.FULL,
    [ROLES.MANAGER]: PERMISSION_TYPES.TEAM_ONLY,
    [ROLES.EMPLOYEE]: PERMISSION_TYPES.NONE
  },

  // Payroll Management
  payrollPayslipsSelf: {
    [ROLES.SUPER_ADMIN]: PERMISSION_TYPES.FULL,
    [ROLES.ADMIN]: PERMISSION_TYPES.FULL,
    [ROLES.HR]: PERMISSION_TYPES.FULL,
    [ROLES.MANAGER]: PERMISSION_TYPES.FULL,
    [ROLES.EMPLOYEE]: PERMISSION_TYPES.FULL
  },

  payrollManagement: {
    [ROLES.SUPER_ADMIN]: PERMISSION_TYPES.FULL,
    [ROLES.ADMIN]: PERMISSION_TYPES.FULL,
    [ROLES.HR]: PERMISSION_TYPES.FULL,
    [ROLES.MANAGER]: PERMISSION_TYPES.NONE,
    [ROLES.EMPLOYEE]: PERMISSION_TYPES.NONE
  },

  // Project Management (restricted to SuperAdmin, Manager, Employee only)
  projectKanbanBoards: {
    [ROLES.SUPER_ADMIN]: PERMISSION_TYPES.FULL,
    [ROLES.ADMIN]: PERMISSION_TYPES.NONE,
    [ROLES.HR]: PERMISSION_TYPES.NONE,
    [ROLES.MANAGER]: PERMISSION_TYPES.TEAM_ONLY,
    [ROLES.EMPLOYEE]: PERMISSION_TYPES.ASSIGNED_ONLY
  },

  taskManagement: {
    [ROLES.SUPER_ADMIN]: PERMISSION_TYPES.FULL,
    [ROLES.ADMIN]: PERMISSION_TYPES.NONE,
    [ROLES.HR]: PERMISSION_TYPES.NONE,
    [ROLES.MANAGER]: PERMISSION_TYPES.TEAM_ONLY,
    [ROLES.EMPLOYEE]: PERMISSION_TYPES.ASSIGNED_ONLY
  },

  // Time Tracking
  timeTracker: {
    [ROLES.SUPER_ADMIN]: PERMISSION_TYPES.FULL,
    [ROLES.ADMIN]: PERMISSION_TYPES.FULL,
    [ROLES.HR]: PERMISSION_TYPES.FULL,
    [ROLES.MANAGER]: PERMISSION_TYPES.FULL,
    [ROLES.EMPLOYEE]: PERMISSION_TYPES.FULL
  },

  // Timesheet Approval
  timesheetApproval: {
    [ROLES.SUPER_ADMIN]: PERMISSION_TYPES.FULL,
    [ROLES.ADMIN]: PERMISSION_TYPES.FULL,
    [ROLES.HR]: PERMISSION_TYPES.FULL,
    [ROLES.MANAGER]: PERMISSION_TYPES.TEAM_ONLY,
    [ROLES.EMPLOYEE]: PERMISSION_TYPES.NONE
  },

  // Ticketing System
  ticketingSystemSelf: {
    [ROLES.SUPER_ADMIN]: PERMISSION_TYPES.FULL,
    [ROLES.ADMIN]: PERMISSION_TYPES.FULL,
    [ROLES.HR]: PERMISSION_TYPES.FULL,
    [ROLES.MANAGER]: PERMISSION_TYPES.FULL,
    [ROLES.EMPLOYEE]: PERMISSION_TYPES.FULL
  },

  ticketManagement: {
    [ROLES.SUPER_ADMIN]: PERMISSION_TYPES.FULL,
    [ROLES.ADMIN]: PERMISSION_TYPES.FULL,
    [ROLES.HR]: PERMISSION_TYPES.FULL,
    [ROLES.MANAGER]: PERMISSION_TYPES.TEAM_ONLY,
    [ROLES.EMPLOYEE]: PERMISSION_TYPES.NONE
  },

  // Delegation & Approvals
  delegationManagement: {
    [ROLES.SUPER_ADMIN]: PERMISSION_TYPES.FULL,
    [ROLES.ADMIN]: PERMISSION_TYPES.FULL,
    [ROLES.HR]: PERMISSION_TYPES.FULL,
    [ROLES.MANAGER]: PERMISSION_TYPES.TEAM_ONLY,
    [ROLES.EMPLOYEE]: PERMISSION_TYPES.NONE
  },

  approvals: {
    [ROLES.SUPER_ADMIN]: PERMISSION_TYPES.FULL,
    [ROLES.ADMIN]: PERMISSION_TYPES.FULL,
    [ROLES.HR]: PERMISSION_TYPES.FULL,
    [ROLES.MANAGER]: PERMISSION_TYPES.TEAM_ONLY,
    [ROLES.EMPLOYEE]: PERMISSION_TYPES.NONE
  },

  // Surveys
  surveysParticipation: {
    [ROLES.SUPER_ADMIN]: PERMISSION_TYPES.FULL,
    [ROLES.ADMIN]: PERMISSION_TYPES.FULL,
    [ROLES.HR]: PERMISSION_TYPES.FULL,
    [ROLES.MANAGER]: PERMISSION_TYPES.FULL,
    [ROLES.EMPLOYEE]: PERMISSION_TYPES.FULL
  },

  surveyManagement: {
    [ROLES.SUPER_ADMIN]: PERMISSION_TYPES.FULL,
    [ROLES.ADMIN]: PERMISSION_TYPES.FULL,
    [ROLES.HR]: PERMISSION_TYPES.FULL,
    [ROLES.MANAGER]: PERMISSION_TYPES.NONE,
    [ROLES.EMPLOYEE]: PERMISSION_TYPES.NONE
  },

  // Performance Reviews
  performanceReviewsSelf: {
    [ROLES.SUPER_ADMIN]: PERMISSION_TYPES.FULL,
    [ROLES.ADMIN]: PERMISSION_TYPES.FULL,
    [ROLES.HR]: PERMISSION_TYPES.FULL,
    [ROLES.MANAGER]: PERMISSION_TYPES.FULL,
    [ROLES.EMPLOYEE]: PERMISSION_TYPES.FULL
  },

  performanceReviewsTeam: {
    [ROLES.SUPER_ADMIN]: PERMISSION_TYPES.FULL,
    [ROLES.ADMIN]: PERMISSION_TYPES.FULL,
    [ROLES.HR]: PERMISSION_TYPES.FULL,
    [ROLES.MANAGER]: PERMISSION_TYPES.TEAM_ONLY,
    [ROLES.EMPLOYEE]: PERMISSION_TYPES.NONE
  },

  // Calendar & Events
  calendarEvents: {
    [ROLES.SUPER_ADMIN]: PERMISSION_TYPES.FULL,
    [ROLES.ADMIN]: PERMISSION_TYPES.FULL,
    [ROLES.HR]: PERMISSION_TYPES.FULL,
    [ROLES.MANAGER]: PERMISSION_TYPES.FULL,
    [ROLES.EMPLOYEE]: PERMISSION_TYPES.FULL
  },

  calendarEventManagement: {
    [ROLES.SUPER_ADMIN]: PERMISSION_TYPES.FULL,
    [ROLES.ADMIN]: PERMISSION_TYPES.FULL,
    [ROLES.HR]: PERMISSION_TYPES.FULL,
    [ROLES.MANAGER]: PERMISSION_TYPES.TEAM_ONLY,
    [ROLES.EMPLOYEE]: PERMISSION_TYPES.SELF_ONLY
  },

  // System Administration
  systemSettings: {
    [ROLES.SUPER_ADMIN]: PERMISSION_TYPES.FULL,
    [ROLES.ADMIN]: PERMISSION_TYPES.FULL,
    [ROLES.HR]: PERMISSION_TYPES.NONE,
    [ROLES.MANAGER]: PERMISSION_TYPES.NONE,
    [ROLES.EMPLOYEE]: PERMISSION_TYPES.NONE
  },

  systemLogs: {
    [ROLES.SUPER_ADMIN]: PERMISSION_TYPES.FULL,
    [ROLES.ADMIN]: PERMISSION_TYPES.FULL,
    [ROLES.HR]: PERMISSION_TYPES.NONE,
    [ROLES.MANAGER]: PERMISSION_TYPES.NONE,
    [ROLES.EMPLOYEE]: PERMISSION_TYPES.NONE
  }
};

/**
 * Core permission checking functions
 */

/**
 * Check if a role has any access to a specific permission
 * @param {string} role - User role
 * @param {string} permission - Permission key
 * @returns {boolean} - Whether user has any access
 */
export function hasPermission(role, permission) {
  if (!role || !permission) return false;
  
  const permissionConfig = PERMISSION_MATRIX[permission];
  if (!permissionConfig) return false;
  
  const userPermission = permissionConfig[role];
  return userPermission && userPermission !== PERMISSION_TYPES.NONE;
}

/**
 * Get the specific permission type for a role and permission
 * @param {string} role - User role
 * @param {string} permission - Permission key
 * @returns {string} - Permission type (full, team_only, assigned_only, self_only, none)
 */
export function getPermissionType(role, permission) {
  if (!role || !permission) return PERMISSION_TYPES.NONE;
  
  const permissionConfig = PERMISSION_MATRIX[permission];
  if (!permissionConfig) return PERMISSION_TYPES.NONE;
  
  return permissionConfig[role] || PERMISSION_TYPES.NONE;
}

/**
 * Check if user has full access to a permission
 * @param {string} role - User role
 * @param {string} permission - Permission key
 * @returns {boolean} - Whether user has full access
 */
export function hasFullAccess(role, permission) {
  return getPermissionType(role, permission) === PERMISSION_TYPES.FULL;
}

/**
 * Check if user has team-only access to a permission
 * @param {string} role - User role
 * @param {string} permission - Permission key
 * @returns {boolean} - Whether user has team-only access
 */
export function hasTeamOnlyAccess(role, permission) {
  return getPermissionType(role, permission) === PERMISSION_TYPES.TEAM_ONLY;
}

/**
 * Check if user has assigned-only access to a permission
 * @param {string} role - User role
 * @param {string} permission - Permission key
 * @returns {boolean} - Whether user has assigned-only access
 */
export function hasAssignedOnlyAccess(role, permission) {
  return getPermissionType(role, permission) === PERMISSION_TYPES.ASSIGNED_ONLY;
}

/**
 * Check if user has self-only access to a permission
 * @param {string} role - User role
 * @param {string} permission - Permission key
 * @returns {boolean} - Whether user has self-only access
 */
export function hasSelfOnlyAccess(role, permission) {
  return getPermissionType(role, permission) === PERMISSION_TYPES.SELF_ONLY;
}

/**
 * Check if a role is higher in hierarchy than another role
 * @param {string} role1 - First role
 * @param {string} role2 - Second role
 * @returns {boolean} - Whether role1 is higher than role2
 */
export function isRoleHigher(role1, role2) {
  const level1 = ROLE_HIERARCHY[role1] || 0;
  const level2 = ROLE_HIERARCHY[role2] || 0;
  return level1 > level2;
}

/**
 * Get all roles that are equal or lower in hierarchy
 * @param {string} role - User role
 * @returns {string[]} - Array of roles
 */
export function getSubordinateRoles(role) {
  const userLevel = ROLE_HIERARCHY[role] || 0;
  return Object.keys(ROLE_HIERARCHY).filter(r => ROLE_HIERARCHY[r] <= userLevel);
}

/**
 * Validate if a role exists in the system
 * @param {string} role - Role to validate
 * @returns {boolean} - Whether role is valid
 */
export function isValidRole(role) {
  return Object.values(ROLES).includes(role);
}
