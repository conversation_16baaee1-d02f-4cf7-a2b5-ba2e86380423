"""
Simple Onboarding API
Simplified version that works with the current database schema
"""

from fastapi import APIRouter, HTTPException, status, Depends
from sqlalchemy.orm import Session
from sqlalchemy import text
from typing import Dict, Any
from uuid import UUID
import uuid
from pydantic import BaseModel, EmailStr
from datetime import date, datetime
import secrets
import string

from ..core.security import get_current_user, require_permission, CurrentUser, SecurityManager
from ..core.rbac import Permissions
from ..db.session import get_db
from ..core.config import settings

# Email imports
import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart

router = APIRouter()

# Email function
async def send_welcome_email_simple(
    employee_name: str,
    employee_email: str,
    employee_id: str,
    temporary_password: str
) -> bool:
    """Send welcome email to new employee"""
    try:
        # SMTP settings
        smtp_server = settings.SMTP_SERVER
        smtp_port = settings.SMTP_PORT
        email_user = settings.EMAIL_USER
        email_password = settings.EMAIL_PASSWORD
        from_name = settings.FROM_NAME

        # Create message
        msg = MIMEMultipart('alternative')
        msg['From'] = f"{from_name} <{email_user}>"
        msg['To'] = employee_email
        msg['Subject'] = f"🎉 Welcome to {from_name} - Your Account is Ready!"

        # HTML email content
        html_body = f"""
        <html>
        <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
            <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
                <h2 style="color: #2c5aa0;">🎉 Welcome to {from_name}!</h2>

                <p>Dear <strong>{employee_name}</strong>,</p>

                <p>Welcome to the team! We're excited to have you on board.</p>

                <div style="background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;">
                    <h3 style="color: #2c5aa0; margin-top: 0;">Your Login Credentials</h3>
                    <p><strong>Employee ID:</strong> {employee_id}</p>
                    <p><strong>Email:</strong> {employee_email}</p>
                    <p><strong>Temporary Password:</strong> <code style="background-color: #e9ecef; padding: 2px 4px; border-radius: 4px;">{temporary_password}</code></p>
                </div>

                <div style="background-color: #fff3cd; padding: 15px; border-radius: 8px; border-left: 4px solid #ffc107;">
                    <p><strong>⚠️ Important:</strong> Please change your password after your first login for security.</p>
                </div>

                <h3 style="color: #2c5aa0;">Next Steps:</h3>
                <ol>
                    <li>Visit our HRMS portal: <a href="http://localhost:5173">http://localhost:5173</a></li>
                    <li>Log in with your credentials above</li>
                    <li>Complete your profile information</li>
                    <li>Review company policies and procedures</li>
                </ol>

                <p>If you have any questions or need assistance, please don't hesitate to reach out to our HR team.</p>

                <p>Welcome aboard!</p>

                <p>Best regards,<br>
                <strong>{from_name} HR Team</strong></p>

                <hr style="border: none; border-top: 1px solid #eee; margin: 30px 0;">
                <p style="font-size: 12px; color: #666;">
                    This email was sent automatically by the {from_name} HRMS system.
                </p>
            </div>
        </body>
        </html>
        """

        msg.attach(MIMEText(html_body, 'html'))

        # Send email
        server = smtplib.SMTP(smtp_server, smtp_port)
        server.starttls()
        server.login(email_user, email_password)
        server.send_message(msg)
        server.quit()

        return True

    except Exception as e:
        print(f"Error sending welcome email: {e}")
        return False

# Simple schemas
class SimpleOnboardingRequest(BaseModel):
    """Simple onboarding request"""
    employee_name: str
    employee_email: EmailStr
    start_date: date = None

class SimpleOnboardingResponse(BaseModel):
    """Simple onboarding response"""
    success: bool
    employee_id: str
    employee_name: str
    email: str
    temporary_password: str
    message: str

@router.post("/simple-onboard", response_model=SimpleOnboardingResponse)
async def simple_onboard_employee(
    request: SimpleOnboardingRequest,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.ONBOARDING_CREATE))
):
    """
    Simple onboarding: Create employee with minimal database operations
    """
    try:
        # Parse name
        name_parts = request.employee_name.strip().split()
        first_name = name_parts[0] if name_parts else "Unknown"
        last_name = " ".join(name_parts[1:]) if len(name_parts) > 1 else ""
        
        # Check if email already exists
        check_email_query = text("SELECT COUNT(*) FROM employees WHERE email = :email")
        email_count = db.execute(check_email_query, {"email": request.employee_email}).scalar()
        
        if email_count > 0:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Employee with this email already exists"
            )
        
        # Generate employee ID
        count_query = text("SELECT COUNT(*) FROM employees")
        employee_count = db.execute(count_query).scalar()
        employee_id = f"EMP{str(employee_count + 1).zfill(4)}"
        
        # Generate temporary password
        temp_password = ''.join(secrets.choice(string.ascii_letters + string.digits) for _ in range(12))
        
        # Create UUIDs for both records
        employee_uuid = uuid.uuid4()
        user_uuid = uuid.uuid4()
        start_date = request.start_date or date.today()

        # Create employee record with user_id
        insert_employee_query = text("""
            INSERT INTO employees (
                id, user_id, employee_id, first_name, last_name, email,
                hire_date, created_at, updated_at, is_active
            ) VALUES (
                :id, :user_id, :employee_id, :first_name, :last_name, :email,
                :hire_date, :created_at, :updated_at, :is_active
            )
        """)

        db.execute(insert_employee_query, {
            'id': employee_uuid,
            'user_id': user_uuid,
            'employee_id': employee_id,
            'first_name': first_name,
            'last_name': last_name,
            'email': request.employee_email,
            'hire_date': start_date,
            'created_at': datetime.utcnow(),
            'updated_at': datetime.utcnow(),
            'is_active': True
        })

        # Create user account
        insert_user_query = text("""
            INSERT INTO users (
                id, email, password, role, is_active, created_at, updated_at
            ) VALUES (
                :id, :email, :password, :role, :is_active, :created_at, :updated_at
            )
        """)

        db.execute(insert_user_query, {
            'id': user_uuid,
            'email': request.employee_email,
            'password': SecurityManager.get_password_hash(temp_password),
            'role': 'EMPLOYEE',
            'is_active': True,
            'created_at': datetime.utcnow(),
            'updated_at': datetime.utcnow()
        })
        
        # Commit the transaction
        db.commit()

        # Send welcome email
        try:
            await send_welcome_email_simple(
                employee_name=request.employee_name,
                employee_email=request.employee_email,
                employee_id=employee_id,
                temporary_password=temp_password
            )
            email_sent = True
        except Exception as e:
            print(f"Warning: Failed to send welcome email: {e}")
            email_sent = False

        message = f"Employee {request.employee_name} created successfully! Login credentials generated."
        if email_sent:
            message += " Welcome email sent."
        else:
            message += " Please manually share the credentials."

        return SimpleOnboardingResponse(
            success=True,
            employee_id=employee_id,
            employee_name=request.employee_name,
            email=request.employee_email,
            temporary_password=temp_password,
            message=message
        )
        
    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        print(f"Error in simple onboarding: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create employee: {str(e)}"
        )

@router.get("/test")
async def test_endpoint():
    """Test endpoint to verify the router is working"""
    return {"message": "Simple onboarding API is working!"}

@router.get("/employees/count")
async def get_employee_count(db: Session = Depends(get_db)):
    """Get employee count for testing"""
    try:
        count_query = text("SELECT COUNT(*) FROM employees")
        count = db.execute(count_query).scalar()
        return {"count": count}
    except Exception as e:
        return {"error": str(e)}
