from core.repositories.activity_admin import AdminActivityRepository

class AdminActivityService:
    def __init__(self) -> None:
        self.repository = AdminActivityRepository()

    # def logActivity(self, message):
    #     return self.repository.log_activity(message)
    def logActivity(self, message, user_id=None):
        return self.repository.log_activity(message, user_id=user_id)
    
    def fetchAllActivities(self):
        activities = self.repository.fetch_all()
        total = len(activities)
        return activities, total

    def getActivityById(self, id):
        return self.repository.fetch_by_id(id)

    def deleteActivity(self, id):
        return self.repository.delete_activity(id)
