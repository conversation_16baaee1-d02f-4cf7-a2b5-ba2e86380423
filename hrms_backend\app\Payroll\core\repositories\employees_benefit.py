from core.models.employee_benefit_pivot import EmployeeBenefitsPivotModel
from core.databases.database import db
from sqlalchemy.exc import IntegrityError
from sqlalchemy.exc import NoResultFound

class EmployeesBenefitRepository:
    def __init__(self) -> None:
        pass

    @classmethod
    def create(self, employee_id, benefits_id):
        # Check if the benefit is already assigned to the employee
        existing_benefit = EmployeeBenefitsPivotModel.query.filter_by(
            employee_id=employee_id, benefits_id=benefits_id
        ).first()

        if existing_benefit:
            # Benefit already assigned to this employee
            raise ValueError(f"Benefit ID {benefits_id} is already assigned to Employee ID {employee_id}.")

        # Create a new pivot entry since it doesn't exist
        salary_pivot = EmployeeBenefitsPivotModel(
            employee_id=employee_id,
            benefits_id=benefits_id,
        )
        try:
            db.session.add(salary_pivot)
            db.session.commit()
        except IntegrityError:
            db.session.rollback()
            raise ValueError("An error occurred while saving the benefit. Please try again.")

        return salary_pivot
    
    
    @classmethod
    def getAttachedEmployee(self, id):
        return EmployeeBenefitsPivotModel.query.filter_by(employee_id=id).all()
    
    @classmethod
    def getAttachedEmployeeBenefit(cls, employee_id, benefit_id):
        """
        Check if a benefit is already assigned to a given employee.
        """
        return EmployeeBenefitsPivotModel.query.filter_by(
            employee_id=employee_id, benefits_id=benefit_id
        ).first()
        
    # @classmethod
    # def delete(self, ids):
    #     db.session.query(EmployeeBenefitsPivotModel).filter(EmployeeBenefitsPivotModel.id.in_(ids)).delete(synchronize_session=False)      
    #     db.session.commit()
    #     return 
    
    @classmethod
    def getBenefitsByEmployeeId(cls, employee_id):
        """
        Get all benefits assigned to a specific employee.
        """
        return EmployeeBenefitsPivotModel.query.filter_by(employee_id=employee_id).all()

    @classmethod
    def getBenefitByEmployeeAndBenefitId(cls, employee_id, benefit_id):
        """
        Get a specific benefit assigned to an employee.
        """
        return EmployeeBenefitsPivotModel.query.filter_by(
            employee_id=employee_id,
            benefits_id=benefit_id
        ).first()

    # @classmethod
    # def deleteBenefit(self, employee_id, benefit_id):
    #     # Find the association between the employee and the benefit
    #     benefit_association = self.session.query(EmployeeBenefitsPivotModel).filter_by(
    #         employee_id=employee_id,
    #         benefit_id=benefit_id
    #     ).first()

    #     if not benefit_association:
    #         raise NoResultFound("Benefit association not found")

    #     # Delete the association
    #     self.session.delete(benefit_association)
    #     self.session.commit()  # Commit the changes to the database
    
    @classmethod
    def delete(cls, employee_id, benefit_ids):
        """
        Delete specific benefit pivot records for a given employee.
        
        Args:
            employee_id (int): The ID of the employee whose benefits should be deleted.
            benefit_ids (list): A list of IDs of the benefit pivots to delete.
        """
        db.session.query(EmployeeBenefitsPivotModel).filter(
            EmployeeBenefitsPivotModel.employee_id == employee_id,
            EmployeeBenefitsPivotModel.id.in_(benefit_ids)
        ).delete(synchronize_session=False)
        db.session.commit()