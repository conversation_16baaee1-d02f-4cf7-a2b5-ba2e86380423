#!/usr/bin/env python3
"""
Debug Employee Creation Issue
Test employee creation endpoint to identify validation problems
"""

import requests
import json
from datetime import datetime, timedelta
from jose import jwt

BASE_URL = 'http://localhost:8001'
SECRET_KEY = 'your-super-secret-jwt-key-change-this-in-production'
ORG_ID = 'fd3d7ca7-bad7-44c6-84a8-e74fc40bba0e'
EMPLOYEE_UUID = '8e750866-f951-40d4-8196-6b9e8f753117'

def create_test_token():
    """Create a test JWT token"""
    test_user_data = {
        'sub': EMPLOYEE_UUID,
        'email': '<EMAIL>', 
        'role': 'hr',
        'organization_id': ORG_ID,
        'exp': datetime.utcnow() + timedelta(hours=24)
    }
    return jwt.encode(test_user_data, SECRET_KEY, algorithm='HS256')

def test_employee_creation():
    """Test employee creation with various data combinations"""
    token = create_test_token()
    headers = {'Authorization': f'Bearer {token}', 'Content-Type': 'application/json'}
    
    print('DEBUGGING EMPLOYEE CREATION ISSUE')
    print('=' * 50)
    
    # Test 1: Minimal required data
    print('\nTest 1: Minimal required data')
    minimal_employee = {
        'employee_id': 'DEBUG001',
        'first_name': 'Debug',
        'last_name': 'Test',
        'email': '<EMAIL>',
        'role': 'employee',
        'hire_date': '2024-01-01'
    }
    
    test_creation(headers, minimal_employee, 'Minimal Employee')
    
    # Test 2: With phone number
    print('\nTest 2: With phone number')
    with_phone = {
        'employee_id': 'DEBUG002',
        'first_name': 'Debug',
        'last_name': 'Phone',
        'email': '<EMAIL>',
        'phone': '+1234567890',
        'role': 'employee',
        'hire_date': '2024-01-01'
    }
    
    test_creation(headers, with_phone, 'Employee with Phone')
    
    # Test 3: Different role
    print('\nTest 3: Different role')
    manager_employee = {
        'employee_id': 'DEBUG003',
        'first_name': 'Debug',
        'last_name': 'Manager',
        'email': '<EMAIL>',
        'role': 'manager',
        'hire_date': '2024-01-01'
    }
    
    test_creation(headers, manager_employee, 'Manager Employee')

def test_creation(headers, employee_data, description):
    """Test creating an employee with given data"""
    print(f'\nTesting: {description}')
    print('Data:', json.dumps(employee_data, indent=2))

    try:
        response = requests.post(f'{BASE_URL}/api/employees/', headers=headers, json=employee_data)
        print(f'Status: {response.status_code}')
        print(f'Response Headers: {dict(response.headers)}')

        if response.status_code in [200, 201]:
            data = response.json()
            print('SUCCESS!')
            print(f'Created: {data.get("first_name")} {data.get("last_name")} - Role: {data.get("role")}')
        else:
            print('FAILED!')
            print(f'Full Response: {response.text}')
            try:
                error_data = response.json()
                print(f'Error JSON: {json.dumps(error_data, indent=2)}')
            except:
                print('Could not parse response as JSON')

    except Exception as e:
        print(f'ERROR: {e}')

def test_direct_database_creation():
    """Test creating employee directly in database to see exact error"""
    print('\n' + '='*50)
    print('TESTING DIRECT DATABASE CREATION')
    print('='*50)

    try:
        import sys
        sys.path.append('.')

        from app.db.session import get_db
        from app.db.models.employee import Employee
        from app.schemas.employee import EmployeeCreate
        from datetime import date

        # Get database session
        db = next(get_db())

        # Test data
        employee_data = EmployeeCreate(
            employee_id='DIRECT001',
            first_name='Direct',
            last_name='Test',
            email='<EMAIL>',
            role='employee',
            hire_date=date(2024, 1, 1)
        )

        print('Creating employee directly in database...')
        print(f'Data: {employee_data.model_dump()}')

        # Create employee
        employee = Employee(
            **employee_data.model_dump(exclude={'skills'}),
            organization_id='fd3d7ca7-bad7-44c6-84a8-e74fc40bba0e',
            skills=employee_data.skills or []
        )

        print('Employee object created, adding to database...')
        db.add(employee)
        print('Added to session, committing...')
        db.commit()
        print('SUCCESS! Employee created directly in database')

        db.refresh(employee)
        print(f'Created employee: {employee.first_name} {employee.last_name} - ID: {employee.id}')

    except Exception as e:
        print(f'DIRECT DATABASE ERROR: {e}')
        import traceback
        traceback.print_exc()
        if 'db' in locals():
            db.rollback()

if __name__ == '__main__':
    test_employee_creation()
    test_direct_database_creation()
