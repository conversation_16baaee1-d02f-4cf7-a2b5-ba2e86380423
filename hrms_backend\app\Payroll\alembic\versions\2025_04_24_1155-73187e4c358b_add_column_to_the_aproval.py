"""add column to the aproval

Revision ID: 73187e4c358b
Revises: ad787f558cd3
Create Date: 2025-04-24 11:55:50.729995

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '73187e4c358b'
down_revision: Union[str, None] = 'ad787f558cd3'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    op.add_column("approvals", sa.Column('name', sa.String(length=150), nullable=True))
    op.add_column("approvals", sa.Column('email', sa.String(length=150), nullable=True))
    op.add_column("approvals", sa.Column('phone', sa.String(length=150), nullable=True))


def downgrade() -> None:
    op.drop_column("approvals", "name")
    op.drop_column("approvals", "email")
    op.drop_column("approvals", "phone")
