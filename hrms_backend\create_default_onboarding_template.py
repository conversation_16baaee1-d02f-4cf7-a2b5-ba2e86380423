"""
Create Default Onboarding Template
Creates a comprehensive default onboarding template with modern best practices
"""

import asyncio
import sys
import os
from datetime import datetime, date, timedelta
import uuid

# Add the app directory to the Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'app'))

from app.db.session import <PERSON><PERSON>ocal
from app.db.models.onboarding import WorkflowTemplate

def create_default_template():
    """Create a comprehensive default onboarding template"""
    
    db = SessionLocal()
    
    try:
        # Check if default template already exists
        existing_template = db.query(WorkflowTemplate).filter(
            WorkflowTemplate.name == "Standard Employee Onboarding",
            WorkflowTemplate.is_default == True
        ).first()
        
        if existing_template:
            print("✅ Default onboarding template already exists")
            return
        
        # Define comprehensive task templates
        task_templates = [
            {
                "title": "Welcome & Introduction",
                "description": "Send welcome email and introduction materials to new employee",
                "task_type": "communication",
                "order_index": 1,
                "assigned_to_role": "HR",
                "estimated_hours": 1,
                "is_mandatory": True,
                "auto_complete": False,
                "requires_approval": False,
                "task_data": {
                    "category": "welcome",
                    "priority": "high",
                    "instructions": "Send personalized welcome email with company handbook and first-day information"
                }
            },
            {
                "title": "IT Setup - Hardware & Accounts",
                "description": "Provision laptop, phone, and create necessary IT accounts",
                "task_type": "it_setup",
                "order_index": 2,
                "assigned_to_role": "IT",
                "estimated_hours": 3,
                "is_mandatory": True,
                "auto_complete": False,
                "requires_approval": True,
                "task_data": {
                    "category": "it_setup",
                    "priority": "high",
                    "checklist": [
                        "Laptop/Desktop setup",
                        "Phone/Mobile device",
                        "Email account creation",
                        "System access credentials",
                        "VPN setup",
                        "Security software installation"
                    ]
                }
            },
            {
                "title": "HR Documentation",
                "description": "Complete employment contracts, tax forms, and benefits enrollment",
                "task_type": "documentation",
                "order_index": 3,
                "assigned_to_role": "HR",
                "estimated_hours": 2,
                "is_mandatory": True,
                "auto_complete": False,
                "requires_approval": True,
                "task_data": {
                    "category": "documentation",
                    "priority": "high",
                    "documents": [
                        "Employment contract",
                        "Tax forms (W-4, etc.)",
                        "Benefits enrollment",
                        "Emergency contact information",
                        "Direct deposit setup",
                        "Handbook acknowledgment"
                    ]
                }
            },
            {
                "title": "Workspace Setup",
                "description": "Assign desk/office space and provide necessary office supplies",
                "task_type": "workspace",
                "order_index": 4,
                "assigned_to_role": "FACILITIES",
                "estimated_hours": 1,
                "is_mandatory": True,
                "auto_complete": False,
                "requires_approval": False,
                "task_data": {
                    "category": "workspace",
                    "priority": "medium",
                    "items": [
                        "Desk assignment",
                        "Chair and ergonomic setup",
                        "Office supplies",
                        "Parking assignment",
                        "Building access card",
                        "Locker assignment (if applicable)"
                    ]
                }
            },
            {
                "title": "Company Orientation",
                "description": "Attend comprehensive company orientation session",
                "task_type": "training",
                "order_index": 5,
                "assigned_to_role": "HR",
                "estimated_hours": 4,
                "is_mandatory": True,
                "auto_complete": False,
                "requires_approval": False,
                "task_data": {
                    "category": "orientation",
                    "priority": "high",
                    "topics": [
                        "Company history and mission",
                        "Organizational structure",
                        "Company policies and procedures",
                        "Code of conduct",
                        "Safety procedures",
                        "Benefits overview"
                    ]
                }
            },
            {
                "title": "Department Introduction",
                "description": "Meet with department head and team members",
                "task_type": "meeting",
                "order_index": 6,
                "assigned_to_role": "MANAGER",
                "estimated_hours": 2,
                "is_mandatory": True,
                "auto_complete": False,
                "requires_approval": False,
                "task_data": {
                    "category": "team_introduction",
                    "priority": "high",
                    "activities": [
                        "Meet department head",
                        "Team introductions",
                        "Department overview",
                        "Role expectations",
                        "Current projects overview",
                        "Team dynamics explanation"
                    ]
                }
            },
            {
                "title": "Buddy/Mentor Assignment",
                "description": "Assign and introduce workplace buddy or mentor",
                "task_type": "mentoring",
                "order_index": 7,
                "assigned_to_role": "HR",
                "estimated_hours": 1,
                "is_mandatory": False,
                "auto_complete": False,
                "requires_approval": False,
                "task_data": {
                    "category": "mentoring",
                    "priority": "medium",
                    "guidelines": [
                        "Select appropriate buddy/mentor",
                        "Introduce new employee to buddy",
                        "Explain buddy program expectations",
                        "Schedule regular check-ins",
                        "Provide buddy program guidelines"
                    ]
                }
            },
            {
                "title": "Role-Specific Training",
                "description": "Complete job-specific training and skill development",
                "task_type": "training",
                "order_index": 8,
                "assigned_to_role": "MANAGER",
                "estimated_hours": 8,
                "is_mandatory": True,
                "auto_complete": False,
                "requires_approval": True,
                "task_data": {
                    "category": "role_training",
                    "priority": "high",
                    "components": [
                        "Job-specific software training",
                        "Process and procedure training",
                        "Industry-specific knowledge",
                        "Compliance training",
                        "Skills assessment",
                        "Certification requirements"
                    ]
                }
            },
            {
                "title": "Security & Compliance Training",
                "description": "Complete mandatory security and compliance training modules",
                "task_type": "compliance",
                "order_index": 9,
                "assigned_to_role": "SECURITY",
                "estimated_hours": 3,
                "is_mandatory": True,
                "auto_complete": False,
                "requires_approval": True,
                "task_data": {
                    "category": "security_compliance",
                    "priority": "high",
                    "modules": [
                        "Information security awareness",
                        "Data protection and privacy",
                        "Cybersecurity best practices",
                        "Industry compliance requirements",
                        "Emergency procedures",
                        "Incident reporting"
                    ]
                }
            },
            {
                "title": "30-Day Check-in",
                "description": "Conduct 30-day performance and satisfaction check-in",
                "task_type": "review",
                "order_index": 10,
                "assigned_to_role": "MANAGER",
                "estimated_hours": 1,
                "is_mandatory": True,
                "auto_complete": False,
                "requires_approval": False,
                "task_data": {
                    "category": "check_in",
                    "priority": "medium",
                    "schedule": "30 days after start date",
                    "topics": [
                        "Performance feedback",
                        "Goal setting",
                        "Challenges and concerns",
                        "Training effectiveness",
                        "Job satisfaction",
                        "Support needs"
                    ]
                }
            },
            {
                "title": "60-Day Review",
                "description": "Formal 60-day performance review and goal adjustment",
                "task_type": "review",
                "order_index": 11,
                "assigned_to_role": "MANAGER",
                "estimated_hours": 2,
                "is_mandatory": True,
                "auto_complete": False,
                "requires_approval": True,
                "task_data": {
                    "category": "performance_review",
                    "priority": "medium",
                    "schedule": "60 days after start date",
                    "components": [
                        "Performance evaluation",
                        "Goal achievement assessment",
                        "Skill development progress",
                        "Career development discussion",
                        "Feedback collection",
                        "Action plan creation"
                    ]
                }
            },
            {
                "title": "90-Day Final Review",
                "description": "Complete 90-day probationary review and confirmation",
                "task_type": "review",
                "order_index": 12,
                "assigned_to_role": "HR",
                "estimated_hours": 2,
                "is_mandatory": True,
                "auto_complete": False,
                "requires_approval": True,
                "task_data": {
                    "category": "final_review",
                    "priority": "high",
                    "schedule": "90 days after start date",
                    "outcomes": [
                        "Probationary period completion",
                        "Employment confirmation",
                        "Performance summary",
                        "Future development plan",
                        "Onboarding feedback",
                        "Integration assessment"
                    ]
                }
            }
        ]
        
        # Create the template
        template = WorkflowTemplate(
            id=uuid.uuid4(),
            name="Standard Employee Onboarding",
            description="Comprehensive 90-day onboarding process for new employees with modern best practices",
            workflow_type="onboarding",
            is_active=True,
            is_default=True,
            department_ids=None,  # Applicable to all departments
            role_names=None,      # Applicable to all roles
            task_templates=task_templates,
            document_templates=[
                {
                    "name": "Employee Handbook",
                    "description": "Company policies and procedures",
                    "is_mandatory": True,
                    "category": "policy"
                },
                {
                    "name": "Emergency Contact Form",
                    "description": "Emergency contact information",
                    "is_mandatory": True,
                    "category": "personal"
                },
                {
                    "name": "Direct Deposit Form",
                    "description": "Banking information for payroll",
                    "is_mandatory": True,
                    "category": "payroll"
                },
                {
                    "name": "Benefits Enrollment",
                    "description": "Health insurance and benefits selection",
                    "is_mandatory": True,
                    "category": "benefits"
                }
            ],
            default_duration_days=90,
            organization_id=None,  # System-wide template
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow()
        )
        
        db.add(template)
        db.commit()
        db.refresh(template)
        
        print("✅ Default onboarding template created successfully!")
        print(f"Template ID: {template.id}")
        print(f"Template Name: {template.name}")
        print(f"Number of Tasks: {len(task_templates)}")
        print(f"Duration: {template.default_duration_days} days")
        
    except Exception as e:
        print(f"❌ Error creating default template: {e}")
        db.rollback()
    finally:
        db.close()

if __name__ == "__main__":
    create_default_template()
