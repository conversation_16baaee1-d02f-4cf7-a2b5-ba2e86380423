from pydantic import BaseModel, <PERSON>, validator
from typing import Optional, List, Dict, Any
from datetime import datetime, date
from uuid import UUID

from ..db.models.onboarding import WorkflowStatus, TaskStatus, TaskType, DocumentStatus


class WorkflowTemplateCreate(BaseModel):
    """Workflow template creation schema"""
    name: str = Field(..., max_length=200, description="Template name")
    description: Optional[str] = Field(None, description="Template description")
    workflow_type: str = Field(..., description="Workflow type (onboarding/offboarding)")
    department_ids: Optional[List[UUID]] = Field(None, description="Applicable department IDs")
    role_names: Optional[List[str]] = Field(None, description="Applicable role names")
    task_templates: List[Dict[str, Any]] = Field(..., description="Task template definitions")
    document_templates: Optional[List[Dict[str, Any]]] = Field(None, description="Document templates")
    default_duration_days: Optional[int] = Field(None, ge=1, description="Default duration in days")
    is_default: bool = Field(default=False, description="Is default template")


class WorkflowTemplateResponse(BaseModel):
    """Workflow template response schema"""
    id: UUID
    name: str
    description: Optional[str]
    workflow_type: str
    is_active: bool
    is_default: bool
    department_ids: Optional[List[UUID]]
    role_names: Optional[List[str]]
    task_templates: List[Dict[str, Any]]
    document_templates: Optional[List[Dict[str, Any]]]
    default_duration_days: Optional[int]
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class OnboardingWorkflowCreate(BaseModel):
    """Onboarding workflow creation schema"""
    employee_id: UUID = Field(..., description="Employee ID")
    template_id: Optional[UUID] = Field(None, description="Template ID")
    title: str = Field(..., max_length=200, description="Workflow title")
    description: Optional[str] = Field(None, description="Workflow description")
    start_date: date = Field(..., description="Start date")
    expected_completion_date: Optional[date] = Field(None, description="Expected completion date")
    assigned_to_id: Optional[UUID] = Field(None, description="Assigned HR/Manager ID")
    buddy_id: Optional[UUID] = Field(None, description="Assigned buddy/mentor ID")
    notes: Optional[str] = Field(None, description="Additional notes")
    custom_fields: Optional[Dict[str, Any]] = Field(None, description="Custom fields")


class OnboardingWorkflowUpdate(BaseModel):
    """Onboarding workflow update schema"""
    title: Optional[str] = Field(None, max_length=200)
    description: Optional[str] = Field(None)
    status: Optional[WorkflowStatus] = Field(None)
    expected_completion_date: Optional[date] = Field(None)
    assigned_to_id: Optional[UUID] = Field(None)
    buddy_id: Optional[UUID] = Field(None)
    notes: Optional[str] = Field(None)
    custom_fields: Optional[Dict[str, Any]] = Field(None)


class OnboardingWorkflowResponse(BaseModel):
    """Onboarding workflow response schema"""
    id: UUID
    employee_id: UUID
    template_id: Optional[UUID]
    title: str
    description: Optional[str]
    status: WorkflowStatus
    start_date: date
    expected_completion_date: Optional[date]
    actual_completion_date: Optional[date]
    assigned_to_id: Optional[UUID]
    buddy_id: Optional[UUID]
    total_tasks: int
    completed_tasks: int
    progress_percentage: int
    notes: Optional[str]
    custom_fields: Optional[Dict[str, Any]]
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class OffboardingWorkflowCreate(BaseModel):
    """Offboarding workflow creation schema"""
    employee_id: UUID = Field(..., description="Employee ID")
    template_id: Optional[UUID] = Field(None, description="Template ID")
    title: str = Field(..., max_length=200, description="Workflow title")
    description: Optional[str] = Field(None, description="Workflow description")
    last_working_date: date = Field(..., description="Last working date")
    reason_for_leaving: Optional[str] = Field(None, max_length=100, description="Reason for leaving")
    resignation_date: Optional[date] = Field(None, description="Resignation date")
    notice_period_days: Optional[int] = Field(None, ge=0, description="Notice period in days")
    start_date: date = Field(..., description="Offboarding start date")
    expected_completion_date: Optional[date] = Field(None, description="Expected completion date")
    assigned_to_id: Optional[UUID] = Field(None, description="Assigned HR ID")
    exit_interview_scheduled: bool = Field(default=False, description="Exit interview scheduled")
    exit_interview_date: Optional[datetime] = Field(None, description="Exit interview date")
    notes: Optional[str] = Field(None, description="Additional notes")
    custom_fields: Optional[Dict[str, Any]] = Field(None, description="Custom fields")


class OffboardingWorkflowUpdate(BaseModel):
    """Offboarding workflow update schema"""
    title: Optional[str] = Field(None, max_length=200)
    description: Optional[str] = Field(None)
    status: Optional[WorkflowStatus] = Field(None)
    last_working_date: Optional[date] = Field(None)
    reason_for_leaving: Optional[str] = Field(None, max_length=100)
    resignation_date: Optional[date] = Field(None)
    notice_period_days: Optional[int] = Field(None, ge=0)
    expected_completion_date: Optional[date] = Field(None)
    assigned_to_id: Optional[UUID] = Field(None)
    exit_interview_scheduled: Optional[bool] = Field(None)
    exit_interview_date: Optional[datetime] = Field(None)
    exit_interview_feedback: Optional[str] = Field(None)
    notes: Optional[str] = Field(None)
    custom_fields: Optional[Dict[str, Any]] = Field(None)


class OffboardingWorkflowResponse(BaseModel):
    """Offboarding workflow response schema"""
    id: UUID
    employee_id: UUID
    template_id: Optional[UUID]
    title: str
    description: Optional[str]
    status: WorkflowStatus
    last_working_date: date
    reason_for_leaving: Optional[str]
    resignation_date: Optional[date]
    notice_period_days: Optional[int]
    start_date: date
    expected_completion_date: Optional[date]
    actual_completion_date: Optional[date]
    assigned_to_id: Optional[UUID]
    total_tasks: int
    completed_tasks: int
    progress_percentage: int
    exit_interview_scheduled: bool
    exit_interview_date: Optional[datetime]
    exit_interview_feedback: Optional[str]
    notes: Optional[str]
    custom_fields: Optional[Dict[str, Any]]
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class OnboardingTaskCreate(BaseModel):
    """Onboarding task creation schema"""
    workflow_id: UUID = Field(..., description="Workflow ID")
    title: str = Field(..., max_length=200, description="Task title")
    description: Optional[str] = Field(None, description="Task description")
    task_type: TaskType = Field(..., description="Task type")
    order_index: int = Field(..., ge=0, description="Order index")
    depends_on_task_ids: Optional[List[UUID]] = Field(None, description="Dependent task IDs")
    assigned_to_id: Optional[UUID] = Field(None, description="Assigned employee ID")
    assigned_to_role: Optional[str] = Field(None, max_length=100, description="Assigned role")
    due_date: Optional[date] = Field(None, description="Due date")
    estimated_hours: Optional[int] = Field(None, ge=1, description="Estimated hours")
    is_mandatory: bool = Field(default=True, description="Is mandatory")
    auto_complete: bool = Field(default=False, description="Auto complete")
    requires_approval: bool = Field(default=False, description="Requires approval")
    task_data: Optional[Dict[str, Any]] = Field(None, description="Task-specific data")
    attachments: Optional[List[str]] = Field(None, description="Attachment URLs")


class OnboardingTaskUpdate(BaseModel):
    """Onboarding task update schema"""
    title: Optional[str] = Field(None, max_length=200)
    description: Optional[str] = Field(None)
    status: Optional[TaskStatus] = Field(None)
    assigned_to_id: Optional[UUID] = Field(None)
    assigned_to_role: Optional[str] = Field(None, max_length=100)
    due_date: Optional[date] = Field(None)
    estimated_hours: Optional[int] = Field(None, ge=1)
    completion_notes: Optional[str] = Field(None)
    task_data: Optional[Dict[str, Any]] = Field(None)
    attachments: Optional[List[str]] = Field(None)


class OnboardingTaskResponse(BaseModel):
    """Onboarding task response schema"""
    id: UUID
    workflow_id: UUID
    title: str
    description: Optional[str]
    task_type: TaskType
    status: TaskStatus
    order_index: int
    depends_on_task_ids: Optional[List[UUID]]
    assigned_to_id: Optional[UUID]
    assigned_to_role: Optional[str]
    due_date: Optional[date]
    estimated_hours: Optional[int]
    completed_date: Optional[datetime]
    completed_by_id: Optional[UUID]
    completion_notes: Optional[str]
    is_mandatory: bool
    auto_complete: bool
    requires_approval: bool
    task_data: Optional[Dict[str, Any]]
    attachments: Optional[List[str]]
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class OffboardingTaskCreate(BaseModel):
    """Offboarding task creation schema"""
    workflow_id: UUID = Field(..., description="Workflow ID")
    title: str = Field(..., max_length=200, description="Task title")
    description: Optional[str] = Field(None, description="Task description")
    task_type: TaskType = Field(..., description="Task type")
    order_index: int = Field(..., ge=0, description="Order index")
    depends_on_task_ids: Optional[List[UUID]] = Field(None, description="Dependent task IDs")
    assigned_to_id: Optional[UUID] = Field(None, description="Assigned employee ID")
    assigned_to_role: Optional[str] = Field(None, max_length=100, description="Assigned role")
    due_date: Optional[date] = Field(None, description="Due date")
    estimated_hours: Optional[int] = Field(None, ge=1, description="Estimated hours")
    is_mandatory: bool = Field(default=True, description="Is mandatory")
    auto_complete: bool = Field(default=False, description="Auto complete")
    requires_approval: bool = Field(default=False, description="Requires approval")
    task_data: Optional[Dict[str, Any]] = Field(None, description="Task-specific data")
    attachments: Optional[List[str]] = Field(None, description="Attachment URLs")


class OffboardingTaskUpdate(BaseModel):
    """Offboarding task update schema"""
    title: Optional[str] = Field(None, max_length=200)
    description: Optional[str] = Field(None)
    status: Optional[TaskStatus] = Field(None)
    assigned_to_id: Optional[UUID] = Field(None)
    assigned_to_role: Optional[str] = Field(None, max_length=100)
    due_date: Optional[date] = Field(None)
    estimated_hours: Optional[int] = Field(None, ge=1)
    completion_notes: Optional[str] = Field(None)
    task_data: Optional[Dict[str, Any]] = Field(None)
    attachments: Optional[List[str]] = Field(None)


class OffboardingTaskResponse(BaseModel):
    """Offboarding task response schema"""
    id: UUID
    workflow_id: UUID
    title: str
    description: Optional[str]
    task_type: TaskType
    status: TaskStatus
    order_index: int
    depends_on_task_ids: Optional[List[UUID]]
    assigned_to_id: Optional[UUID]
    assigned_to_role: Optional[str]
    due_date: Optional[date]
    estimated_hours: Optional[int]
    completed_date: Optional[datetime]
    completed_by_id: Optional[UUID]
    completion_notes: Optional[str]
    is_mandatory: bool
    auto_complete: bool
    requires_approval: bool
    task_data: Optional[Dict[str, Any]]
    attachments: Optional[List[str]]
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class DocumentCreate(BaseModel):
    """Document creation schema"""
    workflow_id: UUID = Field(..., description="Workflow ID")
    name: str = Field(..., max_length=200, description="Document name")
    description: Optional[str] = Field(None, description="Document description")
    document_type: str = Field(..., max_length=100, description="Document type")
    file_url: Optional[str] = Field(None, max_length=500, description="File URL")
    file_name: Optional[str] = Field(None, max_length=255, description="File name")
    file_size: Optional[int] = Field(None, ge=0, description="File size in bytes")
    file_type: Optional[str] = Field(None, max_length=50, description="MIME type")
    expiry_date: Optional[date] = Field(None, description="Document expiry date")
    is_mandatory: bool = Field(default=True, description="Is mandatory")
    requires_verification: bool = Field(default=False, description="Requires verification")


class DocumentUpdate(BaseModel):
    """Document update schema"""
    name: Optional[str] = Field(None, max_length=200)
    description: Optional[str] = Field(None)
    status: Optional[DocumentStatus] = Field(None)
    file_url: Optional[str] = Field(None, max_length=500)
    file_name: Optional[str] = Field(None, max_length=255)
    file_size: Optional[int] = Field(None, ge=0)
    file_type: Optional[str] = Field(None, max_length=50)
    review_notes: Optional[str] = Field(None)
    rejection_reason: Optional[str] = Field(None)
    expiry_date: Optional[date] = Field(None)


class DocumentResponse(BaseModel):
    """Document response schema"""
    id: UUID
    workflow_id: UUID
    name: str
    description: Optional[str]
    document_type: str
    status: DocumentStatus
    submitted_date: Optional[datetime]
    reviewed_date: Optional[datetime]
    expiry_date: Optional[date]
    file_url: Optional[str]
    file_name: Optional[str]
    file_size: Optional[int]
    file_type: Optional[str]
    reviewed_by_id: Optional[UUID]
    review_notes: Optional[str]
    rejection_reason: Optional[str]
    is_mandatory: bool
    requires_verification: bool
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class OnboardingWorkflowListResponse(BaseModel):
    """Onboarding workflow list response"""
    items: List[OnboardingWorkflowResponse]
    total: int
    page: int
    size: int
    pages: int


class OffboardingWorkflowListResponse(BaseModel):
    """Offboarding workflow list response"""
    items: List[OffboardingWorkflowResponse]
    total: int
    page: int
    size: int
    pages: int


class TaskListResponse(BaseModel):
    """Task list response"""
    items: List[OnboardingTaskResponse]
    total: int
    page: int
    size: int
    pages: int


class DocumentListResponse(BaseModel):
    """Document list response"""
    items: List[DocumentResponse]
    total: int
    page: int
    size: int
    pages: int
