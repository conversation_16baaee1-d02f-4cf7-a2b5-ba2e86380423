from core.repositories.approval_histrory import ApprovalHistoryRepository
from core.repositories.approvals import ApprovalsRepository

class ApprovalHistoryService:
    def __init__(self):
        self.repository = ApprovalHistoryRepository()
        self.approval_repo = ApprovalsRepository()

    @staticmethod
    def get_approval_history_by_id(approval_history_id):
        return ApprovalHistoryRepository.get_by_id(approval_history_id)

    @staticmethod
    def get_approval_history_by_employee(employee_id):
        return ApprovalHistoryRepository.get_all_by_employee_id(employee_id)

    @staticmethod
    def update_approval_history(approval_history_id, data):
        history = ApprovalHistoryRepository.get_by_id(approval_history_id)
        if not history:
            return None
        return ApprovalHistoryRepository.update(history, data)
    
    @staticmethod
    def get_all_for_authenticated_user():
        return ApprovalHistoryRepository.get_all_by_authenticated_user()

    @staticmethod
    def get_by_status_for_authenticated_user(status):
        return ApprovalHistoryRepository.get_all_by_status_for_authenticated_user(status)

    @staticmethod
    def get_all_by_employee_id(employee_id):
        return ApprovalHistoryRepository.get_all_by_employee_id(employee_id)

    @staticmethod
    def get_by_status_for_employee(employee_id, status):
        return ApprovalHistoryRepository.get_by_status_for_employee(employee_id, status)
    
    def get_summary(self):
        # return self.repository.get_approval_summary()
        pending = self.repository.get_all_by_status_for_authenticated_user("pending")
        declined = self.repository.get_all_by_status_for_authenticated_user("declined")
        approved = self.repository.get_all_by_status_for_authenticated_user("approved")
        approvals = self.approval_repo.fetchAll()
        total_approvals = len(approvals)
        return {
            "pending" : len(pending),
            "declined" : len(declined),
            "approved" : len(approved),
            "total" : total_approvals,
        }