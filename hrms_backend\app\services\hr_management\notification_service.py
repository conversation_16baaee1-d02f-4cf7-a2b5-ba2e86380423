"""
Enhanced Notification Service for Leave Management
"""

import smtplib
import logging
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from email.mime.base import MIMEBase
from email import encoders
from typing import List, Dict, Optional, Any
from datetime import datetime, date
from jinja2 import Template
import asyncio

from ...core.config import settings
from ...core.websocket_manager import notification_manager

logger = logging.getLogger(__name__)


class NotificationService:
    """Enhanced notification service for leave management"""

    def __init__(self):
        self.smtp_server = getattr(settings, 'SMTP_HOST', 'smtp.gmail.com')
        self.smtp_port = getattr(settings, 'SMTP_PORT', 587)
        self.email_user = getattr(settings, 'SMTP_USERNAME', '')
        self.email_password = getattr(settings, 'SMTP_PASSWORD', '')
        self.from_name = getattr(settings, 'FROM_NAME', 'AgnoShin HRMS')

    async def send_leave_request_notification(
        self,
        to_emails: List[str],
        employee_name: str,
        leave_details: Dict[str, Any],
        notification_type: str = "created"
    ):
        """Send leave request notification email"""
        try:
            subject_map = {
                "created": f"New Leave Request from {employee_name}",
                "approved": f"Leave Request Approved - {employee_name}",
                "rejected": f"Leave Request Rejected - {employee_name}",
                "auto_approved": f"Leave Request Auto-Approved - {employee_name}",
                "auto_rejected": f"Leave Request Auto-Rejected - {employee_name}",
                "escalated": f"Leave Request Escalated - {employee_name}"
            }

            subject = subject_map.get(notification_type, f"Leave Request Update - {employee_name}")

            # Create email template
            template = self._get_leave_request_template(notification_type)
            html_content = template.render(
                employee_name=employee_name,
                leave_details=leave_details,
                notification_type=notification_type,
                system_name=self.from_name
            )

            await self._send_email(to_emails, subject, html_content)
            
            # Also send real-time notification
            for email in to_emails:
                await notification_manager.notify_user(
                    email,  # Assuming email as user identifier
                    "leave_notification",
                    {
                        "type": notification_type,
                        "employee_name": employee_name,
                        "leave_details": leave_details,
                        "timestamp": datetime.utcnow().isoformat()
                    }
                )

        except Exception as e:
            logger.error(f"Error sending leave request notification: {e}")

    async def send_leave_balance_alert(
        self,
        to_emails: List[str],
        employee_name: str,
        balance_details: Dict[str, Any],
        alert_type: str = "low_balance"
    ):
        """Send leave balance alert"""
        try:
            subject_map = {
                "low_balance": f"Low Leave Balance Alert - {employee_name}",
                "expiry_reminder": f"Leave Expiry Reminder - {employee_name}",
                "balance_updated": f"Leave Balance Updated - {employee_name}"
            }

            subject = subject_map.get(alert_type, f"Leave Balance Alert - {employee_name}")

            template = self._get_balance_alert_template(alert_type)
            html_content = template.render(
                employee_name=employee_name,
                balance_details=balance_details,
                alert_type=alert_type,
                system_name=self.from_name
            )

            await self._send_email(to_emails, subject, html_content)

        except Exception as e:
            logger.error(f"Error sending balance alert: {e}")

    async def send_encashment_notification(
        self,
        to_emails: List[str],
        employee_name: str,
        encashment_details: Dict[str, Any],
        notification_type: str = "created"
    ):
        """Send leave encashment notification"""
        try:
            subject_map = {
                "created": f"Leave Encashment Request - {employee_name}",
                "approved": f"Leave Encashment Approved - {employee_name}",
                "rejected": f"Leave Encashment Rejected - {employee_name}",
                "processed": f"Leave Encashment Processed - {employee_name}"
            }

            subject = subject_map.get(notification_type, f"Leave Encashment Update - {employee_name}")

            template = self._get_encashment_template(notification_type)
            html_content = template.render(
                employee_name=employee_name,
                encashment_details=encashment_details,
                notification_type=notification_type,
                system_name=self.from_name
            )

            await self._send_email(to_emails, subject, html_content)

        except Exception as e:
            logger.error(f"Error sending encashment notification: {e}")

    async def send_holiday_notification(
        self,
        to_emails: List[str],
        holiday_details: Dict[str, Any],
        notification_type: str = "upcoming"
    ):
        """Send holiday notification"""
        try:
            subject_map = {
                "upcoming": f"Upcoming Holiday: {holiday_details.get('name', 'Holiday')}",
                "added": f"New Holiday Added: {holiday_details.get('name', 'Holiday')}",
                "cancelled": f"Holiday Cancelled: {holiday_details.get('name', 'Holiday')}"
            }

            subject = subject_map.get(notification_type, "Holiday Notification")

            template = self._get_holiday_template(notification_type)
            html_content = template.render(
                holiday_details=holiday_details,
                notification_type=notification_type,
                system_name=self.from_name
            )

            await self._send_email(to_emails, subject, html_content)

        except Exception as e:
            logger.error(f"Error sending holiday notification: {e}")

    async def send_attendance_alert(
        self,
        to_emails: List[str],
        employee_name: str,
        attendance_details: Dict[str, Any],
        alert_type: str = "overtime"
    ):
        """Send attendance-related alerts"""
        try:
            subject_map = {
                "overtime": f"Overtime Alert - {employee_name}",
                "compensation_credit": f"Compensation Leave Credited - {employee_name}",
                "missed_checkout": f"Missed Check-out Alert - {employee_name}",
                "irregular_hours": f"Irregular Hours Alert - {employee_name}"
            }

            subject = subject_map.get(alert_type, f"Attendance Alert - {employee_name}")

            template = self._get_attendance_template(alert_type)
            html_content = template.render(
                employee_name=employee_name,
                attendance_details=attendance_details,
                alert_type=alert_type,
                system_name=self.from_name
            )

            await self._send_email(to_emails, subject, html_content)

        except Exception as e:
            logger.error(f"Error sending attendance alert: {e}")

    async def send_policy_notification(
        self,
        to_emails: List[str],
        policy_details: Dict[str, Any],
        notification_type: str = "updated"
    ):
        """Send policy update notifications"""
        try:
            subject_map = {
                "updated": f"Leave Policy Updated: {policy_details.get('name', 'Policy')}",
                "new": f"New Leave Policy: {policy_details.get('name', 'Policy')}",
                "expired": f"Leave Policy Expired: {policy_details.get('name', 'Policy')}"
            }

            subject = subject_map.get(notification_type, "Policy Notification")

            template = self._get_policy_template(notification_type)
            html_content = template.render(
                policy_details=policy_details,
                notification_type=notification_type,
                system_name=self.from_name
            )

            await self._send_email(to_emails, subject, html_content)

        except Exception as e:
            logger.error(f"Error sending policy notification: {e}")

    async def _send_email(
        self,
        to_emails: List[str],
        subject: str,
        html_content: str,
        attachments: Optional[List[Dict]] = None
    ):
        """Send email using SMTP"""
        try:
            if not self.email_user or not self.email_password:
                logger.warning("Email credentials not configured")
                return

            msg = MIMEMultipart('alternative')
            msg['From'] = f"{self.from_name} <{self.email_user}>"
            msg['To'] = ", ".join(to_emails)
            msg['Subject'] = subject

            # Add HTML content
            html_part = MIMEText(html_content, 'html')
            msg.attach(html_part)

            # Add attachments if any
            if attachments:
                for attachment in attachments:
                    part = MIMEBase('application', 'octet-stream')
                    part.set_payload(attachment['content'])
                    encoders.encode_base64(part)
                    part.add_header(
                        'Content-Disposition',
                        f'attachment; filename= {attachment["filename"]}'
                    )
                    msg.attach(part)

            # Send email
            server = smtplib.SMTP(self.smtp_server, self.smtp_port)
            server.starttls()
            server.login(self.email_user, self.email_password)
            text = msg.as_string()
            server.sendmail(self.email_user, to_emails, text)
            server.quit()

            logger.info(f"Email sent successfully to {', '.join(to_emails)}")

        except Exception as e:
            logger.error(f"Error sending email: {e}")

    def _get_leave_request_template(self, notification_type: str) -> Template:
        """Get leave request email template"""
        template_content = """
        <!DOCTYPE html>
        <html>
        <head>
            <style>
                body { font-family: Arial, sans-serif; margin: 0; padding: 20px; }
                .container { max-width: 600px; margin: 0 auto; }
                .header { background-color: #4CAF50; color: white; padding: 20px; text-align: center; }
                .content { padding: 20px; border: 1px solid #ddd; }
                .footer { background-color: #f1f1f1; padding: 10px; text-align: center; font-size: 12px; }
                .details { background-color: #f9f9f9; padding: 15px; margin: 10px 0; }
                .status { padding: 5px 10px; border-radius: 3px; color: white; }
                .approved { background-color: #4CAF50; }
                .rejected { background-color: #f44336; }
                .pending { background-color: #ff9800; }
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h2>{{ system_name }} - Leave Request {{ notification_type.title() }}</h2>
                </div>
                <div class="content">
                    <h3>Leave Request Details</h3>
                    <div class="details">
                        <p><strong>Employee:</strong> {{ employee_name }}</p>
                        <p><strong>Leave Type:</strong> {{ leave_details.leave_type }}</p>
                        <p><strong>Start Date:</strong> {{ leave_details.start_date }}</p>
                        <p><strong>End Date:</strong> {{ leave_details.end_date }}</p>
                        <p><strong>Total Days:</strong> {{ leave_details.total_days }}</p>
                        <p><strong>Reason:</strong> {{ leave_details.reason }}</p>
                        <p><strong>Status:</strong>
                            <span class="status {{ leave_details.status }}">{{ leave_details.status }}</span>
                        </p>
                    </div>
                    {% if notification_type == 'created' %}
                    <p>A new leave request has been submitted and requires your approval.</p>
                    {% elif notification_type == 'approved' %}
                    <p>Your leave request has been approved.</p>
                    {% elif notification_type == 'rejected' %}
                    <p>Your leave request has been rejected. Please contact your manager for more details.</p>
                    {% endif %}
                </div>
                <div class="footer">
                    <p>This is an automated message from {{ system_name }}. Please do not reply to this email.</p>
                </div>
            </div>
        </body>
        </html>
        """
        return Template(template_content)

    def _get_balance_alert_template(self, alert_type: str) -> Template:
        """Get balance alert email template"""
        template_content = """
        <!DOCTYPE html>
        <html>
        <head>
            <style>
                body { font-family: Arial, sans-serif; margin: 0; padding: 20px; }
                .container { max-width: 600px; margin: 0 auto; }
                .header { background-color: #ff9800; color: white; padding: 20px; text-align: center; }
                .content { padding: 20px; border: 1px solid #ddd; }
                .footer { background-color: #f1f1f1; padding: 10px; text-align: center; font-size: 12px; }
                .alert { background-color: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; margin: 10px 0; }
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h2>{{ system_name }} - Leave Balance Alert</h2>
                </div>
                <div class="content">
                    <div class="alert">
                        {% if alert_type == 'low_balance' %}
                        <h3>Low Leave Balance Alert</h3>
                        <p>Dear {{ employee_name }},</p>
                        <p>Your leave balance is running low. Please plan your leaves accordingly.</p>
                        {% elif alert_type == 'expiry_reminder' %}
                        <h3>Leave Expiry Reminder</h3>
                        <p>Dear {{ employee_name }},</p>
                        <p>Some of your leave days will expire soon. Please use them before the expiry date.</p>
                        {% endif %}
                        
                        <h4>Current Balance:</h4>
                        {% for balance in balance_details.balances %}
                        <p><strong>{{ balance.leave_type }}:</strong> {{ balance.available_days }} days available</p>
                        {% endfor %}
                    </div>
                </div>
                <div class="footer">
                    <p>This is an automated message from {{ system_name }}. Please do not reply to this email.</p>
                </div>
            </div>
        </body>
        </html>
        """
        return Template(template_content)

    def _get_encashment_template(self, notification_type: str) -> Template:
        """Get encashment email template"""
        # Similar template structure for encashment notifications
        template_content = """
        <!DOCTYPE html>
        <html>
        <body>
            <h2>Leave Encashment {{ notification_type.title() }}</h2>
            <p>Employee: {{ employee_name }}</p>
            <p>Days to Encash: {{ encashment_details.days_to_encash }}</p>
            <p>Amount: {{ encashment_details.total_amount }}</p>
            <p>Status: {{ encashment_details.status }}</p>
        </body>
        </html>
        """
        return Template(template_content)

    def _get_holiday_template(self, notification_type: str) -> Template:
        """Get holiday email template"""
        # Holiday notification template
        template_content = """
        <!DOCTYPE html>
        <html>
        <body>
            <h2>Holiday Notification</h2>
            <p>Holiday: {{ holiday_details.name }}</p>
            <p>Date: {{ holiday_details.date }}</p>
            <p>Type: {{ holiday_details.type }}</p>
        </body>
        </html>
        """
        return Template(template_content)

    def _get_attendance_template(self, alert_type: str) -> Template:
        """Get attendance alert email template"""
        # Attendance alert template
        template_content = """
        <!DOCTYPE html>
        <html>
        <body>
            <h2>Attendance Alert</h2>
            <p>Employee: {{ employee_name }}</p>
            <p>Alert Type: {{ alert_type }}</p>
            <p>Details: {{ attendance_details }}</p>
        </body>
        </html>
        """
        return Template(template_content)

    def _get_policy_template(self, notification_type: str) -> Template:
        """Get policy notification email template"""
        # Policy notification template
        template_content = """
        <!DOCTYPE html>
        <html>
        <body>
            <h2>Policy {{ notification_type.title() }}</h2>
            <p>Policy: {{ policy_details.name }}</p>
            <p>Effective Date: {{ policy_details.effective_date }}</p>
        </body>
        </html>
        """
        return Template(template_content)


# Create service instance
notification_service = NotificationService()
