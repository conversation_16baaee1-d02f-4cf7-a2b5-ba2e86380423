from core.databases.database import db
from core.models.basemodel import ModelBase
from sqlalchemy.orm import relationship
from core.models.employees import EmployeeModel
from core.utils.helper import get_nigeria_time


class EmployeeActivityModel(ModelBase):
    __tablename__ = "employee_activities"

    id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    message = db.Column(db.String(100), nullable=True)
    employee_id = db.Column(db.Integer, db.ForeignKey((ModelBase.dbSchema() + '.employees.id')), nullable=False) 
    employee = db.relationship('EmployeeModel', backref='employee_activities')
    created_at = db.Column(db.DateTime(timezone=True), nullable=False, default=get_nigeria_time)
    updated_at = db.Column(db.DateTime(timezone=True), nullable=False, default=get_nigeria_time, onupdate=get_nigeria_time)

