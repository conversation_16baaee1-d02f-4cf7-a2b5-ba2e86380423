"""create users table

Revision ID: dd39533fe0bf
Revises: 
Create Date: 2024-04-03 15:03:06.864664

"""
from typing import Sequence, Union

from alembic import op
from sqlalchemy import func, TIMESTAMP
from sqlalchemy import <PERSON>umn, Integer, String
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'dd39533fe0bf'
down_revision: Union[str, None] = None
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    op.create_table(
        'users',
        Column('id', Integer, primary_key=True),
        <PERSON>umn('first_name', String(50), nullable=False),
        <PERSON>umn('last_name', String(50), nullable=False),
        <PERSON>umn('org_name', String(50), nullable=False),
        <PERSON>umn('email', String(50), nullable=False),
        <PERSON><PERSON>n('password', String(150), nullable=False),
        <PERSON>umn("timestamp", TIMESTAMP, server_default=func.now()),
        <PERSON><PERSON><PERSON>('email_verified', sa.<PERSON>(), nullable=False, server_default=sa.text('false')),
    )

def downgrade() -> None:
    op.drop_table("users")
