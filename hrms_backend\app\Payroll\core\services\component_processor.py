class ComponentProcessor:
    def __init__(self, employee_id, employee_gross, prorated_gross, prorated_status=False, salary_benefit=None, salary_component=None):
        self.employee_id = employee_id
        self.gross_pay = float(employee_gross) if employee_gross else 0.0
        self.salary_benefit = salary_benefit
        self.salary_component = salary_component
        self.components = [] 
        self.benefit = [] 
        self.total_earnings = 0.0
        self.total_deduction = 0.0 
        self.total_non_taxable = 0.0 
        self.total_other_deduction = 0.0 
        self.basic = 0.0
        self.housing = 0.0
        self.transport = 0.0
        self.is_prorated = prorated_status if prorated_status else False
        self.prorated_gross = float(prorated_gross) if prorated_gross else 0.0

    def fetch_salary_components(self):
        """
        Fetch salary components based on the salary template or assigned components.
        """
        # if self.salary_template:
        #     # print("Fetching components from salary template.")
        #     self.components = [
        #         component['salary_component'] for component in self.salary_template['salary_template_components']
        #     ]
        # elif self.salary_component:
            # print("Fetching assigned salary components.")
        self.components = [
            component['salary_component'] for component in self.salary_component
        ]
        # else:
        #     # print("No components selected.")
        #     raise ValueError("No components found for processing.")

    def fetch_benefit_components(self):
        self.benefit = [
            benefit['benefit'] for benefit in self.salary_benefit
        ]
    
    def process_benefits(self):
        benefit_components = []
        for component in self.benefit:
            benefit_data = self._create_benefit_data(component)
            benefit_components.append(benefit_data)
        return benefit_components

    def _create_benefit_data(self, component):
        calculation = self.calculate_component(component)

        return {
            "id": component["id"],
            "component_name": component["benefit_name"],
            "payslip_name": component["payslip_name"],
            "component_type": component["component_type"],
            "calculation_type": component["calculation_type"],
            "cycle": component["cycle"],
            "amount": component["amount"],
            "value": component["value"],
            "preview_payslip": component["preview_payslip"],
            "total_monthly_calculation": format(calculation,","),
        }

    def process_components(self):
        """
        Processes salary components and reports.
        """
        all_components, nhf_list, pension_list = [], [], []

        for component in self.components:
            component_data = self._create_component_data(component)
            all_components.append(component_data)

            component_name = component.get("component_name", "").lower()
            if component_name == "nhf":
                nhf_list.append(component_data)
            elif component_name == "pension":
                pension_list.append(component_data)
                # print(f"Matched Pension: {component_data}")  # Debug when pension is matched

        # print(f"All Components: {all_components}")  # Debugging all processed components
        # print(f"NHF Components: {nhf_list}")  # Debugging NHF components
        # print(f"Pension Components: {pension_list}")  # Debugging Pension components

        return all_components, nhf_list, pension_list


    def _create_component_data(self, component):
        """
        Creates salary component data.
        """
        calculation = self.calculate_component(component)

        if component["component_type"].lower() == "taxable earning":
            self.total_earnings += calculation

        if component["component_type"].lower() == "non-taxable earning":
            self.total_non_taxable += calculation
            
    
        if component["component_type"].lower() == "statutory deduction":
            self.total_deduction += calculation

        if component["component_type"].lower() == "other deductions":
            self.total_other_deduction += calculation
            

        return {
            "id": component["id"],
            "component_name": component["component_name"],
            "payslip_name": component["payslip_name"],
            "component_type": component["component_type"],
            "calculation_type": component["calculation_type"],
            "cycle": component["cycle"],
            "amount": component["amount"],
            "value": component["value"],
            "total_monthly_calculation": format(calculation,","),
        }

    def calculate_component(self, component):
        """
        Calculates salary component based on its calculation type.
        """
        try:
            value = float(component["value"])
            amount = float(component["amount"])
        except (TypeError, ValueError):
            # print(f"Invalid value for component {component['component_name']}. Defaulting to 0.0")
            return 0.0

        calculation_type = component["calculation_type"].lower()

        if "% of gross" in calculation_type:
            return round((value * self.gross_salary() ) / 100, 4)
        elif "% of basic" in calculation_type:
            return round((value * self.basic) / 100, 4)
        elif "% of housing" in calculation_type:
            return round((value * self.housing) / 100, 4)
        elif "% of transport" in calculation_type:
            return round((value * self.transport) / 100, 4)
        elif "% of bht" in calculation_type:
            total_bht = self.basic + self.housing + self.transport
            return round((value * total_bht) / 100, 4)
        elif calculation_type == "flat":
            return round(amount, 4)
        else:
            print(f"Unknown calculation type for {component['calculation_type']}")
            return 0.0

    def calculate_base_components(self):
        """
        Calculates the values for basic, housing, and transport components.
        """
        for component in self.components:
            component_name = component["component_name"].lower()
            if "basic" in component_name:
                self.basic = round((float(component["value"]) * self.gross_salary()) / 100, 4)
            elif "housing" in component_name:
                self.housing = round((float(component["value"]) * self.gross_salary()) / 100, 4)
            elif "transport" in component_name:
                self.transport = round((float(component["value"]) * self.gross_salary()) / 100, 4)

    def generate_salary_response(self):
        self.fetch_salary_components()
        self.fetch_benefit_components()
        self.calculate_base_components()

        all_components, nhf_components, pension_components = self.process_components()
        all_benefits = self.process_benefits()
        

        return {
            "all_components": all_components,
            "all_benefits": all_benefits,
            "nhf_components": nhf_components,
            "pension_components": pension_components,
            "total_earnings": format(round(self.total_earnings, 4), ","),
            "total_non_taxable": self.total_non_taxable,
            "total_deduction": format(round(self.total_deduction + self.total_other_deduction, 4), ","), 
            "total_statutory": self.total_deduction,
            "total_other_deduction": self.total_other_deduction,
            "basic": self.basic,
            "housing": self.housing,
            "transport": self.transport,
            "prorated_gross": self.prorated_gross,
            # "prorated_net" : self.prorated_net TODO add the prorated net value
        }
    

    def gross_salary(self):
        if self.is_prorated is False:
            return self.gross_pay
        return self.prorated_gross