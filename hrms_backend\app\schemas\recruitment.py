from pydantic import BaseModel, EmailStr, Field, validator
from typing import Optional, List, Dict, Any
from datetime import datetime, date
from uuid import UUID
from decimal import Decimal

from ..db.models.recruitment import JobStatus, JobType, ApplicationStatus, InterviewType, InterviewStatus


class JobPostingCreate(BaseModel):
    """Job posting creation schema"""
    title: str = Field(..., max_length=200, description="Job title")
    description: str = Field(..., description="Job description")
    requirements: Optional[str] = Field(None, description="Job requirements")
    responsibilities: Optional[str] = Field(None, description="Job responsibilities")
    job_type: JobType = Field(..., description="Type of job")
    department_id: UUID = Field(..., description="Department ID")
    location: Optional[str] = Field(None, max_length=255, description="Job location")
    remote_allowed: bool = Field(default=False, description="Whether remote work is allowed")
    salary_min: Optional[Decimal] = Field(None, description="Minimum salary")
    salary_max: Optional[Decimal] = Field(None, description="Maximum salary")
    currency: str = Field(default="USD", max_length=3, description="Currency code")
    benefits: Optional[str] = Field(None, description="Benefits offered")
    application_deadline: Optional[date] = Field(None, description="Application deadline")
    start_date: Optional[date] = Field(None, description="Expected start date")
    hiring_manager_id: Optional[UUID] = Field(None, description="Hiring manager ID")
    recruiter_id: Optional[UUID] = Field(None, description="Recruiter ID")
    positions_available: int = Field(default=1, ge=1, description="Number of positions")
    skills_required: Optional[List[str]] = Field(None, description="Required skills")
    experience_level: Optional[str] = Field(None, description="Experience level required")
    education_requirements: Optional[str] = Field(None, description="Education requirements")


class JobPostingUpdate(BaseModel):
    """Job posting update schema"""
    title: Optional[str] = Field(None, max_length=200)
    description: Optional[str] = Field(None)
    requirements: Optional[str] = Field(None)
    responsibilities: Optional[str] = Field(None)
    job_type: Optional[JobType] = Field(None)
    location: Optional[str] = Field(None, max_length=255)
    remote_allowed: Optional[bool] = Field(None)
    salary_min: Optional[Decimal] = Field(None)
    salary_max: Optional[Decimal] = Field(None)
    benefits: Optional[str] = Field(None)
    status: Optional[JobStatus] = Field(None)
    application_deadline: Optional[date] = Field(None)
    start_date: Optional[date] = Field(None)
    positions_available: Optional[int] = Field(None, ge=1)
    skills_required: Optional[List[str]] = Field(None)
    experience_level: Optional[str] = Field(None)
    education_requirements: Optional[str] = Field(None)


class JobPostingResponse(BaseModel):
    """Job posting response schema"""
    id: UUID
    title: str
    description: str
    requirements: Optional[str]
    responsibilities: Optional[str]
    job_type: JobType
    department_id: UUID
    location: Optional[str]
    remote_allowed: bool
    salary_min: Optional[Decimal]
    salary_max: Optional[Decimal]
    currency: str
    benefits: Optional[str]
    status: JobStatus
    posted_date: Optional[date]
    application_deadline: Optional[date]
    start_date: Optional[date]
    hiring_manager_id: Optional[UUID]
    recruiter_id: Optional[UUID]
    positions_available: int
    positions_filled: int
    skills_required: Optional[List[str]]
    experience_level: Optional[str]
    education_requirements: Optional[str]
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class CandidateCreate(BaseModel):
    """Candidate creation schema"""
    first_name: str = Field(..., max_length=100, description="First name")
    last_name: str = Field(..., max_length=100, description="Last name")
    email: EmailStr = Field(..., description="Email address")
    phone: Optional[str] = Field(None, max_length=20, description="Phone number")
    current_position: Optional[str] = Field(None, max_length=200, description="Current position")
    current_company: Optional[str] = Field(None, max_length=200, description="Current company")
    total_experience: Optional[int] = Field(None, ge=0, description="Years of experience")
    expected_salary: Optional[Decimal] = Field(None, description="Expected salary")
    currency: str = Field(default="USD", max_length=3, description="Currency code")
    current_location: Optional[str] = Field(None, max_length=255, description="Current location")
    willing_to_relocate: bool = Field(default=False, description="Willing to relocate")
    resume_url: Optional[str] = Field(None, max_length=500, description="Resume URL")
    cover_letter_url: Optional[str] = Field(None, max_length=500, description="Cover letter URL")
    portfolio_url: Optional[str] = Field(None, max_length=500, description="Portfolio URL")
    linkedin_url: Optional[str] = Field(None, max_length=500, description="LinkedIn URL")
    skills: Optional[List[str]] = Field(None, description="Skills")
    education: Optional[List[Dict[str, Any]]] = Field(None, description="Education records")
    certifications: Optional[List[Dict[str, Any]]] = Field(None, description="Certifications")
    source: Optional[str] = Field(None, max_length=100, description="How they found the job")
    notes: Optional[str] = Field(None, description="Additional notes")


class CandidateUpdate(BaseModel):
    """Candidate update schema"""
    first_name: Optional[str] = Field(None, max_length=100)
    last_name: Optional[str] = Field(None, max_length=100)
    email: Optional[EmailStr] = Field(None)
    phone: Optional[str] = Field(None, max_length=20)
    current_position: Optional[str] = Field(None, max_length=200)
    current_company: Optional[str] = Field(None, max_length=200)
    total_experience: Optional[int] = Field(None, ge=0)
    expected_salary: Optional[Decimal] = Field(None)
    current_location: Optional[str] = Field(None, max_length=255)
    willing_to_relocate: Optional[bool] = Field(None)
    resume_url: Optional[str] = Field(None, max_length=500)
    cover_letter_url: Optional[str] = Field(None, max_length=500)
    portfolio_url: Optional[str] = Field(None, max_length=500)
    linkedin_url: Optional[str] = Field(None, max_length=500)
    skills: Optional[List[str]] = Field(None)
    education: Optional[List[Dict[str, Any]]] = Field(None)
    certifications: Optional[List[Dict[str, Any]]] = Field(None)
    source: Optional[str] = Field(None, max_length=100)
    notes: Optional[str] = Field(None)


class CandidateResponse(BaseModel):
    """Candidate response schema"""
    id: UUID
    first_name: str
    last_name: str
    email: str
    phone: Optional[str]
    current_position: Optional[str]
    current_company: Optional[str]
    total_experience: Optional[int]
    expected_salary: Optional[Decimal]
    currency: str
    current_location: Optional[str]
    willing_to_relocate: bool
    resume_url: Optional[str]
    cover_letter_url: Optional[str]
    portfolio_url: Optional[str]
    linkedin_url: Optional[str]
    skills: Optional[List[str]]
    education: Optional[List[Dict[str, Any]]]
    certifications: Optional[List[Dict[str, Any]]]
    source: Optional[str]
    notes: Optional[str]
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class JobApplicationCreate(BaseModel):
    """Job application creation schema"""
    job_posting_id: UUID = Field(..., description="Job posting ID")
    candidate_id: UUID = Field(..., description="Candidate ID")
    resume_url: Optional[str] = Field(None, max_length=500, description="Resume URL")
    cover_letter_url: Optional[str] = Field(None, max_length=500, description="Cover letter URL")
    additional_documents: Optional[List[str]] = Field(None, description="Additional document URLs")
    application_responses: Optional[Dict[str, Any]] = Field(None, description="Application responses")


class JobApplicationUpdate(BaseModel):
    """Job application update schema"""
    status: Optional[ApplicationStatus] = Field(None, description="Application status")
    assigned_recruiter_id: Optional[UUID] = Field(None, description="Assigned recruiter ID")
    screening_score: Optional[int] = Field(None, ge=0, le=100, description="Screening score")
    recruiter_notes: Optional[str] = Field(None, description="Recruiter notes")
    rejection_reason: Optional[str] = Field(None, description="Rejection reason")


class JobApplicationResponse(BaseModel):
    """Job application response schema"""
    id: UUID
    job_posting_id: UUID
    candidate_id: UUID
    status: ApplicationStatus
    applied_date: datetime
    resume_url: Optional[str]
    cover_letter_url: Optional[str]
    additional_documents: Optional[List[str]]
    application_responses: Optional[Dict[str, Any]]
    last_updated: datetime
    assigned_recruiter_id: Optional[UUID]
    screening_score: Optional[int]
    recruiter_notes: Optional[str]
    rejection_reason: Optional[str]
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class InterviewCreate(BaseModel):
    """Interview creation schema"""
    application_id: UUID = Field(..., description="Application ID")
    interview_type: InterviewType = Field(..., description="Interview type")
    scheduled_date: datetime = Field(..., description="Scheduled date and time")
    duration_minutes: int = Field(default=60, ge=15, le=480, description="Duration in minutes")
    location: Optional[str] = Field(None, max_length=255, description="Location or video link")
    interviewer_ids: List[UUID] = Field(..., min_items=1, description="Interviewer employee IDs")
    questions_asked: Optional[List[str]] = Field(None, description="Questions to ask")


class InterviewUpdate(BaseModel):
    """Interview update schema"""
    status: Optional[InterviewStatus] = Field(None, description="Interview status")
    scheduled_date: Optional[datetime] = Field(None, description="Rescheduled date and time")
    duration_minutes: Optional[int] = Field(None, ge=15, le=480)
    location: Optional[str] = Field(None, max_length=255)
    interviewer_ids: Optional[List[UUID]] = Field(None, min_items=1)
    feedback: Optional[str] = Field(None, description="Interview feedback")
    rating: Optional[int] = Field(None, ge=1, le=10, description="Interview rating")
    recommendation: Optional[str] = Field(None, description="Recommendation")
    questions_asked: Optional[List[str]] = Field(None)
    candidate_responses: Optional[List[str]] = Field(None, description="Candidate responses")
    technical_assessment: Optional[Dict[str, Any]] = Field(None, description="Technical assessment")


class InterviewResponse(BaseModel):
    """Interview response schema"""
    id: UUID
    application_id: UUID
    interview_type: InterviewType
    status: InterviewStatus
    scheduled_date: datetime
    duration_minutes: int
    location: Optional[str]
    interviewer_ids: List[UUID]
    feedback: Optional[str]
    rating: Optional[int]
    recommendation: Optional[str]
    questions_asked: Optional[List[str]]
    candidate_responses: Optional[List[str]]
    technical_assessment: Optional[Dict[str, Any]]
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class JobOfferCreate(BaseModel):
    """Job offer creation schema"""
    application_id: UUID = Field(..., description="Application ID")
    position_title: str = Field(..., max_length=200, description="Position title")
    department_id: UUID = Field(..., description="Department ID")
    start_date: date = Field(..., description="Start date")
    base_salary: Decimal = Field(..., description="Base salary")
    currency: str = Field(default="USD", max_length=3, description="Currency code")
    bonus: Optional[Decimal] = Field(None, description="Bonus amount")
    benefits: Optional[str] = Field(None, description="Benefits description")
    response_deadline: Optional[date] = Field(None, description="Response deadline")
    probation_period_months: Optional[int] = Field(None, ge=0, description="Probation period")
    notice_period_days: Optional[int] = Field(None, ge=0, description="Notice period")
    additional_terms: Optional[str] = Field(None, description="Additional terms")


class JobOfferUpdate(BaseModel):
    """Job offer update schema"""
    position_title: Optional[str] = Field(None, max_length=200)
    start_date: Optional[date] = Field(None)
    base_salary: Optional[Decimal] = Field(None)
    bonus: Optional[Decimal] = Field(None)
    benefits: Optional[str] = Field(None)
    status: Optional[str] = Field(None, description="Offer status")
    response_deadline: Optional[date] = Field(None)
    response_date: Optional[datetime] = Field(None)
    probation_period_months: Optional[int] = Field(None, ge=0)
    notice_period_days: Optional[int] = Field(None, ge=0)
    additional_terms: Optional[str] = Field(None)


class JobOfferResponse(BaseModel):
    """Job offer response schema"""
    id: UUID
    application_id: UUID
    position_title: str
    department_id: UUID
    start_date: date
    base_salary: Decimal
    currency: str
    bonus: Optional[Decimal]
    benefits: Optional[str]
    status: str
    offered_date: datetime
    response_deadline: Optional[date]
    response_date: Optional[datetime]
    offer_letter_url: Optional[str]
    contract_url: Optional[str]
    probation_period_months: Optional[int]
    notice_period_days: Optional[int]
    additional_terms: Optional[str]
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class JobPostingListResponse(BaseModel):
    """Job posting list response"""
    items: List[JobPostingResponse]
    total: int
    page: int
    size: int
    pages: int


class CandidateListResponse(BaseModel):
    """Candidate list response"""
    items: List[CandidateResponse]
    total: int
    page: int
    size: int
    pages: int


class JobApplicationListResponse(BaseModel):
    """Job application list response"""
    items: List[JobApplicationResponse]
    total: int
    page: int
    size: int
    pages: int


class InterviewListResponse(BaseModel):
    """Interview list response"""
    items: List[InterviewResponse]
    total: int
    page: int
    size: int
    pages: int


class JobOfferListResponse(BaseModel):
    """Job offer list response"""
    items: List[JobOfferResponse]
    total: int
    page: int
    size: int
    pages: int
