"""Add cascade delete to approval_id foreign key

Revision ID: d3e353900775
Revises: 0a8137e3ef70
Create Date: 2025-05-01 21:20:07.382582

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'd3e353900775'
down_revision: Union[str, None] = '0a8137e3ef70'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # Drop the existing FK constraint
    op.drop_constraint(
        'approval_histories_approval_id_fkey',  # Constraint name might vary, check DB if needed
        'approval_histories',
        type_='foreignkey'
    )

    # Recreate FK with ondelete='CASCADE'
    op.create_foreign_key(
        'approval_histories_approval_id_fkey',
        'approval_histories',
        'approvals',
        ['approval_id'],
        ['id'],
        ondelete='CASCADE'
    )


def downgrade() -> None:
    # Drop CASCADE FK
    op.drop_constraint(
        'approval_histories_approval_id_fkey',
        'approval_histories',
        type_='foreignkey'
    )

    # Recreate FK WITHOUT CASCADE
    op.create_foreign_key(
        'approval_histories_approval_id_fkey',
        'approval_histories',
        'approvals',
        ['approval_id'],
        ['id']
    )
