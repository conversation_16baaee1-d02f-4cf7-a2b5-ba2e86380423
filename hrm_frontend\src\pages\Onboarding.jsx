/**
 * Advanced Employee Onboarding Page
 * Comprehensive onboarding workflow management with email notifications
 */

import React, { useState, useEffect } from 'react';
import {
  Users, Plus, Search, Filter, Calendar, AlertCircle, Send,
  Mail, User, FileText, UserPlus, Briefcase, Award, MessageSquare, MoreVertical, TrendingUp
} from 'lucide-react';
import { useAuth } from '../contexts/AuthContext';
import { PermissionGate } from '../components/ProtectedRoute';
import apiService from '../services/api';

export default function Onboarding() {
  const { user } = useAuth();
  const [activeTab, setActiveTab] = useState('pipeline');
  const [workflows, setWorkflows] = useState([]);
  const [templates, setTemplates] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [showQuickOnboardModal, setShowQuickOnboardModal] = useState(false);

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      setLoading(true);
      const [workflowsRes, templatesRes] = await Promise.all([
        apiService.get('/onboarding/onboarding'),
        apiService.get('/onboarding/templates')
      ]);

      setWorkflows(workflowsRes.items || []);
      setTemplates(templatesRes.items || []);
    } catch (error) {
      console.error('Error loading onboarding data:', error);
    } finally {
      setLoading(false);
    }
  };



  const completeWorkflow = async (workflowId) => {
    try {
      await apiService.post(`/onboarding/onboarding/${workflowId}/complete`);
      await loadData();
    } catch (error) {
      console.error('Error completing workflow:', error);
    }
  };

  const quickOnboardEmployee = async (employeeData) => {
    try {
      const response = await apiService.post('/simple-onboarding/simple-onboard', employeeData);
      await loadData();
      setShowQuickOnboardModal(false);

      // Show success message with credentials
      alert(`Employee onboarded successfully!\n\nEmployee ID: ${response.employee_id}\nLogin Details:\nEmail: ${response.email}\nPassword: ${response.temporary_password}\n\n${response.message}`);
    } catch (error) {
      console.error('Error in quick onboarding:', error);
      alert('Error creating employee. Please try again.');
    }
  };



  const filteredWorkflows = workflows.filter(workflow => {
    const matchesSearch = workflow.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         workflow.employee?.full_name?.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === 'all' || workflow.status === statusFilter;
    return matchesSearch && matchesStatus;
  });

  const renderPipelineTab = () => (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Onboarding Pipeline</h1>
          <p className="text-gray-600">Track and manage the onboarding process for new hires</p>
        </div>
        <PermissionGate permission="onboardingCreate">
          <button
            onClick={() => setShowQuickOnboardModal(true)}
            className="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg flex items-center gap-2"
          >
            <UserPlus className="w-4 h-4" />
            Quick Onboard
          </button>
        </PermissionGate>
      </div>

      {/* Filters */}
      <div className="bg-white rounded-lg shadow p-4">
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="flex-1">
            <div className="relative">
              <Search className="w-4 h-4 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
              <input
                type="text"
                placeholder="Search by employee name or workflow title..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
              />
            </div>
          </div>
          <div className="sm:w-48">
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
            >
              <option value="all">All Status</option>
              <option value="NOT_STARTED">Not Started</option>
              <option value="IN_PROGRESS">In Progress</option>
              <option value="COMPLETED">Completed</option>
              <option value="ON_HOLD">On Hold</option>
            </select>
          </div>
        </div>
      </div>



      {/* Workflows List */}
      <div className="bg-white rounded-lg shadow">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900">Active Onboarding Workflows</h3>
        </div>
        <div className="divide-y divide-gray-200">
          {loading ? (
            <div className="p-8 text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-orange-500 mx-auto"></div>
              <p className="mt-2 text-gray-600">Loading workflows...</p>
            </div>
          ) : filteredWorkflows.length === 0 ? (
            <div className="p-8 text-center">
              <UserPlus className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-600">No onboarding workflows found</p>
              <p className="text-sm text-gray-500 mt-1">Start a new onboarding process to get started</p>
            </div>
          ) : (
            filteredWorkflows.map((workflow) => (
              <WorkflowCard 
                key={workflow.id} 
                workflow={workflow} 
                onComplete={completeWorkflow}
              />
            ))
          )}
        </div>
      </div>
    </div>
  );

  const renderContent = () => {
    switch (activeTab) {
      case 'pipeline':
        return renderPipelineTab();
      case 'templates':
        return renderTemplatesTab();
      case 'analytics':
        return renderAnalyticsTab();
      default:
        return renderPipelineTab();
    }
  };

  const renderTemplatesTab = () => (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Onboarding Templates</h1>
          <p className="text-gray-600">Manage reusable onboarding workflow templates</p>
        </div>
        <PermissionGate permission="onboardingCreate">
          <button className="bg-orange-500 hover:bg-orange-600 text-white px-4 py-2 rounded-lg flex items-center gap-2">
            <Plus className="w-4 h-4" />
            Create Template
          </button>
        </PermissionGate>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {templates.map((template) => (
          <div key={template.id} className="bg-white rounded-lg shadow p-6">
            <div className="flex items-start justify-between mb-4">
              <div className="flex items-center">
                <div className="p-2 bg-blue-100 rounded-lg">
                  <FileText className="w-5 h-5 text-blue-600" />
                </div>
                <div className="ml-3">
                  <h3 className="text-lg font-semibold text-gray-900">{template.name}</h3>
                  <p className="text-sm text-gray-600">{template.workflow_type}</p>
                </div>
              </div>
            </div>
            <p className="text-gray-600 text-sm mb-4">{template.description}</p>
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-500">
                {template.task_templates?.length || 0} tasks
              </span>
              <button className="text-orange-600 hover:text-orange-700 text-sm font-medium">
                Use Template
              </button>
            </div>
          </div>
        ))}
      </div>
    </div>
  );

  const renderAnalyticsTab = () => (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold text-gray-900">Onboarding Analytics</h1>
        <p className="text-gray-600">Track onboarding performance and metrics</p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Completion Rate by Month</h3>
          <div className="h-64 flex items-center justify-center text-gray-500">
            Chart placeholder - Integration with chart library needed
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Average Time to Complete</h3>
          <div className="h-64 flex items-center justify-center text-gray-500">
            Chart placeholder - Integration with chart library needed
          </div>
        </div>
      </div>
    </div>
  );

  return (
    <div className="p-6 max-w-7xl mx-auto">
      {/* Tab Navigation */}
      <div className="mb-6">
        <nav className="flex space-x-8">
          {[
            { id: 'pipeline', label: 'Onboarding Pipeline', icon: Users },
            { id: 'templates', label: 'Templates', icon: FileText },
            { id: 'analytics', label: 'Analytics', icon: TrendingUp }
          ].map(({ id, label, icon: Icon }) => (
            <button
              key={id}
              onClick={() => setActiveTab(id)}
              className={`flex items-center gap-2 px-3 py-2 border-b-2 font-medium text-sm ${
                activeTab === id
                  ? 'border-orange-500 text-orange-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              <Icon className="w-4 h-4" />
              {label}
            </button>
          ))}
        </nav>
      </div>

      {/* Content */}
      {renderContent()}

      {/* Quick Onboard Modal */}
      {showQuickOnboardModal && (
        <QuickOnboardModal
          onClose={() => setShowQuickOnboardModal(false)}
          onSubmit={quickOnboardEmployee}
        />
      )}


    </div>
  );
}

// Helper function for status colors
const getStatusColor = (status) => {
  const colors = {
    'NOT_STARTED': 'bg-gray-100 text-gray-800',
    'IN_PROGRESS': 'bg-blue-100 text-blue-800',
    'COMPLETED': 'bg-green-100 text-green-800',
    'ON_HOLD': 'bg-yellow-100 text-yellow-800',
    'CANCELLED': 'bg-red-100 text-red-800'
  };
  return colors[status] || 'bg-gray-100 text-gray-800';
};

// Workflow Card Component
const WorkflowCard = ({ workflow, onComplete }) => {
  const [showDetails, setShowDetails] = useState(false);

  return (
    <div className="p-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <div className="flex-shrink-0">
            <div className="w-10 h-10 bg-orange-100 rounded-full flex items-center justify-center">
              <User className="w-5 h-5 text-orange-600" />
            </div>
          </div>
          <div className="flex-1 min-w-0">
            <div className="flex items-center space-x-2">
              <h3 className="text-lg font-semibold text-gray-900 truncate">
                {workflow.title}
              </h3>
              <span className={`px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(workflow.status)}`}>
                {workflow.status.replace('_', ' ')}
              </span>
            </div>
            <div className="flex items-center space-x-4 mt-1">
              <p className="text-sm text-gray-600">
                Employee: <span className="font-medium">{workflow.employee?.full_name || 'N/A'}</span>
              </p>
              <p className="text-sm text-gray-600">
                Start Date: {new Date(workflow.start_date).toLocaleDateString()}
              </p>
            </div>
          </div>
        </div>
        <div className="flex items-center space-x-3">
          <div className="text-right">
            <div className="text-sm font-medium text-gray-900">
              {workflow.progress_percentage}% Complete
            </div>
            <div className="w-24 bg-gray-200 rounded-full h-2 mt-1">
              <div
                className="bg-orange-500 h-2 rounded-full transition-all duration-300"
                style={{ width: `${workflow.progress_percentage}%` }}
              ></div>
            </div>
          </div>
          {workflow.status !== 'COMPLETED' && (
            <PermissionGate permission="onboardingUpdate">
              <button
                onClick={() => onComplete(workflow.id)}
                className="bg-green-500 hover:bg-green-600 text-white px-3 py-1 rounded text-sm flex items-center gap-1"
              >
                <CheckCircle className="w-4 h-4" />
                Complete
              </button>
            </PermissionGate>
          )}
          <button
            onClick={() => setShowDetails(!showDetails)}
            className="text-gray-400 hover:text-gray-600"
          >
            <MoreVertical className="w-5 h-5" />
          </button>
        </div>
      </div>

      {showDetails && (
        <div className="mt-4 pt-4 border-t border-gray-200">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <p className="text-sm font-medium text-gray-900">Assigned To</p>
              <p className="text-sm text-gray-600">{workflow.assigned_to?.full_name || 'Unassigned'}</p>
            </div>
            <div>
              <p className="text-sm font-medium text-gray-900">Tasks</p>
              <p className="text-sm text-gray-600">
                {workflow.completed_tasks} / {workflow.total_tasks} completed
              </p>
            </div>
            <div>
              <p className="text-sm font-medium text-gray-900">Expected Completion</p>
              <p className="text-sm text-gray-600">
                {workflow.expected_completion_date
                  ? new Date(workflow.expected_completion_date).toLocaleDateString()
                  : 'TBD'
                }
              </p>
            </div>
          </div>
          {workflow.description && (
            <div className="mt-3">
              <p className="text-sm font-medium text-gray-900">Description</p>
              <p className="text-sm text-gray-600">{workflow.description}</p>
            </div>
          )}
        </div>
      )}
    </div>
  );
};





// Quick Onboard Modal Component
const QuickOnboardModal = ({ onClose, onSubmit }) => {
  const [formData, setFormData] = useState({
    employee_name: '',
    employee_email: '',
    start_date: new Date().toISOString().split('T')[0]
  });
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      await onSubmit(formData);
    } catch (error) {
      console.error('Error submitting quick onboard:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-md w-full mx-4">
        <div className="px-6 py-4 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <h2 className="text-xl font-semibold text-gray-900 flex items-center gap-2">
              <UserPlus className="w-5 h-5 text-green-600" />
              Quick Onboard Employee
            </h2>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600"
            >
              ✕
            </button>
          </div>
          <p className="text-sm text-gray-600 mt-1">
            Enter basic details to create employee and start onboarding
          </p>
        </div>

        <form onSubmit={handleSubmit} className="p-6 space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Employee Name *
            </label>
            <input
              type="text"
              value={formData.employee_name}
              onChange={(e) => setFormData({...formData, employee_name: e.target.value})}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
              placeholder="e.g., John Doe"
              required
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Email Address *
            </label>
            <input
              type="email"
              value={formData.employee_email}
              onChange={(e) => setFormData({...formData, employee_email: e.target.value})}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
              placeholder="<EMAIL>"
              required
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Start Date
            </label>
            <input
              type="date"
              value={formData.start_date}
              onChange={(e) => setFormData({...formData, start_date: e.target.value})}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
            />
          </div>

          <div className="bg-green-50 border border-green-200 rounded-lg p-4">
            <h4 className="text-sm font-medium text-green-800 mb-2">What happens next?</h4>
            <ul className="text-xs text-green-700 space-y-1">
              <li>• Employee account will be created automatically</li>
              <li>• Welcome email with login credentials will be sent</li>
              <li>• Default onboarding workflow will be started</li>
              <li>• Onboarding tasks will be assigned</li>
              <li>• HR team will be notified</li>
            </ul>
          </div>

          <div className="flex justify-end space-x-3 pt-4">
            <button
              type="button"
              onClick={onClose}
              disabled={isSubmitting}
              className="px-4 py-2 text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-lg disabled:opacity-50"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={isSubmitting}
              className="px-6 py-2 bg-green-500 hover:bg-green-600 text-white rounded-lg flex items-center gap-2 disabled:opacity-50"
            >
              {isSubmitting ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                  Creating...
                </>
              ) : (
                <>
                  <Send className="w-4 h-4" />
                  Start Onboarding
                </>
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};
