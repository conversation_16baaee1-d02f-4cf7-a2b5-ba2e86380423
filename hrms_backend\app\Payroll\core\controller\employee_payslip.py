
from flask import jsonify, request
from flask.views import MethodView
from flask_smorest import Blueprint, abort
from core.middleware import roles_required
from core.services.payslip_attachment import PaySlipAttachementService
from core.utils.responseBuilder import ResponseBuilder
from core.utils.send_payslip import generate_payslip_pdf, send_payslip
from schemas import PayrollProcesSchema  # Import the 
import json  # Import for better print formatting

blueprint = Blueprint("employee_payslip", __name__, description="Operations for sending employee payslip")

@blueprint.route("/employee/payslip")
class EmployeePaySlip(MethodView):

    @roles_required(['admin'])
    @blueprint.arguments(PayrollProcesSchema)  # This passes 'data' automatically
    @blueprint.response(200, PayrollProcesSchema)
    def post(self, data):  # ✅ Add 'data' argument to accept parsed request body
        """Endpoint to send payslip via email with PDFMonkey."""

        payroll_ids = data.get("payroll_ids", [])  # No need to use request.json anymore

        if not payroll_ids:
            abort(400, message="No payroll records provided")

        service = PaySlipAttachementService()
        verified_payroll_ids = service.verify_payroll_ids(payroll_ids)

        for employee_payroll_id in verified_payroll_ids:
            emp_data_payslip = service.generate_payslip_dynamic_attachment(employee_payroll_id)
            # print(f"employee ,", emp_data_payslip.get("employee", {}).get("employee", {}).get("email",""))

            employee_email = emp_data_payslip.get("employee", {}).get("employee", {}).get("email") #emp_data_payslip["employee"]["email"]

            # Generate the PDF from PDFMonkey
            pdf_url = generate_payslip_pdf(emp_data_payslip)

            if not pdf_url:
                # print(f"🚨 ERROR: PDF generation failed for {employee_email}")
                return ResponseBuilder(data={"error": "Failed to generate PDF"}, status_code=500).build()

            # Send the payslip email
            response = send_payslip(employee_email, pdf_url)

            # Return the response
            if "error" in response:
                return ResponseBuilder(data=response, status_code=500).build()

        return ResponseBuilder(
            data={"message": f"Payslip sent successfully to {employee_email}"},
            status_code=200
        ).build()
