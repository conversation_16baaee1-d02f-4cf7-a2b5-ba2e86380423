from core.repositories.employee import EmployeeRepository
from core.models.employees import EmployeeModel
from core.repositories.user import UserRepository
from core.databases.database import db
from sqlalchemy import or_

class OperatorService:
    def __init__(self) -> None:
        self.repository = EmployeeRepository()

    def createOperator(self, id, role):
        update_query = EmployeeModel.query.filter_by(id=id).update({"role": role})
        db.session.commit()
        return update_query

    
    def fetchAll(self):
        operators = EmployeeModel.query.filter(
        EmployeeModel.user_id == UserRepository().authUserId(),
            or_(
                EmployeeModel.role == "Payroll Admin",
                EmployeeModel.role == "Approver"
            )
        ).order_by(EmployeeModel.created_at.desc()).all()

        total_operator = len(operators)
        return operators, total_operator

    def getOperators<PERSON><PERSON><PERSON><PERSON>(self, Kwarg):
        return self.repository.getEmployeeByKeys(Kwarg)
        
    def deleteOperator(self, id):
        update_query = EmployeeModel.query.filter_by(id=id).update({"role": "Employee"})
        db.session.commit()
        return update_query