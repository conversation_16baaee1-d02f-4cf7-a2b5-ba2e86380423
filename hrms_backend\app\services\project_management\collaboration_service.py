from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, func
from typing import Optional, List, Dict, Any
from uuid import UUID
from datetime import datetime
from fastapi import HTTPException, status
import logging

from ...db.models.project import Project, ProjectAssignment
from ...db.models.kanban import KanbanBoard, KanbanBoardMember, KanbanCard
from ...db.models.employee import Employee
from ...schemas.kanban import KanbanBoardMemberCreate, KanbanBoardMemberUpdate
from ...core.security import CurrentUser
from ...core.websocket_manager import notification_manager

logger = logging.getLogger(__name__)


class CollaborationService:
    """Service for managing project collaboration and team invitations"""

    async def invite_user_to_board(
        self,
        db: Session,
        board_id: UUID,
        member_data: KanbanBoardMemberCreate,
        current_user: CurrentUser
    ) -> Dict[str, Any]:
        """Invite a user to join a kanban board"""
        try:
            # Verify board exists and user has permission
            board = await self._get_accessible_board(db, board_id, current_user, require_admin=True)
            if not board:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Board not found or insufficient permissions"
                )

            # Verify invitee exists
            invitee = db.query(Employee).filter(
                Employee.id == member_data.employee_id,
                Employee.organization_id == current_user.organization_id,
                Employee.is_active == True
            ).first()

            if not invitee:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Employee not found"
                )

            # Check if user is already a member
            existing_member = db.query(KanbanBoardMember).filter(
                KanbanBoardMember.board_id == board_id,
                KanbanBoardMember.employee_id == member_data.employee_id
            ).first()

            if existing_member:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="User is already a member of this board"
                )

            # Create board membership
            board_member = KanbanBoardMember(
                board_id=board_id,
                employee_id=member_data.employee_id,
                role=member_data.role,
                can_edit_board=member_data.can_edit_board,
                can_manage_members=member_data.can_manage_members,
                can_create_cards=member_data.can_create_cards,
                can_edit_cards=member_data.can_edit_cards,
                can_delete_cards=member_data.can_delete_cards,
                invited_by=current_user.user_id,
                invited_at=datetime.utcnow()
            )

            db.add(board_member)
            db.commit()
            db.refresh(board_member)

            # Send notification to invitee
            invitation_data = {
                "board_id": str(board_id),
                "board_name": board.name,
                "invited_by": current_user.email,
                "role": member_data.role.value,
                "invitation_date": datetime.utcnow().isoformat()
            }

            await notification_manager.notify_user(
                str(member_data.employee_id),
                "board_invitation",
                invitation_data
            )

            # Send activity notification to board
            activity_data = {
                "type": "member_added",
                "member_name": f"{invitee.first_name} {invitee.last_name}",
                "member_email": invitee.email,
                "invited_by": current_user.email,
                "timestamp": datetime.utcnow().isoformat()
            }

            await notification_manager.notify_kanban_update(str(board_id), activity_data)

            logger.info(f"User {invitee.email} invited to board {board.name} by {current_user.email}")

            return {
                "member_id": str(board_member.id),
                "employee_id": str(invitee.id),
                "employee_name": f"{invitee.first_name} {invitee.last_name}",
                "employee_email": invitee.email,
                "role": member_data.role.value,
                "permissions": {
                    "can_edit_board": member_data.can_edit_board,
                    "can_manage_members": member_data.can_manage_members,
                    "can_create_cards": member_data.can_create_cards,
                    "can_edit_cards": member_data.can_edit_cards,
                    "can_delete_cards": member_data.can_delete_cards
                },
                "invited_at": board_member.invited_at.isoformat()
            }

        except HTTPException:
            raise
        except Exception as e:
            db.rollback()
            logger.error(f"Error inviting user to board: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Error inviting user to board"
            )

    async def remove_board_member(
        self,
        db: Session,
        board_id: UUID,
        member_id: UUID,
        current_user: CurrentUser
    ) -> Dict[str, Any]:
        """Remove a member from a kanban board"""
        try:
            # Verify board access and admin permissions
            board = await self._get_accessible_board(db, board_id, current_user, require_admin=True)
            if not board:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Board not found or insufficient permissions"
                )

            # Find the member
            member = db.query(KanbanBoardMember).filter(
                KanbanBoardMember.id == member_id,
                KanbanBoardMember.board_id == board_id
            ).first()

            if not member:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Board member not found"
                )

            # Get employee info for notifications
            employee = db.query(Employee).filter(Employee.id == member.employee_id).first()

            # Remove member
            db.delete(member)
            db.commit()

            # Send notification to removed member
            removal_data = {
                "board_id": str(board_id),
                "board_name": board.name,
                "removed_by": current_user.email,
                "removal_date": datetime.utcnow().isoformat()
            }

            if employee:
                await notification_manager.notify_user(
                    str(employee.id),
                    "board_removal",
                    removal_data
                )

            # Send activity notification to board
            activity_data = {
                "type": "member_removed",
                "member_name": f"{employee.first_name} {employee.last_name}" if employee else "Unknown",
                "removed_by": current_user.email,
                "timestamp": datetime.utcnow().isoformat()
            }

            await notification_manager.notify_kanban_update(str(board_id), activity_data)

            logger.info(f"Member {employee.email if employee else member_id} removed from board {board.name}")

            return {
                "success": True,
                "message": "Member removed successfully",
                "removed_member": {
                    "id": str(member_id),
                    "name": f"{employee.first_name} {employee.last_name}" if employee else "Unknown",
                    "email": employee.email if employee else None
                }
            }

        except HTTPException:
            raise
        except Exception as e:
            db.rollback()
            logger.error(f"Error removing board member: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Error removing board member"
            )

    async def update_member_permissions(
        self,
        db: Session,
        board_id: UUID,
        member_id: UUID,
        permission_updates: KanbanBoardMemberUpdate,
        current_user: CurrentUser
    ) -> Dict[str, Any]:
        """Update member permissions on a kanban board"""
        try:
            # Verify board access and admin permissions
            board = await self._get_accessible_board(db, board_id, current_user, require_admin=True)
            if not board:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Board not found or insufficient permissions"
                )

            # Find the member
            member = db.query(KanbanBoardMember).filter(
                KanbanBoardMember.id == member_id,
                KanbanBoardMember.board_id == board_id
            ).first()

            if not member:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Board member not found"
                )

            # Update permissions
            update_data = permission_updates.dict(exclude_unset=True)
            for field, value in update_data.items():
                setattr(member, field, value)

            member.updated_at = datetime.utcnow()
            db.commit()
            db.refresh(member)

            # Get employee info
            employee = db.query(Employee).filter(Employee.id == member.employee_id).first()

            # Send notification to member about permission changes
            permission_data = {
                "board_id": str(board_id),
                "board_name": board.name,
                "updated_by": current_user.email,
                "new_permissions": update_data,
                "update_date": datetime.utcnow().isoformat()
            }

            if employee:
                await notification_manager.notify_user(
                    str(employee.id),
                    "permission_update",
                    permission_data
                )

            logger.info(f"Permissions updated for member {employee.email if employee else member_id} on board {board.name}")

            return {
                "member_id": str(member.id),
                "employee_name": f"{employee.first_name} {employee.last_name}" if employee else "Unknown",
                "updated_permissions": update_data,
                "updated_at": member.updated_at.isoformat()
            }

        except HTTPException:
            raise
        except Exception as e:
            db.rollback()
            logger.error(f"Error updating member permissions: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Error updating member permissions"
            )

    async def get_board_members(
        self,
        db: Session,
        board_id: UUID,
        current_user: CurrentUser
    ) -> List[Dict[str, Any]]:
        """Get all members of a kanban board"""
        try:
            # Verify board access
            board = await self._get_accessible_board(db, board_id, current_user)
            if not board:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Board not found or insufficient permissions"
                )

            # Get board members with employee details
            members = db.query(KanbanBoardMember).join(Employee).filter(
                KanbanBoardMember.board_id == board_id,
                Employee.is_active == True
            ).all()

            return [
                {
                    "member_id": str(member.id),
                    "employee_id": str(member.employee_id),
                    "employee_name": f"{member.employee.first_name} {member.employee.last_name}",
                    "employee_email": member.employee.email,
                    "role": member.role.value,
                    "permissions": {
                        "can_edit_board": member.can_edit_board,
                        "can_manage_members": member.can_manage_members,
                        "can_create_cards": member.can_create_cards,
                        "can_edit_cards": member.can_edit_cards,
                        "can_delete_cards": member.can_delete_cards
                    },
                    "joined_at": member.invited_at.isoformat() if member.invited_at else None,
                    "is_admin": member.is_admin
                }
                for member in members
            ]

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error getting board members: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Error retrieving board members"
            )

    async def _get_accessible_board(
        self,
        db: Session,
        board_id: UUID,
        current_user: CurrentUser,
        require_admin: bool = False
    ) -> Optional[KanbanBoard]:
        """Get board if user has access to it"""
        
        board = db.query(KanbanBoard).filter(
            KanbanBoard.id == board_id,
            KanbanBoard.organization_id == current_user.organization_id
        ).first()

        if not board:
            return None

        # SuperAdmin has access to all boards
        if current_user.role.upper() == "SUPER_ADMIN":
            return board

        # Check if user is owner
        if board.owner_id == current_user.user_id:
            return board

        # Check if user is a board member
        member = db.query(KanbanBoardMember).filter(
            KanbanBoardMember.board_id == board_id,
            KanbanBoardMember.employee_id == current_user.user_id
        ).first()

        if member:
            if require_admin and not (member.is_admin or member.can_manage_members):
                return None
            return board

        # Check if board is public
        if board.is_public:
            return board

        return None
