import casbin
import os
from typing import List, Optional
from fastapi import HTTPException, status
from sqlalchemy.orm import Session
import logging

from .config import settings

logger = logging.getLogger(__name__)


class RBACManager:
    """Casbin RBAC Manager for permission management"""

    def __init__(self):
        self.enforcer = None
        self._initialize_enforcer()

    def _initialize_enforcer(self):
        """Initialize Casbin enforcer"""
        try:
            model_path = os.path.join(os.path.dirname(__file__), "..", "casbin", "model.conf")
            policy_path = os.path.join(os.path.dirname(__file__), "..", "casbin", "policy.csv")

            self.enforcer = casbin.Enforcer(model_path, policy_path)
            logger.info("✅ Casbin enforcer initialized successfully")
        except Exception as e:
            logger.error(f"❌ Failed to initialize Casbin enforcer: {e}")
            raise

    def check_permission(self, user_role: str, resource: str, action: str) -> bool:
        """Check if user has permission for resource and action"""
        try:
            return self.enforcer.enforce(user_role, resource, action)
        except Exception as e:
            logger.error(f"Error checking permission: {e}")
            return False

    def add_policy(self, role: str, resource: str, action: str) -> bool:
        """Add a new policy"""
        try:
            return self.enforcer.add_policy(role, resource, action)
        except Exception as e:
            logger.error(f"Error adding policy: {e}")
            return False

    def remove_policy(self, role: str, resource: str, action: str) -> bool:
        """Remove a policy"""
        try:
            return self.enforcer.remove_policy(role, resource, action)
        except Exception as e:
            logger.error(f"Error removing policy: {e}")
            return False

    def add_role_inheritance(self, child_role: str, parent_role: str) -> bool:
        """Add role inheritance"""
        try:
            return self.enforcer.add_grouping_policy(child_role, parent_role)
        except Exception as e:
            logger.error(f"Error adding role inheritance: {e}")
            return False

    def get_roles_for_user(self, user: str) -> List[str]:
        """Get all roles for a user"""
        try:
            return self.enforcer.get_roles_for_user(user)
        except Exception as e:
            logger.error(f"Error getting roles for user: {e}")
            return []

    def get_permissions_for_user(self, user: str) -> List[List[str]]:
        """Get all permissions for a user"""
        try:
            return self.enforcer.get_permissions_for_user(user)
        except Exception as e:
            logger.error(f"Error getting permissions for user: {e}")
            return []

    def save_policy(self) -> bool:
        """Save policy to file"""
        try:
            return self.enforcer.save_policy()
        except Exception as e:
            logger.error(f"Error saving policy: {e}")
            return False


# Global RBAC manager instance
rbac_manager = RBACManager()


# Permission constants
class Permissions:
    """Permission constants for the HRMS system"""

    # Employee permissions
    EMPLOYEE_READ = "employee:read"
    EMPLOYEE_CREATE = "employee:create"
    EMPLOYEE_UPDATE = "employee:update"
    EMPLOYEE_DELETE = "employee:delete"
    EMPLOYEE_PROFILE_UPDATE = "employee:profile:update"

    # Attendance permissions
    ATTENDANCE_READ = "attendance:read"
    ATTENDANCE_CREATE = "attendance:create"
    ATTENDANCE_UPDATE = "attendance:update"
    ATTENDANCE_DELETE = "attendance:delete"
    ATTENDANCE_APPROVE = "attendance:approve"

    # Leave permissions
    LEAVE_READ = "leave:read"
    LEAVE_CREATE = "leave:create"
    LEAVE_UPDATE = "leave:update"
    LEAVE_DELETE = "leave:delete"
    LEAVE_APPROVE = "leave:approve"
    LEAVE_ADMIN = "leave:admin"

    # Timesheet permissions
    TIMESHEET_READ = "timesheet:read"
    TIMESHEET_CREATE = "timesheet:create"
    TIMESHEET_UPDATE = "timesheet:update"
    TIMESHEET_DELETE = "timesheet:delete"
    TIMESHEET_APPROVE = "timesheet:approve"

    # Shift permissions
    SHIFT_READ = "shift:read"
    SHIFT_CREATE = "shift:create"
    SHIFT_UPDATE = "shift:update"
    SHIFT_DELETE = "shift:delete"
    SHIFT_ASSIGN = "shift:assign"
    SHIFT_ADMIN = "shift:admin"

    # Onboarding permissions
    ONBOARDING_READ = "onboarding:read"
    ONBOARDING_CREATE = "onboarding:create"
    ONBOARDING_UPDATE = "onboarding:update"
    ONBOARDING_DELETE = "onboarding:delete"
    ONBOARDING_MANAGE = "onboarding:manage"

    # Project permissions
    PROJECT_READ = "project:read"
    PROJECT_CREATE = "project:create"
    PROJECT_UPDATE = "project:update"
    PROJECT_DELETE = "project:delete"
    PROJECT_MANAGE = "project:manage"

    # Task permissions
    TASK_READ = "task:read"
    TASK_CREATE = "task:create"
    TASK_UPDATE = "task:update"
    TASK_DELETE = "task:delete"
    TASK_ASSIGN = "task:assign"

    # Kanban permissions
    KANBAN_READ = "kanban:read"
    KANBAN_CREATE = "kanban:create"
    KANBAN_UPDATE = "kanban:update"
    KANBAN_DELETE = "kanban:delete"
    KANBAN_MANAGE = "kanban:manage"

    # Ticket permissions
    TICKET_READ = "ticket:read"
    TICKET_CREATE = "ticket:create"
    TICKET_UPDATE = "ticket:update"
    TICKET_DELETE = "ticket:delete"
    TICKET_ASSIGN = "ticket:assign"
    TICKET_RESOLVE = "ticket:resolve"

    # Delegation permissions
    DELEGATION_READ = "delegation:read"
    DELEGATION_CREATE = "delegation:create"
    DELEGATION_UPDATE = "delegation:update"
    DELEGATION_DELETE = "delegation:delete"
    DELEGATION_APPROVE = "delegation:approve"

    # Payroll permissions
    PAYROLL_READ = "payroll:read"
    PAYROLL_CREATE = "payroll:create"
    PAYROLL_UPDATE = "payroll:update"
    PAYROLL_DELETE = "payroll:delete"
    PAYROLL_PROCESS = "payroll:process"

    # Performance permissions
    PERFORMANCE_READ = "performance:read"
    PERFORMANCE_CREATE = "performance:create"
    PERFORMANCE_UPDATE = "performance:update"
    PERFORMANCE_DELETE = "performance:delete"
    PERFORMANCE_REVIEW = "performance:review"

    # Engagement permissions
    ENGAGEMENT_READ = "engagement:read"
    ENGAGEMENT_CREATE = "engagement:create"
    ENGAGEMENT_UPDATE = "engagement:update"
    ENGAGEMENT_DELETE = "engagement:delete"
    ENGAGEMENT_ANALYZE = "engagement:analyze"

    # Admin permissions
    ADMIN_USERS = "admin:users"
    ADMIN_ROLES = "admin:roles"
    ADMIN_SETTINGS = "admin:settings"
    ADMIN_REPORTS = "admin:reports"


# Role constants
class Roles:
    """Role constants for the HRMS system"""
    SUPER_ADMIN = "SUPER_ADMIN"
    ADMIN = "ADMIN"
    HR = "HR"
    MANAGER = "MANAGER"
    EMPLOYEE = "EMPLOYEE"


def require_permission(permission: str):
    """Decorator to require specific permission for endpoint access"""
    def decorator(func):
        def wrapper(*args, **kwargs):
            # This will be implemented with FastAPI dependencies
            return func(*args, **kwargs)
        return wrapper
    return decorator
