#!/usr/bin/env python3
"""
Create test JWT token for API testing
"""

import sys
import os
from datetime import datetime, timedelta
from jose import jwt

# Add the app directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'app'))

from app.core.config import settings

def create_test_token():
    """Create a test JWT token for API testing"""
    
    # Test user data
    test_user_data = {
        "sub": "test-user-123",  # user_id
        "email": "<EMAIL>",
        "role": "ADMIN",
        "organization_id": "test-org-123",
        "exp": datetime.utcnow() + timedelta(hours=24)  # 24 hour expiry
    }
    
    # Create token
    token = jwt.encode(test_user_data, settings.SECRET_KEY, algorithm=settings.ALGORITHM)
    
    print("🔑 Test JWT Token Created")
    print("=" * 50)
    print(f"Token: {token}")
    print("\n📋 Usage:")
    print(f'curl -H "Authorization: Bearer {token}" http://localhost:8001/api/employees/')
    print("\n👤 Token contains:")
    print(f"  User ID: {test_user_data['sub']}")
    print(f"  Email: {test_user_data['email']}")
    print(f"  Role: {test_user_data['role']}")
    print(f"  Organization: {test_user_data['organization_id']}")
    print(f"  Expires: {test_user_data['exp']}")
    
    return token

if __name__ == "__main__":
    create_test_token()
