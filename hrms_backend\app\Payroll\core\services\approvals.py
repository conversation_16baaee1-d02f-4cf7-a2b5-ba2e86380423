from core.repositories.approvals import ApprovalsRepository

class ApprovalsService:
    def __init__(self) -> None:
        self.repository = ApprovalsRepository()

    def createApprovals(self, Kwargs):
        return self.repository.createApprovals(**Kwargs)
    
    def fetchAll(self):
        approvals = self.repository.fetchAll()
        total_approvals = len(approvals)
        return approvals, total_approvals
    
    def getApprovals(self, id):
        return self.repository.getApprovals(id)
    
    def getApprovalLevel(self, level):
        return self.repository.getApprovalLevel(level)
    
    def updateApprovals(self, id, Kwargs):
        return self.repository.updateApprovals(id, **Kwargs)
    
    def getApprovalsByKey(self, Kwarg):
        return self.repository.getApprovalsByKeys(Kwarg)
    
    def deleteApprovals(self, id):
        return self.repository.deleteApprovals(id)
    
    def findAndDelete(self, id):
        find_approval = self.getApprovalsByKey({ 'employee_id' : id })
        if find_approval is not None:
            self.deleteApprovals(find_approval[0].id)
        return