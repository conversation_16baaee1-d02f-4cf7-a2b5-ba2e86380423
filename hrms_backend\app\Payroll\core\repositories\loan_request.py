from core.models.employees import EmployeeModel
from core.models.loan_requests import LoanRequestModel
from core.databases.database import db
from core.repositories.user import UserRepository

class LoanRequestRepository:
    def __init__(self) -> None:
        pass

    @classmethod
    def createLoanRequest(self, month, principal, date, interest, status, duration, monthly_repayment, total_repayment, employee_id, disbursed_at, balance):
        employee = UserRepository().authUser()

        if not employee:
            raise Exception("Not authenticated")

        # ✔ Get associated user_id from EmployeeModel
        user_id = employee.user_id
        loan_requests = LoanRequestModel(
            month=month,
            principal=principal,
            date=date,
            interest=interest,
            status=status,
            duration=duration,
            monthly_repayment=monthly_repayment,
            total_repayment=total_repayment,
            employee_id=employee_id,
            disbursed_at=disbursed_at,
            balance=balance,
            user_id=user_id        )
        db.session.add(loan_requests)
        db.session.commit()
        return loan_requests

    @classmethod
    def fetchAll(self):
        return LoanRequestModel.query.filter_by(user_id=UserRepository().authUserId()).order_by(LoanRequestModel.timestamp.desc()).all()
    
    @classmethod
    def getLoanRequests(cls):
         employee = UserRepository().authUser()
         if not employee or not isinstance(employee, EmployeeModel):
            raise Exception("Unauthorized or invalid employee.")
         
         return LoanRequestModel.query.filter_by(employee_id=employee.id).order_by(LoanRequestModel.timestamp.desc()).all()


    
    @classmethod
    def getLoanRequestsByKeys(self, kwargs):
        return LoanRequestModel.query.filter_by(user_id=UserRepository().authUserId(), **kwargs).all()
    
    @classmethod
    def updatePendingLoanByEmployee(self, employee_id, update_data):
        loan_requests = LoanRequestModel.query.filter_by( employee_id=employee_id, status="pending").first()
        if not loan_requests:
            raise Exception("Loan not found or already disbursed")
        for key, value in update_data.items():
            setattr(loan_requests, key, value)
        db.session.commit()
        return loan_requests
        

    @classmethod
    def deleteLoanRequests(self, id):
        LoanRequestModel.query.filter(LoanRequestModel.id == id).delete()
        db.session.commit()
        return
    

    @classmethod
    def updateLoanApprovalStatus(cls, loan_id, data):
        loan_request = LoanRequestModel.query.get(loan_id)
        if not loan_request:
           raise Exception("Loan Request not found")
    
          # Optional: only update if currently pending
        if loan_request.status != "pending":
           raise Exception("Only pending loan requests can be updated")

        loan_request.status = data["status"]
        loan_request.employee_id = data["employee_id"]

        db.session.commit()
        return loan_request
        