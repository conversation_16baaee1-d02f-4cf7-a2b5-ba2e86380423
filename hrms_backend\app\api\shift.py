from fastapi import APIRouter, HTTPException, status, Depends, Query
from sqlalchemy.orm import Session
from typing import List, Optional, Dict
from uuid import UUID
from datetime import date, time

from ..core.security import get_current_user, require_permission, CurrentUser
from ..core.rbac import Permissions
from ..db.session import get_db
from ..schemas.shift import (
    ShiftCreate, ShiftUpdate, ShiftResponse, ShiftListResponse,
    ShiftAssignmentCreate, ShiftAssignmentUpdate, ShiftAssignmentResponse,
    ShiftSwapRequest, ShiftSwapResponse, ShiftScheduleResponse
)
from ..services.shift_management.shift_service import ShiftService

router = APIRouter()
shift_service = ShiftService()

# Shift endpoints
@router.get("/", response_model=ShiftListResponse)
async def get_shifts(
    skip: int = Query(0, ge=0),
    limit: int = Query(20, ge=1, le=100),
    search: Optional[str] = Query(None),
    department_id: Optional[UUID] = Query(None),
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.ATTENDANCE_READ))
):
    """Get all shifts with filtering"""
    return await shift_service.get_shifts(
        db=db,
        skip=skip,
        limit=limit,
        search=search,
        department_id=department_id,
        current_user=current_user
    )

@router.post("/", response_model=ShiftResponse)
async def create_shift(
    shift_data: ShiftCreate,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.ATTENDANCE_CREATE))
):
    """Create a new shift"""
    return await shift_service.create_shift(db, shift_data, current_user)

@router.get("/{shift_id}", response_model=ShiftResponse)
async def get_shift(
    shift_id: UUID,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.ATTENDANCE_READ))
):
    """Get shift by ID"""
    return await shift_service.get_shift(db, shift_id, current_user)

@router.put("/{shift_id}", response_model=ShiftResponse)
async def update_shift(
    shift_id: UUID,
    shift_data: ShiftUpdate,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.ATTENDANCE_UPDATE))
):
    """Update shift"""
    return await shift_service.update_shift(db, shift_id, shift_data, current_user)

@router.delete("/{shift_id}")
async def delete_shift(
    shift_id: UUID,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.ATTENDANCE_DELETE))
):
    """Delete shift"""
    await shift_service.delete_shift(db, shift_id, current_user)
    return {"message": "Shift deleted successfully"}

# Shift assignment endpoints
@router.get("/assignments/", response_model=List[ShiftAssignmentResponse])
async def get_shift_assignments(
    employee_id: Optional[UUID] = Query(None),
    shift_id: Optional[UUID] = Query(None),
    start_date: Optional[date] = Query(None),
    end_date: Optional[date] = Query(None),
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.ATTENDANCE_READ))
):
    """Get shift assignments with filtering"""
    return await shift_service.get_shift_assignments(
        db=db,
        current_user=current_user,
        employee_id=employee_id,
        shift_id=shift_id,
        start_date=start_date,
        end_date=end_date
    )

@router.post("/assignments/", response_model=ShiftAssignmentResponse)
async def create_shift_assignment(
    assignment_data: ShiftAssignmentCreate,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.ATTENDANCE_CREATE))
):
    """Create shift assignment"""
    return await shift_service.create_shift_assignment(db, assignment_data, current_user)

@router.get("/my/schedule", response_model=ShiftScheduleResponse)
async def get_my_shift_schedule(
    start_date: Optional[date] = Query(None),
    end_date: Optional[date] = Query(None),
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(get_current_user)
):
    """Get current user's shift schedule"""
    return await shift_service.get_employee_schedule(
        db=db,
        employee_id=current_user.employee_id,
        current_user=current_user,
        start_date=start_date,
        end_date=end_date
    )

# Shift swap endpoints
@router.post("/swap/request", response_model=ShiftSwapResponse)
async def request_shift_swap(
    swap_request: ShiftSwapRequest,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(get_current_user)
):
    """Request shift swap"""
    return await shift_service.request_shift_swap(db, swap_request, current_user)

@router.get("/swap/requests", response_model=List[ShiftSwapResponse])
async def get_shift_swap_requests(
    status: Optional[str] = Query(None),
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.ATTENDANCE_UPDATE))
):
    """Get shift swap requests"""
    return await shift_service.get_shift_swap_requests(db, current_user, status)

@router.put("/swap/{swap_id}/approve")
async def approve_shift_swap(
    swap_id: UUID,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.ATTENDANCE_UPDATE))
):
    """Approve shift swap"""
    await shift_service.approve_shift_swap(db, swap_id, current_user)
    return {"message": "Shift swap approved successfully"}

@router.put("/swap/{swap_id}/reject")
async def reject_shift_swap(
    swap_id: UUID,
    reason: Optional[str] = None,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.ATTENDANCE_UPDATE))
):
    """Reject shift swap"""
    await shift_service.reject_shift_swap(db, swap_id, current_user, reason)
    return {"message": "Shift swap rejected successfully"}


# WFM Integration Endpoints
@router.get("/staffing/forecast", response_model=Dict)
async def get_staffing_forecast(
    start_date: date = Query(...),
    end_date: date = Query(...),
    department_id: Optional[UUID] = Query(None),
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.SHIFT_READ))
):
    """Get staffing forecast for WFM integration"""
    return await shift_service.get_staffing_forecast(
        db, current_user, start_date, end_date, department_id
    )


@router.post("/leave-impact/validate", response_model=Dict)
async def validate_leave_impact(
    employee_id: UUID,
    leave_start_date: date,
    leave_end_date: date,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.LEAVE_READ))
):
    """Validate impact of leave request on shift staffing"""
    return await shift_service.validate_leave_impact_on_staffing(
        db, employee_id, leave_start_date, leave_end_date, current_user
    )


@router.post("/roster/optimize", response_model=Dict)
async def generate_optimal_roster(
    start_date: date,
    end_date: date,
    department_id: Optional[UUID] = None,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.SHIFT_CREATE))
):
    """Generate optimal shift roster using WFM algorithms"""
    return await shift_service.generate_optimal_roster(
        db, start_date, end_date, department_id, current_user
    )


@router.post("/wfm/sync", response_model=Dict)
async def sync_with_wfm(
    wfm_system: str = "default",
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.SHIFT_ADMIN))
):
    """Sync shift data with external WFM system"""
    return await shift_service.sync_with_external_wfm(db, current_user, wfm_system)


@router.get("/analytics/utilization", response_model=Dict)
async def get_shift_utilization_analytics(
    start_date: Optional[date] = Query(None),
    end_date: Optional[date] = Query(None),
    department_id: Optional[UUID] = Query(None),
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.SHIFT_READ))
):
    """Get shift utilization analytics"""
    if not start_date:
        start_date = date.today().replace(day=1)  # Start of current month
    if not end_date:
        end_date = date.today()

    return await shift_service.get_staffing_forecast(
        db, current_user, start_date, end_date, department_id
    )


@router.get("/coverage/gaps", response_model=Dict)
async def identify_coverage_gaps(
    start_date: date = Query(...),
    end_date: date = Query(...),
    department_id: Optional[UUID] = Query(None),
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.SHIFT_READ))
):
    """Identify shift coverage gaps and staffing issues"""
    forecast = await shift_service.get_staffing_forecast(
        db, current_user, start_date, end_date, department_id
    )

    # Analyze forecast for gaps
    gaps = []
    for day in forecast.get("forecast", []):
        if day["utilization_rate"] < 70:  # Less than 70% staffed
            gaps.append({
                "date": day["date"],
                "utilization_rate": day["utilization_rate"],
                "severity": "critical" if day["utilization_rate"] < 50 else "moderate",
                "understaffed_shifts": [
                    shift for shift in day["shifts"]
                    if shift["assigned_count"] < (shift["max_capacity"] * 0.7)
                ]
            })

    return {
        "period": forecast["period"],
        "department_id": forecast["department_id"],
        "coverage_gaps": gaps,
        "summary": {
            "total_gap_days": len(gaps),
            "critical_gaps": len([g for g in gaps if g["severity"] == "critical"]),
            "moderate_gaps": len([g for g in gaps if g["severity"] == "moderate"])
        }
    }
