/**
 * Default Images Utility
 * Provides default images and placeholders for AgnoConnect HRM
 */

// AgnoConnect brand colors
const COLORS = {
  primaryDark: '#073763',
  primary: '#0B2A5A', 
  orange: '#F47C20',
  grayMedium: '#6E7C8E',
  grayDark: '#2E2E2E',
  white: '#FFFFFF'
};

// Generate SVG data URL
const generateSVGDataURL = (svgContent) => {
  return `data:image/svg+xml;base64,${btoa(svgContent)}`;
};

// Default profile avatar SVG
export const generateDefaultAvatar = (initials = 'AC', size = 100) => {
  const svg = `
    <svg width="${size}" height="${size}" viewBox="0 0 ${size} ${size}" xmlns="http://www.w3.org/2000/svg">
      <defs>
        <linearGradient id="avatarGradient" x1="0%" y1="0%" x2="100%" y2="100%">
          <stop offset="0%" style="stop-color:${COLORS.primaryDark};stop-opacity:1" />
          <stop offset="100%" style="stop-color:${COLORS.primary};stop-opacity:1" />
        </linearGradient>
      </defs>
      <rect width="${size}" height="${size}" fill="url(#avatarGradient)" rx="12"/>
      <text x="50%" y="50%" dominant-baseline="middle" text-anchor="middle" 
            fill="${COLORS.white}" font-family="Arial, sans-serif" 
            font-size="${size * 0.4}" font-weight="bold">${initials}</text>
    </svg>
  `;
  return generateSVGDataURL(svg);
};

// Default cover image - AgnoConnect branded
export const generateDefaultCover = (width = 1200, height = 300) => {
  const svg = `
    <svg width="${width}" height="${height}" viewBox="0 0 ${width} ${height}" xmlns="http://www.w3.org/2000/svg">
      <defs>
        <linearGradient id="coverGradient" x1="0%" y1="0%" x2="100%" y2="100%">
          <stop offset="0%" style="stop-color:${COLORS.primaryDark};stop-opacity:1" />
          <stop offset="50%" style="stop-color:${COLORS.primary};stop-opacity:1" />
          <stop offset="100%" style="stop-color:${COLORS.primaryDark};stop-opacity:1" />
        </linearGradient>
        <pattern id="dots" x="0" y="0" width="60" height="60" patternUnits="userSpaceOnUse">
          <circle cx="30" cy="30" r="2" fill="${COLORS.white}" opacity="0.1"/>
        </pattern>
      </defs>
      <rect width="${width}" height="${height}" fill="url(#coverGradient)"/>
      <rect width="${width}" height="${height}" fill="url(#dots)"/>
      <text x="50%" y="40%" dominant-baseline="middle" text-anchor="middle" 
            fill="${COLORS.white}" font-family="Arial, sans-serif" 
            font-size="48" font-weight="300" opacity="0.9">AgnoConnect</text>
      <text x="50%" y="60%" dominant-baseline="middle" text-anchor="middle" 
            fill="${COLORS.white}" font-family="Arial, sans-serif" 
            font-size="18" opacity="0.7">Human Resource Management</text>
    </svg>
  `;
  return generateSVGDataURL(svg);
};

// Office workspace background
export const generateOfficeCover = (width = 1200, height = 300) => {
  const svg = `
    <svg width="${width}" height="${height}" viewBox="0 0 ${width} ${height}" xmlns="http://www.w3.org/2000/svg">
      <defs>
        <linearGradient id="officeGradient" x1="0%" y1="0%" x2="100%" y2="100%">
          <stop offset="0%" style="stop-color:#f8fafc;stop-opacity:1" />
          <stop offset="100%" style="stop-color:#e2e8f0;stop-opacity:1" />
        </linearGradient>
      </defs>
      <rect width="${width}" height="${height}" fill="url(#officeGradient)"/>
      
      <!-- Office elements -->
      <rect x="100" y="150" width="200" height="120" fill="${COLORS.primary}" opacity="0.1" rx="8"/>
      <rect x="350" y="100" width="150" height="80" fill="${COLORS.orange}" opacity="0.1" rx="8"/>
      <rect x="550" y="180" width="180" height="90" fill="${COLORS.primaryDark}" opacity="0.1" rx="8"/>
      <rect x="800" y="120" width="160" height="100" fill="${COLORS.grayMedium}" opacity="0.1" rx="8"/>
      
      <!-- Overlay text -->
      <text x="50%" y="50%" dominant-baseline="middle" text-anchor="middle" 
            fill="${COLORS.primaryDark}" font-family="Arial, sans-serif" 
            font-size="36" font-weight="600" opacity="0.8">Modern Workspace</text>
    </svg>
  `;
  return generateSVGDataURL(svg);
};

// Team collaboration background
export const generateTeamCover = (width = 1200, height = 300) => {
  const svg = `
    <svg width="${width}" height="${height}" viewBox="0 0 ${width} ${height}" xmlns="http://www.w3.org/2000/svg">
      <defs>
        <linearGradient id="teamGradient" x1="0%" y1="0%" x2="100%" y2="100%">
          <stop offset="0%" style="stop-color:${COLORS.orange};stop-opacity:0.1" />
          <stop offset="100%" style="stop-color:${COLORS.primary};stop-opacity:0.1" />
        </linearGradient>
      </defs>
      <rect width="${width}" height="${height}" fill="${COLORS.white}"/>
      <rect width="${width}" height="${height}" fill="url(#teamGradient)"/>
      
      <!-- Team icons -->
      <circle cx="300" cy="150" r="25" fill="${COLORS.orange}" opacity="0.3"/>
      <circle cx="400" cy="120" r="25" fill="${COLORS.primary}" opacity="0.3"/>
      <circle cx="500" cy="150" r="25" fill="${COLORS.primaryDark}" opacity="0.3"/>
      <circle cx="600" cy="130" r="25" fill="${COLORS.grayMedium}" opacity="0.3"/>
      <circle cx="700" cy="150" r="25" fill="${COLORS.orange}" opacity="0.3"/>
      
      <!-- Connection lines -->
      <line x1="325" y1="150" x2="375" y2="120" stroke="${COLORS.primary}" stroke-width="2" opacity="0.2"/>
      <line x1="425" y1="120" x2="475" y2="150" stroke="${COLORS.primary}" stroke-width="2" opacity="0.2"/>
      <line x1="525" y1="150" x2="575" y2="130" stroke="${COLORS.primary}" stroke-width="2" opacity="0.2"/>
      <line x1="625" y1="130" x2="675" y2="150" stroke="${COLORS.primary}" stroke-width="2" opacity="0.2"/>
      
      <text x="50%" y="70%" dominant-baseline="middle" text-anchor="middle" 
            fill="${COLORS.primaryDark}" font-family="Arial, sans-serif" 
            font-size="32" font-weight="500">Team Collaboration</text>
    </svg>
  `;
  return generateSVGDataURL(svg);
};

// Abstract geometric background
export const generateAbstractCover = (width = 1200, height = 300) => {
  const svg = `
    <svg width="${width}" height="${height}" viewBox="0 0 ${width} ${height}" xmlns="http://www.w3.org/2000/svg">
      <defs>
        <linearGradient id="abstractGradient" x1="0%" y1="0%" x2="100%" y2="100%">
          <stop offset="0%" style="stop-color:${COLORS.primaryDark};stop-opacity:1" />
          <stop offset="50%" style="stop-color:${COLORS.orange};stop-opacity:1" />
          <stop offset="100%" style="stop-color:${COLORS.primary};stop-opacity:1" />
        </linearGradient>
      </defs>
      <rect width="${width}" height="${height}" fill="url(#abstractGradient)"/>
      
      <!-- Geometric shapes -->
      <polygon points="200,50 300,100 250,200 150,150" fill="${COLORS.white}" opacity="0.1"/>
      <polygon points="500,80 650,120 600,220 450,180" fill="${COLORS.white}" opacity="0.1"/>
      <polygon points="800,60 950,110 900,190 750,140" fill="${COLORS.white}" opacity="0.1"/>
      
      <circle cx="350" cy="200" r="40" fill="${COLORS.white}" opacity="0.1"/>
      <circle cx="750" cy="80" r="30" fill="${COLORS.white}" opacity="0.1"/>
      
      <text x="50%" y="50%" dominant-baseline="middle" text-anchor="middle" 
            fill="${COLORS.white}" font-family="Arial, sans-serif" 
            font-size="42" font-weight="300" opacity="0.9">Innovation</text>
    </svg>
  `;
  return generateSVGDataURL(svg);
};

// Export all default images
export const defaultImages = {
  avatar: generateDefaultAvatar(),
  cover: generateDefaultCover(),
  office: generateOfficeCover(),
  team: generateTeamCover(),
  abstract: generateAbstractCover()
};

// Preset cover options for the upload component
export const coverPresets = [
  {
    id: 'default',
    name: 'AgnoConnect Default',
    type: 'generated',
    url: generateDefaultCover(),
    thumbnail: generateDefaultCover(300, 75)
  },
  {
    id: 'office',
    name: 'Modern Office',
    type: 'generated',
    url: generateOfficeCover(),
    thumbnail: generateOfficeCover(300, 75)
  },
  {
    id: 'team',
    name: 'Team Collaboration',
    type: 'generated',
    url: generateTeamCover(),
    thumbnail: generateTeamCover(300, 75)
  },
  {
    id: 'abstract',
    name: 'Innovation',
    type: 'generated',
    url: generateAbstractCover(),
    thumbnail: generateAbstractCover(300, 75)
  }
];
