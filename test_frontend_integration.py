#!/usr/bin/env python3
"""
Test the complete frontend-backend integration
"""

import requests
import json
import time

BACKEND_URL = "http://localhost:8085"
FRONTEND_URL = "http://localhost:5173"

def test_complete_integration():
    """Test the complete integration"""
    print("Testing Complete Frontend-Backend Integration")
    print("=" * 60)
    
    # Test 1: Backend Health
    try:
        response = requests.get(f"{BACKEND_URL}/health")
        if response.status_code == 200:
            print("✅ Backend health check: PASSED")
        else:
            print("❌ Backend health check: FAILED")
            return
    except Exception as e:
        print(f"❌ Backend not accessible: {e}")
        return
    
    # Test 2: Frontend Accessibility
    try:
        response = requests.get(FRONTEND_URL, timeout=5)
        if response.status_code == 200:
            print("✅ Frontend accessibility: PASSED")
        else:
            print("❌ Frontend accessibility: FAILED")
    except Exception as e:
        print(f"❌ Frontend not accessible: {e}")
    
    # Test 3: Authentication Flow
    try:
        credentials = {
            "email": "<EMAIL>",
            "password": "password123"
        }
        
        response = requests.post(f"{BACKEND_URL}/api/auth/login", json=credentials)
        if response.status_code == 200:
            data = response.json()
            token = data.get('access_token')
            user = data.get('user')
            print("✅ Authentication: PASSED")
            print(f"   User: {user.get('name')} ({user.get('email')})")
            print(f"   Role: {user.get('role')}")
        else:
            print("❌ Authentication: FAILED")
            return
    except Exception as e:
        print(f"❌ Authentication error: {e}")
        return
    
    # Test 4: Employees API
    try:
        headers = {"Authorization": f"Bearer {token}"}
        response = requests.get(f"{BACKEND_URL}/api/employees/", headers=headers)
        if response.status_code == 200:
            data = response.json()
            employees = data.get('employees', [])
            print("✅ Employees API: PASSED")
            print(f"   Found {len(employees)} employees")
            for emp in employees[:2]:  # Show first 2
                print(f"   - {emp.get('first_name')} {emp.get('last_name')} ({emp.get('employee_id')})")
        else:
            print("❌ Employees API: FAILED")
    except Exception as e:
        print(f"❌ Employees API error: {e}")
    
    # Test 5: Test all user roles
    test_users = [
        {"email": "<EMAIL>", "password": "password123", "role": "HR"},
        {"email": "<EMAIL>", "password": "password123", "role": "MANAGER"},
        {"email": "<EMAIL>", "password": "password123", "role": "EMPLOYEE"}
    ]
    
    print("\n📋 Testing all user roles:")
    for test_user in test_users:
        try:
            response = requests.post(f"{BACKEND_URL}/api/auth/login", json={
                "email": test_user["email"],
                "password": test_user["password"]
            })
            if response.status_code == 200:
                user_data = response.json().get('user', {})
                print(f"   ✅ {test_user['role']}: {user_data.get('name')} - Login successful")
            else:
                print(f"   ❌ {test_user['role']}: Login failed")
        except Exception as e:
            print(f"   ❌ {test_user['role']}: Error - {e}")
    
    print("\n🎉 Integration Test Complete!")
    print("\nNext Steps:")
    print("1. Open http://localhost:5173 in your browser")
    print("2. Login with any of these credentials:")
    print("   - Admin: <EMAIL> / password123")
    print("   - HR: <EMAIL> / password123")
    print("   - Manager: <EMAIL> / password123")
    print("   - Employee: <EMAIL> / password123")
    print("3. Navigate to the Employees page to see the integrated data")

if __name__ == "__main__":
    test_complete_integration()
