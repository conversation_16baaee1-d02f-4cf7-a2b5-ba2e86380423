# Role hierarchy
g, <PERSON><PERSON><PERSON>, SUPER_<PERSON>MIN
g, HR_<PERSON>NAGER, ADMI<PERSON>
g, MANAGER, HR_MANAGER
g, EMPL<PERSON>Y<PERSON>, MANAGER

# SUPER_ADMIN permissions (all permissions)
p, <PERSON><PERSON>ER_ADMIN, employee, read
p, <PERSON><PERSON><PERSON>_ADMIN, employee, create
p, <PERSON>UPER_ADMIN, employee, update
p, <PERSON><PERSON><PERSON>_ADMIN, employee, delete
p, SUPER_ADMIN, attendance, read
p, SUPER_ADMIN, attendance, create
p, SUPER_ADMIN, attendance, update
p, SUPER_ADMIN, attendance, delete
p, SUPER_ADMIN, attendance, approve
p, SUPER_ADMIN, leave, read
p, SUPER_ADMIN, leave, create
p, SUPER_ADMIN, leave, update
p, SUPER_ADMIN, leave, delete
p, SUPER_ADMIN, leave, approve
p, SUPER_ADMIN, timesheet, read
p, SUPER_ADMIN, timesheet, create
p, SUPER_ADMIN, timesheet, update
p, SUPER_ADMIN, timesheet, delete
p, <PERSON><PERSON>ER_ADMIN, timesheet, approve
p, SUPER_ADMIN, project, read
p, <PERSON>UPER_ADMIN, project, create
p, SUPER_ADMIN, project, update
p, SUPER_ADMIN, project, delete
p, SUPER_ADMIN, project, manage
p, SUPER_ADMIN, task, read
p, SUPER_ADMIN, task, create
p, SUPER_ADMIN, task, update
p, SUPER_ADMIN, task, delete
p, SUPER_ADMIN, task, assign
p, SUPER_ADMIN, kanban, read
p, SUPER_ADMIN, kanban, create
p, SUPER_ADMIN, kanban, update
p, SUPER_ADMIN, kanban, delete
p, SUPER_ADMIN, kanban, manage
p, SUPER_ADMIN, ticket, read
p, SUPER_ADMIN, ticket, create
p, SUPER_ADMIN, ticket, update
p, SUPER_ADMIN, ticket, delete
p, SUPER_ADMIN, ticket, assign
p, SUPER_ADMIN, ticket, resolve
p, SUPER_ADMIN, delegation, read
p, SUPER_ADMIN, delegation, create
p, SUPER_ADMIN, delegation, update
p, SUPER_ADMIN, delegation, delete
p, SUPER_ADMIN, delegation, approve
p, SUPER_ADMIN, payroll, read
p, SUPER_ADMIN, payroll, create
p, SUPER_ADMIN, payroll, update
p, SUPER_ADMIN, payroll, delete
p, SUPER_ADMIN, payroll, process
p, SUPER_ADMIN, performance, read
p, SUPER_ADMIN, performance, create
p, SUPER_ADMIN, performance, update
p, SUPER_ADMIN, performance, delete
p, SUPER_ADMIN, performance, review
p, SUPER_ADMIN, engagement, read
p, SUPER_ADMIN, engagement, create
p, SUPER_ADMIN, engagement, update
p, SUPER_ADMIN, engagement, delete
p, SUPER_ADMIN, engagement, analyze
p, SUPER_ADMIN, admin, users
p, SUPER_ADMIN, admin, roles
p, SUPER_ADMIN, admin, settings
p, SUPER_ADMIN, admin, reports
p, SUPER_ADMIN, onboarding, read
p, SUPER_ADMIN, onboarding, create
p, SUPER_ADMIN, onboarding, update
p, SUPER_ADMIN, onboarding, delete
p, SUPER_ADMIN, onboarding, manage

# ADMIN permissions (most permissions except super admin functions and project management)
p, ADMIN, employee, read
p, ADMIN, employee, create
p, ADMIN, employee, update
p, ADMIN, attendance, read
p, ADMIN, attendance, approve
p, ADMIN, leave, read
p, ADMIN, leave, approve
p, ADMIN, timesheet, read
p, ADMIN, timesheet, approve
# Project management removed from ADMIN role
p, ADMIN, ticket, read
p, ADMIN, ticket, assign
p, ADMIN, ticket, resolve
p, ADMIN, delegation, read
p, ADMIN, delegation, approve
p, ADMIN, payroll, read
p, ADMIN, payroll, process
p, ADMIN, performance, read
p, ADMIN, performance, review
p, ADMIN, engagement, read
p, ADMIN, engagement, analyze
p, ADMIN, admin, reports
p, ADMIN, onboarding, read
p, ADMIN, onboarding, create
p, ADMIN, onboarding, update
p, ADMIN, onboarding, manage

# HR permissions
p, HR, employee, read
p, HR, employee, create
p, HR, employee, update
p, HR, attendance, read
p, HR, leave, read
p, HR, leave, approve
p, HR, timesheet, read
# Project management removed from HR role
p, HR, ticket, read
p, HR, ticket, assign
p, HR, delegation, read
p, HR, payroll, read
p, HR, performance, read
p, HR, performance, review
p, HR, engagement, read
p, HR, engagement, create
p, HR, engagement, update
p, HR, engagement, analyze
p, HR, onboarding, read
p, HR, onboarding, create
p, HR, onboarding, update
p, HR, onboarding, manage

# MANAGER permissions
p, MANAGER, employee, read
p, MANAGER, attendance, read
p, MANAGER, leave, read
p, MANAGER, leave, approve
p, MANAGER, timesheet, read
p, MANAGER, timesheet, approve
p, MANAGER, project, read
p, MANAGER, project, create
p, MANAGER, project, update
p, MANAGER, task, read
p, MANAGER, task, create
p, MANAGER, task, update
p, MANAGER, task, assign
p, MANAGER, kanban, read
p, MANAGER, kanban, create
p, MANAGER, kanban, update
p, MANAGER, ticket, read
p, MANAGER, delegation, read
p, MANAGER, delegation, create
p, MANAGER, performance, read
p, MANAGER, performance, review
p, MANAGER, engagement, read

# EMPLOYEE permissions (basic permissions)
p, EMPLOYEE, employee, read
p, EMPLOYEE, attendance, read
p, EMPLOYEE, attendance, create
p, EMPLOYEE, leave, read
p, EMPLOYEE, leave, create
p, EMPLOYEE, timesheet, read
p, EMPLOYEE, timesheet, create
p, EMPLOYEE, timesheet, update
p, EMPLOYEE, project, read
p, EMPLOYEE, task, read
p, EMPLOYEE, task, update
p, EMPLOYEE, kanban, read
p, EMPLOYEE, kanban, update
p, EMPLOYEE, ticket, read
p, EMPLOYEE, ticket, create
p, EMPLOYEE, delegation, read
p, EMPLOYEE, performance, read
p, EMPLOYEE, engagement, read
