# AgnoConnect RBAC Implementation Guide

## Overview

This document provides a comprehensive guide to the Role-Based Access Control (RBAC) system implemented in the AgnoConnect HRM Frontend application. The system provides granular access control for views, tabs, sidebar items, and routes based on user roles, ensuring secure and appropriate access to AgnoConnect's HRM features.

## Architecture

### Core Components

1. **Permission Service** (`src/services/permissions.js`)
   - Centralized permission matrix
   - Role definitions and hierarchies
   - Permission checking utilities

2. **Authentication Context** (`src/contexts/AuthContext.jsx`)
   - Global authentication state management
   - User role management
   - Token handling and persistence

3. **Permission Hooks** (`src/hooks/usePermissions.js`)
   - Custom React hooks for permission checking
   - Memoized permission functions for performance
   - Role-based access utilities

4. **Protected Components** (`src/components/ProtectedRoute.jsx`)
   - Route-level protection
   - Component-level permission gates
   - Conditional rendering based on permissions

## Roles and Hierarchy

### Available Roles (in order of hierarchy)
1. **super_admin** - Full system access
2. **admin** - Administrative access
3. **hr** - Human resources access
4. **manager** - Team management access
5. **employee** - Basic employee access

### Permission Types
- **full** - Complete access to the resource
- **team_only** - Access limited to team members
- **assigned_only** - Access limited to assigned items
- **self_only** - Access limited to own data
- **none** - No access

## Implementation Examples

### 1. Sidebar Menu Protection

```jsx
import { PermissionGate } from './components/ProtectedRoute';

// Only show menu item if user has permission
<PermissionGate permission="employeeDirectory" hideOnDenied={true}>
  <NavItem icon={<Users />} label="Employees" />
</PermissionGate>
```

### 2. Tab Conditional Rendering

```jsx
import { usePermissions } from './hooks/usePermissions';

function Header() {
  const permissions = usePermissions();
  
  return (
    <div>
      {permissions.hasPermission('attendanceManagement') && (
        <Tab>Team Attendance</Tab>
      )}
    </div>
  );
}
```

### 3. Route Protection

```jsx
import { ProtectedRoute } from './components/ProtectedRoute';

// Protect entire page/route
<ProtectedRoute permission="payrollManagement">
  <PayrollPage />
</ProtectedRoute>
```

### 4. Conditional Content Based on Permission Level

```jsx
import { ConditionalRender } from './components/ProtectedRoute';

<ConditionalRender
  permission="employeeDirectory"
  fullAccess={<FullEmployeeList />}
  teamAccess={<TeamEmployeeList />}
  noAccess={<AccessDeniedMessage />}
/>
```

## Permission Matrix

### Dashboard & Core Features
| Feature | super_admin | admin | hr | manager | employee |
|---------|-------------|-------|----|---------|---------| 
| Dashboard | ✅ | ✅ | ✅ | ✅ | ✅ |
| Employee Directory | ✅ | ✅ | ✅ | Team Only | ❌ |
| Profile (Self) | ✅ | ✅ | ✅ | ✅ | ✅ |
| Profile Management | ✅ | ✅ | ✅ | Team Only | ❌ |

### Attendance & Leave
| Feature | super_admin | admin | hr | manager | employee |
|---------|-------------|-------|----|---------|---------| 
| Attendance (Self) | ✅ | ✅ | ✅ | ✅ | ✅ |
| Attendance Management | ✅ | ✅ | ✅ | Team Only | ❌ |
| Leave Requests (Self) | ✅ | ✅ | ✅ | ✅ | ✅ |
| Leave Management | ✅ | ✅ | ✅ | Team Only | ❌ |

### Payroll & Projects
| Feature | super_admin | admin | hr | manager | employee |
|---------|-------------|-------|----|---------|---------| 
| Payroll (Self) | ✅ | ✅ | ✅ | ✅ | ✅ |
| Payroll Management | ✅ | ✅ | ✅ | ❌ | ❌ |
| Project Boards | ✅ | ✅ | ✅ | Team Only | Assigned Only |
| Task Management | ✅ | ✅ | ✅ | Team Only | Assigned Only |

### System Administration
| Feature | super_admin | admin | hr | manager | employee |
|---------|-------------|-------|----|---------|---------| 
| System Settings | ✅ | ✅ | ❌ | ❌ | ❌ |
| System Logs | ✅ | ✅ | ❌ | ❌ | ❌ |

## Usage Guide

### 1. Basic Permission Checking

```jsx
import { usePermissions } from './hooks/usePermissions';

function MyComponent() {
  const permissions = usePermissions();
  
  if (permissions.hasPermission('employeeDirectory')) {
    return <EmployeeList />;
  }
  
  return <AccessDenied />;
}
```

### 2. Multiple Permission Checking

```jsx
import { useMultiplePermissions } from './hooks/usePermissions';

function Dashboard() {
  const permissions = useMultiplePermissions([
    'dashboard',
    'employeeDirectory',
    'attendanceManagement'
  ]);
  
  return (
    <div>
      {permissions.dashboard && <DashboardWidget />}
      {permissions.employeeDirectory && <EmployeeWidget />}
      {permissions.attendanceManagement && <AttendanceWidget />}
    </div>
  );
}
```

### 3. Role-Based Access

```jsx
import { useRoleAccess } from './hooks/usePermissions';

function AdminPanel() {
  const hasAdminAccess = useRoleAccess(['super_admin', 'admin']);
  
  if (!hasAdminAccess) {
    return <AccessDenied />;
  }
  
  return <AdminContent />;
}
```

### 4. Team/Assignment Context

```jsx
import { usePermission } from './hooks/usePermissions';

function ProjectCard({ project }) {
  const { canView, canEdit } = usePermission('projectKanbanBoards', {
    assignedTo: project.assignedUsers,
    isTeamMember: project.teamId === user.teamId
  });
  
  return (
    <div>
      {canView && <ProjectDetails />}
      {canEdit && <EditButton />}
    </div>
  );
}
```

## Authentication Flow

### 1. Login Process
```jsx
import { useAuth } from './contexts/AuthContext';

function LoginForm() {
  const { login, loading, error } = useAuth();
  
  const handleSubmit = async (credentials) => {
    const success = await login(credentials);
    if (success) {
      // User is now authenticated and role is set
    }
  };
}
```

### 2. Demo Accounts
For testing purposes, the following demo accounts are available:

- **<EMAIL>** / password123 - Super Administrator
- **<EMAIL>** / password123 - System Administrator  
- **<EMAIL>** / password123 - HR Manager
- **<EMAIL>** / password123 - Team Manager
- **<EMAIL>** / password123 - Employee

## Security Considerations

### Frontend Security
- ⚠️ **Important**: This RBAC system controls UI visibility only
- Backend API endpoints must implement their own authorization
- Never rely solely on frontend checks for sensitive operations
- Always validate permissions on the server side

### Best Practices
1. Use permission-based checks rather than role-based when possible
2. Implement defense in depth with both frontend and backend validation
3. Regularly audit permission assignments
4. Use the principle of least privilege
5. Test with different role combinations

## Extending the System

### Adding New Roles
1. Add role to `ROLES` constant in `permissions.js`
2. Update `ROLE_HIERARCHY` with appropriate level
3. Add role permissions to `PERMISSION_MATRIX`
4. Update demo accounts if needed

### Adding New Permissions
1. Add permission key to `PERMISSION_MATRIX`
2. Define access levels for each role
3. Use permission in components with `PermissionGate` or hooks
4. Update documentation

### Adding New Components
1. Wrap sensitive content with `PermissionGate`
2. Use `usePermissions` hook for complex logic
3. Implement proper fallback content
4. Test with different role combinations

## Troubleshooting

### Common Issues
1. **Permission not working**: Check if permission exists in matrix
2. **User not authenticated**: Verify token and user data in localStorage
3. **Role not recognized**: Ensure role is valid and in hierarchy
4. **Component not rendering**: Check permission requirements and user role

### Debugging
```jsx
import { useAuth, usePermissions } from './hooks';

function DebugInfo() {
  const { user, userRole, isAuthenticated } = useAuth();
  const permissions = usePermissions();
  
  console.log('User:', user);
  console.log('Role:', userRole);
  console.log('Authenticated:', isAuthenticated);
  console.log('Permissions:', permissions);
}
```

## File Structure

```
src/
├── components/
│   ├── ProtectedRoute.jsx     # Route protection components
│   ├── Sidebar.jsx            # RBAC-enabled sidebar
│   └── Header.jsx             # RBAC-enabled header
├── contexts/
│   └── AuthContext.jsx        # Authentication context
├── hooks/
│   └── usePermissions.js      # Permission hooks
├── pages/
│   ├── Employees.jsx          # RBAC-aware employee page
│   ├── Attendance.jsx         # RBAC-aware attendance page
│   └── ...                    # Other protected pages
├── services/
│   └── permissions.js         # Core permission logic
└── App.jsx                    # Main app with RBAC integration
```

This RBAC system provides a scalable, maintainable solution for controlling access to different parts of the AgnoConnect HRM application based on user roles and permissions.

## AgnoConnect Integration

The RBAC system is fully integrated with AgnoConnect's branding and design system:

- **Color Scheme**: Uses AgnoConnect's brand colors throughout the interface
- **Branding**: All references updated to reflect AgnoConnect identity
- **User Experience**: Consistent with AgnoConnect's professional design standards

## Quick Start

1. **Login with different roles** to see the AgnoConnect RBAC system in action:
   ```javascript
   // Available test users for AgnoConnect HRM
   const testUsers = [
     { email: '<EMAIL>', role: 'super_admin' },
     { email: '<EMAIL>', role: 'admin' },
     { email: '<EMAIL>', role: 'hr' },
     { email: '<EMAIL>', role: 'manager' },
     { email: '<EMAIL>', role: 'employee' }
   ];
   ```

2. **Observe the differences** in available features, navigation items, and permissions based on the selected role.

3. **Test the enhanced features** including attendance tracking, calendar management, and time tracking with appropriate role-based restrictions.
