from fastapi import APIRouter, HTTPException, status, Depends, Query
from sqlalchemy.orm import Session
from typing import List, Optional
from uuid import UUID
from datetime import date, datetime
from pydantic import BaseModel

from ..core.security import get_current_user, require_permission, CurrentUser
from ..core.rbac import Permissions
from ..db.session import get_db

router = APIRouter()

# Pydantic models for timesheet
class TimesheetEntryCreate(BaseModel):
    project_name: str
    task_name: str
    start_time: datetime
    end_time: Optional[datetime] = None
    duration_minutes: Optional[int] = None
    description: Optional[str] = None
    billable: bool = True

class TimesheetEntryUpdate(BaseModel):
    project_name: Optional[str] = None
    task_name: Optional[str] = None
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    duration_minutes: Optional[int] = None
    description: Optional[str] = None
    billable: Optional[bool] = None

class TimesheetEntryResponse(BaseModel):
    id: str
    employee_id: str
    project_name: str
    task_name: str
    start_time: datetime
    end_time: Optional[datetime]
    duration_minutes: int
    description: Optional[str]
    billable: bool
    status: str
    created_at: datetime
    updated_at: datetime

class TimesheetListResponse(BaseModel):
    entries: List[TimesheetEntryResponse]
    total: int
    total_hours: float

@router.get("/", response_model=TimesheetListResponse)
async def get_timesheets(
    employee_id: Optional[UUID] = Query(None),
    start_date: Optional[date] = Query(None),
    end_date: Optional[date] = Query(None),
    project_name: Optional[str] = Query(None),
    skip: int = Query(0, ge=0),
    limit: int = Query(20, ge=1, le=100),
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.TIMESHEET_READ))
):
    """Get timesheet entries with filtering"""
    # Sample data for now
    sample_entries = [
        {
            "id": "1",
            "employee_id": current_user.user_id,
            "project_name": "HRMS Development",
            "task_name": "Frontend Development",
            "start_time": datetime.now().replace(hour=9, minute=0, second=0),
            "end_time": datetime.now().replace(hour=12, minute=0, second=0),
            "duration_minutes": 180,
            "description": "Working on timesheet component",
            "billable": True,
            "status": "submitted",
            "created_at": datetime.now(),
            "updated_at": datetime.now()
        },
        {
            "id": "2",
            "employee_id": current_user.user_id,
            "project_name": "HRMS Development",
            "task_name": "API Integration",
            "start_time": datetime.now().replace(hour=13, minute=0, second=0),
            "end_time": datetime.now().replace(hour=17, minute=0, second=0),
            "duration_minutes": 240,
            "description": "Integrating timesheet APIs",
            "billable": True,
            "status": "draft",
            "created_at": datetime.now(),
            "updated_at": datetime.now()
        }
    ]

    total_hours = sum(entry["duration_minutes"] for entry in sample_entries) / 60

    return TimesheetListResponse(
        entries=sample_entries,
        total=len(sample_entries),
        total_hours=total_hours
    )

@router.post("/", response_model=TimesheetEntryResponse)
async def create_timesheet_entry(
    entry_data: TimesheetEntryCreate,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.TIMESHEET_CREATE))
):
    """Create new timesheet entry"""
    # Calculate duration if not provided
    duration_minutes = entry_data.duration_minutes
    if not duration_minutes and entry_data.start_time and entry_data.end_time:
        duration = entry_data.end_time - entry_data.start_time
        duration_minutes = int(duration.total_seconds() / 60)

    # Sample response
    new_entry = {
        "id": str(datetime.now().timestamp()),
        "employee_id": current_user.user_id,
        "project_name": entry_data.project_name,
        "task_name": entry_data.task_name,
        "start_time": entry_data.start_time,
        "end_time": entry_data.end_time,
        "duration_minutes": duration_minutes or 0,
        "description": entry_data.description,
        "billable": entry_data.billable,
        "status": "draft",
        "created_at": datetime.now(),
        "updated_at": datetime.now()
    }

    return TimesheetEntryResponse(**new_entry)

@router.get("/my", response_model=TimesheetListResponse)
async def get_my_timesheets(
    start_date: Optional[date] = Query(None),
    end_date: Optional[date] = Query(None),
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(get_current_user)
):
    """Get my timesheet entries"""
    return await get_timesheets(
        employee_id=UUID(current_user.user_id),
        start_date=start_date,
        end_date=end_date,
        db=db,
        current_user=current_user
    )

@router.post("/my", response_model=TimesheetEntryResponse)
async def create_my_timesheet_entry(
    entry_data: TimesheetEntryCreate,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(get_current_user)
):
    """Create my timesheet entry"""
    return await create_timesheet_entry(entry_data, db, current_user)

@router.get("/summary")
async def get_timesheet_summary(
    employee_id: Optional[UUID] = Query(None),
    start_date: Optional[date] = Query(None),
    end_date: Optional[date] = Query(None),
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(get_current_user)
):
    """Get timesheet summary"""
    return {
        "total_hours": 7.0,
        "billable_hours": 6.5,
        "non_billable_hours": 0.5,
        "entries_count": 2,
        "projects": ["HRMS Development"],
        "status_breakdown": {
            "draft": 1,
            "submitted": 1,
            "approved": 0
        }
    }
