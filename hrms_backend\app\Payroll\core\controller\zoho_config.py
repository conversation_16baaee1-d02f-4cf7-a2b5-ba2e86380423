from flask.views import <PERSON><PERSON>ie<PERSON>
from flask_smorest import Blueprint, abort
from schemas import ZohoConfigSchema, ZohoConfigUpdateSchema
from sqlalchemy.exc import SQLAlchemyError, IntegrityError
from core.services.zoho_config import ZohoConfigService
from core.utils.responseBuilder import ResponseBuilder
from core.middleware import roles_required
from core.services.zoho_api_service import ZohoAPIService

blueprint = Blueprint("zoho_config", __name__, description="Operations for Zoho configuration")

@blueprint.route("/zoho_config")
class ZohoConfig(MethodView):
    @roles_required(['admin'])
    @blueprint.arguments(ZohoConfigSchema)
    @blueprint.response(200, ZohoConfigSchema)
    def post(self, data):
        service = ZohoConfigService()
        client_id = data.get('client_id')
        client_secret = data.get('client_secret')
        code = data.get('authorization_employees_code')

        try:
            access_token, refresh_token, expires_in = ZohoAPIService()._get_tokens(client_id, client_secret, code)
        except Exception as e:
            abort(400, message=f"Error: {str(e)}")

        config_data = {
            'authorization_employees_code': code,
            'client_id': client_id,
            'client_secret': client_secret,
            'access_token': access_token,
            'refresh_token': refresh_token,
            'org_id': data.get('org_id'),
            'token_expires_at': expires_in,
            'is_enabled': data.get('is_enabled', False)
        }

        config_schema = ZohoConfigSchema()
        zoho_config = service.getConfig()

        try:
            if zoho_config is not None:
                updated_config = service.updateConfig(zoho_config.id, config_data)
                response_config = updated_config
                message = "Zoho configuration updated successfully"
            else:
                new_config = service.createConfig(config_data)
                response_config = new_config
                message = "Zoho configuration created successfully"

            return ResponseBuilder(
                data=config_schema.dump(response_config), 
                status_code=200, 
                total=None,
                meta=None, 
                message=message
            ).build()

        except IntegrityError:
            abort(409, message="Configuration already exists")
        except SQLAlchemyError:
            abort(500, message="Database error occurred")

    @roles_required(['admin'])
    @blueprint.response(200, ZohoConfigSchema)
    def get(self):
        service = ZohoConfigService()
        configuration = service.getConfig()
        config_details = ZohoConfigSchema().dump(configuration)
        return ResponseBuilder(
            data=config_details, 
            status_code=200, 
            total=None,meta=None, 
            message="Zoho Configuration retrived successfully"
        ).build()
    
    @roles_required(['admin'])
    def delete(self):
        service = ZohoConfigService()
        service.deleteConfig()
        return ResponseBuilder(
            data=None, 
            status_code=201, 
            total=None,meta=None, 
            message="Zoho Configuration deleted successfully"
        ).build()

    @roles_required(['admin'])
    @blueprint.arguments(ZohoConfigUpdateSchema)
    @blueprint.response(201, ZohoConfigSchema)
    def put(self, data):
        service = ZohoConfigService()
        get_config = service.getConfig()
        if get_config is None:
            abort(404, message="No configuration set for this user")
        try :
            new_config = service.updateConfig(get_config.id, data)
            config_schema = ZohoConfigUpdateSchema().dump(new_config)
            return ResponseBuilder(
                data=config_schema, 
                status_code=200,
                total=None,meta=None, 
                message="Zoho Configuration updated successfully"
            ).build()    
        except SQLAlchemyError as e:
                abort(500, message=f"Error while updating Loan Request, {str(e)}")
    