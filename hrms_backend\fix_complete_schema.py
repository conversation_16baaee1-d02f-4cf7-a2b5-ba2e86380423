#!/usr/bin/env python3
"""
Complete Database Schema Fix Script
Fixes all missing columns and schema issues in the HRMS database
"""

import os
import sys
import psycopg2
from psycopg2.extensions import ISOLATION_LEVEL_AUTOCOMMIT
from dotenv import load_dotenv

def get_db_connection():
    """Get database connection from environment variables"""
    try:
        conn = psycopg2.connect(
            host=os.getenv('DB_HOST', 'localhost'),
            port=os.getenv('DB_PORT', '5432'),
            database=os.getenv('DB_NAME', 'hrms_db'),
            user=os.getenv('DB_USER', 'postgres'),
            password=os.getenv('DB_PASSWORD', 'password')
        )
        conn.set_isolation_level(ISOLATION_LEVEL_AUTOCOMMIT)
        return conn
    except Exception as e:
        print(f"❌ Error connecting to database: {e}")
        return None

def check_column_exists(cursor, table_name, column_name):
    """Check if a column exists in a table"""
    cursor.execute("""
        SELECT EXISTS (
            SELECT 1 FROM information_schema.columns 
            WHERE table_name = %s AND column_name = %s
        )
    """, (table_name, column_name))
    return cursor.fetchone()[0]

def check_table_exists(cursor, table_name):
    """Check if a table exists"""
    cursor.execute("""
        SELECT EXISTS (
            SELECT 1 FROM information_schema.tables 
            WHERE table_name = %s
        )
    """, (table_name,))
    return cursor.fetchone()[0]

def fix_employees_table(cursor):
    """Fix employees table schema"""
    print("🔧 Fixing employees table schema...")
    
    if not check_table_exists(cursor, 'employees'):
        print("❌ Employees table does not exist!")
        return
    
    # Get existing columns
    cursor.execute("SELECT column_name FROM information_schema.columns WHERE table_name = 'employees'")
    existing_columns = [row[0] for row in cursor.fetchall()]
    print(f"📋 Found {len(existing_columns)} existing columns")
    
    # Required columns based on the Employee model
    required_columns = [
        ('notes', 'TEXT'),
        ('metadata_json', 'JSONB'),
        ('organization_id', 'UUID'),
        ('middle_name', 'VARCHAR(100)'),
        ('mobile_number', 'VARCHAR(20)'),
        ('alternate_phone', 'VARCHAR(20)'),
        ('date_of_birth', 'DATE'),
        ('gender', 'VARCHAR(20)'),
        ('marital_status', 'VARCHAR(20)'),
        ('nationality', 'VARCHAR(100)'),
        ('blood_group', 'VARCHAR(10)'),
        ('address_line1', 'VARCHAR(255)'),
        ('address_line2', 'VARCHAR(255)'),
        ('city', 'VARCHAR(100)'),
        ('state', 'VARCHAR(100)'),
        ('country', 'VARCHAR(100)'),
        ('postal_code', 'VARCHAR(20)'),
        ('emergency_contact_name', 'VARCHAR(200)'),
        ('emergency_contact_phone', 'VARCHAR(20)'),
        ('emergency_contact_relationship', 'VARCHAR(100)'),
        ('hire_date', 'DATE'),
        ('termination_date', 'DATE'),
        ('employment_type', 'VARCHAR(50)'),
        ('status', 'VARCHAR(50)'),
        ('department_id', 'UUID'),
        ('designation_id', 'UUID'),
        ('manager_id', 'UUID'),
        ('work_location', 'VARCHAR(255)'),
        ('timezone', 'VARCHAR(50)'),
        ('profile_picture_url', 'VARCHAR(500)'),
        ('bio', 'TEXT'),
        ('skills', 'JSONB'),
        ('user_id', 'UUID'),
        ('role', 'VARCHAR(50)')
    ]
    
    # Add missing columns
    added_count = 0
    for column_name, column_type in required_columns:
        if column_name not in existing_columns:
            try:
                # Set default values for required columns
                default_clause = ""
                if column_name == 'hire_date':
                    default_clause = " DEFAULT CURRENT_DATE"
                elif column_name == 'employment_type':
                    default_clause = " DEFAULT 'FULL_TIME'"
                elif column_name == 'status':
                    default_clause = " DEFAULT 'ACTIVE'"
                elif column_name == 'role':
                    default_clause = " DEFAULT 'employee'"
                elif column_name == 'timezone':
                    default_clause = " DEFAULT 'UTC'"
                
                cursor.execute(f"ALTER TABLE employees ADD COLUMN {column_name} {column_type}{default_clause}")
                print(f"✅ Added column: employees.{column_name}")
                added_count += 1
            except Exception as e:
                print(f"⚠️ Error adding column {column_name}: {e}")
        else:
            print(f"✅ Column exists: employees.{column_name}")
    
    print(f"📊 Added {added_count} new columns to employees table")

def fix_leave_requests_table(cursor):
    """Fix leave_requests table schema"""
    print("🔧 Fixing leave_requests table schema...")
    
    if not check_table_exists(cursor, 'leave_requests'):
        print("❌ Leave_requests table does not exist!")
        return
    
    # Get existing columns
    cursor.execute("SELECT column_name FROM information_schema.columns WHERE table_name = 'leave_requests'")
    existing_columns = [row[0] for row in cursor.fetchall()]
    print(f"📋 Found {len(existing_columns)} existing columns in leave_requests")
    
    # Required columns for leave_requests
    required_columns = [
        ('employee_id', 'UUID'),
        ('leave_policy_id', 'UUID'),
        ('start_date', 'DATE'),
        ('end_date', 'DATE'),
        ('total_days', 'DECIMAL(5,2)'),
        ('duration_type', 'VARCHAR(50)'),
        ('reason', 'TEXT'),
        ('status', 'VARCHAR(50)'),
        ('applied_at', 'TIMESTAMP'),
        ('approved_by', 'UUID'),
        ('approved_at', 'TIMESTAMP'),
        ('rejected_by', 'UUID'),
        ('rejected_at', 'TIMESTAMP'),
        ('comments', 'TEXT'),
        ('contact_number', 'VARCHAR(20)'),
        ('emergency_contact', 'VARCHAR(200)'),
        ('handover_notes', 'TEXT'),
        ('handover_to', 'UUID'),
        ('attachment_urls', 'JSONB')
    ]
    
    # Add missing columns
    added_count = 0
    for column_name, column_type in required_columns:
        if column_name not in existing_columns:
            try:
                default_clause = ""
                if column_name == 'status':
                    default_clause = " DEFAULT 'PENDING'"
                elif column_name == 'applied_at':
                    default_clause = " DEFAULT CURRENT_TIMESTAMP"
                elif column_name == 'duration_type':
                    default_clause = " DEFAULT 'full_day'"
                
                cursor.execute(f"ALTER TABLE leave_requests ADD COLUMN {column_name} {column_type}{default_clause}")
                print(f"✅ Added column: leave_requests.{column_name}")
                added_count += 1
            except Exception as e:
                print(f"⚠️ Error adding column {column_name}: {e}")
    
    print(f"📊 Added {added_count} new columns to leave_requests table")

def fix_leave_balances_table(cursor):
    """Fix leave_balances table schema"""
    print("🔧 Fixing leave_balances table schema...")
    
    if not check_table_exists(cursor, 'leave_balances'):
        print("❌ Leave_balances table does not exist!")
        return
    
    # Get existing columns
    cursor.execute("SELECT column_name FROM information_schema.columns WHERE table_name = 'leave_balances'")
    existing_columns = [row[0] for row in cursor.fetchall()]
    print(f"📋 Found {len(existing_columns)} existing columns in leave_balances")
    
    # Required columns for leave_balances
    required_columns = [
        ('employee_id', 'UUID'),
        ('leave_policy_id', 'UUID'),
        ('year', 'INTEGER'),
        ('opening_balance', 'DECIMAL(5,2)'),
        ('accrued_balance', 'DECIMAL(5,2)'),
        ('used_balance', 'DECIMAL(5,2)'),
        ('pending_balance', 'DECIMAL(5,2)'),
        ('carried_forward', 'DECIMAL(5,2)'),
        ('available_balance', 'DECIMAL(5,2)')
    ]
    
    # Add missing columns
    added_count = 0
    for column_name, column_type in required_columns:
        if column_name not in existing_columns:
            try:
                default_clause = ""
                if 'balance' in column_name:
                    default_clause = " DEFAULT 0.0"
                elif column_name == 'year':
                    default_clause = " DEFAULT EXTRACT(YEAR FROM CURRENT_DATE)"
                
                cursor.execute(f"ALTER TABLE leave_balances ADD COLUMN {column_name} {column_type}{default_clause}")
                print(f"✅ Added column: leave_balances.{column_name}")
                added_count += 1
            except Exception as e:
                print(f"⚠️ Error adding column {column_name}: {e}")
    
    print(f"📊 Added {added_count} new columns to leave_balances table")

def update_organization_ids(cursor):
    """Update organization_id for existing employees"""
    print("🔧 Updating organization IDs for existing employees...")
    
    try:
        # Get the first organization ID
        cursor.execute("SELECT id FROM organizations LIMIT 1")
        org_result = cursor.fetchone()
        
        if org_result:
            org_id = org_result[0]
            print(f"📋 Using organization ID: {org_id}")
            
            # Update employees without organization_id
            cursor.execute("UPDATE employees SET organization_id = %s WHERE organization_id IS NULL", (org_id,))
            updated_count = cursor.rowcount
            print(f"✅ Updated {updated_count} employees with organization_id")
        else:
            print("⚠️ No organizations found in database")
            
    except Exception as e:
        print(f"❌ Error updating organization IDs: {e}")

def create_sample_leave_balances(cursor):
    """Create sample leave balances for employees"""
    print("🔧 Creating sample leave balances...")
    
    try:
        # Get employees and leave policies
        cursor.execute("SELECT id FROM employees WHERE organization_id IS NOT NULL LIMIT 10")
        employees = cursor.fetchall()
        
        cursor.execute("SELECT id FROM leave_policies LIMIT 5")
        policies = cursor.fetchall()
        
        if not employees or not policies:
            print("⚠️ No employees or policies found for creating balances")
            return
        
        current_year = 2025
        created_count = 0
        
        for employee in employees:
            for policy in policies:
                # Check if balance already exists
                cursor.execute("""
                    SELECT COUNT(*) FROM leave_balances 
                    WHERE employee_id = %s AND leave_policy_id = %s AND year = %s
                """, (employee[0], policy[0], current_year))
                
                if cursor.fetchone()[0] == 0:
                    # Create new balance
                    cursor.execute("""
                        INSERT INTO leave_balances 
                        (id, created_at, updated_at, is_active, employee_id, leave_policy_id, year, 
                         opening_balance, accrued_balance, used_balance, pending_balance, 
                         carried_forward, available_balance)
                        VALUES (gen_random_uuid(), CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, true, 
                                %s, %s, %s, 20.0, 20.0, 0.0, 0.0, 0.0, 20.0)
                    """, (employee[0], policy[0], current_year))
                    created_count += 1
        
        print(f"✅ Created {created_count} leave balance records")
        
    except Exception as e:
        print(f"❌ Error creating leave balances: {e}")

def main():
    """Main function to fix database schema"""
    print("🚀 Starting complete database schema fix...")
    
    # Load environment variables
    load_dotenv()
    
    # Connect to database
    conn = get_db_connection()
    if not conn:
        print("❌ Failed to connect to database")
        return
    
    try:
        cursor = conn.cursor()
        
        # Fix all schemas
        fix_employees_table(cursor)
        fix_leave_requests_table(cursor)
        fix_leave_balances_table(cursor)
        update_organization_ids(cursor)
        create_sample_leave_balances(cursor)
        
        print("✅ Complete database schema fix completed successfully!")
        print("🎉 Leave management system should now work properly!")
        
    except Exception as e:
        print(f"❌ Error during schema fix: {e}")
    finally:
        conn.close()

if __name__ == "__main__":
    main()
