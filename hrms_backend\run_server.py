import os
import sys
import subprocess

# Change to the backend directory
os.chdir(r"D:\AGHR\hrms_backend")

# Run the uvicorn command
cmd = [
    sys.executable, "-m", "uvicorn", 
    "app.main:app", 
    "--host", "0.0.0.0", 
    "--port", "8085", 
    "--reload"
]

print("🚀 Starting HRMS Backend Server...")
print("📍 Command:", " ".join(cmd))
print("📂 Working Directory:", os.getcwd())
print("=" * 50)

subprocess.run(cmd)
