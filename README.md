# 🏢 HRMS - Human Resource Management System

A comprehensive, modern HR management system with unified frontend and backend deployment.

## ✨ Features

### 👥 **Employee Management**
- Employee directory and profiles
- **Quick Onboarding** - Create employees with just name and email
- Advanced onboarding workflows
- Role-based access control

### ⏰ **Time & Attendance**
- Time tracking and timesheets
- Attendance monitoring
- Shift management
- Timesheet approvals

### 🏖️ **Leave Management**
- Leave requests and approvals
- Leave balance tracking
- Holiday management
- Leave policies

### 💰 **Payroll & Benefits**
- Payroll processing
- Salary management
- Benefits administration
- Tax calculations

### 📊 **Project Management**
- Project tracking
- Task management
- Team collaboration
- Progress monitoring

### 🎫 **Ticket Management**
- IT support tickets
- HR requests
- Issue tracking
- SLA management

### 📈 **Performance & Analytics**
- Performance reviews
- Employee engagement
- Advanced reporting
- Analytics dashboard

## 🚀 Quick Start

### Prerequisites
- **Python 3.8+** - [Download Python](https://python.org)
- **Node.js 16+** - [Download Node.js](https://nodejs.org)
- **PostgreSQL** - [Download PostgreSQL](https://postgresql.org)

### 🖱️ One-Click Start

#### Windows
```bash
# Double-click or run in Command Prompt
start_hrms.bat
```

#### Linux/Mac
```bash
# Make executable and run
chmod +x start_hrms.sh
./start_hrms.sh
```

#### Python (Cross-platform)
```bash
# Install dependencies and run
python run_hrms.py --install

# Or just run (if dependencies already installed)
python run_hrms.py
```

### 🌐 Access the Application

Once started, the application will automatically open in your browser:

- **Frontend UI**: http://localhost:5173
- **Backend API**: http://localhost:8085
- **API Documentation**: http://localhost:8085/docs

## 🔐 Default Login Credentials

| Role | Email | Password |
|------|-------|----------|
| **Admin** | <EMAIL> | password123 |
| **HR** | <EMAIL> | password123 |
| **Manager** | <EMAIL> | password123 |
| **Employee** | <EMAIL> | password123 |

## 💡 Quick Onboarding Feature

The standout feature that makes employee onboarding effortless:

1. **Login** as Admin or HR
2. **Navigate** to Onboarding page
3. **Click "Quick Onboard"** (green button)
4. **Enter** just the employee name and email
5. **Click "Start Onboarding"**

The system automatically:
- ✅ Creates employee record
- ✅ Generates login credentials
- ✅ Sends welcome email
- ✅ Starts onboarding workflow
- ✅ Notifies HR team

## 📁 Project Structure

```
HRMS/
├── hrms_backend/          # FastAPI backend
│   ├── app/
│   │   ├── api/           # API endpoints
│   │   ├── core/          # Security, RBAC
│   │   ├── db/            # Database models
│   │   ├── services/      # Business logic
│   │   └── main.py        # FastAPI app
│   └── requirements.txt
├── hrm_frontend/          # React frontend
│   ├── src/
│   │   ├── components/    # React components
│   │   ├── pages/         # Page components
│   │   ├── services/      # API services
│   │   └── App.jsx        # Main app
│   └── package.json
├── run_hrms.py           # Unified launcher
├── start_hrms.bat        # Windows startup
├── start_hrms.sh         # Linux/Mac startup
└── README.md
```

## ⚙️ Configuration

### Database Setup
1. Install PostgreSQL
2. Create database: `hrms_db`
3. Update connection in `hrms_backend/.env`

### Email Configuration (Optional)
Add to `hrms_backend/.env`:
```env
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your-app-password
SMTP_SERVER=smtp.gmail.com
SMTP_PORT=587
```

## 🛠️ Development

### Backend Development
```bash
cd hrms_backend
pip install -r requirements.txt
uvicorn app.main:app --reload --port 8085
```

### Frontend Development
```bash
cd hrm_frontend
npm install
npm run dev
```

### Database Migrations
```bash
cd hrms_backend
python create_test_employees.py
python create_default_onboarding_template.py
```

## 🧪 Testing

### Test Quick Onboarding
```bash
cd hrms_backend
python test_simple_onboard.py
python test_frontend_integration.py
```

### Test Email Service
```bash
cd hrms_backend
python test_onboarding_simple.py
```

## 📚 API Documentation

Visit http://localhost:8085/docs for interactive API documentation with:
- All endpoints
- Request/response schemas
- Authentication examples
- Try-it-out functionality

## 🔒 Security Features

- **JWT Authentication**
- **Role-Based Access Control (RBAC)**
- **Password Hashing**
- **CORS Protection**
- **Input Validation**
- **SQL Injection Prevention**

## 🎨 UI Features

- **Modern React Interface**
- **Responsive Design**
- **Dark/Light Theme Support**
- **Real-time Updates**
- **Interactive Dashboards**
- **Mobile-Friendly**

## 🚀 Deployment

### Production Deployment
1. Build frontend: `npm run build`
2. Configure environment variables
3. Set up reverse proxy (Nginx)
4. Use production WSGI server (Gunicorn)
5. Set up SSL certificates

### Docker Deployment
```bash
# Build and run with Docker Compose
docker-compose up --build
```

## 🤝 Contributing

1. Fork the repository
2. Create feature branch
3. Make changes
4. Add tests
5. Submit pull request

## 📄 License

This project is licensed under the MIT License.

## 🆘 Support

For support and questions:
- Check the API documentation at `/docs`
- Review the test files for examples
- Open an issue on GitHub

## 🎉 Success Stories

> "The Quick Onboarding feature reduced our employee setup time from 2 hours to 2 minutes!" - HR Manager

> "Having both frontend and backend in one application made deployment so much easier." - IT Administrator

---

**Made with ❤️ for modern HR teams**
