
from typing import Optional
from core.models.payroll_history import PayrollHistoryModel
from core.repositories.loan_repayment import LoanRepaymentRepository


class LoanService:
    def __init__(self, repository=None):
        self.repository = repository or LoanRepaymentRepository()
    
    def estimate_loan_repayment(self, employee_id: int, net_pay: float) -> float:
        """Public interface for estimation"""
        try:
            return self.repository.estimate_loan_repayment(
                employee_id=employee_id,
                net_pay=net_pay
            )
        except Exception as e:
            print(f"Estimation failed for employee {employee_id}: {str(e)}")
            return 0.0

    def apply_loan_deductions_to_employee_payroll(
        self, 
        employee_id: int, 
        payroll_data: dict
     ) -> dict:
        """Apply deductions to either new or existing payroll"""
        print("payroll data:", payroll_data)
        try:
            if 'id' in payroll_data:
                print(payroll_data['id'])
                # Existing payroll record
                result = self.repository.apply_loan_deductions_to_payroll(
                    employee_id=employee_id,
                    payroll_id=payroll_data['id']
                )
                if result:
                    return {
                        'netpay': result.netpay,
                        'monthly_loan_repayment': result.monthly_loan_repayment
                    }
                return payroll_data
            else:
                # New payroll record
                return self.repository.process_loan_deductions(
                    employee_id=employee_id,
                    payroll_data=payroll_data
                )
        except Exception as e:
            print(f"Deduction application failed for employee {employee_id}: {str(e)}")
            return payroll_data
        