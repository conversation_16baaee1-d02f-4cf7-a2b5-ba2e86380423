import React from 'react';
import { useAuth } from '../contexts/AuthContext';
import { usePermissions } from '../hooks/usePermissions';
import { PermissionGate, ConditionalRender } from './ProtectedRoute';
import { CompactTimeTracker } from './TimeTracker';
import { LogOut, User, Bell, Settings } from 'lucide-react';

export default function Header({ activeTab, setActiveTab, activeView }) {
  const { user, logout, userRole } = useAuth();
  const permissions = usePermissions();

  // Define tab configurations with permission requirements
  const tabConfigurations = {
    dashboard: [
      { id: 'overview', label: 'Overview', permission: 'dashboard' },
      { id: 'analytics', label: 'Analytics', permission: 'dashboard' },
      { id: 'rbac-demo', label: 'RBAC Demo', permission: 'dashboard' }
    ],
    employees: [
      { id: 'directory', label: 'Directory', permission: 'employeeDirectory' },
      { id: 'profiles', label: 'Profiles', permission: 'profileManagement' },
      { id: 'onboarding', label: 'Onboarding', permission: 'employeeDirectory' }
    ],
    attendance: [
      { id: 'my-attendance', label: 'My Attendance', permission: 'attendanceTrackerSelf' },
      { id: 'team-attendance', label: 'Team Attendance', permission: 'attendanceManagement' },
      { id: 'reports', label: 'Reports', permission: 'attendanceManagement' }
    ],
    leave: [
      { id: 'my-leaves', label: 'My Leaves', permission: 'leaveRequestsSelf' },
      { id: 'team-leaves', label: 'Team Leaves', permission: 'leaveManagement' },
      { id: 'calendar', label: 'Calendar', permission: 'leaveRequestsSelf' }
    ],
    payroll: [
      { id: 'my-payslips', label: 'My Payslips', permission: 'payrollPayslipsSelf' },
      { id: 'payroll-management', label: 'Payroll Management', permission: 'payrollManagement' },
      { id: 'reports', label: 'Reports', permission: 'payrollManagement' }
    ],
    projects: [
      { id: 'kanban', label: 'Kanban Boards', permission: 'projectKanbanBoards' },
      { id: 'timeline', label: 'Timeline', permission: 'projectKanbanBoards' },
      { id: 'reports', label: 'Reports', permission: 'projectKanbanBoards' }
    ],
    tasks: [
      { id: 'my-tasks', label: 'My Tasks', permission: 'taskManagement' },
      { id: 'team-tasks', label: 'Team Tasks', permission: 'taskManagement' },
      { id: 'calendar', label: 'Calendar', permission: 'taskManagement' }
    ],
    tickets: [
      { id: 'my-tickets', label: 'My Tickets', permission: 'ticketingSystemSelf' },
      { id: 'team-tickets', label: 'Team Tickets', permission: 'ticketManagement' },
      { id: 'reports', label: 'Reports', permission: 'ticketManagement' }
    ],
    performance: [
      { id: 'my-reviews', label: 'My Reviews', permission: 'performanceReviewsSelf' },
      { id: 'team-reviews', label: 'Team Reviews', permission: 'performanceReviewsTeam' },
      { id: 'goals', label: 'Goals', permission: 'performanceReviewsSelf' }
    ]
  };

  const currentTabs = tabConfigurations[activeView] || [];

  const handleLogout = () => {
    logout();
  };

  return (
    <header className="bg-white border-b border-gray-200 shadow-agno">
      <div className="flex items-center justify-between px-4 py-2">
        {/* Left side - Navigation tabs */}
        <div className="flex items-center space-x-6 text-sm font-medium">
          {/* Context-based navigation */}
          <ConditionalRender
            permission="employeeDirectory"
            fullAccess={<div className="text-gray-600 mr-6">Organization</div>}
            teamAccess={<div className="text-gray-600 mr-6">Team</div>}
            noAccess={<div className="text-gray-600 mr-6">My Space</div>}
          />

          {/* Dynamic tabs based on current view */}
          {currentTabs.map((tab) => (
            <PermissionGate key={tab.id} permission={tab.permission} hideOnDenied={true}>
              <button
                className={`px-3 py-1 rounded transition-colors ${
                  activeTab === tab.id
                    ? 'bg-accent-100 agno-text-orange'
                    : 'text-gray-600 hover:text-agno-primary hover:bg-gray-100'
                }`}
                onClick={() => setActiveTab && setActiveTab(tab.id)}
              >
                {tab.label}
              </button>
            </PermissionGate>
          ))}
        </div>

        {/* Right side - User menu */}
        <div className="flex items-center space-x-4">
          {/* Time Tracker */}
          <CompactTimeTracker className="hidden lg:flex" />

          {/* Notifications */}
          <button className="text-gray-500 hover:text-gray-700 relative">
            <Bell size={18} />
            <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-4 w-4 flex items-center justify-center">
              3
            </span>
          </button>

          {/* Settings - Only for admin roles */}
          <PermissionGate permission="systemSettings" hideOnDenied={true}>
            <button className="text-gray-500 hover:text-gray-700">
              <Settings size={18} />
            </button>
          </PermissionGate>

          {/* User menu */}
          <div className="flex items-center space-x-2">
            <div className="w-8 h-8 bg-accent-100 rounded-full flex items-center justify-center">
              <User size={16} className="agno-text-orange" />
            </div>
            <div className="hidden md:block">
              <div className="text-sm font-medium text-gray-900">{user?.name}</div>
              <div className="text-xs text-gray-500 capitalize">{userRole?.replace('_', ' ')}</div>
            </div>
            <button
              onClick={handleLogout}
              className="text-gray-500 hover:text-gray-700 ml-2"
              title="Logout"
            >
              <LogOut size={16} />
            </button>
          </div>
        </div>
      </div>
    </header>
  );
}
