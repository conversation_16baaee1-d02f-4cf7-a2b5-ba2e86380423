#!/usr/bin/env python3
"""
Test the employees endpoint specifically to debug the 500 error
"""

import requests
import json

BASE_URL = "http://localhost:8085"

def test_employees_endpoint():
    """Test the employees endpoint with proper authentication"""
    try:
        # First login to get token
        credentials = {
            "email": "<EMAIL>",
            "password": "password123"
        }
        
        login_response = requests.post(f"{BASE_URL}/api/auth/login", json=credentials)
        print(f"Login status: {login_response.status_code}")
        
        if login_response.status_code != 200:
            print(f"Login failed: {login_response.text}")
            return
            
        login_data = login_response.json()
        token = login_data.get('access_token')
        
        if not token:
            print("No access token received")
            return
            
        print("✅ Login successful, testing employees endpoint...")
        
        # Test employees endpoint
        headers = {"Authorization": f"Bearer {token}"}
        
        # Try different endpoints
        endpoints_to_test = [
            "/api/employees/",
            "/api/employees",
        ]
        
        for endpoint in endpoints_to_test:
            print(f"\nTesting {endpoint}...")
            try:
                response = requests.get(f"{BASE_URL}{endpoint}", headers=headers)
                print(f"Status: {response.status_code}")
                print(f"Response: {response.text[:500]}...")
                
                if response.status_code == 200:
                    data = response.json()
                    print(f"Success! Got {len(data.get('employees', []))} employees")
                    break
                    
            except Exception as e:
                print(f"Error calling {endpoint}: {e}")
        
    except Exception as e:
        print(f"Test failed: {e}")

if __name__ == "__main__":
    test_employees_endpoint()
