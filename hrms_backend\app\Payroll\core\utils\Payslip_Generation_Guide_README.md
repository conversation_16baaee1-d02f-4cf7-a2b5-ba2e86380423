# Payslip PDF Generation Guide
This document explains how the Payslip PDF Generation feature was implemented in our Flask codebase. It provides a step-by-step guide on how the system generates, stores, and sends PDF payslips using PDFMonkey and SMTP for email delivery.

## Overview
##### The payslip generation system in our Flask application:

1. Fetches employee salary data.
2. Sends this data to PDFMonkey to generate a PDF.
3. Polls for PDF completion and retrieves the download link.
4. Sends the generated PDF via email using SMTP.

### Setup Instructions

1️. Install Required Dependencies
Ensure you have the necessary Python packages installed:

~~~py
    pip install requests
~~~

### login the PDF monkey and get the credentails
go to the pdf monkey website: https://dashboard.pdfmonkey.io/login
Note::: you must have created an account with PDF monkey and have also created a template to get the template ID.
here is the documentation guid on how to use PDFMonkey https://docs.pdfmonkey.io/guides/getting-started

*login*
EMAIL: <EMAIL>
PASSWORD: Alexwhite96

go to templates and copy the template ID or create a new one if needed:
TEMPLATE_ID: D6C2C5DD-A7BD-48E9-9E71-51697BEE7E80

then go to my account information to get the API KEY
PDFMONKEY_API_KEY: XeEj-DcgB6dwD9tk8S1N336uEHLwQtGn

lastly: Also if email isnt already configured you have to also settup the email server dependig on the SMTP Server provider, for this project we are currently using Webmail.

### Implementation Details

🔹 Step 1: Generate Payslip PDF
We use PDFMonkey API to generate the PDF. The function "generate_payslip_pdf()" does the following:

~~~py
def generate_payslip_pdf(employee_payslip):
~~~
file location: utils.send_payslip.py

Sends employee data as a payload to PDFMonkey.
Polls the API until the PDF is ready.
Returns the download URL of the generated PDF.

🔹 Step 2: Send Payslip via Email
Once the PDF is generated, we email it to the employee using SMTP. The function "send_payslip()" does the following:

~~~py
def send_payslip(employee_email, first_name, last_name, month, pdf_url):
~~~
file location: utils.send_payslip.py

Downloads the PDF file.
Composes an email with the payslip attached.
Sends it via SMTP.

### How to Use the Payslip Generation Function
You can call these functions inside your Flask route:

example below:
```py

@blueprint.route("/employee/payslip")
def post(self, data):
        """Endpoint to send payslip via email with PDFMonkey."""

        payroll_ids = data.get("payroll_ids", [])
```
file location: controller.employee_payslip.py


### Summary

*generate_payslip_pdf(employee_payslip): Calls PDFMonkey API to generate a payslip PDF.*
*send_payslip(employee_email, first_name, last_name, month, pdf_url): Sends the generated PDF via email.*
*Flask API endpoint /generate_payslip: Handles the payslip generation and sending process.*
