#!/usr/bin/env python3
"""
Check enum values in the database
"""

import sys
import os

# Add the app directory to Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'app'))

from sqlalchemy import text
from app.db.session import engine

def check_enums():
    """Check enum values in the database"""
    try:
        with engine.connect() as conn:
            # Check enum types
            result = conn.execute(text("""
                SELECT t.typname, e.enumlabel 
                FROM pg_type t 
                JOIN pg_enum e ON t.oid = e.enumtypid 
                ORDER BY t.typname, e.enumsortorder
            """))
            
            enums = {}
            for row in result.fetchall():
                if row[0] not in enums:
                    enums[row[0]] = []
                enums[row[0]].append(row[1])
            
            print("=== DATABASE ENUM VALUES ===")
            for enum_name, values in enums.items():
                print(f"{enum_name}: {values}")
                
    except Exception as e:
        print(f"Error checking enums: {e}")

if __name__ == "__main__":
    check_enums()
