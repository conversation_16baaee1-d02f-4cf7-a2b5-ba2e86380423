from typing import Optional, List, Dict, Any, Tuple
from sqlalchemy.orm import Session, joinedload
from sqlalchemy import and_, or_, func, desc, asc
from fastapi import HTTPException, status
from uuid import UUID
from datetime import datetime, timedelta
import logging
import json

from ...db.models.ticket import (
    Ticket, TicketComment, TicketActivity, TicketEscalation, TicketWorkflow,
    TicketNotification, TicketTemplate, TicketSLA, TicketStatus, TicketPriority,
    TicketType, WorkflowStatus, EscalationLevel
)
from ...db.models.employee import Employee
from ...core.security import CurrentUser
from ...core.audit_logger import AuditLogger

logger = logging.getLogger(__name__)


class CaseManagementService:
    """Enhanced case management service with workflow automation"""

    async def create_ticket_from_template(
        self,
        db: Session,
        template_id: UUID,
        form_data: Dict[str, Any],
        current_user: CurrentUser
    ) -> Ticket:
        """Create a ticket from a template"""
        try:
            # Get template
            template = db.query(TicketTemplate).filter(
                TicketTemplate.id == template_id,
                TicketTemplate.is_active == True
            ).first()

            if not template:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Ticket template not found"
                )

            # Generate ticket number
            ticket_number = await self._generate_ticket_number(db)

            # Process template content with form data
            title = self._process_template_content(template.title_template, form_data)
            description = self._process_template_content(template.description_template, form_data)

            # Create ticket
            ticket = Ticket(
                ticket_number=ticket_number,
                title=title,
                description=description,
                ticket_type=template.ticket_type,
                category=template.category,
                subcategory=template.subcategory,
                priority=template.priority,
                requester_id=current_user.user_id,
                assigned_to=template.default_assignee_id,
                assigned_team=template.default_team,
                organization_id=current_user.organization_id,
                response_sla_hours=template.response_sla_hours,
                resolution_sla_hours=template.resolution_sla_hours,
                workflow_id=template.workflow_id,
                status=TicketStatus.OPEN
            )

            db.add(ticket)
            db.commit()
            db.refresh(ticket)

            # Update template usage count
            template.usage_count += 1
            db.commit()

            # Start workflow if assigned
            if template.workflow_id:
                await self._start_workflow(db, ticket, current_user)

            # Apply SLA rules
            await self._apply_sla_rules(db, ticket)

            # Send notifications
            await self._send_ticket_notifications(db, ticket, "created", current_user)

            # Log activity
            await self._log_ticket_activity(
                db, ticket.id, current_user.user_id, "created",
                f"Ticket created from template: {template.name}"
            )

            # Log audit event
            AuditLogger.log_action(
                db=db,
                action="CREATE",
                resource_type="tickets",
                resource_id=str(ticket.id),
                user=current_user,
                new_values={
                    "ticket_number": ticket_number,
                    "template_id": str(template_id),
                    "form_data": form_data
                }
            )

            return ticket

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error creating ticket from template: {e}")
            db.rollback()
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to create ticket from template"
            )

    async def auto_assign_ticket(
        self,
        db: Session,
        ticket_id: UUID,
        current_user: CurrentUser
    ) -> Ticket:
        """Auto-assign ticket based on rules"""
        try:
            ticket = db.query(Ticket).filter(Ticket.id == ticket_id).first()
            if not ticket:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Ticket not found"
                )

            # Get assignment rules based on ticket properties
            assignment_rules = await self._get_assignment_rules(db, ticket)
            
            if not assignment_rules:
                return ticket

            # Apply the first matching rule
            for rule in assignment_rules:
                if await self._evaluate_assignment_rule(ticket, rule):
                    # Assign ticket
                    old_assignee = ticket.assigned_to
                    ticket.assigned_to = rule.get("assignee_id")
                    ticket.assigned_team = rule.get("team")
                    ticket.auto_assigned = True
                    ticket.assignment_rules_applied = [rule]

                    db.commit()
                    db.refresh(ticket)

                    # Log activity
                    await self._log_ticket_activity(
                        db, ticket.id, None, "auto_assigned",
                        f"Auto-assigned to {rule.get('assignee_name', 'team')}"
                    )

                    # Send notification to new assignee
                    if ticket.assigned_to:
                        await self._send_assignment_notification(db, ticket, current_user)

                    break

            return ticket

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error auto-assigning ticket: {e}")
            db.rollback()
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to auto-assign ticket"
            )

    async def escalate_ticket(
        self,
        db: Session,
        ticket_id: UUID,
        escalation_level: EscalationLevel,
        reason: str,
        current_user: CurrentUser
    ) -> TicketEscalation:
        """Escalate a ticket"""
        try:
            ticket = db.query(Ticket).filter(Ticket.id == ticket_id).first()
            if not ticket:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Ticket not found"
                )

            # Create escalation record
            escalation = TicketEscalation(
                ticket_id=ticket_id,
                escalated_by=current_user.user_id,
                escalation_level=escalation_level,
                reason=reason,
                escalated_at=datetime.utcnow()
            )

            db.add(escalation)

            # Update ticket priority if needed
            if escalation_level in [EscalationLevel.LEVEL_3, EscalationLevel.LEVEL_4]:
                if ticket.priority not in [TicketPriority.HIGH, TicketPriority.URGENT, TicketPriority.CRITICAL]:
                    ticket.priority = TicketPriority.HIGH

            # Find escalation assignee based on level
            escalation_assignee = await self._get_escalation_assignee(db, ticket, escalation_level)
            if escalation_assignee:
                ticket.assigned_to = escalation_assignee.id

            db.commit()
            db.refresh(escalation)

            # Log activity
            await self._log_ticket_activity(
                db, ticket.id, current_user.user_id, "escalated",
                f"Escalated to {escalation_level.value}: {reason}"
            )

            # Send escalation notifications
            await self._send_escalation_notifications(db, ticket, escalation, current_user)

            return escalation

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error escalating ticket: {e}")
            db.rollback()
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to escalate ticket"
            )

    async def process_workflow_step(
        self,
        db: Session,
        ticket_id: UUID,
        step_data: Dict[str, Any],
        current_user: CurrentUser
    ) -> Ticket:
        """Process a workflow step"""
        try:
            ticket = db.query(Ticket).options(
                joinedload(Ticket.workflow)
            ).filter(Ticket.id == ticket_id).first()

            if not ticket or not ticket.workflow:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Ticket or workflow not found"
                )

            workflow_steps = ticket.workflow.workflow_steps
            current_step = ticket.workflow_step or 0

            if current_step >= len(workflow_steps):
                # Workflow completed
                ticket.workflow_status = WorkflowStatus.COMPLETED
                db.commit()
                return ticket

            # Get current step configuration
            step_config = workflow_steps[current_step]
            
            # Process step based on type
            step_result = await self._process_workflow_step_type(
                db, ticket, step_config, step_data, current_user
            )

            if step_result.get("success", False):
                # Move to next step
                ticket.workflow_step = current_step + 1
                
                # Check if workflow is complete
                if ticket.workflow_step >= len(workflow_steps):
                    ticket.workflow_status = WorkflowStatus.COMPLETED
                    
                    # Auto-close ticket if configured
                    if step_config.get("auto_close_on_completion", False):
                        ticket.status = TicketStatus.RESOLVED
                        ticket.resolved_at = datetime.utcnow()

            else:
                # Step failed
                ticket.workflow_status = WorkflowStatus.FAILED

            db.commit()
            db.refresh(ticket)

            # Log workflow activity
            await self._log_ticket_activity(
                db, ticket.id, current_user.user_id, "workflow_step",
                f"Processed workflow step {current_step + 1}: {step_config.get('name', 'Unknown')}"
            )

            return ticket

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error processing workflow step: {e}")
            db.rollback()
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to process workflow step"
            )

    async def _generate_ticket_number(self, db: Session) -> str:
        """Generate unique ticket number"""
        # Get current year and month
        now = datetime.utcnow()
        prefix = f"TKT-{now.year}{now.month:02d}"
        
        # Get the highest ticket number for this month
        latest_ticket = db.query(Ticket).filter(
            Ticket.ticket_number.like(f"{prefix}%")
        ).order_by(desc(Ticket.ticket_number)).first()
        
        if latest_ticket:
            # Extract sequence number and increment
            sequence = int(latest_ticket.ticket_number.split("-")[-1]) + 1
        else:
            sequence = 1
        
        return f"{prefix}-{sequence:04d}"

    def _process_template_content(self, template: str, form_data: Dict[str, Any]) -> str:
        """Process template content with form data"""
        try:
            # Simple template processing - replace {{field_name}} with form data
            processed = template
            for key, value in form_data.items():
                placeholder = f"{{{{{key}}}}}"
                processed = processed.replace(placeholder, str(value))
            return processed
        except Exception as e:
            logger.error(f"Error processing template content: {e}")
            return template

    async def _start_workflow(self, db: Session, ticket: Ticket, current_user: CurrentUser):
        """Start workflow for a ticket"""
        try:
            if ticket.workflow_id:
                ticket.workflow_status = WorkflowStatus.ACTIVE
                ticket.workflow_step = 0
                db.commit()
        except Exception as e:
            logger.error(f"Error starting workflow: {e}")

    async def _apply_sla_rules(self, db: Session, ticket: Ticket):
        """Apply SLA rules to a ticket"""
        try:
            # Get applicable SLA rules
            sla_rules = db.query(TicketSLA).filter(
                TicketSLA.is_active == True,
                TicketSLA.organization_id == ticket.organization_id
            ).order_by(desc(TicketSLA.priority)).all()

            for sla in sla_rules:
                if await self._evaluate_sla_conditions(ticket, sla.conditions):
                    # Apply SLA
                    ticket.response_sla_hours = sla.response_time_hours
                    ticket.resolution_sla_hours = sla.resolution_time_hours
                    
                    # Calculate due date
                    if sla.business_hours_only:
                        ticket.due_date = await self._calculate_business_hours_due_date(
                            datetime.utcnow(), sla.resolution_time_hours, sla.business_hours
                        )
                    else:
                        ticket.due_date = datetime.utcnow() + timedelta(hours=sla.resolution_time_hours)
                    
                    db.commit()
                    break

        except Exception as e:
            logger.error(f"Error applying SLA rules: {e}")

    async def _send_ticket_notifications(
        self, db: Session, ticket: Ticket, event_type: str, current_user: CurrentUser
    ):
        """Send ticket notifications"""
        try:
            # Implementation for sending notifications
            # This would integrate with email/SMS services
            pass
        except Exception as e:
            logger.error(f"Error sending ticket notifications: {e}")

    async def _log_ticket_activity(
        self, db: Session, ticket_id: UUID, user_id: Optional[UUID], 
        activity_type: str, description: str
    ):
        """Log ticket activity"""
        try:
            activity = TicketActivity(
                ticket_id=ticket_id,
                user_id=user_id,
                activity_type=activity_type,
                description=description,
                is_system_activity=(user_id is None)
            )
            db.add(activity)
            db.commit()
        except Exception as e:
            logger.error(f"Error logging ticket activity: {e}")

    async def _get_assignment_rules(self, db: Session, ticket: Ticket) -> List[Dict[str, Any]]:
        """Get assignment rules for a ticket"""
        # This would be implemented based on your assignment rule configuration
        return []

    async def _evaluate_assignment_rule(self, ticket: Ticket, rule: Dict[str, Any]) -> bool:
        """Evaluate if an assignment rule applies to a ticket"""
        # Implementation for rule evaluation
        return False

    async def _get_escalation_assignee(self, db: Session, ticket: Ticket, level: EscalationLevel) -> Optional[Employee]:
        """Get escalation assignee based on level"""
        # Implementation for finding escalation assignee
        return None

    async def _send_assignment_notification(self, db: Session, ticket: Ticket, current_user: CurrentUser):
        """Send assignment notification"""
        pass

    async def _send_escalation_notifications(
        self, db: Session, ticket: Ticket, escalation: TicketEscalation, current_user: CurrentUser
    ):
        """Send escalation notifications"""
        pass

    async def _process_workflow_step_type(
        self, db: Session, ticket: Ticket, step_config: Dict[str, Any], 
        step_data: Dict[str, Any], current_user: CurrentUser
    ) -> Dict[str, Any]:
        """Process specific workflow step type"""
        # Implementation for different workflow step types
        return {"success": True}

    async def _evaluate_sla_conditions(self, ticket: Ticket, conditions: Dict[str, Any]) -> bool:
        """Evaluate SLA conditions"""
        # Implementation for SLA condition evaluation
        return True

    async def _calculate_business_hours_due_date(
        self, start_time: datetime, hours: int, business_hours: Dict[str, Any]
    ) -> datetime:
        """Calculate due date considering business hours"""
        # Implementation for business hours calculation
        return start_time + timedelta(hours=hours)
