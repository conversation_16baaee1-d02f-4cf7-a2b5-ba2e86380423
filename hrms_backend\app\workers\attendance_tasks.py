from celery import Celery
from sqlalchemy.orm import Session
from datetime import datetime, date, timedelta
import logging

from ..core.celery_app import celery_app
from ..db.session import SessionLocal
from ..services.attendance_service import AttendanceService
from ..db.models.employee import Employee
from ..db.models.attendance import AttendanceRecord

logger = logging.getLogger(__name__)


@celery_app.task(bind=True)
def auto_checkout_employees(self):
    """Auto checkout employees at end of day"""
    db = SessionLocal()
    attendance_service = AttendanceService()
    
    try:
        # Get all organizations
        organizations = db.query(Employee.organization_id).distinct().all()
        
        total_checked_out = 0
        for org_tuple in organizations:
            org_id = org_tuple[0]
            try:
                await attendance_service.auto_checkout_employees(db, org_id)
                total_checked_out += 1
            except Exception as e:
                logger.error(f"Error auto-checking out for organization {org_id}: {e}")
        
        logger.info(f"Auto checkout completed for {total_checked_out} organizations")
        return {"status": "completed", "organizations_processed": total_checked_out}
        
    except Exception as e:
        logger.error(f"Error in auto checkout task: {e}")
        raise self.retry(exc=e, countdown=60, max_retries=3)
    finally:
        db.close()


@celery_app.task(bind=True)
def calculate_daily_attendance_summary(self, organization_id: str, target_date: str = None):
    """Calculate daily attendance summary for organization"""
    db = SessionLocal()
    
    try:
        if target_date:
            calc_date = datetime.strptime(target_date, "%Y-%m-%d").date()
        else:
            calc_date = date.today() - timedelta(days=1)  # Previous day
        
        # Get all attendance records for the date
        records = db.query(AttendanceRecord).join(Employee).filter(
            Employee.organization_id == organization_id,
            AttendanceRecord.date == calc_date
        ).all()
        
        # Calculate summary statistics
        total_employees = db.query(Employee).filter(
            Employee.organization_id == organization_id,
            Employee.is_active == True
        ).count()
        
        present_count = len([r for r in records if r.status == "present"])
        absent_count = total_employees - present_count
        late_count = len([r for r in records if r.status == "late"])
        
        summary = {
            "date": calc_date.isoformat(),
            "organization_id": organization_id,
            "total_employees": total_employees,
            "present": present_count,
            "absent": absent_count,
            "late": late_count,
            "attendance_rate": (present_count / total_employees * 100) if total_employees > 0 else 0
        }
        
        logger.info(f"Attendance summary calculated for {organization_id} on {calc_date}")
        return summary
        
    except Exception as e:
        logger.error(f"Error calculating attendance summary: {e}")
        raise self.retry(exc=e, countdown=60, max_retries=3)
    finally:
        db.close()


@celery_app.task(bind=True)
def send_attendance_reminders(self):
    """Send attendance reminders to employees who haven't checked in"""
    db = SessionLocal()
    
    try:
        from ..core.websocket_manager import notification_manager
        
        today = date.today()
        current_time = datetime.now().time()
        
        # Only send reminders after 10 AM
        if current_time.hour < 10:
            return {"status": "skipped", "reason": "Too early for reminders"}
        
        # Find employees who haven't checked in today
        employees_without_checkin = db.query(Employee).outerjoin(
            AttendanceRecord,
            (Employee.id == AttendanceRecord.employee_id) & 
            (AttendanceRecord.date == today)
        ).filter(
            Employee.is_active == True,
            AttendanceRecord.id.is_(None)  # No attendance record for today
        ).all()
        
        reminder_count = 0
        for employee in employees_without_checkin:
            try:
                # Send real-time notification
                await notification_manager.notify_user(
                    str(employee.id),
                    "attendance_reminder",
                    {
                        "message": "Don't forget to check in for today!",
                        "type": "reminder",
                        "date": today.isoformat()
                    }
                )
                reminder_count += 1
            except Exception as e:
                logger.error(f"Error sending reminder to employee {employee.id}: {e}")
        
        logger.info(f"Sent attendance reminders to {reminder_count} employees")
        return {"status": "completed", "reminders_sent": reminder_count}
        
    except Exception as e:
        logger.error(f"Error sending attendance reminders: {e}")
        raise self.retry(exc=e, countdown=60, max_retries=3)
    finally:
        db.close()


@celery_app.task(bind=True)
def process_overtime_calculations(self, organization_id: str, target_date: str = None):
    """Process overtime calculations for attendance records"""
    db = SessionLocal()
    
    try:
        if target_date:
            calc_date = datetime.strptime(target_date, "%Y-%m-%d").date()
        else:
            calc_date = date.today() - timedelta(days=1)
        
        # Get attendance records that need overtime calculation
        records = db.query(AttendanceRecord).join(Employee).filter(
            Employee.organization_id == organization_id,
            AttendanceRecord.date == calc_date,
            AttendanceRecord.check_out_time.isnot(None),
            AttendanceRecord.total_hours_worked.isnot(None),
            AttendanceRecord.overtime_hours.is_(None)  # Not yet calculated
        ).all()
        
        # Get attendance policy for organization
        from ..db.models.attendance import AttendancePolicy
        policy = db.query(AttendancePolicy).filter(
            AttendancePolicy.organization_id == organization_id,
            AttendancePolicy.is_active == True
        ).first()
        
        if not policy:
            logger.warning(f"No attendance policy found for organization {organization_id}")
            return {"status": "skipped", "reason": "No attendance policy"}
        
        updated_count = 0
        for record in records:
            if record.total_hours_worked > policy.overtime_threshold:
                record.overtime_hours = record.total_hours_worked - policy.overtime_threshold
                updated_count += 1
        
        db.commit()
        
        logger.info(f"Processed overtime for {updated_count} records in organization {organization_id}")
        return {"status": "completed", "records_updated": updated_count}
        
    except Exception as e:
        db.rollback()
        logger.error(f"Error processing overtime calculations: {e}")
        raise self.retry(exc=e, countdown=60, max_retries=3)
    finally:
        db.close()


@celery_app.task(bind=True)
def cleanup_old_attendance_logs(self, days_to_keep: int = 90):
    """Clean up old attendance logs"""
    db = SessionLocal()
    
    try:
        cutoff_date = datetime.now() - timedelta(days=days_to_keep)
        
        from ..db.models.attendance import AttendanceLog
        
        # Delete old logs
        deleted_count = db.query(AttendanceLog).filter(
            AttendanceLog.created_at < cutoff_date
        ).delete()
        
        db.commit()
        
        logger.info(f"Cleaned up {deleted_count} old attendance logs")
        return {"status": "completed", "logs_deleted": deleted_count}
        
    except Exception as e:
        db.rollback()
        logger.error(f"Error cleaning up attendance logs: {e}")
        raise self.retry(exc=e, countdown=60, max_retries=3)
    finally:
        db.close()
