"""Update Zoho config table as 

Revision ID: 883eb9e88818
Revises: 996091fd865e
Create Date: 2025-05-14 17:44:09.419746

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '883eb9e88818'
down_revision: Union[str, None] = '996091fd865e'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    op.alter_column('zoho_people_integration', 'employees_access_token', 
                   new_column_name='access_token')
    op.alter_column('zoho_people_integration', 'employees_refresh_token', 
                   new_column_name='refresh_token')
    
    op.add_column('zoho_people_integration', 
                sa.Column('is_enabled', sa.<PERSON>olean(), 
                nullable=False, server_default='false'))
    op.add_column('zoho_people_integration', sa.Column('org_id', sa.String(length=100)))
    op.add_column('zoho_people_integration', sa.Column('last_sync', sa.DateTime()))


def downgrade() -> None:
    op.alter_column('zoho_people_integration', 'access_token', 
                   new_column_name='employees_access_token')
    op.alter_column('zoho_people_integration', 'refresh_token', 
                   new_column_name='employees_refresh_token')
    
    op.drop_column('zoho_people_integration', 'is_enabled')
    op.drop_column('zoho_people_integration', 'org_id')
    op.drop_column('zoho_people_integration', 'last_sync')
