from core.databases.database import db
from core.models.basemodel import ModelBase
from sqlalchemy.orm import relationship, declarative_base
from sqlalchemy import Boolean


class UserModel(ModelBase):
    __tablename__ = "users"

    id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    email = db.Column(db.String(80), unique=True, nullable=False)
    password = db.Column(db.String(150), nullable=False)
    first_name = db.Column(db.String(45), nullable=False)
    last_name = db.Column(db.String(45), nullable=False)
    org_name = db.Column(db.String(45), nullable=False)
    email_verified = db.Column(Boolean, default=False) 
    organisations = db.relationship('OrganisationModel', back_populates='owner', overlaps="organisation",lazy='dynamic', cascade="all, delete")
    # departments = db.relationship('DepartmentModel', back_populates='owner', overlaps="department",lazy='dynamic', cascade="all, delete")
    # designation = db.relationship('DesignationModel', back_populates='owner', overlaps="designation",lazy='dynamic', cascade="all, delete")