from sqlalchemy import Colum<PERSON>, <PERSON>, Inte<PERSON>, ForeignKey, Boolean, Text, DateTime, Enum
from sqlalchemy.dialects.postgresql import UUID, JSONB
from sqlalchemy.orm import relationship
from enum import Enum as PyEnum
from datetime import datetime
from typing import Optional

from ..base import BaseModel, AuditMixin


class BoardType(PyEnum):
    PROJECT = "project"
    TEAM = "team"
    PERSONAL = "personal"
    DEPARTMENT = "department"


class CardPriority(PyEnum):
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class KanbanBoard(BaseModel, AuditMixin):
    """Kanban board model"""
    __tablename__ = "kanban_boards"

    # Basic information
    name = Column(String(200), nullable=False)
    description = Column(Text, nullable=True)
    organization_id = Column(UUID(as_uuid=True), nullable=False, index=True)

    # Board configuration
    board_type = Column(Enum(BoardType), nullable=False, default=BoardType.TEAM)

    # Association
    project_id = Column(UUID(as_uuid=True), ForeignKey("projects.id"), nullable=True)
    department_id = Column(UUID(as_uuid=True), ForeignKey("departments.id"), nullable=True)
    owner_id = Column(UUID(as_uuid=True), ForeignKey("employees.id"), nullable=False)

    # Settings
    is_public = Column(Boolean, default=False)
    allow_comments = Column(Boolean, default=True)
    allow_attachments = Column(Boolean, default=True)

    # Customization
    background_color = Column(String(7), nullable=True)  # Hex color
    background_image_url = Column(String(500), nullable=True)

    # Workflow settings
    auto_assign_cards = Column(Boolean, default=False)
    card_aging_enabled = Column(Boolean, default=False)
    wip_limits_enabled = Column(Boolean, default=False)

    # Relationships
    project = relationship("Project")
    department = relationship("Department")
    owner = relationship("Employee", foreign_keys=[owner_id])
    columns = relationship("KanbanColumn", back_populates="board", order_by="KanbanColumn.position")
    members = relationship("KanbanBoardMember", back_populates="board")


class KanbanColumn(BaseModel, AuditMixin):
    """Kanban column model"""
    __tablename__ = "kanban_columns"

    board_id = Column(UUID(as_uuid=True), ForeignKey("kanban_boards.id"), nullable=False, index=True)
    name = Column(String(100), nullable=False)
    position = Column(Integer, nullable=False)

    # Column settings
    color = Column(String(7), nullable=True)  # Hex color
    wip_limit = Column(Integer, nullable=True)  # Work in progress limit

    # Column type
    is_done_column = Column(Boolean, default=False)
    is_backlog_column = Column(Boolean, default=False)

    # Automation
    auto_assign_to = Column(UUID(as_uuid=True), ForeignKey("employees.id"), nullable=True)

    # Relationships
    board = relationship("KanbanBoard", back_populates="columns")
    cards = relationship("KanbanCard", back_populates="column", order_by="KanbanCard.position")
    auto_assignee = relationship("Employee", foreign_keys=[auto_assign_to])


class KanbanCard(BaseModel, AuditMixin):
    """Kanban card model"""
    __tablename__ = "kanban_cards"

    # Basic information
    title = Column(String(200), nullable=False)
    description = Column(Text, nullable=True)

    # Board and column
    board_id = Column(UUID(as_uuid=True), ForeignKey("kanban_boards.id"), nullable=False, index=True)
    column_id = Column(UUID(as_uuid=True), ForeignKey("kanban_columns.id"), nullable=False, index=True)
    position = Column(Integer, nullable=False)

    # Assignment
    assignee_id = Column(UUID(as_uuid=True), ForeignKey("employees.id"), nullable=True)
    reporter_id = Column(UUID(as_uuid=True), ForeignKey("employees.id"), nullable=False)

    # Priority and labels
    priority = Column(Enum(CardPriority), nullable=False, default=CardPriority.MEDIUM)
    labels = Column(JSONB, nullable=True)  # Array of label objects

    # Timeline
    due_date = Column(DateTime(timezone=True), nullable=True)
    start_date = Column(DateTime(timezone=True), nullable=True)
    completed_date = Column(DateTime(timezone=True), nullable=True)

    # Estimation
    story_points = Column(Integer, nullable=True)
    estimated_hours = Column(Integer, nullable=True)  # in minutes

    # Task association
    task_id = Column(UUID(as_uuid=True), ForeignKey("tasks.id"), nullable=True)

    # Attachments and links
    attachment_urls = Column(JSONB, nullable=True)  # Array of file URLs
    external_links = Column(JSONB, nullable=True)  # Array of link objects

    # Card aging
    created_in_column_at = Column(DateTime(timezone=True), nullable=False, default=datetime.utcnow)

    # Relationships
    board = relationship("KanbanBoard")
    column = relationship("KanbanColumn", back_populates="cards")
    assignee = relationship("Employee", foreign_keys=[assignee_id])
    reporter = relationship("Employee", foreign_keys=[reporter_id])
    task = relationship("Task")
    comments = relationship("KanbanCardComment", back_populates="card")
    activities = relationship("KanbanCardActivity", back_populates="card")
    checklists = relationship("KanbanCardChecklist", back_populates="card")


class KanbanCardComment(BaseModel, AuditMixin):
    """Kanban card comment model"""
    __tablename__ = "kanban_card_comments"

    card_id = Column(UUID(as_uuid=True), ForeignKey("kanban_cards.id"), nullable=False, index=True)
    author_id = Column(UUID(as_uuid=True), ForeignKey("employees.id"), nullable=False)

    content = Column(Text, nullable=False)
    attachment_urls = Column(JSONB, nullable=True)  # Array of file URLs

    # Threading
    parent_comment_id = Column(UUID(as_uuid=True), ForeignKey("kanban_card_comments.id"), nullable=True)

    # Relationships
    card = relationship("KanbanCard", back_populates="comments")
    author = relationship("Employee")
    parent_comment = relationship("KanbanCardComment", remote_side="KanbanCardComment.id", back_populates="replies")
    replies = relationship("KanbanCardComment", back_populates="parent_comment")


class KanbanCardActivity(BaseModel):
    """Kanban card activity log"""
    __tablename__ = "kanban_card_activities"

    card_id = Column(UUID(as_uuid=True), ForeignKey("kanban_cards.id"), nullable=False, index=True)
    user_id = Column(UUID(as_uuid=True), ForeignKey("employees.id"), nullable=False)

    # Activity details
    activity_type = Column(String(50), nullable=False)  # moved, assigned, commented, etc.
    description = Column(Text, nullable=False)

    # Activity data
    old_value = Column(Text, nullable=True)
    new_value = Column(Text, nullable=True)
    activity_metadata = Column(JSONB, nullable=True)

    # Relationships
    card = relationship("KanbanCard", back_populates="activities")
    user = relationship("Employee")


class KanbanCardChecklist(BaseModel, AuditMixin):
    """Kanban card checklist"""
    __tablename__ = "kanban_card_checklists"

    card_id = Column(UUID(as_uuid=True), ForeignKey("kanban_cards.id"), nullable=False, index=True)
    name = Column(String(200), nullable=False)
    position = Column(Integer, nullable=False)

    # Relationships
    card = relationship("KanbanCard", back_populates="checklists")
    items = relationship("KanbanChecklistItem", back_populates="checklist", order_by="KanbanChecklistItem.position")


class KanbanChecklistItem(BaseModel, AuditMixin):
    """Kanban checklist item"""
    __tablename__ = "kanban_checklist_items"

    checklist_id = Column(UUID(as_uuid=True), ForeignKey("kanban_card_checklists.id"), nullable=False)
    name = Column(String(200), nullable=False)
    position = Column(Integer, nullable=False)

    # Status
    is_completed = Column(Boolean, default=False)
    completed_by = Column(UUID(as_uuid=True), ForeignKey("employees.id"), nullable=True)
    completed_at = Column(DateTime(timezone=True), nullable=True)

    # Assignment
    assignee_id = Column(UUID(as_uuid=True), ForeignKey("employees.id"), nullable=True)
    due_date = Column(DateTime(timezone=True), nullable=True)

    # Relationships
    checklist = relationship("KanbanCardChecklist", back_populates="items")
    assignee = relationship("Employee", foreign_keys=[assignee_id])
    completer = relationship("Employee", foreign_keys=[completed_by])


class KanbanBoardMember(BaseModel, AuditMixin):
    """Kanban board member"""
    __tablename__ = "kanban_board_members"

    board_id = Column(UUID(as_uuid=True), ForeignKey("kanban_boards.id"), nullable=False)
    employee_id = Column(UUID(as_uuid=True), ForeignKey("employees.id"), nullable=False)

    # Permissions
    can_edit_board = Column(Boolean, default=False)
    can_add_cards = Column(Boolean, default=True)
    can_edit_cards = Column(Boolean, default=True)
    can_delete_cards = Column(Boolean, default=False)
    can_add_members = Column(Boolean, default=False)

    # Role
    role = Column(String(50), nullable=True)  # admin, member, viewer

    # Relationships
    board = relationship("KanbanBoard", back_populates="members")
    employee = relationship("Employee")


class KanbanLabel(BaseModel):
    """Kanban label definition"""
    __tablename__ = "kanban_labels"

    board_id = Column(UUID(as_uuid=True), ForeignKey("kanban_boards.id"), nullable=False)
    name = Column(String(100), nullable=False)
    color = Column(String(7), nullable=False)  # Hex color
    description = Column(Text, nullable=True)

    # Relationships
    board = relationship("KanbanBoard")


class KanbanTemplate(BaseModel):
    """Kanban board template"""
    __tablename__ = "kanban_templates"

    name = Column(String(200), nullable=False)
    description = Column(Text, nullable=True)
    organization_id = Column(UUID(as_uuid=True), nullable=False, index=True)

    # Template configuration
    board_config = Column(JSONB, nullable=False)  # Board settings
    columns_config = Column(JSONB, nullable=False)  # Column definitions
    labels_config = Column(JSONB, nullable=True)  # Label definitions

    # Settings
    is_public = Column(Boolean, default=False)
    category = Column(String(100), nullable=True)

    # Usage tracking
    usage_count = Column(Integer, default=0)
