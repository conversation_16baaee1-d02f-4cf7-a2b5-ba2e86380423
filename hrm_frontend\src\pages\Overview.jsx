import { useState, useEffect } from "react";
import { Clock, Calendar, ChevronDown, MoreVertical, Users, Bell, MessageSquare } from 'lucide-react';
import ProfileSection from "../components/ProfileSection";
import WorkSchedule from "../components/WorkSchedule";
import TimeLogsNotification from "../components/TimeLogsNotification";
import CoverImageUpload from "../components/CoverImageUpload";

export default function Overview() {
  const [currentTime, setCurrentTime] = useState(new Date());
  const [activeTab, setActiveTab] = useState("activities");
  const [showMoreActions, setShowMoreActions] = useState(false);
  const [coverImage, setCoverImage] = useState(null);

  // Update current time every second
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);
    return () => clearInterval(timer);
  }, []);

  // Get greeting based on time of day
  const getGreeting = () => {
    const hour = currentTime.getHours();
    if (hour < 12) return "Good Morning";
    if (hour < 18) return "Good Afternoon";
    return "Good Evening";
  };

  // Handle cover image change
  const handleCoverImageChange = (imageUrl) => {
    setCoverImage(imageUrl);
  };

  return (
    <div className="relative">
      {/* Cover Image with Upload Functionality */}
      <div className="absolute inset-0 -z-10">
        <CoverImageUpload onImageChange={handleCoverImageChange} />
      </div>

      <div className="pt-[220px]">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {/* Profile Section */}
          <div className="md:col-span-1">
            <ProfileSection />
          </div>
          
          {/* Main Content */}
          <div className="md:col-span-2">
            <div className="bg-white rounded-lg shadow-sm p-4 mb-4">
              {/* Tabs */}
              <div className="flex space-x-6 border-b pb-2 overflow-x-auto">
                <TabButton 
                  label="Activities" 
                  active={activeTab === "activities"} 
                  onClick={() => setActiveTab("activities")} 
                />
                <TabButton 
                  label="Feeds" 
                  active={activeTab === "feeds"} 
                  onClick={() => setActiveTab("feeds")} 
                />
                <TabButton 
                  label="Profile" 
                  active={activeTab === "profile"} 
                  onClick={() => setActiveTab("profile")} 
                />
                <TabButton 
                  label="Approvals" 
                  active={activeTab === "approvals"} 
                  onClick={() => setActiveTab("approvals")} 
                />
                <TabButton 
                  label="Leave" 
                  active={activeTab === "leave"} 
                  onClick={() => setActiveTab("leave")} 
                />
                <TabButton 
                  label="Attendance" 
                  active={activeTab === "attendance"} 
                  onClick={() => setActiveTab("attendance")} 
                />
                <TabButton 
                  label="Time Logs" 
                  active={activeTab === "timelogs"} 
                  onClick={() => setActiveTab("timelogs")} 
                />
                <TabButton 
                  label="Timesheets" 
                  active={activeTab === "timesheets"} 
                  onClick={() => setActiveTab("timesheets")} 
                />
              </div>
              

              
              {/* Greeting Section with Logo Fallback */}
              <div className="mt-4 flex items-center gap-4 p-4 bg-gray-50 rounded-lg">
                <div className="flex-shrink-0">
                  <LogoWithFallback />
                </div>
                <div>
                  <div className="flex items-center gap-2">
                    <h3 className="text-lg font-medium">{getGreeting()}</h3>
                    <span className="font-medium">User</span>
                  </div>
                  <p className="text-sm text-gray-600">Have a productive day!</p>
                </div>
                <div className="ml-auto">
                  <div className="w-8 h-8">
                    <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path
                        d="M12 22C17.5228 22 22 17.5228 22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22Z"
                        fill="#e2e8f0"
                      />
                      <path
                        d="M17.5 12C17.5 7.30558 13.6944 3.5 9 3.5C4.30558 3.5 0.5 7.30558 0.5 12C0.5 16.6944 4.30558 20.5 9 20.5C13.6944 20.5 17.5 16.6944 17.5 12Z"
                        fill="#1e293b"
                      />
                    </svg>
                  </div>
                </div>
              </div>
              
              {/* Work Schedule */}
              <WorkSchedule currentDate={currentTime} />
              
              {/* Time Logs Notification */}
              <TimeLogsNotification />
            </div>
            
            {/* Recent Activities */}
            <div className="bg-white rounded-lg shadow-sm p-4">
              <div className="flex items-center justify-between mb-4">
                <h3 className="font-medium">Recent Activities</h3>
                <div className="relative">
                  <button 
                    className="text-gray-500 hover:text-gray-700"
                    onClick={() => setShowMoreActions(!showMoreActions)}
                  >
                    <MoreVertical size={18} />
                  </button>
                  
                  {showMoreActions && (
                    <div className="absolute right-0 top-full mt-1 bg-white shadow-md rounded-md py-1 w-40 z-10">
                      <button className="w-full text-left px-3 py-2 text-sm hover:bg-gray-100">
                        View All Activities
                      </button>
                      <button className="w-full text-left px-3 py-2 text-sm hover:bg-gray-100">
                        Filter Activities
                      </button>
                      <button className="w-full text-left px-3 py-2 text-sm hover:bg-gray-100">
                        Export Activities
                      </button>
                    </div>
                  )}
                </div>
              </div>
              
              <div className="space-y-4">
                <ActivityItem 
                  icon={<Bell size={16} />} 
                  iconBg="bg-blue-100" 
                  iconColor="text-blue-600"
                  title="System Notification" 
                  description="Your leave request has been approved" 
                  time="2 hours ago" 
                />
                
                <ActivityItem 
                  icon={<Users size={16} />} 
                  iconBg="bg-green-100" 
                  iconColor="text-green-600"
                  title="Team Update" 
                  description="Michael Johnson has joined your team" 
                  time="Yesterday" 
                />
                
                <ActivityItem 
                  icon={<MessageSquare size={16} />} 
                  iconBg="bg-purple-100" 
                  iconColor="text-purple-600"
                  title="New Message" 
                  description="You have a new message from HR department" 
                  time="2 days ago" 
                />
              </div>
              
              <div className="mt-4 text-center">
                <button className="text-blue-600 text-sm font-medium hover:underline">
                  View All Activities
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

// Logo with Fallback Component
function LogoWithFallback() {
  const [hasError, setHasError] = useState(false);
  
  return (
    <div className="w-12 h-12 bg-white rounded-md border border-gray-200 flex items-center justify-center">
      {!hasError ? (
        <img
          src="/images/agnoconnect-logo.png"
          alt="AgnoConnect"
          className="w-10 h-10"
          onError={() => setHasError(true)}
        />
      ) : (
        <span className="agno-text-orange font-bold">AC</span>
      )}
    </div>
  );
}

// Tab Button Component
function TabButton({ label, active, onClick }) {
  return (
    <button
      className={`whitespace-nowrap px-2 py-2 text-sm font-medium ${
        active 
          ? "text-blue-600 border-b-2 border-blue-600" 
          : "text-gray-600 hover:text-gray-900"
      }`}
      onClick={onClick}
    >
      {label}
    </button>
  );
}

// Activity Item Component
function ActivityItem({ icon, iconBg, iconColor, title, description, time }) {
  return (
    <div className="flex gap-3">
      <div className={`w-8 h-8 rounded-full ${iconBg} ${iconColor} flex items-center justify-center flex-shrink-0`}>
        {icon}
      </div>
      <div className="flex-1">
        <div className="flex items-center justify-between">
          <h4 className="font-medium">{title}</h4>
          <span className="text-xs text-gray-500">{time}</span>
        </div>
        <p className="text-sm text-gray-600 mt-1">{description}</p>
      </div>
    </div>
  );
}