from flask import url_for, jsonify
from flask.views import <PERSON><PERSON>iew
from flask_smorest import Blueprint, abort
from core.middleware import roles_required
from schemas import BenefitSchema
from sqlalchemy.orm.exc import NoResultFound
from sqlalchemy.exc import SQLAlchemyError, IntegrityError

import core.utils.response_message as RESPONSEMESSAGE
from core.services.benefits import BenefitsService
from core.utils.responseBuilder import ResponseBuilder

blueprint = Blueprint("Benefit", __name__, description="Operations for Benefits")
    
@blueprint.route("/benefits/<id>")
class Benefit(MethodView):
    @roles_required(['admin'])
    @blueprint.response(200, BenefitSchema)
    def get(self, id):
        service = BenefitsService()
        benefit = service.getBenefits(id)
        if not benefit:
            abort(401, message="Benefit does not exist")
        benefit_details = BenefitSchema().dump(benefit)
        return ResponseBuilder(data=benefit_details, status_code=200).build()
           
    @roles_required(['admin'])
    def delete(self, id):
        service = BenefitsService()
        benefit = service.getBenefits(id)
        if not benefit:
            abort(404, message="Benefit does not exist")

        if benefit.employee_benefits:
            abort(400, message=f"Cannot delete benefit. It is still assigned to employees.")

        service.deleteBenefits(id)
        return {"message" : "Benefit deleted"}
    
    @roles_required(['admin'])
    @blueprint.arguments(BenefitSchema)
    @blueprint.response(201, BenefitSchema)
    def put(self, data, id):
        service = BenefitsService()
        benefit = service.getBenefits(id)
        if not benefit:
            abort(404, message="Benefit does not exist")
        try :
            new_benefit = service.updateBenefits(id, data)
            return new_benefit
        except SQLAlchemyError:
                abort(500, message="Error while updating Benefit")
    
@blueprint.route("/benefits")
class BenefitList(MethodView):
    @roles_required(['admin',"employee"])
    @blueprint.response(200, BenefitSchema)
    def get(self):
        benefit_service = BenefitsService()
        benefit_list, total_benefits = benefit_service.fetchAll()
        benefit_schema = BenefitSchema(many=True)
        benefit_list = benefit_schema.dump(benefit_list)
        return ResponseBuilder(data=benefit_list, status_code=200, total=total_benefits).build()
    
    @roles_required(['admin'])
    @blueprint.arguments(BenefitSchema)
    @blueprint.response(200, BenefitSchema)
    def post(self, data):
        try:
            # print("Calling post method....")
            service = BenefitsService()
            # print("Calling post method....1")
            benefit = service.getBenefitsByKey({"benefit_name": data['benefit_name']})
            if not benefit:
                # print("Calling post method....3")
                # print("Calling post method....", data)
                new_benefit = service.createBenefits(data)
                # print("Calling post method....4")
                # print("Created benefit:", new_benefit)
                return new_benefit
            else:
                abort(400, message="Benefit already exists")
        except IntegrityError as e:
            # print("IntegrityError:", str(e))
            abort(500, message="Error while creating Benefit due to database integrity issue")
        except SQLAlchemyError as e:
            # print("SQLAlchemyError:", str(e))
            abort(500, message="Error while creating Benefit due to a database error")
        except Exception as e:
            print("Unexpected error:", str(e))
            abort(500, message="An unexpected error occurred while creating Benefit")
