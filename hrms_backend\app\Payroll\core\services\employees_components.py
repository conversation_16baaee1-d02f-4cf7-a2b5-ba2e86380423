from core.repositories.employees_component import EmployeesComponentRepository


class EmployeesComponentService:
    def __init__(self):
        self.repository = EmployeesComponentRepository()

    def addSalaryComponent(self, employee_id, salary_component_id):
        """
        Add a salary component to an employee.
        
        Args:
            employee_id (int): The ID of the employee.
            salary_component_id (int): The ID of the salary component to add.
        
        Returns:
            EmployeeComponentsPivotModel: The created pivot record.
        """
        return self.repository.create(employee_id, salary_component_id)

    def getSalaryComponentsForEmployee(self, employee_id):
        """
        Retrieve all salary components assigned to a specific employee.
        
        Args:
            employee_id (int): The ID of the employee.
        
        Returns:
            list: A list of EmployeeComponentsPivotModel instances.
        """
        return self.repository.getAttachedEmployee(employee_id)

    def deleteSalaryComponents(self, employee_id, component_ids):
        """
        Delete specific salary component pivots assigned to an employee.
        
        Args:
            employee_id (int): The ID of the employee.
            component_ids (list): A list of IDs of the salary component pivots to delete.
        
        Returns:
            None
        """
        return self.repository.delete(employee_id, component_ids)
