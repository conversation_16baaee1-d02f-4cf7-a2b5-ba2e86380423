from core.databases.database import db
from core.models.basemodel import ModelBase
from datetime import datetime

class SalaryTemplateBenefitsPivotModel(ModelBase):
    __tablename__ = "salary_template_benefits_pivot"

    id = db.Column(db.Integer, primary_key=True)
    salary_template_id = db.Column(db.In<PERSON>ger, db.Foreign<PERSON>ey((ModelBase.dbSchema() + '.salary_templates.id')), nullable=False) 
    benefits_id = db.Column(db.Integer, db.ForeignKey((ModelBase.dbSchema() + '.benefits.id')), nullable=False) 

    salary_template = db.relationship('SalaryTemplatesModel', back_populates='salary_template_benefits')
    benefit = db.relationship('BenefitsModel', back_populates='salary_template_benefits')
