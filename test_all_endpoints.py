#!/usr/bin/env python3
"""
Test all available backend endpoints
"""

import requests
import json

BASE_URL = "http://localhost:8085"

def test_all_endpoints():
    """Test all available endpoints"""
    try:
        # Login first
        credentials = {
            "email": "<EMAIL>",
            "password": "password123"
        }
        
        login_response = requests.post(f"{BASE_URL}/api/auth/login", json=credentials)
        if login_response.status_code != 200:
            print("Login failed")
            return
            
        token = login_response.json().get('access_token')
        headers = {"Authorization": f"Bearer {token}"}
        
        # Test endpoints
        endpoints_to_test = [
            "/api/employees/",
            "/api/attendance/",
            "/api/leave/",
            "/api/payroll/",
            "/api/performance/",
            "/api/shift/",
            "/api/timesheet/",
            "/api/project/",
            "/api/kanban/",
            "/api/ticket/",
            "/api/reports/",
            "/api/settings/"
        ]
        
        print("Testing Backend API Endpoints")
        print("=" * 50)
        
        for endpoint in endpoints_to_test:
            try:
                response = requests.get(f"{BASE_URL}{endpoint}", headers=headers, timeout=5)
                status = "✅ WORKING" if response.status_code == 200 else f"❌ {response.status_code}"
                print(f"{endpoint:<25} {status}")
                
                if response.status_code == 200:
                    try:
                        data = response.json()
                        if isinstance(data, dict):
                            if 'total' in data:
                                print(f"{'':25} → Found {data.get('total', 0)} items")
                            elif len(data) > 0:
                                print(f"{'':25} → Response has {len(data)} keys")
                        elif isinstance(data, list):
                            print(f"{'':25} → Found {len(data)} items")
                    except:
                        pass
                        
            except Exception as e:
                print(f"{endpoint:<25} ❌ ERROR: {str(e)[:30]}...")
        
        print("\n" + "=" * 50)
        print("API Documentation available at: http://localhost:8085/docs")
        
    except Exception as e:
        print(f"Test failed: {e}")

if __name__ == "__main__":
    test_all_endpoints()
