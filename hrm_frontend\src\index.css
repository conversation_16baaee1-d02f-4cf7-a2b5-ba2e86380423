@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&family=Poppins:wght@300;400;500;600;700;800;900&family=Roboto:wght@300;400;500;700;900&display=swap');
@tailwind base;
@tailwind components;
@tailwind utilities;

/* AgnoConnect CSS Variables */
:root {
  /* Brand Colors */
  --agno-primary-dark: #073763;
  --agno-primary: #0B2A5A;
  --agno-orange: #F47C20;
  --agno-gray-medium: #6E7C8E;
  --agno-gray-dark: #2E2E2E;
  --agno-white: #FFFFFF;

  /* Semantic Colors */
  --color-primary: var(--agno-primary);
  --color-primary-dark: var(--agno-primary-dark);
  --color-accent: var(--agno-orange);
  --color-text: var(--agno-gray-dark);
  --color-text-secondary: var(--agno-gray-medium);
  --color-background: var(--agno-white);

  /* Gradients */
  --gradient-primary: linear-gradient(135deg, var(--agno-primary-dark) 0%, var(--agno-primary) 100%);
  --gradient-accent: linear-gradient(135deg, var(--agno-orange) 0%, #ea580c 100%);
  --gradient-soft: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);

  /* Shadows */
  --shadow-soft: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  --shadow-medium: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-large: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);

  /* Typography */
  --font-primary: 'Inter', system-ui, -apple-system, sans-serif;
  --font-heading: 'Poppins', system-ui, -apple-system, sans-serif;
  --font-mono: 'Roboto Mono', monospace;
}

/* Global Styles */
body {
  font-family: var(--font-primary);
  color: var(--color-text);
  background-color: #f8fafc;
  line-height: 1.6;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Typography Enhancements */
h1, h2, h3, h4, h5, h6 {
  font-family: var(--font-heading);
  font-weight: 600;
  line-height: 1.3;
  letter-spacing: -0.025em;
}

h1 { font-size: 2.25rem; font-weight: 700; }
h2 { font-size: 1.875rem; font-weight: 600; }
h3 { font-size: 1.5rem; font-weight: 600; }
h4 { font-size: 1.25rem; font-weight: 600; }
h5 { font-size: 1.125rem; font-weight: 500; }
h6 { font-size: 1rem; font-weight: 500; }

/* Professional Button Styles */
.btn-primary {
  background-color: #2563eb;
  color: white;
  font-weight: 500;
  padding: 0.75rem 1.5rem;
  border-radius: 0.5rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  transition: all 0.2s;
}

.btn-primary:hover {
  background-color: #1d4ed8;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

.btn-secondary {
  background-color: white;
  color: #374151;
  font-weight: 500;
  padding: 0.75rem 1.5rem;
  border-radius: 0.5rem;
  border: 1px solid #d1d5db;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  transition: all 0.2s;
}

.btn-secondary:hover {
  background-color: #f9fafb;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.btn-success {
  background-color: #16a34a;
  color: white;
  font-weight: 500;
  padding: 0.75rem 1.5rem;
  border-radius: 0.5rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  transition: all 0.2s;
}

.btn-success:hover {
  background-color: #15803d;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

.btn-danger {
  background-color: #dc2626;
  color: white;
  font-weight: 500;
  padding: 0.75rem 1.5rem;
  border-radius: 0.5rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  transition: all 0.2s;
}

.btn-danger:hover {
  background-color: #b91c1c;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

/* AgnoConnect specific utility classes */
.agno-gradient {
  background: var(--gradient-primary);
}

.agno-gradient-accent {
  background: var(--gradient-accent);
}

.agno-text-primary {
  color: var(--agno-primary);
}

.agno-text-orange {
  color: var(--agno-orange);
}

.agno-bg-primary {
  background-color: var(--agno-primary);
}

.agno-bg-primary-dark {
  background-color: var(--agno-primary-dark);
}

.agno-bg-orange {
  background-color: var(--agno-orange);
}

/* Professional Card Styles */
.card {
  background-color: white;
  border-radius: 0.75rem;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  border: 1px solid #e5e7eb;
  overflow: hidden;
}

.card-header {
  padding: 1.5rem;
  border-bottom: 1px solid #e5e7eb;
  background-color: #f9fafb;
}

.card-body {
  padding: 1.5rem;
}

.card-footer {
  padding: 1.5rem;
  border-top: 1px solid #e5e7eb;
  background-color: #f9fafb;
}

/* Enhanced Form Styles */
.form-input {
  width: 100%;
  padding: 0.75rem 1rem;
  border: 1px solid #d1d5db;
  border-radius: 0.5rem;
  font-weight: 500;
  transition: all 0.2s;
}

.form-input:focus {
  outline: none;
  border-color: transparent;
  box-shadow: 0 0 0 2px #3b82f6, 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-label {
  display: block;
  font-size: 0.875rem;
  font-weight: 600;
  color: #374151;
  margin-bottom: 0.5rem;
}

.form-error {
  color: #ef4444;
  font-size: 0.875rem;
  margin-top: 0.25rem;
  display: flex;
  align-items: center;
}

/* Professional Table Styles */
.table {
  @apply min-w-full divide-y divide-gray-200;
}

.table-header {
  @apply bg-gray-50;
}

.table-header th {
  @apply px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider;
}

.table-body {
  @apply bg-white divide-y divide-gray-200;
}

.table-row {
  @apply hover:bg-gray-50 transition-colors duration-150;
}

.table-cell {
  @apply px-6 py-4 whitespace-nowrap text-sm text-gray-900;
}

/* Status Badge Styles */
.badge {
  @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
}

.badge-success {
  @apply bg-green-100 text-green-800;
}

.badge-warning {
  @apply bg-amber-100 text-amber-800;
}

.badge-danger {
  @apply bg-red-100 text-red-800;
}

.badge-info {
  @apply bg-blue-100 text-blue-800;
}

/* Animation Classes */
.fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

.slide-up {
  animation: slideUp 0.3s ease-out;
}

.scale-in {
  animation: scaleIn 0.2s ease-out;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* Loading Spinner */
.spinner {
  @apply animate-spin rounded-full border-b-2 border-blue-600;
}

/* Professional Modal Styles */
.modal-overlay {
  @apply fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4;
}

.modal-content {
  @apply bg-white rounded-2xl shadow-2xl w-full max-h-[90vh] overflow-hidden;
}

.modal-header {
  @apply px-6 py-4 border-b border-gray-200;
}

.modal-body {
  @apply p-6 overflow-y-auto;
}

.modal-footer {
  @apply px-6 py-4 border-t border-gray-200 bg-gray-50 flex justify-end space-x-3;
}

/* Responsive Design Enhancements */
@media (max-width: 768px) {
  .card {
    @apply rounded-lg;
  }

  .modal-content {
    @apply rounded-lg mx-4;
  }

  h1 { font-size: 1.875rem; }
  h2 { font-size: 1.5rem; }
  h3 { font-size: 1.25rem; }
}

/* Button styles */
.btn-agno-primary {
  background-color: var(--agno-primary);
  color: var(--agno-white);
  border: 1px solid var(--agno-primary);
  transition: all 0.2s ease-in-out;
}

.btn-agno-primary:hover {
  background-color: var(--agno-primary-dark);
  border-color: var(--agno-primary-dark);
}

.btn-agno-accent {
  background-color: var(--agno-orange);
  color: var(--agno-white);
  border: 1px solid var(--agno-orange);
  transition: all 0.2s ease-in-out;
}

.btn-agno-accent:hover {
  background-color: #ea580c;
  border-color: #ea580c;
}

/* Card shadows with AgnoConnect colors */
.shadow-agno {
  box-shadow: 0 4px 6px -1px rgba(7, 55, 99, 0.1), 0 2px 4px -1px rgba(7, 55, 99, 0.06);
}

.shadow-agno-lg {
  box-shadow: 0 10px 15px -3px rgba(7, 55, 99, 0.1), 0 4px 6px -2px rgba(7, 55, 99, 0.05);
}
