from typing import Optional, List, Dict, Any, Tuple
from sqlalchemy.orm import Session, joinedload
from sqlalchemy import and_, or_, func, desc, asc
from fastapi import HTTPException, status
from uuid import UUID
import uuid
from datetime import datetime, date, timedelta
import logging

from ...db.models.onboarding import (
    OnboardingWorkflow, OffboardingWorkflow, WorkflowTemplate,
    OnboardingTask, OffboardingTask, OnboardingDocument, OffboardingDocument,
    WorkflowStatus, TaskStatus, DocumentStatus
)
from ...db.models.employee import Employee
from ...schemas.onboarding import (
    WorkflowTemplateCreate, OnboardingWorkflowCreate, OnboardingWorkflowUpdate,
    OffboardingWorkflowCreate, OffboardingWorkflowUpdate, OnboardingTaskCreate,
    OnboardingTaskUpdate, OffboardingTaskCreate, OffboardingTaskUpdate,
    DocumentCreate, DocumentUpdate
)
from ...core.security import CurrentUser
from ...core.audit_logger import AuditLogger
from .onboarding_email_service import OnboardingEmailService

logger = logging.getLogger(__name__)


class OnboardingService:
    """Service for onboarding and offboarding workflows"""

    def __init__(self):
        self.email_service = OnboardingEmailService()

    # Workflow Template Methods
    async def create_workflow_template(
        self,
        db: Session,
        template_data: WorkflowTemplateCreate,
        current_user: CurrentUser
    ) -> WorkflowTemplate:
        """Create a workflow template"""
        try:
            template = WorkflowTemplate(
                **template_data.dict(),
                organization_id=current_user.organization_id
            )

            db.add(template)
            db.commit()
            db.refresh(template)

            # Log audit event
            AuditLogger.log_action(
                db=db,
                action="CREATE",
                resource_type="workflow_templates",
                resource_id=str(template.id),
                user=current_user,
                new_values=template_data.dict()
            )

            return template

        except Exception as e:
            logger.error(f"Error creating workflow template: {e}")
            db.rollback()
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to create workflow template"
            )

    # Onboarding Workflow Methods
    async def quick_onboard_employee(
        self,
        db: Session,
        employee_name: str,
        employee_email: str,
        department_id: Optional[UUID] = None,
        designation_id: Optional[UUID] = None,
        start_date: Optional[date] = None,
        current_user: CurrentUser = None
    ) -> Dict[str, Any]:
        """
        Quick onboarding: Create employee and start onboarding workflow from minimal info
        """
        try:
            # Validate email format
            import re
            email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
            if not re.match(email_pattern, employee_email):
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Invalid email format"
                )

            # Check if employee with this email already exists
            existing_employee = db.query(Employee).filter(Employee.email == employee_email).first()
            if existing_employee:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Employee with this email already exists"
                )

            # Parse name
            name_parts = employee_name.strip().split()
            first_name = name_parts[0] if name_parts else "Unknown"
            last_name = " ".join(name_parts[1:]) if len(name_parts) > 1 else ""

            # Generate employee ID
            employee_count = db.query(Employee).count()
            employee_id = f"EMP{str(employee_count + 1).zfill(4)}"

            # Create temporary password
            import secrets
            import string
            temp_password = ''.join(secrets.choice(string.ascii_letters + string.digits) for _ in range(12))

            # Create employee record using raw SQL to avoid model issues
            from sqlalchemy import text

            # Insert employee directly
            employee_insert_query = text("""
                INSERT INTO employees (
                    id, employee_id, first_name, last_name, email, status, hire_date,
                    department_id, designation_id, organization_id, created_at, updated_at, is_active
                ) VALUES (
                    :id, :employee_id, :first_name, :last_name, :email, :status, :hire_date,
                    :department_id, :designation_id, :organization_id, :created_at, :updated_at, :is_active
                ) RETURNING id
            """)

            employee_id_uuid = uuid.uuid4()
            employee_result = db.execute(employee_insert_query, {
                'id': employee_id_uuid,
                'employee_id': employee_id,
                'first_name': first_name,
                'last_name': last_name,
                'email': employee_email,
                'status': 'ACTIVE',
                'hire_date': start_date or date.today(),
                'department_id': department_id,
                'designation_id': designation_id,
                'organization_id': getattr(current_user, 'organization_id', None),
                'created_at': datetime.utcnow(),
                'updated_at': datetime.utcnow(),
                'is_active': True
            })

            # Get the created employee
            employee = db.query(Employee).filter(Employee.id == employee_id_uuid).first()

            if not employee:
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail="Failed to create employee record"
                )

            # Create user account for the employee using raw SQL
            from ...core.security import get_password_hash

            user_insert_query = text("""
                INSERT INTO users (
                    id, email, password_hash, first_name, last_name, role, is_active,
                    employee_id, created_at, updated_at
                ) VALUES (
                    :id, :email, :password_hash, :first_name, :last_name, :role, :is_active,
                    :employee_id, :created_at, :updated_at
                ) RETURNING id
            """)

            user_id_uuid = uuid.uuid4()
            user_result = db.execute(user_insert_query, {
                'id': user_id_uuid,
                'email': employee_email,
                'password_hash': get_password_hash(temp_password),
                'first_name': first_name,
                'last_name': last_name,
                'role': 'employee',
                'is_active': True,
                'employee_id': employee.id,
                'created_at': datetime.utcnow(),
                'updated_at': datetime.utcnow()
            })

            # Update employee with user_id using raw SQL
            update_employee_query = text("""
                UPDATE employees SET user_id = :user_id WHERE id = :employee_id
            """)

            db.execute(update_employee_query, {
                'user_id': user_id_uuid,
                'employee_id': employee.id
            })

            # Get default onboarding template
            template = db.query(WorkflowTemplate).filter(
                WorkflowTemplate.is_default == True,
                WorkflowTemplate.workflow_type == "onboarding",
                WorkflowTemplate.is_active == True
            ).first()

            if not template:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="No default onboarding template found"
                )

            # Create onboarding workflow
            workflow_data = OnboardingWorkflowCreate(
                employee_id=employee.id,
                template_id=template.id,
                title=f"Onboarding - {employee.full_name}",
                description=f"Complete onboarding process for {employee.full_name}",
                start_date=start_date or date.today(),
                expected_completion_date=(start_date or date.today()) + timedelta(days=template.default_duration_days or 90),
                assigned_to_id=getattr(current_user, 'user_id', None)
            )

            workflow = await self.create_onboarding_workflow(db, workflow_data, current_user)

            # Send welcome and onboarding emails
            try:
                # Send welcome email with login credentials
                await self.email_service.send_welcome_email(
                    employee=employee,
                    workflow=workflow,
                    login_credentials={
                        "email": employee_email,
                        "password": temp_password
                    }
                )

                # Get tasks for onboarding start email
                tasks = db.query(OnboardingTask).filter(
                    OnboardingTask.workflow_id == workflow.id
                ).order_by(OnboardingTask.order_index).limit(5).all()

                if tasks:
                    await self.email_service.send_onboarding_start_notification(
                        employee, workflow, tasks
                    )

                # Notify HR team
                if current_user:
                    hr_user = db.query(Employee).filter(Employee.user_id == current_user.user_id).first()
                    if hr_user:
                        await self.email_service.send_hr_notification(
                            hr_user.email, employee, workflow, "new_employee"
                        )

            except Exception as e:
                logger.warning(f"Failed to send onboarding emails: {e}")

            db.commit()

            # Log audit event
            AuditLogger.log_action(
                db=db,
                action="CREATE",
                resource_type="quick_onboarding",
                resource_id=str(employee.id),
                user=current_user,
                new_values={
                    "employee_id": employee.employee_id,
                    "email": employee_email,
                    "workflow_id": str(workflow.id)
                }
            )

            return {
                "success": True,
                "employee": {
                    "id": str(employee.id),
                    "employee_id": employee.employee_id,
                    "name": employee.full_name,
                    "email": employee.email,
                    "hire_date": str(employee.hire_date)
                },
                "workflow": {
                    "id": str(workflow.id),
                    "title": workflow.title,
                    "status": workflow.status,
                    "progress": workflow.progress_percentage
                },
                "credentials": {
                    "email": employee_email,
                    "temporary_password": temp_password,
                    "login_url": "http://localhost:5173"
                },
                "message": "Employee created and onboarding started successfully! Welcome email sent."
            }

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error in quick onboarding: {e}")
            db.rollback()
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to create employee and start onboarding"
            )

    async def create_onboarding_workflow(
        self,
        db: Session,
        workflow_data: OnboardingWorkflowCreate,
        current_user: CurrentUser
    ) -> OnboardingWorkflow:
        """Create an onboarding workflow"""
        try:
            # Verify employee exists
            employee = db.query(Employee).filter(
                Employee.id == workflow_data.employee_id
            ).first()

            if not employee:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Employee not found"
                )

            # Check if onboarding workflow already exists for this employee
            existing_workflow = db.query(OnboardingWorkflow).filter(
                OnboardingWorkflow.employee_id == workflow_data.employee_id,
                OnboardingWorkflow.status.in_([WorkflowStatus.NOT_STARTED, WorkflowStatus.IN_PROGRESS])
            ).first()

            if existing_workflow:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Active onboarding workflow already exists for this employee"
                )

            # Create workflow
            workflow = OnboardingWorkflow(
                **workflow_data.dict(),
                organization_id=current_user.organization_id,
                status=WorkflowStatus.NOT_STARTED
            )

            db.add(workflow)
            db.commit()
            db.refresh(workflow)

            # Create tasks from template if provided
            if workflow_data.template_id:
                await self._create_tasks_from_template(db, workflow, workflow_data.template_id, current_user)

            # Log audit event
            AuditLogger.log_action(
                db=db,
                action="CREATE",
                resource_type="onboarding_workflows",
                resource_id=str(workflow.id),
                user=current_user,
                new_values=workflow_data.dict()
            )

            # Send welcome email to employee
            try:
                employee = db.query(Employee).filter(Employee.id == workflow_data.employee_id).first()
                if employee:
                    await self.email_service.send_welcome_email(employee, workflow)

                    # Get tasks for onboarding start email
                    tasks = db.query(OnboardingTask).filter(
                        OnboardingTask.workflow_id == workflow.id
                    ).order_by(OnboardingTask.order_index).all()

                    if tasks:
                        await self.email_service.send_onboarding_start_notification(
                            employee, workflow, tasks
                        )

                    # Notify HR team
                    if workflow.assigned_to_id:
                        hr_employee = db.query(Employee).filter(Employee.id == workflow.assigned_to_id).first()
                        if hr_employee:
                            await self.email_service.send_hr_notification(
                                hr_employee.email, employee, workflow, "new_employee"
                            )
            except Exception as e:
                logger.warning(f"Failed to send onboarding emails: {e}")

            return workflow

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error creating onboarding workflow: {e}")
            db.rollback()
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to create onboarding workflow"
            )

    async def get_onboarding_workflows(
        self,
        db: Session,
        skip: int = 0,
        limit: int = 20,
        status: Optional[WorkflowStatus] = None,
        employee_id: Optional[UUID] = None,
        assigned_to_id: Optional[UUID] = None,
        current_user: CurrentUser = None
    ) -> Tuple[List[OnboardingWorkflow], int]:
        """Get onboarding workflows with filtering"""
        try:
            query = db.query(OnboardingWorkflow).options(
                joinedload(OnboardingWorkflow.employee),
                joinedload(OnboardingWorkflow.assigned_to),
                joinedload(OnboardingWorkflow.buddy)
            )

            # Apply organization filter
            if current_user and current_user.organization_id:
                query = query.filter(OnboardingWorkflow.organization_id == current_user.organization_id)

            # Apply filters
            if status:
                query = query.filter(OnboardingWorkflow.status == status)
            
            if employee_id:
                query = query.filter(OnboardingWorkflow.employee_id == employee_id)
            
            if assigned_to_id:
                query = query.filter(OnboardingWorkflow.assigned_to_id == assigned_to_id)

            # Get total count
            total = query.count()

            # Apply pagination and ordering
            workflows = query.order_by(desc(OnboardingWorkflow.created_at)).offset(skip).limit(limit).all()

            return workflows, total

        except Exception as e:
            logger.error(f"Error getting onboarding workflows: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to retrieve onboarding workflows"
            )

    async def get_onboarding_workflow(self, db: Session, workflow_id: UUID) -> OnboardingWorkflow:
        """Get a specific onboarding workflow"""
        workflow = db.query(OnboardingWorkflow).options(
            joinedload(OnboardingWorkflow.employee),
            joinedload(OnboardingWorkflow.assigned_to),
            joinedload(OnboardingWorkflow.buddy),
            joinedload(OnboardingWorkflow.tasks),
            joinedload(OnboardingWorkflow.documents)
        ).filter(OnboardingWorkflow.id == workflow_id).first()

        if not workflow:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Onboarding workflow not found"
            )

        return workflow

    async def update_onboarding_workflow(
        self,
        db: Session,
        workflow_id: UUID,
        workflow_data: OnboardingWorkflowUpdate,
        current_user: CurrentUser
    ) -> OnboardingWorkflow:
        """Update an onboarding workflow"""
        try:
            workflow = await self.get_onboarding_workflow(db, workflow_id)

            # Store old values for audit
            old_values = {
                "title": workflow.title,
                "status": workflow.status.value if workflow.status else None,
                "assigned_to_id": str(workflow.assigned_to_id) if workflow.assigned_to_id else None
            }

            # Update fields
            update_data = workflow_data.dict(exclude_unset=True)
            for field, value in update_data.items():
                setattr(workflow, field, value)

            db.commit()
            db.refresh(workflow)

            # Log audit event
            AuditLogger.log_action(
                db=db,
                action="UPDATE",
                resource_type="onboarding_workflows",
                resource_id=str(workflow.id),
                user=current_user,
                old_values=old_values,
                new_values=update_data
            )

            return workflow

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error updating onboarding workflow: {e}")
            db.rollback()
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to update onboarding workflow"
            )

    # Offboarding Workflow Methods
    async def create_offboarding_workflow(
        self,
        db: Session,
        workflow_data: OffboardingWorkflowCreate,
        current_user: CurrentUser
    ) -> OffboardingWorkflow:
        """Create an offboarding workflow"""
        try:
            # Verify employee exists
            employee = db.query(Employee).filter(
                Employee.id == workflow_data.employee_id
            ).first()

            if not employee:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Employee not found"
                )

            # Check if offboarding workflow already exists for this employee
            existing_workflow = db.query(OffboardingWorkflow).filter(
                OffboardingWorkflow.employee_id == workflow_data.employee_id,
                OffboardingWorkflow.status.in_([WorkflowStatus.NOT_STARTED, WorkflowStatus.IN_PROGRESS])
            ).first()

            if existing_workflow:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Active offboarding workflow already exists for this employee"
                )

            # Create workflow
            workflow = OffboardingWorkflow(
                **workflow_data.dict(),
                organization_id=current_user.organization_id,
                status=WorkflowStatus.NOT_STARTED
            )

            db.add(workflow)
            db.commit()
            db.refresh(workflow)

            # Create tasks from template if provided
            if workflow_data.template_id:
                await self._create_offboarding_tasks_from_template(db, workflow, workflow_data.template_id, current_user)

            # Log audit event
            AuditLogger.log_action(
                db=db,
                action="CREATE",
                resource_type="offboarding_workflows",
                resource_id=str(workflow.id),
                user=current_user,
                new_values=workflow_data.dict()
            )

            return workflow

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error creating offboarding workflow: {e}")
            db.rollback()
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to create offboarding workflow"
            )

    # Task Methods
    async def create_onboarding_task(
        self,
        db: Session,
        task_data: OnboardingTaskCreate,
        current_user: CurrentUser
    ) -> OnboardingTask:
        """Create an onboarding task"""
        try:
            # Verify workflow exists
            workflow = await self.get_onboarding_workflow(db, task_data.workflow_id)

            # Create task
            task = OnboardingTask(
                **task_data.dict(),
                organization_id=current_user.organization_id,
                status=TaskStatus.PENDING
            )

            db.add(task)
            db.commit()
            db.refresh(task)

            # Update workflow task count
            await self._update_workflow_progress(db, workflow.id, "onboarding")

            # Log audit event
            AuditLogger.log_action(
                db=db,
                action="CREATE",
                resource_type="onboarding_tasks",
                resource_id=str(task.id),
                user=current_user,
                new_values=task_data.dict()
            )

            return task

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error creating onboarding task: {e}")
            db.rollback()
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to create onboarding task"
            )

    async def update_task_status(
        self,
        db: Session,
        task_id: UUID,
        new_status: TaskStatus,
        current_user: CurrentUser,
        completion_notes: Optional[str] = None
    ) -> OnboardingTask:
        """Update task status"""
        try:
            task = db.query(OnboardingTask).filter(OnboardingTask.id == task_id).first()
            
            if not task:
                # Try offboarding task
                task = db.query(OffboardingTask).filter(OffboardingTask.id == task_id).first()
                if not task:
                    raise HTTPException(
                        status_code=status.HTTP_404_NOT_FOUND,
                        detail="Task not found"
                    )

            old_status = task.status
            task.status = new_status

            if new_status == TaskStatus.COMPLETED:
                task.completed_date = datetime.utcnow()
                task.completed_by_id = current_user.user_id

            if completion_notes:
                task.completion_notes = completion_notes

            db.commit()
            db.refresh(task)

            # Update workflow progress
            workflow_type = "onboarding" if isinstance(task, OnboardingTask) else "offboarding"
            await self._update_workflow_progress(db, task.workflow_id, workflow_type)

            # Log audit event
            AuditLogger.log_action(
                db=db,
                action="STATUS_UPDATE",
                resource_type=f"{workflow_type}_tasks",
                resource_id=str(task.id),
                user=current_user,
                old_values={"status": old_status.value if old_status else None},
                new_values={"status": new_status.value, "completion_notes": completion_notes}
            )

            return task

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error updating task status: {e}")
            db.rollback()
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to update task status"
            )

    async def _create_tasks_from_template(
        self,
        db: Session,
        workflow: OnboardingWorkflow,
        template_id: UUID,
        current_user: CurrentUser
    ):
        """Create tasks from workflow template"""
        try:
            template = db.query(WorkflowTemplate).filter(
                WorkflowTemplate.id == template_id
            ).first()

            if not template or not template.task_templates:
                return

            for task_template in template.task_templates:
                task = OnboardingTask(
                    workflow_id=workflow.id,
                    title=task_template.get("title", ""),
                    description=task_template.get("description"),
                    task_type=task_template.get("task_type", "custom"),
                    order_index=task_template.get("order_index", 0),
                    assigned_to_role=task_template.get("assigned_to_role"),
                    estimated_hours=task_template.get("estimated_hours"),
                    is_mandatory=task_template.get("is_mandatory", True),
                    auto_complete=task_template.get("auto_complete", False),
                    requires_approval=task_template.get("requires_approval", False),
                    task_data=task_template.get("task_data"),
                    organization_id=current_user.organization_id,
                    status=TaskStatus.PENDING
                )

                # Calculate due date if duration is specified
                if task_template.get("due_days_from_start"):
                    task.due_date = workflow.start_date + timedelta(days=task_template["due_days_from_start"])

                db.add(task)

            db.commit()

        except Exception as e:
            logger.error(f"Error creating tasks from template: {e}")
            db.rollback()

    async def _create_offboarding_tasks_from_template(
        self,
        db: Session,
        workflow: OffboardingWorkflow,
        template_id: UUID,
        current_user: CurrentUser
    ):
        """Create offboarding tasks from workflow template"""
        # Similar implementation to onboarding tasks
        pass

    async def complete_onboarding_workflow(
        self,
        db: Session,
        workflow_id: UUID,
        current_user: CurrentUser
    ) -> OnboardingWorkflow:
        """Complete an onboarding workflow and send completion email"""
        try:
            workflow = await self.get_onboarding_workflow(db, workflow_id)

            # Update workflow status
            workflow.status = WorkflowStatus.COMPLETED
            workflow.actual_completion_date = date.today()
            workflow.progress_percentage = 100

            db.commit()
            db.refresh(workflow)

            # Send completion email
            try:
                employee = db.query(Employee).filter(Employee.id == workflow.employee_id).first()
                if employee:
                    await self.email_service.send_completion_email(employee, workflow)

                    # Notify HR team
                    if workflow.assigned_to_id:
                        hr_employee = db.query(Employee).filter(Employee.id == workflow.assigned_to_id).first()
                        if hr_employee:
                            await self.email_service.send_hr_notification(
                                hr_employee.email, employee, workflow, "completion"
                            )
            except Exception as e:
                logger.warning(f"Failed to send completion emails: {e}")

            # Log audit event
            AuditLogger.log_action(
                db=db,
                action="COMPLETE",
                resource_type="onboarding_workflows",
                resource_id=str(workflow.id),
                user=current_user,
                new_values={"status": "COMPLETED", "completion_date": str(date.today())}
            )

            return workflow

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error completing onboarding workflow: {e}")
            db.rollback()
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to complete onboarding workflow"
            )

    async def _update_workflow_progress(self, db: Session, workflow_id: UUID, workflow_type: str):
        """Update workflow progress based on task completion"""
        try:
            if workflow_type == "onboarding":
                workflow = db.query(OnboardingWorkflow).filter(
                    OnboardingWorkflow.id == workflow_id
                ).first()
                tasks = db.query(OnboardingTask).filter(
                    OnboardingTask.workflow_id == workflow_id
                ).all()
            else:
                workflow = db.query(OffboardingWorkflow).filter(
                    OffboardingWorkflow.id == workflow_id
                ).first()
                tasks = db.query(OffboardingTask).filter(
                    OffboardingTask.workflow_id == workflow_id
                ).all()

            if not workflow or not tasks:
                return

            total_tasks = len(tasks)
            completed_tasks = len([t for t in tasks if t.status == TaskStatus.COMPLETED])

            workflow.total_tasks = total_tasks
            workflow.completed_tasks = completed_tasks
            workflow.progress_percentage = int((completed_tasks / total_tasks) * 100) if total_tasks > 0 else 0

            # Update workflow status
            if completed_tasks == total_tasks and total_tasks > 0:
                workflow.status = WorkflowStatus.COMPLETED
                workflow.actual_completion_date = date.today()
            elif completed_tasks > 0:
                workflow.status = WorkflowStatus.IN_PROGRESS

            db.commit()

        except Exception as e:
            logger.error(f"Error updating workflow progress: {e}")
            db.rollback()
