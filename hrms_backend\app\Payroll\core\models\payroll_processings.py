from core.databases.database import db
from core.models.basemodel import ModelBase
from sqlalchemy.orm import relationship
class PayrollProcessingsModel(ModelBase):
    __tablename__ = "payroll_processings"

    id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    month = db.Column(db.String(50), nullable=False)
    year = db.Column(db.String(50), nullable=False)
    organisation_id = db.Column(db.Integer, db.ForeignKey('organisations.id'), nullable=False)
    childpayDe = db.relationship("PayrollDetailsModel")       
    
    
      