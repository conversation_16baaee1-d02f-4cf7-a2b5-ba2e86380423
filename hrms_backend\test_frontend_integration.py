"""
Test Frontend Integration
Test the simple onboarding API with the exact data format from frontend
"""

import requests
import json

def test_frontend_integration():
    """Test the simple onboarding API with frontend data format"""
    
    base_url = "http://localhost:8085/api"
    
    print("🌐 Testing Frontend Integration")
    print("=" * 50)
    
    # Step 1: Login
    print("1. Logging in as admin...")
    login_response = requests.post(f"{base_url}/auth/login", json={
        "email": "<EMAIL>",
        "password": "password123"
    })
    
    if login_response.status_code != 200:
        print(f"❌ Login failed: {login_response.status_code}")
        return
    
    token = login_response.json()["access_token"]
    headers = {"Authorization": f"Bearer {token}"}
    print("✅ Login successful")
    
    # Step 2: Test with frontend data format (same as the modal form)
    print("\n2. Testing with frontend data format...")
    
    frontend_data = {
        "employee_name": "<PERSON> Bala <PERSON>",
        "employee_email": "<EMAIL>",
        "start_date": "2025-07-02"
    }
    
    response = requests.post(
        f"{base_url}/simple-onboarding/simple-onboard",
        json=frontend_data,
        headers=headers
    )
    
    print(f"Status Code: {response.status_code}")
    
    if response.status_code == 200:
        result = response.json()
        print("✅ Frontend integration successful!")
        print(f"Employee ID: {result['employee_id']}")
        print(f"Employee Name: {result['employee_name']}")
        print(f"Email: {result['email']}")
        print(f"Temporary Password: {result['temporary_password']}")
        print(f"Message: {result['message']}")
        
        # Format the success message like the frontend would show
        success_message = f"""Employee onboarded successfully!

Employee ID: {result['employee_id']}
Login Details:
Email: {result['email']}
Password: {result['temporary_password']}

{result['message']}"""
        
        print("\n📱 Frontend Success Message:")
        print("-" * 40)
        print(success_message)
        
    elif response.status_code == 400:
        error_detail = response.json().get('detail', 'Unknown error')
        if 'already exists' in error_detail:
            print("⚠️  Employee with this email already exists")
            print("This is expected if you've already tested with this email")
        else:
            print(f"❌ Bad request: {error_detail}")
    else:
        print(f"❌ Frontend integration failed: {response.status_code}")
        print("Response:", response.text)
    
    # Step 3: Test onboarding endpoints to verify they work
    print("\n3. Testing onboarding endpoints...")
    
    endpoints = [
        "/onboarding/templates",
        "/onboarding/onboarding"
    ]
    
    for endpoint in endpoints:
        try:
            resp = requests.get(f"{base_url}{endpoint}", headers=headers)
            status = "✅ Working" if resp.status_code == 200 else f"❌ {resp.status_code}"
            print(f"   {endpoint}: {status}")
        except Exception as e:
            print(f"   {endpoint}: ❌ Error - {e}")
    
    print("\n🎯 Integration Test Summary")
    print("=" * 50)
    print("✅ Simple onboarding API is ready for frontend use!")
    print("✅ The Quick Onboard button should work in the browser")
    print("✅ Employee creation and credential generation working")
    print("\n📋 Next Steps:")
    print("1. Open http://localhost:5173 in your browser")
    print("2. Navigate to the Onboarding page")
    print("3. Click 'Quick Onboard' (green button)")
    print("4. Enter employee name and email")
    print("5. Click 'Start Onboarding'")
    print("6. You should see a success message with login credentials")

if __name__ == "__main__":
    test_frontend_integration()
