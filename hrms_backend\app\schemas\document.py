from pydantic import BaseModel, Field, validator
from typing import Optional, List
from uuid import UUID
from datetime import datetime
from enum import Enum


class DocumentType(str, Enum):
    LEAVE_POLICY = "leave_policy"
    HOLIDAY_LIST = "holiday_list"
    EMPLOYEE_HANDBOOK = "employee_handbook"
    FORM_TEMPLATE = "form_template"
    CERTIFICATE = "certificate"
    ATTACHMENT = "attachment"
    OTHER = "other"


class DocumentStatus(str, Enum):
    DRAFT = "draft"
    ACTIVE = "active"
    ARCHIVED = "archived"
    DELETED = "deleted"


class AccessType(str, Enum):
    VIEW = "view"
    DOWNLOAD = "download"
    EDIT = "edit"


# Base schemas
class DocumentBase(BaseModel):
    name: str = Field(..., min_length=1, max_length=255)
    description: Optional[str] = None
    document_type: DocumentType
    tags: Optional[List[str]] = None
    is_public: bool = False
    allowed_roles: Optional[List[str]] = None
    allowed_departments: Optional[List[UUID]] = None
    valid_from: Optional[datetime] = None
    valid_until: Optional[datetime] = None

    @validator('valid_until')
    def valid_until_must_be_after_valid_from(cls, v, values):
        if v and 'valid_from' in values and values['valid_from']:
            if v <= values['valid_from']:
                raise ValueError('valid_until must be after valid_from')
        return v


class DocumentCreate(DocumentBase):
    file_name: str = Field(..., min_length=1, max_length=255)
    mime_type: str = Field(..., min_length=1, max_length=100)
    file_size: int = Field(..., gt=0)


class DocumentUpdate(BaseModel):
    name: Optional[str] = Field(None, min_length=1, max_length=255)
    description: Optional[str] = None
    status: Optional[DocumentStatus] = None
    tags: Optional[List[str]] = None
    is_public: Optional[bool] = None
    allowed_roles: Optional[List[str]] = None
    allowed_departments: Optional[List[UUID]] = None
    valid_from: Optional[datetime] = None
    valid_until: Optional[datetime] = None


class DocumentResponse(DocumentBase):
    id: UUID
    status: DocumentStatus
    file_name: str
    file_path: str
    file_size: int
    file_size_mb: float
    mime_type: str
    version: str
    parent_document_id: Optional[UUID] = None
    is_latest_version: bool
    uploaded_by: UUID
    is_expired: bool
    created_at: datetime
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True


class DocumentListResponse(BaseModel):
    documents: List[DocumentResponse]
    total: int
    skip: int
    limit: int


# Document Upload
class DocumentUploadRequest(BaseModel):
    name: str = Field(..., min_length=1, max_length=255)
    description: Optional[str] = None
    document_type: DocumentType
    tags: Optional[List[str]] = None
    is_public: bool = False
    allowed_roles: Optional[List[str]] = None
    allowed_departments: Optional[List[UUID]] = None
    valid_from: Optional[datetime] = None
    valid_until: Optional[datetime] = None


class DocumentUploadResponse(BaseModel):
    document_id: UUID
    upload_url: str
    expires_at: datetime


# Document Access
class DocumentAccessCreate(BaseModel):
    access_type: AccessType
    ip_address: Optional[str] = None
    user_agent: Optional[str] = None


class DocumentAccessResponse(BaseModel):
    id: UUID
    document_id: UUID
    user_id: UUID
    access_type: str
    ip_address: Optional[str] = None
    accessed_at: datetime

    class Config:
        from_attributes = True


# Document Sharing
class DocumentShareCreate(BaseModel):
    shared_with: Optional[UUID] = None
    shared_with_role: Optional[str] = None
    shared_with_department: Optional[UUID] = None
    can_view: bool = True
    can_download: bool = False
    can_edit: bool = False
    can_share: bool = False
    expires_at: Optional[datetime] = None

    @validator('shared_with_role')
    def validate_sharing_target(cls, v, values):
        shared_with = values.get('shared_with')
        shared_with_department = values.get('shared_with_department')
        
        # Must specify at least one sharing target
        if not any([shared_with, v, shared_with_department]):
            raise ValueError('Must specify at least one sharing target')
        return v


class DocumentShareResponse(BaseModel):
    id: UUID
    document_id: UUID
    shared_by: UUID
    shared_with: Optional[UUID] = None
    shared_with_role: Optional[str] = None
    shared_with_department: Optional[UUID] = None
    can_view: bool
    can_download: bool
    can_edit: bool
    can_share: bool
    expires_at: Optional[datetime] = None
    is_active: bool
    created_at: datetime

    class Config:
        from_attributes = True


# Document Templates
class DocumentTemplateBase(BaseModel):
    name: str = Field(..., min_length=1, max_length=255)
    description: Optional[str] = None
    template_type: str = Field(..., min_length=1, max_length=50)
    content: str = Field(..., min_length=1)
    variables: Optional[dict] = None
    is_default: bool = False


class DocumentTemplateCreate(DocumentTemplateBase):
    pass


class DocumentTemplateUpdate(BaseModel):
    name: Optional[str] = Field(None, min_length=1, max_length=255)
    description: Optional[str] = None
    content: Optional[str] = Field(None, min_length=1)
    variables: Optional[dict] = None
    is_default: Optional[bool] = None
    is_active: Optional[bool] = None


class DocumentTemplateResponse(DocumentTemplateBase):
    id: UUID
    version: str
    is_active: bool
    created_at: datetime
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True


# Document Version
class DocumentVersionResponse(BaseModel):
    id: UUID
    version: str
    file_name: str
    file_size: int
    uploaded_by: UUID
    created_at: datetime
    is_latest_version: bool

    class Config:
        from_attributes = True


class DocumentVersionListResponse(BaseModel):
    versions: List[DocumentVersionResponse]
    total: int
