"""create payroll history table

Revision ID: 7283cbbb5efc
Revises: f102088bfe88
Create Date: 2024-10-30 20:59:16.159166

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy import Column, Integer, String, Float
from sqlalchemy import func, DateTime, ForeignKey
from sqlalchemy.dialects.postgresql import BOOLEAN

# revision identifiers, used by Alembic.
revision: str = '7283cbbb5efc'
down_revision: Union[str, None] = 'f102088bfe88'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    op.create_table(
        'payroll_history',
        Column('id', Integer, primary_key=True, autoincrement=True),
        Column('first_name', String(length=45), nullable=True),
        Column('last_name', String(length=45), nullable=True),
        Column('email', String(length=100), nullable=True),
        Column('hire_date', String(length=50), nullable=True),
        Column('dob', String(length=50), nullable=True),
        Column('department_id', Integer, ForeignKey('departments.id'), nullable=True),
        Column('designation_id', Integer, ForeignKey('designations.id'), nullable=True),
        Column('user_id', Integer, ForeignKey('users.id'), nullable=True),
        Column('organisation_id', Integer, ForeignKey('organisations.id'), nullable=True),
        Column('template_id', Integer, ForeignKey('salary_templates.id'), nullable=True),
        Column('business_unit', String(length=50), nullable=True),
        Column('division', String(length=50), nullable=True),
        Column('taxID', String(length=45), nullable=True),
        Column('employee_id', Integer, ForeignKey('employees.id'), nullable=False),
        Column('employee_type', String(length=45), nullable=True),
        Column('status', String(length=45), nullable=True),
        Column('role', String(length=45), nullable=True),
        Column('employment_type', String(length=45), nullable=True),
        Column('bank_name', String(length=45), nullable=True),
        Column('bank_account', String(length=45), nullable=True),
        Column('salary_type', String(length=45), nullable=True),
        Column('rate', String(length=45), nullable=True),
        Column('hours_worked', String(length=45), nullable=True),
        Column('number_of_days_worked', Integer, nullable=True),
        Column('sort_code', String(length=45), nullable=True),
        Column('address', String(length=45), nullable=True),
        Column('country', String(length=45), nullable=True),
        Column('state', String(length=45), nullable=True),
        Column('city', String(length=45), nullable=True),
        Column('zip_code', String(length=45), nullable=True),
        Column('phone', String(length=45), nullable=True),
        Column('level', String(length=45), nullable=True),
        Column('tax_type', String(length=45), nullable=True),
        Column('currency', String(length=45), nullable=True),
        Column('work_schedule', String(length=45), nullable=True),
        Column('gross_pay', Float, nullable=True),
        Column('monthly_tax', Float, nullable=True),
        Column('annual_tax', Float, nullable=True),
        Column('total_taxable_monthly_sum', Float, nullable=True),
        Column('total_taxable_annual_sum', Float, nullable=True),
        Column('total_non_taxable_monthly_sum', Float, nullable=True),
        Column('total_non_taxable_annual_sum', Float, nullable=True),
        Column('total_statutory_monthly_sum', Float, nullable=True),
        Column('total_statutory_annual_sum', Float, nullable=True),
        Column('total_other_deductions_monthly_sum', Float, nullable=True),
        Column('total_other_deductions_annual_sum', Float, nullable=True),
        Column('netpay', Float, nullable=True),
        Column('is_prorated', BOOLEAN, nullable=True, default=True),
        Column('is_processed', BOOLEAN, nullable=True, default=False),
        #Column('is_processed_created',String(length=250), nullable=True),
        Column('message', String(length=250), nullable=True),
        Column('paymentId', String(length=45), nullable=True),
        Column('payment_status', String(length=45), nullable=True),
         # Newly added columns
        Column('gender', String(length=100), nullable=True),
        Column('source_tag', String(length=45), nullable=True),
        Column('pension_no', String, nullable=True),
        Column('pension_pfa', String, nullable=True),
        Column('pfa_name', String, nullable=True),
        Column('pfa_number', String, nullable=True),
        Column('nhf_no', String, nullable=True),
        Column('nhf_mortgage_bank', String(length=45), nullable=True),
        Column('tax_state', String, nullable=True),
        Column('pfc_name', String, nullable=True),
        Column('pfc_account_number', String, nullable=True),
        Column('annual_leave_days', String(length=45), nullable=True),
        Column('unpaid_leave_days', String(length=45), nullable=True),
        Column('sick_leave_days', String(length=45), nullable=True),
        Column('maternity_paternity_leave_days', String(length=45), nullable=True),
        Column('casual_leave_days', String(length=45), nullable=True),
        Column('compassionate_leave_days', String(length=45), nullable=True),
        Column('total_working_days', String(length=45), nullable=True),
        Column('total_present_days', String(length=45), nullable=True),
        Column('total_absent_days', String(length=45), nullable=True),
        Column('employeeID', String(length=45), nullable=True),
        Column('transaction_id', String(length=250), nullable=True),
        Column('transaction_date', String(length=250), nullable=True),
        Column('created_at', DateTime, nullable=False, server_default=sa.func.now()),
        Column('updated_at', DateTime, nullable=True, onupdate=sa.func.now())
    )

def downgrade() -> None:
    op.drop_table('payroll_history')