"""
Enhanced error handling for the HRMS system
"""

import logging
import traceback
from typing import Dict, Any, Optional
from fastapi import Request, HTTPException, status
from fastapi.responses import J<PERSON><PERSON>esponse
from fastapi.exceptions import RequestValidationError
from sqlalchemy.exc import SQLAlchemyError, IntegrityError
from pydantic import ValidationError
import uuid

logger = logging.getLogger(__name__)


class ErrorCode:
    """Standard error codes for the HRMS system"""
    
    # Authentication & Authorization
    AUTHENTICATION_FAILED = "AUTH_001"
    INVALID_TOKEN = "AUTH_002"
    TOKEN_EXPIRED = "AUTH_003"
    INSUFFICIENT_PERMISSIONS = "AUTH_004"
    
    # Database Errors
    DATABASE_CONNECTION_ERROR = "DB_001"
    DATABASE_CONSTRAINT_VIOLATION = "DB_002"
    DATABASE_INTEGRITY_ERROR = "DB_003"
    RECORD_NOT_FOUND = "DB_004"
    DUPLICATE_RECORD = "DB_005"
    
    # Validation Errors
    INVALID_INPUT_DATA = "VAL_001"
    MISSING_REQUIRED_FIELD = "VAL_002"
    INVALID_DATE_RANGE = "VAL_003"
    INVALID_FILE_FORMAT = "VAL_004"
    
    # Business Logic Errors
    EMPLOYEE_NOT_FOUND = "EMP_001"
    EMPLOYEE_ALREADY_EXISTS = "EMP_002"
    INVALID_EMPLOYEE_STATUS = "EMP_003"
    
    ATTENDANCE_ALREADY_CHECKED_IN = "ATT_001"
    ATTENDANCE_NOT_CHECKED_IN = "ATT_002"
    INVALID_ATTENDANCE_TIME = "ATT_003"
    
    TIMESHEET_ALREADY_SUBMITTED = "TS_001"
    TIMESHEET_PERIOD_CLOSED = "TS_002"
    INVALID_TIMESHEET_HOURS = "TS_003"
    
    LEAVE_INSUFFICIENT_BALANCE = "LV_001"
    LEAVE_OVERLAPPING_DATES = "LV_002"
    LEAVE_INVALID_DATES = "LV_003"
    
    PROJECT_NOT_FOUND = "PRJ_001"
    PROJECT_ACCESS_DENIED = "PRJ_002"
    
    # System Errors
    EXTERNAL_SERVICE_ERROR = "SYS_001"
    CACHE_ERROR = "SYS_002"
    FILE_UPLOAD_ERROR = "SYS_003"
    EMAIL_SEND_ERROR = "SYS_004"


class HRMSException(Exception):
    """Base exception class for HRMS-specific errors"""
    
    def __init__(
        self,
        message: str,
        error_code: str,
        status_code: int = 400,
        details: Optional[Dict[str, Any]] = None
    ):
        self.message = message
        self.error_code = error_code
        self.status_code = status_code
        self.details = details or {}
        super().__init__(message)


class AuthenticationError(HRMSException):
    """Authentication-related errors"""
    
    def __init__(self, message: str = "Authentication failed", details: Optional[Dict[str, Any]] = None):
        super().__init__(
            message=message,
            error_code=ErrorCode.AUTHENTICATION_FAILED,
            status_code=status.HTTP_401_UNAUTHORIZED,
            details=details
        )


class AuthorizationError(HRMSException):
    """Authorization-related errors"""
    
    def __init__(self, message: str = "Insufficient permissions", details: Optional[Dict[str, Any]] = None):
        super().__init__(
            message=message,
            error_code=ErrorCode.INSUFFICIENT_PERMISSIONS,
            status_code=status.HTTP_403_FORBIDDEN,
            details=details
        )


class ValidationError(HRMSException):
    """Validation-related errors"""
    
    def __init__(self, message: str, details: Optional[Dict[str, Any]] = None):
        super().__init__(
            message=message,
            error_code=ErrorCode.INVALID_INPUT_DATA,
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
            details=details
        )


class BusinessLogicError(HRMSException):
    """Business logic-related errors"""
    
    def __init__(self, message: str, error_code: str, details: Optional[Dict[str, Any]] = None):
        super().__init__(
            message=message,
            error_code=error_code,
            status_code=status.HTTP_400_BAD_REQUEST,
            details=details
        )


class DatabaseError(HRMSException):
    """Database-related errors"""
    
    def __init__(self, message: str, error_code: str = ErrorCode.DATABASE_CONNECTION_ERROR, details: Optional[Dict[str, Any]] = None):
        super().__init__(
            message=message,
            error_code=error_code,
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            details=details
        )


def create_error_response(
    error_id: str,
    message: str,
    error_code: str,
    status_code: int,
    details: Optional[Dict[str, Any]] = None,
    request_path: Optional[str] = None
) -> Dict[str, Any]:
    """Create standardized error response"""
    
    error_response = {
        "error": {
            "id": error_id,
            "code": error_code,
            "message": message,
            "timestamp": "now()",
            "path": request_path
        }
    }
    
    if details:
        error_response["error"]["details"] = details
    
    return error_response


async def hrms_exception_handler(request: Request, exc: HRMSException) -> JSONResponse:
    """Handle HRMS-specific exceptions"""
    
    error_id = str(uuid.uuid4())
    
    logger.error(
        f"HRMS Exception [{error_id}]: {exc.error_code} - {exc.message}",
        extra={
            "error_id": error_id,
            "error_code": exc.error_code,
            "status_code": exc.status_code,
            "path": str(request.url),
            "details": exc.details
        }
    )
    
    error_response = create_error_response(
        error_id=error_id,
        message=exc.message,
        error_code=exc.error_code,
        status_code=exc.status_code,
        details=exc.details,
        request_path=str(request.url)
    )
    
    return JSONResponse(
        status_code=exc.status_code,
        content=error_response
    )


async def http_exception_handler(request: Request, exc: HTTPException) -> JSONResponse:
    """Handle FastAPI HTTP exceptions"""
    
    error_id = str(uuid.uuid4())
    
    # Map common HTTP status codes to error codes
    error_code_mapping = {
        401: ErrorCode.AUTHENTICATION_FAILED,
        403: ErrorCode.INSUFFICIENT_PERMISSIONS,
        404: ErrorCode.RECORD_NOT_FOUND,
        422: ErrorCode.INVALID_INPUT_DATA,
        500: ErrorCode.DATABASE_CONNECTION_ERROR
    }
    
    error_code = error_code_mapping.get(exc.status_code, "HTTP_ERROR")
    
    logger.error(
        f"HTTP Exception [{error_id}]: {exc.status_code} - {exc.detail}",
        extra={
            "error_id": error_id,
            "status_code": exc.status_code,
            "path": str(request.url)
        }
    )
    
    error_response = create_error_response(
        error_id=error_id,
        message=str(exc.detail),
        error_code=error_code,
        status_code=exc.status_code,
        request_path=str(request.url)
    )
    
    return JSONResponse(
        status_code=exc.status_code,
        content=error_response
    )


async def validation_exception_handler(request: Request, exc: RequestValidationError) -> JSONResponse:
    """Handle Pydantic validation exceptions"""
    
    error_id = str(uuid.uuid4())
    
    # Extract validation error details
    validation_errors = []
    for error in exc.errors():
        validation_errors.append({
            "field": " -> ".join(str(loc) for loc in error["loc"]),
            "message": error["msg"],
            "type": error["type"]
        })
    
    logger.error(
        f"Validation Exception [{error_id}]: {len(validation_errors)} validation errors",
        extra={
            "error_id": error_id,
            "path": str(request.url),
            "validation_errors": validation_errors
        }
    )
    
    error_response = create_error_response(
        error_id=error_id,
        message="Validation failed",
        error_code=ErrorCode.INVALID_INPUT_DATA,
        status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
        details={"validation_errors": validation_errors},
        request_path=str(request.url)
    )
    
    return JSONResponse(
        status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
        content=error_response
    )


async def sqlalchemy_exception_handler(request: Request, exc: SQLAlchemyError) -> JSONResponse:
    """Handle SQLAlchemy database exceptions"""
    
    error_id = str(uuid.uuid4())
    
    # Determine specific error type
    if isinstance(exc, IntegrityError):
        error_code = ErrorCode.DATABASE_INTEGRITY_ERROR
        message = "Database integrity constraint violation"
    else:
        error_code = ErrorCode.DATABASE_CONNECTION_ERROR
        message = "Database operation failed"
    
    logger.error(
        f"Database Exception [{error_id}]: {type(exc).__name__} - {str(exc)}",
        extra={
            "error_id": error_id,
            "path": str(request.url),
            "exception_type": type(exc).__name__
        }
    )
    
    error_response = create_error_response(
        error_id=error_id,
        message=message,
        error_code=error_code,
        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
        request_path=str(request.url)
    )
    
    return JSONResponse(
        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
        content=error_response
    )


async def general_exception_handler(request: Request, exc: Exception) -> JSONResponse:
    """Handle all other exceptions"""
    
    error_id = str(uuid.uuid4())
    
    logger.error(
        f"Unhandled Exception [{error_id}]: {type(exc).__name__} - {str(exc)}",
        extra={
            "error_id": error_id,
            "path": str(request.url),
            "exception_type": type(exc).__name__,
            "traceback": traceback.format_exc()
        }
    )
    
    error_response = create_error_response(
        error_id=error_id,
        message="An unexpected error occurred",
        error_code="INTERNAL_ERROR",
        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
        request_path=str(request.url)
    )
    
    return JSONResponse(
        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
        content=error_response
    )


# Export all error classes and handlers
__all__ = [
    "ErrorCode",
    "HRMSException",
    "AuthenticationError",
    "AuthorizationError",
    "ValidationError",
    "BusinessLogicError",
    "DatabaseError",
    "hrms_exception_handler",
    "http_exception_handler",
    "validation_exception_handler",
    "sqlalchemy_exception_handler",
    "general_exception_handler"
]
