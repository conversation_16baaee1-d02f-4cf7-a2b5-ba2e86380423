# Ticket Management System - HRMS Backend

## Overview

The HRMS Ticket Management System provides comprehensive support for employee requests, IT support, HR queries, and various organizational needs. It includes advanced features like workflow automation, SLA management, escalation handling, and omnichannel support.

## Key Features

### ✅ Core Functionality
- **Ticket Creation & Tracking**: Complete ticket lifecycle management
- **Auto-assignment**: Intelligent ticket routing based on rules
- **SLA Management**: Service level agreement tracking and monitoring
- **Resolution Tracking**: Complete ticket resolution workflow
- **Priority Management**: 5-level priority system (Low, Medium, High, Urgent, Critical)
- **Status Management**: 6 status types (Open, In Progress, Pending, Resolved, Closed, Cancelled)

### ✅ Advanced Features
- **Workflow Automation**: Configurable automated workflows
- **Escalation Management**: Automatic and manual escalation handling
- **Template System**: Reusable ticket templates for common requests
- **Bulk Operations**: Mass update multiple tickets
- **Satisfaction Surveys**: Customer satisfaction rating system
- **Knowledge Base Integration**: Search and suggest relevant articles
- **Omnichannel Support**: Email, phone, web portal, chat, mobile app
- **Reporting & Analytics**: Comprehensive reporting and metrics

### ✅ Role-Based Access Control

#### SuperAdmin
- Full access to all ticket management features
- Can manage tickets, view reports, handle escalations
- Access to system-wide ticket configurations
- SLA compliance monitoring across all organizations

#### Admin
- Manage tickets within organization
- View reports and handle escalations
- Assign tickets to teams and monitor workflows
- SLA compliance for organizational tickets
- Cannot access super admin functions

#### HR
- Handle employee requests and HR-related tickets
- Create and manage tickets for onboarding, training, support
- Use workflows for resolution and receive notifications
- Prioritize tickets related to HR tasks
- Limited to HR-related ticket configurations

#### Manager
- View and manage team-specific tickets
- Approve leave requests and resolve team IT issues
- Assign tickets within team and monitor workflows
- Receive notifications for team-related tickets
- Access restricted to team-level tickets only

#### Employee
- Create tickets for self-service needs (leave, IT support, etc.)
- View status updates and track personal requests
- Receive notifications about ticket progress
- Submit satisfaction ratings for resolved tickets
- No access to management or administrative features

## API Endpoints

### Core Ticket Operations

#### Get Tickets
```http
GET /api/ticket/
```
**Query Parameters:**
- `status`: Filter by ticket status
- `priority`: Filter by priority level
- `ticket_type`: Filter by ticket type
- `assigned_to`: Filter by assignee
- `requester_id`: Filter by requester
- `search`: Search in title and description
- `skip`: Pagination offset
- `limit`: Number of results (max 100)

**Permissions Required:** `TICKET_READ`

#### Create Ticket
```http
POST /api/ticket/
```
**Request Body:**
```json
{
  "title": "IT Support Request",
  "description": "Need help with password reset",
  "ticket_type": "it_support",
  "priority": "medium",
  "category": "Password Issues",
  "contact_method": "email",
  "contact_details": "<EMAIL>",
  "department_id": "uuid",
  "attachment_urls": ["url1", "url2"]
}
```
**Permissions Required:** `TICKET_CREATE`

#### Update Ticket
```http
PUT /api/ticket/{ticket_id}
```
**Permissions Required:** `TICKET_UPDATE`

#### Assign Ticket
```http
PUT /api/ticket/{ticket_id}/assign
```
**Request Body:**
```json
{
  "assigned_to": "uuid",
  "assignment_notes": "Assigning to specialist"
}
```
**Permissions Required:** `TICKET_ASSIGN`

#### Resolve Ticket
```http
PUT /api/ticket/{ticket_id}/resolve
```
**Request Body:**
```json
{
  "resolution": "Issue resolved by password reset",
  "resolution_category": "Password Reset",
  "notify_requester": true
}
```
**Permissions Required:** `TICKET_RESOLVE`

### Personal Ticket Operations

#### Get My Tickets
```http
GET /api/ticket/my/tickets
```
**Description:** Get tickets created by current user
**Permissions Required:** Authenticated user

#### Get Assigned Tickets
```http
GET /api/ticket/assigned/tickets
```
**Description:** Get tickets assigned to current user
**Permissions Required:** Authenticated user

### Comments & Communication

#### Add Comment
```http
POST /api/ticket/{ticket_id}/comments
```
**Request Body:**
```json
{
  "content": "Comment text",
  "is_internal": false,
  "is_solution": false,
  "time_spent_minutes": 30,
  "attachment_urls": ["url1"]
}
```

#### Get Comments
```http
GET /api/ticket/{ticket_id}/comments
```

### Escalation Management

#### Escalate Ticket
```http
POST /api/ticket/{ticket_id}/escalate
```
**Request Body:**
```json
{
  "escalated_to": "uuid",
  "reason": "SLA breach - requires immediate attention"
}
```

#### Get Escalations
```http
GET /api/ticket/escalations
```

#### Acknowledge Escalation
```http
PUT /api/ticket/escalations/{escalation_id}/acknowledge
```

#### Resolve Escalation
```http
PUT /api/ticket/escalations/{escalation_id}/resolve
```

### Template Management

#### Get Templates
```http
GET /api/ticket/templates
```

#### Create Template
```http
POST /api/ticket/templates
```
**Request Body:**
```json
{
  "name": "Password Reset Request",
  "description": "Template for password reset requests",
  "ticket_type": "it_support",
  "default_title": "Password Reset - {{user_name}}",
  "default_description": "User {{user_name}} needs password reset",
  "default_priority": "medium",
  "custom_fields": [
    {
      "name": "system",
      "type": "select",
      "options": ["Windows", "Mac", "Linux"],
      "required": true
    }
  ],
  "is_public": true
}
```

#### Create Ticket from Template
```http
POST /api/ticket/templates/{template_id}/create-ticket
```

### SLA Management

#### Get SLAs
```http
GET /api/ticket/slas
```
**Permissions Required:** `ADMIN_SETTINGS`

#### Create SLA
```http
POST /api/ticket/slas
```
**Request Body:**
```json
{
  "name": "IT Support SLA",
  "ticket_types": ["it_support"],
  "priorities": ["high", "urgent", "critical"],
  "first_response_hours": 2,
  "resolution_hours": 24,
  "business_hours_only": true,
  "business_hours_start": "09:00",
  "business_hours_end": "17:00",
  "business_days": [0, 1, 2, 3, 4],
  "escalation_enabled": true,
  "escalation_hours": 4
}
```

### Workflow Management

#### Get Workflows
```http
GET /api/ticket/workflows
```

#### Create Workflow
```http
POST /api/ticket/workflows
```
**Request Body:**
```json
{
  "name": "IT Support Workflow",
  "description": "Automated workflow for IT support tickets",
  "trigger_conditions": {
    "events": ["created"],
    "ticket_types": ["it_support"],
    "priorities": ["high", "urgent"]
  },
  "workflow_steps": [
    {
      "type": "auto_assign",
      "assignment_rules": {
        "team": "IT Support"
      }
    },
    {
      "type": "notification",
      "notification": {
        "recipients": ["assignee"],
        "type": "email",
        "subject": "New IT Support Ticket Assigned"
      }
    }
  ]
}
```

### Advanced Features

#### Bulk Update Tickets
```http
POST /api/ticket/bulk-update
```
**Request Body:**
```json
{
  "ticket_ids": ["uuid1", "uuid2", "uuid3"],
  "update_data": {
    "status": "in_progress",
    "assigned_to": "uuid",
    "priority": "high"
  }
}
```

#### Submit Satisfaction Rating
```http
POST /api/ticket/{ticket_id}/satisfaction
```
**Query Parameters:**
- `rating`: Integer 1-5
- `feedback`: Optional feedback text

#### Search Knowledge Base
```http
GET /api/ticket/knowledge-base/search
```
**Query Parameters:**
- `query`: Search query (min 3 characters)
- `ticket_type`: Optional ticket type filter

### Reporting & Analytics

#### Ticket Overview Report
```http
GET /api/ticket/reports/overview
```

#### Ticket Trends Report
```http
GET /api/ticket/reports/trends
```
**Query Parameters:**
- `group_by`: hour|day|week|month

#### Agent Performance Report
```http
GET /api/ticket/reports/agent-performance
```

#### Satisfaction Metrics
```http
GET /api/ticket/reports/satisfaction
```

#### SLA Metrics
```http
GET /api/ticket/reports/sla-metrics
```

#### Escalation Report
```http
GET /api/ticket/reports/escalations
```

## Ticket Types

The system supports the following ticket types:

- `it_support` - IT Support requests
- `hr_query` - HR related queries
- `facilities` - Facilities management
- `payroll` - Payroll related issues
- `leave` - Leave requests
- `equipment` - Equipment requests
- `access_request` - System access requests
- `complaint` - Employee complaints
- `suggestion` - Suggestions and feedback
- `training_request` - Training requests
- `policy_clarification` - Policy questions
- `document_request` - Document requests
- `system_access` - System access issues
- `workspace_request` - Workspace requests
- `travel_request` - Travel requests
- `expense_query` - Expense related queries
- `benefits_query` - Benefits questions
- `performance_query` - Performance related queries
- `recruitment_support` - Recruitment support
- `onboarding_support` - Onboarding assistance
- `offboarding_support` - Offboarding assistance
- `other` - Other requests

## Priority Levels

- `low` - Low priority (72+ hours)
- `medium` - Medium priority (24-48 hours)
- `high` - High priority (8-24 hours)
- `urgent` - Urgent priority (2-8 hours)
- `critical` - Critical priority (< 2 hours)

## Status Workflow

1. **Open** - Newly created ticket
2. **In Progress** - Ticket being worked on
3. **Pending** - Waiting for information/approval
4. **Resolved** - Issue resolved, awaiting confirmation
5. **Closed** - Ticket completed and closed
6. **Cancelled** - Ticket cancelled/invalid

## Database Models

### Core Models
- `Ticket` - Main ticket entity
- `TicketComment` - Ticket comments and communications
- `TicketActivity` - Activity log and audit trail
- `TicketEscalation` - Escalation tracking
- `TicketNotification` - Notification management

### Configuration Models
- `TicketTemplate` - Reusable ticket templates
- `TicketSLA` - SLA configurations
- `TicketWorkflow` - Workflow automation rules
- `TicketCategory` - Ticket categorization

## Services Architecture

### Core Services
- `TicketService` - Main ticket operations
- `TicketWorkflowService` - Workflow automation
- `TicketSLAService` - SLA monitoring and management
- `TicketEscalationService` - Escalation handling
- `TicketTemplateService` - Template management
- `TicketReportingService` - Analytics and reporting

## Integration Points

### Notification System
- Email notifications for ticket updates
- SMS alerts for urgent tickets
- In-app notifications
- Push notifications for mobile apps

### External Systems
- Email-to-ticket conversion
- Chat system integration
- Phone system integration
- Mobile app API

### HRMS Integration
- Employee directory integration
- Department and role-based routing
- Leave management system integration
- Performance management integration

## Configuration

### Environment Variables
```env
# Ticket System Configuration
TICKET_AUTO_ASSIGNMENT_ENABLED=true
TICKET_SLA_MONITORING_ENABLED=true
TICKET_ESCALATION_ENABLED=true
TICKET_SATISFACTION_SURVEY_ENABLED=true

# Notification Settings
TICKET_EMAIL_NOTIFICATIONS=true
TICKET_SMS_NOTIFICATIONS=false
TICKET_PUSH_NOTIFICATIONS=true

# Integration Settings
TICKET_EMAIL_INTEGRATION_ENABLED=true
TICKET_CHAT_INTEGRATION_ENABLED=false
```

## Usage Examples

### Creating a Ticket with Template
```python
# 1. Get available templates
templates = await template_service.get_templates(db, current_user, ticket_type="it_support")

# 2. Create ticket from template
form_data = {
    "system": "Windows",
    "urgency": "high",
    "description": "Cannot access email system"
}
ticket = await template_service.create_ticket_from_template(
    db, template_id, form_data, current_user
)
```

### Setting up SLA Rules
```python
sla_data = {
    "name": "Critical IT Support SLA",
    "ticket_types": ["it_support"],
    "priorities": ["critical", "urgent"],
    "first_response_hours": 1,
    "resolution_hours": 4,
    "business_hours_only": False,
    "escalation_enabled": True,
    "escalation_hours": 2
}
sla = await sla_service.create_sla(db, sla_data, current_user)
```

### Workflow Automation
```python
workflow_data = {
    "name": "HR Query Auto-Assignment",
    "trigger_conditions": {
        "events": ["created"],
        "ticket_types": ["hr_query"]
    },
    "workflow_steps": [
        {
            "type": "auto_assign",
            "assignment_rules": {"team": "HR"}
        },
        {
            "type": "status_change",
            "new_status": "in_progress"
        }
    ]
}
workflow = await workflow_service.create_workflow(db, workflow_data, current_user)
```

## Monitoring & Maintenance

### Health Checks
- SLA breach monitoring
- Escalation queue monitoring
- Workflow execution monitoring
- Notification delivery monitoring

### Performance Metrics
- Average resolution time
- First response time
- Customer satisfaction scores
- Agent performance metrics
- SLA compliance rates

### Maintenance Tasks
- Archive old tickets
- Clean up notification logs
- Update knowledge base articles
- Review and update SLA rules
- Optimize workflow configurations

## Security Considerations

### Data Protection
- All ticket data is encrypted at rest
- Sensitive information is masked in logs
- Access controls based on RBAC
- Audit trail for all operations

### API Security
- JWT token authentication
- Rate limiting on API endpoints
- Input validation and sanitization
- SQL injection prevention

### Privacy Compliance
- GDPR compliance for EU users
- Data retention policies
- Right to be forgotten implementation
- Consent management for communications

## Troubleshooting

### Common Issues

#### Tickets Not Auto-Assigning
1. Check workflow configurations
2. Verify assignment rules
3. Ensure target agents are active
4. Review organization settings

#### SLA Breaches Not Detected
1. Verify SLA configurations
2. Check business hours settings
3. Ensure SLA monitoring is enabled
4. Review ticket priority mappings

#### Notifications Not Sending
1. Check notification service status
2. Verify recipient email addresses
3. Review notification templates
4. Check rate limiting settings

### Logs and Debugging
- Application logs: `/var/log/hrms/ticket_management.log`
- Error logs: `/var/log/hrms/errors.log`
- Audit logs: Database table `audit_logs`
- Performance logs: `/var/log/hrms/performance.log`

## Support and Documentation

For additional support and documentation:
- API Documentation: `/docs` endpoint
- Database Schema: `docs/database_schema.md`
- Deployment Guide: `docs/deployment.md`
- Configuration Reference: `docs/configuration.md`

---

**Last Updated:** 2024-01-01  
**Version:** 1.0.0  
**Maintainer:** HRMS Development Team
