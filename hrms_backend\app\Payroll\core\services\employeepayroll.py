from core.repositories.employeepayroll import EmployeePayrollRepository

class EmployeePayrollService:
    def __init__(self) -> None:
        self.repository = EmployeePayrollRepository()

    def createEmployeePay(self, Kwargs):
        # print(Kwargs)
        return self.repository.createEmployeePayroll(**Kwargs)
    
    def getEmployeePay(self, id):
        return self.repository.getEmployeePayroll(id)
    
    def updateEmployeePay(self, id, **Kwargs):
        return self.repository.updateEmployeePayroll(id, **Kwargs)
    
    def getEmployeePayByKey(self, Kwarg):
        return self.repository.getEmployeePayrollByKeys(Kwarg)
    
    def deleteEmployeePay(self, id):
        return self.repository.deleteEmployeePayroll(id)