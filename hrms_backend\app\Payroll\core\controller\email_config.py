from flask import url_for, jsonify
from flask.views import <PERSON><PERSON>ie<PERSON>
from flask_smorest import Blueprint, abort
from core.middleware import roles_required
from schemas import EmailConfigSchema, EmailConfigUpdateSchema
from sqlalchemy.orm.exc import NoResultFound
from sqlalchemy.exc import SQLAlchemyError, IntegrityError

import core.utils.response_message as RESPONSEMESSAGE
from core.services.email_config import EmailConfig
from core.utils.responseBuilder import ResponseBuilder

blueprint = Blueprint("email_config", __name__, description="Operations for email operation")
    
@blueprint.route("/email_config")
class EmailSettings(MethodView):
    @roles_required(['admin'])
    @blueprint.response(200, EmailConfigSchema)
    def get(self):
        service = EmailConfig()
        configuration = service.getConfig()
        if configuration is None:
            abort(404, message="No configuration set for this user")

        config_details = EmailConfigSchema().dump(configuration)
        return ResponseBuilder(
            data=config_details, 
            status_code=200, 
            total=None,meta=None, 
            message="Email Configuration retrived successfully"
        ).build() 
    
    @roles_required(['admin'])
    def delete(self):    
        service = EmailConfig()
        configuration = service.deleteConfig()
        if configuration is None:
            abort(404, message="No configuration set for this user")
            
        return ResponseBuilder(
            data=None, 
            status_code=201, 
            total=None,meta=None, 
            message="Email Configuration deleted successfully"
        ).build()
    
    @roles_required(['admin'])
    @blueprint.arguments(EmailConfigSchema)
    @blueprint.response(201, EmailConfigSchema)
    def put(self, data, id):
        service = EmailConfig()
        get_config = service.getConfig()
        if get_config is None:
            abort(404, message="No configuration set for this user")
        try :
            new_config = service.updateConfig(get_config.id, data)
            config_schema = EmailConfigUpdateSchema().dump(new_config)
            return ResponseBuilder(
                data=config_schema, 
                status_code=200,
                total=None,meta=None, 
                message="Email Configuration updated successfully"
            ).build()    
        except SQLAlchemyError as e:
                abort(500, message=f"Error while updating Loan Request, {str(e)}")
    
    @roles_required(['admin'])
    @blueprint.arguments(EmailConfigSchema)
    @blueprint.response(200, EmailConfigSchema)
    def post(self, data):
        service = EmailConfig()
        config_schema = EmailConfigSchema()
        email_config = service.getConfig()

        try:
            if email_config is not None:
                updated_config = service.updateConfig(email_config.id, data)
                response_config = updated_config
                message = "Email configuration updated successfully"
            else:
                new_config = service.createConfig(data)
                response_config = new_config
                message = "Email configuration created successfully"

            return ResponseBuilder(
                data=config_schema.dump(response_config), 
                status_code=201, 
                message=message
            ).build()

        except IntegrityError:
            abort(409, message="Configuration already exists")
        except SQLAlchemyError:
            abort(500, message="Database error occurred")