import { useState, useRef } from "react";
import { Camera, Upload } from "lucide-react";
import { defaultImages } from "../utils/defaultImages";

export default function ProfileSection() {
  const [isCheckedIn, setIsCheckedIn] = useState(false);
  const [checkInTime, setCheckInTime] = useState(null);
  const [elapsedTime, setElapsedTime] = useState("00:00:00");
  const [profileImage, setProfileImage] = useState(defaultImages.avatar);
  const fileInputRef = useRef(null);

  // Update elapsed time
  const updateElapsedTime = () => {
    if (!checkInTime) return;
    
    const now = new Date();
    const diff = now - checkInTime;
    const hours = Math.floor(diff / (1000 * 60 * 60)).toString().padStart(2, "0");
    const minutes = Math.floor((diff / (1000 * 60)) % 60).toString().padStart(2, "0");
    const seconds = Math.floor((diff / 1000) % 60).toString().padStart(2, "0");
    
    setElapsedTime(`${hours}:${minutes}:${seconds}`);
  };

  // Handle profile image upload
  const handleImageUpload = (event) => {
    const file = event.target.files[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        setProfileImage(e.target.result);
      };
      reader.readAsDataURL(file);
    }
  };

  // Handle check-in/out
  const handleCheckInOut = () => {
    if (isCheckedIn) {
      // Check out
      setIsCheckedIn(false);
      setCheckInTime(null);
      setElapsedTime("00:00:00");
    } else {
      // Check in
      setIsCheckedIn(true);
      setCheckInTime(new Date());

      // Start timer
      const timer = setInterval(() => {
        updateElapsedTime();
      }, 1000);

      // Store timer ID in a ref to clear it later
      return () => clearInterval(timer);
    }
  };

  return (
    <div className="bg-white rounded-lg shadow-sm p-4">
      <div className="flex justify-center mb-4">
        <div className="relative w-20 h-20 rounded-lg overflow-hidden group">
          <img
            src={profileImage}
            alt="Profile"
            className="w-full h-full object-cover"
          />

          {/* Upload overlay */}
          <div
            className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity cursor-pointer"
            onClick={() => fileInputRef.current?.click()}
          >
            <Camera className="text-white" size={20} />
          </div>

          {/* Hidden file input */}
          <input
            ref={fileInputRef}
            type="file"
            accept="image/*"
            onChange={handleImageUpload}
            className="hidden"
          />
        </div>
      </div>
      
      <div className="text-center mb-4">
        <div className={`font-medium ${isCheckedIn ? "text-green-500" : "text-red-500"}`}>
          {isCheckedIn ? "In" : "Out"}
        </div>
        <div className="text-2xl font-bold mt-1">
          {elapsedTime}
        </div>
      </div>
      
      <div className="flex justify-center mb-6">
        <button
          className={`px-4 py-2 border rounded-md transition-colors ${
            isCheckedIn
              ? "border-red-500 text-red-500 hover:bg-red-50"
              : "agno-bg-orange text-white hover:bg-accent-600 border-transparent"
          }`}
          onClick={handleCheckInOut}
        >
          {isCheckedIn ? "Check-out" : "Check-in"}
        </button>
      </div>
      
      <div className="border-t pt-4">
        <h3 className="font-medium mb-3">Reportees</h3>
        
        <div className="space-y-4">
          <ReporteeItem 
            id="S19" 
            name="Michael Johnson" 
            status="yet-to-check-in"
          />
          
          <ReporteeItem 
            id="S2" 
            name="Lily Williams" 
            status="yet-to-check-in"
          />
          
          <ReporteeItem 
            id="S20" 
            name="Christopher Brown" 
            status="yet-to-check-in"
          />
          
          <div className="text-blue-600 text-sm font-medium cursor-pointer hover:underline">
            +1 More
          </div>
        </div>
      </div>
    </div>
  );
}

// Reportee Item Component
function ReporteeItem({ id, name, status }) {
  const getStatusText = (status) => {
    switch (status) {
      case "checked-in": return "Checked in";
      case "checked-out": return "Checked out";
      case "on-leave": return "On leave";
      case "yet-to-check-in": 
      default: return "Yet to check-in";
    }
  };
  
  const getStatusColor = (status) => {
    switch (status) {
      case "checked-in": return "text-green-500";
      case "checked-out": return "text-blue-500";
      case "on-leave": return "text-orange-500";
      case "yet-to-check-in": 
      default: return "text-red-500";
    }
  };
  
  return (
    <div className="flex items-center gap-3">
      <div className="w-8 h-8 rounded-full overflow-hidden flex-shrink-0 bg-gradient-to-br from-gray-500 to-gray-700 flex items-center justify-center text-white text-xs font-medium">
        {name.split(" ").map(n => n[0]).join("")}
      </div>
      <div>
        <div className="flex items-center gap-2">
          <span className="text-gray-500 text-sm">{id}</span>
          <span className="font-medium">{name}</span>
        </div>
        <div className={`text-xs ${getStatusColor(status)}`}>
          {getStatusText(status)}
        </div>
      </div>
    </div>
  );
}