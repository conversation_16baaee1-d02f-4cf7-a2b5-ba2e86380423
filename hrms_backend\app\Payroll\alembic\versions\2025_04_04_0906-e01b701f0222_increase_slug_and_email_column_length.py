"""Increase slug and email column length

Revision ID: e01b701f0222
Revises: 77c698a62cdd
Create Date: 2025-04-04 09:06:03.526257

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'e01b701f0222'
down_revision: Union[str, None] = '77c698a62cdd'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None

def upgrade():
    
    op.alter_column(
        'organisations', 
        'slug',
        existing_type=sa.VARCHAR(50),
        type_=sa.VARCHAR(255),  
        existing_nullable=False
    )

   
    op.alter_column(
        'organisations',
        'email',
        existing_type=sa.VARCHAR(50),
        type_=sa.VARCHAR(255),
        existing_nullable=False
    )


def downgrade():
   
    op.alter_column(
        'organisations', 
        'slug',
        existing_type=sa.VARCHAR(255),  
        type_=sa.VARCHAR(50),  
        existing_nullable=False
    )

    # Revert 'email' column length to 50
    op.alter_column(
        'organisations',
        'email',
        existing_type=sa.VARCHAR(255),  
        type_=sa.VARCHAR(50),  
        existing_nullable=False
    )
