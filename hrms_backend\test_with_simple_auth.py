#!/usr/bin/env python3
"""
Test with simple authentication bypass for development
"""

import requests
import json

def test_with_auth():
    """Test with authentication"""
    
    base_url = "http://localhost:8085"
    
    # Try to create a simple test user and login
    try:
        # First check if we can access any endpoint
        print("🔍 Testing basic connectivity...")
        response = requests.get(f"{base_url}/")
        print(f"Root endpoint status: {response.status_code}")
        
        # Try the auth endpoints
        print("\n🔍 Testing auth endpoints...")
        response = requests.get(f"{base_url}/api/auth/")
        print(f"Auth overview status: {response.status_code}")
        
        # Try to login with default credentials
        login_data = {
            "email": "<EMAIL>",
            "password": "admin123"
        }
        
        print("\n🔐 Attempting login...")
        response = requests.post(f"{base_url}/api/auth/login", json=login_data)
        print(f"Login status: {response.status_code}")
        
        if response.status_code == 200:
            token_data = response.json()
            token = token_data.get("access_token")
            print(f"✅ Got token: {token[:50]}...")
            
            # Test leave policies with token
            headers = {"Authorization": f"Bearer {token}"}
            response = requests.get(f"{base_url}/api/leave/policies", headers=headers)
            print(f"\nLeave policies status: {response.status_code}")
            
            if response.status_code == 200:
                policies = response.json()
                print(f"✅ Got {len(policies)} policies")
                for policy in policies[:3]:  # Show first 3
                    print(f"  - {policy.get('name', 'Unknown')} ({policy.get('leave_type', 'Unknown')})")
            else:
                print(f"❌ Error: {response.text}")
        else:
            print(f"❌ Login failed: {response.text}")
            
            # Try alternative login
            login_data2 = {
                "email": "<EMAIL>",
                "password": "admin"
            }
            print("\n🔐 Trying alternative login...")
            response = requests.post(f"{base_url}/api/auth/login", json=login_data2)
            print(f"Alt login status: {response.status_code}")
            if response.status_code != 200:
                print(f"Response: {response.text}")
        
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    test_with_auth()
