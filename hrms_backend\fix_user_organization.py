#!/usr/bin/env python3
"""
Fix user organization assignment
"""

import psycopg2
import os
from dotenv import load_dotenv

load_dotenv()

def fix_user_organization():
    """Assign user to an organization with leave policies"""
    
    conn = psycopg2.connect(
        host=os.getenv("DB_HOST", "localhost"),
        database=os.getenv("DB_NAME", "hrms_db"),
        user=os.getenv("DB_USER", "postgres"),
        password=os.getenv("DB_PASSWORD", "password"),
        port=os.getenv("DB_PORT", "5432")
    )
    
    try:
        cursor = conn.cursor()
        
        # Get an organization that has leave policies
        cursor.execute("""
            SELECT organization_id, COUNT(*) as policy_count
            FROM leave_policies 
            WHERE is_active = true
            GROUP BY organization_id
            ORDER BY policy_count DESC
            LIMIT 1
        """)
        
        result = cursor.fetchone()
        if result:
            target_org_id = result[0]
            policy_count = result[1]
            
            # Get organization name
            cursor.execute("SELECT name FROM organizations WHERE id = %s", (target_org_id,))
            org_name = cursor.fetchone()[0]
            
            print(f"Assigning user to organization: {org_name} (ID: {target_org_id})")
            print(f"This organization has {policy_count} leave policies")
            
            # Update user's organization
            cursor.execute("""
                UPDATE users 
                SET organization_id = %s 
                WHERE email = '<EMAIL>'
            """, (target_org_id,))
            
            # Also update other test users
            test_users = ['<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>']
            for user_email in test_users:
                cursor.execute("""
                    UPDATE users 
                    SET organization_id = %s 
                    WHERE email = %s AND organization_id IS NULL
                """, (target_org_id, user_email))
                print(f"Updated {user_email}")
            
            conn.commit()
            print("✅ Successfully updated user organizations!")
            
            # Verify the update
            cursor.execute("""
                SELECT email, organization_id 
                FROM users 
                WHERE email IN ('<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>')
            """)
            updated_users = cursor.fetchall()
            print("\nUpdated users:")
            for user in updated_users:
                print(f"  - {user[0]}: {user[1]}")
        
        else:
            print("No organizations with leave policies found!")
        
    except Exception as e:
        print(f"Error: {e}")
        conn.rollback()
    finally:
        cursor.close()
        conn.close()

if __name__ == "__main__":
    fix_user_organization()
