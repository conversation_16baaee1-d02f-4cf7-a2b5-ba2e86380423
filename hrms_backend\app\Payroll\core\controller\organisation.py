from flask import request, url_for, jsonify
from flask.views import MethodView
from flask_smorest import Blueprint, abort
from core.middleware import roles_required
from core.services.CloudinaryService import CloudinaryService
from schemas import LogoUploadSchema, OrganisationSchema, OrganisationUpdateSchema
from sqlalchemy.orm.exc import NoResultFound
from sqlalchemy.exc import SQLAlchemyError, IntegrityError
from core.utils.responseBuilder import ResponseBuilder
import core.utils.response_message as RESPONSEMESSAGE
from core.services.organisation import OrganisationService
from marshmallow import ValidationError
from core.utils.activity_logger import ActivityLogger

blueprint = Blueprint("Organisations", __name__, description="Operations for organisations")
    
@blueprint.route("/organisation/<id>")
class Organisation(MethodView):
    @roles_required(['admin'])
    @blueprint.response(200, OrganisationSchema)
    def get(self, id):
        serviceOrganisation = OrganisationService()
        organsation = serviceOrganisation.getOrganisationById(id)
        if not organsation:
            abort(400, message="Organisation does not exist")
        organisation_schema = OrganisationSchema()
        response_data = organisation_schema.dump(organsation)
        return ResponseBuilder(data=response_data, status_code=200).build()
    
    @roles_required(['admin'])
    def delete(self, id):
        serviceOrganisation = OrganisationService()
        organisation = serviceOrganisation.getOrganisationById(id)
        if not organisation:
            abort(404, message="Organisation does not exist")

        if organisation.employees:
            abort(400, message="Cannot delete organisation with existing employees")

        id = organisation.id
        serviceOrganisation.deleteOrganisation(id)
        return {"message" : "Organisation deleted"}
    
    @roles_required(['admin'])
    @blueprint.arguments(OrganisationUpdateSchema)
    @blueprint.response(201, OrganisationSchema)
    def put(self, org_data, id):
        serviceOrganisation = OrganisationService()
        organisation = serviceOrganisation.getOrganisationById(id)
        if not organisation:
            abort(404, message="Organisation does not exist")
        try :
            new_org = serviceOrganisation.updateOrganisation(id, org_data)
            return new_org
        except SQLAlchemyError:
            abort(500, message="Error while updating organisation")
    
@blueprint.route("/organisation")
class OrganisationList(MethodView):
    @roles_required(['admin'])
    @blueprint.response(200, OrganisationSchema)
    def post(self):
        try:
            serviceOrganisation = OrganisationService()
            cloudinary_service = CloudinaryService()  # Initialize Cloudinary service
           
            # Retrieve form data manually instead of using @blueprint.arguments
            form_data = request.form.to_dict()

            form_data["slug"] = serviceOrganisation.slug(form_data["organisation_name"])
            try:
                schema = OrganisationSchema()
                data = schema.dump(form_data)
            except ValidationError as err:
                abort(422, message=err.messages)

            # Validate required fields manually
            # if "organisation_name" not in data_org or not data_org["organisation_name"]:
            #     abort(422, message="organisation_name is required")
            # if "email" not in data_org or not data_org["email"]:
            #     abort(422, message="email is required")

            organisation = serviceOrganisation.getOrganisationByKey({"email": data["email"]})
            if organisation:
                abort(400, message="Organisation already exists")

             # Handle image upload (if provided)
            if "logoUrl" in request.files:
                image_file = request.files["logoUrl"]
                if image_file and image_file.filename :  # Ensures a file is actually uploaded
                    cloudinary_response = cloudinary_service.upload_image(image_file)
                    data["logoUrl"] = cloudinary_response.get("secure_url")  # Add Cloudinary URL
            # print("form_data ", data)

            # Create new organisation
            new_organisation = serviceOrganisation.createOrganisation(data)
            ActivityLogger.log_admin_activity(f"You created a new organisation: {data['organisation_name']}")
        except IntegrityError:
            abort(500, message="Error while creating an organisation")
        except SQLAlchemyError as e:
            print(e)
            abort(500, message="Error while creating an organisation")
        return new_organisation

    
    @roles_required(['admin'])
    @blueprint.response(200, OrganisationSchema)
    def get(self):
        organisationService = OrganisationService()
        organisation_list, total_organisation = organisationService.getAllOrganisation()
        return ResponseBuilder(data=organisation_list, status_code=200, total=total_organisation).build()
    

    @blueprint.route("/organisation/upload-logo/<id>")    
    class OrganisationLogoUpload(MethodView):
     @blueprint.response(201, LogoUploadSchema)
     @roles_required(['admin'])
     def post(self, id):
        """
        Upload a new logo for the organisation.
        """
        serviceOrganisation = OrganisationService()   
        organisation = serviceOrganisation.getOrganisationById(id)

        if not organisation:
            abort(404, message="Organisation does not exist.")

        if 'image' not in request.files:
            abort(400, message="No image file provided.")

        image_file = request.files['image']

        if image_file.filename == '':
            abort(400, message="No selected file.")

        try:
            # Delegate the update to the service
            updated_organisation = serviceOrganisation.updateOrganisation(id, logo_file=image_file)
            
            if updated_organisation:
                # Access logoUrl from the updated organisation object
                return {
                    "message": "Logo uploaded successfully.",
                    "logoUrl": updated_organisation.logoUrl
                }, 201
            else:
                abort(500, message="Failed to update organisation.")
        except Exception as e:
            print(e)
            abort(500, message="Failed to upload logo.")

    @blueprint.route("/organisation/update-logo/<id>")
    class OrganisationLogoUpdate(MethodView):
     @blueprint.response(201, LogoUploadSchema)
     @roles_required(['admin'])
     def put(self, id):
        """
        Update the existing logo for the organisation.
        """
        serviceOrganisation = OrganisationService()
        organisation = serviceOrganisation.getOrganisationById(id)

        if not organisation:
            abort(404, message="Organisation does not exist.")

        if 'image' not in request.files:
            abort(400, message="No image file provided.")

        image_file = request.files['image']

        if image_file.filename == '':
            abort(400, message="No selected file.")

        try:
             # Delegate Cloudinary update and organisation update to the repository
                    updated_organization = serviceOrganisation.updateOrganisation(id, logo_file=image_file)
                    if updated_organization:
                     return {"message": "Logo updated successfully.","logoUrl":updated_organization.logoUrl}, 200

        except Exception as e:
            print(e)
            abort(500, message="Failed to update logo.")


@blueprint.route("/edit-organisation/<int:id>", methods=["PUT"])
@roles_required(['admin'])
@blueprint.response(200, OrganisationSchema)
def put(id):
    try:
        serviceOrganisation = OrganisationService()
        cloudinary_service = CloudinaryService()

        organisation = serviceOrganisation.getOrganisationById(id)
        if not organisation:
            abort(404, message="Organisation does not exist")

        # Parse form data
        data_org = {key: request.form[key] for key in request.form}

        # Remove fields that shouldn't be updated
        for field in ["id", "slug", "user_id"]:
            data_org.pop(field, None)

        # Handle image upload
        if "logoUrl" in request.files and request.files["logoUrl"].filename:
            image_file = request.files["logoUrl"]
            cloudinary_response = cloudinary_service.upload_image(image_file)
            if cloudinary_response and "secure_url" in cloudinary_response:
                data_org["logoUrl"] = cloudinary_response["secure_url"]

        # Ensure there is at least one field to update
        if not data_org:
            abort(400, message="No valid fields provided for update.")

        # Call the update function
        updated_org = serviceOrganisation.updateOrganisation(id, data_org)

        # Prevent None from breaking execution
        if updated_org is None:
            return {"message": "No changes were made."}, 200

        return updated_org

    except SQLAlchemyError as e:
        abort(500, message="Error while updating organisation")