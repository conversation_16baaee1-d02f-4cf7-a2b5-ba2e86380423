from core.databases.database import db
from core.models.basemodel import ModelBase
from sqlalchemy.orm import relationship
from datetime import datetime
from sqlalchemy.dialects.postgresql import NUMERIC


class RemarkModel(ModelBase):
        __tablename__ = "remark"
        id = db.Column(db.Integer, primary_key=True, autoincrement=True)
        employee_id = db.Column(db.<PERSON><PERSON><PERSON>, db.<PERSON>((ModelBase.dbSchema() + '.employees.id'),ondelete='CASCADE'), nullable=True)
        payroll_history_id = db.Column(db.Integer, db.ForeignKey((ModelBase.dbSchema() + '.payroll_history.id'),ondelete='CASCADE'), nullable=True)
        user_id = db.Column(db.Integer, db.ForeignKey((ModelBase.dbSchema() + '.users.id'),ondelete='CASCADE'), nullable=True) 
        remark = db.Column(db.String(200), nullable=True)
        created_at = db.Column(db.DateTime, nullable=False, default=datetime.now())
        updated_at = db.Column(db.DateTime, nullable=False, default=datetime.now())


