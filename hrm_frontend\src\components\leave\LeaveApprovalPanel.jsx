/**
 * Professional Leave Approval Panel for Managers
 * Features: Bulk approval, detailed review, impact analysis
 */

import React, { useState, useEffect } from 'react';
import {
  CheckCircle,
  XCircle,
  Clock,
  User,
  Calendar,
  FileText,
  AlertTriangle,
  MessageSquare,
  Filter,
  Search,
  Download
} from 'lucide-react';
import apiService from '../../services/api';

const LeaveApprovalPanel = () => {
  const [pendingRequests, setPendingRequests] = useState([]);
  const [selectedRequests, setSelectedRequests] = useState([]);
  const [loading, setLoading] = useState(true);
  const [processingApproval, setProcessingApproval] = useState(false);
  const [filterStatus, setFilterStatus] = useState('pending');
  const [searchTerm, setSearchTerm] = useState('');
  const [showApprovalModal, setShowApprovalModal] = useState(false);
  const [currentRequest, setCurrentRequest] = useState(null);

  useEffect(() => {
    loadPendingRequests();
  }, [filterStatus]);

  const loadPendingRequests = async () => {
    try {
      setLoading(true);
      let response;
      if (filterStatus === 'pending') {
        response = await apiService.getPendingLeaveRequests();
      } else {
        response = await apiService.getLeaveRequests({
          status: filterStatus === 'all' ? undefined : filterStatus,
          limit: 50
        });
      }
      setPendingRequests(Array.isArray(response) ? response : (response.requests || []));
    } catch (error) {
      console.error('Error loading pending requests:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleSelectRequest = (requestId) => {
    setSelectedRequests(prev => 
      prev.includes(requestId) 
        ? prev.filter(id => id !== requestId)
        : [...prev, requestId]
    );
  };

  const handleSelectAll = () => {
    const filteredRequests = getFilteredRequests();
    const allSelected = filteredRequests.every(req => selectedRequests.includes(req.id));
    
    if (allSelected) {
      setSelectedRequests([]);
    } else {
      setSelectedRequests(filteredRequests.map(req => req.id));
    }
  };

  const handleApproval = async (requestId, approved, comments = '') => {
    try {
      setProcessingApproval(true);
      await apiService.approveLeaveRequest(requestId, approved, comments);
      await loadPendingRequests();
      setSelectedRequests(prev => prev.filter(id => id !== requestId));
      setShowApprovalModal(false);
      setCurrentRequest(null);
    } catch (error) {
      console.error('Error processing approval:', error);
    } finally {
      setProcessingApproval(false);
    }
  };

  const handleBulkApproval = async (approved) => {
    try {
      setProcessingApproval(true);
      await Promise.all(
        selectedRequests.map(requestId => 
          apiService.approveLeaveRequest(requestId, approved)
        )
      );
      await loadPendingRequests();
      setSelectedRequests([]);
    } catch (error) {
      console.error('Error processing bulk approval:', error);
    } finally {
      setProcessingApproval(false);
    }
  };

  const getFilteredRequests = () => {
    return pendingRequests.filter(request => {
      const matchesSearch = searchTerm === '' || 
        request.employee_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        request.leave_type_name?.toLowerCase().includes(searchTerm.toLowerCase());
      
      return matchesSearch;
    });
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'pending': return 'bg-amber-100 text-amber-800';
      case 'approved': return 'bg-green-100 text-green-800';
      case 'rejected': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getPriorityLevel = (request) => {
    const startDate = new Date(request.start_date);
    const today = new Date();
    const daysUntilStart = Math.ceil((startDate - today) / (1000 * 60 * 60 * 24));
    
    if (daysUntilStart <= 3) return { level: 'high', color: 'text-red-600', label: 'Urgent' };
    if (daysUntilStart <= 7) return { level: 'medium', color: 'text-amber-600', label: 'Medium' };
    return { level: 'low', color: 'text-green-600', label: 'Low' };
  };

  const filteredRequests = getFilteredRequests();

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Leave Approvals</h2>
          <p className="text-gray-600 mt-1">Review and approve team leave requests</p>
        </div>
        
        <div className="flex items-center space-x-3">
          {selectedRequests.length > 0 && (
            <>
              <button
                onClick={() => handleBulkApproval(true)}
                disabled={processingApproval}
                className="flex items-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50"
              >
                <CheckCircle size={16} className="mr-2" />
                Approve Selected ({selectedRequests.length})
              </button>
              
              <button
                onClick={() => handleBulkApproval(false)}
                disabled={processingApproval}
                className="flex items-center px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 disabled:opacity-50"
              >
                <XCircle size={16} className="mr-2" />
                Reject Selected
              </button>
            </>
          )}
          
          <button className="flex items-center px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50">
            <Download size={16} className="mr-2" />
            Export
          </button>
        </div>
      </div>

      {/* Filters and Search */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="flex-1">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={16} />
              <input
                type="text"
                placeholder="Search by employee name or leave type..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-agno-primary focus:border-transparent"
              />
            </div>
          </div>
          
          <div className="flex items-center space-x-3">
            <select
              value={filterStatus}
              onChange={(e) => setFilterStatus(e.target.value)}
              className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-agno-primary focus:border-transparent"
            >
              <option value="pending">Pending</option>
              <option value="approved">Approved</option>
              <option value="rejected">Rejected</option>
              <option value="all">All Requests</option>
            </select>
          </div>
        </div>
      </div>

      {/* Requests List */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        {loading ? (
          <div className="flex items-center justify-center h-64">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-agno-primary"></div>
          </div>
        ) : filteredRequests.length === 0 ? (
          <div className="text-center py-12">
            <Clock className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-500">No leave requests found</p>
          </div>
        ) : (
          <>
            {/* Table Header */}
            <div className="px-6 py-4 border-b border-gray-200 bg-gray-50">
              <div className="flex items-center">
                <input
                  type="checkbox"
                  checked={filteredRequests.length > 0 && filteredRequests.every(req => selectedRequests.includes(req.id))}
                  onChange={handleSelectAll}
                  className="h-4 w-4 text-agno-primary focus:ring-agno-primary border-gray-300 rounded"
                />
                <span className="ml-3 text-sm font-medium text-gray-700">Select All</span>
              </div>
            </div>

            {/* Requests */}
            <div className="divide-y divide-gray-200">
              {filteredRequests.map((request) => {
                const priority = getPriorityLevel(request);
                const isSelected = selectedRequests.includes(request.id);
                
                return (
                  <div
                    key={request.id}
                    className={`p-6 hover:bg-gray-50 transition-colors ${
                      isSelected ? 'bg-blue-50 border-l-4 border-l-blue-500' : ''
                    }`}
                  >
                    <div className="flex items-start space-x-4">
                      <input
                        type="checkbox"
                        checked={isSelected}
                        onChange={() => handleSelectRequest(request.id)}
                        className="mt-1 h-4 w-4 text-agno-primary focus:ring-agno-primary border-gray-300 rounded"
                      />
                      
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-3">
                            <h3 className="text-lg font-medium text-gray-900">
                              {request.employee_name || 'Unknown Employee'}
                            </h3>
                            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(request.status)}`}>
                              {request.status.charAt(0).toUpperCase() + request.status.slice(1)}
                            </span>
                            <span className={`text-sm font-medium ${priority.color}`}>
                              {priority.label} Priority
                            </span>
                          </div>
                          
                          <div className="flex items-center space-x-2">
                            <button
                              onClick={() => {
                                setCurrentRequest(request);
                                setShowApprovalModal(true);
                              }}
                              className="text-agno-primary hover:text-blue-700 text-sm font-medium"
                            >
                              Review
                            </button>
                          </div>
                        </div>
                        
                        <div className="mt-2 grid grid-cols-1 sm:grid-cols-3 gap-4 text-sm text-gray-600">
                          <div className="flex items-center">
                            <FileText size={14} className="mr-2" />
                            <span>{request.leave_type_name || request.leave_type}</span>
                          </div>
                          <div className="flex items-center">
                            <Calendar size={14} className="mr-2" />
                            <span>
                              {new Date(request.start_date).toLocaleDateString()} - {new Date(request.end_date).toLocaleDateString()}
                            </span>
                          </div>
                          <div className="flex items-center">
                            <Clock size={14} className="mr-2" />
                            <span>{request.total_days} days</span>
                          </div>
                        </div>
                        
                        {request.reason && (
                          <div className="mt-3 p-3 bg-gray-50 rounded-lg">
                            <p className="text-sm text-gray-700">
                              <strong>Reason:</strong> {request.reason}
                            </p>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          </>
        )}
      </div>

      {/* Approval Modal */}
      {showApprovalModal && currentRequest && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-2xl shadow-2xl w-full max-w-2xl max-h-[90vh] overflow-hidden">
            <div className="p-6 border-b border-gray-200">
              <h3 className="text-xl font-semibold text-gray-900">
                Review Leave Request
              </h3>
            </div>
            
            <div className="p-6 space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700">Employee</label>
                  <p className="mt-1 text-sm text-gray-900">{currentRequest.employee_name}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">Leave Type</label>
                  <p className="mt-1 text-sm text-gray-900">{currentRequest.leave_type_name}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">Duration</label>
                  <p className="mt-1 text-sm text-gray-900">
                    {new Date(currentRequest.start_date).toLocaleDateString()} - {new Date(currentRequest.end_date).toLocaleDateString()}
                  </p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">Total Days</label>
                  <p className="mt-1 text-sm text-gray-900">{currentRequest.total_days} days</p>
                </div>
              </div>
              
              {currentRequest.reason && (
                <div>
                  <label className="block text-sm font-medium text-gray-700">Reason</label>
                  <p className="mt-1 text-sm text-gray-900 p-3 bg-gray-50 rounded-lg">
                    {currentRequest.reason}
                  </p>
                </div>
              )}
            </div>
            
            <div className="px-6 py-4 bg-gray-50 flex justify-end space-x-3">
              <button
                onClick={() => setShowApprovalModal(false)}
                className="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50"
              >
                Cancel
              </button>
              <button
                onClick={() => handleApproval(currentRequest.id, false)}
                disabled={processingApproval}
                className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 disabled:opacity-50"
              >
                Reject
              </button>
              <button
                onClick={() => handleApproval(currentRequest.id, true)}
                disabled={processingApproval}
                className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50"
              >
                Approve
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default LeaveApprovalPanel;
