#!/usr/bin/env python3
"""
Final test to verify email notifications are working
"""

import requests
import json
import time
from datetime import datetime, timedelta

def test_email_final():
    """Final email test"""
    try:
        print("📧 FINAL EMAIL NOTIFICATION TEST")
        print("=" * 50)
        
        # Login
        login_data = {'email': '<EMAIL>', 'password': 'password123'}
        response = requests.post('http://localhost:8000/api/auth/login', json=login_data)
        
        if response.status_code != 200:
            print('❌ Login failed')
            return False
            
        data = response.json()
        token = data.get('access_token')
        headers = {'Authorization': f'Bearer {token}'}
        print('✅ Login successful')
        
        # Get leave policies
        policies_response = requests.get('http://localhost:8000/api/leave/policies', headers=headers)
        if policies_response.status_code != 200:
            print('❌ Failed to get leave policies')
            return False
            
        policies = policies_response.json()
        print(f'✅ Got {len(policies)} leave policies')
        
        # Create leave request
        policy_id = policies[0]['id']
        start_date = (datetime.now() + timedelta(days=42)).strftime('%Y-%m-%d')
        end_date = (datetime.now() + timedelta(days=44)).strftime('%Y-%m-%d')
        
        leave_request_data = {
            'leave_policy_id': policy_id,
            'start_date': start_date,
            'end_date': end_date,
            'duration_type': 'FULL_DAY',
            'reason': 'Final email test - checking nirmalkumar@agnoshin.<NAME_EMAIL>'
        }
        
        print(f'📋 Creating leave request...')
        print(f'📅 Dates: {start_date} to {end_date}')
        
        create_response = requests.post(
            'http://localhost:8000/api/leave/my/requests',
            json=leave_request_data,
            headers=headers
        )
        
        print(f'📊 Response Status: {create_response.status_code}')
        
        if create_response.status_code in [200, 201]:
            leave_request = create_response.json()
            print('✅ Leave request created successfully!')
            print(f'📧 Emails should be sent to:')
            print(f'   - <EMAIL> (Manager)')
            print(f'   - <EMAIL> (Employee)')
            print(f'   - <EMAIL> (HR)')
            
            # Wait for email processing
            print('\n⏳ Waiting 10 seconds for email processing...')
            time.sleep(10)
            
            print('\n🎉 EMAIL TEST COMPLETED!')
            print('📧 Please check the email inboxes now!')
            return True
        else:
            print(f'❌ Leave request failed: {create_response.text[:200]}')
            return False
        
    except Exception as e:
        print(f'❌ Error: {e}')
        return False

if __name__ == "__main__":
    success = test_email_final()
    if success:
        print('\n✅ Email test completed successfully!')
        print('📧 Check nirmalkumar@agnoshin.<NAME_EMAIL>')
    else:
        print('\n❌ Email test failed.')
