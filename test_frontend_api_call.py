#!/usr/bin/env python3
"""
Test the exact API call that the frontend would make
"""

import requests
import json

def test_frontend_api_call():
    """Test the exact API call that the frontend would make"""
    try:
        # Test the exact endpoint the frontend uses
        url = "http://localhost:8085/api/auth/login"
        
        # Test super admin credentials
        credentials = {
            "email": "<EMAIL>",
            "password": "password123"
        }
        
        headers = {
            'Content-Type': 'application/json'
        }
        
        print("Testing Frontend API Call")
        print("=" * 40)
        print(f"URL: {url}")
        print(f"Credentials: {credentials}")
        print(f"Headers: {headers}")
        print()
        
        response = requests.post(url, json=credentials, headers=headers)
        
        print(f"Status Code: {response.status_code}")
        print(f"Response Headers: {dict(response.headers)}")
        print(f"Response Body: {response.text}")
        
        if response.status_code == 200:
            print("\n✅ API call successful!")
            data = response.json()
            print(f"User: {data.get('user', {})}")
            print(f"Token received: {'Yes' if data.get('access_token') else 'No'}")
        else:
            print(f"\n❌ API call failed with status {response.status_code}")
            
        # Test all demo accounts
        demo_accounts = [
            {"email": "<EMAIL>", "password": "password123"},
            {"email": "<EMAIL>", "password": "password123"},
            {"email": "<EMAIL>", "password": "password123"},
            {"email": "<EMAIL>", "password": "password123"}
        ]
        
        print("\n" + "=" * 40)
        print("Testing All Demo Accounts:")
        
        for account in demo_accounts:
            try:
                response = requests.post(url, json=account, headers=headers)
                status = "✅ SUCCESS" if response.status_code == 200 else f"❌ FAILED ({response.status_code})"
                print(f"{account['email']:<25} {status}")
            except Exception as e:
                print(f"{account['email']:<25} ❌ ERROR: {e}")
                
    except Exception as e:
        print(f"❌ Test failed: {e}")

if __name__ == "__main__":
    test_frontend_api_call()
