#!/usr/bin/env python3
"""
Simple script to create default leave policies using direct SQL
"""

import psycopg2
from uuid import uuid4
from datetime import datetime
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def create_leave_policies_sql():
    """Create leave policies using direct SQL"""
    
    # Database connection
    conn = psycopg2.connect(
        host=os.getenv("DB_HOST", "localhost"),
        database=os.getenv("DB_NAME", "hrms_db"),
        user=os.getenv("DB_USER", "postgres"),
        password=os.getenv("DB_PASSWORD", "password"),
        port=os.getenv("DB_PORT", "5432")
    )
    
    try:
        cursor = conn.cursor()
        
        # First, get all organizations
        cursor.execute("SELECT id, name FROM organizations WHERE is_active = true")
        organizations = cursor.fetchall()
        
        if not organizations:
            print("No active organizations found. Creating a default organization...")
            # Create a default organization
            org_id = str(uuid4())
            cursor.execute("""
                INSERT INTO organizations (id, name, email, phone, website, 
                                         address_line1, city, state, country, postal_code,
                                         industry, size, tax_id, timezone, currency, date_format,
                                         is_active, created_at, updated_at)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
            """, (
                org_id, 'AgnoConnect', '<EMAIL>', '******-0123', 'https://agnoconnect.com',
                '123 Business St', 'Business City', 'Business State', 'USA', '12345',
                'Technology', 'Medium', 'TAX123456', 'UTC', 'USD', 'YYYY-MM-DD',
                True, datetime.utcnow(), datetime.utcnow()
            ))
            organizations = [(org_id, 'AgnoConnect')]
            print(f"✅ Created default organization: AgnoConnect")
        
        # Leave policies to create
        leave_policies = [
            ('Annual Leave', 'ANNUAL', 21, 5, 30, 'monthly', 'hire_date', 7, 15, 0.5, True, 3, False, None),
            ('Sick Leave', 'SICK', 10, 2, 15, 'monthly', 'hire_date', 0, 7, 0.5, True, 2, True, 3),
            ('Casual Leave', 'PERSONAL', 12, 3, 18, 'monthly', 'hire_date', 1, 5, 0.5, True, 2, False, None),
            ('Maternity Leave', 'MATERNITY', 180, 0, 180, 'yearly', 'calendar_year', 30, 180, 1, True, None, True, 1),
            ('Paternity Leave', 'PATERNITY', 15, 0, 15, 'yearly', 'calendar_year', 14, 15, 1, True, None, True, 1),
            ('Emergency Leave', 'EMERGENCY', 5, 0, 5, 'yearly', 'calendar_year', 0, 3, 0.5, True, 1, True, 1),
            ('Bereavement Leave', 'BEREAVEMENT', 7, 0, 7, 'yearly', 'calendar_year', 0, 7, 0.5, True, None, True, 1),
            ('Study Leave', 'STUDY', 10, 5, 20, 'yearly', 'calendar_year', 30, 10, 1, True, None, True, 1),
            ('Sabbatical Leave', 'SABBATICAL', 90, 0, 90, 'yearly', 'calendar_year', 90, 90, 1, True, None, True, 1),
            ('Unpaid Leave', 'UNPAID', 30, 0, 30, 'yearly', 'calendar_year', 14, 30, 1, True, None, True, 1)
        ]
        
        for org_id, org_name in organizations:
            print(f"\nCreating leave policies for organization: {org_name}")
            
            for policy_data in leave_policies:
                (name, leave_type, annual_entitlement, max_carry_forward, max_accumulation,
                 accrual_frequency, accrual_start_date, min_notice_days, max_consecutive_days,
                 min_application_days, requires_approval, auto_approve_threshold,
                 requires_documentation, documentation_threshold) = policy_data
                
                # Check if policy already exists
                cursor.execute("""
                    SELECT id FROM leave_policies 
                    WHERE organization_id = %s AND leave_type = %s AND is_active = true
                """, (org_id, leave_type))
                
                if cursor.fetchone():
                    print(f"  - {name} already exists, skipping...")
                    continue
                
                # Create new policy
                policy_id = str(uuid4())
                cursor.execute("""
                    INSERT INTO leave_policies (
                        id, name, leave_type, organization_id, annual_entitlement,
                        max_carry_forward, max_accumulation, accrual_frequency,
                        accrual_start_date, min_notice_days, max_consecutive_days,
                        min_application_days, requires_approval, auto_approve_threshold,
                        requires_documentation, documentation_threshold,
                        available_during_probation, probation_entitlement,
                        is_active, created_at, updated_at
                    ) VALUES (
                        %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s
                    )
                """, (
                    policy_id, name, leave_type, org_id, annual_entitlement,
                    max_carry_forward, max_accumulation, accrual_frequency,
                    accrual_start_date, min_notice_days, max_consecutive_days,
                    min_application_days, requires_approval, auto_approve_threshold,
                    requires_documentation, documentation_threshold,
                    True, 0,  # available_during_probation, probation_entitlement
                    True, datetime.utcnow(), datetime.utcnow()
                ))
                
                print(f"  ✅ Created {name}")
        
        conn.commit()
        print("\n🎉 Successfully created all default leave policies!")
        
    except Exception as e:
        print(f"❌ Error creating leave policies: {e}")
        conn.rollback()
        raise
    finally:
        cursor.close()
        conn.close()

if __name__ == "__main__":
    create_leave_policies_sql()
