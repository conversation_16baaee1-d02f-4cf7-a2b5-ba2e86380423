#!/usr/bin/env python3
"""
Check the actual leave_policies table schema
"""

import psycopg2
import os
from dotenv import load_dotenv

load_dotenv()

def check_leave_table_schema():
    """Check what columns exist in leave_policies table"""
    
    conn = psycopg2.connect(
        host=os.getenv("DB_HOST", "localhost"),
        database=os.getenv("DB_NAME", "hrms_db"),
        user=os.getenv("DB_USER", "postgres"),
        password=os.getenv("DB_PASSWORD", "password"),
        port=os.getenv("DB_PORT", "5432")
    )
    
    try:
        cursor = conn.cursor()
        
        # Get table schema
        cursor.execute("""
            SELECT column_name, data_type, is_nullable, column_default
            FROM information_schema.columns 
            WHERE table_name = 'leave_policies'
            ORDER BY ordinal_position
        """)
        
        columns = cursor.fetchall()
        print("leave_policies table schema:")
        print("-" * 60)
        for col in columns:
            print(f"{col[0]:<30} {col[1]:<15} {col[2]:<10} {col[3] or ''}")
        
        print(f"\nTotal columns: {len(columns)}")
        
        # Check if there are any leave policies
        cursor.execute("SELECT COUNT(*) FROM leave_policies")
        count = cursor.fetchone()[0]
        print(f"Total leave policies in database: {count}")
        
        if count > 0:
            cursor.execute("SELECT name, leave_type FROM leave_policies LIMIT 5")
            policies = cursor.fetchall()
            print("\nSample policies:")
            for policy in policies:
                print(f"  - {policy[0]} ({policy[1]})")
        
    except Exception as e:
        print(f"Error checking schema: {e}")
    finally:
        cursor.close()
        conn.close()

if __name__ == "__main__":
    check_leave_table_schema()
