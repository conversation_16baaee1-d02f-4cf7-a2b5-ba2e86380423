from sqlalchemy import Column, DateTime, String, Boolean, Text
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.sql import func
from uuid import uuid4

from .session import Base


class BaseModel(Base):
    """Base model with common fields"""
    __abstract__ = True

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid4, index=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), nullable=False)
    # Note: created_by and updated_by columns commented out for integration testing
    # as they don't exist in the current database schema
    # created_by = Column(UUID(as_uuid=True), nullable=True)
    # updated_by = Column(UUID(as_uuid=True), nullable=True)
    is_active = Column(Boolean, default=True, nullable=False)

    def __repr__(self):
        return f"<{self.__class__.__name__}(id={self.id})>"


class AuditMixin:
    """Mixin for audit fields"""
    notes = Column(Text, nullable=True)
    metadata_json = Column(Text, nullable=True)  # For storing additional JSON data


# Import all models to ensure they are registered with SQLAlchemy
from .models.employee import *
from .models.attendance import *
from .models.leave import *
from .models.shift import *
from .models.timesheet import *
from .models.project import *
from .models.kanban import *
from .models.ticket import *
from .models.delegation import *
from .models.payroll import *
from .models.performance import *
from .models.engagement import *
from .models.user import *
from .models.recruitment import *
from .models.lms import *
from .models.onboarding import *
from .models.reporting import *
