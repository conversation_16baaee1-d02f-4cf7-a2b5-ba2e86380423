/**
 * Tickets Management Page with RBAC Integration
 * Displays support tickets and ticketing system with role-based access control
 */

import React, { useState } from 'react';
import { Plus, Filter, Search, MessageSquare, Clock, CheckCircle, AlertTriangle, User, Calendar } from 'lucide-react';
import { usePermissions } from '../hooks/usePermissions';
import { PermissionGate, ConditionalRender } from '../components/ProtectedRoute';

export default function Tickets({ activeTab = 'my-tickets' }) {
  const permissions = usePermissions();
  const [selectedStatus, setSelectedStatus] = useState('all');
  const [selectedPriority, setSelectedPriority] = useState('all');
  const [searchTerm, setSearchTerm] = useState('');

  // Mock ticket data
  const tickets = [
    {
      id: 'TKT-001',
      title: 'Unable to access payroll system',
      description: 'Getting error when trying to view my payslip for this month',
      status: 'open',
      priority: 'high',
      category: 'Technical',
      submitter: '<PERSON>',
      assignee: 'IT Support',
      createdDate: '2024-01-15',
      updatedDate: '2024-01-16',
      dueDate: '2024-01-20',
      comments: 3
    },
    {
      id: 'TKT-002',
      title: 'Request for additional leave days',
      description: 'Need to request additional personal leave for family emergency',
      status: 'in-progress',
      priority: 'medium',
      category: 'HR',
      submitter: 'Jane Smith',
      assignee: 'HR Team',
      createdDate: '2024-01-14',
      updatedDate: '2024-01-17',
      dueDate: '2024-01-22',
      comments: 5
    },
    {
      id: 'TKT-003',
      title: 'Office equipment request',
      description: 'Need a new monitor for my workstation',
      status: 'resolved',
      priority: 'low',
      category: 'Facilities',
      submitter: 'Mike Johnson',
      assignee: 'Facilities Team',
      createdDate: '2024-01-10',
      updatedDate: '2024-01-18',
      dueDate: '2024-01-25',
      comments: 2
    },
    {
      id: 'TKT-004',
      title: 'Password reset request',
      description: 'Forgot my password and unable to login to the system',
      status: 'closed',
      priority: 'medium',
      category: 'Technical',
      submitter: 'Alice Brown',
      assignee: 'IT Support',
      createdDate: '2024-01-12',
      updatedDate: '2024-01-13',
      dueDate: '2024-01-15',
      comments: 1
    },
    {
      id: 'TKT-005',
      title: 'Training request for new software',
      description: 'Need training on the new project management tool',
      status: 'pending',
      priority: 'low',
      category: 'Training',
      submitter: 'Bob Wilson',
      assignee: 'Training Team',
      createdDate: '2024-01-16',
      updatedDate: '2024-01-16',
      dueDate: '2024-01-30',
      comments: 0
    }
  ];

  const getStatusColor = (status) => {
    switch (status) {
      case 'open': return 'text-blue-600 bg-blue-100';
      case 'in-progress': return 'text-yellow-600 bg-yellow-100';
      case 'pending': return 'text-orange-600 bg-orange-100';
      case 'resolved': return 'text-green-600 bg-green-100';
      case 'closed': return 'text-gray-600 bg-gray-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'open': return <MessageSquare size={16} />;
      case 'in-progress': return <Clock size={16} />;
      case 'pending': return <AlertTriangle size={16} />;
      case 'resolved': return <CheckCircle size={16} />;
      case 'closed': return <CheckCircle size={16} />;
      default: return <MessageSquare size={16} />;
    }
  };

  const getPriorityColor = (priority) => {
    switch (priority) {
      case 'high': return 'text-red-600 bg-red-100';
      case 'medium': return 'text-yellow-600 bg-yellow-100';
      case 'low': return 'text-green-600 bg-green-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const renderMyTickets = () => (
    <div className="space-y-6">
      {/* Ticket Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <MessageSquare className="h-8 w-8 text-blue-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Total Tickets</p>
              <p className="text-2xl font-bold text-gray-900">{tickets.length}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <Clock className="h-8 w-8 text-yellow-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Open</p>
              <p className="text-2xl font-bold text-gray-900">
                {tickets.filter(t => ['open', 'in-progress', 'pending'].includes(t.status)).length}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <CheckCircle className="h-8 w-8 text-green-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Resolved</p>
              <p className="text-2xl font-bold text-gray-900">
                {tickets.filter(t => t.status === 'resolved').length}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <AlertTriangle className="h-8 w-8 text-red-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">High Priority</p>
              <p className="text-2xl font-bold text-gray-900">
                {tickets.filter(t => t.priority === 'high').length}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Filters */}
      <div className="flex justify-between items-center">
        <div className="flex space-x-4">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={16} />
            <input
              type="text"
              placeholder="Search tickets..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 pr-4 py-2 border border-gray-300 rounded-md"
            />
          </div>
          <select 
            value={selectedStatus} 
            onChange={(e) => setSelectedStatus(e.target.value)}
            className="border border-gray-300 rounded-md px-3 py-2"
          >
            <option value="all">All Status</option>
            <option value="open">Open</option>
            <option value="in-progress">In Progress</option>
            <option value="pending">Pending</option>
            <option value="resolved">Resolved</option>
            <option value="closed">Closed</option>
          </select>
          <select 
            value={selectedPriority} 
            onChange={(e) => setSelectedPriority(e.target.value)}
            className="border border-gray-300 rounded-md px-3 py-2"
          >
            <option value="all">All Priority</option>
            <option value="high">High</option>
            <option value="medium">Medium</option>
            <option value="low">Low</option>
          </select>
        </div>
        <PermissionGate permission="ticketingSystemSelf">
          <button className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 flex items-center space-x-2">
            <Plus size={16} />
            <span>New Ticket</span>
          </button>
        </PermissionGate>
      </div>

      {/* Tickets Table */}
      <div className="bg-white rounded-lg shadow overflow-hidden">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Ticket</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Priority</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Category</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Assignee</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Updated</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {tickets.map((ticket) => (
              <tr key={ticket.id} className="hover:bg-gray-50">
                <td className="px-6 py-4 whitespace-nowrap">
                  <div>
                    <div className="text-sm font-medium text-gray-900">{ticket.id}</div>
                    <div className="text-sm text-gray-500 max-w-xs truncate">{ticket.title}</div>
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <span className={`inline-flex items-center space-x-1 px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(ticket.status)}`}>
                    {getStatusIcon(ticket.status)}
                    <span className="capitalize">{ticket.status.replace('-', ' ')}</span>
                  </span>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <span className={`inline-flex px-2 py-1 rounded-full text-xs font-medium ${getPriorityColor(ticket.priority)}`}>
                    {ticket.priority}
                  </span>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  {ticket.category}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  {ticket.assignee}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  {ticket.updatedDate}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                  <div className="flex space-x-2">
                    <button className="text-blue-600 hover:text-blue-900">
                      View
                    </button>
                    {ticket.status !== 'closed' && (
                      <button className="text-gray-600 hover:text-gray-900">
                        Edit
                      </button>
                    )}
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );

  const renderTicketManagement = () => (
    <PermissionGate permission="ticketManagement">
      <div className="space-y-6">
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Ticket Management</h3>
          <p className="text-gray-600">Manage and assign support tickets...</p>
        </div>
      </div>
    </PermissionGate>
  );

  const renderContent = () => {
    switch (activeTab) {
      case 'my-tickets':
        return renderMyTickets();
      case 'ticket-management':
        return renderTicketManagement();
      default:
        return renderMyTickets();
    }
  };

  return (
    <div className="p-6">
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-gray-900">Support Tickets</h1>
        <p className="text-gray-600">Submit and track your support requests</p>
      </div>

      {renderContent()}
    </div>
  );
}
