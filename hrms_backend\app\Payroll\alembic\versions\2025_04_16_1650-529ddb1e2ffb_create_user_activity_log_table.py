"""Create user activity log table

Revision ID: 529ddb1e2ffb
Revises: 874f0f6ccf22
Create Date: 2025-04-16 16:50:07.228062

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy import func, TIMESTAMP, ForeignKey
from sqlalchemy import Column, Integer, String, Float

# revision identifiers, used by Alembic.
revision: str = '529ddb1e2ffb'
down_revision: Union[str, None] = '874f0f6ccf22'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    op.create_table(
        'admin_activities',
        <PERSON>umn('id', Integer, primary_key=True),
        <PERSON>umn('message', sa.String(length=100), nullable=True),
        <PERSON>umn('user_id', Integer, ForeignKey("users.id")),
        <PERSON><PERSON><PERSON>('created_at', sa.DateTime(timezone=True), nullable=False),
        <PERSON>umn('updated_at', sa.DateTime(timezone=True), nullable=False),
    )


def downgrade() -> None:
    op.drop_table('admin_activities')
