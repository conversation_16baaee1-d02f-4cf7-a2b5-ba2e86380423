"""
Enhanced API documentation configuration for the HRMS system
"""

from typing import Dict, Any, List
from fastapi import FastAPI
from fastapi.openapi.utils import get_openapi


def get_enhanced_openapi_schema(app: FastAPI) -> Dict[str, Any]:
    """Generate enhanced OpenAPI schema with comprehensive documentation"""
    
    if app.openapi_schema:
        return app.openapi_schema
    
    openapi_schema = get_openapi(
        title="AGHR - Advanced Human Resource Management System",
        version="1.0.0",
        description="""
# AGHR - Advanced Human Resource Management System

A comprehensive HRMS solution built with FastAPI and modern web technologies.

## Features

### 👥 Employee Management
- Complete employee lifecycle management
- Department and designation management
- Employee profiles with detailed information
- Organizational hierarchy support

### ⏰ Time & Attendance
- Real-time attendance tracking
- Check-in/check-out with location support
- Break time management
- Attendance reports and analytics

### 📋 Timesheet Management
- Project-based time tracking
- Billable and non-billable hours
- Timesheet approvals workflow
- Comprehensive reporting

### 🏖️ Leave Management
- Multiple leave types support
- Leave balance tracking
- Approval workflows
- Leave calendar integration

### 📊 Project Management
- Project creation and management
- Task assignment and tracking
- Kanban board integration
- Project analytics

### 🎫 Ticket System
- IT support ticket management
- Priority and category management
- Assignment and escalation
- SLA tracking

### 💰 Payroll Integration
- Salary structure management
- Payroll processing
- Tax calculations
- Payroll reports

### 📈 Performance Management
- Goal setting and tracking
- Performance reviews
- 360-degree feedback
- Development planning

## Authentication

This API uses JWT (JSON Web Tokens) for authentication. Include the token in the Authorization header:

```
Authorization: Bearer <your-jwt-token>
```

## Error Handling

All API endpoints return standardized error responses:

```json
{
  "error": {
    "id": "unique-error-id",
    "code": "ERROR_CODE",
    "message": "Human readable error message",
    "timestamp": "2025-06-29T20:00:00Z",
    "path": "/api/endpoint",
    "details": {
      "additional": "error details"
    }
  }
}
```

## Rate Limiting

API requests are rate limited to ensure fair usage:
- 1000 requests per hour for authenticated users
- 100 requests per hour for unauthenticated requests

## Pagination

List endpoints support pagination with the following parameters:
- `skip`: Number of records to skip (default: 0)
- `limit`: Maximum number of records to return (default: 20, max: 100)

## Filtering and Search

Many endpoints support filtering and search:
- Use query parameters for filtering (e.g., `?status=active`)
- Use `search` parameter for text-based search
- Date ranges can be specified with `start_date` and `end_date`

## Data Formats

- **Dates**: ISO 8601 format (YYYY-MM-DD)
- **Timestamps**: ISO 8601 format with timezone (YYYY-MM-DDTHH:MM:SSZ)
- **UUIDs**: Standard UUID format (xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx)

## Support

For API support and questions:
- Email: <EMAIL>
- Documentation: https://docs.aghr.com
- Status Page: https://status.aghr.com
        """,
        routes=app.routes,
        servers=[
            {
                "url": "http://localhost:8085",
                "description": "Development server"
            },
            {
                "url": "https://api.aghr.com",
                "description": "Production server"
            }
        ]
    )
    
    # Add security schemes
    openapi_schema["components"]["securitySchemes"] = {
        "BearerAuth": {
            "type": "http",
            "scheme": "bearer",
            "bearerFormat": "JWT",
            "description": "JWT token obtained from the login endpoint"
        }
    }
    
    # Add global security requirement
    openapi_schema["security"] = [{"BearerAuth": []}]
    
    # Add custom tags with descriptions
    openapi_schema["tags"] = [
        {
            "name": "Authentication",
            "description": "User authentication and authorization endpoints"
        },
        {
            "name": "Employees",
            "description": "Employee management operations including CRUD operations, departments, and designations"
        },
        {
            "name": "Attendance",
            "description": "Time and attendance tracking including check-in/out, breaks, and attendance records"
        },
        {
            "name": "Timesheet",
            "description": "Project time tracking, timesheet entries, and time reporting"
        },
        {
            "name": "Leave",
            "description": "Leave management including requests, approvals, and balance tracking"
        },
        {
            "name": "Projects",
            "description": "Project management including creation, assignment, and tracking"
        },
        {
            "name": "Kanban",
            "description": "Kanban board management for project task organization"
        },
        {
            "name": "Tickets",
            "description": "IT support and helpdesk ticket management system"
        },
        {
            "name": "Payroll",
            "description": "Payroll processing and salary management"
        },
        {
            "name": "Performance",
            "description": "Performance management including goals, reviews, and feedback"
        },
        {
            "name": "Reports",
            "description": "Analytics and reporting across all modules"
        },
        {
            "name": "System",
            "description": "System administration and configuration endpoints"
        }
    ]
    
    # Add common response schemas
    openapi_schema["components"]["schemas"].update({
        "ErrorResponse": {
            "type": "object",
            "properties": {
                "error": {
                    "type": "object",
                    "properties": {
                        "id": {
                            "type": "string",
                            "format": "uuid",
                            "description": "Unique error identifier"
                        },
                        "code": {
                            "type": "string",
                            "description": "Error code for programmatic handling"
                        },
                        "message": {
                            "type": "string",
                            "description": "Human-readable error message"
                        },
                        "timestamp": {
                            "type": "string",
                            "format": "date-time",
                            "description": "When the error occurred"
                        },
                        "path": {
                            "type": "string",
                            "description": "API endpoint where error occurred"
                        },
                        "details": {
                            "type": "object",
                            "description": "Additional error details"
                        }
                    },
                    "required": ["id", "code", "message", "timestamp"]
                }
            },
            "required": ["error"]
        },
        "PaginatedResponse": {
            "type": "object",
            "properties": {
                "total": {
                    "type": "integer",
                    "description": "Total number of items"
                },
                "skip": {
                    "type": "integer",
                    "description": "Number of items skipped"
                },
                "limit": {
                    "type": "integer",
                    "description": "Maximum number of items returned"
                }
            },
            "required": ["total", "skip", "limit"]
        },
        "SuccessResponse": {
            "type": "object",
            "properties": {
                "success": {
                    "type": "boolean",
                    "example": True
                },
                "message": {
                    "type": "string",
                    "description": "Success message"
                }
            },
            "required": ["success"]
        }
    })
    
    # Add common parameters
    openapi_schema["components"]["parameters"] = {
        "SkipParam": {
            "name": "skip",
            "in": "query",
            "description": "Number of items to skip for pagination",
            "required": False,
            "schema": {
                "type": "integer",
                "minimum": 0,
                "default": 0
            }
        },
        "LimitParam": {
            "name": "limit",
            "in": "query",
            "description": "Maximum number of items to return",
            "required": False,
            "schema": {
                "type": "integer",
                "minimum": 1,
                "maximum": 100,
                "default": 20
            }
        },
        "SearchParam": {
            "name": "search",
            "in": "query",
            "description": "Search term for filtering results",
            "required": False,
            "schema": {
                "type": "string",
                "minLength": 1,
                "maxLength": 100
            }
        },
        "StartDateParam": {
            "name": "start_date",
            "in": "query",
            "description": "Start date for date range filtering (YYYY-MM-DD)",
            "required": False,
            "schema": {
                "type": "string",
                "format": "date"
            }
        },
        "EndDateParam": {
            "name": "end_date",
            "in": "query",
            "description": "End date for date range filtering (YYYY-MM-DD)",
            "required": False,
            "schema": {
                "type": "string",
                "format": "date"
            }
        }
    }
    
    # Add contact information
    openapi_schema["info"]["contact"] = {
        "name": "AGHR API Support",
        "email": "<EMAIL>",
        "url": "https://docs.aghr.com"
    }
    
    # Add license information
    openapi_schema["info"]["license"] = {
        "name": "MIT License",
        "url": "https://opensource.org/licenses/MIT"
    }
    
    # Add external documentation
    openapi_schema["externalDocs"] = {
        "description": "Complete AGHR Documentation",
        "url": "https://docs.aghr.com"
    }
    
    app.openapi_schema = openapi_schema
    return app.openapi_schema


def setup_api_documentation(app: FastAPI) -> None:
    """Setup enhanced API documentation for the FastAPI app"""
    
    # Override the default OpenAPI schema
    app.openapi = lambda: get_enhanced_openapi_schema(app)
    
    # Update app metadata
    app.title = "AGHR - Advanced Human Resource Management System"
    app.description = "Comprehensive HRMS API with advanced features"
    app.version = "1.0.0"


# Common response examples for documentation
RESPONSE_EXAMPLES = {
    "success": {
        "description": "Operation completed successfully",
        "content": {
            "application/json": {
                "example": {
                    "success": True,
                    "message": "Operation completed successfully"
                }
            }
        }
    },
    "error_400": {
        "description": "Bad Request - Invalid input data",
        "content": {
            "application/json": {
                "example": {
                    "error": {
                        "id": "550e8400-e29b-41d4-a716-446655440000",
                        "code": "INVALID_INPUT_DATA",
                        "message": "The provided data is invalid",
                        "timestamp": "2025-06-29T20:00:00Z",
                        "path": "/api/endpoint"
                    }
                }
            }
        }
    },
    "error_401": {
        "description": "Unauthorized - Authentication required",
        "content": {
            "application/json": {
                "example": {
                    "error": {
                        "id": "550e8400-e29b-41d4-a716-446655440001",
                        "code": "AUTHENTICATION_FAILED",
                        "message": "Authentication is required to access this resource",
                        "timestamp": "2025-06-29T20:00:00Z",
                        "path": "/api/endpoint"
                    }
                }
            }
        }
    },
    "error_403": {
        "description": "Forbidden - Insufficient permissions",
        "content": {
            "application/json": {
                "example": {
                    "error": {
                        "id": "550e8400-e29b-41d4-a716-446655440002",
                        "code": "INSUFFICIENT_PERMISSIONS",
                        "message": "You don't have permission to access this resource",
                        "timestamp": "2025-06-29T20:00:00Z",
                        "path": "/api/endpoint"
                    }
                }
            }
        }
    },
    "error_404": {
        "description": "Not Found - Resource not found",
        "content": {
            "application/json": {
                "example": {
                    "error": {
                        "id": "550e8400-e29b-41d4-a716-446655440003",
                        "code": "RECORD_NOT_FOUND",
                        "message": "The requested resource was not found",
                        "timestamp": "2025-06-29T20:00:00Z",
                        "path": "/api/endpoint"
                    }
                }
            }
        }
    },
    "error_422": {
        "description": "Unprocessable Entity - Validation error",
        "content": {
            "application/json": {
                "example": {
                    "error": {
                        "id": "550e8400-e29b-41d4-a716-446655440004",
                        "code": "INVALID_INPUT_DATA",
                        "message": "Validation failed",
                        "timestamp": "2025-06-29T20:00:00Z",
                        "path": "/api/endpoint",
                        "details": {
                            "validation_errors": [
                                {
                                    "field": "email",
                                    "message": "Invalid email format",
                                    "type": "value_error"
                                }
                            ]
                        }
                    }
                }
            }
        }
    },
    "error_500": {
        "description": "Internal Server Error",
        "content": {
            "application/json": {
                "example": {
                    "error": {
                        "id": "550e8400-e29b-41d4-a716-446655440005",
                        "code": "INTERNAL_ERROR",
                        "message": "An unexpected error occurred",
                        "timestamp": "2025-06-29T20:00:00Z",
                        "path": "/api/endpoint"
                    }
                }
            }
        }
    }
}
