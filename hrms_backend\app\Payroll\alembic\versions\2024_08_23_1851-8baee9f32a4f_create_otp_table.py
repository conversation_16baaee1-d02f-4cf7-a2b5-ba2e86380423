"""Create otp table

Revision ID: 8baee9f32a4f
Revises: fd506a9972c6
Create Date: 2024-08-23 18:51:05.692718

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy import func, DateTime, ForeignKey
from sqlalchemy import <PERSON>umn, Integer, String
import datetime


# revision identifiers, used by Alembic.
revision: str = '8baee9f32a4f'
down_revision: Union[str, None] = 'fd506a9972c6'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    op.create_table(
        'otps',
        Column('id', Integer, primary_key=True, autoincrement=True),
        <PERSON>umn('email', String(80), nullable=False),
        Column('user_id', Integer, ForeignKey('users.id'), nullable=False),
        <PERSON>umn('otp', String(6), nullable=False),
        Column('created_at', DateTime, default=datetime.datetime.now, nullable=False),
    )
    op.create_index('idx_email_otp', 'otps', ['email', 'otp'])

def downgrade() -> None:
    op.drop_index('idx_email_otp', table_name='otps')
    op.drop_table('otps')
