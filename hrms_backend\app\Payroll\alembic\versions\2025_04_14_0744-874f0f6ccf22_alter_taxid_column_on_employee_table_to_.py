"""Alter taxID column on employee table to String

Revision ID: 874f0f6ccf22
Revises: b8ae0542f046
Create Date: 2025-04-14 07:44:40.330607

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '874f0f6ccf22'
down_revision: Union[str, None] = 'b8ae0542f046'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    op.alter_column(
        'employees', 'taxID', existing_type=sa.Integer(),
        type_=sa.String(50), existing_nullable=True
    )


def downgrade() -> None:
    op.alter_column(
        'employees', 'taxID', existing_type=sa.String(),
        type_=sa.Integer(), existing_nullable=True
    )

