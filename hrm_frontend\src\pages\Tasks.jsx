/**
 * Tasks Management Page with RBAC Integration
 * Displays task management with role-based access control
 */

import React, { useState } from 'react';
import { Plus, Filter, Search, Calendar, Clock, CheckCircle, AlertCircle, User } from 'lucide-react';
import { usePermissions } from '../hooks/usePermissions';
import { PermissionGate, ConditionalRender } from '../components/ProtectedRoute';

export default function Tasks({ activeTab = 'my-tasks' }) {
  const permissions = usePermissions();
  const [selectedStatus, setSelectedStatus] = useState('all');
  const [selectedPriority, setSelectedPriority] = useState('all');
  const [searchTerm, setSearchTerm] = useState('');

  // Mock task data
  const tasks = [
    {
      id: 1,
      title: 'Complete user authentication module',
      description: 'Implement login, logout, and password reset functionality',
      status: 'in-progress',
      priority: 'high',
      project: 'HRMS Development',
      assignee: '<PERSON>',
      dueDate: '2024-01-20',
      createdDate: '2024-01-10',
      estimatedHours: 16,
      actualHours: 12
    },
    {
      id: 2,
      title: 'Design database schema',
      description: 'Create comprehensive database design for HR system',
      status: 'completed',
      priority: 'high',
      project: 'HRMS Development',
      assignee: 'Jane Smith',
      dueDate: '2024-01-15',
      createdDate: '2024-01-05',
      estimatedHours: 24,
      actualHours: 20
    },
    {
      id: 3,
      title: 'Write API documentation',
      description: 'Document all REST API endpoints with examples',
      status: 'todo',
      priority: 'medium',
      project: 'HRMS Development',
      assignee: 'Mike Johnson',
      dueDate: '2024-01-25',
      createdDate: '2024-01-12',
      estimatedHours: 8,
      actualHours: 0
    },
    {
      id: 4,
      title: 'Setup CI/CD pipeline',
      description: 'Configure automated testing and deployment',
      status: 'in-progress',
      priority: 'medium',
      project: 'HRMS Development',
      assignee: 'Alice Brown',
      dueDate: '2024-01-30',
      createdDate: '2024-01-15',
      estimatedHours: 12,
      actualHours: 6
    },
    {
      id: 5,
      title: 'Mobile app wireframes',
      description: 'Create wireframes for mobile application',
      status: 'review',
      priority: 'low',
      project: 'Mobile App',
      assignee: 'Bob Wilson',
      dueDate: '2024-02-05',
      createdDate: '2024-01-18',
      estimatedHours: 20,
      actualHours: 18
    }
  ];

  const getStatusColor = (status) => {
    switch (status) {
      case 'completed': return 'text-green-600 bg-green-100';
      case 'in-progress': return 'text-blue-600 bg-blue-100';
      case 'review': return 'text-purple-600 bg-purple-100';
      case 'todo': return 'text-gray-600 bg-gray-100';
      case 'blocked': return 'text-red-600 bg-red-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'completed': return <CheckCircle size={16} />;
      case 'in-progress': return <Clock size={16} />;
      case 'review': return <AlertCircle size={16} />;
      case 'todo': return <Calendar size={16} />;
      default: return <Calendar size={16} />;
    }
  };

  const getPriorityColor = (priority) => {
    switch (priority) {
      case 'high': return 'text-red-600 bg-red-100';
      case 'medium': return 'text-yellow-600 bg-yellow-100';
      case 'low': return 'text-green-600 bg-green-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const isOverdue = (dueDate) => {
    return new Date(dueDate) < new Date() && tasks.find(t => t.dueDate === dueDate)?.status !== 'completed';
  };

  const renderMyTasks = () => (
    <div className="space-y-6">
      {/* Task Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <Calendar className="h-8 w-8 text-blue-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Total Tasks</p>
              <p className="text-2xl font-bold text-gray-900">{tasks.length}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <Clock className="h-8 w-8 text-yellow-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">In Progress</p>
              <p className="text-2xl font-bold text-gray-900">
                {tasks.filter(t => t.status === 'in-progress').length}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <CheckCircle className="h-8 w-8 text-green-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Completed</p>
              <p className="text-2xl font-bold text-gray-900">
                {tasks.filter(t => t.status === 'completed').length}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <AlertCircle className="h-8 w-8 text-red-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Overdue</p>
              <p className="text-2xl font-bold text-gray-900">
                {tasks.filter(t => isOverdue(t.dueDate)).length}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Filters */}
      <div className="flex justify-between items-center">
        <div className="flex space-x-4">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={16} />
            <input
              type="text"
              placeholder="Search tasks..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 pr-4 py-2 border border-gray-300 rounded-md"
            />
          </div>
          <select 
            value={selectedStatus} 
            onChange={(e) => setSelectedStatus(e.target.value)}
            className="border border-gray-300 rounded-md px-3 py-2"
          >
            <option value="all">All Status</option>
            <option value="todo">To Do</option>
            <option value="in-progress">In Progress</option>
            <option value="review">Review</option>
            <option value="completed">Completed</option>
          </select>
          <select 
            value={selectedPriority} 
            onChange={(e) => setSelectedPriority(e.target.value)}
            className="border border-gray-300 rounded-md px-3 py-2"
          >
            <option value="all">All Priority</option>
            <option value="high">High</option>
            <option value="medium">Medium</option>
            <option value="low">Low</option>
          </select>
        </div>
        <PermissionGate permission="taskManagement">
          <button className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 flex items-center space-x-2">
            <Plus size={16} />
            <span>New Task</span>
          </button>
        </PermissionGate>
      </div>

      {/* Tasks List */}
      <div className="bg-white rounded-lg shadow overflow-hidden">
        <div className="divide-y divide-gray-200">
          {tasks.map((task) => (
            <div key={task.id} className="p-6 hover:bg-gray-50">
              <div className="flex items-center justify-between">
                <div className="flex-1">
                  <div className="flex items-center space-x-3">
                    <h3 className="text-lg font-medium text-gray-900">{task.title}</h3>
                    <span className={`inline-flex items-center space-x-1 px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(task.status)}`}>
                      {getStatusIcon(task.status)}
                      <span className="capitalize">{task.status.replace('-', ' ')}</span>
                    </span>
                    <span className={`inline-flex px-2 py-1 rounded-full text-xs font-medium ${getPriorityColor(task.priority)}`}>
                      {task.priority}
                    </span>
                    {isOverdue(task.dueDate) && (
                      <span className="inline-flex px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-600">
                        Overdue
                      </span>
                    )}
                  </div>
                  <p className="mt-1 text-sm text-gray-600">{task.description}</p>
                  <div className="mt-2 flex items-center space-x-6 text-sm text-gray-500">
                    <div className="flex items-center space-x-1">
                      <User size={14} />
                      <span>{task.assignee}</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <Calendar size={14} />
                      <span>Due: {task.dueDate}</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <Clock size={14} />
                      <span>{task.actualHours}h / {task.estimatedHours}h</span>
                    </div>
                    <span className="text-blue-600">{task.project}</span>
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  <button className="text-blue-600 hover:text-blue-900 text-sm">
                    View
                  </button>
                  <button className="text-gray-600 hover:text-gray-900 text-sm">
                    Edit
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );

  const renderTeamTasks = () => (
    <PermissionGate permission="taskManagement">
      <div className="space-y-6">
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Team Task Management</h3>
          <p className="text-gray-600">Manage and assign tasks to team members...</p>
        </div>
      </div>
    </PermissionGate>
  );

  const renderContent = () => {
    switch (activeTab) {
      case 'my-tasks':
        return renderMyTasks();
      case 'team-tasks':
        return renderTeamTasks();
      default:
        return renderMyTasks();
    }
  };

  return (
    <div className="p-6">
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-gray-900">Task Management</h1>
        <p className="text-gray-600">Manage your tasks and track progress</p>
      </div>

      {renderContent()}
    </div>
  );
}
