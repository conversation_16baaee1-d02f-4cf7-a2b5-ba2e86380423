from core.databases.database import db
from core.models.basemodel import ModelBase
from datetime import datetime, timezone


class ProrateSalaryModel(ModelBase):
    __tablename__ = "prorate_salary"

    id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    first_name = db.Column(db.String(45), nullable=True)
    last_name = db.Column(db.String(45), nullable=True)
    email = db.Column(db.String(100), nullable=True)
        
    is_prorated = db.Column(db.<PERSON><PERSON><PERSON>, default=True, nullable=True)
    
    gross_pay = db.Column(db.Float, nullable=True)
    monthly_tax = db.Column(db.Float, nullable=True)
    annual_tax = db.Column(db.Float, nullable=True)
    total_taxable_monthly_sum = db.Column(db.Float, nullable=True)
    total_taxable_annual_sum = db.Column(db.Float, nullable=True)
    total_non_taxable_monthly_sum = db.Column(db.Float, nullable=True)
    total_non_taxable_annual_sum = db.Column(db.Float, nullable=True)
    total_statutory_monthly_sum = db.Column(db.Float, nullable=True)
    total_statutory_annual_sum = db.Column(db.Float, nullable=True)
    total_other_deductions_monthly_sum = db.Column(db.Float, nullable=True)
    total_other_deductions_annual_sum = db.Column(db.Float, nullable=True)
    netpay = db.Column(db.Float, nullable=True)
    
    user_id = db.Column(db.Integer, db.ForeignKey((ModelBase.dbSchema() + '.users.id')), nullable=True) 
    # timestamp = db.Column(db.DateTime, nullable=False, default=datetime.now())
    timestamp = db.Column(db.DateTime, nullable=False, default=lambda: datetime.now(timezone.utc))
    # employees = db.relationship(
    #     'EmployeeModel', 
    #     backref='prorate_salary',
    #     single_parent=True  # Add this if a single EmployeeModel should be associated with a ProrateSalaryModel at a time.
    # )
    employee_id = db.Column(db.Integer, db.ForeignKey((ModelBase.dbSchema() + '.employees.id')), nullable=False) 
    # employee = db.relationship('EmployeeModel', back_populates='prorated_salaries')
    
    tax_overtime = db.Column(db.Boolean, default=False, nullable=True)
    overtime_hours = db.Column(db.Float, default=0.0, nullable=True)
    overtime_rate = db.Column(db.Float, default=0.0, nullable=True)
    overtime_amount = db.Column(db.Float, default=0.0, nullable=True)
    salary_arrears = db.Column(db.Float, default=0.0, nullable=True)
    surcharge = db.Column(db.Float, default=0.0, nullable=True)