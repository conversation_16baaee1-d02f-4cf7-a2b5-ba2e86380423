#!/usr/bin/env python3
"""
Add role column to employees table
"""

import sys
import os

# Add the app directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'app'))

from sqlalchemy import text
from app.db.session import SessionLocal, engine

def add_role_column():
    """Add role column to employees table"""
    db = SessionLocal()
    
    try:
        print("Adding role column to employees table...")

        # Check if role column already exists
        check_query = """
        SELECT column_name
        FROM information_schema.columns
        WHERE table_name='employees' AND column_name='role';
        """

        result = db.execute(text(check_query))
        if result.fetchone():
            print("✅ Role column already exists!")
            return

        # Check existing enum types
        enum_query = """
        SELECT typname, enumlabel
        FROM pg_type t
        JOIN pg_enum e ON t.oid = e.enumtypid
        WHERE typname = 'role'
        ORDER BY enumsortorder;
        """

        result = db.execute(text(enum_query))
        existing_enums = result.fetchall()
        if existing_enums:
            print(f"Found existing role enum with values: {[row[1] for row in existing_enums]}")
            # Drop the existing enum type first
            drop_enum_query = "DROP TYPE IF EXISTS role CASCADE;"
            db.execute(text(drop_enum_query))
            db.commit()
            print("✅ Dropped existing role enum type")
        
        # Create the role enum type if it doesn't exist
        create_enum_query = """
        DO $$ BEGIN
            CREATE TYPE role AS ENUM ('admin', 'hr', 'manager', 'employee', 'supervisor');
        EXCEPTION
            WHEN duplicate_object THEN null;
        END $$;
        """
        
        db.execute(text(create_enum_query))
        db.commit()
        print("✅ Role enum type created")
        
        # Add the role column with proper enum value
        add_column_query = """
        ALTER TABLE employees
        ADD COLUMN role role NOT NULL DEFAULT 'employee'::role;
        """
        
        db.execute(text(add_column_query))
        db.commit()
        print("✅ Role column added to employees table")
        
        # Update existing employees with appropriate roles
        update_queries = [
            "UPDATE employees SET role = 'admin'::role WHERE employee_id = 'EMP001';",
            "UPDATE employees SET role = 'hr'::role WHERE employee_id = 'EMP002';",
            "UPDATE employees SET role = 'manager'::role WHERE employee_id = 'EMP004';",
            "UPDATE employees SET role = 'employee'::role WHERE role = 'employee'::role;"  # Default for others
        ]
        
        for query in update_queries:
            try:
                db.execute(text(query))
                db.commit()
            except Exception as e:
                print(f"Warning: {query} - {e}")
                db.rollback()
        
        print("✅ Existing employee roles updated")
        print("\n🎉 Role column migration completed successfully!")
        
    except Exception as e:
        print(f"❌ Error adding role column: {e}")
        db.rollback()
        raise
    finally:
        db.close()

if __name__ == "__main__":
    add_role_column()
