"""Added overtime, salary arrears, and surcharge columns to payroll history

Revision ID: ba4198c6bdd1
Revises: 3cbe2d0913fc
Create Date: 2025-03-30 15:32:41.731944

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'ba4198c6bdd1'
down_revision: Union[str, None] = '3cbe2d0913fc'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    op.add_column('payroll_history', sa.Column('tax_overtime', sa.<PERSON>an(), nullable=True, server_default=sa.text("false")))
    op.add_column('payroll_history', sa.Column('overtime_hours', sa.Float(), nullable=True, server_default="0.0"))
    op.add_column('payroll_history', sa.Column('overtime_rate', sa.Float(), nullable=True, server_default="0.0"))
    op.add_column('payroll_history', sa.Column('overtime_amount', sa.Float(), nullable=True, server_default="0.0"))
    op.add_column('payroll_history', sa.Column('salary_arrears', sa.Float(), nullable=True, server_default="0.0"))
    op.add_column('payroll_history', sa.Column('surcharge', sa.Float(), nullable=True, server_default="0.0"))

def downgrade():
    # Drop the columns if we need to revert the migration
    op.drop_column('payroll_history', 'tax_overtime')
    op.drop_column('payroll_history', 'overtime_hours')
    op.drop_column('payroll_history', 'overtime_rate')
    op.drop_column('payroll_history', 'overtime_amount')
    op.drop_column('payroll_history', 'salary_arrears')
    op.drop_column('payroll_history', 'surcharge')

