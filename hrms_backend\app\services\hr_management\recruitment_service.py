from typing import Optional, List, Dict, Any, Tuple
from sqlalchemy.orm import Session, joinedload
from sqlalchemy import and_, or_, func, desc, asc
from fastapi import HTTPException, status
from uuid import UUID
from datetime import datetime, date
import logging

from ...db.models.recruitment import (
    JobPosting, Candidate, JobApplication, Interview, JobOffer,
    JobStatus, ApplicationStatus, InterviewStatus
)
from ...db.models.employee import Employee, Department
from ...schemas.recruitment import (
    JobPostingCreate, JobPostingUpdate, CandidateCreate, CandidateUpdate,
    JobApplicationCreate, JobApplicationUpdate, InterviewCreate, InterviewUpdate,
    JobOfferCreate, JobOfferUpdate
)
from ...core.security import CurrentUser
from ...core.audit_logger import AuditLogger

logger = logging.getLogger(__name__)


class RecruitmentService:
    """Service for recruitment management"""

    # Job Posting Methods
    async def create_job_posting(
        self,
        db: Session,
        job_data: JobPostingCreate,
        current_user: CurrentUser
    ) -> JobPosting:
        """Create a new job posting"""
        try:
            # Verify department exists
            department = db.query(Department).filter(Department.id == job_data.department_id).first()
            if not department:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Department not found"
                )

            # Create job posting
            job_posting = JobPosting(
                **job_data.dict(),
                organization_id=current_user.organization_id,
                status=JobStatus.DRAFT
            )

            db.add(job_posting)
            db.commit()
            db.refresh(job_posting)

            # Log audit event
            AuditLogger.log_action(
                db=db,
                action="CREATE",
                resource_type="job_postings",
                resource_id=str(job_posting.id),
                user=current_user,
                new_values=job_data.dict()
            )

            return job_posting

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error creating job posting: {e}")
            db.rollback()
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to create job posting"
            )

    async def get_job_postings(
        self,
        db: Session,
        skip: int = 0,
        limit: int = 20,
        status: Optional[JobStatus] = None,
        department_id: Optional[UUID] = None,
        job_type: Optional[str] = None,
        search: Optional[str] = None,
        current_user: CurrentUser = None
    ) -> Tuple[List[JobPosting], int]:
        """Get job postings with filtering"""
        try:
            query = db.query(JobPosting).options(
                joinedload(JobPosting.department),
                joinedload(JobPosting.hiring_manager),
                joinedload(JobPosting.recruiter)
            )

            # Apply organization filter
            if current_user and current_user.organization_id:
                query = query.filter(JobPosting.organization_id == current_user.organization_id)

            # Apply filters
            if status:
                query = query.filter(JobPosting.status == status)
            
            if department_id:
                query = query.filter(JobPosting.department_id == department_id)
            
            if job_type:
                query = query.filter(JobPosting.job_type == job_type)
            
            if search:
                search_filter = or_(
                    JobPosting.title.ilike(f"%{search}%"),
                    JobPosting.description.ilike(f"%{search}%"),
                    JobPosting.location.ilike(f"%{search}%")
                )
                query = query.filter(search_filter)

            # Get total count
            total = query.count()

            # Apply pagination and ordering
            job_postings = query.order_by(desc(JobPosting.created_at)).offset(skip).limit(limit).all()

            return job_postings, total

        except Exception as e:
            logger.error(f"Error getting job postings: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to retrieve job postings"
            )

    async def get_job_posting(self, db: Session, job_id: UUID) -> JobPosting:
        """Get a specific job posting"""
        job_posting = db.query(JobPosting).options(
            joinedload(JobPosting.department),
            joinedload(JobPosting.hiring_manager),
            joinedload(JobPosting.recruiter),
            joinedload(JobPosting.applications)
        ).filter(JobPosting.id == job_id).first()

        if not job_posting:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Job posting not found"
            )

        return job_posting

    async def update_job_posting(
        self,
        db: Session,
        job_id: UUID,
        job_data: JobPostingUpdate,
        current_user: CurrentUser
    ) -> JobPosting:
        """Update a job posting"""
        try:
            job_posting = await self.get_job_posting(db, job_id)

            # Store old values for audit
            old_values = {
                "title": job_posting.title,
                "status": job_posting.status.value if job_posting.status else None,
                "description": job_posting.description
            }

            # Update fields
            update_data = job_data.dict(exclude_unset=True)
            for field, value in update_data.items():
                setattr(job_posting, field, value)

            db.commit()
            db.refresh(job_posting)

            # Log audit event
            AuditLogger.log_action(
                db=db,
                action="UPDATE",
                resource_type="job_postings",
                resource_id=str(job_posting.id),
                user=current_user,
                old_values=old_values,
                new_values=update_data
            )

            return job_posting

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error updating job posting: {e}")
            db.rollback()
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to update job posting"
            )

    async def publish_job_posting(
        self,
        db: Session,
        job_id: UUID,
        current_user: CurrentUser
    ) -> JobPosting:
        """Publish a job posting"""
        try:
            job_posting = await self.get_job_posting(db, job_id)

            if job_posting.status != JobStatus.DRAFT:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Only draft job postings can be published"
                )

            job_posting.status = JobStatus.ACTIVE
            job_posting.posted_date = date.today()

            db.commit()
            db.refresh(job_posting)

            # Log audit event
            AuditLogger.log_action(
                db=db,
                action="PUBLISH",
                resource_type="job_postings",
                resource_id=str(job_posting.id),
                user=current_user,
                new_values={"status": "active", "posted_date": str(date.today())}
            )

            return job_posting

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error publishing job posting: {e}")
            db.rollback()
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to publish job posting"
            )

    # Candidate Methods
    async def create_candidate(
        self,
        db: Session,
        candidate_data: CandidateCreate,
        current_user: CurrentUser
    ) -> Candidate:
        """Create a new candidate"""
        try:
            # Check if candidate with email already exists
            existing_candidate = db.query(Candidate).filter(
                Candidate.email == candidate_data.email
            ).first()

            if existing_candidate:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Candidate with this email already exists"
                )

            # Create candidate
            candidate = Candidate(
                **candidate_data.dict(),
                organization_id=current_user.organization_id
            )

            db.add(candidate)
            db.commit()
            db.refresh(candidate)

            # Log audit event
            AuditLogger.log_action(
                db=db,
                action="CREATE",
                resource_type="candidates",
                resource_id=str(candidate.id),
                user=current_user,
                new_values=candidate_data.dict()
            )

            return candidate

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error creating candidate: {e}")
            db.rollback()
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to create candidate"
            )

    async def get_candidates(
        self,
        db: Session,
        skip: int = 0,
        limit: int = 20,
        search: Optional[str] = None,
        skills: Optional[List[str]] = None,
        experience_min: Optional[int] = None,
        experience_max: Optional[int] = None,
        current_user: CurrentUser = None
    ) -> Tuple[List[Candidate], int]:
        """Get candidates with filtering"""
        try:
            query = db.query(Candidate)

            # Apply organization filter
            if current_user and current_user.organization_id:
                query = query.filter(Candidate.organization_id == current_user.organization_id)

            # Apply filters
            if search:
                search_filter = or_(
                    Candidate.first_name.ilike(f"%{search}%"),
                    Candidate.last_name.ilike(f"%{search}%"),
                    Candidate.email.ilike(f"%{search}%"),
                    Candidate.current_position.ilike(f"%{search}%"),
                    Candidate.current_company.ilike(f"%{search}%")
                )
                query = query.filter(search_filter)

            if skills:
                # Filter by skills (assuming skills is stored as JSON array)
                for skill in skills:
                    query = query.filter(Candidate.skills.contains([skill]))

            if experience_min is not None:
                query = query.filter(Candidate.total_experience >= experience_min)

            if experience_max is not None:
                query = query.filter(Candidate.total_experience <= experience_max)

            # Get total count
            total = query.count()

            # Apply pagination and ordering
            candidates = query.order_by(desc(Candidate.created_at)).offset(skip).limit(limit).all()

            return candidates, total

        except Exception as e:
            logger.error(f"Error getting candidates: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to retrieve candidates"
            )

    async def get_candidate(self, db: Session, candidate_id: UUID) -> Candidate:
        """Get a specific candidate"""
        candidate = db.query(Candidate).options(
            joinedload(Candidate.applications)
        ).filter(Candidate.id == candidate_id).first()

        if not candidate:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Candidate not found"
            )

        return candidate

    # Job Application Methods
    async def create_job_application(
        self,
        db: Session,
        application_data: JobApplicationCreate,
        current_user: CurrentUser
    ) -> JobApplication:
        """Create a new job application"""
        try:
            # Verify job posting and candidate exist
            job_posting = await self.get_job_posting(db, application_data.job_posting_id)
            candidate = await self.get_candidate(db, application_data.candidate_id)

            # Check if application already exists
            existing_application = db.query(JobApplication).filter(
                and_(
                    JobApplication.job_posting_id == application_data.job_posting_id,
                    JobApplication.candidate_id == application_data.candidate_id
                )
            ).first()

            if existing_application:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Application already exists for this job and candidate"
                )

            # Create application
            application = JobApplication(
                **application_data.dict(),
                organization_id=current_user.organization_id,
                status=ApplicationStatus.APPLIED
            )

            db.add(application)
            db.commit()
            db.refresh(application)

            # Log audit event
            AuditLogger.log_action(
                db=db,
                action="CREATE",
                resource_type="job_applications",
                resource_id=str(application.id),
                user=current_user,
                new_values=application_data.dict()
            )

            return application

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error creating job application: {e}")
            db.rollback()
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to create job application"
            )

    async def update_application_status(
        self,
        db: Session,
        application_id: UUID,
        new_status: ApplicationStatus,
        current_user: CurrentUser,
        notes: Optional[str] = None
    ) -> JobApplication:
        """Update job application status"""
        try:
            application = db.query(JobApplication).filter(
                JobApplication.id == application_id
            ).first()

            if not application:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Job application not found"
                )

            old_status = application.status
            application.status = new_status
            application.last_updated = datetime.utcnow()

            if notes:
                application.recruiter_notes = notes

            db.commit()
            db.refresh(application)

            # Log audit event
            AuditLogger.log_action(
                db=db,
                action="STATUS_UPDATE",
                resource_type="job_applications",
                resource_id=str(application.id),
                user=current_user,
                old_values={"status": old_status.value if old_status else None},
                new_values={"status": new_status.value, "notes": notes}
            )

            return application

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error updating application status: {e}")
            db.rollback()
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to update application status"
            )

    # Interview Methods
    async def schedule_interview(
        self,
        db: Session,
        interview_data: InterviewCreate,
        current_user: CurrentUser
    ) -> Interview:
        """Schedule an interview"""
        try:
            # Verify application exists
            application = db.query(JobApplication).filter(
                JobApplication.id == interview_data.application_id
            ).first()

            if not application:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Job application not found"
                )

            # Verify interviewers exist
            interviewers = db.query(Employee).filter(
                Employee.id.in_(interview_data.interviewer_ids)
            ).all()

            if len(interviewers) != len(interview_data.interviewer_ids):
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="One or more interviewers not found"
                )

            # Create interview
            interview = Interview(
                **interview_data.dict(),
                organization_id=current_user.organization_id,
                status=InterviewStatus.SCHEDULED
            )

            db.add(interview)
            db.commit()
            db.refresh(interview)

            # Update application status
            application.status = ApplicationStatus.INTERVIEW_SCHEDULED
            application.last_updated = datetime.utcnow()
            db.commit()

            # Log audit event
            AuditLogger.log_action(
                db=db,
                action="SCHEDULE",
                resource_type="interviews",
                resource_id=str(interview.id),
                user=current_user,
                new_values=interview_data.dict()
            )

            return interview

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error scheduling interview: {e}")
            db.rollback()
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to schedule interview"
            )

    # Job Offer Methods
    async def create_job_offer(
        self,
        db: Session,
        offer_data: JobOfferCreate,
        current_user: CurrentUser
    ) -> JobOffer:
        """Create a job offer"""
        try:
            # Verify application exists
            application = db.query(JobApplication).filter(
                JobApplication.id == offer_data.application_id
            ).first()

            if not application:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Job application not found"
                )

            # Verify department exists
            department = db.query(Department).filter(
                Department.id == offer_data.department_id
            ).first()

            if not department:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Department not found"
                )

            # Create job offer
            job_offer = JobOffer(
                **offer_data.dict(),
                organization_id=current_user.organization_id,
                status="pending"
            )

            db.add(job_offer)
            db.commit()
            db.refresh(job_offer)

            # Update application status
            application.status = ApplicationStatus.OFFER_EXTENDED
            application.last_updated = datetime.utcnow()
            db.commit()

            # Log audit event
            AuditLogger.log_action(
                db=db,
                action="CREATE",
                resource_type="job_offers",
                resource_id=str(job_offer.id),
                user=current_user,
                new_values=offer_data.dict()
            )

            return job_offer

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error creating job offer: {e}")
            db.rollback()
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to create job offer"
            )
