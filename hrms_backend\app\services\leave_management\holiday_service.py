"""
Holiday Management Service for business logic
"""

from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, func
from typing import Optional, List, Dict
from uuid import UUID
from datetime import datetime, date, timedelta
from fastapi import HTTPException, status
import logging
from calendar import monthrange

from ...db.models.leave import (
    LeaveCalendar, WeeklyOffPattern, HolidayConflict, LocationCalendar, LeaveRequest
)
from ...db.models.employee import Employee
from ...db.models.attendance import AttendanceRecord
from ...schemas.leave import (
    LeaveCalendarCreate, LeaveCalendarUpdate, LeaveCalendarResponse
)
from ...core.security import CurrentUser

logger = logging.getLogger(__name__)


class HolidayService:
    """Holiday service for business logic"""

    async def get_holidays(
        self,
        db: Session,
        current_user: CurrentUser,
        start_date: Optional[date] = None,
        end_date: Optional[date] = None,
        location: Optional[str] = None,
        holiday_type: Optional[str] = None,
        include_weekly_offs: bool = False
    ) -> List[Dict]:
        """Get holidays with filtering and weekly offs"""
        try:
            # Default date range to current year if not provided
            if not start_date:
                start_date = date(date.today().year, 1, 1)
            if not end_date:
                end_date = date(date.today().year, 12, 31)

            holidays = []

            # Get regular holidays
            query = db.query(LeaveCalendar).filter(
                LeaveCalendar.organization_id == current_user.organization_id,
                LeaveCalendar.date >= start_date,
                LeaveCalendar.date <= end_date
            )

            if location:
                query = query.filter(
                    or_(
                        LeaveCalendar.applicable_locations.is_(None),
                        LeaveCalendar.applicable_locations.contains([location])
                    )
                )

            if holiday_type:
                query = query.filter(LeaveCalendar.holiday_type == holiday_type)

            regular_holidays = query.all()
            
            for holiday in regular_holidays:
                holidays.append({
                    "id": str(holiday.id),
                    "date": holiday.date.isoformat(),
                    "name": holiday.name,
                    "description": holiday.description,
                    "type": "holiday",
                    "holiday_type": holiday.holiday_type,
                    "is_optional": holiday.is_optional,
                    "applicable_locations": holiday.applicable_locations or [],
                    "applicable_departments": holiday.applicable_departments or []
                })

            # Add weekly offs if requested
            if include_weekly_offs:
                weekly_offs = await self._get_weekly_offs_in_range(
                    db, current_user, start_date, end_date, location
                )
                holidays.extend(weekly_offs)

            # Sort by date
            holidays.sort(key=lambda x: x["date"])
            return holidays

        except Exception as e:
            logger.error(f"Error getting holidays: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Error retrieving holidays"
            )

    async def create_holiday(
        self,
        db: Session,
        holiday_data: LeaveCalendarCreate,
        current_user: CurrentUser
    ) -> LeaveCalendarResponse:
        """Create a new holiday"""
        try:
            # Check for existing holiday on the same date
            existing = db.query(LeaveCalendar).filter(
                LeaveCalendar.organization_id == current_user.organization_id,
                LeaveCalendar.date == holiday_data.date
            ).first()

            if existing:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Holiday already exists on this date"
                )

            holiday = LeaveCalendar(
                **holiday_data.model_dump(),
                organization_id=current_user.organization_id,
                created_by=current_user.user_id
            )

            db.add(holiday)
            db.commit()
            db.refresh(holiday)

            # Check for conflicts
            await self._check_holiday_conflicts(db, holiday, current_user)

            logger.info(f"Holiday created: {holiday.id} for {holiday.date}")
            return LeaveCalendarResponse.from_orm(holiday)

        except HTTPException:
            raise
        except Exception as e:
            db.rollback()
            logger.error(f"Error creating holiday: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Error creating holiday"
            )

    async def _get_weekly_offs_in_range(
        self,
        db: Session,
        current_user: CurrentUser,
        start_date: date,
        end_date: date,
        location: Optional[str] = None
    ) -> List[Dict]:
        """Get weekly offs in date range"""
        try:
            weekly_offs = []
            
            # Get applicable weekly off pattern
            pattern = await self._get_weekly_off_pattern(db, current_user, location)
            if not pattern:
                return weekly_offs

            current_date = start_date
            while current_date <= end_date:
                if await self._is_weekly_off(current_date, pattern):
                    weekly_offs.append({
                        "date": current_date.isoformat(),
                        "name": "Weekly Off",
                        "description": f"Weekly off as per {pattern.name}",
                        "type": "weekly_off",
                        "holiday_type": "weekly_off",
                        "is_optional": False,
                        "pattern_name": pattern.name
                    })
                current_date += timedelta(days=1)

            return weekly_offs

        except Exception as e:
            logger.error(f"Error getting weekly offs: {e}")
            return []

    async def _get_weekly_off_pattern(
        self,
        db: Session,
        current_user: CurrentUser,
        location: Optional[str] = None
    ) -> Optional[WeeklyOffPattern]:
        """Get applicable weekly off pattern"""
        try:
            query = db.query(WeeklyOffPattern).filter(
                WeeklyOffPattern.organization_id == current_user.organization_id,
                WeeklyOffPattern.is_active == True,
                WeeklyOffPattern.effective_from <= date.today()
            ).filter(
                or_(
                    WeeklyOffPattern.effective_until.is_(None),
                    WeeklyOffPattern.effective_until >= date.today()
                )
            )

            if location:
                query = query.filter(
                    or_(
                        WeeklyOffPattern.applicable_locations.is_(None),
                        WeeklyOffPattern.applicable_locations.contains([location])
                    )
                )

            # Get the most specific pattern (location-specific over general)
            pattern = query.filter(
                WeeklyOffPattern.applicable_locations.isnot(None)
            ).first()

            if not pattern:
                # Fall back to default pattern
                pattern = query.filter(
                    WeeklyOffPattern.is_default == True
                ).first()

            return pattern

        except Exception as e:
            logger.error(f"Error getting weekly off pattern: {e}")
            return None

    async def _is_weekly_off(self, check_date: date, pattern: WeeklyOffPattern) -> bool:
        """Check if a date is a weekly off based on pattern"""
        try:
            weekday = check_date.weekday()  # 0=Monday, 6=Sunday
            
            if pattern.pattern_type == "fixed":
                return weekday in pattern.weekly_off_days

            elif pattern.pattern_type == "rotating":
                # Calculate which week in the rotation cycle
                start_date = pattern.effective_from
                days_since_start = (check_date - start_date).days
                week_in_cycle = (days_since_start // 7) % pattern.rotation_weeks
                
                # Get the weekly off days for this week in the cycle
                rotation_schedule = pattern.rotation_schedule or {}
                week_offs = rotation_schedule.get(str(week_in_cycle), [])
                return weekday in week_offs

            elif pattern.pattern_type == "alternate":
                # Alternate Saturdays or similar
                week_number = check_date.isocalendar()[1]
                if weekday in pattern.weekly_off_days:
                    # Check if it's an alternate week
                    return week_number % 2 == 0  # Even weeks

            return False

        except Exception as e:
            logger.error(f"Error checking weekly off: {e}")
            return False

    async def _check_holiday_conflicts(
        self,
        db: Session,
        holiday: LeaveCalendar,
        current_user: CurrentUser
    ):
        """Check for conflicts with leaves and attendance"""
        try:
            conflicts = []

            # Check for leave requests on this date
            leave_conflicts = db.query(LeaveRequest).join(Employee).filter(
                Employee.organization_id == current_user.organization_id,
                LeaveRequest.start_date <= holiday.date,
                LeaveRequest.end_date >= holiday.date,
                LeaveRequest.status.in_(["approved", "pending"])
            ).all()

            for leave_request in leave_conflicts:
                conflict = HolidayConflict(
                    organization_id=current_user.organization_id,
                    conflict_date=holiday.date,
                    conflict_type="leave_overlap",
                    holiday_id=holiday.id,
                    leave_request_id=leave_request.id,
                    employee_id=leave_request.employee_id,
                    description=f"Leave request overlaps with holiday {holiday.name}",
                    severity="medium"
                )
                conflicts.append(conflict)

            # Check for attendance records on this date
            attendance_conflicts = db.query(AttendanceRecord).join(Employee).filter(
                Employee.organization_id == current_user.organization_id,
                AttendanceRecord.date == holiday.date,
                AttendanceRecord.status == "present"
            ).all()

            for attendance in attendance_conflicts:
                conflict = HolidayConflict(
                    organization_id=current_user.organization_id,
                    conflict_date=holiday.date,
                    conflict_type="attendance_on_holiday",
                    holiday_id=holiday.id,
                    employee_id=attendance.employee_id,
                    description=f"Employee marked present on holiday {holiday.name}",
                    severity="low"
                )
                conflicts.append(conflict)

            # Save conflicts
            if conflicts:
                db.add_all(conflicts)
                db.commit()
                logger.info(f"Found {len(conflicts)} conflicts for holiday {holiday.id}")

        except Exception as e:
            logger.error(f"Error checking holiday conflicts: {e}")

    async def get_holiday_conflicts(
        self,
        db: Session,
        current_user: CurrentUser,
        start_date: Optional[date] = None,
        end_date: Optional[date] = None,
        severity: Optional[str] = None,
        status: Optional[str] = None
    ) -> List[Dict]:
        """Get holiday conflicts"""
        try:
            query = db.query(HolidayConflict).filter(
                HolidayConflict.organization_id == current_user.organization_id
            )

            if start_date:
                query = query.filter(HolidayConflict.conflict_date >= start_date)
            if end_date:
                query = query.filter(HolidayConflict.conflict_date <= end_date)
            if severity:
                query = query.filter(HolidayConflict.severity == severity)
            if status:
                query = query.filter(HolidayConflict.status == status)

            conflicts = query.all()
            
            return [
                {
                    "id": str(conflict.id),
                    "conflict_date": conflict.conflict_date.isoformat(),
                    "conflict_type": conflict.conflict_type,
                    "description": conflict.description,
                    "severity": conflict.severity,
                    "status": conflict.status,
                    "holiday_id": str(conflict.holiday_id) if conflict.holiday_id else None,
                    "leave_request_id": str(conflict.leave_request_id) if conflict.leave_request_id else None,
                    "employee_id": str(conflict.employee_id) if conflict.employee_id else None,
                    "created_at": conflict.created_at.isoformat()
                }
                for conflict in conflicts
            ]

        except Exception as e:
            logger.error(f"Error getting holiday conflicts: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Error retrieving holiday conflicts"
            )


# Create service instance
holiday_service = HolidayService()
