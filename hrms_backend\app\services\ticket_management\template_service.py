from sqlalchemy.orm import Session, joinedload
from sqlalchemy import and_, or_, func
from typing import Optional, List, Dict, Any
from uuid import UUID
from datetime import datetime
from fastapi import HTTPException, status
import logging
import json

from ...db.models.ticket import (
    Ticket, TicketTemplate, TicketCategory, TicketActivity,
    TicketStatus, TicketPriority, TicketType
)
from ...db.models.employee import Employee
from ...core.security import CurrentUser
from ...core.audit_logger import AuditLogger

logger = logging.getLogger(__name__)


class TicketTemplateService:
    """Service for managing ticket templates"""

    async def create_template(
        self,
        db: Session,
        template_data: Dict[str, Any],
        current_user: CurrentUser
    ) -> TicketTemplate:
        """Create a new ticket template"""
        try:
            template = TicketTemplate(
                name=template_data["name"],
                description=template_data.get("description"),
                organization_id=current_user.organization_id,
                ticket_type=TicketType(template_data["ticket_type"]),
                category_id=template_data.get("category_id"),
                default_title=template_data.get("default_title"),
                default_description=template_data.get("default_description"),
                default_priority=TicketPriority(template_data.get("default_priority", TicketPriority.MEDIUM)),
                custom_fields=template_data.get("custom_fields", []),
                is_public=template_data.get("is_public", True),
                requires_approval=template_data.get("requires_approval", False),
                usage_count=0,
                created_by=current_user.user_id
            )

            db.add(template)
            db.commit()
            db.refresh(template)

            # Log template creation
            await AuditLogger.log_action(
                db, current_user.user_id, "template_created",
                f"Created ticket template: {template.name}",
                {"template_id": str(template.id)}
            )

            logger.info(f"Ticket template {template.name} created by {current_user.user_id}")
            return template

        except Exception as e:
            db.rollback()
            logger.error(f"Error creating ticket template: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Error creating ticket template"
            )

    async def get_templates(
        self,
        db: Session,
        current_user: CurrentUser,
        ticket_type: Optional[TicketType] = None,
        category_id: Optional[UUID] = None,
        is_public: Optional[bool] = None,
        skip: int = 0,
        limit: int = 20
    ) -> List[TicketTemplate]:
        """Get ticket templates with filtering"""
        try:
            query = db.query(TicketTemplate).filter(
                TicketTemplate.organization_id == current_user.organization_id
            )

            if ticket_type:
                query = query.filter(TicketTemplate.ticket_type == ticket_type)

            if category_id:
                query = query.filter(TicketTemplate.category_id == category_id)

            if is_public is not None:
                query = query.filter(TicketTemplate.is_public == is_public)

            templates = query.order_by(TicketTemplate.usage_count.desc()).offset(skip).limit(limit).all()
            return templates

        except Exception as e:
            logger.error(f"Error getting ticket templates: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Error retrieving ticket templates"
            )

    async def get_template(
        self,
        db: Session,
        template_id: UUID,
        current_user: CurrentUser
    ) -> Optional[TicketTemplate]:
        """Get a specific ticket template"""
        try:
            template = db.query(TicketTemplate).filter(
                TicketTemplate.id == template_id,
                TicketTemplate.organization_id == current_user.organization_id
            ).first()

            return template

        except Exception as e:
            logger.error(f"Error getting ticket template {template_id}: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Error retrieving ticket template"
            )

    async def update_template(
        self,
        db: Session,
        template_id: UUID,
        template_data: Dict[str, Any],
        current_user: CurrentUser
    ) -> Optional[TicketTemplate]:
        """Update a ticket template"""
        try:
            template = db.query(TicketTemplate).filter(
                TicketTemplate.id == template_id,
                TicketTemplate.organization_id == current_user.organization_id
            ).first()

            if not template:
                return None

            # Update fields
            for field, value in template_data.items():
                if hasattr(template, field) and field not in ['id', 'organization_id', 'usage_count', 'created_by', 'created_at']:
                    if field == "ticket_type" and value:
                        setattr(template, field, TicketType(value))
                    elif field == "default_priority" and value:
                        setattr(template, field, TicketPriority(value))
                    else:
                        setattr(template, field, value)

            template.updated_by = current_user.user_id
            template.updated_at = datetime.utcnow()

            db.commit()
            db.refresh(template)

            # Log template update
            await AuditLogger.log_action(
                db, current_user.user_id, "template_updated",
                f"Updated ticket template: {template.name}",
                {"template_id": str(template.id)}
            )

            logger.info(f"Ticket template {template.name} updated by {current_user.user_id}")
            return template

        except Exception as e:
            db.rollback()
            logger.error(f"Error updating ticket template {template_id}: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Error updating ticket template"
            )

    async def delete_template(
        self,
        db: Session,
        template_id: UUID,
        current_user: CurrentUser
    ) -> bool:
        """Delete a ticket template"""
        try:
            template = db.query(TicketTemplate).filter(
                TicketTemplate.id == template_id,
                TicketTemplate.organization_id == current_user.organization_id
            ).first()

            if not template:
                return False

            template_name = template.name
            db.delete(template)
            db.commit()

            # Log template deletion
            await AuditLogger.log_action(
                db, current_user.user_id, "template_deleted",
                f"Deleted ticket template: {template_name}",
                {"template_id": str(template_id)}
            )

            logger.info(f"Ticket template {template_name} deleted by {current_user.user_id}")
            return True

        except Exception as e:
            db.rollback()
            logger.error(f"Error deleting ticket template {template_id}: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Error deleting ticket template"
            )

    async def create_ticket_from_template(
        self,
        db: Session,
        template_id: UUID,
        form_data: Dict[str, Any],
        current_user: CurrentUser
    ) -> Ticket:
        """Create a ticket from a template"""
        try:
            # Get template
            template = db.query(TicketTemplate).filter(
                TicketTemplate.id == template_id,
                TicketTemplate.organization_id == current_user.organization_id
            ).first()

            if not template:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Ticket template not found"
                )

            # Generate ticket number
            ticket_count = db.query(Ticket).filter(
                Ticket.organization_id == current_user.organization_id
            ).count()
            ticket_number = f"TKT-{ticket_count + 1:06d}"

            # Process template fields
            title = form_data.get("title") or template.default_title or "Ticket from template"
            description = form_data.get("description") or template.default_description or ""

            # Replace template variables
            title = await self._process_template_variables(title, form_data, current_user)
            description = await self._process_template_variables(description, form_data, current_user)

            # Create ticket
            ticket = Ticket(
                ticket_number=ticket_number,
                title=title,
                description=description,
                ticket_type=template.ticket_type,
                category=template.category.name if template.category else None,
                priority=form_data.get("priority") or template.default_priority,
                requester_id=current_user.user_id,
                organization_id=current_user.organization_id,
                department_id=form_data.get("department_id"),
                status=TicketStatus.OPEN,
                requires_approval=template.requires_approval,
                contact_method=form_data.get("contact_method"),
                contact_details=form_data.get("contact_details"),
                location=form_data.get("location"),
                asset_tag=form_data.get("asset_tag"),
                attachment_urls=form_data.get("attachment_urls", []),
                created_by=current_user.user_id
            )

            db.add(ticket)

            # Update template usage count
            template.usage_count += 1

            db.commit()
            db.refresh(ticket)

            # Create activity log
            await self._create_activity(
                db, ticket.id, "created_from_template",
                f"Ticket created from template '{template.name}' by {current_user.email}",
                current_user
            )

            logger.info(f"Ticket {ticket.ticket_number} created from template {template.name} by {current_user.user_id}")
            return ticket

        except HTTPException:
            raise
        except Exception as e:
            db.rollback()
            logger.error(f"Error creating ticket from template {template_id}: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Error creating ticket from template"
            )

    async def get_template_usage_stats(
        self,
        db: Session,
        current_user: CurrentUser,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None
    ) -> Dict[str, Any]:
        """Get template usage statistics"""
        try:
            if not start_date:
                start_date = datetime.utcnow() - timedelta(days=30)
            if not end_date:
                end_date = datetime.utcnow()

            # Get all templates for organization
            templates = db.query(TicketTemplate).filter(
                TicketTemplate.organization_id == current_user.organization_id
            ).all()

            template_stats = []
            total_usage = 0

            for template in templates:
                # Count tickets created from this template in date range
                usage_count = db.query(TicketActivity).join(Ticket).filter(
                    Ticket.organization_id == current_user.organization_id,
                    TicketActivity.activity_type == "created_from_template",
                    TicketActivity.description.contains(f"template '{template.name}'"),
                    TicketActivity.created_at >= start_date,
                    TicketActivity.created_at <= end_date
                ).count()

                template_stats.append({
                    "template_id": str(template.id),
                    "template_name": template.name,
                    "ticket_type": template.ticket_type.value,
                    "usage_count": usage_count,
                    "total_usage": template.usage_count,
                    "is_public": template.is_public
                })

                total_usage += usage_count

            # Sort by usage count
            template_stats.sort(key=lambda x: x["usage_count"], reverse=True)

            return {
                "period": {
                    "start_date": start_date.isoformat(),
                    "end_date": end_date.isoformat()
                },
                "total_templates": len(templates),
                "total_usage": total_usage,
                "template_stats": template_stats
            }

        except Exception as e:
            logger.error(f"Error getting template usage stats: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Error retrieving template usage statistics"
            )

    async def _process_template_variables(
        self,
        text: str,
        form_data: Dict[str, Any],
        current_user: CurrentUser
    ) -> str:
        """Process template variables in text"""
        try:
            if not text:
                return text

            # Replace common variables
            replacements = {
                "{{user_name}}": f"{current_user.first_name} {current_user.last_name}",
                "{{user_email}}": current_user.email,
                "{{date}}": datetime.utcnow().strftime("%Y-%m-%d"),
                "{{datetime}}": datetime.utcnow().strftime("%Y-%m-%d %H:%M:%S"),
            }

            # Add form data variables
            for key, value in form_data.items():
                replacements[f"{{{{{key}}}}}"] = str(value) if value is not None else ""

            # Replace variables in text
            for variable, replacement in replacements.items():
                text = text.replace(variable, replacement)

            return text

        except Exception as e:
            logger.error(f"Error processing template variables: {e}")
            return text

    async def _create_activity(
        self,
        db: Session,
        ticket_id: UUID,
        activity_type: str,
        description: str,
        current_user: CurrentUser
    ):
        """Create ticket activity log"""
        try:
            activity = TicketActivity(
                ticket_id=ticket_id,
                user_id=current_user.user_id,
                activity_type=activity_type,
                description=description,
                created_at=datetime.utcnow()
            )
            db.add(activity)
            db.commit()
        except Exception as e:
            logger.error(f"Error creating activity log: {e}")

    async def duplicate_template(
        self,
        db: Session,
        template_id: UUID,
        new_name: str,
        current_user: CurrentUser
    ) -> Optional[TicketTemplate]:
        """Duplicate an existing template"""
        try:
            original_template = db.query(TicketTemplate).filter(
                TicketTemplate.id == template_id,
                TicketTemplate.organization_id == current_user.organization_id
            ).first()

            if not original_template:
                return None

            # Create duplicate
            duplicate_template = TicketTemplate(
                name=new_name,
                description=f"Copy of {original_template.description}" if original_template.description else None,
                organization_id=current_user.organization_id,
                ticket_type=original_template.ticket_type,
                category_id=original_template.category_id,
                default_title=original_template.default_title,
                default_description=original_template.default_description,
                default_priority=original_template.default_priority,
                custom_fields=original_template.custom_fields,
                is_public=original_template.is_public,
                requires_approval=original_template.requires_approval,
                usage_count=0,
                created_by=current_user.user_id
            )

            db.add(duplicate_template)
            db.commit()
            db.refresh(duplicate_template)

            # Log template duplication
            await AuditLogger.log_action(
                db, current_user.user_id, "template_duplicated",
                f"Duplicated ticket template: {original_template.name} -> {new_name}",
                {
                    "original_template_id": str(template_id),
                    "new_template_id": str(duplicate_template.id)
                }
            )

            logger.info(f"Ticket template {original_template.name} duplicated as {new_name} by {current_user.user_id}")
            return duplicate_template

        except Exception as e:
            db.rollback()
            logger.error(f"Error duplicating ticket template {template_id}: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Error duplicating ticket template"
            )
