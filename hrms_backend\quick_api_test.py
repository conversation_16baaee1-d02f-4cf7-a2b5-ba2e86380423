#!/usr/bin/env python3
"""
Quick test to verify leave policies API
"""

import requests
import json

def quick_test():
    """Quick test of the leave policies API"""
    
    base_url = "http://localhost:8085"
    
    try:
        # Test the leave overview endpoint (no auth required)
        print("🔍 Testing leave overview...")
        response = requests.get(f"{base_url}/api/leave/")
        print(f"Status: {response.status_code}")
        if response.status_code == 200:
            print("✅ Leave API is accessible")
            print(f"Response: {response.json()}")
        else:
            print(f"❌ Error: {response.text}")
            
        # Test if we can access policies without auth (should fail)
        print("\n🔍 Testing policies without auth...")
        response = requests.get(f"{base_url}/api/leave/policies")
        print(f"Status: {response.status_code}")
        print(f"Response: {response.text[:200]}...")
        
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    quick_test()
