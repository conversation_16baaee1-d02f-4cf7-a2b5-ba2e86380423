#!/usr/bin/env python3
"""
<PERSON>ript to create default leave policies for all organizations
"""

import asyncio
import sys
import os
from uuid import uuid4
from datetime import datetime
from decimal import Decimal

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.db.session import SessionLocal
from app.db.models.leave import LeavePolicy, LeaveType
from app.db.models.employee import Organization

def create_default_leave_policies():
    """Create comprehensive default leave policies for all organizations"""
    
    # Default leave policies configuration
    default_policies = [
        {
            "name": "Annual Leave",
            "leave_type": LeaveType.ANNUAL,
            "annual_entitlement": Decimal("21"),
            "max_carry_forward": Decimal("5"),
            "max_accumulation": Decimal("30"),
            "accrual_frequency": "monthly",
            "accrual_start_date": "hire_date",
            "min_notice_days": 7,
            "max_consecutive_days": 15,
            "min_application_days": Decimal("0.5"),
            "requires_approval": True,
            "auto_approve_threshold": 3,
            "requires_documentation": False,
            "documentation_threshold": None,
            "applicable_genders": None,
            "applicable_employment_types": None,
            "available_during_probation": False,
            "probation_entitlement": Decimal("0")
        },
        {
            "name": "Sick Leave",
            "leave_type": LeaveType.SICK,
            "annual_entitlement": Decimal("10"),
            "max_carry_forward": Decimal("2"),
            "max_accumulation": Decimal("15"),
            "accrual_frequency": "monthly",
            "accrual_start_date": "hire_date",
            "min_notice_days": 0,
            "max_consecutive_days": 7,
            "min_application_days": Decimal("0.5"),
            "requires_approval": True,
            "auto_approve_threshold": 2,
            "requires_documentation": True,
            "documentation_threshold": 3,
            "applicable_genders": None,
            "applicable_employment_types": None,
            "available_during_probation": True,
            "probation_entitlement": Decimal("5")
        },
        {
            "name": "Casual Leave",
            "leave_type": LeaveType.PERSONAL,
            "annual_entitlement": Decimal("12"),
            "max_carry_forward": Decimal("3"),
            "max_accumulation": Decimal("18"),
            "accrual_frequency": "monthly",
            "accrual_start_date": "hire_date",
            "min_notice_days": 1,
            "max_consecutive_days": 5,
            "min_application_days": Decimal("0.5"),
            "requires_approval": True,
            "auto_approve_threshold": 2,
            "requires_documentation": False,
            "documentation_threshold": None,
            "applicable_genders": None,
            "applicable_employment_types": None,
            "available_during_probation": True,
            "probation_entitlement": Decimal("3")
        },
        {
            "name": "Maternity Leave",
            "leave_type": LeaveType.MATERNITY,
            "annual_entitlement": Decimal("180"),
            "max_carry_forward": Decimal("0"),
            "max_accumulation": Decimal("180"),
            "accrual_frequency": "yearly",
            "accrual_start_date": "calendar_year",
            "min_notice_days": 30,
            "max_consecutive_days": 180,
            "min_application_days": Decimal("1"),
            "requires_approval": True,
            "auto_approve_threshold": None,
            "requires_documentation": True,
            "documentation_threshold": 1,
            "applicable_genders": ["female"],
            "applicable_employment_types": None,
            "available_during_probation": False,
            "probation_entitlement": Decimal("0")
        },
        {
            "name": "Paternity Leave",
            "leave_type": LeaveType.PATERNITY,
            "annual_entitlement": Decimal("15"),
            "max_carry_forward": Decimal("0"),
            "max_accumulation": Decimal("15"),
            "accrual_frequency": "yearly",
            "accrual_start_date": "calendar_year",
            "min_notice_days": 14,
            "max_consecutive_days": 15,
            "min_application_days": Decimal("1"),
            "requires_approval": True,
            "auto_approve_threshold": None,
            "requires_documentation": True,
            "documentation_threshold": 1,
            "applicable_genders": ["male"],
            "applicable_employment_types": None,
            "available_during_probation": False,
            "probation_entitlement": Decimal("0")
        },
        {
            "name": "Emergency Leave",
            "leave_type": LeaveType.EMERGENCY,
            "annual_entitlement": Decimal("5"),
            "max_carry_forward": Decimal("0"),
            "max_accumulation": Decimal("5"),
            "accrual_frequency": "yearly",
            "accrual_start_date": "calendar_year",
            "min_notice_days": 0,
            "max_consecutive_days": 3,
            "min_application_days": Decimal("0.5"),
            "requires_approval": True,
            "auto_approve_threshold": 1,
            "requires_documentation": True,
            "documentation_threshold": 1,
            "applicable_genders": None,
            "applicable_employment_types": None,
            "available_during_probation": True,
            "probation_entitlement": Decimal("2")
        },
        {
            "name": "Bereavement Leave",
            "leave_type": LeaveType.BEREAVEMENT,
            "annual_entitlement": Decimal("7"),
            "max_carry_forward": Decimal("0"),
            "max_accumulation": Decimal("7"),
            "accrual_frequency": "yearly",
            "accrual_start_date": "calendar_year",
            "min_notice_days": 0,
            "max_consecutive_days": 7,
            "min_application_days": Decimal("0.5"),
            "requires_approval": True,
            "auto_approve_threshold": None,
            "requires_documentation": True,
            "documentation_threshold": 1,
            "applicable_genders": None,
            "applicable_employment_types": None,
            "available_during_probation": True,
            "probation_entitlement": Decimal("3")
        },
        {
            "name": "Study Leave",
            "leave_type": LeaveType.STUDY,
            "annual_entitlement": Decimal("10"),
            "max_carry_forward": Decimal("5"),
            "max_accumulation": Decimal("20"),
            "accrual_frequency": "yearly",
            "accrual_start_date": "calendar_year",
            "min_notice_days": 30,
            "max_consecutive_days": 10,
            "min_application_days": Decimal("1"),
            "requires_approval": True,
            "auto_approve_threshold": None,
            "requires_documentation": True,
            "documentation_threshold": 1,
            "applicable_genders": None,
            "applicable_employment_types": None,
            "available_during_probation": False,
            "probation_entitlement": Decimal("0")
        },
        {
            "name": "Sabbatical Leave",
            "leave_type": LeaveType.SABBATICAL,
            "annual_entitlement": Decimal("90"),
            "max_carry_forward": Decimal("0"),
            "max_accumulation": Decimal("90"),
            "accrual_frequency": "yearly",
            "accrual_start_date": "calendar_year",
            "min_notice_days": 90,
            "max_consecutive_days": 90,
            "min_application_days": Decimal("1"),
            "requires_approval": True,
            "auto_approve_threshold": None,
            "requires_documentation": True,
            "documentation_threshold": 1,
            "applicable_genders": None,
            "applicable_employment_types": ["permanent"],
            "available_during_probation": False,
            "probation_entitlement": Decimal("0")
        },
        {
            "name": "Unpaid Leave",
            "leave_type": LeaveType.UNPAID,
            "annual_entitlement": Decimal("30"),
            "max_carry_forward": Decimal("0"),
            "max_accumulation": Decimal("30"),
            "accrual_frequency": "yearly",
            "accrual_start_date": "calendar_year",
            "min_notice_days": 14,
            "max_consecutive_days": 30,
            "min_application_days": Decimal("1"),
            "requires_approval": True,
            "auto_approve_threshold": None,
            "requires_documentation": True,
            "documentation_threshold": 1,
            "applicable_genders": None,
            "applicable_employment_types": None,
            "available_during_probation": False,
            "probation_entitlement": Decimal("0")
        }
    ]
    
    db = SessionLocal()
    try:
        # Get all organizations
        organizations = db.query(Organization).filter(Organization.is_active == True).all()
        
        for org in organizations:
            print(f"Creating leave policies for organization: {org.name}")
            
            for policy_config in default_policies:
                # Check if policy already exists
                existing_policy = db.query(LeavePolicy).filter(
                    LeavePolicy.organization_id == org.id,
                    LeavePolicy.leave_type == policy_config["leave_type"],
                    LeavePolicy.is_active == True
                ).first()
                
                if existing_policy:
                    print(f"  - {policy_config['name']} already exists, skipping...")
                    continue
                
                # Create new policy
                policy = LeavePolicy(
                    id=uuid4(),
                    organization_id=org.id,
                    is_active=True,
                    created_at=datetime.utcnow(),
                    updated_at=datetime.utcnow(),
                    **policy_config
                )
                
                db.add(policy)
                print(f"  ✅ Created {policy_config['name']}")
        
        db.commit()
        print("\n🎉 Successfully created all default leave policies!")
        
    except Exception as e:
        print(f"❌ Error creating leave policies: {e}")
        db.rollback()
        raise
    finally:
        db.close()

if __name__ == "__main__":
    create_default_leave_policies()
