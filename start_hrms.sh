#!/bin/bash

echo ""
echo "========================================"
echo "   HRMS - Human Resource Management"
echo "========================================"
echo ""

# Check if Python is installed
if ! command -v python3 &> /dev/null; then
    echo "ERROR: Python 3 is not installed or not in PATH"
    echo "Please install Python 3.8+ from https://python.org"
    exit 1
fi

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo "ERROR: Node.js is not installed or not in PATH"
    echo "Please install Node.js from https://nodejs.org"
    exit 1
fi

echo "Starting HRMS Application..."
echo ""

# Make the script executable
chmod +x run_hrms.py

# Run the unified launcher
python3 run_hrms.py

echo ""
echo "HRMS Application has stopped."
