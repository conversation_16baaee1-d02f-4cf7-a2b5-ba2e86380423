"""Create transaction_history table

Revision ID: eaadf73de156
Revises: 603965313e4b
Create Date: 2025-02-19 17:58:26.424696

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy import Column, Integer, String, Float
from sqlalchemy import func, DateTime, ForeignKey
from sqlalchemy.dialects.postgresql import BOOLEAN


# revision identifiers, used by Alembic.
revision: str = 'eaadf73de156'
down_revision: Union[str, None] = '603965313e4b'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None



def upgrade() -> None:
    op.create_table(
        'transaction_history',
        Column('id', Integer, primary_key=True, autoincrement=True),
        Column('amount_paid', String(length=45), nullable=True),
        Column('currency', String(length=45), nullable=True),
        Column('failures', String(length=45), nullable=True),
        Column('transaction_id', String(length=45), nullable=True),
        Column('integration', String(length=45), nullable=True),
        Column('reason', String(length=45), nullable=True),
        Column('recipient_code', String(length=250), nullable=True),
        Column('reference_code', String(length=250), nullable=True),
        Column('request', String(length=250), nullable=True),
        Column('transaction_process_status', String(length=250), nullable=True),
        Column('transfer_code', String(length=250), nullable=True),
        Column('transaction_message', String(length=250), nullable=True),
        Column('paystack_status', String(length=250), nullable=True),
        Column('verified_bank_name', String(length=250), nullable=True),
        Column('verified_bank_sort_code', String(length=250), nullable=True),
        Column('verified_account_number', String(length=250), nullable=True),       
        Column('verified_account_name', String(length=250), nullable=True),
        Column('transferred_at', String(length=250), nullable=True),
        Column('createdAt', String(length=250), nullable=True),
        Column('updatedAt', String(length=250), nullable=True),
        Column('user_id', Integer, ForeignKey('users.id'), nullable=True),
        Column('timestamp', DateTime, nullable=False, default=sa.func.now()),
    )


def downgrade() -> None:
    op.drop_table('transaction_history')


