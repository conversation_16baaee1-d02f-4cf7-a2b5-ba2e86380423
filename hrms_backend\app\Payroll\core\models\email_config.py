from datetime import datetime
from core.databases.database import db
from sqlalchemy import <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, String, Boolean, DateTime, ForeignKey
from sqlalchemy.orm import relationship
from core.utils.encryption import encrypt, decrypt 
from core.models.basemodel import ModelBase


class EmailConfiguration(ModelBase):
    __tablename__ = 'email_configurations'

    id = db.Column(db.Integer, primary_key=True, index=True)
    user_id = db.Column(db.Integer, db.ForeignKey((ModelBase.dbSchema() + '.users.id')), nullable=False) 

    smtp_server = db.Column(db.String(255), nullable=False)
    smtp_port = db.Column(db.Integer, nullable=False)
    username = db.Column(db.String(255), nullable=False)
    password = db.Column(db.String(255), nullable=False)  # Stored encrypted
    use_ssl = db.Column(db.<PERSON><PERSON>, default=False, nullable=False)
    use_tls = db.Column(db.<PERSON>, default=False, nullable=False)
    sender_email = db.Column(db.String(255), nullable=False)
    is_active = db.Column(db.Boolean, default=True, nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)

    def set_password(self, password: str):
        self.password = encrypt(password)

    def get_password(self) -> str:
        return decrypt(self.password)