from core.databases.database import db
from core.models.basemodel import ModelBase
from sqlalchemy.orm import relationship
from datetime import datetime

class TransactionHistory(ModelBase):
    __tablename__ = "transaction_history"

    id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    payroll_history_id = db.Column(db.Integer, nullable=True)
    employee_id = db.Column(db.Integer, db.ForeignKey((ModelBase.dbSchema() + '.employees.id'),ondelete='CASCADE'), nullable=True)
    amount_paid = db.Column(db.String(45), nullable=False)
    currency = db.Column(db.String(45), nullable=False)
    failures = db.Column(db.String(45), nullable=False)
    transaction_id  = db.Column(db.String(45), nullable=False)
    integration = db.Column(db.String(45), nullable=False)
    reason = db.Column(db.String(45), nullable=False)
    recipient_code = db.Column(db.String(45), nullable=False)
    reference_code  = db.Column(db.String(45), nullable=False)
    request  = db.Column(db.String(45), nullable=False)
    transaction_process_status = db.Column(db.String(250), nullable=False)
    transfer_code = db.Column(db.String(250), nullable=False)
    transaction_message = db.Column(db.String(250), nullable=False)
    paystack_status = db.Column(db.String(250), nullable=False)
    verified_bank_name = db.Column(db.String(250), nullable=False)
    verified_bank_sort_code  = db.Column(db.String(250), nullable=False)
    verified_account_number  = db.Column(db.String(250), nullable=False)
    verified_account_name = db.Column(db.String(250), nullable=False)
    transferred_at = db.Column(db.String(250), nullable=False)
    createdAt = db.Column(db.String(250), nullable=False)
    updatedAt = db.Column(db.String(250), nullable=False)  
    user_id = db.Column(db.Integer, db.ForeignKey((ModelBase.dbSchema() + '.users.id')), nullable=False) 
    timestamp = db.Column(db.DateTime, nullable=False, default=datetime.now()) 
    employee = relationship("EmployeeModel", backref="transaction_histories", lazy="joined")
