from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, func
from typing import Optional, List
from uuid import UUID
from datetime import datetime, date
from decimal import Decimal
from fastapi import HTTPException, status
import logging

from ...db.models.project import (
    Project, ProjectAssignment, Task, TaskDependency, TaskComment,
    TaskAssignment, ProjectMilestone
)
from ...db.models.employee import Employee
from ...schemas.project import (
    ProjectCreate, ProjectUpdate, ProjectResponse, ProjectListResponse,
    ProjectAssignmentCreate, ProjectAssignmentUpdate, ProjectAssignmentResponse,
    TaskCreate, TaskUpdate, TaskResponse, TaskListResponse,
    TaskCommentCreate, TaskCommentUpdate, TaskCommentResponse,
    TaskDependencyCreate, TaskDependencyResponse,
    ProjectMilestoneCreate, ProjectMilestoneUpdate, ProjectMilestoneResponse,
    BulkTaskUpdate, TaskAssignmentRequest, ProjectStatus, TaskStatus
)
from ...core.security import CurrentUser
from ...core.websocket_manager import notification_manager

logger = logging.getLogger(__name__)


class ProjectService:
    """Project service for business logic"""

    async def create_project(
        self,
        db: Session,
        project_data: ProjectCreate,
        current_user: CurrentUser
    ) -> ProjectResponse:
        """Create new project"""
        try:
            # Check if project code already exists
            existing = db.query(Project).filter(
                Project.code == project_data.code,
                Project.organization_id == current_user.organization_id
            ).first()

            if existing:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Project code already exists"
                )

            # Verify project manager exists if provided
            if project_data.project_manager_id:
                manager = db.query(Employee).filter(
                    Employee.id == project_data.project_manager_id,
                    Employee.organization_id == current_user.organization_id,
                    Employee.is_active == True
                ).first()

                if not manager:
                    raise HTTPException(
                        status_code=status.HTTP_404_NOT_FOUND,
                        detail="Project manager not found"
                    )

            # Create project
            project = Project(
                **project_data.dict(),
                organization_id=current_user.organization_id,
                actual_hours=Decimal(0),
                actual_cost=Decimal(0),
                progress_percentage=Decimal(0),
                created_by=current_user.user_id
            )

            db.add(project)
            db.commit()
            db.refresh(project)

            logger.info(f"Project {project.code} created by {current_user.user_id}")
            return ProjectResponse.from_orm(project)

        except HTTPException:
            raise
        except Exception as e:
            db.rollback()
            logger.error(f"Error creating project: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Error creating project"
            )

    async def get_projects(
        self,
        db: Session,
        status: Optional[ProjectStatus] = None,
        project_manager_id: Optional[UUID] = None,
        department_id: Optional[UUID] = None,
        search: Optional[str] = None,
        skip: int = 0,
        limit: int = 20,
        current_user: CurrentUser = None
    ) -> ProjectListResponse:
        """Get projects with filtering"""
        try:
            query = db.query(Project).filter(
                Project.organization_id == current_user.organization_id,
                Project.is_active == True
            )

            # Apply filters
            if status:
                query = query.filter(Project.status == status)

            if project_manager_id:
                query = query.filter(Project.project_manager_id == project_manager_id)

            if department_id:
                query = query.filter(Project.department_id == department_id)

            if search:
                search_filter = or_(
                    Project.name.ilike(f"%{search}%"),
                    Project.code.ilike(f"%{search}%"),
                    Project.description.ilike(f"%{search}%")
                )
                query = query.filter(search_filter)

            # Get total count
            total = query.count()

            # Apply pagination and ordering
            projects = query.order_by(
                Project.created_at.desc()
            ).offset(skip).limit(limit).all()

            # Convert to response format
            project_responses = [ProjectResponse.from_orm(project) for project in projects]

            return ProjectListResponse(
                projects=project_responses,
                total=total,
                skip=skip,
                limit=limit
            )

        except Exception as e:
            logger.error(f"Error getting projects: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Error retrieving projects"
            )

    async def get_project_by_id(
        self,
        db: Session,
        project_id: UUID,
        current_user: CurrentUser
    ) -> Optional[ProjectResponse]:
        """Get project by ID"""
        try:
            project = db.query(Project).filter(
                Project.id == project_id,
                Project.organization_id == current_user.organization_id,
                Project.is_active == True
            ).first()

            if not project:
                return None

            return ProjectResponse.from_orm(project)

        except Exception as e:
            logger.error(f"Error getting project {project_id}: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Error retrieving project"
            )

    async def update_project(
        self,
        db: Session,
        project_id: UUID,
        project_data: ProjectUpdate,
        current_user: CurrentUser
    ) -> Optional[ProjectResponse]:
        """Update project"""
        try:
            project = db.query(Project).filter(
                Project.id == project_id,
                Project.organization_id == current_user.organization_id,
                Project.is_active == True
            ).first()

            if not project:
                return None

            # Update fields
            update_data = project_data.dict(exclude_unset=True)
            for field, value in update_data.items():
                setattr(project, field, value)

            project.updated_by = current_user.user_id

            db.commit()
            db.refresh(project)

            logger.info(f"Project {project.code} updated by {current_user.user_id}")
            return ProjectResponse.from_orm(project)

        except Exception as e:
            db.rollback()
            logger.error(f"Error updating project {project_id}: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Error updating project"
            )

    # Task Management Methods
    async def create_task(
        self,
        db: Session,
        task_data: TaskCreate,
        current_user: CurrentUser
    ) -> TaskResponse:
        """Create new task"""
        try:
            # Verify project exists
            project = db.query(Project).filter(
                Project.id == task_data.project_id,
                Project.organization_id == current_user.organization_id,
                Project.is_active == True
            ).first()

            if not project:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Project not found"
                )

            # Verify assignee exists if provided
            if task_data.assignee_id:
                assignee = db.query(Employee).filter(
                    Employee.id == task_data.assignee_id,
                    Employee.organization_id == current_user.organization_id,
                    Employee.is_active == True
                ).first()

                if not assignee:
                    raise HTTPException(
                        status_code=status.HTTP_404_NOT_FOUND,
                        detail="Assignee not found"
                    )

            # Generate task number
            task_count = db.query(Task).filter(Task.project_id == task_data.project_id).count()
            task_number = f"{project.code}-{task_count + 1:04d}"

            # Create task
            task = Task(
                **task_data.dict(exclude={'labels', 'tags', 'attachment_urls'}),
                task_number=task_number,
                reporter_id=current_user.user_id,
                actual_hours=Decimal(0),
                progress_percentage=Decimal(0),
                labels=task_data.labels or [],
                tags=task_data.tags or [],
                attachment_urls=task_data.attachment_urls or [],
                created_by=current_user.user_id
            )

            db.add(task)
            db.commit()
            db.refresh(task)

            # Send notification to assignee
            if task.assignee_id:
                await notification_manager.notify_task_assignment(
                    str(task.assignee_id),
                    {
                        "task_id": str(task.id),
                        "task_title": task.title,
                        "project_name": project.name,
                        "assigned_by": current_user.email
                    }
                )

            logger.info(f"Task {task.task_number} created by {current_user.user_id}")
            return TaskResponse.from_orm(task)

        except HTTPException:
            raise
        except Exception as e:
            db.rollback()
            logger.error(f"Error creating task: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Error creating task"
            )

    async def get_tasks(
        self,
        db: Session,
        project_id: Optional[UUID] = None,
        assignee_id: Optional[UUID] = None,
        status: Optional[TaskStatus] = None,
        priority: Optional[str] = None,
        search: Optional[str] = None,
        skip: int = 0,
        limit: int = 20,
        current_user: CurrentUser = None
    ) -> TaskListResponse:
        """Get tasks with filtering"""
        try:
            # Join with Project to filter by organization
            query = db.query(Task).join(Project).filter(
                Project.organization_id == current_user.organization_id,
                Project.is_active == True
            )

            # Apply filters
            if project_id:
                query = query.filter(Task.project_id == project_id)

            if assignee_id:
                query = query.filter(Task.assignee_id == assignee_id)

            if status:
                query = query.filter(Task.status == status)

            if priority:
                query = query.filter(Task.priority == priority)

            if search:
                search_filter = or_(
                    Task.title.ilike(f"%{search}%"),
                    Task.description.ilike(f"%{search}%"),
                    Task.task_number.ilike(f"%{search}%")
                )
                query = query.filter(search_filter)

            # Get total count
            total = query.count()

            # Apply pagination and ordering
            tasks = query.order_by(
                Task.created_at.desc()
            ).offset(skip).limit(limit).all()

            # Convert to response format
            task_responses = [TaskResponse.from_orm(task) for task in tasks]

            return TaskListResponse(
                tasks=task_responses,
                total=total,
                skip=skip,
                limit=limit
            )

        except Exception as e:
            logger.error(f"Error getting tasks: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Error retrieving tasks"
            )

    async def update_task(
        self,
        db: Session,
        task_id: UUID,
        task_data: TaskUpdate,
        current_user: CurrentUser
    ) -> Optional[TaskResponse]:
        """Update task"""
        try:
            # Join with Project to ensure organization access
            task = db.query(Task).join(Project).filter(
                Task.id == task_id,
                Project.organization_id == current_user.organization_id,
                Project.is_active == True
            ).first()

            if not task:
                return None

            # Store old assignee for notification
            old_assignee_id = task.assignee_id

            # Update fields
            update_data = task_data.dict(exclude_unset=True, exclude={'labels', 'tags', 'attachment_urls'})
            for field, value in update_data.items():
                setattr(task, field, value)

            # Handle array fields separately
            if task_data.labels is not None:
                task.labels = task_data.labels
            if task_data.tags is not None:
                task.tags = task_data.tags
            if task_data.attachment_urls is not None:
                task.attachment_urls = task_data.attachment_urls

            task.updated_by = current_user.user_id

            db.commit()
            db.refresh(task)

            # Send notification if assignee changed
            if task_data.assignee_id and task_data.assignee_id != old_assignee_id:
                project = db.query(Project).filter(Project.id == task.project_id).first()
                await notification_manager.notify_task_assignment(
                    str(task.assignee_id),
                    {
                        "task_id": str(task.id),
                        "task_title": task.title,
                        "project_name": project.name if project else "Unknown",
                        "assigned_by": current_user.email
                    }
                )

            logger.info(f"Task {task.task_number} updated by {current_user.user_id}")
            return TaskResponse.from_orm(task)

        except Exception as e:
            db.rollback()
            logger.error(f"Error updating task {task_id}: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Error updating task"
            )
