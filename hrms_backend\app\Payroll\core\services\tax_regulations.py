from core.repositories.tax_regulations import TaxRegulationsRepository

class TaxRegulationsService:
    def __init__(self) -> None:
        self.repository = TaxRegulationsRepository()

    def createTaxRegulations(self, Kwargs):
        # print(Kwargs)
        return self.repository.createTaxRegulations(**Kwargs)
    
    def getTaxRegulations(self, id):
        return self.repository.getTaxRegulations(id)
    
    def updateTaxRegulations(self, id, **Kwargs):
        return self.repository.updateTaxRegulations(id, **Kwargs)
    
    def getTaxRegulationsByKey(self, Kwarg):
        return self.repository.getTaxRegulationsByKeys(Kwarg)
    
    def deleteTaxRegulations(self, id):
        return self.repository.deleteTaxRegulations(id)