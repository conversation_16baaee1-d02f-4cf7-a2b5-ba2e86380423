from core.models.announcements import AnnouncementModel
from core.databases.database import db
from core.repositories.user import UserRepository

class AnnouncementsRepository:
    def __init__(self) -> None:
        pass

    @classmethod
    def createAnnouncements(self, title, sent_to, description, status, date):
        announcements = AnnouncementModel(
            title=title,
            sent_to=sent_to,
            description=description,
            status=status,
            date=date,
            user_id=UserRepository.authUserId()
        )
        db.session.add(announcements)
        db.session.commit()
        return announcements

    @classmethod
    def fetchAll(self):
        return AnnouncementModel.query.filter_by(user_id=UserRepository().authUserId()).order_by(AnnouncementModel.timestamp.desc()).all()
    
    @classmethod
    def getAnnouncements(self, id):
        return AnnouncementModel.query.filter(AnnouncementModel.id == id).first()
    
    @classmethod
    def getAnnouncementsByKeys(self, kwargs):
        return AnnouncementModel.query.filter_by(user_id=UserRepository().authUserId(), **kwargs).all()

    @classmethod
    def updateAnnouncements(self, id, **kwargs):
        announcements = AnnouncementModel.query.filter_by(id=id).first()
        if announcements:
            for key, value in kwargs.items():
                setattr(announcements, key, value)
            db.session.commit()
            return announcements
        else:
            return None

    @classmethod
    def deleteAnnouncements(self, id):
        AnnouncementModel.query.filter(AnnouncementModel.id == id).delete()
        db.session.commit()
        return
        