"""create departments table

Revision ID: 4d6aef792dcd
Revises: f85c1bb32a40
Create Date: 2024-04-03 15:04:30.673194

"""
from typing import Sequence, Union

from alembic import op
from sqlalchemy import func, TIMESTAMP, ForeignKey
from sqlalchemy import <PERSON>umn, Integer, String

# revision identifiers, used by Alembic.
revision: str = '4d6aef792dcd'
down_revision: Union[str, None] = 'f85c1bb32a40'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None

def upgrade() -> None:
    op.create_table(
        'departments',
        Column('id', Integer, primary_key=True),
        <PERSON><PERSON><PERSON>('name', String(45), nullable=False),
        <PERSON>umn('user_id', Integer, Foreign<PERSON>ey("users.id")),
        <PERSON>umn("timestamp", TIMESTAMP, server_default=func.now()),
    )


def downgrade() -> None:
    op.drop_table("departments")
