from core.databases.database import db
from core.models.basemodel import ModelBase
from sqlalchemy.orm import relationship

class TaxRegulationsModel(ModelBase):
    __tablename__ = "tax_regulations"

    id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    country = db.Column(db.String(100), nullable=False)
    tax_rate = db.Column(db.String(100), nullable=False)
    tax_type = db.Column(db.String(100), nullable=False)
    currency = db.Column(db.Double, nullable=False)
    childTaxReg = db.relationship("EmployeeIncTaxModel")
    
    
      