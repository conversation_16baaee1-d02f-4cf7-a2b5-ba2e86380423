/**
 * Professional Leave Management Dashboard
 * Features: Analytics, quick stats, recent activity, calendar view
 */

import React, { useState, useEffect } from 'react';
import {
  Calendar,
  Clock,
  TrendingUp,
  Users,
  CheckCircle,
  XCircle,
  AlertCircle,
  Plus,
  Filter,
  Download,
  Bar<PERSON>hart3,
  <PERSON><PERSON><PERSON>
} from 'lucide-react';
import apiService from '../../services/api';
import LeaveApplicationForm from './LeaveApplicationForm';

const LeaveDashboard = () => {
  const [dashboardData, setDashboardData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [showApplicationForm, setShowApplicationForm] = useState(false);
  const [selectedView, setSelectedView] = useState('overview');

  useEffect(() => {
    loadDashboardData();
  }, []);

  const loadDashboardData = async () => {
    try {
      setLoading(true);
      const data = await apiService.get('/leave/dashboard');
      setDashboardData(data);
    } catch (error) {
      console.error('Error loading dashboard data:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleLeaveApplication = async (formData) => {
    try {
      await apiService.createLeaveRequest(formData);
      await loadDashboardData(); // Refresh dashboard
    } catch (error) {
      console.error('Error submitting leave application:', error);
      throw error;
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-agno-primary"></div>
      </div>
    );
  }

  const { summary, recent_requests, pending_approvals, upcoming_leaves, analytics } = dashboardData || {};

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Leave Management</h1>
          <p className="text-gray-600 mt-1">Manage your leave requests and view team analytics</p>
        </div>
        
        <div className="flex items-center space-x-3">
          <button
            onClick={() => setShowApplicationForm(true)}
            className="flex items-center px-4 py-2 agno-bg-primary text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            <Plus size={16} className="mr-2" />
            Apply for Leave
          </button>
          
          <button className="flex items-center px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50">
            <Download size={16} className="mr-2" />
            Export
          </button>
        </div>
      </div>

      {/* Quick Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {/* My Leave Balance */}
        {summary?.my_balance && (
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Available Balance</p>
                <p className="text-2xl font-bold text-green-600 mt-1">
                  {summary.my_balance[0]?.available_balance || 0} days
                </p>
              </div>
              <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                <Calendar className="w-6 h-6 text-green-600" />
              </div>
            </div>
            <div className="mt-4 flex items-center text-sm text-gray-500">
              <span>Total: {summary.my_balance[0]?.total_entitlement || 0} days</span>
            </div>
          </div>
        )}

        {/* Pending Requests */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Pending Requests</p>
              <p className="text-2xl font-bold text-amber-600 mt-1">
                {analytics?.current_year_stats?.pending_requests || 0}
              </p>
            </div>
            <div className="w-12 h-12 bg-amber-100 rounded-lg flex items-center justify-center">
              <Clock className="w-6 h-6 text-amber-600" />
            </div>
          </div>
          <div className="mt-4 flex items-center text-sm text-gray-500">
            <AlertCircle className="w-4 h-4 mr-1" />
            <span>Awaiting approval</span>
          </div>
        </div>

        {/* Approved This Year */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Approved This Year</p>
              <p className="text-2xl font-bold text-blue-600 mt-1">
                {analytics?.current_year_stats?.approved_requests || 0}
              </p>
            </div>
            <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
              <CheckCircle className="w-6 h-6 text-blue-600" />
            </div>
          </div>
          <div className="mt-4 flex items-center text-sm text-gray-500">
            <TrendingUp className="w-4 h-4 mr-1" />
            <span>{analytics?.current_year_stats?.approval_rate || 0}% approval rate</span>
          </div>
        </div>

        {/* Team Overview */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Team on Leave</p>
              <p className="text-2xl font-bold text-purple-600 mt-1">
                {upcoming_leaves?.filter(leave => 
                  new Date(leave.start_date) <= new Date() && 
                  new Date(leave.end_date) >= new Date()
                ).length || 0}
              </p>
            </div>
            <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
              <Users className="w-6 h-6 text-purple-600" />
            </div>
          </div>
          <div className="mt-4 flex items-center text-sm text-gray-500">
            <span>Currently away</span>
          </div>
        </div>
      </div>

      {/* Main Content Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Recent Requests */}
        <div className="lg:col-span-2 bg-white rounded-xl shadow-sm border border-gray-200">
          <div className="p-6 border-b border-gray-200">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-semibold text-gray-900">Recent Requests</h3>
              <button className="text-agno-primary hover:text-blue-700 text-sm font-medium">
                View All
              </button>
            </div>
          </div>
          
          <div className="p-6">
            {recent_requests && recent_requests.length > 0 ? (
              <div className="space-y-4">
                {recent_requests.slice(0, 5).map((request) => (
                  <div key={request.id} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                    <div className="flex items-center space-x-4">
                      <div className={`w-3 h-3 rounded-full ${
                        request.status === 'approved' ? 'bg-green-500' :
                        request.status === 'rejected' ? 'bg-red-500' :
                        'bg-amber-500'
                      }`}></div>
                      <div>
                        <p className="font-medium text-gray-900">
                          {request.leave_type_name || 'Leave Request'}
                        </p>
                        <p className="text-sm text-gray-500">
                          {new Date(request.start_date).toLocaleDateString()} - {new Date(request.end_date).toLocaleDateString()}
                        </p>
                      </div>
                    </div>
                    <div className="text-right">
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                        request.status === 'approved' ? 'bg-green-100 text-green-800' :
                        request.status === 'rejected' ? 'bg-red-100 text-red-800' :
                        'bg-amber-100 text-amber-800'
                      }`}>
                        {request.status.charAt(0).toUpperCase() + request.status.slice(1)}
                      </span>
                      <p className="text-sm text-gray-500 mt-1">{request.total_days} days</p>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-8">
                <Calendar className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-500">No recent leave requests</p>
              </div>
            )}
          </div>
        </div>

        {/* Upcoming Leaves */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-200">
          <div className="p-6 border-b border-gray-200">
            <h3 className="text-lg font-semibold text-gray-900">Upcoming Leaves</h3>
          </div>
          
          <div className="p-6">
            {upcoming_leaves && upcoming_leaves.length > 0 ? (
              <div className="space-y-4">
                {upcoming_leaves.slice(0, 4).map((leave) => (
                  <div key={leave.id} className="flex items-start space-x-3">
                    <div className="w-2 h-2 bg-agno-primary rounded-full mt-2"></div>
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium text-gray-900 truncate">
                        {leave.employee_name}
                      </p>
                      <p className="text-xs text-gray-500">
                        {leave.leave_type} • {leave.total_days} days
                      </p>
                      <p className="text-xs text-gray-400 mt-1">
                        {new Date(leave.start_date).toLocaleDateString()}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-8">
                <Users className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-500">No upcoming leaves</p>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Analytics Section */}
      {analytics && (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Monthly Trends */}
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-lg font-semibold text-gray-900">Monthly Trends</h3>
              <BarChart3 className="w-5 h-5 text-gray-400" />
            </div>
            
            <div className="space-y-3">
              {analytics.monthly_trends?.slice(-6).map((month, index) => (
                <div key={index} className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">{month.month}</span>
                  <div className="flex items-center space-x-2">
                    <div className="w-24 bg-gray-200 rounded-full h-2">
                      <div 
                        className="bg-agno-primary h-2 rounded-full"
                        style={{ width: `${Math.min((month.requests / 10) * 100, 100)}%` }}
                      ></div>
                    </div>
                    <span className="text-sm font-medium text-gray-900 w-8 text-right">
                      {month.requests}
                    </span>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Leave Type Distribution */}
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-lg font-semibold text-gray-900">Leave Type Distribution</h3>
              <PieChart className="w-5 h-5 text-gray-400" />
            </div>
            
            <div className="space-y-3">
              {analytics.leave_type_distribution?.map((type, index) => (
                <div key={index} className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <div className={`w-3 h-3 rounded-full bg-${['blue', 'green', 'purple', 'amber', 'red'][index % 5]}-500`}></div>
                    <span className="text-sm text-gray-600 capitalize">{type.leave_type}</span>
                  </div>
                  <div className="text-right">
                    <span className="text-sm font-medium text-gray-900">{type.count}</span>
                    <span className="text-xs text-gray-500 ml-1">({type.total_days}d)</span>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}

      {/* Leave Application Form Modal */}
      {showApplicationForm && (
        <LeaveApplicationForm
          onClose={() => setShowApplicationForm(false)}
          onSubmit={handleLeaveApplication}
        />
      )}
    </div>
  );
};

export default LeaveDashboard;
