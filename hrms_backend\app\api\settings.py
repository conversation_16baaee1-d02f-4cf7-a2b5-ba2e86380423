from fastapi import APIRouter, HTTPException, status, Depends
from sqlalchemy.orm import Session
from typing import Dict, Any, Optional
from pydantic import BaseModel

from ..core.security import get_current_user, require_permission, CurrentUser
from ..core.rbac import Permissions
from ..db.session import get_db

router = APIRouter()

# Root endpoint
@router.get("/")
async def get_settings_overview(
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(get_current_user)
):
    """Get settings overview"""
    try:
        return {
            "message": "Settings management system",
            "endpoints": [
                "/user/preferences - Get user preferences",
                "/user/notifications - Get notification settings",
                "/user/security - Get security settings",
                "/system - Get system settings"
            ]
        }
    except Exception as e:
        return {"error": str(e)}

# Pydantic models for settings
class UserPreferences(BaseModel):
    language: str = "en"
    timezone: str = "Africa/Lagos"
    date_format: str = "DD/MM/YYYY"
    currency: str = "NGN"

class NotificationSettings(BaseModel):
    email: bool = True
    push: bool = False
    sms: bool = False
    leave_requests: bool = True
    payroll_updates: bool = True
    system_alerts: bool = False

class SecuritySettings(BaseModel):
    two_factor_auth: bool = False
    session_timeout: int = 30
    password_expiry: int = 90

class SystemSettings(BaseModel):
    company_name: str = "AgnoConnect"
    company_email: str = "<EMAIL>"
    working_hours_start: str = "09:00"
    working_hours_end: str = "17:00"
    working_days: list = ["monday", "tuesday", "wednesday", "thursday", "friday"]
    leave_approval_required: bool = True
    timesheet_approval_required: bool = True

class UserSettingsResponse(BaseModel):
    preferences: UserPreferences
    notifications: NotificationSettings
    security: SecuritySettings

class SystemSettingsResponse(BaseModel):
    system: SystemSettings

# User settings endpoints
@router.get("/user/preferences", response_model=UserPreferences)
async def get_user_preferences(
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(get_current_user)
):
    """Get user preferences"""
    # Mock data for now - replace with actual database implementation
    return UserPreferences(
        language="en",
        timezone="Africa/Lagos",
        date_format="DD/MM/YYYY",
        currency="NGN"
    )

@router.put("/user/preferences", response_model=UserPreferences)
async def update_user_preferences(
    preferences: UserPreferences,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(get_current_user)
):
    """Update user preferences"""
    # Mock implementation - replace with actual database update
    return preferences

@router.get("/user/notifications", response_model=NotificationSettings)
async def get_notification_settings(
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(get_current_user)
):
    """Get notification settings"""
    # Mock data for now - replace with actual database implementation
    return NotificationSettings(
        email=True,
        push=False,
        sms=False,
        leave_requests=True,
        payroll_updates=True,
        system_alerts=False
    )

@router.put("/user/notifications", response_model=NotificationSettings)
async def update_notification_settings(
    notifications: NotificationSettings,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(get_current_user)
):
    """Update notification settings"""
    # Mock implementation - replace with actual database update
    return notifications

@router.get("/user/security", response_model=SecuritySettings)
async def get_security_settings(
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(get_current_user)
):
    """Get security settings"""
    # Mock data for now - replace with actual database implementation
    return SecuritySettings(
        two_factor_auth=False,
        session_timeout=30,
        password_expiry=90
    )

@router.put("/user/security", response_model=SecuritySettings)
async def update_security_settings(
    security: SecuritySettings,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(get_current_user)
):
    """Update security settings"""
    # Mock implementation - replace with actual database update
    return security

@router.get("/user/all", response_model=UserSettingsResponse)
async def get_all_user_settings(
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(get_current_user)
):
    """Get all user settings"""
    return UserSettingsResponse(
        preferences=UserPreferences(),
        notifications=NotificationSettings(),
        security=SecuritySettings()
    )

# System settings endpoints (admin only)
@router.get("/system", response_model=SystemSettings)
async def get_system_settings(
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.ADMIN_SETTINGS))
):
    """Get system settings"""
    # Mock data for now - replace with actual database implementation
    return SystemSettings(
        company_name="AgnoConnect",
        company_email="<EMAIL>",
        working_hours_start="09:00",
        working_hours_end="17:00",
        working_days=["monday", "tuesday", "wednesday", "thursday", "friday"],
        leave_approval_required=True,
        timesheet_approval_required=True
    )

@router.put("/system", response_model=SystemSettings)
async def update_system_settings(
    settings: SystemSettings,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.ADMIN_SETTINGS))
):
    """Update system settings"""
    # Mock implementation - replace with actual database update
    return settings

@router.get("/system/backup")
async def backup_system_settings(
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.ADMIN_SETTINGS))
):
    """Backup system settings"""
    return {
        "message": "System settings backup initiated",
        "backup_id": "backup_123456789",
        "timestamp": "2024-01-20T10:30:00Z"
    }

@router.post("/system/restore")
async def restore_system_settings(
    backup_id: str,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.ADMIN_SETTINGS))
):
    """Restore system settings from backup"""
    return {
        "message": f"System settings restored from backup {backup_id}",
        "timestamp": "2024-01-20T10:35:00Z"
    }

# Email settings
@router.get("/email")
async def get_email_settings(
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.ADMIN_SETTINGS))
):
    """Get email configuration"""
    return {
        "smtp_server": "smtp.gmail.com",
        "smtp_port": 587,
        "smtp_username": "<EMAIL>",
        "smtp_use_tls": True,
        "from_email": "<EMAIL>",
        "from_name": "AgnoConnect HRMS"
    }

@router.put("/email")
async def update_email_settings(
    email_config: Dict[str, Any],
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.ADMIN_SETTINGS))
):
    """Update email configuration"""
    return {
        "message": "Email settings updated successfully",
        "config": email_config
    }

@router.post("/email/test")
async def test_email_settings(
    test_email: str,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.ADMIN_SETTINGS))
):
    """Test email configuration"""
    return {
        "message": f"Test email sent to {test_email}",
        "status": "success",
        "timestamp": "2024-01-20T10:40:00Z"
    }

# Integration settings
@router.get("/integrations")
async def get_integration_settings(
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.ADMIN_SETTINGS))
):
    """Get integration settings"""
    return {
        "paystack": {
            "enabled": True,
            "public_key": "pk_test_***",
            "webhook_url": "https://api.agnoconnect.com/webhooks/paystack"
        },
        "zoho": {
            "enabled": False,
            "client_id": "",
            "redirect_uri": ""
        },
        "slack": {
            "enabled": False,
            "webhook_url": ""
        }
    }

@router.put("/integrations/{integration_name}")
async def update_integration_settings(
    integration_name: str,
    config: Dict[str, Any],
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.ADMIN_SETTINGS))
):
    """Update integration settings"""
    return {
        "message": f"{integration_name} integration settings updated successfully",
        "config": config
    }

# Audit logs
@router.get("/audit-logs")
async def get_audit_logs(
    limit: int = 50,
    offset: int = 0,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.ADMIN_REPORTS))
):
    """Get system audit logs"""
    # Mock data for now - replace with actual audit log implementation
    return {
        "logs": [
            {
                "id": 1,
                "user": "<EMAIL>",
                "action": "UPDATE_SYSTEM_SETTINGS",
                "resource": "system_settings",
                "timestamp": "2024-01-20T10:30:00Z",
                "ip_address": "*************",
                "user_agent": "Mozilla/5.0..."
            },
            {
                "id": 2,
                "user": "<EMAIL>",
                "action": "UPDATE_USER_PREFERENCES",
                "resource": "user_preferences",
                "timestamp": "2024-01-20T10:25:00Z",
                "ip_address": "*************",
                "user_agent": "Mozilla/5.0..."
            }
        ],
        "total": 2,
        "limit": limit,
        "offset": offset
    }
