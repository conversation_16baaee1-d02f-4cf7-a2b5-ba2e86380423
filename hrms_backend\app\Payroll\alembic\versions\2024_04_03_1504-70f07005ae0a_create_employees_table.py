"""create employees table

Revision ID: 70f07005ae0a
Revises: 3e39e8ce74f0
Create Date: 2024-04-03 15:04:43.991232

"""
from typing import Sequence, Union

from alembic import op
from sqlalchemy import func, DateTime, ForeignKey
from sqlalchemy import Column, Integer, String, Float
import datetime

# revision identifiers, used by Alembic.
revision: str = '70f07005ae0a'
down_revision: Union[str, None] = "3e39e8ce74f0"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    op.create_table(
        'employees',
        Column('id', Integer, primary_key=True),
        Column('first_name', String(45), nullable=True),
        Column('last_name', String(45), nullable=True),
        Column('email', String(45), nullable=False),
        Column('gender', String(45), nullable=False),
        Column('business_unit', String(45), nullable=True),
        Column('employee_type', String(45), nullable=True),
        Column('employment_type', String(45), nullable=True),
        Column('division', String(45), nullable=True),
        Column('phone', String(45), nullable=True),
        Column('bank_name', String(45), nullable=True),
        Column('sort_code', String(45), nullable=True),
        Column('bank_account', String(45), nullable=True),
        Column('address', String(150), nullable=True),
        Column('state', String(45), nullable=True),
        Column('city', String(45), nullable=True),
        Column('country', String(45), nullable=True),
        Column('currency', String(45), nullable=True),
        Column('zip_code', String(45), nullable=True),
        Column('tax_type', String(45), nullable=True),
        Column("taxID", Integer, nullable=True),
        Column("dob", DateTime, nullable=True, server_default=func.now()),
        Column("hire_date", DateTime, nullable=True, server_default=func.now()),
        Column('status', String(45), nullable=True),
        Column('employeeID', String(45), nullable=True),
        Column('role', String(45), nullable=True),
        Column('salary_type', String(45), nullable=True),
        Column('rate', String(45), nullable=True),
        Column('hours_worked', String(45), nullable=True),
        Column('number_of_days_worked', Integer, nullable=True),    
        Column('level', String(45), nullable=True),
        Column('annual_leave_days', String(45), nullable=True),
        Column('unpaid_leave_days', String(45), nullable=True),
        Column('sick_leave_days', String(45), nullable=True),
        Column('maternity_paternity_leave_days', String(45), nullable=True),
        Column('casual_leave_days', String(45), nullable=True),
        Column('compassionate_leave_days', String(45), nullable=True),
        Column('work_schedule', String(45), nullable=True),
        Column('total_working_days', String(45), nullable=True),
        Column('total_present_days', String(45), nullable=True),
        Column('total_absent_days', String(45), nullable=True),
        Column("pension_no", String, nullable=True),
        Column("pension_pfa", String(45), nullable=True),
        Column("nhf_no", String, nullable=True),
        Column("nhf_mortgage_bank", String(45), nullable=True),
        Column("tax_state", String(45), nullable=True),
        Column("pfc_account_number", String(45), nullable=True),
        Column("pfc_name", String(45), nullable=True),
        Column("pfa_number", String(45), nullable=True),
        Column("pfa_name", String(45), nullable=True),
        Column("source_tag", String(45), nullable=True),
        Column("department_id", Integer, ForeignKey("departments.id"), nullable=True),
        Column("designation_id", Integer, ForeignKey("designations.id"), nullable=True),
        Column("organisation_id", Integer, ForeignKey("organisations.id"), nullable=True),
        Column("user_id", Integer, ForeignKey("users.id"), nullable=False),
        Column("template_id", Integer, ForeignKey("salary_templates.id"), nullable=True),
        Column('gross_pay', Float, nullable=True),
        Column("pension_value", Float, nullable=True),
        Column("nhf_value", Float, nullable=True),
        Column("password_hashed", String(250), nullable=True),
        Column("created_at", DateTime, default=datetime.datetime.utcnow),
        Column("updated_at", DateTime, default=datetime.datetime.utcnow, onupdate=datetime.datetime.utcnow),
    )

def downgrade() -> None:
    op.drop_table("employees")