#!/usr/bin/env python3
"""
Check the actual table structure in the database
"""

import sys
import os

# Add the app directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'app'))

from sqlalchemy.orm import Session
from sqlalchemy import text
from app.db.session import SessionLocal

def check_table_structure():
    """Check the actual table structure"""
    db = SessionLocal()
    
    try:
        print("Checking employees table structure...")
        
        # Get table columns
        result = db.execute(text("""
            SELECT column_name, data_type, is_nullable, column_default
            FROM information_schema.columns 
            WHERE table_name = 'employees'
            ORDER BY ordinal_position;
        """))
        
        columns = result.fetchall()
        
        if columns:
            print("\nEmployees table columns:")
            print("-" * 60)
            for col in columns:
                print(f"{col[0]:<25} {col[1]:<20} {col[2]:<10} {col[3] or ''}")
        else:
            print("No employees table found or no columns returned")
            
        # Check if there are any existing employees
        result = db.execute(text("SELECT COUNT(*) FROM employees"))
        count = result.scalar()
        print(f"\nExisting employees count: {count}")
        
        if count > 0:
            result = db.execute(text("SELECT employee_id, first_name, last_name, email FROM employees LIMIT 5"))
            employees = result.fetchall()
            print("\nExisting employees:")
            for emp in employees:
                print(f"  {emp[0]} - {emp[1]} {emp[2]} ({emp[3]})")
        
    except Exception as e:
        print(f"❌ Error checking table structure: {e}")
    finally:
        db.close()

if __name__ == "__main__":
    check_table_structure()
