from core.databases.database import db
from core.models.basemodel import ModelBase
class EmployeePayrollsModel(ModelBase):
    __tablename__ = "employee_payrolls"

    id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    gross_pay = db.Column(db.Double, nullable=False)
    net_pay = db.Column(db.Double, nullable=False)
    annual_tax = db.Column(db.Double, nullable=False)
    total_earnings = db.Column(db.Double, nullable=False)
    total_deductions = db.Column(db.Double, nullable=False)
    employee_id = db.Column(db.Integer, db.ForeignKey('employees.id'), nullable=False) 
    cost_company = db.Column(db.Integer, nullable=False)

    
    
    
      