"""
Test Email Service
Test if the email service is working properly
"""

import sys
import os
from datetime import datetime

# Add the app directory to the Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'app'))

def test_email_service():
    """Test the email service functionality"""

    print("📧 Testing Email Service")
    print("=" * 50)

    try:
        # Import email service
        from app.services.email_service import EmailService
        from app.core.config import settings

        print("✅ Email service imported successfully")
        print(f"📧 SMTP Server: {settings.SMTP_SERVER}")
        print(f"📧 SMTP Port: {settings.SMTP_PORT}")
        print(f"📧 Email User: {settings.EMAIL_USER}")
        print(f"📧 From Name: {settings.FROM_NAME}")

        # Initialize email service
        email_service = EmailService()
        print("✅ Email service initialized")

        # Test welcome email template
        print("\n📧 Testing welcome email for Ni<PERSON> Kumar M...")

        welcome_result = email_service.send_welcome_email(
            employee_name="<PERSON><PERSON>",
            employee_email="ni<PERSON><PERSON>@agnoshin.com",
            employee_id="EMP0008",
            temporary_password="qkXScDKKyFIs"
        )

        if welcome_result:
            print("✅ Welcome email sent successfully!")
            print("📧 <NAME_EMAIL> inbox")
        else:
            print("❌ Failed to send welcome email")

    except Exception as e:
        print(f"❌ Email service error: {e}")

def test_smtp_direct():
    """Test SMTP connection directly"""

    print("\n🔌 Testing Direct SMTP")
    print("=" * 50)

    try:
        import smtplib
        from email.mime.text import MIMEText
        from email.mime.multipart import MIMEMultipart

        # SMTP settings
        smtp_server = "smtp.gmail.com"
        smtp_port = 587
        email_user = "<EMAIL>"
        email_password = "nhhkkbkauxvczkzf"

        print(f"📧 Connecting to {smtp_server}:{smtp_port}")

        # Create SMTP connection
        server = smtplib.SMTP(smtp_server, smtp_port)
        server.starttls()
        server.login(email_user, email_password)

        print("✅ SMTP connection successful")

        # Send welcome email
        msg = MIMEMultipart('alternative')
        msg['From'] = f"AgnoShin HRMS <{email_user}>"
        msg['To'] = "<EMAIL>"
        msg['Subject'] = "🎉 Welcome to AgnoShin - Your Account is Ready!"

        html_body = """
        <html>
        <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
            <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
                <h2 style="color: #2c5aa0;">🎉 Welcome to AgnoShin!</h2>

                <p>Dear <strong>Nirmal Kumar M</strong>,</p>

                <p>Welcome to the AgnoShin team! We're excited to have you on board.</p>

                <div style="background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;">
                    <h3 style="color: #2c5aa0; margin-top: 0;">Your Login Credentials</h3>
                    <p><strong>Employee ID:</strong> EMP0008</p>
                    <p><strong>Email:</strong> <EMAIL></p>
                    <p><strong>Temporary Password:</strong> <code style="background-color: #e9ecef; padding: 2px 4px; border-radius: 4px;">qkXScDKKyFIs</code></p>
                </div>

                <div style="background-color: #fff3cd; padding: 15px; border-radius: 8px; border-left: 4px solid #ffc107;">
                    <p><strong>⚠️ Important:</strong> Please change your password after your first login for security.</p>
                </div>

                <h3 style="color: #2c5aa0;">Next Steps:</h3>
                <ol>
                    <li>Visit our HRMS portal: <a href="http://localhost:5173">http://localhost:5173</a></li>
                    <li>Log in with your credentials above</li>
                    <li>Complete your profile information</li>
                    <li>Review company policies and procedures</li>
                </ol>

                <p>If you have any questions or need assistance, please don't hesitate to reach out to our HR team.</p>

                <p>Welcome aboard!</p>

                <p>Best regards,<br>
                <strong>AgnoShin HR Team</strong></p>

                <hr style="border: none; border-top: 1px solid #eee; margin: 30px 0;">
                <p style="font-size: 12px; color: #666;">
                    This email was sent automatically by the AgnoShin HRMS system.
                </p>
            </div>
        </body>
        </html>
        """

        msg.attach(MIMEText(html_body, 'html'))

        server.send_message(msg)
        server.quit()

        print("✅ Welcome email sent successfully!")
        print("📧 Check <EMAIL> inbox")

    except Exception as e:
        print(f"❌ SMTP error: {e}")

if __name__ == "__main__":
    test_email_service()
    test_smtp_direct()
