from datetime import datetime
from core.models.approval_history import ApprovalHistoryModel
from core.databases.database import db
from core.models.approvals import ApprovalModel
from core.repositories.user import UserRepository


class ApprovalHistoryRepository:

    @staticmethod
    def get_by_id(approval_history_id):
        return db.session.query(ApprovalHistoryModel).filter_by(id=approval_history_id).first()

    @staticmethod
    def get_all_by_employee_id(employee_id):
        return db.session.query(ApprovalHistoryModel).filter_by(employee_id=employee_id).all()

    @staticmethod
    def update(approval_history, data):
        approval_history.status = data["status"]
        approval_history.reason = data["reason"] if data["reason"] else ""
        
        if data["status"].lower() in ["approved", "declined"]:
            approval_history.date_approved = datetime.utcnow()
        if "pay_schedules_id" in data:
            approval_history.pay_schedules_id = data["pay_schedules_id"]
        db.session.commit()
        return approval_history

    @staticmethod
    def get_all_by_authenticated_user():
        user_id = UserRepository().authUserId()
        return (
            db.session.query(ApprovalHistoryModel)
            .join(ApprovalModel, ApprovalModel.id == ApprovalHistoryModel.approval_id)
            .filter(ApprovalModel.user_id == user_id)
            .order_by(ApprovalHistoryModel.created_at.desc())
            .all()
        )

    @staticmethod
    def get_all_by_status_for_authenticated_user(status):
        user_id = UserRepository().authUserId()
        return (
            db.session.query(ApprovalHistoryModel)
            .join(ApprovalModel, ApprovalModel.id == ApprovalHistoryModel.approval_id)
            .filter(ApprovalModel.user_id == user_id)
            .filter(ApprovalHistoryModel.status.ilike(status))
            .order_by(ApprovalHistoryModel.created_at.desc())
            .all()
        )
        
    @staticmethod
    def get_all_by_employee_id(employee_id):
        return (
            db.session.query(ApprovalHistoryModel)
            .filter_by(employee_id=employee_id)
            .order_by(ApprovalHistoryModel.created_at.desc())
            .all()
        )

    @staticmethod
    def get_by_status_for_employee(employee_id, status):
        return (
            db.session.query(ApprovalHistoryModel)
            .filter_by(employee_id=employee_id)
            .filter(ApprovalHistoryModel.status.ilike(status))
            .order_by(ApprovalHistoryModel.created_at.desc())
            .all()
        )