# HRMS Backend Testing Guide

## 🎯 Overview

This guide provides comprehensive instructions for testing the HRMS backend system, including database connectivity, API endpoints, and AI-powered features.

## 📋 Prerequisites

### 1. System Requirements
- Python 3.9 or higher
- PostgreSQL 12 or higher
- Git

### 2. Database Setup
Ensure PostgreSQL is running and create a database for testing:

```sql
-- Connect to PostgreSQL as superuser
CREATE DATABASE hrms_test;
CREATE USER hrms_user WITH PASSWORD 'your_password';
GRANT ALL PRIVILEGES ON DATABASE hrms_test TO hrms_user;
```

### 3. Environment Configuration
Create a `.env` file in the `hrms_backend` directory:

```env
# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_NAME=hrms_test
DB_USER=hrms_user
DB_PASSWORD=your_password
DB_SCHEMA=public

# Application Configuration
SECRET_KEY=your-secret-key-here
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# AI Features
AI_CATEGORIZATION_ENABLED=true
AI_CONFIDENCE_THRESHOLD=0.7
SENTIMENT_ANALYSIS_ENABLED=true
SMART_ROUTING_ENABLED=true

# Real-time Features
WEBSOCKET_ENABLED=true
REDIS_URL=redis://localhost:6379

# Multi-channel Integration
EMAIL_INTEGRATION_ENABLED=true
CHATBOT_INTEGRATION_ENABLED=true
```

## 🚀 Quick Start

### 1. Install Dependencies
```bash
cd hrms_backend
pip install -r test_requirements.txt
```

### 2. Setup Database
```bash
python setup_database.py
```

### 3. Run All Tests
```bash
python run_tests.py
```

## 🔧 Manual Testing

### 1. Database Testing Only
```bash
python test_backend_database.py
```

### 2. API Testing Only
First, start the server:
```bash
uvicorn app.main:app --reload --port 8000
```

Then in another terminal:
```bash
python test_api_endpoints.py
```

### 3. Individual Component Testing

#### Test Database Connection
```python
from app.db.session import test_connection
print("Database connected:", test_connection())
```

#### Test AI Categorization
```python
from app.services.ticket_management.ai_categorization_service import AICategorization

ai_cat = AICategorization()
result = await ai_cat.categorize_ticket(
    "Password reset needed",
    "I can't access my email account"
)
print(result)
```

#### Test Sentiment Analysis
```python
from app.services.ticket_management.sentiment_analysis_service import SentimentAnalysisService

sentiment = SentimentAnalysisService()
result = await sentiment.analyze_sentiment(
    "I'm really frustrated with this issue!"
)
print(result)
```

## 📊 Test Coverage

### Database Tests
- ✅ Connection testing
- ✅ Table creation and structure
- ✅ CRUD operations
- ✅ Relationships and foreign keys
- ✅ Data integrity constraints
- ✅ Performance queries
- ✅ AI metadata storage

### API Tests
- ✅ Server health check
- ✅ Authentication endpoints
- ✅ Core ticket management
- ✅ AI-powered features
- ✅ Multi-channel integration
- ✅ Reporting and analytics
- ✅ Template management
- ✅ WebSocket endpoints

### AI Features Tests
- ✅ Ticket categorization
- ✅ Sentiment analysis
- ✅ Smart routing
- ✅ Urgency detection
- ✅ Escalation risk assessment
- ✅ Knowledge base search

## 🔍 Test Results

### Understanding Test Reports

#### Database Test Report (`test_report.json`)
```json
{
  "test_summary": {
    "total_tests": 25,
    "passed_tests": 24,
    "failed_tests": 1,
    "success_rate": 96.0
  },
  "database_info": {
    "host": "localhost",
    "port": 5432,
    "database": "hrms_test"
  }
}
```

#### API Test Report (`api_test_report.json`)
```json
{
  "test_summary": {
    "total_tests": 30,
    "passed_tests": 28,
    "failed_tests": 2,
    "success_rate": 93.33
  },
  "api_info": {
    "base_url": "http://localhost:8000"
  }
}
```

#### Comprehensive Report (`comprehensive_test_report.json`)
Contains combined results from all test suites with overall success metrics.

## 🐛 Troubleshooting

### Common Issues

#### 1. Database Connection Failed
**Error**: `Database connection failed`
**Solutions**:
- Check if PostgreSQL is running: `sudo systemctl status postgresql`
- Verify database credentials in `.env` file
- Ensure database exists: `psql -U postgres -c "\l"`
- Check firewall settings

#### 2. Import Errors
**Error**: `ModuleNotFoundError: No module named 'app'`
**Solutions**:
- Ensure you're in the `hrms_backend` directory
- Check Python path: `export PYTHONPATH="${PYTHONPATH}:$(pwd)"`
- Install dependencies: `pip install -r test_requirements.txt`

#### 3. Server Won't Start
**Error**: `Server is not responding`
**Solutions**:
- Check if port 8000 is available: `lsof -i :8000`
- Try a different port: `uvicorn app.main:app --port 8001`
- Check for syntax errors in code
- Verify all dependencies are installed

#### 4. AI Features Not Working
**Error**: `AI categorization failed`
**Solutions**:
- Check if AI features are enabled in `.env`
- Verify confidence threshold settings
- Review model dependencies
- Check for sufficient memory

#### 5. WebSocket Connection Issues
**Error**: `WebSocket connection failed`
**Solutions**:
- Verify WebSocket endpoint is accessible
- Check authentication tokens
- Review firewall settings
- Monitor connection limits

### Performance Issues

#### Slow Database Queries
- Check database indexes
- Analyze query execution plans
- Consider connection pooling
- Monitor database performance

#### High Memory Usage
- Review AI model loading
- Check for memory leaks
- Monitor connection counts
- Optimize data structures

## 📈 Performance Benchmarks

### Expected Performance Metrics

#### Database Operations
- Simple CRUD: < 10ms
- Complex joins: < 100ms
- Bulk operations: < 1s per 1000 records

#### API Response Times
- Health check: < 5ms
- Simple endpoints: < 50ms
- AI-powered endpoints: < 500ms
- Complex reports: < 2s

#### AI Processing
- Categorization: < 200ms
- Sentiment analysis: < 300ms
- Smart routing: < 400ms

## 🔒 Security Testing

### Authentication Tests
- Token validation
- Permission checks
- Role-based access
- Session management

### Data Protection Tests
- Input validation
- SQL injection prevention
- XSS protection
- Data encryption

## 📝 Test Maintenance

### Adding New Tests

#### Database Tests
1. Add test method to `DatabaseTester` class
2. Include in test execution sequence
3. Update expected results

#### API Tests
1. Add test method to `APITester` class
2. Include endpoint in test suite
3. Handle authentication requirements

### Updating Test Data
- Modify test data in `create_test_data()` method
- Ensure cleanup in `cleanup_test_data()` method
- Update expected results accordingly

## 🎯 Best Practices

### Test Development
- Write descriptive test names
- Include error handling
- Clean up test data
- Document expected behavior

### Test Execution
- Run tests in isolated environment
- Use consistent test data
- Monitor resource usage
- Review test reports

### Continuous Integration
- Automate test execution
- Set up test notifications
- Monitor test trends
- Maintain test environments

## 📞 Support

### Getting Help
- Check error logs in test reports
- Review this troubleshooting guide
- Verify environment configuration
- Test individual components

### Reporting Issues
When reporting test failures, include:
- Complete error messages
- Environment configuration
- Test execution logs
- System specifications

---

**Last Updated**: 2024-01-01  
**Version**: 1.0.0  
**Maintainer**: HRMS Development Team
