#!/usr/bin/env python3
"""
Check employee table schema
"""

import psycopg2
import os
from dotenv import load_dotenv

load_dotenv()

def check_employee_schema():
    """Check employee table structure"""
    
    conn = psycopg2.connect(
        host=os.getenv("DB_HOST", "localhost"),
        database=os.getenv("DB_NAME", "hrms_db"),
        user=os.getenv("DB_USER", "postgres"),
        password=os.getenv("DB_PASSWORD", "password"),
        port=os.getenv("DB_PORT", "5432")
    )
    
    try:
        cursor = conn.cursor()
        
        # Get employee table schema
        cursor.execute("""
            SELECT column_name, data_type, is_nullable
            FROM information_schema.columns 
            WHERE table_name = 'employees'
            ORDER BY ordinal_position
        """)
        
        columns = cursor.fetchall()
        print("employees table schema:")
        print("-" * 50)
        for col in columns:
            print(f"{col[0]:<25} {col[1]:<15} {col[2]}")
        
        # Check if there are any employees <NAME_EMAIL>
        cursor.execute("SELECT email FROM employees WHERE email = '<EMAIL>'")
        employee_exists = cursor.fetchone()
        
        if employee_exists:
            print(f"\n✅ Employee record <NAME_EMAIL>")
        else:
            print(f"\n❌ No employee <NAME_EMAIL>")
            
        # Check where organization_id comes from in JWT
        print("\nLet me check the authentication code to see where organization_id comes from...")
        
    except Exception as e:
        print(f"Error: {e}")
    finally:
        cursor.close()
        conn.close()

if __name__ == "__main__":
    check_employee_schema()
