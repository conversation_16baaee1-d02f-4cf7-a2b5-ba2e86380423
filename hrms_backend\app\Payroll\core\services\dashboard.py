from calendar import month
from core.repositories.dashboard import DashboardRepository



class DashboardService:
    def __init__(self):
        self.repository = DashboardRepository()

    def getDashboardData(self):
        return {
            "total_net_pay": self.repository.totalNetPay(),
            "total_monthly_pay": self.repository.totalMonthlyPay(),
            "total_monthly_tax": self.repository.totalMonthlyTax(),
            "total_organizations": self.repository.totalOrganizations(),
            "total_Statutory_deduction": self.repository.totalStatutoryDeduction(),

        }
    
    def getPayrollData(self, month=None, year=None):
        """Retrieve payroll summary."""
        return DashboardRepository.get_payroll_summary(year, month)