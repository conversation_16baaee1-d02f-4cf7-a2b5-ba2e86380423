from datetime import datetime
from core.models.prorate_salary import ProrateSalaryModel
from core.databases.database import db
from core.repositories.user import UserRepository
from sqlalchemy import desc
import traceback
from sqlalchemy import extract

class ProrateSalaryRepository:
    def __init__(self) -> None:
        pass
    
    @classmethod
    def create_prorate_salary(cls, **kwargs):
        # Retrieve the authenticated user's ID
        user_id = UserRepository.authUserId()
        kwargs['user_id'] = user_id  # Add user_id to the keyword arguments

        # Create a new instance of the ProrateSalaryModel
        prorate_salary = ProrateSalaryModel(**kwargs)
        db.session.add(prorate_salary)
        db.session.commit()

        return prorate_salary  # Return the newly created record

   
    @classmethod
    def fetch_all(cls):
        try:
            return ProrateSalaryModel.query.filter_by(
                user_id=UserRepository.authUserId()
            ).order_by(
                ProrateSalaryModel.timestamp.desc()
            ).all()
        except Exception as e:
            print(f"Error fetching prorate salary records: {str(e)}")
            traceback.print_exc()
            return []

    @classmethod
    def get_by_id(cls, id):
        try:
            return ProrateSalaryModel.query.filter(
                ProrateSalaryModel.id == id,
                ProrateSalaryModel.user_id == UserRepository.authUserId()
            ).first()
        except Exception as e:
            print(f"Error fetching prorate salary by ID: {str(e)}")
            traceback.print_exc()
            return None
        
    @staticmethod
    def get_by_employee_id(employee_id):
        """
        Fetch a prorated salary record by employee ID.
        
        :param employee_id: The ID of the employee.
        :return: ProrateSalaryModel instance or None if not found.
        """
        return ProrateSalaryModel.query.filter_by(employee_id=employee_id).first()

    # @classmethod
    # def get_most_recent_by_employee_id(cls, employee_id):
    #     """
    #     Fetch the most recent prorated salary record by employee ID.
    #     """
    #     try:
    #         return (
    #             ProrateSalaryModel.query.filter_by(employee_id=employee_id)
    #             .order_by(ProrateSalaryModel.timestamp.desc())  # Order by timestamp to get the latest
    #             .first()  # Get the most recent record
    #         )
    #     except Exception as e:
    #         print(f"Error fetching the most recent prorate salary record: {str(e)}")
    #         traceback.print_exc()
    #         return None
        
    @classmethod
    def get_most_recent_by_employee_id(cls, employee_id):
        """
        Fetch the most recent prorated salary record for the current month by employee ID.
        
        If the record is not from the current month, it will be ignored.
        """
        try:
            # Get the current month and year
            current_month = datetime.now().month
            current_year = datetime.now().year

            # Query for the most recent prorated salary in the current month and year
            return (
                ProrateSalaryModel.query.filter_by(employee_id=employee_id)
                .filter(
                    db.extract('month', ProrateSalaryModel.timestamp) == current_month,
                    db.extract('year', ProrateSalaryModel.timestamp) == current_year
                )
                .order_by(ProrateSalaryModel.timestamp.desc())
                .first()
            )
        except Exception as e:
            print(f"Error fetching the most recent prorate salary record: {str(e)}")
            traceback.print_exc()
            return None
        
    @classmethod
    def get_latest_by_employee_month_year(cls, employee_id, month, year):
        """
        Fetch the most recent prorated salary record for a specific employee, month, and year.
        
        :param employee_id: The ID of the employee.
        :param month: The month for which to fetch the prorated salary.
        :param year: The year for which to fetch the prorated salary.
        :return: ProrateSalaryModel instance or None if not found.
        """
        try:
            # Query for the most recent prorated salary record for the specified month and year
            return (
                ProrateSalaryModel.query
                .filter_by(employee_id=employee_id)
                .filter(
                    extract('month', ProrateSalaryModel.timestamp) == month,
                    extract('year', ProrateSalaryModel.timestamp) == year
                )
                .order_by(ProrateSalaryModel.timestamp.desc())
                .first()
            )
        except Exception as e:
            print(f"Error fetching the latest prorate salary record: {str(e)}")
            traceback.print_exc()
            return None