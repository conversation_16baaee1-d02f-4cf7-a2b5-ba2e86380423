#!/usr/bin/env python3
"""
Test script to verify all previously failing endpoints are now working
"""

import requests
import json
import sys
from datetime import datetime, timedelta
from jose import jwt

# Configuration
BASE_URL = "http://localhost:8001"
SECRET_KEY = "your-super-secret-jwt-key-change-this-in-production"  # Matches app config
ALGORITHM = "HS256"

def create_test_token(role="admin"):
    """Create a test JWT token"""
    test_user_data = {
        "sub": "test-user-123",
        "email": "<EMAIL>", 
        "role": role.upper(),
        "organization_id": "test-org-123",
        "exp": datetime.utcnow() + timedelta(hours=24)
    }
    
    token = jwt.encode(test_user_data, SECRET_KEY, algorithm=ALGORITHM)
    return token

def test_endpoint(method, endpoint, token=None, data=None, description=""):
    """Test an API endpoint"""
    url = f"{BASE_URL}{endpoint}"
    headers = {"Content-Type": "application/json"}
    
    if token:
        headers["Authorization"] = f"Bearer {token}"
    
    try:
        if method.upper() == "GET":
            response = requests.get(url, headers=headers)
        elif method.upper() == "POST":
            response = requests.post(url, headers=headers, json=data)
        elif method.upper() == "PUT":
            response = requests.put(url, headers=headers, json=data)
        elif method.upper() == "DELETE":
            response = requests.delete(url, headers=headers)
        else:
            print(f"❌ Unsupported method: {method}")
            return False
        
        status_code = response.status_code
        
        if status_code == 200:
            print(f"✅ {description}")
            print(f"   Status: {status_code}")
            if response.text:
                try:
                    data = response.json()
                    if isinstance(data, list):
                        print(f"   Response: List with {len(data)} items")
                        if data:
                            print(f"   Sample: {json.dumps(data[0], indent=2)[:200]}...")
                    else:
                        print(f"   Response: {json.dumps(data, indent=2)[:200]}...")
                except:
                    print(f"   Response: {response.text[:100]}...")
            print()
            return True
        else:
            print(f"❌ {description}")
            print(f"   Status: {status_code}")
            print(f"   Error: {response.text[:200]}...")
            print()
            return False
            
    except requests.exceptions.ConnectionError:
        print(f"❌ {description}")
        print(f"   Error: Cannot connect to {url}")
        print(f"   Make sure the HRMS backend server is running on port 8001")
        print()
        return False
    except Exception as e:
        print(f"❌ {description}")
        print(f"   Error: {str(e)}")
        print()
        return False

def main():
    """Run comprehensive endpoint tests"""
    print("🧪 Testing Previously Failing API Endpoints")
    print("=" * 50)
    
    # Create test tokens for different roles
    admin_token = create_test_token("admin")
    employee_token = create_test_token("employee")
    
    # Test results tracking
    tests = []
    
    # 1. Health Check (should work without auth)
    tests.append(test_endpoint(
        "GET", "/health", 
        description="Health Check Endpoint"
    ))
    
    # 2. Auth/Me endpoint (was giving 500 error)
    tests.append(test_endpoint(
        "GET", "/api/auth/me", admin_token,
        description="Auth Me Endpoint (/api/auth/me)"
    ))
    
    # 3. Employee listing (was giving 500 error)
    tests.append(test_endpoint(
        "GET", "/api/employees/", admin_token,
        description="Employee Listing (/api/employees/)"
    ))
    
    # 4. Employee details
    tests.append(test_endpoint(
        "GET", "/api/employees/", admin_token,
        description="Employee Details with Role Information"
    ))
    
    # 5. Attendance endpoints (was giving 500 error)
    tests.append(test_endpoint(
        "GET", "/api/attendance/", admin_token,
        description="Attendance Listing (/api/attendance/)"
    ))
    
    # 6. Leave management endpoints (was giving 404/500 errors)
    tests.append(test_endpoint(
        "GET", "/api/leave/", admin_token,
        description="Leave Management (/api/leave/)"
    ))
    
    # 7. Test with employee role
    tests.append(test_endpoint(
        "GET", "/api/employees/", employee_token,
        description="Employee Access with Employee Role"
    ))
    
    # 8. Organizations endpoint
    tests.append(test_endpoint(
        "GET", "/api/organizations/", admin_token,
        description="Organizations Endpoint"
    ))
    
    # 9. Departments endpoint
    tests.append(test_endpoint(
        "GET", "/api/departments/", admin_token,
        description="Departments Endpoint"
    ))
    
    # 10. Test employee creation with role
    new_employee_data = {
        "employee_id": "TEST001",
        "first_name": "Test",
        "last_name": "User",
        "email": "<EMAIL>",
        "phone": "+1234567890",
        "role": "employee",
        "hire_date": "2024-01-01"
    }
    
    tests.append(test_endpoint(
        "POST", "/api/employees/", admin_token, new_employee_data,
        description="Create Employee with Role"
    ))
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 TEST SUMMARY")
    print("=" * 50)
    
    passed = sum(tests)
    total = len(tests)
    
    print(f"✅ Passed: {passed}/{total}")
    print(f"❌ Failed: {total - passed}/{total}")
    
    if passed == total:
        print("\n🎉 ALL TESTS PASSED! The 500 errors have been fixed!")
        print("\n✅ Key Fixes Verified:")
        print("   • Database schema mismatch resolved")
        print("   • Role field added to Employee model")
        print("   • Authentication system working")
        print("   • All CRUD operations functional")
        print("   • Employee roles properly stored and retrieved")
    else:
        print(f"\n⚠️  {total - passed} tests still failing. Check the errors above.")
        
    print("\n🔧 Next Steps:")
    print("   • Test the frontend integration")
    print("   • Verify role-based access control")
    print("   • Test complex operations (leave requests, attendance)")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
