from flask import url_for, jsonify
from flask.views import MethodView
from flask_smorest import Blueprint, abort
from core.middleware import roles_required
from schemas import EmployeeSchema
from core.services.zoho_api_service import ZohoAPIService
from core.utils.responseBuilder import ResponseBuilder
from datetime import datetime
from passlib.hash import pbkdf2_sha256
from core.services.employee import EmployeeService

blueprint = Blueprint("bulk_employee", __name__, description="Operations for bulk employee registeration")
    
@blueprint.route("/bulk_employee/zoho_people")
class EmployeeBulkUpload(MethodView):
    @roles_required(['admin'])
    @blueprint.response(200)
    def get(self):        
        try:
            service = ZohoAPIService()
            zoho_employees = service.get_employees()        
        except Exception as e:
            abort(400, message=f"Error: {str(e)}")
   

        employee_data = []
        existing_count = 0
        created_count = 0

        for employee in zoho_employees:
            zoho_id = next(iter(employee.keys())) 
            zoho_emp = employee[zoho_id][0]

            existing_employee = EmployeeService().getEmployeeByEmailOrId(
                zoho_emp.get('EmailID', '').strip(),
                zoho_emp.get('EmployeeID', '').strip()
            )
            
            if existing_employee:
                existing_count += 1
                continue

            hire_date = None
            if zoho_emp.get('Dateofjoining'):
                try:
                    hire_date = datetime.strptime(zoho_emp['Dateofjoining'], '%d-%b-%Y').strftime('%Y-%m-%d')
                except:
                    hire_date = None
            
            dob = None
            if zoho_emp.get('Date_of_birth'):
                try:
                    dob = datetime.strptime(zoho_emp['Date_of_birth'], '%d-%b-%Y').strftime('%Y-%m-%d')
                except:
                    dob = None
                                
            department= zoho_emp.get('Department', '').strip()
            department_id = None #create_if_not_exist(department)

            designation = zoho_emp.get('Department', '').strip()
            designation_id = None #create_if_not_exist(designation)
            

            emp = {
                "first_name": zoho_emp.get('FirstName', '').strip(),
                "last_name": zoho_emp.get('LastName', '').strip(),
                "email": zoho_emp.get('EmailID', '').strip(),
                "gender": zoho_emp.get('Gender', '').strip(),
                "hire_date": hire_date,
                "dob": dob,
                "department_id": department_id,
                "designation_id": designation_id,
                "status": zoho_emp.get('Employeestatus', '').strip(),
                "business_unit": "", 
                "employee_type": zoho_emp.get('Employee_type', '').strip(),
                "employeeID": zoho_emp.get('EmployeeID', '').strip(),
                "division": "",
                "employment_type": zoho_emp.get('Employee_type', '').strip(),
                "address": zoho_emp.get('Work_location', '').strip(),
                "city": zoho_emp.get('LocationName', '').strip(),
                "state": "",
                "country": "",
                "phone": zoho_emp.get('Mobile', zoho_emp.get('Work_phone', '')),
                "role": "Employee",
                "level": "", 
                "work_schedule": "",
                "gross_pay": 0.0, 
                "source_tag": "Imported(Zohopeople)",

                "password_hash": pbkdf2_sha256.hash("password"),
                "taxID": None,
                "bank_name": None,
                "sort_code": None,
                "zip_code": None,
                "salary_type": None,
                "rate": None,
                "hours_worked": None,
                "number_of_days_worked": 0,
                "organisation_id": None,
                "template_id": None,
                "bank_account": None,
                "tax_type": None,
                "currency": None,
                "monthly_tax": 0.0,
                "pension_no": None,
                "pension_pfa": None,
                "nhf_no": None,
                "nhf_mortgage_bank": None,
                "pfa_name": None,
                "pfa_number": None,
                "tax_state": None,
                "pfc_name": None,
                "pfc_account_number": None,
                "annual_tax": 0.0,
                "annual_leave_days": None,
                "unpaid_leave_days": None,
                "sick_leave_days": None,
                "maternity_paternity_leave_days": None,
                "casual_leave_days": None,
                "compassionate_leave_days": None,
                "total_working_days": None,
                "total_present_days": None,
                "total_absent_days": None,
                "total_taxable_monthly_sum": 0.0,
                "total_taxable_annual_sum": 0.0,
                "total_non_taxable_monthly_sum": 0.0,
                "total_non_taxable_annual_sum": 0.0,
                "total_statutory_monthly_sum": 0.0,
                "total_statutory_annual_sum": 0.0,
                "total_other_deductions_monthly_sum": 0.0,
                "total_other_deductions_annual_sum": 0.0,
                "netpay": 0.0,
                "is_prorated": None,
            }
            employee_data.append(emp)
            employees_schema = EmployeeSchema().dump(emp)
            EmployeeService().createEmployee(employees_schema)

        return ResponseBuilder(
            data={
                "total_zoho_records": len(zoho_employees),
                "existing_records": existing_count,
                "created_records": created_count,
                "failed_records": len(zoho_employees) - existing_count - created_count
            }, 
            status_code=201, 
            total=None, meta=None,
            message=f"Zoho people import completed. {created_count} new records created, {existing_count} existing records skipped."
        ).build()
