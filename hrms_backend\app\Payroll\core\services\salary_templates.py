from core.repositories.salary_templates import SalaryTemplatesRepository
from core.repositories.salary_templates_component import SalaryTemplatesComponentRepository
from core.repositories.salary_templates_benefit import SalaryTemplatesBenefitRepository
from flask_smorest import abort
from core.services.salary_components import SalaryComponentsService
from core.services.benefits import BenefitsService

class SalaryTemplatesService:
    def __init__(self) -> None:
        self.repository = SalaryTemplatesRepository()
        self.benefit_repo = SalaryTemplatesBenefitRepository()
        self.benefit_service = BenefitsService()

        self.component_repo = SalaryTemplatesComponentRepository()
        self.component_service = SalaryComponentsService()


    def createSalaryTemplates(self, Kwargs):
        return self.repository.createSalaryTemplates(**Kwargs)
    
    def fetchAll(self):
        salary_templates = self.repository.fetchAll()
        total_salary_templates = len(salary_templates)
        return salary_templates, total_salary_templates
    
    def getSalaryTemplates(self, id):
        return self.repository.getSalaryTemplates(id)
    
    def updateSalaryTemplates(self, id, Kwargs):
        return self.repository.updateSalaryTemplates(id, **Kwargs)
    
    def getSalaryTemplatesByKey(self, Kwarg):
        return self.repository.getSalaryTemplatesByKeys(Kwarg)
    
    def deleteSalaryTemplates(self, id):
        return self.repository.deleteSalaryTemplates(id)
    
    def save_salary_template(self, Kwargs, template_id=None):
        selected_components = Kwargs.pop("salary_components_id", [])
        selected_benefits = Kwargs.pop("benefits_id", [])
        if template_id is not None:
            # Update logic
            salary_template = self.getSalaryTemplates(template_id)
            if not salary_template:
                abort(404, message="Template does not exist")
        
            updated_template = self.updateSalaryTemplates(template_id, Kwargs)

            # Clear existing associations
            self.component_repo.delete_components_by_template_id(template_id)
            self.benefit_repo.delete_components_by_template_id(template_id)
        else:
            # Create logic
            updated_template = self.createSalaryTemplates(Kwargs)

        # Add components
        if selected_components is not None:
            for component_id in selected_components:
                component = SalaryComponentsService().getSalaryComponents(component_id)
                if component:
                    self.component_repo.create(updated_template.id, component.id)

        # Add benefits
        if selected_benefits is not None:
            for benefit_id in selected_benefits:
                benefit = self.benefit_service.getBenefits(benefit_id)
                if benefit:
                    self.benefit_repo.create(updated_template.id, benefit.id)

        return updated_template
