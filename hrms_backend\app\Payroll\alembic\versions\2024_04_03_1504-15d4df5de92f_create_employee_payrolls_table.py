"""create employee_payrolls table

Revision ID: 15d4df5de92f
Revises: 8e42ed311908
Create Date: 2024-04-03 15:04:56.780480

"""
from typing import Sequence, Union

from alembic import op
from sqlalchemy import func, TIMESTAMP, ForeignKey
from sqlalchemy import Column, Integer, Float

# revision identifiers, used by Alembic.
revision: str = '15d4df5de92f'
down_revision: Union[str, None] = '8e42ed311908'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    op.create_table(
        'employee_payrolls',
        Column('id', Integer, primary_key=True),
        <PERSON>umn('gross_pay', Float, nullable=False),
        <PERSON>umn('net_pay', Float, nullable=False),
        <PERSON>umn('annual_tax', Float, nullable=False),
        <PERSON>umn('total_earnings', Float, nullable=False),
        <PERSON>umn('total_deductions', Float, nullable=False),
        <PERSON>umn('employee_id', Integer, ForeignKey("employees.id")),
        <PERSON><PERSON>n('cost_company', Integer, nullable=False),
        Column("timestamp", TIMESTAMP, server_default=func.now()),
    )


def downgrade() -> None:
    op.drop_table("employee_payrolls")
