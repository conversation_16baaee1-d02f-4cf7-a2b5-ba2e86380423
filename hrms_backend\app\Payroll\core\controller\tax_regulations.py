from flask import url_for
from flask.views import MethodView
from flask_smorest import Blueprint, abort
from core.middleware import roles_required
from schemas import TaxRegulationSchema
from sqlalchemy.orm.exc import NoResultFound
from sqlalchemy.exc import SQLAlchemyError, IntegrityError
import core.utils.response_message as RESPONSEMESSAGE
from core.services.tax_regulations import TaxRegulationsService

blueprint = Blueprint("Tax Regulations", __name__, description="Operations for Tax Regulations")
    
@blueprint.route("/tax_regulations/<id>")
class TaxRegulationList(MethodView):
    @roles_required(['admin'])
    @blueprint.response(200, TaxRegulationSchema)
    def get(self, id):
        service = TaxRegulationsService()
        regulation = service.getTaxRegulations(id)
        if not regulation:
            abort(401, message="Tax Regulation does not exist")
        return regulation    
    
    @roles_required(['admin'])
    def delete(self, id):
        service = TaxRegulationsService()
        regulation = service.getTaxRegulations(id)
        if not regulation:
            abort(404, message="Tax Regulation does not exist")
        service.deleteTaxRegulations(id)
        return {"message" : "Tax Regulation deleted"}
    
    @roles_required(['admin'])
    @blueprint.arguments(TaxRegulationSchema)
    @blueprint.response(201, TaxRegulationSchema)
    def put(self, id, data):
        service = TaxRegulationsService()
        regulation = service.getTaxRegulations(id)
        if not regulation:
            abort(404, message="Tax Regulation does not exist")
        try :
            new_regulation = service.updateTaxRegulations(id, data)
            return new_regulation
        except SQLAlchemyError:
                abort(500, message="Error while updating Tax Regulation")
    
@blueprint.route("/tax_regulations")
class TaxRegulation(MethodView):
    @roles_required(['admin'])
    @blueprint.arguments(TaxRegulationSchema)
    @blueprint.response(200, TaxRegulationSchema)
    def post(self, data):
        try:
            service = TaxRegulationsService()
            regulation = service.getTaxRegulationsByKey({"id": data['id']})
            if not regulation:
                new_regulation = service.createTaxRegulations(data)
            else:
                abort(400, message="Tax Regulation already exist")
        except IntegrityError:
            abort(500, message="Error while creating Tax Regulation")
        except SQLAlchemyError as e:
            print(e)
            abort(500, message="Error while creating Tax Regulation")
        return new_regulation