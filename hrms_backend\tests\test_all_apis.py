#!/usr/bin/env python3
"""
Comprehensive API Testing Script for HRMS Backend
Tests all available API endpoints
"""

import requests
import json
from datetime import datetime, date

BASE_URL = "http://localhost:8001"
ADMIN_TOKEN = None
EMPLOYEE_TOKEN = None

def get_auth_tokens():
    """Get authentication tokens for testing"""
    global ADMIN_TOKEN, EMPLOYEE_TOKEN
    
    # Admin login
    admin_response = requests.post(f"{BASE_URL}/api/auth/login", json={
        "email": "ADMIN001",
        "password": "password123"
    })
    
    if admin_response.status_code == 200:
        ADMIN_TOKEN = admin_response.json()["access_token"]
        print("✅ Admin token obtained")
    else:
        print("❌ Failed to get admin token")
        return False
    
    # Employee login
    employee_response = requests.post(f"{BASE_URL}/api/auth/login", json={
        "email": "EMP001", 
        "password": "password123"
    })
    
    if employee_response.status_code == 200:
        EMPLOYEE_TOKEN = employee_response.json()["access_token"]
        print("✅ Employee token obtained")
    else:
        print("❌ Failed to get employee token")
        return False
    
    return True

def test_auth_endpoints():
    """Test authentication endpoints"""
    print("\n🔐 Testing Authentication Endpoints")
    print("-" * 50)
    
    endpoints = [
        ("POST", "/api/auth/login", {"email": "ADMIN001", "password": "password123"}),
        ("GET", "/api/auth/validate", None, {"Authorization": f"Bearer {ADMIN_TOKEN}"}),
        ("GET", "/api/auth/me", None, {"Authorization": f"Bearer {ADMIN_TOKEN}"}),
        ("POST", "/api/auth/logout", {})
    ]
    
    for method, endpoint, data, *headers in endpoints:
        try:
            headers_dict = headers[0] if headers else {}
            if method == "GET":
                response = requests.get(f"{BASE_URL}{endpoint}", headers=headers_dict)
            else:
                response = requests.post(f"{BASE_URL}{endpoint}", json=data, headers=headers_dict)
            
            status = "✅" if response.status_code in [200, 201] else "❌"
            print(f"{status} {method} {endpoint} - {response.status_code}")
        except Exception as e:
            print(f"❌ {method} {endpoint} - Error: {e}")

def test_employee_endpoints():
    """Test employee management endpoints"""
    print("\n👥 Testing Employee Endpoints")
    print("-" * 50)
    
    headers = {"Authorization": f"Bearer {ADMIN_TOKEN}"}
    
    endpoints = [
        ("GET", "/api/employees/"),
        ("GET", "/api/employees/departments/"),
        ("GET", "/api/employees/designations/"),
    ]
    
    for method, endpoint in endpoints:
        try:
            response = requests.get(f"{BASE_URL}{endpoint}", headers=headers)
            status = "✅" if response.status_code in [200, 201] else "❌"
            print(f"{status} {method} {endpoint} - {response.status_code}")
        except Exception as e:
            print(f"❌ {method} {endpoint} - Error: {e}")

def test_attendance_endpoints():
    """Test attendance endpoints"""
    print("\n⏰ Testing Attendance Endpoints")
    print("-" * 50)
    
    headers = {"Authorization": f"Bearer {ADMIN_TOKEN}"}
    
    endpoints = [
        ("GET", "/api/attendance/"),
        ("GET", "/api/attendance/my/status", {"Authorization": f"Bearer {EMPLOYEE_TOKEN}"}),
    ]
    
    for method, endpoint, *custom_headers in endpoints:
        try:
            headers_to_use = custom_headers[0] if custom_headers else headers
            response = requests.get(f"{BASE_URL}{endpoint}", headers=headers_to_use)
            status = "✅" if response.status_code in [200, 201] else "❌"
            print(f"{status} {method} {endpoint} - {response.status_code}")
        except Exception as e:
            print(f"❌ {method} {endpoint} - Error: {e}")

def test_leave_endpoints():
    """Test leave management endpoints"""
    print("\n🏖️ Testing Leave Endpoints")
    print("-" * 50)
    
    headers = {"Authorization": f"Bearer {ADMIN_TOKEN}"}
    
    endpoints = [
        ("GET", "/api/leave/requests"),
        ("GET", "/api/leave/policies"),
        ("GET", "/api/leave/my/balance", {"Authorization": f"Bearer {EMPLOYEE_TOKEN}"}),
    ]
    
    for method, endpoint, *custom_headers in endpoints:
        try:
            headers_to_use = custom_headers[0] if custom_headers else headers
            response = requests.get(f"{BASE_URL}{endpoint}", headers=headers_to_use)
            status = "✅" if response.status_code in [200, 201] else "❌"
            print(f"{status} {method} {endpoint} - {response.status_code}")
        except Exception as e:
            print(f"❌ {method} {endpoint} - Error: {e}")

def test_timesheet_endpoints():
    """Test timesheet endpoints"""
    print("\n📋 Testing Timesheet Endpoints")
    print("-" * 50)
    
    headers = {"Authorization": f"Bearer {ADMIN_TOKEN}"}
    
    endpoints = [
        ("GET", "/api/timesheet/"),
        ("POST", "/api/timesheet/"),
    ]
    
    for method, endpoint in endpoints:
        try:
            if method == "GET":
                response = requests.get(f"{BASE_URL}{endpoint}", headers=headers)
            else:
                response = requests.post(f"{BASE_URL}{endpoint}", headers=headers, json={})
            
            status = "✅" if response.status_code in [200, 201] else "❌"
            print(f"{status} {method} {endpoint} - {response.status_code}")
        except Exception as e:
            print(f"❌ {method} {endpoint} - Error: {e}")

def test_project_endpoints():
    """Test project management endpoints"""
    print("\n📊 Testing Project Endpoints")
    print("-" * 50)
    
    headers = {"Authorization": f"Bearer {ADMIN_TOKEN}"}
    
    endpoints = [
        ("GET", "/api/project/"),
        ("GET", "/api/project/tasks"),
    ]
    
    for method, endpoint in endpoints:
        try:
            response = requests.get(f"{BASE_URL}{endpoint}", headers=headers)
            status = "✅" if response.status_code in [200, 201] else "❌"
            print(f"{status} {method} {endpoint} - {response.status_code}")
        except Exception as e:
            print(f"❌ {method} {endpoint} - Error: {e}")

def test_kanban_endpoints():
    """Test kanban board endpoints"""
    print("\n📋 Testing Kanban Endpoints")
    print("-" * 50)
    
    headers = {"Authorization": f"Bearer {ADMIN_TOKEN}"}
    
    endpoints = [
        ("GET", "/api/kanban/boards"),
    ]
    
    for method, endpoint in endpoints:
        try:
            response = requests.get(f"{BASE_URL}{endpoint}", headers=headers)
            status = "✅" if response.status_code in [200, 201] else "❌"
            print(f"{status} {method} {endpoint} - {response.status_code}")
        except Exception as e:
            print(f"❌ {method} {endpoint} - Error: {e}")

def test_ticket_endpoints():
    """Test ticket system endpoints"""
    print("\n🎫 Testing Ticket Endpoints")
    print("-" * 50)
    
    headers = {"Authorization": f"Bearer {ADMIN_TOKEN}"}
    
    endpoints = [
        ("GET", "/api/ticket/"),
    ]
    
    for method, endpoint in endpoints:
        try:
            response = requests.get(f"{BASE_URL}{endpoint}", headers=headers)
            status = "✅" if response.status_code in [200, 201] else "❌"
            print(f"{status} {method} {endpoint} - {response.status_code}")
        except Exception as e:
            print(f"❌ {method} {endpoint} - Error: {e}")

def test_health_endpoint():
    """Test health check endpoint"""
    print("\n🏥 Testing Health Endpoint")
    print("-" * 50)
    
    try:
        response = requests.get(f"{BASE_URL}/health")
        status = "✅" if response.status_code == 200 else "❌"
        print(f"{status} GET /health - {response.status_code}")
        if response.status_code == 200:
            print(f"   Response: {response.json()}")
    except Exception as e:
        print(f"❌ GET /health - Error: {e}")

def main():
    """Run all API tests"""
    print("🧪 HRMS Backend API Testing Suite")
    print("=" * 60)
    
    # Test health first
    test_health_endpoint()
    
    # Get authentication tokens
    if not get_auth_tokens():
        print("❌ Failed to get authentication tokens. Stopping tests.")
        return
    
    # Run all endpoint tests
    test_auth_endpoints()
    test_employee_endpoints()
    test_attendance_endpoints()
    test_leave_endpoints()
    test_timesheet_endpoints()
    test_project_endpoints()
    test_kanban_endpoints()
    test_ticket_endpoints()
    
    print("\n" + "=" * 60)
    print("🎯 API Testing Complete!")
    print("\nNote: Some endpoints may return 403/401 due to permission requirements.")
    print("This is expected behavior for role-based access control.")

if __name__ == "__main__":
    main()
