{"test_summary": {"total_tests": 7, "passed_tests": 3, "failed_tests": 4, "success_rate": 42.86}, "database_info": {"url": "postgresql://postgres:admin@localhost:5432/agnoconnect_hrms", "host": "localhost", "port": 5432, "database": "agnoconnect_hrms", "schema": "public"}, "features_tested": ["PostgreSQL connectivity and configuration", "Database table structure and constraints", "CRUD operations (Create, Read, Update, Delete)", "Advanced JSON/AI metadata storage", "Complex queries and joins", "Email OTP functionality", "Performance benchmarks", "Data integrity and constraints"], "test_results": [{"test_name": "Database Connection", "success": true, "message": "Connected to agnoconnect_hrms - PostgreSQL 16.8, compiled by Visual C++ build 1942...", "timestamp": "2025-07-01T15:35:11.643227", "data": null}, {"test_name": "Table Structure", "success": true, "message": "Found 122 total tables, 13 critical tables", "timestamp": "2025-07-01T15:35:11.643227", "data": null}, {"test_name": "Database Constraints", "success": true, "message": "Primary keys: 10, Foreign keys: 209", "timestamp": "2025-07-01T15:35:11.660896", "data": null}, {"test_name": "CRUD Operations", "success": false, "message": "Error: 'Connection' object has no attribute 'commit'", "timestamp": "2025-07-01T15:35:11.676790", "data": null}, {"test_name": "Advanced Features", "success": false, "message": "Error: (psycopg2.errors.UndefinedColumn) column \"ai_metadata\" of relation \"tickets\" does not exist\nLINE 4:                                        ai_metadata, created_...\n                                               ^\n\n[SQL: \n                    INSERT INTO tickets (id, ticket_number, title, description, \n                                       ticket_type, priority, status, organization_id,\n                                       ai_metadata, created_at, updated_at, is_active)\n                    VALUES (%(id)s, %(ticket_number)s, %(title)s, %(description)s,\n                           %(ticket_type)s, %(priority)s, %(status)s, %(organization_id)s,\n                           %(ai_metadata)s, %(created_at)s, %(updated_at)s, %(is_active)s)\n                ]\n[parameters: {'id': 'bdaf8f37-c10e-4b8c-a24a-8a921c974bbf', 'ticket_number': 'TKT-AI-001', 'title': 'AI Enhanced Ticket', 'description': 'Testing AI metadata storage capabilities', 'ticket_type': 'it_support', 'priority': 'high', 'status': 'open', 'organization_id': '700d8987-9d3a-4795-9587-b1ddffd8b1ed', 'ai_metadata': '{\"predicted_type\": \"it_support\", \"confidence_score\": 0.92, \"sentiment_analysis\": {\"sentiment\": \"frustrated\", \"urgency_score\": 0.8, \"escalation_risk\": ... (17 characters truncated) ... ted_tags\": [\"urgent\", \"password\", \"access\"], \"routing_suggestions\": {\"department\": \"IT\", \"priority\": \"high\", \"estimated_resolution_time\": \"2 hours\"}}', 'created_at': datetime.datetime(2025, 7, 1, 15, 35, 11, 676790), 'updated_at': datetime.datetime(2025, 7, 1, 15, 35, 11, 676790), 'is_active': True}]\n(Background on this error at: https://sqlalche.me/e/14/f405)", "timestamp": "2025-07-01T15:35:11.676790", "data": null}, {"test_name": "Email Configuration", "success": false, "message": "SMTP credentials not configured in settings", "timestamp": "2025-07-01T15:35:11.693286", "data": null}, {"test_name": "Performance Benchmarks", "success": false, "message": "Error: 'Connection' object has no attribute 'commit'", "timestamp": "2025-07-01T15:35:11.710125", "data": null}], "timestamp": "2025-07-01T15:35:11.710125"}