/**
 * Leave Policy Management Component
 * Features: Policy configuration, entitlements, rules, encashment settings
 */

import React, { useState, useEffect } from 'react';
import {
  Plus,
  Edit,
  Trash2,
  Save,
  X,
  FileText,
  Calendar,
  Clock,
  DollarSign,
  Settings,
  AlertCircle,
  CheckCircle
} from 'lucide-react';
import apiService from '../../services/api';

const LeavePolicyManager = () => {
  const [policies, setPolicies] = useState([]);
  const [loading, setLoading] = useState(true);
  const [showForm, setShowForm] = useState(false);
  const [editingPolicy, setEditingPolicy] = useState(null);
  const [formData, setFormData] = useState({
    name: '',
    leave_type: 'annual',
    annual_entitlement: 20,
    max_carry_forward: 5,
    max_accumulation: 30,
    accrual_frequency: 'monthly',
    accrual_start_date: 'hire_date',
    min_notice_days: 1,
    max_consecutive_days: null,
    min_application_days: 0.5,
    requires_approval: true,
    auto_approve_threshold: null,
    requires_documentation: false,
    documentation_threshold: null,
    allow_encashment: false,
    min_balance_for_encashment: 5,
    max_encashment_days_per_year: 10,
    encashment_rate_percentage: 100,
    available_during_notice_period: false,
    is_active: true
  });
  const [errors, setErrors] = useState({});

  useEffect(() => {
    loadPolicies();
  }, []);

  const loadPolicies = async () => {
    try {
      setLoading(true);
      const response = await apiService.getLeavePolicies();
      setPolicies(response.policies || []);
    } catch (error) {
      console.error('Error loading policies:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (field, value) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: null }));
    }
  };

  const validateForm = () => {
    const newErrors = {};

    if (!formData.name.trim()) newErrors.name = 'Policy name is required';
    if (formData.annual_entitlement <= 0) newErrors.annual_entitlement = 'Annual entitlement must be greater than 0';
    if (formData.min_notice_days < 0) newErrors.min_notice_days = 'Minimum notice days cannot be negative';

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async () => {
    if (!validateForm()) return;

    try {
      if (editingPolicy) {
        await apiService.put(`/leave/policies/${editingPolicy.id}`, formData);
      } else {
        await apiService.post('/leave/policies', formData);
      }
      
      await loadPolicies();
      resetForm();
    } catch (error) {
      console.error('Error saving policy:', error);
    }
  };

  const handleEdit = (policy) => {
    setEditingPolicy(policy);
    setFormData({ ...policy });
    setShowForm(true);
  };

  const handleDelete = async (policyId) => {
    if (window.confirm('Are you sure you want to delete this policy?')) {
      try {
        await apiService.delete(`/leave/policies/${policyId}`);
        await loadPolicies();
      } catch (error) {
        console.error('Error deleting policy:', error);
      }
    }
  };

  const resetForm = () => {
    setFormData({
      name: '',
      leave_type: 'annual',
      annual_entitlement: 20,
      max_carry_forward: 5,
      max_accumulation: 30,
      accrual_frequency: 'monthly',
      accrual_start_date: 'hire_date',
      min_notice_days: 1,
      max_consecutive_days: null,
      min_application_days: 0.5,
      requires_approval: true,
      auto_approve_threshold: null,
      requires_documentation: false,
      documentation_threshold: null,
      allow_encashment: false,
      min_balance_for_encashment: 5,
      max_encashment_days_per_year: 10,
      encashment_rate_percentage: 100,
      available_during_notice_period: false,
      is_active: true
    });
    setEditingPolicy(null);
    setShowForm(false);
    setErrors({});
  };

  const leaveTypes = [
    { value: 'annual', label: 'Annual Leave' },
    { value: 'sick', label: 'Sick Leave' },
    { value: 'maternity', label: 'Maternity Leave' },
    { value: 'paternity', label: 'Paternity Leave' },
    { value: 'personal', label: 'Personal Leave' },
    { value: 'emergency', label: 'Emergency Leave' },
    { value: 'bereavement', label: 'Bereavement Leave' },
    { value: 'study', label: 'Study Leave' },
    { value: 'sabbatical', label: 'Sabbatical' },
    { value: 'unpaid', label: 'Unpaid Leave' }
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Leave Policies</h2>
          <p className="text-gray-600 mt-1">Configure leave types, entitlements, and rules</p>
        </div>
        
        <button
          onClick={() => setShowForm(true)}
          className="flex items-center px-4 py-2 agno-bg-primary text-white rounded-lg hover:bg-blue-700"
        >
          <Plus size={16} className="mr-2" />
          Add Policy
        </button>
      </div>

      {/* Policies List */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        {loading ? (
          <div className="flex items-center justify-center h-64">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-agno-primary"></div>
          </div>
        ) : policies.length === 0 ? (
          <div className="text-center py-12">
            <FileText className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-500">No leave policies configured</p>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Policy Name
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Type
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Annual Entitlement
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Carry Forward
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Notice Required
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {policies.map((policy) => (
                  <tr key={policy.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div>
                          <div className="text-sm font-medium text-gray-900">{policy.name}</div>
                          <div className="text-sm text-gray-500">
                            {policy.requires_approval ? 'Requires Approval' : 'Auto Approved'}
                          </div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 capitalize">
                        {policy.leave_type}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {policy.annual_entitlement} days
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {policy.max_carry_forward || 0} days
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {policy.min_notice_days} days
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                        policy.is_active 
                          ? 'bg-green-100 text-green-800' 
                          : 'bg-red-100 text-red-800'
                      }`}>
                        {policy.is_active ? 'Active' : 'Inactive'}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <div className="flex items-center space-x-2">
                        <button
                          onClick={() => handleEdit(policy)}
                          className="text-agno-primary hover:text-blue-700"
                        >
                          <Edit size={16} />
                        </button>
                        <button
                          onClick={() => handleDelete(policy.id)}
                          className="text-red-600 hover:text-red-800"
                        >
                          <Trash2 size={16} />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>

      {/* Policy Form Modal */}
      {showForm && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-2xl shadow-2xl w-full max-w-4xl max-h-[90vh] overflow-hidden">
            {/* Header */}
            <div className="agno-gradient text-white p-6">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="text-xl font-bold">
                    {editingPolicy ? 'Edit Leave Policy' : 'Create Leave Policy'}
                  </h3>
                  <p className="text-blue-100 mt-1">Configure leave entitlements and rules</p>
                </div>
                <button
                  onClick={resetForm}
                  className="text-white hover:text-blue-200 transition-colors"
                >
                  <X size={24} />
                </button>
              </div>
            </div>

            {/* Form Content */}
            <div className="p-6 overflow-y-auto max-h-[calc(90vh-200px)]">
              <div className="space-y-6">
                {/* Basic Information */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-semibold text-gray-700 mb-2">
                      Policy Name *
                    </label>
                    <input
                      type="text"
                      value={formData.name}
                      onChange={(e) => handleInputChange('name', e.target.value)}
                      className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-agno-primary focus:border-transparent ${
                        errors.name ? 'border-red-500' : 'border-gray-300'
                      }`}
                      placeholder="e.g., Annual Leave Policy"
                    />
                    {errors.name && (
                      <p className="text-red-500 text-sm mt-1 flex items-center">
                        <AlertCircle size={14} className="mr-1" />
                        {errors.name}
                      </p>
                    )}
                  </div>

                  <div>
                    <label className="block text-sm font-semibold text-gray-700 mb-2">
                      Leave Type *
                    </label>
                    <select
                      value={formData.leave_type}
                      onChange={(e) => handleInputChange('leave_type', e.target.value)}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-agno-primary focus:border-transparent"
                    >
                      {leaveTypes.map((type) => (
                        <option key={type.value} value={type.value}>
                          {type.label}
                        </option>
                      ))}
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-semibold text-gray-700 mb-2">
                      Annual Entitlement (Days) *
                    </label>
                    <input
                      type="number"
                      value={formData.annual_entitlement}
                      onChange={(e) => handleInputChange('annual_entitlement', parseFloat(e.target.value))}
                      className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-agno-primary focus:border-transparent ${
                        errors.annual_entitlement ? 'border-red-500' : 'border-gray-300'
                      }`}
                      min="0"
                      step="0.5"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-semibold text-gray-700 mb-2">
                      Max Carry Forward (Days)
                    </label>
                    <input
                      type="number"
                      value={formData.max_carry_forward}
                      onChange={(e) => handleInputChange('max_carry_forward', parseFloat(e.target.value))}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-agno-primary focus:border-transparent"
                      min="0"
                      step="0.5"
                    />
                  </div>
                </div>

                {/* Application Rules */}
                <div className="border-t pt-6">
                  <h4 className="text-lg font-semibold text-gray-900 mb-4">Application Rules</h4>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div>
                      <label className="block text-sm font-semibold text-gray-700 mb-2">
                        Minimum Notice (Days)
                      </label>
                      <input
                        type="number"
                        value={formData.min_notice_days}
                        onChange={(e) => handleInputChange('min_notice_days', parseInt(e.target.value))}
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-agno-primary focus:border-transparent"
                        min="0"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-semibold text-gray-700 mb-2">
                        Max Consecutive Days
                      </label>
                      <input
                        type="number"
                        value={formData.max_consecutive_days || ''}
                        onChange={(e) => handleInputChange('max_consecutive_days', e.target.value ? parseInt(e.target.value) : null)}
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-agno-primary focus:border-transparent"
                        min="1"
                        placeholder="No limit"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-semibold text-gray-700 mb-2">
                        Min Application (Days)
                      </label>
                      <input
                        type="number"
                        value={formData.min_application_days}
                        onChange={(e) => handleInputChange('min_application_days', parseFloat(e.target.value))}
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-agno-primary focus:border-transparent"
                        min="0.5"
                        step="0.5"
                      />
                    </div>
                  </div>
                </div>

                {/* Approval Settings */}
                <div className="border-t pt-6">
                  <h4 className="text-lg font-semibold text-gray-900 mb-4">Approval Settings</h4>
                  <div className="space-y-4">
                    <div className="flex items-center">
                      <input
                        type="checkbox"
                        checked={formData.requires_approval}
                        onChange={(e) => handleInputChange('requires_approval', e.target.checked)}
                        className="h-4 w-4 text-agno-primary focus:ring-agno-primary border-gray-300 rounded"
                      />
                      <label className="ml-2 text-sm text-gray-700">Requires manager approval</label>
                    </div>

                    {formData.requires_approval && (
                      <div>
                        <label className="block text-sm font-semibold text-gray-700 mb-2">
                          Auto-approve threshold (days)
                        </label>
                        <input
                          type="number"
                          value={formData.auto_approve_threshold || ''}
                          onChange={(e) => handleInputChange('auto_approve_threshold', e.target.value ? parseInt(e.target.value) : null)}
                          className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-agno-primary focus:border-transparent"
                          placeholder="Auto-approve if <= days (optional)"
                          min="1"
                        />
                      </div>
                    )}
                  </div>
                </div>

                {/* Encashment Settings */}
                <div className="border-t pt-6">
                  <h4 className="text-lg font-semibold text-gray-900 mb-4">Encashment Settings</h4>
                  <div className="space-y-4">
                    <div className="flex items-center">
                      <input
                        type="checkbox"
                        checked={formData.allow_encashment}
                        onChange={(e) => handleInputChange('allow_encashment', e.target.checked)}
                        className="h-4 w-4 text-agno-primary focus:ring-agno-primary border-gray-300 rounded"
                      />
                      <label className="ml-2 text-sm text-gray-700">Allow leave encashment</label>
                    </div>

                    {formData.allow_encashment && (
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div>
                          <label className="block text-sm font-semibold text-gray-700 mb-2">
                            Min Balance for Encashment
                          </label>
                          <input
                            type="number"
                            value={formData.min_balance_for_encashment}
                            onChange={(e) => handleInputChange('min_balance_for_encashment', parseFloat(e.target.value))}
                            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-agno-primary focus:border-transparent"
                            min="0"
                            step="0.5"
                          />
                        </div>

                        <div>
                          <label className="block text-sm font-semibold text-gray-700 mb-2">
                            Max Encashment/Year
                          </label>
                          <input
                            type="number"
                            value={formData.max_encashment_days_per_year}
                            onChange={(e) => handleInputChange('max_encashment_days_per_year', parseFloat(e.target.value))}
                            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-agno-primary focus:border-transparent"
                            min="0"
                            step="0.5"
                          />
                        </div>

                        <div>
                          <label className="block text-sm font-semibold text-gray-700 mb-2">
                            Encashment Rate (%)
                          </label>
                          <input
                            type="number"
                            value={formData.encashment_rate_percentage}
                            onChange={(e) => handleInputChange('encashment_rate_percentage', parseFloat(e.target.value))}
                            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-agno-primary focus:border-transparent"
                            min="0"
                            max="100"
                          />
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>

            {/* Footer */}
            <div className="px-6 py-4 bg-gray-50 flex justify-end space-x-3">
              <button
                onClick={resetForm}
                className="px-6 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50"
              >
                Cancel
              </button>
              <button
                onClick={handleSubmit}
                className="flex items-center px-6 py-2 agno-bg-primary text-white rounded-lg hover:bg-blue-700"
              >
                <Save size={16} className="mr-2" />
                {editingPolicy ? 'Update Policy' : 'Create Policy'}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default LeavePolicyManager;
