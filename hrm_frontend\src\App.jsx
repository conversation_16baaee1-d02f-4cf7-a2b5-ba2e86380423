import { useState } from "react";
import { AuthProvider } from "./contexts/AuthContext";
import { ProtectedRoute } from "./components/ProtectedRoute";
import Sidebar from "./components/Sidebar";
import Header from "./components/Header";
import Dashboard from "./pages/Dashboard";
import Overview from "./pages/Overview";
import Calendar from "./pages/Calendar";
import Employees from "./pages/Employees";
import Onboarding from "./pages/Onboarding";
import Attendance from "./pages/Attendance";
import Leave from "./pages/Leave";
import Payroll from "./pages/Payroll";
import Projects from "./pages/Projects";
import Tasks from "./pages/Tasks";
import Tickets from "./pages/Tickets";
import Performance from "./pages/Performance";
import Reports from "./pages/Reports";
import Settings from "./pages/Settings";
import TimesheetApproval from "./pages/TimesheetApproval";
import TimeTracker from "./components/TimeTracker";
import RBACDemo from "./components/RBACDemo";

function App() {
  const [activeView, setActiveView] = useState("dashboard");
  const [activeTab, setActiveTab] = useState("overview");

  return (
    <AuthProvider>
      <ProtectedRoute permission="dashboard">
        <div className="flex h-screen bg-slate-100">
          {/* Sidebar */}
          <Sidebar
            activeView={activeView}
            setActiveView={setActiveView}
          />

          {/* Main Content */}
          <div className="flex-1 flex flex-col overflow-hidden">
            <Header
              activeTab={activeTab}
              setActiveTab={setActiveTab}
              activeView={activeView}
            />

            <main className="flex-1 overflow-y-auto bg-slate-100">
              <AppContent
                activeView={activeView}
                activeTab={activeTab}
                setActiveTab={setActiveTab}
              />
            </main>
          </div>
        </div>
      </ProtectedRoute>
    </AuthProvider>
  );
}

// Main content router component
function AppContent({ activeView, activeTab, setActiveTab }) {
  // Reset tab when view changes
  const handleViewChange = (newView) => {
    if (newView !== activeView) {
      setActiveTab(getDefaultTabForView(newView));
    }
  };

  // Get default tab for each view
  const getDefaultTabForView = (view) => {
    const defaultTabs = {
      dashboard: 'overview',
      employees: 'directory',
      attendance: 'my-attendance',
      leave: 'my-leaves',
      payroll: 'my-payslips',
      projects: 'kanban',
      tasks: 'my-tasks',
      tickets: 'my-tickets',
      performance: 'my-reviews',
      'time-tracker': 'tracker',
      reports: 'overview',
      settings: 'general'
    };
    return defaultTabs[view] || 'overview';
  };

  // Render content based on active view
  const renderContent = () => {
    switch (activeView) {
      case 'dashboard':
        return (
          <div className="p-4">
            {activeTab === "overview" && <Overview />}
            {activeTab === "dashboard" && <Dashboard />}
            {activeTab === "analytics" && <Dashboard />}
            {activeTab === "rbac-demo" && <RBACDemo />}
          </div>
        );

      case 'employees':
        return <Employees activeTab={activeTab} />;

      case 'onboarding':
        return <Onboarding />;

      case 'attendance':
        return <Attendance activeTab={activeTab} />;

      case 'calendar':
        return <Calendar />;

      case 'leave':
        return <Leave activeTab={activeTab} />;

      case 'payroll':
        return (
          <ProtectedRoute permission="payrollPayslipsSelf">
            <Payroll activeTab={activeTab} />
          </ProtectedRoute>
        );

      case 'projects':
        return (
          <ProtectedRoute permission="projectKanbanBoards">
            <Projects activeTab={activeTab} />
          </ProtectedRoute>
        );

      case 'tasks':
        return (
          <ProtectedRoute permission="taskManagement">
            <Tasks activeTab={activeTab} />
          </ProtectedRoute>
        );

      case 'time-tracker':
        return (
          <ProtectedRoute permission="timeTracker">
            <div className="p-6">
              <h1 className="text-2xl font-bold text-gray-900 mb-4">Time Tracker</h1>
              <TimeTracker />
            </div>
          </ProtectedRoute>
        );

      case 'tickets':
        return (
          <ProtectedRoute permission="ticketingSystemSelf">
            <Tickets activeTab={activeTab} />
          </ProtectedRoute>
        );

      case 'performance':
        return (
          <ProtectedRoute permission="performanceReviewsSelf">
            <Performance activeTab={activeTab} />
          </ProtectedRoute>
        );

      case 'reports':
        return (
          <ProtectedRoute permission="systemLogs">
            <Reports activeTab={activeTab} />
          </ProtectedRoute>
        );

      case 'settings':
        return (
          <ProtectedRoute permission="systemSettings">
            <Settings activeTab={activeTab} />
          </ProtectedRoute>
        );

      case 'timesheet-approval':
        return (
          <ProtectedRoute permission="timesheetApproval">
            <TimesheetApproval activeTab={activeTab} />
          </ProtectedRoute>
        );

      default:
        return (
          <ProtectedRoute permission="dashboard">
            <div className="p-4">
              <Overview />
            </div>
          </ProtectedRoute>
        );
    }
  };

  return renderContent();
}

export default App;