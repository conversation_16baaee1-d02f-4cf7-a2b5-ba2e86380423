from sqlalchemy.orm import Session, joinedload
from sqlalchemy import and_, or_, func
from typing import Optional, List, Dict, Any, Tuple
from uuid import UUID
from datetime import datetime, timedelta
from fastapi import HTTPException, status
import logging
import json

from ...db.models.ticket import (
    Ticket, TicketSLA, TicketActivity, TicketNotification,
    TicketStatus, TicketPriority, TicketType
)
from ...db.models.employee import Employee
from ...core.security import CurrentUser
from ...core.audit_logger import AuditLogger

logger = logging.getLogger(__name__)


class TicketSLAService:
    """Service for managing ticket SLAs and monitoring compliance"""

    async def create_sla(
        self,
        db: Session,
        sla_data: Dict[str, Any],
        current_user: CurrentUser
    ) -> TicketSLA:
        """Create a new SLA configuration"""
        try:
            sla = TicketSLA(
                name=sla_data["name"],
                organization_id=current_user.organization_id,
                ticket_types=sla_data.get("ticket_types"),
                priorities=sla_data.get("priorities"),
                categories=sla_data.get("categories"),
                first_response_hours=sla_data.get("first_response_hours"),
                resolution_hours=sla_data.get("resolution_hours"),
                business_hours_only=sla_data.get("business_hours_only", True),
                business_hours_start=sla_data.get("business_hours_start"),
                business_hours_end=sla_data.get("business_hours_end"),
                business_days=sla_data.get("business_days"),
                escalation_enabled=sla_data.get("escalation_enabled", False),
                escalation_hours=sla_data.get("escalation_hours"),
                escalation_to=sla_data.get("escalation_to"),
                is_active=sla_data.get("is_active", True),
                created_by=current_user.user_id
            )

            db.add(sla)
            db.commit()
            db.refresh(sla)

            # Log SLA creation
            await AuditLogger.log_action(
                db, current_user.user_id, "sla_created",
                f"Created SLA: {sla.name}",
                {"sla_id": str(sla.id)}
            )

            logger.info(f"SLA {sla.name} created by {current_user.user_id}")
            return sla

        except Exception as e:
            db.rollback()
            logger.error(f"Error creating SLA: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Error creating SLA"
            )

    async def get_slas(
        self,
        db: Session,
        current_user: CurrentUser,
        is_active: Optional[bool] = None,
        skip: int = 0,
        limit: int = 20
    ) -> List[TicketSLA]:
        """Get SLA configurations for organization"""
        try:
            query = db.query(TicketSLA).filter(
                TicketSLA.organization_id == current_user.organization_id
            )

            if is_active is not None:
                query = query.filter(TicketSLA.is_active == is_active)

            slas = query.offset(skip).limit(limit).all()
            return slas

        except Exception as e:
            logger.error(f"Error getting SLAs: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Error retrieving SLAs"
            )

    async def update_sla(
        self,
        db: Session,
        sla_id: UUID,
        sla_data: Dict[str, Any],
        current_user: CurrentUser
    ) -> Optional[TicketSLA]:
        """Update an SLA configuration"""
        try:
            sla = db.query(TicketSLA).filter(
                TicketSLA.id == sla_id,
                TicketSLA.organization_id == current_user.organization_id
            ).first()

            if not sla:
                return None

            # Update fields
            for field, value in sla_data.items():
                if hasattr(sla, field):
                    setattr(sla, field, value)

            sla.updated_by = current_user.user_id
            sla.updated_at = datetime.utcnow()

            db.commit()
            db.refresh(sla)

            # Log SLA update
            await AuditLogger.log_action(
                db, current_user.user_id, "sla_updated",
                f"Updated SLA: {sla.name}",
                {"sla_id": str(sla.id)}
            )

            logger.info(f"SLA {sla.name} updated by {current_user.user_id}")
            return sla

        except Exception as e:
            db.rollback()
            logger.error(f"Error updating SLA {sla_id}: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Error updating SLA"
            )

    async def delete_sla(
        self,
        db: Session,
        sla_id: UUID,
        current_user: CurrentUser
    ) -> bool:
        """Delete an SLA configuration"""
        try:
            sla = db.query(TicketSLA).filter(
                TicketSLA.id == sla_id,
                TicketSLA.organization_id == current_user.organization_id
            ).first()

            if not sla:
                return False

            db.delete(sla)
            db.commit()

            # Log SLA deletion
            await AuditLogger.log_action(
                db, current_user.user_id, "sla_deleted",
                f"Deleted SLA: {sla.name}",
                {"sla_id": str(sla.id)}
            )

            logger.info(f"SLA {sla.name} deleted by {current_user.user_id}")
            return True

        except Exception as e:
            db.rollback()
            logger.error(f"Error deleting SLA {sla_id}: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Error deleting SLA"
            )

    async def apply_sla_to_ticket(
        self,
        db: Session,
        ticket: Ticket,
        current_user: CurrentUser
    ) -> Optional[TicketSLA]:
        """Apply appropriate SLA to a ticket"""
        try:
            # Get active SLAs for organization
            slas = db.query(TicketSLA).filter(
                TicketSLA.organization_id == ticket.organization_id,
                TicketSLA.is_active == True
            ).all()

            for sla in slas:
                if await self._evaluate_sla_conditions(ticket, sla):
                    # Apply SLA to ticket
                    ticket.response_sla_hours = sla.first_response_hours
                    ticket.resolution_sla_hours = sla.resolution_hours

                    # Calculate due dates
                    if sla.business_hours_only:
                        ticket.due_date = await self._calculate_business_hours_due_date(
                            datetime.utcnow(), sla.resolution_hours, sla
                        )
                    else:
                        ticket.due_date = datetime.utcnow() + timedelta(hours=sla.resolution_hours)

                    db.commit()

                    # Log SLA application
                    await self._create_activity(
                        db, ticket.id, "sla_applied",
                        f"SLA '{sla.name}' applied to ticket",
                        current_user
                    )

                    logger.info(f"SLA {sla.name} applied to ticket {ticket.ticket_number}")
                    return sla

            return None

        except Exception as e:
            logger.error(f"Error applying SLA to ticket {ticket.id}: {e}")
            return None

    async def check_sla_breaches(
        self,
        db: Session,
        organization_id: UUID
    ) -> List[Dict[str, Any]]:
        """Check for SLA breaches and return list of breached tickets"""
        try:
            current_time = datetime.utcnow()
            breached_tickets = []

            # Get tickets that might have SLA breaches
            tickets = db.query(Ticket).filter(
                Ticket.organization_id == organization_id,
                Ticket.status.in_([TicketStatus.OPEN, TicketStatus.IN_PROGRESS, TicketStatus.PENDING]),
                Ticket.due_date.isnot(None),
                Ticket.due_date < current_time,
                Ticket.sla_breach == False
            ).all()

            for ticket in tickets:
                # Mark as breached
                ticket.sla_breach = True
                
                breach_info = {
                    "ticket_id": str(ticket.id),
                    "ticket_number": ticket.ticket_number,
                    "title": ticket.title,
                    "priority": ticket.priority,
                    "due_date": ticket.due_date,
                    "breach_time": current_time,
                    "hours_overdue": (current_time - ticket.due_date).total_seconds() / 3600,
                    "requester_id": str(ticket.requester_id),
                    "assigned_to": str(ticket.assigned_to) if ticket.assigned_to else None
                }
                
                breached_tickets.append(breach_info)

                # Log SLA breach
                await self._create_activity(
                    db, ticket.id, "sla_breach",
                    f"SLA breach detected - {breach_info['hours_overdue']:.1f} hours overdue",
                    None
                )

            db.commit()

            if breached_tickets:
                logger.warning(f"Found {len(breached_tickets)} SLA breaches for organization {organization_id}")

            return breached_tickets

        except Exception as e:
            logger.error(f"Error checking SLA breaches: {e}")
            return []

    async def get_sla_metrics(
        self,
        db: Session,
        organization_id: UUID,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None
    ) -> Dict[str, Any]:
        """Get SLA performance metrics"""
        try:
            if not start_date:
                start_date = datetime.utcnow() - timedelta(days=30)
            if not end_date:
                end_date = datetime.utcnow()

            # Base query for tickets in date range
            base_query = db.query(Ticket).filter(
                Ticket.organization_id == organization_id,
                Ticket.created_at >= start_date,
                Ticket.created_at <= end_date
            )

            total_tickets = base_query.count()
            
            # SLA compliance metrics
            tickets_with_sla = base_query.filter(
                Ticket.resolution_sla_hours.isnot(None)
            ).count()

            breached_tickets = base_query.filter(
                Ticket.sla_breach == True
            ).count()

            # Calculate compliance rate
            compliance_rate = 0
            if tickets_with_sla > 0:
                compliance_rate = ((tickets_with_sla - breached_tickets) / tickets_with_sla) * 100

            # Average resolution time
            resolved_tickets = base_query.filter(
                Ticket.status == TicketStatus.RESOLVED,
                Ticket.resolved_at.isnot(None)
            ).all()

            avg_resolution_hours = 0
            if resolved_tickets:
                total_resolution_time = sum([
                    (ticket.resolved_at - ticket.created_at).total_seconds() / 3600
                    for ticket in resolved_tickets
                ])
                avg_resolution_hours = total_resolution_time / len(resolved_tickets)

            # Priority breakdown
            priority_breakdown = {}
            for priority in TicketPriority:
                priority_count = base_query.filter(Ticket.priority == priority).count()
                priority_breaches = base_query.filter(
                    Ticket.priority == priority,
                    Ticket.sla_breach == True
                ).count()
                
                priority_breakdown[priority.value] = {
                    "total": priority_count,
                    "breached": priority_breaches,
                    "compliance_rate": ((priority_count - priority_breaches) / priority_count * 100) if priority_count > 0 else 0
                }

            return {
                "period": {
                    "start_date": start_date.isoformat(),
                    "end_date": end_date.isoformat()
                },
                "total_tickets": total_tickets,
                "tickets_with_sla": tickets_with_sla,
                "breached_tickets": breached_tickets,
                "compliance_rate": round(compliance_rate, 2),
                "avg_resolution_hours": round(avg_resolution_hours, 2),
                "priority_breakdown": priority_breakdown
            }

        except Exception as e:
            logger.error(f"Error getting SLA metrics: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Error retrieving SLA metrics"
            )

    async def _evaluate_sla_conditions(
        self,
        ticket: Ticket,
        sla: TicketSLA
    ) -> bool:
        """Evaluate if SLA conditions match the ticket"""
        try:
            # Check ticket types
            if sla.ticket_types and ticket.ticket_type not in sla.ticket_types:
                return False

            # Check priorities
            if sla.priorities and ticket.priority not in sla.priorities:
                return False

            # Check categories
            if sla.categories and ticket.category not in sla.categories:
                return False

            return True

        except Exception as e:
            logger.error(f"Error evaluating SLA conditions: {e}")
            return False

    async def _calculate_business_hours_due_date(
        self,
        start_time: datetime,
        hours_to_add: int,
        sla: TicketSLA
    ) -> datetime:
        """Calculate due date considering business hours"""
        try:
            # Simple implementation - in production, use a proper business hours library
            business_start = sla.business_hours_start or "09:00"
            business_end = sla.business_hours_end or "17:00"
            business_days = sla.business_days or [0, 1, 2, 3, 4]  # Monday to Friday

            current_time = start_time
            remaining_hours = hours_to_add

            while remaining_hours > 0:
                # Check if current day is a business day
                if current_time.weekday() in business_days:
                    # Calculate hours available in current business day
                    start_hour, start_minute = map(int, business_start.split(":"))
                    end_hour, end_minute = map(int, business_end.split(":"))
                    
                    business_start_today = current_time.replace(
                        hour=start_hour, minute=start_minute, second=0, microsecond=0
                    )
                    business_end_today = current_time.replace(
                        hour=end_hour, minute=end_minute, second=0, microsecond=0
                    )

                    if current_time < business_start_today:
                        current_time = business_start_today

                    if current_time < business_end_today:
                        hours_available = (business_end_today - current_time).total_seconds() / 3600
                        
                        if remaining_hours <= hours_available:
                            # Can complete within this business day
                            return current_time + timedelta(hours=remaining_hours)
                        else:
                            # Use all available hours and move to next business day
                            remaining_hours -= hours_available
                            current_time = business_end_today

                # Move to next day
                current_time = current_time.replace(hour=0, minute=0, second=0, microsecond=0) + timedelta(days=1)

            return current_time

        except Exception as e:
            logger.error(f"Error calculating business hours due date: {e}")
            # Fallback to simple calculation
            return start_time + timedelta(hours=hours_to_add)

    async def _create_activity(
        self,
        db: Session,
        ticket_id: UUID,
        activity_type: str,
        description: str,
        current_user: Optional[CurrentUser]
    ):
        """Create ticket activity log"""
        try:
            activity = TicketActivity(
                ticket_id=ticket_id,
                user_id=current_user.user_id if current_user else None,
                activity_type=activity_type,
                description=description,
                is_system_activity=current_user is None,
                created_at=datetime.utcnow()
            )
            db.add(activity)
            db.commit()
        except Exception as e:
            logger.error(f"Error creating activity log: {e}")
