from flask import url_for
from flask.views import <PERSON>View
from flask_smorest import Blueprint, abort
from schemas import PayrollDetailSchema
from sqlalchemy.orm.exc import NoResultFound
from sqlalchemy.exc import SQLAlchemyError, IntegrityError
import core.utils.response_message as RESPONSEMESSAGE
from core.services.payroll_details import PayrollDetailsService
from core.middleware import roles_required

blueprint = Blueprint("Payroll Detail", __name__, description="Operations for Payroll Details")
    
@blueprint.route("/payroll_details/<id>")
class PayrollDetailList(MethodView):
    @roles_required(['admin'])
    @blueprint.response(200, PayrollDetailSchema)
    def get(self, id):
        service = PayrollDetailsService()
        detail = service.getPayrollDetails(id)
        if not detail:
            abort(401, message="Payroll Detail does not exist")
        return detail    
    
    @roles_required(['admin'])
    def delete(self, id):
        service = PayrollDetailsService()
        detail = service.getPayrollDetails(id)
        if not detail:
            abort(404, message="Payroll Detail does not exist")
        service.deletePayrollDetails(id)
        return {"message" : "Payroll Detail deleted"}
    
    @roles_required(['admin'])
    @blueprint.arguments(PayrollDetailSchema)
    @blueprint.response(201, PayrollDetailSchema)
    def put(self, id, data):
        service = PayrollDetailsService()
        detail = service.getPayrollDetails(id)
        if not detail:
            abort(404, message="Payroll Detail does not exist")
        try :
            new_detail = service.updatePayrollDetails(id, data)
            return new_detail
        except SQLAlchemyError:
                abort(500, message="Error while updating Payroll Detail")
    
@blueprint.route("/payroll_details")
class PayrollDetail(MethodView):
    @roles_required(['admin'])
    @blueprint.arguments(PayrollDetailSchema)
    @blueprint.response(200, PayrollDetailSchema)
    def post(self, data):
        try:
            service = PayrollDetailsService()
            detail = service.getPayrollDetailsByKey({"id": data['id']})
            if not detail:
                new_detail = service.createPayrollDetails(data)
            else:
                abort(400, message="Payroll Detail already exist")
        except IntegrityError:
            abort(500, message="Error while creating Payroll Detail")
        except SQLAlchemyError as e:
            print(e)
            abort(500, message="Error while creating Payroll Detail")
        return new_detail