from flask import url_for, jsonify
from flask.views import <PERSON><PERSON>iew
from flask_smorest import Blueprint, abort
from core.middleware import roles_required
from schemas import BankSchema, UpdateBankSchema
from sqlalchemy.orm.exc import NoResultFound
from sqlalchemy.exc import SQLAlchemyError, IntegrityError

import core.utils.response_message as RESPONSEMESSAGE
from core.services.banks import BanksService
from core.utils.responseBuilder import ResponseBuilder

blueprint = Blueprint("Bank", __name__, description="Operations for banks")
    
@blueprint.route("/bank/<id>")
class BankList(MethodView):
    @roles_required(['admin'])
    @blueprint.response(200, BankSchema)
    def get(self, id):
        service = BanksService()
        bank = service.getBanks(id)
        if not bank:
            abort(401, message="Bank does not exist")
        bank_details = BankSchema().dump(bank)
        return ResponseBuilder(data=bank_details, status_code=200).build()     
    
    @roles_required(['admin'])
    def delete(self, id):
        service = BanksService()
        bank = service.getBanks(id)
        if not bank:
            abort(404, message="Bank does not exist")
        service.deleteBanks(id)
        return {"message" : "Bank deleted"}
    
    @roles_required(['admin'])
    @blueprint.arguments(UpdateBankSchema)
    @blueprint.response(201, BankSchema)
    def put(self, data, id):
        service = BanksService()
        bank = service.getBanks(id)
        if not bank:
            abort(404, message="Bank does not exist")
        try :
            new_bank = service.updateBanks(id, data)
            return new_bank
        except SQLAlchemyError:
                abort(500, message="Error while updating bank")
    
@blueprint.route("/bank")
class Bank(MethodView):
    @roles_required(['admin'])
    @blueprint.response(200, BankSchema)
    def get(self):
        bank_service = BanksService()
        bank_list, total_banks = bank_service.fetchAll()
        benefit_schema = BankSchema(many=True)
        bank_list = benefit_schema.dump(bank_list)
        return ResponseBuilder(data=bank_list, status_code=200, total=total_banks).build()
    
    @roles_required(['admin'])
    @blueprint.arguments(BankSchema)
    @blueprint.response(200, BankSchema)
    def post(self, data):
        try:
            service = BanksService()
            bank = service.getBanksByKey({"name": data['name']})
            if not bank:
                new_bank = service.createBanks(data)
            else:
                abort(400, message="Bank already exist")
        except IntegrityError:
            abort(500, message="Error while creating a bank")
        except SQLAlchemyError as e:
            print(e)
            abort(500, message="Error while creating a bank")
        return new_bank