from datetime import datetime
from typing import Optional

from typing import Optional, <PERSON><PERSON>  # Correct import for <PERSON>ple
from core.databases.database import db
from core.models.loan_repayment import LoanRepaymentModel
from core.models.loan_requests import LoanRequestModel
from core.models.payroll_history import PayrollHistoryModel




class LoanRepaymentRepository:

    @classmethod
    def _get_active_loans(cls, employee_id: int) -> list[LoanRequestModel]:
        """Get active loans for employee with proper locking"""
        return db.session.query(LoanRequestModel).filter(
            LoanRequestModel.employee_id == employee_id,
            LoanRequestModel.status == 'disbursed',
            LoanRequestModel.balance > 0
        ).order_by(
            LoanRequestModel.interest.desc(),
            LoanRequestModel.disbursed_at.asc()
        ).with_for_update().all()

    @classmethod
    def _process_loan_deduction(
        cls,
        loan: LoanRequestModel,
        remaining_net: float,
        save_changes: bool = True
     ) -> tuple[float, float]:
        """
        Process deduction for a single loan.
        Returns (deduction_amount, new_remaining_net)
        """
        # Calculate repayment amounts if not set
        if loan.monthly_repayment is None:
            if not loan.principal or not loan.duration or loan.duration == 0:
                return 0, remaining_net
            
            if loan.interest and loan.interest > 0:
                total_interest = loan.principal * loan.interest
                loan.total_repayment = round(loan.principal + total_interest, 2)
                loan.monthly_repayment = round(loan.total_repayment / loan.duration, 2)
            else:
                loan.total_repayment = loan.principal
                loan.monthly_repayment = round(loan.principal / loan.duration, 2)

        deduction = loan.monthly_repayment
        if deduction > loan.balance:
            deduction = loan.balance
        if deduction > remaining_net:
            deduction = remaining_net
        deduction = round(deduction, 2)
        print(f"[DEBUG] Processing loan ID {loan.id} | Deduction: {deduction} | Save: {save_changes}")


        if deduction <= 0:
            return 0, remaining_net

        # Update loan balance if saving changes
        if save_changes:
            loan.balance = round(loan.balance - deduction, 2)
            if loan.balance <= 0:
                loan.status = 'completed'
                loan.balance = 0
            db.session.merge(loan)
            db.session.flush()  # <- Add this to ensure DB is aware of the update
            print(f"[DEBUG] Committed loan update: ID={loan.id}, New Balance={loan.balance}")


        return deduction, remaining_net - deduction

    @classmethod
    def apply_loan_deductions_to_payroll(cls, employee_id: int, payroll_id: int) -> Optional[PayrollHistoryModel]:
        """Apply loan deductions to payroll and update loan balances"""
        if not payroll_id:
            print(f"Missing payroll_id for employee {employee_id}")
            return None

        try:
            # Start transaction
            payroll = db.session.query(PayrollHistoryModel).filter_by(
                id=payroll_id,
                employee_id=employee_id
            ).with_for_update().one_or_none()

            if not payroll:
                print(f"No payroll record found for employee {employee_id} with payroll ID {payroll_id}")
                return None

            active_loans = cls._get_active_loans(employee_id)
            if not active_loans:
                return payroll

            remaining_net = payroll.netpay or 0
            total_deductions = 0

            for loan in active_loans:
                if remaining_net <= 0:
                    break

                print(f"[DEBUG] Calling _process_loan_deduction for loan ID {loan.id} with save_changes=True")
                deduction, remaining_net = cls._process_loan_deduction(
                    loan=loan,
                    remaining_net=remaining_net,
                    save_changes=True
                )
                print(f"[DEBUG] About to add repayment of {deduction} for Loan ID {loan.id}")
                if deduction > 0:
                    total_deductions += deduction
                    # Create repayment record
                    repayment = LoanRepaymentModel(
                        payroll_id=payroll.id,
                        loan_id=loan.id,
                        amount=deduction,
                        created_at=datetime.utcnow()
                    )
                    db.session.add(repayment)
                    db.session.flush()

                    print(f"[DEBUG] Repayment created: {repayment.amount} for Loan ID {loan.id}")
                    # Debug check
                    repayments = db.session.query(LoanRepaymentModel).filter_by(loan_id=loan.id).all()
                    print(f"[DEBUG] Total repayments for loan ID {loan.id}: {len(repayments)}")



            # Update payroll
            if total_deductions > 0:
                payroll.monthly_loan_repayment = total_deductions
                payroll.netpay = max(0, round(remaining_net, 2))
                db.session.add(payroll)

            db.session.commit()
            print("[DEBUG] All changes committed successfully")

            

            return payroll

        except Exception as e:
            db.session.rollback()
            print(f"Error in apply_loan_deductions_to_payroll: {e}", exc_info=True)
            raise

    @classmethod
    def estimate_loan_repayment(cls, employee_id: int, net_pay: float) -> float:
        """Estimate total loan deductions for given net pay"""
        active_loans = cls._get_active_loans(employee_id)
        remaining_net = net_pay
        total_deduction = 0.0

        for loan in active_loans:
            if remaining_net <= 0:
                break

            deduction, remaining_net = cls._process_loan_deduction(
                loan=loan,
                remaining_net=remaining_net,
                save_changes=True
                
            )
            total_deduction += deduction

        return round(total_deduction, 2)
    
    @classmethod
    def process_loan_deductions(cls, employee_id: int, payroll_data: dict) -> dict:
     """Process deductions for payroll that doesn't exist yet"""
     try:
         net_pay = float(payroll_data.get('netpay', 0))
         total_deduction = cls.estimate_loan_repayment(
            employee_id=employee_id,
            net_pay=net_pay
         )
        
         return {
            **payroll_data,
            'netpay': round(net_pay - total_deduction, 2),
            'monthly_loan_repayment': round(total_deduction, 2)
         }
     except (ValueError, TypeError) as e:
        print(f"Error processing loan deductions: {e}")
        return payroll_data  # Return original data if error occurs