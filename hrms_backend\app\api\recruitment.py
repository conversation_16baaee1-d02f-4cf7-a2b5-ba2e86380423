from fastapi import APIRouter, HTTPException, status, Depends, Query
from sqlalchemy.orm import Session
from typing import List, Optional
from uuid import UUID

from ..core.security import get_current_user, require_permission, CurrentUser
from ..core.rbac import Permissions
from ..db.session import get_db
from ..schemas.recruitment import (
    JobPostingCreate, JobPostingUpdate, JobPostingResponse, JobPostingListResponse,
    CandidateCreate, CandidateUpdate, CandidateResponse, CandidateListResponse,
    JobApplicationCreate, JobApplicationUpdate, JobApplicationResponse, JobApplicationListResponse,
    InterviewCreate, InterviewUpdate, InterviewResponse, InterviewListResponse,
    JobOfferCreate, JobOfferUpdate, JobOfferResponse, JobOfferListResponse
)
from ..services.hr_management.recruitment_service import RecruitmentService
from ..db.models.recruitment import JobStatus, ApplicationStatus, InterviewType

router = APIRouter()
recruitment_service = RecruitmentService()


# Job Posting Endpoints
@router.post("/job-postings", response_model=JobPostingResponse)
async def create_job_posting(
    job_data: JobPostingCreate,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.RECRUITMENT_CREATE))
):
    """Create a new job posting"""
    return await recruitment_service.create_job_posting(db, job_data, current_user)


@router.get("/job-postings", response_model=JobPostingListResponse)
async def get_job_postings(
    skip: int = Query(0, ge=0),
    limit: int = Query(20, ge=1, le=100),
    status: Optional[JobStatus] = Query(None),
    department_id: Optional[UUID] = Query(None),
    job_type: Optional[str] = Query(None),
    search: Optional[str] = Query(None),
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.RECRUITMENT_READ))
):
    """Get job postings with filtering and pagination"""
    job_postings, total = await recruitment_service.get_job_postings(
        db=db,
        skip=skip,
        limit=limit,
        status=status,
        department_id=department_id,
        job_type=job_type,
        search=search,
        current_user=current_user
    )
    
    return JobPostingListResponse(
        items=job_postings,
        total=total,
        page=skip // limit + 1,
        size=limit,
        pages=(total + limit - 1) // limit
    )


@router.get("/job-postings/{job_id}", response_model=JobPostingResponse)
async def get_job_posting(
    job_id: UUID,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.RECRUITMENT_READ))
):
    """Get a specific job posting"""
    return await recruitment_service.get_job_posting(db, job_id)


@router.put("/job-postings/{job_id}", response_model=JobPostingResponse)
async def update_job_posting(
    job_id: UUID,
    job_data: JobPostingUpdate,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.RECRUITMENT_UPDATE))
):
    """Update a job posting"""
    return await recruitment_service.update_job_posting(db, job_id, job_data, current_user)


@router.post("/job-postings/{job_id}/publish", response_model=JobPostingResponse)
async def publish_job_posting(
    job_id: UUID,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.RECRUITMENT_UPDATE))
):
    """Publish a job posting"""
    return await recruitment_service.publish_job_posting(db, job_id, current_user)


# Candidate Endpoints
@router.post("/candidates", response_model=CandidateResponse)
async def create_candidate(
    candidate_data: CandidateCreate,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.RECRUITMENT_CREATE))
):
    """Create a new candidate"""
    return await recruitment_service.create_candidate(db, candidate_data, current_user)


@router.get("/candidates", response_model=CandidateListResponse)
async def get_candidates(
    skip: int = Query(0, ge=0),
    limit: int = Query(20, ge=1, le=100),
    search: Optional[str] = Query(None),
    skills: Optional[List[str]] = Query(None),
    experience_min: Optional[int] = Query(None, ge=0),
    experience_max: Optional[int] = Query(None, ge=0),
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.RECRUITMENT_READ))
):
    """Get candidates with filtering and pagination"""
    candidates, total = await recruitment_service.get_candidates(
        db=db,
        skip=skip,
        limit=limit,
        search=search,
        skills=skills,
        experience_min=experience_min,
        experience_max=experience_max,
        current_user=current_user
    )
    
    return CandidateListResponse(
        items=candidates,
        total=total,
        page=skip // limit + 1,
        size=limit,
        pages=(total + limit - 1) // limit
    )


@router.get("/candidates/{candidate_id}", response_model=CandidateResponse)
async def get_candidate(
    candidate_id: UUID,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.RECRUITMENT_READ))
):
    """Get a specific candidate"""
    return await recruitment_service.get_candidate(db, candidate_id)


@router.put("/candidates/{candidate_id}", response_model=CandidateResponse)
async def update_candidate(
    candidate_id: UUID,
    candidate_data: CandidateUpdate,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.RECRUITMENT_UPDATE))
):
    """Update a candidate"""
    candidate = await recruitment_service.get_candidate(db, candidate_id)
    
    # Update fields
    update_data = candidate_data.dict(exclude_unset=True)
    for field, value in update_data.items():
        setattr(candidate, field, value)
    
    db.commit()
    db.refresh(candidate)
    
    return candidate


# Job Application Endpoints
@router.post("/applications", response_model=JobApplicationResponse)
async def create_job_application(
    application_data: JobApplicationCreate,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.RECRUITMENT_CREATE))
):
    """Create a new job application"""
    return await recruitment_service.create_job_application(db, application_data, current_user)


@router.get("/applications", response_model=JobApplicationListResponse)
async def get_job_applications(
    skip: int = Query(0, ge=0),
    limit: int = Query(20, ge=1, le=100),
    job_posting_id: Optional[UUID] = Query(None),
    candidate_id: Optional[UUID] = Query(None),
    status: Optional[ApplicationStatus] = Query(None),
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.RECRUITMENT_READ))
):
    """Get job applications with filtering and pagination"""
    from ..db.models.recruitment import JobApplication
    
    query = db.query(JobApplication)
    
    # Apply organization filter
    if current_user.organization_id:
        query = query.filter(JobApplication.organization_id == current_user.organization_id)
    
    # Apply filters
    if job_posting_id:
        query = query.filter(JobApplication.job_posting_id == job_posting_id)
    
    if candidate_id:
        query = query.filter(JobApplication.candidate_id == candidate_id)
    
    if status:
        query = query.filter(JobApplication.status == status)
    
    # Get total count
    total = query.count()
    
    # Apply pagination
    applications = query.offset(skip).limit(limit).all()
    
    return JobApplicationListResponse(
        items=applications,
        total=total,
        page=skip // limit + 1,
        size=limit,
        pages=(total + limit - 1) // limit
    )


@router.put("/applications/{application_id}/status")
async def update_application_status(
    application_id: UUID,
    status: ApplicationStatus,
    notes: Optional[str] = None,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.RECRUITMENT_UPDATE))
):
    """Update job application status"""
    return await recruitment_service.update_application_status(
        db, application_id, status, current_user, notes
    )


# Interview Endpoints
@router.post("/interviews", response_model=InterviewResponse)
async def schedule_interview(
    interview_data: InterviewCreate,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.RECRUITMENT_CREATE))
):
    """Schedule an interview"""
    return await recruitment_service.schedule_interview(db, interview_data, current_user)


@router.get("/interviews", response_model=InterviewListResponse)
async def get_interviews(
    skip: int = Query(0, ge=0),
    limit: int = Query(20, ge=1, le=100),
    application_id: Optional[UUID] = Query(None),
    interview_type: Optional[InterviewType] = Query(None),
    interviewer_id: Optional[UUID] = Query(None),
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.RECRUITMENT_READ))
):
    """Get interviews with filtering and pagination"""
    from ..db.models.recruitment import Interview
    
    query = db.query(Interview)
    
    # Apply organization filter
    if current_user.organization_id:
        query = query.filter(Interview.organization_id == current_user.organization_id)
    
    # Apply filters
    if application_id:
        query = query.filter(Interview.application_id == application_id)
    
    if interview_type:
        query = query.filter(Interview.interview_type == interview_type)
    
    if interviewer_id:
        query = query.filter(Interview.interviewer_ids.contains([str(interviewer_id)]))
    
    # Get total count
    total = query.count()
    
    # Apply pagination
    interviews = query.offset(skip).limit(limit).all()
    
    return InterviewListResponse(
        items=interviews,
        total=total,
        page=skip // limit + 1,
        size=limit,
        pages=(total + limit - 1) // limit
    )


# Job Offer Endpoints
@router.post("/offers", response_model=JobOfferResponse)
async def create_job_offer(
    offer_data: JobOfferCreate,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.RECRUITMENT_CREATE))
):
    """Create a job offer"""
    return await recruitment_service.create_job_offer(db, offer_data, current_user)


@router.get("/offers", response_model=JobOfferListResponse)
async def get_job_offers(
    skip: int = Query(0, ge=0),
    limit: int = Query(20, ge=1, le=100),
    application_id: Optional[UUID] = Query(None),
    status: Optional[str] = Query(None),
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.RECRUITMENT_READ))
):
    """Get job offers with filtering and pagination"""
    from ..db.models.recruitment import JobOffer
    
    query = db.query(JobOffer)
    
    # Apply organization filter
    if current_user.organization_id:
        query = query.filter(JobOffer.organization_id == current_user.organization_id)
    
    # Apply filters
    if application_id:
        query = query.filter(JobOffer.application_id == application_id)
    
    if status:
        query = query.filter(JobOffer.status == status)
    
    # Get total count
    total = query.count()
    
    # Apply pagination
    offers = query.offset(skip).limit(limit).all()
    
    return JobOfferListResponse(
        items=offers,
        total=total,
        page=skip // limit + 1,
        size=limit,
        pages=(total + limit - 1) // limit
    )
