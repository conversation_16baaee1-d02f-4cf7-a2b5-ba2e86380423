from fastapi import APIRouter, HTTPException, status, Depends, Query, UploadFile, File, Form
from sqlalchemy.orm import Session
from typing import List, Optional
from uuid import UUID
import json

from ..core.security import get_current_user, require_permission, CurrentUser
from ..core.rbac import Permissions
from ..db.session import get_db
from ..schemas.document import (
    DocumentResponse, DocumentListResponse, DocumentUpdate,
    DocumentUploadRequest, DocumentAccessCreate, DocumentShareCreate,
    DocumentShareResponse, DocumentTemplateCreate, DocumentTemplateUpdate,
    DocumentTemplateResponse, DocumentVersionListResponse, DocumentType, DocumentStatus
)
from ..services.hr_management.document_service import DocumentService

router = APIRouter()
document_service = DocumentService()


# Document CRUD endpoints
@router.get("/", response_model=DocumentListResponse)
async def get_documents(
    document_type: Optional[DocumentType] = Query(None),
    status: Optional[DocumentStatus] = Query(None),
    search: Optional[str] = Query(None),
    skip: int = Query(0, ge=0),
    limit: int = Query(20, ge=1, le=100),
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.DOCUMENT_READ))
):
    """Get documents with filtering"""
    return await document_service.get_documents(
        db=db,
        current_user=current_user,
        skip=skip,
        limit=limit,
        document_type=document_type,
        status=status,
        search=search
    )


@router.get("/{document_id}", response_model=DocumentResponse)
async def get_document(
    document_id: UUID,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.DOCUMENT_READ))
):
    """Get document by ID"""
    document = await document_service.get_document_by_id(db, document_id, current_user)
    if not document:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Document not found"
        )
    return document


@router.post("/upload", response_model=DocumentResponse)
async def upload_document(
    file: UploadFile = File(...),
    name: str = Form(...),
    description: Optional[str] = Form(None),
    document_type: DocumentType = Form(...),
    tags: Optional[str] = Form(None),  # JSON string
    is_public: bool = Form(False),
    allowed_roles: Optional[str] = Form(None),  # JSON string
    allowed_departments: Optional[str] = Form(None),  # JSON string
    valid_from: Optional[str] = Form(None),  # ISO datetime string
    valid_until: Optional[str] = Form(None),  # ISO datetime string
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.DOCUMENT_CREATE))
):
    """Upload a new document"""
    try:
        # Parse JSON fields
        tags_list = json.loads(tags) if tags else None
        allowed_roles_list = json.loads(allowed_roles) if allowed_roles else None
        allowed_departments_list = json.loads(allowed_departments) if allowed_departments else None
        
        # Parse datetime fields
        from datetime import datetime
        valid_from_dt = datetime.fromisoformat(valid_from) if valid_from else None
        valid_until_dt = datetime.fromisoformat(valid_until) if valid_until else None
        
        document_data = DocumentUploadRequest(
            name=name,
            description=description,
            document_type=document_type,
            tags=tags_list,
            is_public=is_public,
            allowed_roles=allowed_roles_list,
            allowed_departments=allowed_departments_list,
            valid_from=valid_from_dt,
            valid_until=valid_until_dt
        )
        
        return await document_service.upload_document(db, file, document_data, current_user)
        
    except json.JSONDecodeError:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid JSON in form fields"
        )
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Invalid datetime format: {str(e)}"
        )


@router.put("/{document_id}", response_model=DocumentResponse)
async def update_document(
    document_id: UUID,
    document_data: DocumentUpdate,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.DOCUMENT_UPDATE))
):
    """Update document metadata"""
    return await document_service.update_document(db, document_id, document_data, current_user)


@router.delete("/{document_id}")
async def delete_document(
    document_id: UUID,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.DOCUMENT_DELETE))
):
    """Delete document"""
    success = await document_service.delete_document(db, document_id, current_user)
    if success:
        return {"message": "Document deleted successfully"}
    else:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to delete document"
        )


@router.get("/{document_id}/download")
async def download_document(
    document_id: UUID,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.DOCUMENT_READ))
):
    """Download document file"""
    return await document_service.download_document(db, document_id, current_user)


@router.get("/{document_id}/versions", response_model=DocumentVersionListResponse)
async def get_document_versions(
    document_id: UUID,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.DOCUMENT_READ))
):
    """Get document version history"""
    return await document_service.get_document_versions(db, document_id, current_user)


@router.post("/{document_id}/share", response_model=DocumentShareResponse)
async def share_document(
    document_id: UUID,
    share_data: DocumentShareCreate,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.DOCUMENT_SHARE))
):
    """Share document with users/roles/departments"""
    return await document_service.share_document(db, document_id, share_data, current_user)


@router.get("/{document_id}/shares", response_model=List[DocumentShareResponse])
async def get_document_shares(
    document_id: UUID,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.DOCUMENT_READ))
):
    """Get document sharing information"""
    return await document_service.get_document_shares(db, document_id, current_user)


# Document Templates
@router.get("/templates/", response_model=List[DocumentTemplateResponse])
async def get_document_templates(
    template_type: Optional[str] = Query(None),
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.DOCUMENT_READ))
):
    """Get document templates"""
    return await document_service.get_document_templates(db, template_type, current_user)


@router.post("/templates/", response_model=DocumentTemplateResponse)
async def create_document_template(
    template_data: DocumentTemplateCreate,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.DOCUMENT_CREATE))
):
    """Create document template"""
    return await document_service.create_document_template(db, template_data, current_user)


@router.put("/templates/{template_id}", response_model=DocumentTemplateResponse)
async def update_document_template(
    template_id: UUID,
    template_data: DocumentTemplateUpdate,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.DOCUMENT_UPDATE))
):
    """Update document template"""
    return await document_service.update_document_template(db, template_id, template_data, current_user)


# Public endpoints for document access
@router.get("/public/{document_id}")
async def get_public_document(
    document_id: UUID,
    db: Session = Depends(get_db)
):
    """Get public document (no authentication required)"""
    return await document_service.get_public_document(db, document_id)


@router.get("/public/{document_id}/download")
async def download_public_document(
    document_id: UUID,
    db: Session = Depends(get_db)
):
    """Download public document (no authentication required)"""
    return await document_service.download_public_document(db, document_id)
