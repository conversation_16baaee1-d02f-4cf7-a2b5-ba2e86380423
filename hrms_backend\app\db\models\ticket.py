from sqlalchemy import Colum<PERSON>, String, DateTime, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, Integer, Enum
from sqlalchemy.dialects.postgresql import UUID, JSONB
from sqlalchemy.orm import relationship
from enum import Enum as PyEnum
from datetime import datetime
from typing import Optional

from ..base import BaseModel, AuditMixin


class TicketStatus(PyEnum):
    OPEN = "open"
    IN_PROGRESS = "in_progress"
    PENDING = "pending"
    RESOLVED = "resolved"
    CLOSED = "closed"
    CANCELLED = "cancelled"


class TicketPriority(PyEnum):
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    URGENT = "urgent"
    CRITICAL = "critical"


class TicketType(PyEnum):
    IT_SUPPORT = "it_support"
    HR_QUERY = "hr_query"
    FACILITIES = "facilities"
    PAYROLL = "payroll"
    LEAVE = "leave"
    EQUIPMENT = "equipment"
    ACCESS_REQUEST = "access_request"
    COMPLAINT = "complaint"
    SUGGESTION = "suggestion"
    TRAINING_REQUEST = "training_request"
    POLICY_CLARIFICATION = "policy_clarification"
    DOCUMENT_REQUEST = "document_request"
    SYSTEM_ACCESS = "system_access"
    WORKSPACE_REQUEST = "workspace_request"
    TRAVEL_REQUEST = "travel_request"
    EXPENSE_QUERY = "expense_query"
    BENEFITS_QUERY = "benefits_query"
    PERFORMANCE_QUERY = "performance_query"
    RECRUITMENT_SUPPORT = "recruitment_support"
    ONBOARDING_SUPPORT = "onboarding_support"
    OFFBOARDING_SUPPORT = "offboarding_support"
    OTHER = "other"


class WorkflowStatus(PyEnum):
    """Workflow status for automated case management"""
    ACTIVE = "active"
    PAUSED = "paused"
    COMPLETED = "completed"
    FAILED = "failed"


class EscalationLevel(PyEnum):
    """Escalation levels"""
    LEVEL_1 = "level_1"  # First line support
    LEVEL_2 = "level_2"  # Specialist/Senior support
    LEVEL_3 = "level_3"  # Expert/Management
    LEVEL_4 = "level_4"  # Executive/Critical


class Ticket(BaseModel, AuditMixin):
    """Support ticket model"""
    __tablename__ = "tickets"

    # Basic information
    ticket_number = Column(String(50), unique=True, nullable=False, index=True)
    title = Column(String(200), nullable=False)
    description = Column(Text, nullable=False)

    # Classification
    ticket_type = Column(Enum(TicketType), nullable=False)
    category = Column(String(100), nullable=True)
    subcategory = Column(String(100), nullable=True)

    # Priority and status
    priority = Column(Enum(TicketPriority), nullable=False, default=TicketPriority.MEDIUM)
    status = Column(Enum(TicketStatus), nullable=False, default=TicketStatus.OPEN)

    # People involved
    requester_id = Column(UUID(as_uuid=True), ForeignKey("employees.id"), nullable=False, index=True)
    assigned_to = Column(UUID(as_uuid=True), ForeignKey("employees.id"), nullable=True)
    assigned_team = Column(String(100), nullable=True)  # IT, HR, Facilities, etc.

    # Organization
    organization_id = Column(UUID(as_uuid=True), nullable=False, index=True)
    department_id = Column(UUID(as_uuid=True), ForeignKey("departments.id"), nullable=True)

    # Timeline
    due_date = Column(DateTime(timezone=True), nullable=True)
    first_response_at = Column(DateTime(timezone=True), nullable=True)
    resolved_at = Column(DateTime(timezone=True), nullable=True)
    closed_at = Column(DateTime(timezone=True), nullable=True)

    # SLA tracking
    sla_breach = Column(Boolean, default=False)
    response_sla_hours = Column(Integer, nullable=True)
    resolution_sla_hours = Column(Integer, nullable=True)

    # Contact information
    contact_method = Column(String(50), nullable=True)  # email, phone, in_person
    contact_details = Column(String(255), nullable=True)

    # Location
    location = Column(String(255), nullable=True)
    asset_tag = Column(String(100), nullable=True)  # For equipment-related tickets

    # Attachments
    attachment_urls = Column(JSONB, nullable=True)  # Array of file URLs

    # Resolution
    resolution = Column(Text, nullable=True)
    resolution_category = Column(String(100), nullable=True)

    # Satisfaction
    satisfaction_rating = Column(Integer, nullable=True)  # 1-5 scale
    satisfaction_feedback = Column(Text, nullable=True)

    # Auto-assignment and workflow
    auto_assigned = Column(Boolean, default=False)
    assignment_rules_applied = Column(JSONB, nullable=True)
    workflow_id = Column(UUID(as_uuid=True), ForeignKey("ticket_workflows.id"), nullable=True)
    workflow_status = Column(Enum(WorkflowStatus), nullable=True)
    workflow_step = Column(Integer, nullable=True, default=0)

    # Knowledge base and FAQ
    related_kb_articles = Column(JSONB, nullable=True)  # Array of KB article IDs
    suggested_solutions = Column(JSONB, nullable=True)  # AI-suggested solutions

    # Customer communication
    customer_visible = Column(Boolean, default=True)
    communication_preferences = Column(JSONB, nullable=True)  # Email, SMS, etc.

    # Business impact
    business_impact = Column(String(50), nullable=True)  # low, medium, high, critical
    affected_users_count = Column(Integer, nullable=True)
    estimated_cost = Column(Integer, nullable=True)  # Cost in cents

    # Approval workflow
    requires_approval = Column(Boolean, default=False)
    approval_status = Column(String(50), nullable=True)  # pending, approved, rejected
    approved_by_id = Column(UUID(as_uuid=True), ForeignKey("employees.id"), nullable=True)
    approved_at = Column(DateTime(timezone=True), nullable=True)

    # Parent/child relationships for complex cases
    parent_ticket_id = Column(UUID(as_uuid=True), ForeignKey("tickets.id"), nullable=True)
    is_subtask = Column(Boolean, default=False)

    # Relationships
    requester = relationship("Employee", foreign_keys=[requester_id])
    assignee = relationship("Employee", foreign_keys=[assigned_to])
    approved_by = relationship("Employee", foreign_keys=[approved_by_id])
    department = relationship("Department")
    workflow = relationship("TicketWorkflow", back_populates="tickets")
    comments = relationship("TicketComment", back_populates="ticket")
    activities = relationship("TicketActivity", back_populates="ticket")
    escalations = relationship("TicketEscalation", back_populates="ticket")
    notifications = relationship("TicketNotification", back_populates="ticket")
    parent_ticket = relationship("Ticket", remote_side="Ticket.id", back_populates="subtasks")
    subtasks = relationship("Ticket", back_populates="parent_ticket")


class TicketComment(BaseModel, AuditMixin):
    """Ticket comment model"""
    __tablename__ = "ticket_comments"

    ticket_id = Column(UUID(as_uuid=True), ForeignKey("tickets.id"), nullable=False, index=True)
    author_id = Column(UUID(as_uuid=True), ForeignKey("employees.id"), nullable=False)

    content = Column(Text, nullable=False)
    is_internal = Column(Boolean, default=False)  # Internal notes vs customer-visible
    is_solution = Column(Boolean, default=False)  # Mark as solution

    # Attachments
    attachment_urls = Column(JSONB, nullable=True)  # Array of file URLs

    # Time tracking
    time_spent_minutes = Column(Integer, nullable=True)

    # Relationships
    ticket = relationship("Ticket", back_populates="comments")
    author = relationship("Employee")


class TicketActivity(BaseModel):
    """Ticket activity log"""
    __tablename__ = "ticket_activities"

    ticket_id = Column(UUID(as_uuid=True), ForeignKey("tickets.id"), nullable=False, index=True)
    user_id = Column(UUID(as_uuid=True), ForeignKey("employees.id"), nullable=True)

    # Activity details
    activity_type = Column(String(50), nullable=False)  # status_change, assignment, comment, etc.
    description = Column(Text, nullable=False)

    # Activity data
    old_value = Column(Text, nullable=True)
    new_value = Column(Text, nullable=True)
    activity_metadata = Column(JSONB, nullable=True)

    # System vs user activity
    is_system_activity = Column(Boolean, default=False)

    # Relationships
    ticket = relationship("Ticket", back_populates="activities")
    user = relationship("Employee")


class TicketWorkflow(BaseModel, AuditMixin):
    """Ticket workflow automation model"""
    __tablename__ = "ticket_workflows"

    # Basic information
    name = Column(String(200), nullable=False)
    description = Column(Text, nullable=True)

    # Workflow configuration
    trigger_conditions = Column(JSONB, nullable=False)  # Conditions to trigger this workflow
    workflow_steps = Column(JSONB, nullable=False)  # Array of workflow steps

    # Status and settings
    is_active = Column(Boolean, default=True)
    priority = Column(Integer, default=0)  # Higher priority workflows run first

    # Organization
    organization_id = Column(UUID(as_uuid=True), nullable=False, index=True)

    # Relationships
    tickets = relationship("Ticket", back_populates="workflow")


class TicketNotification(BaseModel):
    """Ticket notification model"""
    __tablename__ = "ticket_notifications"

    ticket_id = Column(UUID(as_uuid=True), ForeignKey("tickets.id"), nullable=False, index=True)
    recipient_id = Column(UUID(as_uuid=True), ForeignKey("employees.id"), nullable=False)

    # Notification details
    notification_type = Column(String(50), nullable=False)  # email, sms, push, in_app
    subject = Column(String(255), nullable=False)
    message = Column(Text, nullable=False)

    # Status
    sent_at = Column(DateTime(timezone=True), nullable=True)
    delivered_at = Column(DateTime(timezone=True), nullable=True)
    read_at = Column(DateTime(timezone=True), nullable=True)
    failed_at = Column(DateTime(timezone=True), nullable=True)
    failure_reason = Column(Text, nullable=True)

    # Retry logic
    retry_count = Column(Integer, default=0)
    max_retries = Column(Integer, default=3)
    next_retry_at = Column(DateTime(timezone=True), nullable=True)

    # Relationships
    ticket = relationship("Ticket", back_populates="notifications")
    recipient = relationship("Employee")


class TicketTemplate(BaseModel, AuditMixin):
    """Ticket template for common requests"""
    __tablename__ = "ticket_templates"

    # Basic information
    name = Column(String(200), nullable=False)
    description = Column(Text, nullable=True)

    # Template configuration
    ticket_type = Column(Enum(TicketType), nullable=False)
    category = Column(String(100), nullable=True)
    subcategory = Column(String(100), nullable=True)
    priority = Column(Enum(TicketPriority), nullable=False, default=TicketPriority.MEDIUM)

    # Template content
    title_template = Column(String(200), nullable=False)
    description_template = Column(Text, nullable=False)

    # Auto-assignment
    default_assignee_id = Column(UUID(as_uuid=True), ForeignKey("employees.id"), nullable=True)
    default_team = Column(String(100), nullable=True)

    # SLA settings
    response_sla_hours = Column(Integer, nullable=True)
    resolution_sla_hours = Column(Integer, nullable=True)

    # Form fields for data collection
    form_fields = Column(JSONB, nullable=True)  # Dynamic form fields

    # Workflow
    workflow_id = Column(UUID(as_uuid=True), ForeignKey("ticket_workflows.id"), nullable=True)

    # Status and usage
    is_active = Column(Boolean, default=True)
    usage_count = Column(Integer, default=0)

    # Organization
    organization_id = Column(UUID(as_uuid=True), nullable=False, index=True)

    # Relationships
    default_assignee = relationship("Employee")
    workflow = relationship("TicketWorkflow")


class TicketSLA(BaseModel, AuditMixin):
    """SLA configuration for tickets"""
    __tablename__ = "ticket_slas"

    # Basic information
    name = Column(String(200), nullable=False)
    description = Column(Text, nullable=True)

    # SLA conditions
    conditions = Column(JSONB, nullable=False)  # Conditions for this SLA to apply

    # SLA targets
    response_time_hours = Column(Integer, nullable=False)
    resolution_time_hours = Column(Integer, nullable=False)

    # Business hours
    business_hours_only = Column(Boolean, default=True)
    business_hours = Column(JSONB, nullable=True)  # Business hours configuration

    # Escalation
    escalation_enabled = Column(Boolean, default=True)
    escalation_levels = Column(JSONB, nullable=True)  # Escalation configuration

    # Status
    is_active = Column(Boolean, default=True)
    priority = Column(Integer, default=0)  # Higher priority SLAs are checked first

    # Organization
    organization_id = Column(UUID(as_uuid=True), nullable=False, index=True)


class TicketEscalation(BaseModel, AuditMixin):
    """Ticket escalation model"""
    __tablename__ = "ticket_escalations"

    ticket_id = Column(UUID(as_uuid=True), ForeignKey("tickets.id"), nullable=False)
    escalated_by = Column(UUID(as_uuid=True), ForeignKey("employees.id"), nullable=False)
    escalated_to = Column(UUID(as_uuid=True), ForeignKey("employees.id"), nullable=False)

    # Escalation details
    reason = Column(Text, nullable=False)
    escalation_level = Column(Integer, nullable=False, default=1)

    # Timeline
    escalated_at = Column(DateTime(timezone=True), nullable=False, default=datetime.utcnow)
    acknowledged_at = Column(DateTime(timezone=True), nullable=True)
    resolved_at = Column(DateTime(timezone=True), nullable=True)

    # Status
    is_active = Column(Boolean, default=True)

    # Relationships
    ticket = relationship("Ticket", back_populates="escalations")
    escalator = relationship("Employee", foreign_keys=[escalated_by])
    escalatee = relationship("Employee", foreign_keys=[escalated_to])


class TicketCategory(BaseModel):
    """Ticket category configuration"""
    __tablename__ = "ticket_categories"

    name = Column(String(100), nullable=False)
    description = Column(Text, nullable=True)
    organization_id = Column(UUID(as_uuid=True), nullable=False, index=True)

    # Category settings
    ticket_type = Column(Enum(TicketType), nullable=False)
    parent_category_id = Column(UUID(as_uuid=True), ForeignKey("ticket_categories.id"), nullable=True)

    # SLA settings
    default_priority = Column(Enum(TicketPriority), nullable=False, default=TicketPriority.MEDIUM)
    response_sla_hours = Column(Integer, nullable=True)
    resolution_sla_hours = Column(Integer, nullable=True)

    # Auto-assignment
    auto_assign_team = Column(String(100), nullable=True)
    auto_assign_to = Column(UUID(as_uuid=True), ForeignKey("employees.id"), nullable=True)

    # Settings
    requires_approval = Column(Boolean, default=False)
    is_active = Column(Boolean, default=True)

    # Relationships
    parent_category = relationship("TicketCategory", remote_side="TicketCategory.id", back_populates="subcategories")
    subcategories = relationship("TicketCategory", back_populates="parent_category")
    auto_assignee = relationship("Employee", foreign_keys=[auto_assign_to])






