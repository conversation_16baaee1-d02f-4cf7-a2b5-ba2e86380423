#!/usr/bin/env python3
"""
Simple test for the approval workflow endpoint
"""

import requests
import json

def test_approval_workflow():
    """Test just the approval workflow endpoint"""
    try:
        print("🔍 Testing approval workflow endpoint...")
        
        # Login
        login_data = {'email': '<EMAIL>', 'password': 'password123'}
        response = requests.post('http://localhost:8000/api/auth/login', json=login_data)
        
        if response.status_code != 200:
            print('❌ Login failed')
            return False
            
        data = response.json()
        token = data.get('access_token')
        print('✅ Login successful')
        
        headers = {'Authorization': f'Bearer {token}'}
        
        # Test the approval workflow endpoint
        print('📋 Testing GET /api/leave/requests?status=PENDING...')
        pending_response = requests.get('http://localhost:8000/api/leave/requests?status=PENDING', headers=headers)
        
        print(f'Status Code: {pending_response.status_code}')
        print(f'Response: {pending_response.text[:500]}')
        
        if pending_response.status_code == 200:
            print('✅ Approval workflow endpoint is working!')
            return True
        else:
            print('❌ Approval workflow endpoint failed')
            return False
        
    except Exception as e:
        print(f'❌ Error: {e}')
        return False

if __name__ == "__main__":
    success = test_approval_workflow()
    if success:
        print('\n🎉 Approval workflow is working!')
    else:
        print('\n❌ Approval workflow needs fixing.')
