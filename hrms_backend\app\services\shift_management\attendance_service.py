from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, func, text
from typing import Optional, List, Tuple, Dict
from uuid import UUID
from datetime import datetime, date, time, timedelta
from decimal import Decimal
from fastapi import HTTPException, status
import logging
import pytz
from geopy.distance import geodesic

from ...db.models.attendance import AttendanceRecord, AttendanceLog, AttendancePolicy
from ...db.models.employee import Employee
from ...db.models.shift import Shift
from ...schemas.attendance import (
    AttendanceRecordCreate, AttendanceRecordUpdate, AttendanceRecordResponse,
    AttendanceListResponse, CheckInRequest, CheckOutRequest, BreakRequest,
    AttendanceLogResponse, AttendancePolicyCreate, AttendancePolicyUpdate,
    AttendancePolicyResponse, AttendanceStatusResponse, AttendanceSummary,
    AttendanceReport, BulkAttendanceUpdate, AttendanceApprovalRequest,
    CheckType, AttendanceStatus
)
from ...core.security import CurrentUser
from ...core.websocket_manager import notification_manager

logger = logging.getLogger(__name__)


class AttendanceService:
    """Attendance service for business logic"""

    async def check_in(
        self,
        db: Session,
        employee_id: UUID,
        check_in_data: CheckInRequest,
        current_user: CurrentUser
    ) -> AttendanceRecordResponse:
        """Employee check-in"""
        try:
            # Verify employee exists and belongs to organization
            employee = db.query(Employee).filter(
                Employee.id == employee_id,
                Employee.organization_id == current_user.organization_id,
                Employee.is_active == True
            ).first()

            if not employee:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Employee not found"
                )

            # Check if already checked in today
            today = date.today()
            existing_record = db.query(AttendanceRecord).filter(
                AttendanceRecord.employee_id == employee_id,
                AttendanceRecord.date == today
            ).first()

            if existing_record and existing_record.check_in_time:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Already checked in today"
                )

            # Get attendance policy
            policy = await self._get_attendance_policy(db, current_user.organization_id)

            # Validate geofencing if enabled
            if policy and policy.enable_geofencing:
                await self._validate_geofencing(check_in_data, policy)

            # Create or update attendance record
            now = datetime.utcnow()

            if existing_record:
                # Update existing record
                existing_record.check_in_time = now
                existing_record.work_location = check_in_data.work_location
                existing_record.is_remote = check_in_data.is_remote
                existing_record.status = AttendanceStatus.PRESENT
                attendance_record = existing_record
            else:
                # Create new record
                attendance_record = AttendanceRecord(
                    employee_id=employee_id,
                    date=today,
                    check_in_time=now,
                    work_location=check_in_data.work_location,
                    is_remote=check_in_data.is_remote,
                    status=AttendanceStatus.PRESENT
                )
                db.add(attendance_record)

            # Create attendance log
            attendance_log = AttendanceLog(
                employee_id=employee_id,
                timestamp=now,
                check_type=CheckType.CHECK_IN,
                latitude=check_in_data.latitude,
                longitude=check_in_data.longitude,
                location_name=check_in_data.location_name,
                photo_url=check_in_data.photo_url
            )
            db.add(attendance_log)

            db.commit()
            db.refresh(attendance_record)

            # Link log to attendance record
            attendance_log.attendance_record_id = attendance_record.id
            db.commit()

            # Send real-time notification
            await notification_manager.notify_attendance_update(
                str(employee_id),
                {
                    "type": "check_in",
                    "time": now.isoformat(),
                    "location": check_in_data.location_name
                }
            )

            logger.info(f"Employee {employee_id} checked in at {now}")
            return AttendanceRecordResponse.from_orm(attendance_record)

        except HTTPException:
            raise
        except Exception as e:
            db.rollback()
            logger.error(f"Error during check-in for employee {employee_id}: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Error processing check-in"
            )

    async def check_out(
        self,
        db: Session,
        employee_id: UUID,
        check_out_data: CheckOutRequest,
        current_user: CurrentUser
    ) -> AttendanceRecordResponse:
        """Employee check-out"""
        try:
            # Get today's attendance record
            today = date.today()
            attendance_record = db.query(AttendanceRecord).filter(
                AttendanceRecord.employee_id == employee_id,
                AttendanceRecord.date == today
            ).first()

            if not attendance_record or not attendance_record.check_in_time:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="No check-in found for today"
                )

            if attendance_record.check_out_time:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Already checked out today"
                )

            # Update attendance record
            now = datetime.utcnow()
            attendance_record.check_out_time = now

            # Calculate total hours worked
            total_seconds = (now - attendance_record.check_in_time).total_seconds()

            # Subtract break time if any
            if attendance_record.total_break_duration:
                total_seconds -= (attendance_record.total_break_duration * 60)

            attendance_record.total_hours_worked = Decimal(total_seconds / 3600)

            # Calculate overtime if applicable
            policy = await self._get_attendance_policy(db, current_user.organization_id)
            if policy and attendance_record.total_hours_worked > policy.overtime_threshold:
                attendance_record.overtime_hours = attendance_record.total_hours_worked - policy.overtime_threshold

            # Create attendance log
            attendance_log = AttendanceLog(
                employee_id=employee_id,
                attendance_record_id=attendance_record.id,
                timestamp=now,
                check_type=CheckType.CHECK_OUT,
                latitude=check_out_data.latitude,
                longitude=check_out_data.longitude,
                location_name=check_out_data.location_name,
                photo_url=check_out_data.photo_url,
                created_by=current_user.user_id
            )
            db.add(attendance_log)

            db.commit()
            db.refresh(attendance_record)

            # Check for compensation leave eligibility
            if attendance_record.overtime_hours and attendance_record.overtime_hours > 0:
                await self._process_overtime_compensation(
                    db, employee_id, attendance_record, current_user
                )

            # Send real-time notification
            await notification_manager.notify_attendance_update(
                str(employee_id),
                {
                    "type": "check_out",
                    "time": now.isoformat(),
                    "total_hours": float(attendance_record.total_hours_worked),
                    "overtime_hours": float(attendance_record.overtime_hours or 0)
                }
            )

            logger.info(f"Employee {employee_id} checked out at {now}")
            return AttendanceRecordResponse.from_orm(attendance_record)

        except HTTPException:
            raise
        except Exception as e:
            db.rollback()
            logger.error(f"Error during check-out for employee {employee_id}: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Error processing check-out"
            )

    async def manage_break(
        self,
        db: Session,
        employee_id: UUID,
        break_data: BreakRequest,
        current_user: CurrentUser
    ) -> AttendanceRecordResponse:
        """Manage employee break (start/end)"""
        try:
            # Get today's attendance record
            today = date.today()
            attendance_record = db.query(AttendanceRecord).filter(
                AttendanceRecord.employee_id == employee_id,
                AttendanceRecord.date == today
            ).first()

            if not attendance_record or not attendance_record.check_in_time:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="No check-in found for today"
                )

            now = datetime.utcnow()

            if break_data.break_type == CheckType.BREAK_START:
                if attendance_record.break_start_time:
                    raise HTTPException(
                        status_code=status.HTTP_400_BAD_REQUEST,
                        detail="Break already started"
                    )
                attendance_record.break_start_time = now

            elif break_data.break_type == CheckType.BREAK_END:
                if not attendance_record.break_start_time:
                    raise HTTPException(
                        status_code=status.HTTP_400_BAD_REQUEST,
                        detail="Break not started"
                    )
                if attendance_record.break_end_time:
                    raise HTTPException(
                        status_code=status.HTTP_400_BAD_REQUEST,
                        detail="Break already ended"
                    )

                attendance_record.break_end_time = now

                # Calculate break duration
                break_duration = (now - attendance_record.break_start_time).total_seconds() / 60
                attendance_record.total_break_duration = int(break_duration)

            # Create attendance log
            attendance_log = AttendanceLog(
                employee_id=employee_id,
                attendance_record_id=attendance_record.id,
                timestamp=now,
                check_type=break_data.break_type,
                latitude=break_data.latitude,
                longitude=break_data.longitude,
                location_name=break_data.location_name,
                created_by=current_user.user_id
            )
            db.add(attendance_log)

            db.commit()
            db.refresh(attendance_record)

            logger.info(f"Employee {employee_id} {break_data.break_type} at {now}")
            return AttendanceRecordResponse.from_orm(attendance_record)

        except HTTPException:
            raise
        except Exception as e:
            db.rollback()
            logger.error(f"Error managing break for employee {employee_id}: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Error managing break"
            )

    async def get_attendance_status(
        self,
        db: Session,
        employee_id: UUID,
        current_user: CurrentUser
    ) -> AttendanceStatusResponse:
        """Get current attendance status for employee"""
        try:
            today = date.today()
            attendance_record = db.query(AttendanceRecord).filter(
                AttendanceRecord.employee_id == employee_id,
                AttendanceRecord.date == today
            ).first()

            # Get latest log entry
            latest_log = db.query(AttendanceLog).filter(
                AttendanceLog.employee_id == employee_id,
                func.date(AttendanceLog.timestamp) == today
            ).order_by(AttendanceLog.timestamp.desc()).first()

            # Determine current status
            current_status = "not_checked_in"
            is_on_break = False

            if attendance_record:
                if attendance_record.check_out_time:
                    current_status = "checked_out"
                elif attendance_record.break_start_time and not attendance_record.break_end_time:
                    current_status = "on_break"
                    is_on_break = True
                elif attendance_record.check_in_time:
                    current_status = "checked_in"

            # Calculate total hours today
            total_hours_today = None
            if attendance_record and attendance_record.total_hours_worked:
                total_hours_today = attendance_record.total_hours_worked
            elif attendance_record and attendance_record.check_in_time:
                # Calculate current hours if still checked in
                now = datetime.utcnow()
                total_seconds = (now - attendance_record.check_in_time).total_seconds()
                if attendance_record.total_break_duration:
                    total_seconds -= (attendance_record.total_break_duration * 60)
                total_hours_today = Decimal(total_seconds / 3600)

            return AttendanceStatusResponse(
                employee_id=employee_id,
                current_status=current_status,
                last_action=latest_log.check_type if latest_log else None,
                last_action_time=latest_log.timestamp if latest_log else None,
                today_check_in=attendance_record.check_in_time if attendance_record else None,
                today_check_out=attendance_record.check_out_time if attendance_record else None,
                total_hours_today=total_hours_today,
                is_on_break=is_on_break,
                break_start_time=attendance_record.break_start_time if attendance_record else None
            )

        except Exception as e:
            logger.error(f"Error getting attendance status for employee {employee_id}: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Error retrieving attendance status"
            )

    async def get_attendance_records(
        self,
        db: Session,
        employee_id: Optional[UUID] = None,
        start_date: Optional[date] = None,
        end_date: Optional[date] = None,
        skip: int = 0,
        limit: int = 20,
        current_user: CurrentUser = None
    ) -> AttendanceListResponse:
        """Get attendance records with filtering"""
        try:
            query = db.query(AttendanceRecord)

            # Filter by organization
            if current_user and current_user.organization_id:
                # Join with Employee to filter by organization
                query = query.join(Employee, AttendanceRecord.employee_id == Employee.id).filter(
                    Employee.organization_id == current_user.organization_id
                )

            # Apply filters
            if employee_id:
                query = query.filter(AttendanceRecord.employee_id == employee_id)

            if start_date:
                query = query.filter(AttendanceRecord.date >= start_date)

            if end_date:
                query = query.filter(AttendanceRecord.date <= end_date)

            # Get total count
            total = query.count()

            # Apply pagination and ordering
            records = query.order_by(
                AttendanceRecord.date.desc(),
                AttendanceRecord.check_in_time.desc()
            ).offset(skip).limit(limit).all()

            # Convert to response format
            record_responses = [AttendanceRecordResponse.from_orm(record) for record in records]

            return AttendanceListResponse(
                records=record_responses,
                total=total,
                skip=skip,
                limit=limit
            )

        except Exception as e:
            logger.error(f"Error getting attendance records: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Error retrieving attendance records"
            )

    async def approve_attendance(
        self,
        db: Session,
        approval_data: AttendanceApprovalRequest,
        current_user: CurrentUser
    ) -> List[AttendanceRecordResponse]:
        """Approve or reject attendance records"""
        try:
            updated_records = []

            for record_id in approval_data.attendance_record_ids:
                record = db.query(AttendanceRecord).filter(
                    AttendanceRecord.id == record_id
                ).first()

                if record:
                    # Verify organization access through employee
                    employee = db.query(Employee).filter(
                        Employee.id == record.employee_id,
                        Employee.organization_id == current_user.organization_id
                    ).first()

                    if employee:
                        record.is_approved = approval_data.approved
                        record.approved_by = current_user.user_id
                        record.approved_at = datetime.utcnow()

                        if approval_data.comments:
                            record.notes = approval_data.comments

                        updated_records.append(AttendanceRecordResponse.from_orm(record))

            db.commit()

            logger.info(f"Approved {len(updated_records)} attendance records by {current_user.user_id}")
            return updated_records

        except Exception as e:
            db.rollback()
            logger.error(f"Error approving attendance records: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Error approving attendance records"
            )

    # Helper methods
    async def _get_attendance_policy(
        self,
        db: Session,
        organization_id: UUID
    ) -> Optional[AttendancePolicy]:
        """Get attendance policy for organization"""
        return db.query(AttendancePolicy).filter(
            AttendancePolicy.organization_id == organization_id,
            AttendancePolicy.is_active == True
        ).first()

    async def _validate_geofencing(
        self,
        location_data: CheckInRequest,
        policy: AttendancePolicy
    ):
        """Validate geofencing constraints"""
        if not location_data.latitude or not location_data.longitude:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Location required for geofencing"
            )

        if not policy.office_latitude or not policy.office_longitude:
            return  # Skip validation if office location not set

        # Calculate distance
        employee_location = (float(location_data.latitude), float(location_data.longitude))
        office_location = (float(policy.office_latitude), float(policy.office_longitude))

        distance = geodesic(employee_location, office_location).meters

        if distance > policy.geofence_radius:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Outside allowed location. Distance: {distance:.0f}m, Allowed: {policy.geofence_radius}m"
            )

    async def auto_checkout_employees(
        self,
        db: Session,
        organization_id: UUID
    ):
        """Auto checkout employees at end of day (Celery task)"""
        try:
            policy = await self._get_attendance_policy(db, organization_id)
            if not policy or not policy.auto_clock_out_enabled:
                return

            today = date.today()

            # Find employees who are still checked in
            records = db.query(AttendanceRecord).join(Employee).filter(
                Employee.organization_id == organization_id,
                AttendanceRecord.date == today,
                AttendanceRecord.check_in_time.isnot(None),
                AttendanceRecord.check_out_time.is_(None)
            ).all()

            auto_checkout_time = datetime.combine(today, policy.auto_clock_out_time)

            for record in records:
                record.check_out_time = auto_checkout_time

                # Calculate hours worked
                total_seconds = (auto_checkout_time - record.check_in_time).total_seconds()
                if record.total_break_duration:
                    total_seconds -= (record.total_break_duration * 60)

                record.total_hours_worked = Decimal(total_seconds / 3600)

                # Calculate overtime
                if record.total_hours_worked > policy.overtime_threshold:
                    record.overtime_hours = record.total_hours_worked - policy.overtime_threshold

            db.commit()
            logger.info(f"Auto checked out {len(records)} employees for organization {organization_id}")

        except Exception as e:
            db.rollback()
            logger.error(f"Error in auto checkout: {e}")
            raise

    async def _process_overtime_compensation(
        self,
        db: Session,
        employee_id: UUID,
        attendance_record: AttendanceRecord,
        current_user: CurrentUser
    ):
        """Process overtime hours for compensation leave"""
        try:
            from ...db.models.leave import LeavePolicy, LeaveBalance, LeaveRequest
            from ...schemas.leave import LeaveStatus

            # Get compensation leave policy
            comp_leave_policy = db.query(LeavePolicy).filter(
                LeavePolicy.organization_id == current_user.organization_id,
                LeavePolicy.leave_type == "COMPENSATION",
                LeavePolicy.is_active == True
            ).first()

            if not comp_leave_policy:
                logger.warning("No compensation leave policy found")
                return

            # Calculate compensation days (e.g., 8 hours = 1 day)
            hours_per_day = 8
            overtime_hours = float(attendance_record.overtime_hours)
            compensation_days = overtime_hours / hours_per_day

            # Only process if overtime is significant (e.g., >= 2 hours)
            min_overtime_hours = 2
            if overtime_hours < min_overtime_hours:
                return

            # Check if employee already has a compensation leave balance
            balance = db.query(LeaveBalance).filter(
                LeaveBalance.employee_id == employee_id,
                LeaveBalance.leave_policy_id == comp_leave_policy.id,
                LeaveBalance.year == date.today().year
            ).first()

            if not balance:
                # Create new balance
                balance = LeaveBalance(
                    employee_id=employee_id,
                    leave_policy_id=comp_leave_policy.id,
                    year=date.today().year,
                    opening_balance=Decimal('0'),
                    accrued_balance=Decimal('0'),
                    used_balance=Decimal('0'),
                    pending_balance=Decimal('0'),
                    carried_forward=Decimal('0'),
                    available_balance=Decimal('0'),
                    created_by=current_user.user_id
                )
                db.add(balance)

            # Add compensation days to balance
            balance.accrued_balance += Decimal(str(compensation_days))
            balance.available_balance += Decimal(str(compensation_days))

            # Create a compensation leave request (auto-approved)
            leave_request = LeaveRequest(
                employee_id=employee_id,
                leave_policy_id=comp_leave_policy.id,
                start_date=attendance_record.date,
                end_date=attendance_record.date,
                total_days=Decimal(str(compensation_days)),
                duration_type="FULL_DAY",
                reason=f"Compensation for {overtime_hours:.2f} hours overtime on {attendance_record.date}",
                status=LeaveStatus.APPROVED,  # Auto-approve compensation
                applied_at=datetime.utcnow(),
                approved_at=datetime.utcnow(),
                approved_by=current_user.user_id,  # System approval
                created_by=current_user.user_id
            )

            db.add(leave_request)
            db.commit()

            # Send notification about compensation leave credit
            await notification_manager.notify_compensation_credit(
                str(employee_id),
                {
                    "overtime_hours": overtime_hours,
                    "compensation_days": compensation_days,
                    "date": attendance_record.date.isoformat(),
                    "total_available": float(balance.available_balance)
                }
            )

            logger.info(f"Processed {compensation_days:.2f} compensation days for employee {employee_id}")

        except Exception as e:
            logger.error(f"Error processing overtime compensation: {e}")
            # Don't raise exception as this is supplementary functionality

    async def get_overtime_summary(
        self,
        db: Session,
        employee_id: UUID,
        start_date: Optional[date] = None,
        end_date: Optional[date] = None,
        current_user: CurrentUser = None
    ) -> Dict:
        """Get overtime summary for employee"""
        try:
            if not start_date:
                start_date = date.today().replace(day=1)  # Start of current month
            if not end_date:
                end_date = date.today()

            # Get attendance records with overtime
            records = db.query(AttendanceRecord).filter(
                AttendanceRecord.employee_id == employee_id,
                AttendanceRecord.date >= start_date,
                AttendanceRecord.date <= end_date,
                AttendanceRecord.overtime_hours > 0
            ).all()

            total_overtime_hours = sum(float(record.overtime_hours or 0) for record in records)
            total_compensation_days = total_overtime_hours / 8  # 8 hours = 1 day

            overtime_by_date = [
                {
                    "date": record.date.isoformat(),
                    "overtime_hours": float(record.overtime_hours or 0),
                    "total_hours": float(record.total_hours_worked or 0)
                }
                for record in records
            ]

            return {
                "employee_id": str(employee_id),
                "period": {
                    "start_date": start_date.isoformat(),
                    "end_date": end_date.isoformat()
                },
                "summary": {
                    "total_overtime_hours": total_overtime_hours,
                    "total_compensation_days": total_compensation_days,
                    "overtime_days_count": len(records)
                },
                "overtime_by_date": overtime_by_date
            }

        except Exception as e:
            logger.error(f"Error getting overtime summary: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Error retrieving overtime summary"
            )
