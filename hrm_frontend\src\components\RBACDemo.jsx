/**
 * RBAC Demo Component
 * Demonstrates the Role-Based Access Control system functionality
 * Shows different permission levels and role-based content
 */

import React from 'react';
import { useAuth } from '../contexts/AuthContext';
import { usePermissions, useMultiplePermissions, useRoleAccess } from '../hooks/usePermissions';
import { PermissionGate, RoleGate, ConditionalRender } from './ProtectedRoute';
import { Shield, Users, Settings, Eye, Edit, Trash2 } from 'lucide-react';

export default function RBACDemo() {
  const { user, userRole } = useAuth();
  const permissions = usePermissions();
  
  // Check multiple permissions at once
  const multiplePermissions = useMultiplePermissions([
    'employeeDirectory',
    'attendanceManagement',
    'payrollManagement',
    'systemSettings'
  ]);

  // Check role-based access
  const isAdmin = useRoleAccess(['super_admin', 'admin']);
  const isManager = useRoleAccess(['super_admin', 'admin', 'hr', 'manager']);

  return (
    <div className="p-6 space-y-8">
      {/* Header */}
      <div className="bg-white rounded-lg shadow p-6">
        <div className="flex items-center space-x-3 mb-4">
          <Shield className="text-blue-600" size={24} />
          <h1 className="text-2xl font-bold text-gray-900">RBAC System Demo</h1>
        </div>
        <p className="text-gray-600">
          This demo showcases the Role-Based Access Control system. Different content will be visible based on your current role.
        </p>
        
        {/* Current user info */}
        <div className="mt-4 p-4 bg-blue-50 rounded-lg">
          <h3 className="font-semibold text-blue-900">Current User</h3>
          <p className="text-blue-800">Name: {user?.name}</p>
          <p className="text-blue-800">Role: {userRole?.replace('_', ' ').toUpperCase()}</p>
          <p className="text-blue-800">Department: {user?.department}</p>
        </div>
      </div>

      {/* Permission Gates Demo */}
      <div className="bg-white rounded-lg shadow p-6">
        <h2 className="text-xl font-semibold mb-4">Permission Gates</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          
          <PermissionGate permission="employeeDirectory">
            <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
              <div className="flex items-center space-x-2">
                <Users className="text-green-600" size={20} />
                <span className="font-medium text-green-800">Employee Directory Access</span>
              </div>
              <p className="text-green-700 text-sm mt-1">You can view the employee directory</p>
            </div>
          </PermissionGate>

          <PermissionGate 
            permission="systemSettings" 
            fallback={
              <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
                <div className="flex items-center space-x-2">
                  <Settings className="text-red-600" size={20} />
                  <span className="font-medium text-red-800">System Settings - Access Denied</span>
                </div>
                <p className="text-red-700 text-sm mt-1">You don't have access to system settings</p>
              </div>
            }
          >
            <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
              <div className="flex items-center space-x-2">
                <Settings className="text-green-600" size={20} />
                <span className="font-medium text-green-800">System Settings Access</span>
              </div>
              <p className="text-green-700 text-sm mt-1">You can access system settings</p>
            </div>
          </PermissionGate>

        </div>
      </div>

      {/* Role Gates Demo */}
      <div className="bg-white rounded-lg shadow p-6">
        <h2 className="text-xl font-semibold mb-4">Role-Based Access</h2>
        <div className="space-y-4">
          
          <RoleGate roles={['super_admin', 'admin']}>
            <div className="p-4 bg-purple-50 border border-purple-200 rounded-lg">
              <h3 className="font-medium text-purple-800">Administrator Panel</h3>
              <p className="text-purple-700 text-sm">This content is only visible to Super Admins and Admins</p>
            </div>
          </RoleGate>

          <RoleGate roles={['super_admin', 'admin', 'hr', 'manager']}>
            <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
              <h3 className="font-medium text-blue-800">Management Panel</h3>
              <p className="text-blue-700 text-sm">This content is visible to management roles</p>
            </div>
          </RoleGate>

          <RoleGate roles="employee">
            <div className="p-4 bg-gray-50 border border-gray-200 rounded-lg">
              <h3 className="font-medium text-gray-800">Employee Panel</h3>
              <p className="text-gray-700 text-sm">This content is only for employees</p>
            </div>
          </RoleGate>

        </div>
      </div>

      {/* Conditional Rendering Demo */}
      <div className="bg-white rounded-lg shadow p-6">
        <h2 className="text-xl font-semibold mb-4">Conditional Rendering</h2>
        
        <ConditionalRender
          permission="attendanceManagement"
          fullAccess={
            <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
              <h3 className="font-medium text-green-800">Full Attendance Management</h3>
              <p className="text-green-700 text-sm">You have full access to all attendance data</p>
              <div className="flex space-x-2 mt-2">
                <button className="flex items-center space-x-1 px-3 py-1 bg-green-600 text-white rounded text-sm">
                  <Eye size={14} />
                  <span>View All</span>
                </button>
                <button className="flex items-center space-x-1 px-3 py-1 bg-green-600 text-white rounded text-sm">
                  <Edit size={14} />
                  <span>Edit All</span>
                </button>
                <button className="flex items-center space-x-1 px-3 py-1 bg-red-600 text-white rounded text-sm">
                  <Trash2 size={14} />
                  <span>Delete</span>
                </button>
              </div>
            </div>
          }
          teamAccess={
            <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
              <h3 className="font-medium text-yellow-800">Team Attendance Management</h3>
              <p className="text-yellow-700 text-sm">You can manage attendance for your team members only</p>
              <div className="flex space-x-2 mt-2">
                <button className="flex items-center space-x-1 px-3 py-1 bg-yellow-600 text-white rounded text-sm">
                  <Eye size={14} />
                  <span>View Team</span>
                </button>
                <button className="flex items-center space-x-1 px-3 py-1 bg-yellow-600 text-white rounded text-sm">
                  <Edit size={14} />
                  <span>Edit Team</span>
                </button>
              </div>
            </div>
          }
          noAccess={
            <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
              <h3 className="font-medium text-red-800">Attendance Management - No Access</h3>
              <p className="text-red-700 text-sm">You can only view your own attendance data</p>
              <div className="flex space-x-2 mt-2">
                <button className="flex items-center space-x-1 px-3 py-1 bg-gray-600 text-white rounded text-sm">
                  <Eye size={14} />
                  <span>View Own</span>
                </button>
              </div>
            </div>
          }
        />
      </div>

      {/* Permission Summary */}
      <div className="bg-white rounded-lg shadow p-6">
        <h2 className="text-xl font-semibold mb-4">Your Permissions Summary</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          
          {Object.entries(multiplePermissions).map(([permission, hasAccess]) => (
            <div 
              key={permission}
              className={`p-3 rounded-lg border ${
                hasAccess 
                  ? 'bg-green-50 border-green-200' 
                  : 'bg-red-50 border-red-200'
              }`}
            >
              <div className="flex items-center justify-between">
                <span className={`text-sm font-medium ${
                  hasAccess ? 'text-green-800' : 'text-red-800'
                }`}>
                  {permission.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}
                </span>
                <span className={`text-xs px-2 py-1 rounded ${
                  hasAccess 
                    ? 'bg-green-100 text-green-800' 
                    : 'bg-red-100 text-red-800'
                }`}>
                  {hasAccess ? 'Allowed' : 'Denied'}
                </span>
              </div>
            </div>
          ))}

        </div>
      </div>

      {/* Role Information */}
      <div className="bg-white rounded-lg shadow p-6">
        <h2 className="text-xl font-semibold mb-4">Role Information</h2>
        <div className="space-y-3">
          <div className="flex items-center justify-between p-3 bg-gray-50 rounded">
            <span className="font-medium">Is Administrator:</span>
            <span className={`px-2 py-1 rounded text-sm ${
              isAdmin ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
            }`}>
              {isAdmin ? 'Yes' : 'No'}
            </span>
          </div>
          
          <div className="flex items-center justify-between p-3 bg-gray-50 rounded">
            <span className="font-medium">Is Manager or Above:</span>
            <span className={`px-2 py-1 rounded text-sm ${
              isManager ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
            }`}>
              {isManager ? 'Yes' : 'No'}
            </span>
          </div>
          
          <div className="flex items-center justify-between p-3 bg-gray-50 rounded">
            <span className="font-medium">Current Role Level:</span>
            <span className="px-2 py-1 bg-blue-100 text-blue-800 rounded text-sm">
              {permissions.userRole?.replace('_', ' ').toUpperCase()}
            </span>
          </div>
        </div>
      </div>
    </div>
  );
}
