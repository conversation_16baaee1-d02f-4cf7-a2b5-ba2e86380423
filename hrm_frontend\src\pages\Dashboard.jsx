import React from 'react';
import { useAuth } from '../contexts/AuthContext';
import TopBanner from '../components/TopBanner';
import TimeTracker from '../components/TimeTracker';
import { PermissionGate } from '../components/ProtectedRoute';

export default function Dashboard() {
  const { user } = useAuth();

  return (
    <div className="space-y-6">
      {/* Top Banner */}
      <TopBanner user={user} />

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* Time Tracker - Takes full width on mobile, 1 column on large screens */}
        <div className="lg:col-span-1">
          <TimeTracker />
        </div>

        {/* Dashboard Cards - Takes remaining space */}
        <div className="lg:col-span-3">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
      <DashboardCard title="Birthday">
        <div className="flex items-center justify-center h-32 text-gray-500">No birthdays today</div>
      </DashboardCard>

      <DashboardCard title="New Hires">
        <div className="space-y-4">
          <EmployeeItem id="S7" name="<PERSON>" position="Assistant Manager - Marketing" phone="************" />

          <EmployeeItem id="S10" name="Lindon Smith" position="Team Member - Marketing" phone="************" />
        </div>
      </DashboardCard>

      <DashboardCard title="Favorites" count={4}>
        <div className="space-y-3">
          <FavoriteItem id="S11" name="Olivia Smith" phone="************" extension="14" />

          <FavoriteItem id="S5" name="Andrew Turner" phone="************" extension="7" />

          <FavoriteItem id="S3" name="Clarkson Walter" phone="************" extension="3" />

          <FavoriteItem id="S2" name="Lily Williams" phone="************" extension="2" />
        </div>
      </DashboardCard>

      <DashboardCard title="Quick Links" badge={3}>
        <div className="flex items-center justify-center h-32 text-gray-500">No quick links</div>
      </DashboardCard>

      <DashboardCard title="Announcements" badge={1}>
        <div className="p-3 border-b">
          <div className="font-medium">Welcome to AgnoConnect HRM</div>
          <div className="text-xs text-gray-500 mt-1">Yesterday 2:41 PM</div>
        </div>
      </DashboardCard>

      <DashboardCard title="Leave Report">
        <div className="space-y-2">
          <LeaveItem type="Casual Leave" days={12} />
          <LeaveItem type="Earned Leave" days={12} />
          <LeaveItem type="Leave Without Pay" days={0} />
          <LeaveItem type="Paternity Leave" days={0} />
          <LeaveItem type="Sabbatical Leave" days={0} />
          <LeaveItem type="Sick Leave" days={12} />
        </div>
      </DashboardCard>

      <DashboardCard title="Upcoming Holidays">
        <div className="flex items-center justify-center h-32 text-gray-500">No Data Found</div>
      </DashboardCard>

      <DashboardCard title="My Pending Tasks" count={0}>
        <div className="flex items-center justify-center h-32 text-gray-500">There are no tasks available</div>
      </DashboardCard>

      <DashboardCard title="My Files" count={0}>
        <div className="border-b flex">
          <button className="py-2 px-4 border-b-2 border-blue-600 text-blue-600 font-medium">Organization Files</button>
          <button className="py-2 px-4 text-gray-600">Employee Files</button>
        </div>
        <div className="flex items-center justify-center h-32 text-gray-500">No Files Found</div>
      </DashboardCard>

      <DashboardCard title="Work Anniversary">
        <div className="flex items-center justify-center h-32 text-gray-500">No work anniversaries today</div>
      </DashboardCard>

      <DashboardCard title="Wedding Anniversary">
        <div className="flex items-center justify-center h-32 text-gray-500">No wedding anniversaries today</div>
      </DashboardCard>

      <DashboardCard title="On Leave Today">
        <div className="flex items-center justify-center h-32 text-gray-500">
          None of your direct reportees are on leave today
        </div>
      </DashboardCard>

      <DashboardCard title="Employee Engagement" count={0} label="Pending">
        <div className="flex items-center justify-center h-32 text-gray-500">No pending surveys</div>
      </DashboardCard>
          </div>
        </div>
      </div>
    </div>
  )
}

function DashboardCard({ title, children, count, badge, label }) {
  return (
    <div className="bg-white rounded-lg shadow-sm overflow-hidden">
      <div className="p-4 border-b flex items-center justify-between">
        <h3 className="font-medium">{title}</h3>
        {count !== undefined && (
          <div className="bg-gray-100 px-2 py-0.5 rounded text-xs text-gray-700">
            {label ? `${label} ${count}` : count}
          </div>
        )}
        {badge !== undefined && (
          <div className="w-5 h-5 rounded-full bg-green-500 text-white flex items-center justify-center text-xs">
            {badge}
          </div>
        )}
      </div>
      <div className="p-4">{children}</div>
    </div>
  )
}

function EmployeeItem({ id, name, position, phone }) {
  return (
    <div className="flex gap-3">
      <div className="w-12 h-12 rounded overflow-hidden flex-shrink-0 bg-gray-400"></div>
      <div>
        <div className="flex items-center gap-2">
          <span className="text-gray-500 text-xs">{id}</span>
          <span className="font-medium">{name}</span>
        </div>
        <div className="text-sm text-gray-600 mt-1">{position}</div>
        <div className="flex items-center gap-1 mt-1 text-sm text-gray-600">
          <PhoneIcon className="w-3 h-3" />
          <span>{phone}</span>
        </div>
      </div>
    </div>
  )
}

function FavoriteItem({ id, name, phone, extension }) {
  return (
    <div className="flex gap-3">
      <div className="w-10 h-10 rounded-full overflow-hidden flex-shrink-0 bg-gray-400"></div>
      <div>
        <div className="flex items-center gap-2">
          <span className="text-gray-500 text-xs">{id}</span>
          <span className="font-medium">{name}</span>
        </div>
        <div className="flex items-center gap-1 mt-1">
          <PhoneIcon className="w-3 h-3 text-gray-500" />
          <span className="text-xs text-gray-600">{phone}</span>
          <span className="text-xs text-gray-500 ml-1">x {extension}</span>
        </div>
      </div>
    </div>
  )
}

function LeaveItem({ type, days }) {
  return (
    <div className="flex items-center justify-between py-1.5">
      <div className="flex items-center gap-2">
        <div className="w-5 h-5 rounded-full bg-orange-100 flex items-center justify-center text-orange-600 text-xs">
          0
        </div>
        <span className="text-sm">{type}</span>
      </div>
      <div className="text-xs text-gray-600">Available {days} Day(s)</div>
    </div>
  )
}

function PhoneIcon({ className }) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
      className={className}
    >
      <path d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z" />
    </svg>
  )
}
