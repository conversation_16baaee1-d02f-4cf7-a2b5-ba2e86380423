"""Create employee activity log table

Revision ID: 44f0934df832
Revises: 529ddb1e2ffb
Create Date: 2025-04-16 16:50:21.977245

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy import func, TIMESTAMP, ForeignKey
from sqlalchemy import Column, Integer, String, Float


# revision identifiers, used by Alembic.
revision: str = '44f0934df832'
down_revision: Union[str, None] = '529ddb1e2ffb'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    op.create_table(
        'employee_activities',
        <PERSON>umn('id', Integer, primary_key=True),
        <PERSON>umn('message', sa.String(length=100), nullable=True),
        <PERSON><PERSON><PERSON>('employee_id', Integer, ForeignKey('employees.id')),
        <PERSON><PERSON><PERSON>('created_at', sa.DateTime(timezone=True), nullable=False),
        <PERSON>umn('updated_at', sa.DateTime(timezone=True), nullable=False),
    )


def downgrade() -> None:
    op.drop_table('employee_activities')
