#!/usr/bin/env python3
"""
Quick test to verify basic functionality
"""

import sys
import os

# Add the app directory to Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'app'))

def test_database_connection():
    """Test database connection"""
    try:
        from app.db.session import test_connection
        result = test_connection()
        print(f"✅ Database connection: {result}")
        return result
    except Exception as e:
        print(f"❌ Database connection failed: {e}")
        return False

def test_basic_imports():
    """Test basic imports"""
    try:
        from app.core.config import settings
        print(f"✅ Config imported")
        
        from app.db.session import engine, SessionLocal
        print("✅ Database session imported")
        
        from app.db.base import BaseModel
        print("✅ Base model imported")
        
        return True
    except Exception as e:
        print(f"❌ Basic imports failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_model_imports():
    """Test model imports"""
    try:
        from app.db.models.user import User
        print("✅ User model imported")
        
        from app.db.models.employee import Employee, Department, Organization
        print("✅ Employee models imported")
        
        from app.db.models.ticket import Ticket
        print("✅ Ticket model imported")
        
        return True
    except Exception as e:
        print(f"❌ Model imports failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_table_creation():
    """Test table creation"""
    try:
        from app.db.session import create_tables
        create_tables()
        print("✅ Tables created successfully")
        return True
    except Exception as e:
        print(f"❌ Table creation failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main test function"""
    print("🚀 Running Quick Backend Tests")
    print("=" * 50)
    
    tests = [
        ("Database Connection", test_database_connection),
        ("Basic Imports", test_basic_imports),
        ("Model Imports", test_model_imports),
        ("Table Creation", test_table_creation)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n🔍 Testing: {test_name}")
        try:
            result = test_func()
            results.append(result)
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
            results.append(False)
    
    # Summary
    passed = sum(results)
    total = len(results)
    
    print(f"\n📊 Test Summary:")
    print(f"Passed: {passed}/{total}")
    print(f"Success Rate: {(passed/total)*100:.1f}%")
    
    if passed == total:
        print("🎉 All tests passed!")
        return True
    else:
        print("⚠️ Some tests failed!")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
