from core.databases.database import db
from core.models.basemodel import ModelBase
from sqlalchemy.orm import relationship
from core.models.employees import EmployeeModel
from datetime import datetime

class ZohoPeopleIntegrationAttendanceModel(ModelBase):
    __tablename__ = "attendance_zoho_people_integration"

    id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    first_name = db.Column(db.String(250), unique=True, nullable=True)
    last_name = db.Column(db.String(250), unique=True, nullable=True)
    email = db.Column(db.String(250), unique=True, nullable=True)
    gender = db.Column(db.String(250), unique=True, nullable=True)
    organization_profile = db.Column(db.String(250), unique=True, nullable=True)
    employee_status = db.Column(db.String(250), unique=True, nullable=True)
    employee_type = db.Column(db.String(250), unique=True, nullable=True)
    employee_id = db.Column(db.String(250), unique=True, nullable=True)
    bank_code = db.Column(db.String(250), unique=True, nullable=True)
    bank_name = db.Column(db.String(250), unique=True, nullable=True)
    account_no = db.Column(db.String(250), unique=True, nullable=True)
    department = db.Column(db.String(250), unique=True, nullable=True)
    designation = db.Column(db.String(250), unique=True, nullable=True)
    salary_templates = db.Column(db.String(250), unique=True, nullable=True)
    mobile_no  = db.Column(db.String(250), unique=True, nullable=True)
    tax_type  = db.Column(db.String(250), unique=True, nullable=True)
    business_unit  = db.Column(db.String(250), unique=True, nullable=True)
    # employee_type  = db.Column(db.String(250), unique=True, nullable=True)
    division =  db.Column(db.String(250), unique=True, nullable=True)
    location  =  db.Column(db.String(250), unique=True, nullable=True)
    gross_pay  = db.Column(db.String(250), unique=True, nullable=True)
    nhf_no =  db.Column(db.String(250), unique=True, nullable=True)
    nhf_mortgage_bank =  db.Column(db.String(250), unique=True, nullable=True)
    pension_pfa =  db.Column(db.String(250), unique=True, nullable=True)
    pfa_no =  db.Column(db.String(250), unique=True, nullable=True)
    currency =  db.Column(db.String(250), unique=True, nullable=True)
    annual_leave_days =  db.Column(db.String(250), unique=True, nullable=True)
    unpaid_leave_days =  db.Column(db.String(250), unique=True, nullable=True)
    sick_leave_days =  db.Column(db.String(250), unique=True, nullable=True)
    maternity_paternity_leave_days =  db.Column(db.String(250), unique=True, nullable=True)
    casual_leave_days =  db.Column(db.String(250), unique=True, nullable=True)
    compassionate_leave_days =  db.Column(db.String(250), unique=True, nullable=True)
    total_working_days =  db.Column(db.String(250), unique=True, nullable=True)
    total_present_days =  db.Column(db.String(250), unique=True, nullable=True)
    total_absent_days =  db.Column(db.String(250), unique=True, nullable=True)
    timestamp = db.Column(db.DateTime, nullable=False, default=datetime.now())
    








