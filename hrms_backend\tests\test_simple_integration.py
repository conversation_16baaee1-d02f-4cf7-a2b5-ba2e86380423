#!/usr/bin/env python3
"""
Simple Integration Test for Payroll System
Tests basic functionality without database dependencies
"""

import os
import sys

def test_basic_imports():
    """Test that basic modules can be imported"""
    print("🔍 Testing Basic Imports...")
    
    try:
        # Test core imports
        from app.models.payroll import PayrollRecord, SalaryComponent, Deduction
        print("  ✅ Payroll models imported successfully")
        
        from app.schemas.payroll import PayrollRecordCreate, PayrollRecordResponse
        print("  ✅ Payroll schemas imported successfully")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Basic imports failed: {e}")
        return False

def test_payroll_calculations():
    """Test payroll calculation logic"""
    print("🧮 Testing Payroll Calculations...")
    
    try:
        # Test basic salary calculations
        basic_salary = 200000
        housing_allowance = 40000
        transport_allowance = 20000
        
        gross_salary = basic_salary + housing_allowance + transport_allowance
        
        # Calculate deductions
        pension_rate = 0.08
        nhf_rate = 0.025
        
        pension = basic_salary * pension_rate
        nhf = basic_salary * nhf_rate
        
        # Simple tax calculation (basic rate)
        tax_rate = 0.15
        tax = gross_salary * tax_rate
        
        total_deductions = pension + nhf + tax
        net_salary = gross_salary - total_deductions
        
        print(f"  ✅ Gross salary calculation: ₦{gross_salary:,.2f}")
        print(f"  ✅ Total deductions: ₦{total_deductions:,.2f}")
        print(f"    - Pension: ₦{pension:,.2f}")
        print(f"    - NHF: ₦{nhf:,.2f}")
        print(f"    - Tax: ₦{tax:,.2f}")
        print(f"  ✅ Net salary: ₦{net_salary:,.2f}")
        
        # Validate calculations
        assert gross_salary > 0, "Gross salary should be positive"
        assert total_deductions > 0, "Total deductions should be positive"
        assert net_salary > 0, "Net salary should be positive"
        assert net_salary < gross_salary, "Net salary should be less than gross"
        
        return True
        
    except Exception as e:
        print(f"  ❌ Payroll calculations failed: {e}")
        return False

def test_tax_calculation():
    """Test tax calculation functionality"""
    print("💰 Testing Tax Calculation...")
    
    try:
        # Nigerian PAYE tax bands (simplified)
        def calculate_paye_tax(annual_income):
            # Tax bands for 2024
            bands = [
                (300000, 0.07),    # First ₦300,000 at 7%
                (300000, 0.11),    # Next ₦300,000 at 11%
                (500000, 0.15),    # Next ₦500,000 at 15%
                (500000, 0.19),    # Next ₦500,000 at 19%
                (1600000, 0.21),   # Next ₦1,600,000 at 21%
                (float('inf'), 0.24)  # Above ₦3,200,000 at 24%
            ]
            
            tax = 0
            remaining_income = annual_income
            
            for band_limit, rate in bands:
                if remaining_income <= 0:
                    break
                
                taxable_in_band = min(remaining_income, band_limit)
                tax += taxable_in_band * rate
                remaining_income -= taxable_in_band
            
            return tax
        
        # Test with different income levels
        test_incomes = [2400000, 3600000, 6000000]  # Annual incomes
        
        for annual_income in test_incomes:
            annual_tax = calculate_paye_tax(annual_income)
            monthly_tax = annual_tax / 12
            effective_rate = (annual_tax / annual_income) * 100
            
            print(f"  ✅ Income: ₦{annual_income:,.2f}")
            print(f"    - Annual Tax: ₦{annual_tax:,.2f}")
            print(f"    - Monthly Tax: ₦{monthly_tax:,.2f}")
            print(f"    - Effective Rate: {effective_rate:.2f}%")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Tax calculation failed: {e}")
        return False

def test_benefit_calculations():
    """Test benefit calculation logic"""
    print("🎁 Testing Benefit Calculations...")
    
    try:
        # Define benefits
        benefits = [
            {"name": "Health Insurance", "type": "fixed", "amount": 25000},
            {"name": "Performance Bonus", "type": "percentage", "percentage": 10},
            {"name": "Transport Allowance", "type": "fixed", "amount": 30000},
            {"name": "Meal Allowance", "type": "percentage", "percentage": 5}
        ]
        
        base_salary = 200000
        total_benefits = 0
        
        print(f"  Base Salary: ₦{base_salary:,.2f}")
        
        for benefit in benefits:
            if benefit["type"] == "fixed":
                benefit_amount = benefit["amount"]
            elif benefit["type"] == "percentage":
                benefit_amount = base_salary * (benefit["percentage"] / 100)
            else:
                benefit_amount = 0
            
            total_benefits += benefit_amount
            print(f"  ✅ {benefit['name']}: ₦{benefit_amount:,.2f}")
        
        print(f"  ✅ Total Benefits: ₦{total_benefits:,.2f}")
        
        assert total_benefits > 0, "Total benefits should be positive"
        
        return True
        
    except Exception as e:
        print(f"  ❌ Benefit calculations failed: {e}")
        return False

def test_loan_calculations():
    """Test loan calculation logic"""
    print("🏦 Testing Loan Calculations...")
    
    try:
        # Loan parameters
        principal = 500000
        annual_interest_rate = 12  # 12% per annum
        loan_term_months = 24
        
        # Calculate monthly payment using formula
        monthly_interest_rate = annual_interest_rate / 100 / 12
        
        if monthly_interest_rate > 0:
            monthly_payment = principal * (
                monthly_interest_rate * (1 + monthly_interest_rate) ** loan_term_months
            ) / (
                (1 + monthly_interest_rate) ** loan_term_months - 1
            )
        else:
            monthly_payment = principal / loan_term_months
        
        total_payment = monthly_payment * loan_term_months
        total_interest = total_payment - principal
        
        print(f"  ✅ Loan Details:")
        print(f"    - Principal: ₦{principal:,.2f}")
        print(f"    - Interest Rate: {annual_interest_rate}% per annum")
        print(f"    - Term: {loan_term_months} months")
        print(f"    - Monthly Payment: ₦{monthly_payment:,.2f}")
        print(f"    - Total Payment: ₦{total_payment:,.2f}")
        print(f"    - Total Interest: ₦{total_interest:,.2f}")
        
        assert monthly_payment > 0, "Monthly payment should be positive"
        assert total_interest >= 0, "Total interest should be non-negative"
        
        return True
        
    except Exception as e:
        print(f"  ❌ Loan calculations failed: {e}")
        return False

def test_working_days():
    """Test working days calculation"""
    print("📅 Testing Working Days Calculation...")
    
    try:
        from datetime import date, timedelta
        
        # Test for different months
        test_months = [
            (2024, 1),   # January 2024
            (2024, 2),   # February 2024 (leap year)
            (2024, 12),  # December 2024
        ]
        
        for year, month in test_months:
            # Get first and last day of month
            start_date = date(year, month, 1)
            if month == 12:
                end_date = date(year + 1, 1, 1) - timedelta(days=1)
            else:
                end_date = date(year, month + 1, 1) - timedelta(days=1)
            
            # Calculate working days
            total_days = (end_date - start_date).days + 1
            working_days = 0
            current_date = start_date
            
            while current_date <= end_date:
                if current_date.weekday() < 5:  # Monday = 0, Sunday = 6
                    working_days += 1
                current_date += timedelta(days=1)
            
            weekend_days = total_days - working_days
            
            print(f"  ✅ {start_date.strftime('%B %Y')}:")
            print(f"    - Total days: {total_days}")
            print(f"    - Working days: {working_days}")
            print(f"    - Weekend days: {weekend_days}")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Working days calculation failed: {e}")
        return False

def test_proration_logic():
    """Test salary proration logic"""
    print("⏰ Testing Proration Logic...")
    
    try:
        from datetime import date
        
        # Test proration scenarios
        scenarios = [
            {
                "name": "Mid-month start",
                "monthly_salary": 200000,
                "start_date": date(2024, 1, 15),
                "end_date": date(2024, 1, 31),
                "total_days": 31
            },
            {
                "name": "Early termination",
                "monthly_salary": 300000,
                "start_date": date(2024, 2, 1),
                "end_date": date(2024, 2, 20),
                "total_days": 29  # February 2024 (leap year)
            }
        ]
        
        for scenario in scenarios:
            days_worked = (scenario["end_date"] - scenario["start_date"]).days + 1
            proration_factor = days_worked / scenario["total_days"]
            prorated_salary = scenario["monthly_salary"] * proration_factor
            
            print(f"  ✅ {scenario['name']}:")
            print(f"    - Full Monthly Salary: ₦{scenario['monthly_salary']:,.2f}")
            print(f"    - Days Worked: {days_worked}/{scenario['total_days']}")
            print(f"    - Proration Factor: {proration_factor:.4f}")
            print(f"    - Prorated Salary: ₦{prorated_salary:,.2f}")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Proration logic failed: {e}")
        return False

def main():
    """Run all simple integration tests"""
    print("🚀 Starting Simple Payroll Integration Tests")
    print("=" * 50)
    
    tests = [
        ("Basic Imports", test_basic_imports),
        ("Payroll Calculations", test_payroll_calculations),
        ("Tax Calculation", test_tax_calculation),
        ("Benefit Calculations", test_benefit_calculations),
        ("Loan Calculations", test_loan_calculations),
        ("Working Days", test_working_days),
        ("Proration Logic", test_proration_logic)
    ]
    
    passed = 0
    failed = 0
    
    for test_name, test_func in tests:
        print(f"\n📋 Running {test_name} tests...")
        try:
            if test_func():
                print(f"✅ {test_name} tests PASSED")
                passed += 1
            else:
                print(f"❌ {test_name} tests FAILED")
                failed += 1
        except Exception as e:
            print(f"❌ {test_name} tests FAILED with exception: {e}")
            failed += 1
    
    total_tests = passed + failed
    success_rate = (passed / total_tests * 100) if total_tests > 0 else 0
    
    print("\n" + "=" * 50)
    print("📊 Test Results Summary:")
    print(f"  ✅ Passed: {passed}")
    print(f"  ❌ Failed: {failed}")
    print(f"  📈 Success Rate: {success_rate:.1f}%")
    
    if failed > 0:
        print(f"\n⚠️  {failed} test(s) FAILED. Please review the errors above.")
        return 1
    else:
        print(f"\n🎉 All tests PASSED! Payroll integration is working correctly.")
        return 0

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
