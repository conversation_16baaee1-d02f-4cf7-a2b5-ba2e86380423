#!/usr/bin/env python3
"""
Check table schemas for ticket_comments and ticket_activities
"""

import sys
import os

# Add the app directory to Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'app'))

from sqlalchemy import text
from app.db.session import engine

def check_table_schemas():
    """Check table schemas"""
    try:
        with engine.connect() as conn:
            print('=== TICKET_COMMENTS TABLE SCHEMA ===')
            result = conn.execute(text("""
                SELECT column_name, data_type, is_nullable 
                FROM information_schema.columns 
                WHERE table_name = 'ticket_comments' AND table_schema = 'public'
                ORDER BY ordinal_position
            """))
            
            for row in result.fetchall():
                nullable = 'NULL' if row[2] == 'YES' else 'NOT NULL'
                print(f'  {row[0]} ({row[1]}) {nullable}')
            
            print('\n=== TICKET_ACTIVITIES TABLE SCHEMA ===')
            result = conn.execute(text("""
                SELECT column_name, data_type, is_nullable 
                FROM information_schema.columns 
                WHERE table_name = 'ticket_activities' AND table_schema = 'public'
                ORDER BY ordinal_position
            """))
            
            for row in result.fetchall():
                nullable = 'NULL' if row[2] == 'YES' else 'NOT NULL'
                print(f'  {row[0]} ({row[1]}) {nullable}')
                
    except Exception as e:
        print(f"Error checking schemas: {e}")

if __name__ == "__main__":
    check_table_schemas()
