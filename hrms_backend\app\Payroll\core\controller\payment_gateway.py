from flask_smorest import Blueprint, abort
from core.middleware import roles_required
from flask.views import MethodView
import requests
from sqlalchemy import func
from ..repositories.user import UserRepository
from flask import url_for, jsonify,request,redirect,session,redirect,url_for,jsonify,render_template,flash
from core.services.payment_gateway_service import PaymentGatewayService
from core.models.payment_gateway import PaymentGatewayModel
from schemas import PaymentGatewaySchema


blueprint = Blueprint("Payment Gateway", __name__, description="Hold the list of all the paymet gateway")



@blueprint.route("/payment_gateway/")
class PaymentGate(MethodView):

    def __init__(self):
        self.payment_gateway_service = PaymentGatewayService()
        self.payment_gateway_schema = PaymentGatewaySchema()
    
    @roles_required(['admin'])
    # @blueprint.response(200, PaymentGate)
    def post(self):
        data = request.get_json()

        user = UserRepository().authUser()#Gets the current login user data
        user_id=user.id
        payment_gateway =   data['payment_gateway']
        payment_gateway = payment_gateway.lower()

        from sqlalchemy import func

        # Filter payment gateways by user_id and case-insensitive match for payment_gateway_name
        checker_payment_gateway = PaymentGatewayModel.query.filter(
            PaymentGatewayModel.user_id == user_id,
            func.lower(PaymentGatewayModel.payment_gateway_name) == payment_gateway.lower()
        ).all()

        if checker_payment_gateway:
            checker_payment_gateway = self.payment_gateway_schema.dump(checker_payment_gateway)

            return jsonify({"payment gateway":checker_payment_gateway,
                            "message":"payment gateway already created"})


        data_payment_gateway = {
                'payment_gateway_name':payment_gateway
        }
        res = self.payment_gateway_service.create_payment_gateway(data_payment_gateway)
        if res :
            # Return the created resource and a 201 Created status code
            return jsonify({"message": "Resource created", "resource": res}), 201
        
        return res
    

    @roles_required(['admin'])
    # @blueprint.response(200, PaymentGate)
    def get(self):
        
        user = UserRepository().authUser()#Gets the current login user data
        if not user:
            return jsonify({"user":"User not Authenticated"})
        
        user_id=user.id
        payment_gateway_data = PaymentGatewayModel.query.filter_by(user_id=user_id).all()
       

        if payment_gateway_data:
            return jsonify({
                "payment_gateways": [
                    {"id": gateway.id, "user_id":gateway.user_id,"payment_gateway_name": gateway.payment_gateway_name.title()}
                    for gateway in payment_gateway_data
                ]
            }), 200
        else:
            return jsonify({"message": "No payment gateways found"}), 

    @roles_required(['admin'])
    # @blueprint.response(200, PaymentGate)
    def delete(self):
        pass





        

   