from core.models.paystack_credential import Paystack_IntegrationModel
from core.databases.database import db
from sqlalchemy import desc
import traceback

class PaystackIntegrationRepository:
    def __init__(self) -> None:
        pass

    @classmethod
    def createIntegration(self, user_id,paystack_key):
        paystack_integration =  Paystack_IntegrationModel(
            user_id=user_id,
            paystack_key=paystack_key
          
        )

        db.session.add(paystack_integration)
        db.session.commit()
        return  paystack_integration

    @classmethod
    def fetchAll(self):
        return  Paystack_IntegrationModel.query.order_by( Paystack_IntegrationModel.created_at.desc()).all()

    @classmethod
    def getIntegrationById(self, id):
        return Paystack_IntegrationModel.query.filter(Paystack_IntegrationModel.id == id).first()

    @classmethod
    def getIntegrationByClientId(self, client_id):
        return Paystack_IntegrationModel.query.filter_by(client_id=client_id).first()

    @classmethod
    def updateIntegration(self, id, **kwargs):
        integration = Paystack_IntegrationModel.query.filter_by(id=id).first()
        if integration:
            for key, value in kwargs.items():
                setattr(integration, key, value)
            db.session.commit()
            return integration
        else:
            return None

    @classmethod
    def deleteIntegration(self, id):
        Paystack_IntegrationModel.query.filter(Paystack_IntegrationModel.id == id).delete()
        db.session.commit()
        return
    