from sqlalchemy.orm import Session, joinedload
from sqlalchemy import and_, or_, func, case, extract
from typing import Optional, List, Dict, Any
from uuid import UUID
from datetime import datetime, timedelta
from fastapi import HTTPException, status
import logging

from ...db.models.ticket import (
    Ticket, TicketComment, TicketActivity, TicketEscalation,
    TicketStatus, TicketPriority, TicketType
)
from ...db.models.employee import Employee, Department
from ...core.security import CurrentUser

logger = logging.getLogger(__name__)


class TicketReportingService:
    """Service for ticket reporting and analytics"""

    async def get_ticket_overview(
        self,
        db: Session,
        organization_id: UUID,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None
    ) -> Dict[str, Any]:
        """Get ticket overview metrics"""
        try:
            if not start_date:
                start_date = datetime.utcnow() - timedelta(days=30)
            if not end_date:
                end_date = datetime.utcnow()

            # Base query
            base_query = db.query(Ticket).filter(
                Ticket.organization_id == organization_id,
                Ticket.created_at >= start_date,
                Ticket.created_at <= end_date
            )

            # Basic counts
            total_tickets = base_query.count()
            open_tickets = base_query.filter(Ticket.status == TicketStatus.OPEN).count()
            in_progress_tickets = base_query.filter(Ticket.status == TicketStatus.IN_PROGRESS).count()
            pending_tickets = base_query.filter(Ticket.status == TicketStatus.PENDING).count()
            resolved_tickets = base_query.filter(Ticket.status == TicketStatus.RESOLVED).count()
            closed_tickets = base_query.filter(Ticket.status == TicketStatus.CLOSED).count()

            # SLA metrics
            sla_breached = base_query.filter(Ticket.sla_breach == True).count()
            sla_compliance_rate = 0
            if total_tickets > 0:
                sla_compliance_rate = ((total_tickets - sla_breached) / total_tickets) * 100

            # Resolution time metrics
            resolved_tickets_with_time = base_query.filter(
                Ticket.status == TicketStatus.RESOLVED,
                Ticket.resolved_at.isnot(None)
            ).all()

            avg_resolution_hours = 0
            if resolved_tickets_with_time:
                total_resolution_time = sum([
                    (ticket.resolved_at - ticket.created_at).total_seconds() / 3600
                    for ticket in resolved_tickets_with_time
                ])
                avg_resolution_hours = total_resolution_time / len(resolved_tickets_with_time)

            return {
                "period": {
                    "start_date": start_date.isoformat(),
                    "end_date": end_date.isoformat()
                },
                "summary": {
                    "total_tickets": total_tickets,
                    "open_tickets": open_tickets,
                    "in_progress_tickets": in_progress_tickets,
                    "pending_tickets": pending_tickets,
                    "resolved_tickets": resolved_tickets,
                    "closed_tickets": closed_tickets,
                    "sla_breached": sla_breached,
                    "sla_compliance_rate": round(sla_compliance_rate, 2),
                    "avg_resolution_hours": round(avg_resolution_hours, 2)
                }
            }

        except Exception as e:
            logger.error(f"Error getting ticket overview: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Error retrieving ticket overview"
            )

    async def get_ticket_trends(
        self,
        db: Session,
        organization_id: UUID,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        group_by: str = "day"
    ) -> Dict[str, Any]:
        """Get ticket trends over time"""
        try:
            if not start_date:
                start_date = datetime.utcnow() - timedelta(days=30)
            if not end_date:
                end_date = datetime.utcnow()

            # Determine grouping
            if group_by == "hour":
                date_trunc = func.date_trunc('hour', Ticket.created_at)
            elif group_by == "day":
                date_trunc = func.date_trunc('day', Ticket.created_at)
            elif group_by == "week":
                date_trunc = func.date_trunc('week', Ticket.created_at)
            elif group_by == "month":
                date_trunc = func.date_trunc('month', Ticket.created_at)
            else:
                date_trunc = func.date_trunc('day', Ticket.created_at)

            # Query for ticket creation trends
            creation_trends = db.query(
                date_trunc.label('period'),
                func.count(Ticket.id).label('count')
            ).filter(
                Ticket.organization_id == organization_id,
                Ticket.created_at >= start_date,
                Ticket.created_at <= end_date
            ).group_by(date_trunc).order_by(date_trunc).all()

            # Query for ticket resolution trends
            resolution_trends = db.query(
                func.date_trunc(group_by, Ticket.resolved_at).label('period'),
                func.count(Ticket.id).label('count')
            ).filter(
                Ticket.organization_id == organization_id,
                Ticket.resolved_at >= start_date,
                Ticket.resolved_at <= end_date,
                Ticket.resolved_at.isnot(None)
            ).group_by(func.date_trunc(group_by, Ticket.resolved_at)).order_by(func.date_trunc(group_by, Ticket.resolved_at)).all()

            return {
                "period": {
                    "start_date": start_date.isoformat(),
                    "end_date": end_date.isoformat(),
                    "group_by": group_by
                },
                "creation_trends": [
                    {
                        "period": trend.period.isoformat(),
                        "count": trend.count
                    }
                    for trend in creation_trends
                ],
                "resolution_trends": [
                    {
                        "period": trend.period.isoformat(),
                        "count": trend.count
                    }
                    for trend in resolution_trends
                ]
            }

        except Exception as e:
            logger.error(f"Error getting ticket trends: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Error retrieving ticket trends"
            )

    async def get_ticket_breakdown(
        self,
        db: Session,
        organization_id: UUID,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None
    ) -> Dict[str, Any]:
        """Get ticket breakdown by various dimensions"""
        try:
            if not start_date:
                start_date = datetime.utcnow() - timedelta(days=30)
            if not end_date:
                end_date = datetime.utcnow()

            base_query = db.query(Ticket).filter(
                Ticket.organization_id == organization_id,
                Ticket.created_at >= start_date,
                Ticket.created_at <= end_date
            )

            # Priority breakdown
            priority_breakdown = {}
            for priority in TicketPriority:
                count = base_query.filter(Ticket.priority == priority).count()
                priority_breakdown[priority.value] = count

            # Type breakdown
            type_breakdown = {}
            for ticket_type in TicketType:
                count = base_query.filter(Ticket.ticket_type == ticket_type).count()
                type_breakdown[ticket_type.value] = count

            # Status breakdown
            status_breakdown = {}
            for status in TicketStatus:
                count = base_query.filter(Ticket.status == status).count()
                status_breakdown[status.value] = count

            # Department breakdown
            department_breakdown = db.query(
                Department.name.label('department'),
                func.count(Ticket.id).label('count')
            ).join(Ticket, Ticket.department_id == Department.id).filter(
                Ticket.organization_id == organization_id,
                Ticket.created_at >= start_date,
                Ticket.created_at <= end_date
            ).group_by(Department.name).all()

            # Category breakdown
            category_breakdown = db.query(
                Ticket.category.label('category'),
                func.count(Ticket.id).label('count')
            ).filter(
                Ticket.organization_id == organization_id,
                Ticket.created_at >= start_date,
                Ticket.created_at <= end_date,
                Ticket.category.isnot(None)
            ).group_by(Ticket.category).all()

            return {
                "period": {
                    "start_date": start_date.isoformat(),
                    "end_date": end_date.isoformat()
                },
                "priority_breakdown": priority_breakdown,
                "type_breakdown": type_breakdown,
                "status_breakdown": status_breakdown,
                "department_breakdown": [
                    {"department": dept.department, "count": dept.count}
                    for dept in department_breakdown
                ],
                "category_breakdown": [
                    {"category": cat.category, "count": cat.count}
                    for cat in category_breakdown
                ]
            }

        except Exception as e:
            logger.error(f"Error getting ticket breakdown: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Error retrieving ticket breakdown"
            )

    async def get_agent_performance(
        self,
        db: Session,
        organization_id: UUID,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None
    ) -> Dict[str, Any]:
        """Get agent performance metrics"""
        try:
            if not start_date:
                start_date = datetime.utcnow() - timedelta(days=30)
            if not end_date:
                end_date = datetime.utcnow()

            # Get agents with ticket assignments
            agent_stats = db.query(
                Employee.id.label('agent_id'),
                Employee.first_name.label('first_name'),
                Employee.last_name.label('last_name'),
                Employee.email.label('email'),
                func.count(Ticket.id).label('total_assigned'),
                func.sum(case([(Ticket.status == TicketStatus.RESOLVED, 1)], else_=0)).label('resolved'),
                func.sum(case([(Ticket.sla_breach == True, 1)], else_=0)).label('sla_breached'),
                func.avg(
                    case([
                        (Ticket.resolved_at.isnot(None),
                         extract('epoch', Ticket.resolved_at - Ticket.created_at) / 3600)
                    ])
                ).label('avg_resolution_hours')
            ).join(Ticket, Ticket.assigned_to == Employee.id).filter(
                Employee.organization_id == organization_id,
                Ticket.created_at >= start_date,
                Ticket.created_at <= end_date
            ).group_by(
                Employee.id, Employee.first_name, Employee.last_name, Employee.email
            ).all()

            agent_performance = []
            for agent in agent_stats:
                resolution_rate = 0
                if agent.total_assigned > 0:
                    resolution_rate = (agent.resolved / agent.total_assigned) * 100

                sla_compliance_rate = 0
                if agent.total_assigned > 0:
                    sla_compliance_rate = ((agent.total_assigned - (agent.sla_breached or 0)) / agent.total_assigned) * 100

                agent_performance.append({
                    "agent_id": str(agent.agent_id),
                    "name": f"{agent.first_name} {agent.last_name}",
                    "email": agent.email,
                    "total_assigned": agent.total_assigned,
                    "resolved": agent.resolved or 0,
                    "resolution_rate": round(resolution_rate, 2),
                    "sla_breached": agent.sla_breached or 0,
                    "sla_compliance_rate": round(sla_compliance_rate, 2),
                    "avg_resolution_hours": round(agent.avg_resolution_hours or 0, 2)
                })

            # Sort by resolution rate
            agent_performance.sort(key=lambda x: x["resolution_rate"], reverse=True)

            return {
                "period": {
                    "start_date": start_date.isoformat(),
                    "end_date": end_date.isoformat()
                },
                "agent_performance": agent_performance
            }

        except Exception as e:
            logger.error(f"Error getting agent performance: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Error retrieving agent performance"
            )

    async def get_satisfaction_metrics(
        self,
        db: Session,
        organization_id: UUID,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None
    ) -> Dict[str, Any]:
        """Get customer satisfaction metrics"""
        try:
            if not start_date:
                start_date = datetime.utcnow() - timedelta(days=30)
            if not end_date:
                end_date = datetime.utcnow()

            # Get satisfaction ratings
            satisfaction_stats = db.query(
                func.count(Ticket.id).label('total_rated'),
                func.avg(Ticket.satisfaction_rating).label('avg_rating'),
                func.sum(case([(Ticket.satisfaction_rating >= 4, 1)], else_=0)).label('positive_ratings'),
                func.sum(case([(Ticket.satisfaction_rating <= 2, 1)], else_=0)).label('negative_ratings')
            ).filter(
                Ticket.organization_id == organization_id,
                Ticket.created_at >= start_date,
                Ticket.created_at <= end_date,
                Ticket.satisfaction_rating.isnot(None)
            ).first()

            # Rating distribution
            rating_distribution = db.query(
                Ticket.satisfaction_rating.label('rating'),
                func.count(Ticket.id).label('count')
            ).filter(
                Ticket.organization_id == organization_id,
                Ticket.created_at >= start_date,
                Ticket.created_at <= end_date,
                Ticket.satisfaction_rating.isnot(None)
            ).group_by(Ticket.satisfaction_rating).order_by(Ticket.satisfaction_rating).all()

            satisfaction_rate = 0
            if satisfaction_stats.total_rated > 0:
                satisfaction_rate = (satisfaction_stats.positive_ratings / satisfaction_stats.total_rated) * 100

            return {
                "period": {
                    "start_date": start_date.isoformat(),
                    "end_date": end_date.isoformat()
                },
                "summary": {
                    "total_rated": satisfaction_stats.total_rated or 0,
                    "avg_rating": round(satisfaction_stats.avg_rating or 0, 2),
                    "satisfaction_rate": round(satisfaction_rate, 2),
                    "positive_ratings": satisfaction_stats.positive_ratings or 0,
                    "negative_ratings": satisfaction_stats.negative_ratings or 0
                },
                "rating_distribution": [
                    {"rating": rating.rating, "count": rating.count}
                    for rating in rating_distribution
                ]
            }

        except Exception as e:
            logger.error(f"Error getting satisfaction metrics: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Error retrieving satisfaction metrics"
            )

    async def get_escalation_report(
        self,
        db: Session,
        organization_id: UUID,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None
    ) -> Dict[str, Any]:
        """Get escalation report"""
        try:
            if not start_date:
                start_date = datetime.utcnow() - timedelta(days=30)
            if not end_date:
                end_date = datetime.utcnow()

            # Escalation summary
            escalation_stats = db.query(
                func.count(TicketEscalation.id).label('total_escalations'),
                func.sum(case([(TicketEscalation.escalated_by.is_(None), 1)], else_=0)).label('auto_escalations'),
                func.avg(
                    case([
                        (TicketEscalation.resolved_at.isnot(None),
                         extract('epoch', TicketEscalation.resolved_at - TicketEscalation.escalated_at) / 3600)
                    ])
                ).label('avg_resolution_hours')
            ).join(Ticket).filter(
                Ticket.organization_id == organization_id,
                TicketEscalation.escalated_at >= start_date,
                TicketEscalation.escalated_at <= end_date
            ).first()

            # Escalation level breakdown
            level_breakdown = db.query(
                TicketEscalation.escalation_level.label('level'),
                func.count(TicketEscalation.id).label('count')
            ).join(Ticket).filter(
                Ticket.organization_id == organization_id,
                TicketEscalation.escalated_at >= start_date,
                TicketEscalation.escalated_at <= end_date
            ).group_by(TicketEscalation.escalation_level).order_by(TicketEscalation.escalation_level).all()

            return {
                "period": {
                    "start_date": start_date.isoformat(),
                    "end_date": end_date.isoformat()
                },
                "summary": {
                    "total_escalations": escalation_stats.total_escalations or 0,
                    "auto_escalations": escalation_stats.auto_escalations or 0,
                    "manual_escalations": (escalation_stats.total_escalations or 0) - (escalation_stats.auto_escalations or 0),
                    "avg_resolution_hours": round(escalation_stats.avg_resolution_hours or 0, 2)
                },
                "level_breakdown": [
                    {"level": level.level, "count": level.count}
                    for level in level_breakdown
                ]
            }

        except Exception as e:
            logger.error(f"Error getting escalation report: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Error retrieving escalation report"
            )
