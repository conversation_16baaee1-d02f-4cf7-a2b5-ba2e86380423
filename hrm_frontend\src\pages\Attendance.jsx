/**
 * Attendance Page with RBAC Integration
 * Displays attendance tracking with role-based access control
 */

import React, { useState, useEffect } from 'react';
import { Calendar, Clock, CheckCircle, XCircle, Users, Download, Play, Pause, Filter, MoreHorizontal, Grid, List } from 'lucide-react';
import { usePermissions } from '../hooks/usePermissions';
import { PermissionGate, ConditionalRender } from '../components/ProtectedRoute';

export default function Attendance({ activeTab = 'my-attendance' }) {
  const permissions = usePermissions();
  const [selectedDate, setSelectedDate] = useState(new Date().toISOString().split('T')[0]);
  const [workTimer, setWorkTimer] = useState(0);
  const [isCheckedIn, setIsCheckedIn] = useState(false);
  const [viewMode, setViewMode] = useState('timeline'); // 'timeline', 'grid', 'list'
  const [checkoutNotes, setCheckoutNotes] = useState('');

  // Real-time timer effect
  useEffect(() => {
    let interval;
    if (isCheckedIn) {
      interval = setInterval(() => {
        setWorkTimer(prev => prev + 1);
      }, 1000);
    }
    return () => clearInterval(interval);
  }, [isCheckedIn]);

  // Format timer display
  const formatTimer = (seconds) => {
    const hrs = String(Math.floor(seconds / 3600)).padStart(2, '0');
    const mins = String(Math.floor((seconds % 3600) / 60)).padStart(2, '0');
    const secs = String(seconds % 60).padStart(2, '0');
    return `${hrs}:${mins}:${secs}`;
  };

  // Enhanced attendance data with timeline view
  const attendanceData = [
    { day: 'Sun', date: '01', status: 'Weekend', color: 'bg-orange-400', hours: '00:00' },
    { day: 'Mon', date: '02', status: 'Present', color: 'bg-green-400', checkIn: '11:08 AM', checkOut: '10:46 PM', hours: '11:38' },
    { day: 'Tue', date: '03', status: 'Absent', color: 'bg-red-400', hours: '00:00' },
    { day: 'Wed', date: '04', status: 'Present', color: 'bg-green-400', checkIn: '11:08 AM', checkOut: '10:46 PM', hours: '11:38' },
    { day: 'Thu', date: '05', status: 'Absent', color: 'bg-red-400', hours: '00:00' },
    { day: 'Fri', date: '06', status: 'Present', color: 'bg-green-400', checkIn: '10:08 AM', checkOut: '09:00 PM', hours: '10:52' },
    { day: 'Sat', date: '07', status: 'Weekend', color: 'bg-orange-400', hours: '00:00' },
  ];

  const summaryItems = [
    { label: 'Payable Days', value: '3 Days', color: 'border-l-yellow-400' },
    { label: 'Present', value: '1 Days', color: 'border-l-green-400' },
    { label: 'On Duty', value: '0 Days', color: 'border-l-purple-400' },
    { label: 'Paid leave', value: '0 Days', color: 'border-l-orange-400' },
    { label: 'Holidays', value: '0 Days', color: 'border-l-blue-400' },
    { label: 'Weekend', value: '2 Days', color: 'border-l-gray-400' },
  ];

  // Mock attendance data
  const myAttendance = [
    { date: '2024-01-15', checkIn: '09:00', checkOut: '18:00', status: 'present', hours: 9 },
    { date: '2024-01-14', checkIn: '09:15', checkOut: '18:30', status: 'late', hours: 9.25 },
    { date: '2024-01-13', checkIn: null, checkOut: null, status: 'absent', hours: 0 },
    { date: '2024-01-12', checkIn: '08:45', checkOut: '17:45', status: 'present', hours: 9 }
  ];

  const teamAttendance = [
    { id: 1, name: 'John Doe', status: 'present', checkIn: '09:00', department: 'Engineering' },
    { id: 2, name: 'Jane Smith', status: 'late', checkIn: '09:30', department: 'Marketing' },
    { id: 3, name: 'Mike Johnson', status: 'absent', checkIn: null, department: 'HR' }
  ];

  const renderMyAttendanceTab = () => (
    <div className="space-y-6">
      {/* Enhanced Header with Controls */}
      <div className="bg-gray-50 p-4 rounded-lg shadow-sm">
        <div className="flex justify-between items-center mb-4">
          <div className="flex items-center gap-2">
            <Calendar size={20} className="text-blue-600" />
            <span className="font-bold text-gray-900">01-Jun-2025 - 07-Jun-2025</span>
          </div>

          <div className="flex items-center gap-2">
            <button
              onClick={() => setViewMode('timeline')}
              className={`p-2 rounded ${viewMode === 'timeline' ? 'agno-bg-orange text-white' : 'bg-white text-gray-600'}`}
            >
              <Clock size={16} />
            </button>
            <button
              onClick={() => setViewMode('grid')}
              className={`p-2 rounded ${viewMode === 'grid' ? 'agno-bg-orange text-white' : 'bg-white text-gray-600'}`}
            >
              <Grid size={16} />
            </button>
            <button
              onClick={() => setViewMode('list')}
              className={`p-2 rounded ${viewMode === 'list' ? 'agno-bg-orange text-white' : 'bg-white text-gray-600'}`}
            >
              <List size={16} />
            </button>
            <button className="p-2 rounded bg-white text-gray-600">
              <Filter size={16} />
            </button>
            <button className="p-2 rounded bg-white text-gray-600">
              <MoreHorizontal size={16} />
            </button>
          </div>
        </div>

        {/* Check-in/Check-out Controls */}
        <div className="bg-white rounded-lg p-4 flex justify-between items-center">
          <span className="font-bold text-gray-900">General [ 9:00 AM - 6:00 PM ]</span>
          <div className="flex items-center gap-4 flex-1 mx-4">
            <input
              type="text"
              placeholder="Add notes for check-out"
              value={checkoutNotes}
              onChange={(e) => setCheckoutNotes(e.target.value)}
              className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-accent-500"
            />
          </div>
          <button
            onClick={() => setIsCheckedIn(!isCheckedIn)}
            className={`px-4 py-2 rounded-lg flex items-center gap-2 font-medium ${
              isCheckedIn
                ? 'bg-red-600 text-white hover:bg-red-700'
                : 'agno-bg-orange text-white hover:bg-accent-600'
            }`}
          >
            {isCheckedIn ? <Pause size={16} /> : <Play size={16} />}
            {isCheckedIn ? `Check-out ${formatTimer(workTimer)} Hrs` : 'Check-in'}
          </button>
        </div>
      </div>

      {/* Conditional Rendering Based on View Mode */}
      {viewMode === 'timeline' ? (
        <AttendanceTimeline attendanceData={attendanceData} />
      ) : (
        <>
          {/* Quick stats */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <StatCard
              title="Present Days"
              value="22"
              subtitle="This month"
              icon={<CheckCircle className="text-green-500" size={24} />}
              color="green"
            />
            <StatCard
              title="Late Days"
              value="3"
              subtitle="This month"
              icon={<Clock className="text-yellow-500" size={24} />}
              color="yellow"
            />
            <StatCard
              title="Absent Days"
              value="1"
              subtitle="This month"
              icon={<XCircle className="text-red-500" size={24} />}
              color="red"
            />
            <StatCard
              title="Total Hours"
              value="176"
              subtitle="This month"
              icon={<Clock className="text-blue-500" size={24} />}
              color="blue"
            />
          </div>
        </>
      )}

      {viewMode !== 'timeline' && (
        <>
          {/* Attendance history */}
          <div className="bg-white rounded-lg shadow">
            <div className="px-6 py-4 border-b border-gray-200">
              <h3 className="text-lg font-semibold">Recent Attendance</h3>
            </div>
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Date</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Check In</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Check Out</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Hours</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Status</th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-200">
                  {myAttendance.map((record, index) => (
                    <tr key={index}>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {new Date(record.date).toLocaleDateString()}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {record.checkIn || '-'}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {record.checkOut || '-'}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {record.hours}h
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <StatusBadge status={record.status} />
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </>
      )}

      {/* Summary Footer */}
      <AttendanceSummaryFooter summaryItems={summaryItems} />
    </div>
  );

  const renderTeamAttendanceTab = () => (
    <PermissionGate permission="attendanceManagement">
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Team Attendance</h1>
            <ConditionalRender
              permission="attendanceManagement"
              fullAccess={<p className="text-gray-600">Monitor attendance for all employees</p>}
              teamAccess={<p className="text-gray-600">Monitor attendance for your team members</p>}
            />
          </div>
          
          <div className="flex items-center space-x-4">
            <input
              type="date"
              value={selectedDate}
              onChange={(e) => setSelectedDate(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
            />
            <button className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center gap-2">
              <Download size={16} />
              Export Report
            </button>
          </div>
        </div>

        {/* Team attendance overview */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <StatCard
            title="Present Today"
            value="15"
            subtitle="out of 18 employees"
            icon={<Users className="text-green-500" size={24} />}
            color="green"
          />
          <StatCard
            title="Late Today"
            value="2"
            subtitle="employees"
            icon={<Clock className="text-yellow-500" size={24} />}
            color="yellow"
          />
          <StatCard
            title="Absent Today"
            value="1"
            subtitle="employee"
            icon={<XCircle className="text-red-500" size={24} />}
            color="red"
          />
        </div>

        {/* Team attendance list */}
        <div className="bg-white rounded-lg shadow">
          <div className="px-6 py-4 border-b border-gray-200">
            <h3 className="text-lg font-semibold">Today's Attendance</h3>
          </div>
          <div className="divide-y divide-gray-200">
            {teamAttendance.map((employee) => (
              <div key={employee.id} className="px-6 py-4 flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                    <span className="text-blue-600 font-semibold text-sm">
                      {employee.name.split(' ').map(n => n[0]).join('')}
                    </span>
                  </div>
                  <div>
                    <p className="font-medium text-gray-900">{employee.name}</p>
                    <p className="text-sm text-gray-500">{employee.department}</p>
                  </div>
                </div>
                
                <div className="flex items-center space-x-4">
                  <div className="text-right">
                    <p className="text-sm text-gray-900">
                      {employee.checkIn ? `Check-in: ${employee.checkIn}` : 'Not checked in'}
                    </p>
                  </div>
                  <StatusBadge status={employee.status} />
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </PermissionGate>
  );

  const renderReportsTab = () => (
    <PermissionGate permission="attendanceManagement">
      <div className="space-y-6">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Attendance Reports</h1>
          <p className="text-gray-600">Generate and view attendance reports</p>
        </div>
        
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-semibold mb-4">Report Generation</h3>
          <p className="text-gray-600">Generate detailed attendance reports for analysis.</p>
        </div>
      </div>
    </PermissionGate>
  );

  // Render content based on active tab
  const renderContent = () => {
    switch (activeTab) {
      case 'team-attendance':
        return renderTeamAttendanceTab();
      case 'reports':
        return renderReportsTab();
      case 'my-attendance':
      default:
        return renderMyAttendanceTab();
    }
  };

  return (
    <div className="p-6">
      {renderContent()}
    </div>
  );
}

// Stat card component
function StatCard({ title, value, subtitle, icon, color }) {
  const colorClasses = {
    green: 'border-green-200 bg-green-50',
    yellow: 'border-yellow-200 bg-yellow-50',
    red: 'border-red-200 bg-red-50',
    blue: 'border-blue-200 bg-blue-50'
  };

  return (
    <div className={`bg-white rounded-lg border-2 ${colorClasses[color]} p-6`}>
      <div className="flex items-center justify-between">
        <div>
          <p className="text-sm font-medium text-gray-600">{title}</p>
          <p className="text-2xl font-bold text-gray-900">{value}</p>
          <p className="text-sm text-gray-500">{subtitle}</p>
        </div>
        <div className="flex-shrink-0">
          {icon}
        </div>
      </div>
    </div>
  );
}

// Status badge component
function StatusBadge({ status }) {
  const statusConfig = {
    present: { label: 'Present', className: 'bg-green-100 text-green-800' },
    late: { label: 'Late', className: 'bg-yellow-100 text-yellow-800' },
    absent: { label: 'Absent', className: 'bg-red-100 text-red-800' }
  };

  const config = statusConfig[status] || statusConfig.absent;

  return (
    <span className={`px-2 py-1 text-xs font-medium rounded-full ${config.className}`}>
      {config.label}
    </span>
  );
}

// Enhanced Timeline Component
function AttendanceTimeline({ attendanceData }) {
  return (
    <div className="bg-white rounded-lg shadow p-6">
      <h3 className="text-lg font-semibold mb-6">Weekly Attendance Timeline</h3>

      <div className="space-y-4">
        {attendanceData.map((item, idx) => (
          <div key={idx} className="flex items-center">
            {/* Day and Date */}
            <div className="w-20 text-center">
              <div className="font-bold text-gray-900">{item.day}</div>
              <div className="text-sm text-gray-500">{item.date}</div>
            </div>

            {/* Timeline Bar */}
            <div className="flex-1 relative mx-8">
              <div className={`h-1 ${item.color} rounded-full`}></div>

              {/* Status Label for non-present days */}
              {item.status !== 'Present' && (
                <div className="absolute top-0 left-1/2 transform -translate-x-1/2 -translate-y-6">
                  <span className={`px-2 py-1 text-xs font-medium rounded border ${item.color} bg-white`}>
                    {item.status}
                  </span>
                </div>
              )}

              {/* Check-in/Check-out times for present days */}
              {item.status === 'Present' && (
                <div className="flex justify-between mt-2">
                  <div className="text-sm font-medium text-gray-900">{item.checkIn}</div>
                  <div className="text-sm font-medium text-gray-900">{item.checkOut}</div>
                </div>
              )}
            </div>

            {/* Hours Worked */}
            <div className="w-24 text-right">
              <div className="font-bold text-gray-900">{item.hours}</div>
              <div className="text-xs text-gray-500">Hrs worked</div>
            </div>
          </div>
        ))}
      </div>

      {/* Time Scale */}
      <div className="mt-8 pt-4 border-t border-gray-200">
        <div className="flex justify-between text-xs text-gray-500 mb-2">
          {Array.from({ length: 10 }, (_, i) => (
            <span key={i}>{9 + i}AM</span>
          ))}
        </div>
      </div>
    </div>
  );
}

// Summary Footer Component
function AttendanceSummaryFooter({ summaryItems }) {
  return (
    <div className="bg-white rounded-lg shadow p-4">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <div className="text-sm font-medium text-gray-700">Days</div>
          <div className="text-sm bg-gray-100 px-3 py-1 rounded">Hours</div>
        </div>

        <div className="flex items-center gap-6">
          {summaryItems.map((item, i) => (
            <div key={i} className={`border-l-4 ${item.color} pl-3`}>
              <div className="text-sm font-medium text-gray-900">{item.label}</div>
              <div className="text-sm text-gray-600">{item.value}</div>
            </div>
          ))}
        </div>

        <div className="text-sm font-medium text-gray-900">
          General [ 9:00 AM - 6:00 PM ]
        </div>
      </div>
    </div>
  );
}
