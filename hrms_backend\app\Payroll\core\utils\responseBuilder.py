from flask import jsonify

class ResponseBuilder:
    def __init__(self, data=None, status_code=200, total=None, meta=None, message=""):
        self.data = data
        self.status_code = status_code
        self.total = total
        self.message = message
        self.meta = meta  # ✅ Store meta as an instance attribute

    def build(self):
        response = {
            "message": self.message,
            "data": self.data,
            "status_code": self.status_code            
        }

        if self.total is not None:
            response["total"] = self.total

        if self.meta:
            response["meta"] = self.meta  # ✅ Safely add meta here

        return jsonify(response), self.status_code
