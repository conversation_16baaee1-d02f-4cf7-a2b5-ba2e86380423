from core.databases.database import db
from core.models.basemodel import ModelBase
from sqlalchemy.orm import relationship
from datetime import datetime

class Paystack_IntegrationModel(ModelBase):
    __tablename__ = "pay_stack_credential"

    id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    paystack_key = db.Column(db.String(1024), nullable=False)
    user_id = db.Column(db.Integer, db.ForeignKey((ModelBase.dbSchema() + '.users.id')), nullable=False) 
    timestamp = db.Column(db.DateTime, nullable=False, default=datetime.now()) 
