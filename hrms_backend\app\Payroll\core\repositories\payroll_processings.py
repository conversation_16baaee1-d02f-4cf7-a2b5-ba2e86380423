from core.models.payroll_processings import PayrollProcessingsModel
from core.databases.database import db

class PayrollProcessingsRepository:
    def __init__(self) -> None:
        pass

    @classmethod
    def createPayrollProcessings(self, month, year, organisation_id):
        payroll_processings = PayrollProcessingsModel(
            month=month,
            year=year,
            organisation_id=organisation_id
        )
        db.session.add(payroll_processings)
        db.session.commit()
        return payroll_processings

    @classmethod
    def getPayrollProcessings(self, id):
        return PayrollProcessingsModel.query.filter(PayrollProcessingsModel.id == id).first()
    
    @classmethod
    def getPayrollProcessingsByKeys(self, kwargs):
        return PayrollProcessingsModel.query.filter_by(**kwargs).all()

    @classmethod
    def updatePayrollProcessings(self, id, **kwargs):
        payroll_processings = PayrollProcessingsModel.query.filter_by(id=id).first()
        if payroll_processings:
            for key, value in kwargs.items():
                setattr(payroll_processings, key, value)
            db.session.commit()
            return payroll_processings
        else:
            return None

    @classmethod
    def deletePayrollProcessings(self, id):
        return PayrollProcessingsModel.query.filter(PayrollProcessingsModel.id == id).delete()
        