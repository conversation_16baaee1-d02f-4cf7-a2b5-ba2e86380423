#!/usr/bin/env python3
"""
Comprehensive Backend and Database Testing Script
Tests all ticket management features and verifies PostgreSQL database storage
"""

import asyncio
import sys
import os
import json
import logging
from datetime import datetime, timedelta
from typing import Dict, Any, List
from uuid import uuid4

# Add the app directory to Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'app'))

from sqlalchemy.orm import Session
from sqlalchemy import text, inspect
from app.db.session import engine, SessionLocal, test_connection, create_tables
from app.core.config import settings
from app.db.models.ticket import (
    Ticket, TicketComment, TicketActivity, TicketEscalation, TicketNotification,
    TicketTemplate, TicketSLA, TicketWorkflow, TicketCategory,
    TicketStatus, TicketPriority, TicketType
)
from app.db.models.employee import Employee, Department, Organization
from app.db.models.user import User

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class DatabaseTester:
    """Comprehensive database testing class"""

    def __init__(self):
        self.db: Session = SessionLocal()
        self.test_data = {}
        self.test_results = []

    def __enter__(self):
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        self.db.close()

    def log_test(self, test_name: str, success: bool, message: str = "", data: Any = None):
        """Log test results"""
        result = {
            "test_name": test_name,
            "success": success,
            "message": message,
            "timestamp": datetime.utcnow().isoformat(),
            "data": data
        }
        self.test_results.append(result)
        
        status = "✅ PASS" if success else "❌ FAIL"
        logger.info(f"{status} - {test_name}: {message}")
        
        if not success:
            logger.error(f"Test failed: {test_name} - {message}")

    async def test_database_connection(self) -> bool:
        """Test basic database connectivity"""
        try:
            # Test connection
            connection_ok = test_connection()
            self.log_test("Database Connection", connection_ok, 
                         "Connected to PostgreSQL" if connection_ok else "Failed to connect")
            
            if not connection_ok:
                return False

            # Test database info
            with engine.connect() as conn:
                result = conn.execute(text("SELECT version()"))
                version = result.fetchone()[0]
                self.log_test("PostgreSQL Version", True, f"Version: {version}")

                # Test current database
                result = conn.execute(text("SELECT current_database()"))
                db_name = result.fetchone()[0]
                self.log_test("Current Database", True, f"Database: {db_name}")

                # Test schema
                result = conn.execute(text("SELECT current_schema()"))
                schema = result.fetchone()[0]
                self.log_test("Current Schema", True, f"Schema: {schema}")

            return True

        except Exception as e:
            self.log_test("Database Connection", False, f"Error: {str(e)}")
            return False

    async def test_table_creation(self) -> bool:
        """Test table creation and structure"""
        try:
            # Create tables
            create_tables()
            self.log_test("Table Creation", True, "All tables created successfully")

            # Inspect tables
            inspector = inspect(engine)
            tables = inspector.get_table_names()
            
            # Check for key ticket management tables
            required_tables = [
                'tickets', 'ticket_comments', 'ticket_activities', 'ticket_escalations',
                'ticket_notifications', 'ticket_templates', 'ticket_slas', 'ticket_workflows',
                'ticket_categories', 'employees', 'users', 'organizations', 'departments'
            ]

            missing_tables = []
            for table in required_tables:
                if table not in tables:
                    missing_tables.append(table)

            if missing_tables:
                self.log_test("Required Tables", False, f"Missing tables: {missing_tables}")
                return False
            else:
                self.log_test("Required Tables", True, f"All {len(required_tables)} required tables exist")

            # Test table structure for tickets table
            columns = inspector.get_columns('tickets')
            column_names = [col['name'] for col in columns]
            
            required_columns = [
                'id', 'ticket_number', 'title', 'description', 'ticket_type', 'priority',
                'status', 'requester_id', 'assigned_to', 'organization_id', 'created_at'
            ]

            missing_columns = [col for col in required_columns if col not in column_names]
            if missing_columns:
                self.log_test("Ticket Table Structure", False, f"Missing columns: {missing_columns}")
                return False
            else:
                self.log_test("Ticket Table Structure", True, f"All required columns present")

            return True

        except Exception as e:
            self.log_test("Table Creation", False, f"Error: {str(e)}")
            return False

    async def create_test_data(self) -> bool:
        """Create test data for testing"""
        try:
            # Create test organization
            org = Organization(
                name="Test Organization",
                domain="test.com",
                is_active=True
            )
            self.db.add(org)
            self.db.flush()
            self.test_data['organization_id'] = org.id

            # Create test user
            user = User(
                username="testuser",
                email="<EMAIL>",
                password_hash="hashed_password",
                first_name="Test",
                last_name="User",
                organization_id=org.id,
                is_active=True
            )
            self.db.add(user)
            self.db.flush()
            self.test_data['user_id'] = user.id

            # Create test department
            dept = Department(
                name="IT Department",
                description="Information Technology",
                organization_id=org.id,
                is_active=True
            )
            self.db.add(dept)
            self.db.flush()
            self.test_data['department_id'] = dept.id

            # Create test employee
            employee = Employee(
                employee_id="EMP001",
                first_name="John",
                last_name="Doe",
                email="<EMAIL>",
                phone="+1234567890",
                department_id=dept.id,
                organization_id=org.id,
                user_id=user.id,
                is_active=True
            )
            self.db.add(employee)
            self.db.flush()
            self.test_data['employee_id'] = employee.id

            # Create test agent
            agent = Employee(
                employee_id="AGT001",
                first_name="Jane",
                last_name="Agent",
                email="<EMAIL>",
                phone="+1234567891",
                department_id=dept.id,
                organization_id=org.id,
                is_active=True
            )
            self.db.add(agent)
            self.db.flush()
            self.test_data['agent_id'] = agent.id

            self.db.commit()
            self.log_test("Test Data Creation", True, "Created organization, users, department, and employees")
            return True

        except Exception as e:
            self.db.rollback()
            self.log_test("Test Data Creation", False, f"Error: {str(e)}")
            return False

    async def test_ticket_crud_operations(self) -> bool:
        """Test basic ticket CRUD operations"""
        try:
            # CREATE - Test ticket creation
            ticket = Ticket(
                ticket_number="TKT-000001",
                title="Test Ticket",
                description="This is a test ticket for database testing",
                ticket_type=TicketType.IT_SUPPORT,
                priority=TicketPriority.MEDIUM,
                status=TicketStatus.OPEN,
                requester_id=self.test_data['employee_id'],
                organization_id=self.test_data['organization_id'],
                department_id=self.test_data['department_id'],
                contact_method="web",
                contact_details="<EMAIL>"
            )
            self.db.add(ticket)
            self.db.commit()
            self.db.refresh(ticket)
            
            ticket_id = ticket.id
            self.test_data['ticket_id'] = ticket_id
            self.log_test("Ticket Creation", True, f"Created ticket with ID: {ticket_id}")

            # READ - Test ticket retrieval
            retrieved_ticket = self.db.query(Ticket).filter(Ticket.id == ticket_id).first()
            if retrieved_ticket:
                self.log_test("Ticket Retrieval", True, f"Retrieved ticket: {retrieved_ticket.ticket_number}")
            else:
                self.log_test("Ticket Retrieval", False, "Could not retrieve created ticket")
                return False

            # UPDATE - Test ticket update
            retrieved_ticket.status = TicketStatus.IN_PROGRESS
            retrieved_ticket.assigned_to = self.test_data['agent_id']
            retrieved_ticket.updated_at = datetime.utcnow()
            self.db.commit()
            
            # Verify update
            updated_ticket = self.db.query(Ticket).filter(Ticket.id == ticket_id).first()
            if updated_ticket.status == TicketStatus.IN_PROGRESS:
                self.log_test("Ticket Update", True, f"Updated ticket status to: {updated_ticket.status}")
            else:
                self.log_test("Ticket Update", False, "Ticket update failed")
                return False

            # Test ticket with AI metadata
            ai_ticket = Ticket(
                ticket_number="TKT-000002",
                title="AI Test Ticket",
                description="This ticket tests AI metadata storage",
                ticket_type=TicketType.HR_QUERY,
                priority=TicketPriority.HIGH,
                status=TicketStatus.OPEN,
                requester_id=self.test_data['employee_id'],
                organization_id=self.test_data['organization_id'],
                ai_metadata={
                    "predicted_type": "hr_query",
                    "confidence_score": 0.85,
                    "sentiment_analysis": {
                        "sentiment": "neutral",
                        "urgency_score": 0.6
                    },
                    "suggested_tags": ["hr", "policy", "question"]
                }
            )
            self.db.add(ai_ticket)
            self.db.commit()
            self.db.refresh(ai_ticket)
            
            self.test_data['ai_ticket_id'] = ai_ticket.id
            self.log_test("AI Metadata Storage", True, f"Stored AI metadata in ticket: {ai_ticket.id}")

            return True

        except Exception as e:
            self.db.rollback()
            self.log_test("Ticket CRUD Operations", False, f"Error: {str(e)}")
            return False

    async def test_ticket_relationships(self) -> bool:
        """Test ticket relationships and foreign keys"""
        try:
            ticket_id = self.test_data['ticket_id']

            # Test ticket comments
            comment = TicketComment(
                ticket_id=ticket_id,
                user_id=self.test_data['user_id'],
                content="This is a test comment",
                is_internal=False,
                is_solution=False,
                time_spent_minutes=30
            )
            self.db.add(comment)
            self.db.commit()
            self.db.refresh(comment)
            
            self.log_test("Ticket Comment Creation", True, f"Created comment with ID: {comment.id}")

            # Test ticket activity
            activity = TicketActivity(
                ticket_id=ticket_id,
                user_id=self.test_data['user_id'],
                activity_type="status_changed",
                description="Ticket status changed to In Progress",
                is_system_activity=False
            )
            self.db.add(activity)
            self.db.commit()
            self.db.refresh(activity)
            
            self.log_test("Ticket Activity Creation", True, f"Created activity with ID: {activity.id}")

            # Test ticket escalation
            escalation = TicketEscalation(
                ticket_id=ticket_id,
                escalated_by=self.test_data['user_id'],
                escalated_to=self.test_data['agent_id'],
                reason="SLA breach",
                escalation_level=1,
                escalated_at=datetime.utcnow(),
                is_active=True
            )
            self.db.add(escalation)
            self.db.commit()
            self.db.refresh(escalation)
            
            self.log_test("Ticket Escalation Creation", True, f"Created escalation with ID: {escalation.id}")

            # Test ticket notification
            notification = TicketNotification(
                ticket_id=ticket_id,
                recipient_id=self.test_data['agent_id'],
                notification_type="email",
                subject="Ticket Assigned",
                message="A new ticket has been assigned to you",
                sent_at=datetime.utcnow()
            )
            self.db.add(notification)
            self.db.commit()
            self.db.refresh(notification)
            
            self.log_test("Ticket Notification Creation", True, f"Created notification with ID: {notification.id}")

            # Test relationships by querying
            ticket_with_relations = self.db.query(Ticket).filter(Ticket.id == ticket_id).first()
            
            # Count related records
            comments_count = self.db.query(TicketComment).filter(TicketComment.ticket_id == ticket_id).count()
            activities_count = self.db.query(TicketActivity).filter(TicketActivity.ticket_id == ticket_id).count()
            escalations_count = self.db.query(TicketEscalation).filter(TicketEscalation.ticket_id == ticket_id).count()
            notifications_count = self.db.query(TicketNotification).filter(TicketNotification.ticket_id == ticket_id).count()

            self.log_test("Ticket Relationships", True, 
                         f"Ticket has {comments_count} comments, {activities_count} activities, "
                         f"{escalations_count} escalations, {notifications_count} notifications")

            return True

        except Exception as e:
            self.db.rollback()
            self.log_test("Ticket Relationships", False, f"Error: {str(e)}")
            return False

    async def test_ticket_management_features(self) -> bool:
        """Test advanced ticket management features"""
        try:
            # Test ticket template
            template = TicketTemplate(
                name="IT Support Template",
                description="Template for IT support requests",
                organization_id=self.test_data['organization_id'],
                ticket_type=TicketType.IT_SUPPORT,
                default_title="IT Support Request - {{user_name}}",
                default_description="Please describe your IT issue",
                default_priority=TicketPriority.MEDIUM,
                custom_fields=[
                    {"name": "system", "type": "select", "options": ["Windows", "Mac", "Linux"]},
                    {"name": "urgency", "type": "text", "required": True}
                ],
                is_public=True,
                usage_count=0,
                created_by=self.test_data['user_id']
            )
            self.db.add(template)
            self.db.commit()
            self.db.refresh(template)
            
            self.log_test("Ticket Template Creation", True, f"Created template with ID: {template.id}")

            # Test SLA configuration
            sla = TicketSLA(
                name="IT Support SLA",
                organization_id=self.test_data['organization_id'],
                ticket_types=[TicketType.IT_SUPPORT],
                priorities=[TicketPriority.HIGH, TicketPriority.URGENT],
                first_response_hours=2,
                resolution_hours=24,
                business_hours_only=True,
                business_hours_start="09:00",
                business_hours_end="17:00",
                business_days=[0, 1, 2, 3, 4],
                escalation_enabled=True,
                escalation_hours=4,
                is_active=True,
                created_by=self.test_data['user_id']
            )
            self.db.add(sla)
            self.db.commit()
            self.db.refresh(sla)
            
            self.log_test("SLA Configuration Creation", True, f"Created SLA with ID: {sla.id}")

            # Test workflow configuration
            workflow = TicketWorkflow(
                name="IT Support Workflow",
                description="Automated workflow for IT support tickets",
                organization_id=self.test_data['organization_id'],
                trigger_conditions={
                    "events": ["created"],
                    "ticket_types": ["it_support"],
                    "priorities": ["high", "urgent"]
                },
                workflow_steps=[
                    {
                        "type": "auto_assign",
                        "assignment_rules": {"team": "IT Support"}
                    },
                    {
                        "type": "notification",
                        "notification": {
                            "recipients": ["assignee"],
                            "type": "email",
                            "subject": "New IT Support Ticket"
                        }
                    }
                ],
                is_active=True,
                priority=1,
                created_by=self.test_data['user_id']
            )
            self.db.add(workflow)
            self.db.commit()
            self.db.refresh(workflow)
            
            self.log_test("Workflow Configuration Creation", True, f"Created workflow with ID: {workflow.id}")

            # Test ticket category
            category = TicketCategory(
                name="Password Issues",
                description="Password reset and access issues",
                organization_id=self.test_data['organization_id'],
                ticket_type=TicketType.IT_SUPPORT,
                is_active=True,
                created_by=self.test_data['user_id']
            )
            self.db.add(category)
            self.db.commit()
            self.db.refresh(category)
            
            self.log_test("Ticket Category Creation", True, f"Created category with ID: {category.id}")

            return True

        except Exception as e:
            self.db.rollback()
            self.log_test("Ticket Management Features", False, f"Error: {str(e)}")
            return False

    async def test_data_integrity(self) -> bool:
        """Test data integrity and constraints"""
        try:
            # Test unique constraints
            try:
                duplicate_ticket = Ticket(
                    ticket_number="TKT-000001",  # Duplicate ticket number
                    title="Duplicate Test",
                    description="This should fail",
                    ticket_type=TicketType.IT_SUPPORT,
                    priority=TicketPriority.LOW,
                    status=TicketStatus.OPEN,
                    requester_id=self.test_data['employee_id'],
                    organization_id=self.test_data['organization_id']
                )
                self.db.add(duplicate_ticket)
                self.db.commit()
                
                self.log_test("Unique Constraint Test", False, "Duplicate ticket number was allowed")
                return False
                
            except Exception:
                self.db.rollback()
                self.log_test("Unique Constraint Test", True, "Duplicate ticket number correctly rejected")

            # Test foreign key constraints
            try:
                invalid_ticket = Ticket(
                    ticket_number="TKT-INVALID",
                    title="Invalid Test",
                    description="This should fail",
                    ticket_type=TicketType.IT_SUPPORT,
                    priority=TicketPriority.LOW,
                    status=TicketStatus.OPEN,
                    requester_id=uuid4(),  # Non-existent employee
                    organization_id=self.test_data['organization_id']
                )
                self.db.add(invalid_ticket)
                self.db.commit()
                
                self.log_test("Foreign Key Constraint Test", False, "Invalid foreign key was allowed")
                return False
                
            except Exception:
                self.db.rollback()
                self.log_test("Foreign Key Constraint Test", True, "Invalid foreign key correctly rejected")

            # Test enum constraints
            ticket = self.db.query(Ticket).filter(Ticket.id == self.test_data['ticket_id']).first()
            if ticket:
                # Verify enum values are stored correctly
                self.log_test("Enum Storage Test", True, 
                             f"Ticket type: {ticket.ticket_type}, Priority: {ticket.priority}, Status: {ticket.status}")

            return True

        except Exception as e:
            self.log_test("Data Integrity Test", False, f"Error: {str(e)}")
            return False

    async def test_performance_queries(self) -> bool:
        """Test database performance with complex queries"""
        try:
            # Test complex join query
            start_time = datetime.utcnow()
            
            query = self.db.query(Ticket).join(Employee, Ticket.requester_id == Employee.id)\
                           .join(Department, Employee.department_id == Department.id)\
                           .filter(Ticket.organization_id == self.test_data['organization_id'])
            
            results = query.all()
            end_time = datetime.utcnow()
            
            query_time = (end_time - start_time).total_seconds()
            self.log_test("Complex Join Query", True, 
                         f"Query returned {len(results)} results in {query_time:.3f} seconds")

            # Test aggregation query
            start_time = datetime.utcnow()
            
            stats = self.db.query(
                Ticket.status,
                self.db.query(Ticket).filter(Ticket.status == Ticket.status).count().label('count')
            ).filter(Ticket.organization_id == self.test_data['organization_id']).all()
            
            end_time = datetime.utcnow()
            query_time = (end_time - start_time).total_seconds()
            
            self.log_test("Aggregation Query", True, 
                         f"Aggregation query completed in {query_time:.3f} seconds")

            return True

        except Exception as e:
            self.log_test("Performance Queries", False, f"Error: {str(e)}")
            return False

    async def cleanup_test_data(self) -> bool:
        """Clean up test data"""
        try:
            # Delete in reverse order of creation to respect foreign keys
            self.db.query(TicketNotification).filter(
                TicketNotification.ticket_id == self.test_data.get('ticket_id')
            ).delete()
            
            self.db.query(TicketEscalation).filter(
                TicketEscalation.ticket_id == self.test_data.get('ticket_id')
            ).delete()
            
            self.db.query(TicketActivity).filter(
                TicketActivity.ticket_id == self.test_data.get('ticket_id')
            ).delete()
            
            self.db.query(TicketComment).filter(
                TicketComment.ticket_id == self.test_data.get('ticket_id')
            ).delete()
            
            self.db.query(Ticket).filter(
                Ticket.organization_id == self.test_data.get('organization_id')
            ).delete()
            
            self.db.query(TicketTemplate).filter(
                TicketTemplate.organization_id == self.test_data.get('organization_id')
            ).delete()
            
            self.db.query(TicketSLA).filter(
                TicketSLA.organization_id == self.test_data.get('organization_id')
            ).delete()
            
            self.db.query(TicketWorkflow).filter(
                TicketWorkflow.organization_id == self.test_data.get('organization_id')
            ).delete()
            
            self.db.query(TicketCategory).filter(
                TicketCategory.organization_id == self.test_data.get('organization_id')
            ).delete()
            
            self.db.query(Employee).filter(
                Employee.organization_id == self.test_data.get('organization_id')
            ).delete()
            
            self.db.query(Department).filter(
                Department.organization_id == self.test_data.get('organization_id')
            ).delete()
            
            self.db.query(User).filter(
                User.organization_id == self.test_data.get('organization_id')
            ).delete()
            
            self.db.query(Organization).filter(
                Organization.id == self.test_data.get('organization_id')
            ).delete()
            
            self.db.commit()
            self.log_test("Test Data Cleanup", True, "All test data cleaned up successfully")
            return True

        except Exception as e:
            self.db.rollback()
            self.log_test("Test Data Cleanup", False, f"Error: {str(e)}")
            return False

    def generate_test_report(self) -> Dict[str, Any]:
        """Generate comprehensive test report"""
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results if result['success'])
        failed_tests = total_tests - passed_tests
        
        report = {
            "test_summary": {
                "total_tests": total_tests,
                "passed_tests": passed_tests,
                "failed_tests": failed_tests,
                "success_rate": round((passed_tests / total_tests) * 100, 2) if total_tests > 0 else 0
            },
            "database_info": {
                "host": settings.DB_HOST,
                "port": settings.DB_PORT,
                "database": settings.DB_NAME,
                "schema": settings.DB_SCHEMA
            },
            "test_results": self.test_results,
            "generated_at": datetime.utcnow().isoformat()
        }
        
        return report


async def main():
    """Main test execution function"""
    print("Starting Comprehensive Backend and Database Testing...")
    print(f"Database: {settings.database_url}")
    print("=" * 80)

    with DatabaseTester() as tester:
        # Run all tests
        tests = [
            ("Database Connection", tester.test_database_connection),
            ("Table Creation", tester.test_table_creation),
            ("Test Data Creation", tester.create_test_data),
            ("Ticket CRUD Operations", tester.test_ticket_crud_operations),
            ("Ticket Relationships", tester.test_ticket_relationships),
            ("Ticket Management Features", tester.test_ticket_management_features),
            ("Data Integrity", tester.test_data_integrity),
            ("Performance Queries", tester.test_performance_queries),
            ("Test Data Cleanup", tester.cleanup_test_data)
        ]

        for test_name, test_func in tests:
            print(f"\n🔍 Running: {test_name}")
            try:
                await test_func()
            except Exception as e:
                tester.log_test(test_name, False, f"Unexpected error: {str(e)}")

        # Generate and save report
        report = tester.generate_test_report()
        
        # Save report to file
        with open('test_report.json', 'w') as f:
            json.dump(report, f, indent=2)

        # Print summary
        print("\n" + "=" * 80)
        print("📋 TEST SUMMARY")
        print("=" * 80)
        print(f"Total Tests: {report['test_summary']['total_tests']}")
        print(f"Passed: {report['test_summary']['passed_tests']}")
        print(f"Failed: {report['test_summary']['failed_tests']}")
        print(f"Success Rate: {report['test_summary']['success_rate']}%")
        
        if report['test_summary']['failed_tests'] > 0:
            print("\n❌ FAILED TESTS:")
            for result in report['test_results']:
                if not result['success']:
                    print(f"  - {result['test_name']}: {result['message']}")
        else:
            print("\n✅ ALL TESTS PASSED!")

        print(f"\n📄 Detailed report saved to: test_report.json")
        
        return report['test_summary']['success_rate'] == 100


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
