<!doctype html>
<html lang="en">
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Bootstrap demo</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet" integrity="sha384-QWTKZyjpPEjISv5WaRU9OFeRpok6YctnYmDr5pNlyT2bRjXh0JMhjY6hW+ALEwIH" crossorigin="anonymous">
   
    <style>
        * {
            border: none !important;
            outline: none !important;
            box-shadow: none !important;
        }
        table {
            border-spacing: 0 !important;
            border-collapse: collapse !important;
        }
    </style>
    

</head>
  <body>
    <div class="container">

        <div style="position: relative; width: 100%; height: 100%; overflow: hidden;">
          <!-- Watermark -->
            <div style="
                position: absolute;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                font-size: 50px;
                color: rgba(0, 0, 0, 0.1); /* Light gray for watermark effect */
                white-space: nowrap;
                z-index: -1; /* Ensure watermark is behind other content */
                text-transform: uppercase; /* Optional: Makes text uppercase */
            ">
                {{ employee_payslip["employee"]["organisation"]["organisation_name"] }} 
        </div>
        

        <div class="row">
            <!-- Page Header -->
            <div class="col-12">
                <h2>PaySlip Details</h2>
            </div>
    
            <!-- Organisation Details  Start -->
            <div class="d-flex flex-column justify-content-center align-items-center text-center">
                <h2 class="fw-bold">{{ employee_payslip["employee"]["organisation"]["organisation_name"] }}</h3>
                <h3>{{ employee_payslip["organisation_address"] }}</h3>
                <h5>{{ employee_payslip["employee"]["organisation"]["email"] }}</h5>
                <h3 class="mt-4">Payment slip for the month of February 2025</h4> <!-- #TODO Make it Dynamic -->
            </div>
        </div>
         <!-- Organisation Details  End -->
        
         <!-- Employee Details  Start -->
        <div class="mt-4" style="border: none; outline: none;">
            <table style="width: 100%; border-spacing: 0; border-collapse: collapse; border: none; margin: 0; padding: 0; outline: none;">
                <tr>
                    <td style="width: 33%; vertical-align: top; padding: 5px; border: none; outline: none;">
                        <h5>Employee Name: {{ employee_payslip["employee"]["first_name"] }} {{ employee_payslip["employee"]["last_name"] }}</h5>
                        <h5>Employee ID: {{ employee_payslip["employee"]["id"] }}</h5>
                    </td>
                    <td style="width: 33%; vertical-align: top; padding: 5px; border: none; outline: none;">
                        <h5>Department: {{ employee_payslip["employee"]["department"]["name"] }}</h5>
                        <h5>Designation: {{ employee_payslip["employee"]["designation"]["name"] }}</h5>
                    </td>
                    <td style="width: 33%; vertical-align: top; padding: 5px; border: none; outline: none;">
                        <h5>Email: {{ employee_payslip["email"] }}</h5>
                        <h5>Bank Name: {{ employee_payslip["bank_name"] }}</h5>
                    </td>
                </tr>
            </table>
        </div>
     <!-- Employee Details  End -->
    
     <!-- Earnings Details  Start -->
     
     <div clas="mt-4" style="width: 100%; overflow: hidden;">
        <!-- Left Table: Earnings -->
        <div style="float: left; width: 48%; margin-right: 4%; text-align: left;">
            <table style="width: 100%; border-collapse: collapse; border: 1px solid black;">
                <thead style="background-color: #f8f9fa;">
                    <tr>
                        <th style=" background: #BB3C2D; color: white; font-size: 14px;    border: 1px solid black; padding: 5px;">Earnings</th>
                        <th style="background: #BB3C2D; color: white; font-size: 14px;border: 1px solid black; padding: 5px;">Amount</th>
                    </tr>
                </thead>
                <tbody id="tdata1" >
                    {% if employee_payslip["all_components"] %}
                        {% for component in employee_payslip["all_components"] %}
                        <tr>
                            {% if component["component_type"].lower() == "taxable earning" %}
                            <td>{{ component["payslip_name"] }}</td>
                            <td>{{ component["total_monthly_calculation"] }}</td>
                            {% endif %}
                        </tr>
                        {% endfor %}
                    {% endif %}
                    <tr style="background: lightgrey; font-weight: bold;">
                        <td>Total Taxable</td>
                        <td id="tdata">{{ employee_payslip["total_earnings"] }}</td>
                    </tr>
                </tbody>
            </table>
        </div>
        
        <!-- Right Table: Non-Taxable Earnings -->
        <div style="float: right; width: 48%; text-align: left;">
            <table style="width: 100%; border-collapse: collapse; border: 1px solid black;">
                <thead style="background-color: #f8f9fa;">
                    <tr>
                        <th style="background: #BB3C2D; color: white; font-size: 14px;border: 1px solid black; padding: 5px;">Non-Taxable Earnings</th>
                        <th style="background: #BB3C2D; color: white; font-size: 14px;border: 1px solid black; padding: 5px;">Amount</th>
                    </tr>
                </thead>
                <tbody id="tdata1" >
                    {% if employee_payslip["all_components"] %}
                        {% for component in employee_payslip["all_components"] %}
                        <tr>
                            {% if component["component_type"].lower() == "non-taxable earning" %}
                            <td>{{ component["payslip_name"] }}</td>
                            <td>{{ component["total_monthly_calculation"] }}</td>
                            {% endif %}
                        </tr>
                        {% endfor %}
                    {% endif %}
                    </tbody>
            </table>
        </div>


        <!-- Left Table: Statory-->
        <div style="float: left; width: 48%; margin-right: 4%; text-align: left;">
            <table style="width: 100%; border-collapse: collapse; border: 1px solid black;">
                <thead style="background-color: #f8f9fa;">
                    <tr>
                        <th style=" background: #BB3C2D; color: white; font-size: 14px;    border: 1px solid black; padding: 5px;">Statutory Deduction</th>
                        <th style="background: #BB3C2D; color: white; font-size: 14px;border: 1px solid black; padding: 5px;">Amount</th>
                    </tr>
                </thead>
                <tbody id="tdata1" >
                    {% if employee_payslip["components"] %}
                        {% for component in employee_payslip["components"] %}
                        <tr>
                            {% if component["component_type"].lower() == "statutory deduction" %}
                            <td>{{ component["payslip_name"] }}</td>
                            <td>{{ component["total_monthly_calculation"] }}</td>
                            {% endif %}
                            
                        </tr>
                        {% endfor %}
                    {% endif %}
                   
                </tbody>
            </table>
        </div>
        
         <!-- Right Table: other Earnings -->
         <div style="float: right; width: 48%; text-align: left;">
            <table style="width: 100%; border-collapse: collapse; border: 1px solid black;">
                <thead style="background-color: #f8f9fa;">
                    <tr>
                        <th style="background: #BB3C2D; color: white; font-size: 14px;border: 1px solid black; padding: 5px;">Other deduction</th>
                        <th style="background: #BB3C2D; color: white; font-size: 14px;border: 1px solid black; padding: 5px;">Amount</th>
                    </tr>
                </thead>
                <tbody id="tdata1" >
                    {% if employee_payslip["all_components"] %}
                        {% for component in employee_payslip["all_components"] %}
                        <tr>
                            {% if component["component_type"].lower() == "other deductions" %}
                            <td>{{ component["payslip_name"] }}</td>
                            <td>{{ component["total_monthly_calculation"] }}</td>
                            {% endif %}
                        </tr>
                        {% endfor %}
                    {% endif %}
                     
                </tbody>
            </table>
        </div>

          <!-- Left Table: Netpay-->
          <div style="float: left; width: 48%; margin-right: 4%; text-align: left;">
            <table style="width: 100%; border-collapse: collapse; border: 1px solid black;">
                <thead style="background-color: #f8f9fa;">
                    <tr>
                        <th style=" background: #BB3C2D; color: white; font-size: 14px;    border: 1px solid black; padding: 5px;">Netpay</th>
                        <th style="background: #BB3C2D; color: white; font-size: 14px;border: 1px solid black; padding: 5px;">Amount</th>
                    </tr>
                </thead>
                <tbody id="tdata1" >
                    {% if employee_payslip %}
                        
                    <tr  style="background: lightgrey; font-weight: bold;">
                        <td>Netpay</td>
                        <td>{{ employee_payslip["net_pay"] }}</td>
                    </tr>
                        
                    {% endif %}
                   
                </tbody>
            </table>
        </div>
    </div>
      
    
      <!-- Earnings Details  End -->
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js" integrity="sha384-YvpcrYf0tY3lHB60NNkmXc5s9fDVZLESaAA55NDzOxhy9GkcIdslK1eN7N6jIeHz" crossorigin="anonymous"></script>
  </body>
</html>