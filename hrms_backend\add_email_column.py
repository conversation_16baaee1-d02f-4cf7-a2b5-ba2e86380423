#!/usr/bin/env python3
"""
Add email column to employees table if it doesn't exist
"""

import sys
import os

# Add the app directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'app'))

from sqlalchemy.orm import Session
from sqlalchemy import text, inspect
from app.db.session import SessionLocal

def add_email_column():
    """Add email column to employees table if it doesn't exist"""
    db = SessionLocal()
    
    try:
        print("Checking if email column exists in employees table...")
        
        # Check if email column exists
        inspector = inspect(db.bind)
        columns = [col['name'] for col in inspector.get_columns('employees')]
        
        if 'email' in columns:
            print("✅ Email column already exists in employees table")
            return
        
        print("❌ Email column missing. Adding email column...")
        
        # Add email column
        add_column_sql = text("""
            ALTER TABLE employees 
            ADD COLUMN email VARCHAR(255) UNIQUE;
        """)
        
        db.execute(add_column_sql)
        
        # Add index on email
        add_index_sql = text("""
            CREATE INDEX IF NOT EXISTS idx_employees_email ON employees(email);
        """)
        
        db.execute(add_index_sql)
        
        db.commit()
        print("✅ Email column added successfully!")
        
    except Exception as e:
        print(f"❌ Error adding email column: {e}")
        db.rollback()
        raise
    finally:
        db.close()

def check_table_structure():
    """Check the current structure of employees table"""
    db = SessionLocal()
    
    try:
        print("\nChecking employees table structure...")
        
        # Get table columns
        inspector = inspect(db.bind)
        columns = inspector.get_columns('employees')
        
        print("Current columns in employees table:")
        for col in columns:
            print(f"  - {col['name']}: {col['type']} (nullable: {col['nullable']})")
            
    except Exception as e:
        print(f"❌ Error checking table structure: {e}")
    finally:
        db.close()

if __name__ == "__main__":
    check_table_structure()
    add_email_column()
    check_table_structure()
