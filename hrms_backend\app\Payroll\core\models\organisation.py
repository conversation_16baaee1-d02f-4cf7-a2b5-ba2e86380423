from core.databases.database import db
from core.models.basemodel import ModelBase
from sqlalchemy.orm import relationship, declarative_base
from datetime import datetime
from core.models.employees import EmployeeModel

# import re
# from sqlalchemy.ext.hybrid import hybrid_property
# from sqlalchemy import func

class OrganisationModel(ModelBase):
    __tablename__ = "organisations"

    id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    slug = db.Column(db.String(80), unique=True, nullable=False)
    organisation_name = db.Column(db.String(45), unique=True, nullable=False)
    user_id = db.Column(db.Integer, db.ForeignKey((ModelBase.dbSchema() + '.users.id')), nullable=False) 
    owner = db.relationship('UserModel', back_populates='organisations', lazy=True)
    email= db.Column(db.String(45), unique=True, nullable=False)
    address= db.Column(db.String(80), nullable=False)
    country= db.Column(db.String(45), nullable=False)
    city= db.Column(db.String(45), nullable=False)
    state = db.Column(db.String(45), nullable=False)
    zipcode= db.Column(db.String(45), nullable=False)
    phone= db.Column(db.String(45), nullable=False)
    industry= db.Column(db.String(45), nullable=False)
    type= db.Column(db.String(45), nullable=False)
    timestamp = db.Column(db.DateTime, nullable=False, default=datetime.now())
    employees = db.relationship('EmployeeModel', backref='organisation', cascade='all, delete-orphan')
    logoUrl = db.Column(db.Text, nullable=True)
    
    pay_schedules = db.relationship("PaySchedulesModel", back_populates="organisation", cascade="all, delete-orphan")
    