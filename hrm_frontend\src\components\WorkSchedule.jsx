import { Clock } from 'lucide-react';

export default function WorkSchedule({ currentDate }) {
  // Calculate the start of the week (Sunday)
  const getStartOfWeek = (date) => {
    const d = new Date(date);
    const day = d.getDay(); // 0 for Sunday
    d.setDate(d.getDate() - day);
    return d;
  };
  
  const startOfWeek = getStartOfWeek(currentDate);
  const weekDays = Array.from({ length: 7 }, (_, i) => {
    const day = new Date(startOfWeek);
    day.setDate(startOfWeek.getDate() + i);
    return day;
  });
  
  const formatDay = (date) => {
    return date.toLocaleDateString("en-US", { weekday: "short" });
  };
  
  const formatDate = (date) => {
    return date.getDate();
  };
  
  const isToday = (date) => {
    const today = new Date();
    return date.getDate() === today.getDate() &&
           date.getMonth() === today.getMonth() &&
           date.getFullYear() === today.getFullYear();
  };
  
  const getDayStatus = (date) => {
    const day = date.getDay();
    if (day === 0 || day === 6) return "Weekend";
    
    // For demo purposes, let's say Monday and Tuesday are absent days
    if (day === 1 || day === 2) return "Absent";
    
    return "";
  };
  
  const getHours = (date) => {
    if (isToday(date)) return "03:16 Hrs";
    return null;
  };
  
  return (
    <div className="mt-6">
      <div className="flex items-center gap-2 mb-2">
        <Clock className="w-5 h-5 text-gray-500" />
        <h3 className="font-medium">Work Schedule</h3>
      </div>
      
      <div className="flex items-center gap-2 text-sm text-gray-600 mb-4">
        <span>
          {weekDays[0].toLocaleDateString("en-US", { day: "2-digit", month: "short", year: "numeric" })}
        </span>
        <span>-</span>
        <span>
          {weekDays[6].toLocaleDateString("en-US", { day: "2-digit", month: "short", year: "numeric" })}
        </span>
      </div>
      
      <div className="border rounded-md mb-4">
        <div className="p-3 border-b">
          <div className="font-medium">General</div>
          <div className="text-sm text-gray-600">9:00 AM - 6:00 PM</div>
        </div>
        
        <div className="grid grid-cols-7 text-center">
          {weekDays.map((day, index) => (
            <div 
              key={index} 
              className={`py-3 ${isToday(day) ? "bg-blue-50" : ""}`}
            >
              <div className="text-sm">{formatDay(day)}</div>
              <div className={`text-lg font-medium ${isToday(day) ? "text-blue-600" : ""}`}>
                {formatDate(day)}
              </div>
              <div
                className={`text-xs mt-1 ${
                  getDayStatus(day) === "Weekend" ? "text-orange-500" : 
                  getDayStatus(day) === "Absent" ? "text-red-500" : ""
                }`}
              >
                {getDayStatus(day)}
              </div>
              {getHours(day) && <div className="text-xs mt-1">{getHours(day)}</div>}
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}