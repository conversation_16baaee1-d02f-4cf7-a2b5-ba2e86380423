"""add column to approval hisory

Revision ID: 2b4137d3e25b
Revises: 9ceabeec6a3c
Create Date: 2025-04-25 14:15:30.946651

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '2b4137d3e25b'
down_revision: Union[str, None] = '9ceabeec6a3c'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None



def upgrade() -> None:
    op.add_column(
        "approval_histories",
        sa.Column('employee_id', sa.In<PERSON>ger, sa.<PERSON>("employees.id"), nullable=True)
    )

def downgrade() -> None:
    op.drop_column("approval_histories", "employee_id")


