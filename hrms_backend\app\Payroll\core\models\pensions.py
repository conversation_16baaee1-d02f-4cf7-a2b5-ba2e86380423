from core.databases.database import db
from core.models.basemodel import ModelBase

class PensionsModel(ModelBase):
    __tablename__ = "pensions"

    id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    pension_name = db.Column(db.String(100), nullable=False)
    pension_fund_custodian = db.Column(db.String(100), nullable=False)
    pfc_account_number = db.Column(db.Integer, nullable=False) 
    address = db.Column(db.String(100), nullable=False)
    city = db.Column(db.String(100), nullable=False)
    state = db.Column(db.String(100), nullable=False)
    country = db.Column(db.String(100), nullable=False)
    zip_code = db.Column(db.Integer, nullable=False)
    
     

    
      