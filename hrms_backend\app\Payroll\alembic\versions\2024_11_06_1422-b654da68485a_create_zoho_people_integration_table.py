"""Create zoho_people_integration
  table

Revision ID: b654da68485a
Revises: b73f1ed75444
Create Date: 2024-11-06 14:22:22.986385

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy import func, TIMESTAMP, ForeignKey
from sqlalchemy import Column, Integer, String


# revision identifiers, used by Alembic.
revision: str = 'b654da68485a'
down_revision: Union[str, None] = 'b73f1ed75444'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None



def upgrade() -> None:
    op.create_table(
        'zoho_people_integration',
        Column('id', Integer, primary_key=True),
        <PERSON><PERSON><PERSON>("user_id", Integer, ForeignKey("users.id")),
        Column('authorization_employees_code', String(250), nullable=True),
        Column('client_id', String(250), nullable=True),
        Column('client_secret', String(250), nullable=True),
        Column('employees_access_token', String(250), nullable=True),
        <PERSON>umn('employees_refresh_token', String(250), nullable=True),
        Column('token_expires_at', String(250), nullable=True),
        Column("timestamp", TIMESTAMP, server_default=func.now()),
        
    )

def downgrade() -> None:
    op.drop_table("zoho_people_integration")