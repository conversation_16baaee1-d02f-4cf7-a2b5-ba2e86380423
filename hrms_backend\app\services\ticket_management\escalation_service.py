from sqlalchemy.orm import Session, joinedload
from sqlalchemy import and_, or_, func
from typing import Optional, List, Dict, Any
from uuid import UUID
from datetime import datetime, timedelta
from fastapi import HTTPException, status
import logging

from ...db.models.ticket import (
    Ticket, TicketEscalation, TicketActivity, TicketNotification,
    TicketStatus, TicketPriority, TicketType, EscalationLevel
)
from ...db.models.employee import Employee
from ...core.security import CurrentUser
from ...core.audit_logger import AuditLogger

logger = logging.getLogger(__name__)


class TicketEscalationService:
    """Service for managing ticket escalations"""

    async def create_escalation(
        self,
        db: Session,
        escalation_data: Dict[str, Any],
        current_user: CurrentUser
    ) -> TicketEscalation:
        """Create a new ticket escalation"""
        try:
            ticket_id = escalation_data["ticket_id"]
            escalated_to = escalation_data["escalated_to"]
            reason = escalation_data["reason"]

            # Verify ticket exists and user has permission
            ticket = db.query(Ticket).filter(
                Ticket.id == ticket_id,
                Ticket.organization_id == current_user.organization_id
            ).first()

            if not ticket:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Ticket not found"
                )

            # Verify escalation target exists
            escalation_target = db.query(Employee).filter(
                Employee.id == escalated_to,
                Employee.organization_id == current_user.organization_id,
                Employee.is_active == True
            ).first()

            if not escalation_target:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Escalation target not found"
                )

            # Determine escalation level
            current_escalations = db.query(TicketEscalation).filter(
                TicketEscalation.ticket_id == ticket_id,
                TicketEscalation.is_active == True
            ).count()

            escalation_level = min(current_escalations + 1, 4)  # Max level 4

            escalation = TicketEscalation(
                ticket_id=ticket_id,
                escalated_by=current_user.user_id,
                escalated_to=escalated_to,
                reason=reason,
                escalation_level=escalation_level,
                escalated_at=datetime.utcnow(),
                is_active=True
            )

            db.add(escalation)

            # Update ticket assignment
            ticket.assigned_to = escalated_to
            ticket.priority = await self._increase_priority(ticket.priority)

            db.commit()
            db.refresh(escalation)

            # Create activity log
            await self._create_activity(
                db, ticket_id, "escalated",
                f"Ticket escalated to {escalation_target.first_name} {escalation_target.last_name} (Level {escalation_level}): {reason}",
                current_user
            )

            # Send notification to escalation target
            await self._send_escalation_notification(db, escalation, ticket, current_user)

            # Log escalation
            await AuditLogger.log_action(
                db, current_user.user_id, "ticket_escalated",
                f"Escalated ticket {ticket.ticket_number} to {escalation_target.email}",
                {
                    "ticket_id": str(ticket_id),
                    "escalated_to": str(escalated_to),
                    "escalation_level": escalation_level
                }
            )

            logger.info(f"Ticket {ticket.ticket_number} escalated to level {escalation_level} by {current_user.user_id}")
            return escalation

        except HTTPException:
            raise
        except Exception as e:
            db.rollback()
            logger.error(f"Error creating escalation: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Error creating escalation"
            )

    async def get_escalations(
        self,
        db: Session,
        ticket_id: Optional[UUID] = None,
        escalated_to: Optional[UUID] = None,
        is_active: Optional[bool] = None,
        current_user: CurrentUser = None,
        skip: int = 0,
        limit: int = 20
    ) -> List[TicketEscalation]:
        """Get escalations with filtering"""
        try:
            query = db.query(TicketEscalation).join(Ticket).filter(
                Ticket.organization_id == current_user.organization_id
            )

            if ticket_id:
                query = query.filter(TicketEscalation.ticket_id == ticket_id)

            if escalated_to:
                query = query.filter(TicketEscalation.escalated_to == escalated_to)

            if is_active is not None:
                query = query.filter(TicketEscalation.is_active == is_active)

            escalations = query.order_by(TicketEscalation.escalated_at.desc()).offset(skip).limit(limit).all()
            return escalations

        except Exception as e:
            logger.error(f"Error getting escalations: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Error retrieving escalations"
            )

    async def acknowledge_escalation(
        self,
        db: Session,
        escalation_id: UUID,
        current_user: CurrentUser
    ) -> Optional[TicketEscalation]:
        """Acknowledge an escalation"""
        try:
            escalation = db.query(TicketEscalation).join(Ticket).filter(
                TicketEscalation.id == escalation_id,
                Ticket.organization_id == current_user.organization_id,
                TicketEscalation.escalated_to == current_user.user_id
            ).first()

            if not escalation:
                return None

            escalation.acknowledged_at = datetime.utcnow()
            db.commit()
            db.refresh(escalation)

            # Create activity log
            await self._create_activity(
                db, escalation.ticket_id, "escalation_acknowledged",
                f"Escalation acknowledged by {current_user.email}",
                current_user
            )

            logger.info(f"Escalation {escalation_id} acknowledged by {current_user.user_id}")
            return escalation

        except Exception as e:
            db.rollback()
            logger.error(f"Error acknowledging escalation {escalation_id}: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Error acknowledging escalation"
            )

    async def resolve_escalation(
        self,
        db: Session,
        escalation_id: UUID,
        resolution_notes: str,
        current_user: CurrentUser
    ) -> Optional[TicketEscalation]:
        """Resolve an escalation"""
        try:
            escalation = db.query(TicketEscalation).join(Ticket).filter(
                TicketEscalation.id == escalation_id,
                Ticket.organization_id == current_user.organization_id,
                TicketEscalation.escalated_to == current_user.user_id
            ).first()

            if not escalation:
                return None

            escalation.resolved_at = datetime.utcnow()
            escalation.is_active = False
            db.commit()
            db.refresh(escalation)

            # Create activity log
            await self._create_activity(
                db, escalation.ticket_id, "escalation_resolved",
                f"Escalation resolved by {current_user.email}: {resolution_notes}",
                current_user
            )

            logger.info(f"Escalation {escalation_id} resolved by {current_user.user_id}")
            return escalation

        except Exception as e:
            db.rollback()
            logger.error(f"Error resolving escalation {escalation_id}: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Error resolving escalation"
            )

    async def check_auto_escalations(
        self,
        db: Session,
        organization_id: UUID
    ) -> List[Dict[str, Any]]:
        """Check for tickets that need automatic escalation"""
        try:
            current_time = datetime.utcnow()
            escalated_tickets = []

            # Get tickets that might need escalation
            tickets = db.query(Ticket).filter(
                Ticket.organization_id == organization_id,
                Ticket.status.in_([TicketStatus.OPEN, TicketStatus.IN_PROGRESS, TicketStatus.PENDING]),
                Ticket.sla_breach == True,
                Ticket.assigned_to.isnot(None)
            ).all()

            for ticket in tickets:
                # Check if ticket should be auto-escalated
                should_escalate = await self._should_auto_escalate(db, ticket, current_time)
                
                if should_escalate:
                    escalation_target = await self._find_escalation_target(db, ticket)
                    
                    if escalation_target:
                        # Create auto-escalation
                        escalation = TicketEscalation(
                            ticket_id=ticket.id,
                            escalated_by=None,  # System escalation
                            escalated_to=escalation_target.id,
                            reason="Automatic escalation due to SLA breach",
                            escalation_level=await self._get_next_escalation_level(db, ticket.id),
                            escalated_at=current_time,
                            is_active=True
                        )

                        db.add(escalation)

                        # Update ticket
                        ticket.assigned_to = escalation_target.id
                        ticket.priority = await self._increase_priority(ticket.priority)

                        escalated_info = {
                            "ticket_id": str(ticket.id),
                            "ticket_number": ticket.ticket_number,
                            "escalated_to": str(escalation_target.id),
                            "escalation_level": escalation.escalation_level,
                            "reason": escalation.reason
                        }

                        escalated_tickets.append(escalated_info)

                        # Create activity log
                        await self._create_activity(
                            db, ticket.id, "auto_escalated",
                            f"Automatically escalated to {escalation_target.first_name} {escalation_target.last_name} due to SLA breach",
                            None
                        )

                        # Send notification
                        await self._send_escalation_notification(db, escalation, ticket, None)

            db.commit()

            if escalated_tickets:
                logger.info(f"Auto-escalated {len(escalated_tickets)} tickets for organization {organization_id}")

            return escalated_tickets

        except Exception as e:
            db.rollback()
            logger.error(f"Error checking auto-escalations: {e}")
            return []

    async def get_escalation_metrics(
        self,
        db: Session,
        organization_id: UUID,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None
    ) -> Dict[str, Any]:
        """Get escalation metrics"""
        try:
            if not start_date:
                start_date = datetime.utcnow() - timedelta(days=30)
            if not end_date:
                end_date = datetime.utcnow()

            # Base query for escalations in date range
            base_query = db.query(TicketEscalation).join(Ticket).filter(
                Ticket.organization_id == organization_id,
                TicketEscalation.escalated_at >= start_date,
                TicketEscalation.escalated_at <= end_date
            )

            total_escalations = base_query.count()
            
            # Escalation level breakdown
            level_breakdown = {}
            for level in range(1, 5):
                level_count = base_query.filter(TicketEscalation.escalation_level == level).count()
                level_breakdown[f"level_{level}"] = level_count

            # Resolution metrics
            resolved_escalations = base_query.filter(
                TicketEscalation.resolved_at.isnot(None)
            ).all()

            avg_resolution_hours = 0
            if resolved_escalations:
                total_resolution_time = sum([
                    (escalation.resolved_at - escalation.escalated_at).total_seconds() / 3600
                    for escalation in resolved_escalations
                ])
                avg_resolution_hours = total_resolution_time / len(resolved_escalations)

            # Auto vs manual escalations
            auto_escalations = base_query.filter(TicketEscalation.escalated_by.is_(None)).count()
            manual_escalations = total_escalations - auto_escalations

            return {
                "period": {
                    "start_date": start_date.isoformat(),
                    "end_date": end_date.isoformat()
                },
                "total_escalations": total_escalations,
                "auto_escalations": auto_escalations,
                "manual_escalations": manual_escalations,
                "resolved_escalations": len(resolved_escalations),
                "avg_resolution_hours": round(avg_resolution_hours, 2),
                "level_breakdown": level_breakdown
            }

        except Exception as e:
            logger.error(f"Error getting escalation metrics: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Error retrieving escalation metrics"
            )

    async def _should_auto_escalate(
        self,
        db: Session,
        ticket: Ticket,
        current_time: datetime
    ) -> bool:
        """Check if ticket should be auto-escalated"""
        try:
            # Check if ticket has been breached for more than escalation threshold
            if not ticket.sla_breach or not ticket.due_date:
                return False

            # Check if already escalated recently
            recent_escalation = db.query(TicketEscalation).filter(
                TicketEscalation.ticket_id == ticket.id,
                TicketEscalation.escalated_at > current_time - timedelta(hours=4),
                TicketEscalation.is_active == True
            ).first()

            if recent_escalation:
                return False

            # Check escalation threshold (e.g., 2 hours after SLA breach)
            escalation_threshold = timedelta(hours=2)
            time_since_breach = current_time - ticket.due_date

            return time_since_breach > escalation_threshold

        except Exception as e:
            logger.error(f"Error checking auto-escalation conditions: {e}")
            return False

    async def _find_escalation_target(
        self,
        db: Session,
        ticket: Ticket
    ) -> Optional[Employee]:
        """Find appropriate escalation target"""
        try:
            # Simple escalation logic - find manager or senior team member
            # In production, this would use more sophisticated rules
            
            current_assignee = db.query(Employee).filter(Employee.id == ticket.assigned_to).first()
            if not current_assignee:
                return None

            # Find manager or senior employee in same department
            escalation_target = db.query(Employee).filter(
                Employee.organization_id == ticket.organization_id,
                Employee.department_id == current_assignee.department_id,
                Employee.is_active == True,
                Employee.id != ticket.assigned_to,
                # Add role-based filtering here
            ).first()

            return escalation_target

        except Exception as e:
            logger.error(f"Error finding escalation target: {e}")
            return None

    async def _get_next_escalation_level(
        self,
        db: Session,
        ticket_id: UUID
    ) -> int:
        """Get the next escalation level for a ticket"""
        try:
            max_level = db.query(func.max(TicketEscalation.escalation_level)).filter(
                TicketEscalation.ticket_id == ticket_id
            ).scalar() or 0

            return min(max_level + 1, 4)  # Max level 4

        except Exception as e:
            logger.error(f"Error getting next escalation level: {e}")
            return 1

    async def _increase_priority(self, current_priority: TicketPriority) -> TicketPriority:
        """Increase ticket priority during escalation"""
        priority_order = [TicketPriority.LOW, TicketPriority.MEDIUM, TicketPriority.HIGH, TicketPriority.URGENT, TicketPriority.CRITICAL]
        
        try:
            current_index = priority_order.index(current_priority)
            if current_index < len(priority_order) - 1:
                return priority_order[current_index + 1]
            return current_priority
        except ValueError:
            return current_priority

    async def _send_escalation_notification(
        self,
        db: Session,
        escalation: TicketEscalation,
        ticket: Ticket,
        current_user: Optional[CurrentUser]
    ):
        """Send notification about escalation"""
        try:
            escalation_target = db.query(Employee).filter(Employee.id == escalation.escalated_to).first()
            
            if escalation_target:
                notification = TicketNotification(
                    ticket_id=ticket.id,
                    recipient_id=escalation.escalated_to,
                    notification_type="email",
                    subject=f"Ticket Escalated: {ticket.ticket_number}",
                    message=f"Ticket {ticket.ticket_number} has been escalated to you. Reason: {escalation.reason}",
                    created_at=datetime.utcnow()
                )
                db.add(notification)
                db.commit()

        except Exception as e:
            logger.error(f"Error sending escalation notification: {e}")

    async def _create_activity(
        self,
        db: Session,
        ticket_id: UUID,
        activity_type: str,
        description: str,
        current_user: Optional[CurrentUser]
    ):
        """Create ticket activity log"""
        try:
            activity = TicketActivity(
                ticket_id=ticket_id,
                user_id=current_user.user_id if current_user else None,
                activity_type=activity_type,
                description=description,
                is_system_activity=current_user is None,
                created_at=datetime.utcnow()
            )
            db.add(activity)
            db.commit()
        except Exception as e:
            logger.error(f"Error creating activity log: {e}")
