"""created aproval history table

Revision ID: acb71d2e9572
Revises: 73187e4c358b
Create Date: 2025-04-24 12:35:18.671652

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy import func, TIMESTAMP, ForeignKey
from sqlalchemy import Column, Integer, String, Float


# revision identifiers, used by Alembic.
revision: str = 'acb71d2e9572'
down_revision: Union[str, None] = '73187e4c358b'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    op.create_table(
        'approval_histories',
        Column('id', Integer, primary_key=True),
        <PERSON>umn('reason', sa.String(length=200), nullable=True),
        <PERSON>umn('status', sa.String(length=50), nullable=True),
        <PERSON>um<PERSON>('approval_id', Integer, Foreign<PERSON>ey("approvals.id")),
        <PERSON><PERSON><PERSON>('pay_schedules_id', <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>("pay_schedules.id")),
        <PERSON>um<PERSON>('created_at', sa.DateTime(timezone=True), nullable=False),
    )


def downgrade() -> None:
    op.drop_table('approval_histories')

