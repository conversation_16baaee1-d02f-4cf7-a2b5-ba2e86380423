from core.models.approval_settings import ApprovalSettingsModel
from core.models.pay_schedules import PaySchedulesModel
from core.databases.database import db
from core.repositories.user import UserRepository

class PaySchedulesRepository:
    def __init__(self) -> None:
        pass

    @classmethod
    def createPaySchedules(self, payment_based, name, pay_date, clear_all, number_of_employees,salary_template_id=None, 
    organisation_id=None, employment_type=None):
        pay_schedules = PaySchedulesModel(
            payment_based=payment_based,
            name = name, 
            pay_date = pay_date, 
            clear_all = clear_all, 
            number_of_employees = number_of_employees,
            user_id=UserRepository().authUserId(),
            salary_template_id=salary_template_id,
            organisation_id=organisation_id,
            employment_type=employment_type
        )
        db.session.add(pay_schedules)
        db.session.commit()
        return pay_schedules

    def fetchPaySchedule(cls, page=1, limit=10):
      return (
        PaySchedulesModel.query
        .filter_by(user_id=UserRepository().authUserId())
        .order_by(PaySchedulesModel.timestamp.desc())
        .offset((page - 1) * limit)
        .limit(limit)
        .all()
     )
    
    @classmethod
    def getPaySchedules(self, id):
        return PaySchedulesModel.query.filter_by(user_id=UserRepository().authUserId()).filter(PaySchedulesModel.id == id).first()
    
    @classmethod
    def getPaySchedulesByKeys(self, kwargs):
        return PaySchedulesModel.query.filter_by(user_id=UserRepository().authUserId(),**kwargs).all()

    @classmethod
    def updatePaySchedules(self, id, **kwargs):
        pay_schedules = PaySchedulesModel.query.filter_by(id=id).first()
        if pay_schedules:
            for key, value in kwargs.items():
                setattr(pay_schedules, key, value)
            db.session.commit()
            return pay_schedules
        else:
            return None

    @classmethod
    def deletePaySchedules(self, id):
        PaySchedulesModel.query.filter(PaySchedulesModel.id == id).delete()
        db.session.commit()
        return True        
    
    @classmethod
    def hasUnapprovedPaySchedule(cls):
        user_id = UserRepository().authUserId()
        # Step 1: Check if approval is required for this user
        approval_setting = ApprovalSettingsModel.query.filter_by(user_id=user_id).first()
        if not approval_setting or not approval_setting.status:
            # Approval is turned OFF — allow creation
            return False
        # Step 2: Check if an unapproved schedule exists
        unapproved_schedule = PaySchedulesModel.query.filter_by(is_approved=False, user_id=user_id).first()
        return unapproved_schedule is not None