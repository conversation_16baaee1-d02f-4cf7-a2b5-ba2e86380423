from core.databases.database import db
from core.models.basemodel import ModelBase
from datetime import datetime
from core.utils.helper import get_nigeria_time

class PaySchedulesModel(ModelBase):
    __tablename__ = "pay_schedules"

    id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    name = db.Column(db.String(100), nullable=False)
    number_of_employees = db.Column(db.Integer, nullable=False)
    pay_date = db.Column(db.String(100), nullable=False)
    clear_all = db.Column(db.String(100), nullable=True)
    payment_based = db.Column(db.String(100), nullable=False)
    is_approved = db.Column(db.<PERSON>, default=False, nullable=True)
    approved_date = db.Column(db.DateTime, nullable=True)
    timestamp = db.Column(db.DateTime, nullable=False, default=get_nigeria_time)
    user_id = db.Column(db.Integer, db.ForeignKey((ModelBase.dbSchema() + '.users.id')), nullable=False) 
    
    organisation_id = db.Column(db.Integer, db.ForeignKey((ModelBase.dbSchema() + '.organisations.id'),ondelete='SET NULL'), nullable=True)
    organisation = db.relationship("OrganisationModel", back_populates="pay_schedules")
    salary_template_id = db.Column(db.Integer, db.ForeignKey((ModelBase.dbSchema() + '.salary_templates.id'), ondelete='SET NULL'), nullable=True)
    salary_template = db.relationship("SalaryTemplatesModel", back_populates="pay_schedules")
    employment_type = db.Column(db.String(100), nullable=True)

    
    pay_schedles = db.relationship(
        'PayrollHistoryModel',
        back_populates='pay_schedle',
        passive_deletes=True
    )