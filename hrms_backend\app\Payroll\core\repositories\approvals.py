from core.models.approvals import ApprovalModel
from core.databases.database import db
from core.repositories.user import UserRepository

class ApprovalsRepository:
    def __init__(self) -> None:
        pass

    @classmethod
    def createApprovals(self, employee_id, role, level, pay_schedules_id=None, name=None, email=None, phone=None):
        approvals = ApprovalModel(
            employee_id=employee_id,
            role=role,
            level=level,
            pay_schedules_id=pay_schedules_id,
            name=name,
            email=email,
            phone=phone,
            user_id=UserRepository.authUserId()
        )
        db.session.add(approvals)
        db.session.commit()
        return approvals

    @classmethod
    def getApprovals(self, id):
        return ApprovalModel.query.filter(ApprovalModel.employee_id == id).first()
    
    @classmethod
    def getApprovalLevel(self, level):
        return ApprovalModel.query.filter_by(user_id=UserRepository().authUserId(), level=level).first()
    
    @classmethod
    def fetchAll(self):
        return ApprovalModel.query.filter_by(user_id=UserRepository().authUserId()).order_by(ApprovalModel.timestamp.desc()).all()

    
    @classmethod
    def getApprovalsByKeys(self, kwargs):
        return ApprovalModel.query.filter_by(**kwargs).all()
    
    @classmethod
    def updateApprovals(self, id, **kwargs):
        approvals = ApprovalModel.query.filter_by(id=id).first()
        if approvals:
            for key, value in kwargs.items():
                setattr(approvals, key, value)
            db.session.commit()
            return approvals
        else:
            return None

    @classmethod
    def deleteApprovals(self, id):
        ApprovalModel.query.filter(ApprovalModel.id == id).delete()
        db.session.commit()
        return