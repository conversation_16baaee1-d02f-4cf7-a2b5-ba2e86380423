#!/usr/bin/env python3
"""
Create test users for different roles with correct table structure
"""

import sys
import os
from uuid import uuid4
from datetime import datetime, date

# Add the app directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'app'))

from sqlalchemy.orm import Session
from sqlalchemy import text
from app.db.session import SessionLocal

def create_test_users():
    """Create test users for all roles"""
    db = SessionLocal()
    
    try:
        print("Creating test users for all roles...")
        
        # Test users data
        test_users = [
            {
                'employee_id': 'ADMIN001',
                'first_name': 'Admin',
                'last_name': 'User',
                'email': '<EMAIL>',
                'department': 'IT',
                'position': 'System Administrator'
            },
            {
                'employee_id': 'HR001',
                'first_name': 'HR',
                'last_name': 'Manager',
                'email': '<EMAIL>',
                'department': 'Human Resources',
                'position': 'HR Manager'
            },
            {
                'employee_id': 'MGR001',
                'first_name': 'Manager',
                'last_name': 'User',
                'email': '<EMAIL>',
                'department': 'Operations',
                'position': 'Operations Manager'
            },
            {
                'employee_id': 'EMP001',
                'first_name': 'Employee',
                'last_name': 'User',
                'email': '<EMAIL>',
                'department': 'Operations',
                'position': 'Staff'
            }
        ]
        
        for user_data in test_users:
            # Check if employee already exists
            result = db.execute(text("SELECT employee_id FROM employees WHERE employee_id = :emp_id LIMIT 1"), 
                              {'emp_id': user_data['employee_id']})
            existing = result.fetchone()

            if existing:
                print(f"✅ User {user_data['employee_id']} already exists")
                continue
            
            # Create employee with correct table structure
            employee_id = uuid4()
            user_id = uuid4()
            
            insert_sql = text("""
                INSERT INTO employees (
                    id, user_id, employee_id, first_name, last_name, email,
                    phone, department, position, hire_date, is_active, created_at, updated_at
                ) VALUES (
                    :id, :user_id, :employee_id, :first_name, :last_name, :email,
                    :phone, :department, :position, :hire_date, :is_active, :created_at, :updated_at
                )
            """)
            
            db.execute(insert_sql, {
                'id': employee_id,
                'user_id': user_id,
                'employee_id': user_data['employee_id'],
                'first_name': user_data['first_name'],
                'last_name': user_data['last_name'],
                'email': user_data['email'],
                'phone': '+1234567890',
                'department': user_data['department'],
                'position': user_data['position'],
                'hire_date': datetime.now(),
                'is_active': True,
                'created_at': datetime.utcnow(),
                'updated_at': datetime.utcnow()
            })
            
            print(f"✅ Created user: {user_data['first_name']} {user_data['last_name']} ({user_data['employee_id']}) - {user_data['email']}")
        
        db.commit()
        print("\n✅ All test users created successfully!")
        print("\nTest accounts created:")
        print("- <EMAIL> (Admin)")
        print("- <EMAIL> (HR Manager)") 
        print("- <EMAIL> (Manager)")
        print("- <EMAIL> (Employee)")
        print("\nNote: These are employee records. For login, you'll need to create corresponding auth records.")
        
    except Exception as e:
        print(f"❌ Error creating test users: {e}")
        db.rollback()
        raise
    finally:
        db.close()

if __name__ == "__main__":
    create_test_users()
