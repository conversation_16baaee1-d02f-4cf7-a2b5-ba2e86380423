from core.models.approval_settings import ApprovalSettingsModel
from core.databases.database import db
from core.models.pay_schedules import PaySchedulesModel
from core.models.payroll_history import PayrollHistoryModel
from core.repositories.user import UserRepository

class ApprovalSettingsRepository:
    def __init__(self) -> None:
        pass

    @classmethod
    def createApprovalSettings(self, number_of_approval, status):
        approval_settings = ApprovalSettingsModel(
            number_of_approval=number_of_approval,
            status=status,
            user_id=UserRepository.authUserId()
        )
        db.session.add(approval_settings)
        db.session.commit()
        return approval_settings
    
    @classmethod
    def getFirst(self):
        return ApprovalSettingsModel.query.filter_by(user_id=UserRepository().authUserId()).first()

    @classmethod
    def fetchAll(self):
        return ApprovalSettingsModel.query.filter_by(user_id=UserRepository().authUserId()).order_by(ApprovalSettingsModel.timestamp.desc()).all()

    @classmethod
    def getApprovalSettings(self, id):
        return ApprovalSettingsModel.query.filter(ApprovalSettingsModel.id == id).first()
    
    @classmethod
    def getApprovalSettingsByKeys(self, kwargs):
        return ApprovalSettingsModel.query.filter_by(**kwargs).all()

    @classmethod
    def updateApprovalSettings(self, id, **kwargs):
        approval_settings = ApprovalSettingsModel.query.filter_by(id=id).first()
        if approval_settings:
            for key, value in kwargs.items():
                setattr(approval_settings, key, value)
            db.session.commit()
            return approval_settings
        else:
            return None

    @classmethod
    def deleteApprovalSettings(self, id):
        ApprovalSettingsModel.query.filter(ApprovalSettingsModel.id == id).delete()
        db.session.commit()
        return 
    
    @classmethod
    def getApprovalSettingsByUserId(cls, user_id):
        return ApprovalSettingsModel.query.filter_by(user_id=user_id).first()
    
    @staticmethod
    def get_user_approval_settings(user_id):
        return db.session.query(ApprovalSettingsModel).filter_by(user_id=user_id).first()

    @staticmethod
    def get_first_payroll_history(payroll_ids):
        return db.session.query(PayrollHistoryModel).filter_by(id=payroll_ids[0]).first()

    @staticmethod
    def get_pay_schedule_by_id(schedule_id):
        return db.session.query(PaySchedulesModel).filter_by(id=schedule_id).first()
