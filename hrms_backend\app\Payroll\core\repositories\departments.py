from core.models.departments import DepartmentModel
from core.databases.database import db
from core.repositories.user import UserRepository
from core.models.employees import EmployeeModel
from sqlalchemy.orm import joinedload
from sqlalchemy import func
class DepartmentsRepository:
    def __init__(self) -> None:
        pass

    @classmethod
    def createDepartments(self, name):
        departments = DepartmentModel(
            name=name,
            user_id=UserRepository.authUserId()
        )
        db.session.add(departments)
        db.session.commit()
        return departments
    
    @classmethod
    def get_or_create_department(self, name):
        department = DepartmentModel.query.filter_by(name=name, user_id=UserRepository.authUserId()).first()
        if not department:
            department = self.createDepartments(name)
        return department.id

    @classmethod
    def fetchAll(self):
        return (
            db.session.query(
                DepartmentModel,
                func.count(EmployeeModel.id).label("employee_count")
            )
            .filter(DepartmentModel.user_id == UserRepository().authUserId())
            .outerjoin(EmployeeModel, DepartmentModel.id == EmployeeModel.department_id)
            .group_by(DepartmentModel.id)
            .order_by(DepartmentModel.timestamp.desc())
            .all()
        )

    @classmethod
    def getDepartments(self, id):
        return DepartmentModel.query.filter(DepartmentModel.id == id).first()
    
    @classmethod
    def getDepartmentsByKeys(self, kwargs):
        return DepartmentModel.query.filter_by(user_id=UserRepository().authUserId(), **kwargs).all()

    @classmethod
    def updateDepartments(self, id, **kwargs):
        departments = DepartmentModel.query.filter_by(id=id).first()
        if departments:
            for key, value in kwargs.items():
                setattr(departments, key, value)
            db.session.commit()
            return departments
        else:
            return None

    @classmethod
    def deleteDepartments(self, id):
        DepartmentModel.query.filter(DepartmentModel.id == id).delete()
        db.session.commit()
        return