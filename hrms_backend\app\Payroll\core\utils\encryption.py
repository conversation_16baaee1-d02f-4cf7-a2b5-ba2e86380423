from cryptography.fernet import Fernet
import base64
import os
from dotenv import load_dotenv

load_dotenv() 

ENCRYPTION_KEY = os.getenv("SECRET_KEY_ENCRYPT_DECRYPT")
if not ENCRYPTION_KEY:
    raise ValueError("No ENCRYPTION_KEY found in environment variables")

# Ensure the key is URL-safe base64 (32 bytes)
fernet = Fernet(base64.urlsafe_b64encode(ENCRYPTION_KEY.encode()[:32].ljust(32, b'=')))

def encrypt(data: str) -> str:
    """Encrypt a string using AES (Fernet)."""
    return fernet.encrypt(data.encode()).decode()

def decrypt(encrypted_data: str) -> str:
    """Decrypt an encrypted string."""
    return fernet.decrypt(encrypted_data.encode()).decode()