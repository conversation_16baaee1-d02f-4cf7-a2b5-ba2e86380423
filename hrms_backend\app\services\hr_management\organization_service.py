from fastapi import Fast<PERSON><PERSON>, HTTPException, Depends, status, Header
from pydantic import BaseModel, <PERSON>
from typing import Optional, List
from uuid import UUID, uuid4
from datetime import datetime, date
from enum import Enum
import uvicorn

app = FastAPI(title="Organization Service", version="1.0.0")

# Enums
class OrganizationStatus(str, Enum):
    PENDING_APPROVAL = "PENDING_APPROVAL"
    APPROVED = "APPROVED"

# Models
class Organization(BaseModel):
    id: UUID = Field(default_factory=uuid4)
    name: str
    address: Optional[str]
    status: OrganizationStatus = OrganizationStatus.PENDING_APPROVAL
    created_at: datetime = Field(default_factory=datetime.now)

class License(BaseModel):
    id: UUID = Field(default_factory=uuid4)
    organization_id: UUID
    license_key: str
    issue_date: datetime = Field(default_factory=datetime.now)
    expiry_date: datetime
    is_active: bool = True

# Request Models
class OrganizationRequestDTO(BaseModel):
    name: str
    address: Optional[str]
    admin_user_ids: List[UUID] # Assuming this is how admin users are passed

class LicenseRequest(BaseModel):
    organization_id: UUID
    expiry_date: datetime

# Dummy database (replace with actual database)
organizations_db = []
licenses_db = []

# Services (simplified for now)
class OrganizationService:
    def create_organization(self, dto: OrganizationRequestDTO):
        new_org = Organization(name=dto.name, address=dto.address)
        organizations_db.append(new_org)
        # In a real scenario, you'd associate admin_user_ids with the organization
        return {"message": "Organization created successfully", "organization_id": new_org.id}

    def get_organization_by_id(self, org_id: UUID):
        for org in organizations_db:
            if org.id == org_id:
                return org
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Organization not found")

    def get_all_organizations(self):
        return organizations_db

    def approve_organization(self, org_id: UUID, user_id: UUID):
        # In a real scenario, you'd validate if the user_id has Super Admin privileges
        for org in organizations_db:
            if org.id == org_id:
                org.status = OrganizationStatus.APPROVED
                return {"message": f"Organization {org_id} approved by {user_id}"}
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Organization not found")

class LicenseService:
    def generate_license(self, request: LicenseRequest):
        # Dummy license key generation
        license_key = f"LIC-{uuid4()}"
        new_license = License(organization_id=request.organization_id, license_key=license_key, expiry_date=request.expiry_date)
        licenses_db.append(new_license)
        return {"message": "License generated successfully", "license_key": license_key}

organization_service = OrganizationService()
license_service = LicenseService()

# Endpoints
@app.post("/", summary="Create new organization with admin list", response_model=dict)
async def create_organization(dto: OrganizationRequestDTO):
    return organization_service.create_organization(dto)

@app.get("/health")
async def health_check():
    return {"status": "healthy", "service": "organization-service"}

@app.get("/{org_id}", summary="Get organization by ID", response_model=Organization)
async def get_organization_by_id(org_id: UUID):
    return organization_service.get_organization_by_id(org_id)

@app.get("/", summary="Get all organizations", response_model=List[Organization])
async def get_all_organizations():
    return organization_service.get_all_organizations()

@app.put("/approve/{org_id}", summary="Approve organization by Super Admin", response_model=dict)
async def approve_organization(org_id: UUID, x_user_id: UUID = Header(..., alias="X-USER-ID")):
    return organization_service.approve_organization(org_id, x_user_id)

@app.post("/license", summary="Generates and assigns a new software license to an organization.", response_model=dict)
async def generate_license(request: LicenseRequest):
    return license_service.generate_license(request)

@app.get("/health")
async def health_check():
    return {"status": "healthy", "service": "organization-service"}

if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8082)

