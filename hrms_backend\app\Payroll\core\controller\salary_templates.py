from flask import url_for, jsonify, request
from flask.views import MethodView
from core.repositories.benefits import BenefitsRepository
from flask_smorest import Blueprint, abort
from core.middleware import roles_required
from core.repositories.employees_component import EmployeesComponentRepository
from core.services.employee import EmployeeService
from schemas import AssignTemplateSchema, EmployeeSchema, SalaryComponentSchema, SalaryTemplateSchema, SalaryTemplateUpdateSchema, RemoveComponentFromTemplate, RemoveBenefitFromTemplate
from sqlalchemy.orm.exc import NoResultFound
from sqlalchemy.exc import SQLAlchemyError, IntegrityError
from core.databases.database import db
import core.utils.response_message as RESPONSEMESSAGE
from core.services.salary_templates import SalaryTemplatesService
from core.services.salary_components import SalaryComponentsService
from core.services.benefits import BenefitsService
from core.services.designations import DesignationsService
from core.services.departments import DepartmentsService
from core.repositories.salary_templates_component import SalaryTemplatesComponentRepository
from core.repositories.salary_templates_benefit import SalaryTemplatesBenefitRepository
from core.utils.responseBuilder import ResponseBuilder
from core.services.component_processor import ComponentProcessor
from core.services.tax_calculator_service import TaxCalculatorService


from core.services.component_processor import ComponentProcessor

blueprint = Blueprint("salary_template", __name__, description="Operations for Salary Templates")
    
@blueprint.route("/salary_template/<id>")
class SalaryTemplate(MethodView):
    @roles_required(['admin'])
    @blueprint.response(200, SalaryTemplateSchema)
    def get(self, id):
        service = SalaryTemplatesService()
        salary_template = service.getSalaryTemplates(id)
        if not salary_template:
            abort(401, message="Template does not exist")
        template_details = SalaryTemplateSchema().dump(salary_template)
        component_processor = ComponentProcessor(None, template_details.get("gross_pay"), 0, False,
                                                 salary_benefit=template_details.get("salary_template_benefits"), 
                                                 salary_component=template_details.get("salary_template_components")
                                                 )
        procssed_component = component_processor.generate_salary_response()
        template_data = {**template_details, **procssed_component}
        exclude_dates  = { "salary_template_components", "salary_template_benefits"}
        filter_template_data  = {k: v for k, v in template_data.items() if k not in exclude_dates}

        return ResponseBuilder(data=filter_template_data, status_code=200).build()    
    
    @roles_required(['admin'])
    def delete(self, id):
        service = SalaryTemplatesService()
        salary_template = service.getSalaryTemplates(id)
        if not salary_template:
            abort(400, message="Template does not exist")
        
        if salary_template.employees:
            abort(400, message=f"Cannot delete template. It is still assigned to employees.")

        attached_template = SalaryTemplatesComponentRepository().getAttachedTemplate(id)

        if len(attached_template) > 0:
            abort(400, message="Cannot delete salary template with existing salary component")
            
        service.deleteSalaryTemplates(id)
        return {"message" : "Template deleted"}
    
    @roles_required(['admin'])
    @blueprint.arguments(SalaryTemplateUpdateSchema)
    @blueprint.response(201, SalaryTemplateUpdateSchema)
    def put(self, data, id):
        service = SalaryTemplatesService()
        try:
            payload = SalaryTemplateUpdateSchema().dump(data)
            updated_template = service.save_salary_template(payload, template_id=id)
            return updated_template
        except SQLAlchemyError as e:
        # Log the error
            print(f"SQLAlchemy Error: {e}")
            abort(500, message="Database error occurred while updating the template")
        except Exception as e:
            # Handle unexpected errors
            print(f"Unexpected Error: {e}")
            abort(500, message="An unexpected error occurred")

@blueprint.route("/salary_template")
class SalaryTemplateList(MethodView):
    @roles_required(['admin'])
    @blueprint.response(200, SalaryTemplateSchema)
    def get(self):
        template_service = SalaryTemplatesService()
        template_list, total_template = template_service.fetchAll()
        template_schema = SalaryTemplateSchema(many=True)
        template_list = template_schema.dump(template_list)
        return ResponseBuilder(data=template_list, status_code=200, total=total_template).build()
    
    @roles_required(['admin'])
    @blueprint.arguments(SalaryTemplateSchema)
    @blueprint.response(200, SalaryTemplateSchema)
    def post(self, data):
        service = SalaryTemplatesService()
        salary_template = service.getSalaryTemplatesByKey({"template_name": data['template_name']})
        if salary_template:
            abort(400, message="Template already exists")

        try:
            payload = SalaryTemplateSchema().dump(data)
            create_template = service.save_salary_template(payload)
            return create_template
        except SQLAlchemyError as e:
        # Log the error
            print(f"SQLAlchemy Error: {e}")
            abort(500, message="Database error occurred while updating the template")
        # except Exception as e:
        #     # Handle unexpected errors
        #     print(f"Unexpected Error: {e}")
        #     abort(500, message="An unexpected error occurred")

@blueprint.route("/salary_template/<id>/salary_component")
class SalaryTemplateComponent(MethodView):
    @roles_required(['admin'])
    @blueprint.arguments(RemoveComponentFromTemplate)
    @blueprint.response(200)
    def post(self, data, id ):
        selected_components = data.get("components_id")

        service = SalaryTemplatesService()
        salary_template = service.getSalaryTemplates(id)
        if not salary_template:
            abort(401, message="Template does not exist")

        template_compo = salary_template.salary_template_components
        temp_comp_id = [component.id for component in template_compo if component.salary_component.id in selected_components]
        try:
            SalaryTemplatesComponentRepository().delete(temp_comp_id)
        except Exception as e:
            print(e)
        return {"message" : "Components removed from Template"}
    
@blueprint.route("/salary_template/<id>/benefit")
class SalaryTemplateBenefit(MethodView):
    @roles_required(['admin'])
    @blueprint.arguments(RemoveBenefitFromTemplate)
    @blueprint.response(200)
    def post(self, data, id ):
        selected_benefits = data.get("benefits_id")

        service = SalaryTemplatesService()
        salary_template = service.getSalaryTemplates(id)
        if not salary_template:
            abort(401, message="Template does not exist")

        template_benefit = salary_template.salary_template_benefits
        temp_benefit_id = [benefit.id for benefit in template_benefit if benefit.benefit.id in selected_benefits]
        try:
            SalaryTemplatesBenefitRepository().delete(temp_benefit_id)
        except Exception as e:
            print(e)
        return {"message" : "Benefits removed from Template"}





@blueprint.route("/assign_template")
class AssignTemplate(MethodView):
    @roles_required(['admin'])
    @blueprint.arguments(AssignTemplateSchema)
    @blueprint.response(200)
    def post(self, data):
        """
        Assign a salary template (including salary components and benefits) to a list of employees.
        """
        employeeService = EmployeeService()
        salaryComponentService = SalaryTemplatesService()
        employeeComponentRepo = EmployeesComponentRepository()
        benefitsRepo = BenefitsRepository()
        enefitsService = BenefitsService()
        departmentService = DepartmentsService()
        designationService = DesignationsService()

        # Get the salary template ID from request
        template_id = data.get("template_id")
        if not template_id:
            abort(400, message="Salary template ID is required")

        # Validate salary template
        salary_template = salaryComponentService.getSalaryTemplates(template_id)
        if not salary_template:
            abort(404, message="Salary template not found")

        # Initialize employee list
        employees = []

        # Get filter criteria from request
        employee_ids = data.get("employee_ids")
        department_id = data.get("department_id")
        designation_id = data.get("designation_id")
        employment_type = data.get("employment_type")


        # Retrieve employees based on provided filters
        if employee_ids:
            employees = employeeService.getEmployeesByIds(employee_ids)
        
        elif department_id:
            department = departmentService.getDepartments(department_id)
            if not department:
                abort(404, message="Department not found")
            employees = employeeService.getEmployeesByDepartment(department_id)

        elif designation_id:
            designation = designationService.getDesignations(designation_id)
            if not designation:
                abort(404, message="Designation not found")
            employees = employeeService.getEmployeesByDesignation(designation_id)

        elif employment_type:
            employees = employeeService.getEmployeesByType(employment_type)

        else:
            abort(400, message="No valid employee filter provided (department, designation, or employee type)")

        if not employees:
            abort(404, message="No employees found for the given criteria")
            
        print("employees......", employees)

        # Extract salary component IDs from the template
        component_ids = [component.salary_component_id for component in salary_template.salary_template_components]
        # print("Extracted component IDs:", component_ids)
        # if not component_ids:
        #     abort(400, message="Salary template has no components")
            
        # Extract benefit component IDs from the template
        benefit_ids = [benefit.benefits_id for benefit in salary_template.salary_template_benefits]
        # if not benefit_ids:
        #     abort(400, message="Salary template has no benefits")

        try:
            employee_ids = [employee.id for employee in employees]  # Collect all employee IDs

            # Assign salary components
            for employee in employees:
                assigned_component_ids = []
                assigned_benefit_ids = []
    
                for component_id in component_ids:
                    existing_component = employeeComponentRepo.getAttachedEmployeeComponent(employee.id, component_id)
                    if not existing_component:
                        employeeComponentRepo.create(employee.id, component_id)
                    assigned_component_ids.append(component_id)

                # Assign benefits
                for benefit_id in benefit_ids:
                    existing_benefit = benefitsRepo.getAttachedEmployeeBenefit(employee.id, benefit_id)
                    if not existing_benefit:
                        benefitsRepo.assign(employee.id, benefit_id)
                    assigned_benefit_ids.append(benefit_id)
                        
                if assigned_component_ids or assigned_benefit_ids:
                    emplpyee_details = EmployeeSchema().dump(employee)
                    employee_compoents = emplpyee_details["employee_components"]
                    employee_benefits = emplpyee_details["employee_benefits"]
                    tax_type = emplpyee_details["tax_type"]

                    component_processor = ComponentProcessor(
                        employee.id, employee.gross_pay, 0, False, employee_benefits, employee_compoents
                    )
                    processed_salary = component_processor.generate_salary_response()

                    salary_components = [(component.salary_component.id,component.salary_component.component_name.lower()) for component in employee.employee_components]
                    pension_id = next((comp_id for comp_id, name in salary_components if name == "pension"), None)
                    nhf_id = next((comp_id for comp_id, name in salary_components if name == "nhf"), None)

                    pension_data = SalaryComponentsService().getSalaryComponents(pension_id)
                    pension_object = SalaryComponentSchema().dump(pension_data)

                    nhf_data = SalaryComponentsService().getSalaryComponents(nhf_id)
                    nhf_object = SalaryComponentSchema().dump(nhf_data)

                    annual_pension = 0
                    annual_nhf = 0
                    if pension_object:
                        processed_pension = component_processor._create_component_data(pension_object)
                        annual_pension = float(processed_pension["total_monthly_calculation"].replace(",", "")) * 12
                        # print(f"processed_pension {processed_pension}")
                        # print(f"annual_pension {annual_pension}")

                    if nhf_object:
                        processed_nhf = component_processor._create_component_data(nhf_object)
                        annual_nhf = float(processed_nhf["total_monthly_calculation"].replace(",", "")) * 12
                        # print(f"processed_nhf {processed_nhf}")
                        # print(f"annual_nhf {annual_nhf}")

                    monthly_earnings = float(processed_salary["total_earnings"].replace(",",""))
                    annual_earnings = monthly_earnings * 12
                    total_non_taxable_monthly = processed_salary["total_non_taxable"]
                    total_non_taxable_annual = total_non_taxable_monthly * 12
                    total_monthly_statutory = processed_salary["total_statutory"]
                    total_annual_statutory = total_monthly_statutory * 12
                    total_monthly_other_deduction = processed_salary["total_other_deduction"] 
                    total_annual_other_deduction = total_monthly_other_deduction * 12
                    total_deduction = float(processed_salary["total_deduction"].replace(",",""))

                    calculate_tax = TaxCalculatorService("Nigeria", tax_type)
                    annual_tax = round(calculate_tax.calculate_annual_tax(annual_earnings, annual_pension, annual_nhf), 2)
                    monthly_tax = round(calculate_tax.calculate_monthly_tax(annual_tax),2)
                    all_deductions = total_deduction + monthly_tax
                    net_pay = round(monthly_earnings - (monthly_tax)) + total_non_taxable_monthly - total_monthly_statutory - total_monthly_other_deduction
                    # print(f"Bross pay :", employee.gross_pay)
                    # print(f"Total Monthly Earnings :", monthly_earnings)
                    # print(f"Total Annual Earnings :", annual_earnings)
                    # print(f"Annual Tax :", annual_tax)
                    # print(f"Tax type:", tax_type)
                    # print(f"Monthly Tax :", monthly_tax)
                    # print(f"Total statutory Deductions :", total_deduction)
                    # print(f"total_monthly_statutory :", total_monthly_statutory)
                    # print(f"total_annual_statutory :", total_annual_statutory)
                    # print("total_non_taxable_monthly:", total_non_taxable_monthly)
                    # print("total_non_taxable_annual:", total_non_taxable_annual)
                    # print(f"total_monthly_other_deduction :", total_monthly_other_deduction)
                    # print(f"total_annual_other_deduction :", total_annual_other_deduction)
                    # print(f"Net pay :", net_pay)
                    employee_record = {
                        "monthly_tax": monthly_tax,
                        "annual_tax": round(annual_tax, 2),
                        "total_taxable_monthly_sum": monthly_earnings,
                        "total_taxable_annual_sum": annual_earnings,
                        "total_non_taxable_monthly_sum": total_non_taxable_monthly,
                        "total_non_taxable_annual_sum": total_non_taxable_annual,
                        "total_statutory_monthly_sum": total_monthly_statutory,
                        "total_statutory_annual_sum": total_annual_statutory,
                        "total_other_deductions_monthly_sum": total_monthly_other_deduction,
                        "total_other_deductions_annual_sum": total_annual_other_deduction,
                        "netpay": net_pay
                    }
                    employeeService.updateEmployee(employee.id, employee_record)
    

                    # Bulk update all employees at once
                    employeeService.updateEmployeeTemplate(employee_ids, template_id)

            return {"message": "Salary components and benefits assigned to employees successfully"}, 200
        except SQLAlchemyError as e:
            print(e)
            abort(500, message="Error while assigning salary components and benefits")


