from sqlalchemy import Column, String, <PERSON><PERSON>an, DateTime, Text, Integer, ForeignKey, Enum as SQLE<PERSON>, Float, Date
from sqlalchemy.dialects.postgresql import UUID, JSONB
from sqlalchemy.orm import relationship
from uuid import uuid4
from datetime import datetime, date
import enum

from ..base import BaseModel, AuditMixin


class ReportType(str, enum.Enum):
    """Report type enumeration"""
    EMPLOYEE_REPORT = "employee_report"
    ATTENDANCE_REPORT = "attendance_report"
    LEAVE_REPORT = "leave_report"
    PAYROLL_REPORT = "payroll_report"
    PERFORMANCE_REPORT = "performance_report"
    RECRUITMENT_REPORT = "recruitment_report"
    TRAINING_REPORT = "training_report"
    COMPLIANCE_REPORT = "compliance_report"
    CUSTOM_REPORT = "custom_report"


class ReportFormat(str, enum.Enum):
    """Report format enumeration"""
    PDF = "pdf"
    EXCEL = "excel"
    CSV = "csv"
    JSON = "json"


class ReportFrequency(str, enum.Enum):
    """Report frequency enumeration"""
    DAILY = "daily"
    WEEKLY = "weekly"
    MONTHLY = "monthly"
    QUARTERLY = "quarterly"
    YEARLY = "yearly"
    ON_DEMAND = "on_demand"


class ReportStatus(str, enum.Enum):
    """Report generation status"""
    PENDING = "pending"
    GENERATING = "generating"
    COMPLETED = "completed"
    FAILED = "failed"
    EXPIRED = "expired"


class ChartType(str, enum.Enum):
    """Chart type enumeration"""
    BAR = "bar"
    LINE = "line"
    PIE = "pie"
    DOUGHNUT = "doughnut"
    AREA = "area"
    SCATTER = "scatter"
    TABLE = "table"


class ReportTemplate(BaseModel, AuditMixin):
    """Report template model"""
    __tablename__ = "report_templates"

    # Basic information
    name = Column(String(200), nullable=False, index=True)
    description = Column(Text, nullable=True)
    report_type = Column(SQLEnum(ReportType), nullable=False)
    category = Column(String(100), nullable=True, index=True)
    
    # Data source configuration
    data_sources = Column(JSONB, nullable=False)  # Array of data source tables/views
    filters = Column(JSONB, nullable=True)  # Default filters
    columns = Column(JSONB, nullable=False)  # Report columns configuration
    
    # Visualization
    charts = Column(JSONB, nullable=True)  # Chart configurations
    layout = Column(JSONB, nullable=True)  # Report layout configuration
    
    # Access control
    roles_allowed = Column(JSONB, nullable=False)  # Array of allowed roles
    departments_allowed = Column(JSONB, nullable=True)  # Array of allowed department IDs
    
    # Scheduling
    is_scheduled = Column(Boolean, default=False, nullable=False)
    frequency = Column(SQLEnum(ReportFrequency), nullable=True)
    schedule_config = Column(JSONB, nullable=True)  # Schedule configuration
    
    # Output settings
    default_format = Column(SQLEnum(ReportFormat), nullable=False, default=ReportFormat.PDF)
    auto_email = Column(Boolean, default=False, nullable=False)
    email_recipients = Column(JSONB, nullable=True)  # Array of email addresses
    
    # Status and usage
    is_active = Column(Boolean, default=True, nullable=False)
    usage_count = Column(Integer, default=0, nullable=False)
    last_generated = Column(DateTime, nullable=True)
    
    # Organization
    organization_id = Column(UUID(as_uuid=True), nullable=False, index=True)
    
    # Relationships
    reports = relationship("Report", back_populates="template")
    schedules = relationship("ReportSchedule", back_populates="template")

    def __repr__(self):
        return f"<ReportTemplate(name='{self.name}', type='{self.report_type}')>"


class Report(BaseModel, AuditMixin):
    """Generated report model"""
    __tablename__ = "reports"

    # References
    template_id = Column(UUID(as_uuid=True), ForeignKey("report_templates.id"), nullable=False)
    
    # Report information
    name = Column(String(200), nullable=False)
    report_type = Column(SQLEnum(ReportType), nullable=False)
    format = Column(SQLEnum(ReportFormat), nullable=False)
    
    # Generation details
    status = Column(SQLEnum(ReportStatus), nullable=False, default=ReportStatus.PENDING)
    generated_by = Column(UUID(as_uuid=True), ForeignKey("employees.id"), nullable=False)
    generated_at = Column(DateTime, nullable=True)
    
    # Parameters and filters used
    parameters = Column(JSONB, nullable=True)
    filters = Column(JSONB, nullable=True)
    date_range = Column(JSONB, nullable=True)
    
    # File information
    file_url = Column(String(500), nullable=True)
    file_name = Column(String(255), nullable=True)
    file_size = Column(Integer, nullable=True)  # Size in bytes
    file_hash = Column(String(64), nullable=True)  # SHA-256 hash
    
    # Expiration
    expires_at = Column(DateTime, nullable=True)
    
    # Error handling
    error_message = Column(Text, nullable=True)
    retry_count = Column(Integer, default=0, nullable=False)
    
    # Statistics
    generation_time_ms = Column(Integer, nullable=True)
    record_count = Column(Integer, nullable=True)
    
    # Organization
    organization_id = Column(UUID(as_uuid=True), nullable=False, index=True)
    
    # Relationships
    template = relationship("ReportTemplate", back_populates="reports")
    generated_by_employee = relationship("Employee")

    def __repr__(self):
        return f"<Report(name='{self.name}', status='{self.status}')>"


class Dashboard(BaseModel, AuditMixin):
    """Dashboard model"""
    __tablename__ = "dashboards"

    # Basic information
    name = Column(String(200), nullable=False, index=True)
    description = Column(Text, nullable=True)
    category = Column(String(100), nullable=True, index=True)
    
    # Layout and widgets
    layout = Column(JSONB, nullable=False)  # Dashboard layout configuration
    widgets = Column(JSONB, nullable=False)  # Array of widget configurations
    
    # Access control
    is_public = Column(Boolean, default=False, nullable=False)
    roles_allowed = Column(JSONB, nullable=False)  # Array of allowed roles
    departments_allowed = Column(JSONB, nullable=True)  # Array of allowed department IDs
    
    # Refresh settings
    auto_refresh = Column(Boolean, default=False, nullable=False)
    refresh_interval = Column(Integer, nullable=True)  # Refresh interval in minutes
    
    # Status and usage
    is_active = Column(Boolean, default=True, nullable=False)
    view_count = Column(Integer, default=0, nullable=False)
    last_accessed = Column(DateTime, nullable=True)
    
    # Creator
    created_by = Column(UUID(as_uuid=True), ForeignKey("employees.id"), nullable=False)
    
    # Organization
    organization_id = Column(UUID(as_uuid=True), nullable=False, index=True)
    
    # Relationships
    creator = relationship("Employee")

    def __repr__(self):
        return f"<Dashboard(name='{self.name}', public='{self.is_public}')>"


class KPI(BaseModel, AuditMixin):
    """Key Performance Indicator model"""
    __tablename__ = "kpis"

    # Basic information
    name = Column(String(200), nullable=False, index=True)
    description = Column(Text, nullable=True)
    category = Column(String(100), nullable=False, index=True)
    
    # Calculation
    calculation_method = Column(String(100), nullable=False)  # sql, formula, api
    data_source = Column(String(200), nullable=False)  # Table or API endpoint
    formula = Column(Text, nullable=False)  # SQL query or calculation formula
    
    # Targets and thresholds
    target_value = Column(Float, nullable=True)
    warning_threshold = Column(Float, nullable=True)
    critical_threshold = Column(Float, nullable=True)
    
    # Display settings
    unit = Column(String(50), nullable=True)  # %, $, count, etc.
    format_type = Column(String(50), nullable=False, default="number")  # number, currency, percentage
    chart_type = Column(SQLEnum(ChartType), nullable=False, default=ChartType.LINE)
    
    # Update frequency
    update_frequency = Column(String(50), nullable=False, default="daily")
    last_calculated = Column(DateTime, nullable=True)
    next_calculation = Column(DateTime, nullable=True)
    
    # Status
    is_active = Column(Boolean, default=True, nullable=False)
    
    # Organization
    organization_id = Column(UUID(as_uuid=True), nullable=False, index=True)
    
    # Relationships
    values = relationship("KPIValue", back_populates="kpi")

    def __repr__(self):
        return f"<KPI(name='{self.name}', category='{self.category}')>"


class KPIValue(BaseModel):
    """KPI value model for storing calculated values"""
    __tablename__ = "kpi_values"

    # References
    kpi_id = Column(UUID(as_uuid=True), ForeignKey("kpis.id"), nullable=False, index=True)
    
    # Value information
    value = Column(Float, nullable=False)
    target_value = Column(Float, nullable=True)
    
    # Period
    period_start = Column(Date, nullable=False)
    period_end = Column(Date, nullable=False)
    
    # Status and trends
    status = Column(String(50), nullable=False)  # on_target, warning, critical
    trend = Column(String(50), nullable=True)  # up, down, stable
    change_percentage = Column(Float, nullable=True)
    
    # Calculation details
    calculated_at = Column(DateTime, nullable=False, default=datetime.utcnow)
    calculation_time_ms = Column(Integer, nullable=True)
    data_points = Column(Integer, nullable=True)  # Number of data points used
    
    # Organization
    organization_id = Column(UUID(as_uuid=True), nullable=False, index=True)
    
    # Relationships
    kpi = relationship("KPI", back_populates="values")

    def __repr__(self):
        return f"<KPIValue(kpi_id='{self.kpi_id}', value='{self.value}', period='{self.period_start}')>"


class ReportSchedule(BaseModel, AuditMixin):
    """Report schedule model"""
    __tablename__ = "report_schedules"

    # References
    template_id = Column(UUID(as_uuid=True), ForeignKey("report_templates.id"), nullable=False)
    
    # Schedule information
    name = Column(String(200), nullable=False)
    frequency = Column(SQLEnum(ReportFrequency), nullable=False)
    
    # Schedule configuration
    schedule_config = Column(JSONB, nullable=False)  # Cron-like configuration
    timezone = Column(String(50), nullable=False, default="UTC")
    
    # Recipients
    email_recipients = Column(JSONB, nullable=False)  # Array of email addresses
    
    # Parameters
    parameters = Column(JSONB, nullable=True)
    filters = Column(JSONB, nullable=True)
    format = Column(SQLEnum(ReportFormat), nullable=False, default=ReportFormat.PDF)
    
    # Status and execution
    is_active = Column(Boolean, default=True, nullable=False)
    last_run = Column(DateTime, nullable=True)
    next_run = Column(DateTime, nullable=True)
    run_count = Column(Integer, default=0, nullable=False)
    failure_count = Column(Integer, default=0, nullable=False)
    last_error = Column(Text, nullable=True)
    
    # Organization
    organization_id = Column(UUID(as_uuid=True), nullable=False, index=True)
    
    # Relationships
    template = relationship("ReportTemplate", back_populates="schedules")

    def __repr__(self):
        return f"<ReportSchedule(name='{self.name}', frequency='{self.frequency}')>"


class AnalyticsQuery(BaseModel):
    """Analytics query model for storing and caching queries"""
    __tablename__ = "analytics_queries"

    # Query information
    name = Column(String(200), nullable=True)
    query_hash = Column(String(64), nullable=False, unique=True, index=True)  # SHA-256 hash of query
    
    # Query details
    metric = Column(String(100), nullable=False)
    dimensions = Column(JSONB, nullable=False)  # Array of dimensions
    filters = Column(JSONB, nullable=True)
    aggregation = Column(String(50), nullable=False, default="count")
    
    # Results
    result_data = Column(JSONB, nullable=True)  # Cached results
    total_records = Column(Integer, nullable=True)
    execution_time_ms = Column(Integer, nullable=True)
    
    # Cache information
    cached_at = Column(DateTime, nullable=True)
    cache_expires_at = Column(DateTime, nullable=True)
    hit_count = Column(Integer, default=0, nullable=False)
    
    # Query metadata
    created_by = Column(UUID(as_uuid=True), ForeignKey("employees.id"), nullable=False)
    
    # Organization
    organization_id = Column(UUID(as_uuid=True), nullable=False, index=True)
    
    # Relationships
    creator = relationship("Employee")

    def __repr__(self):
        return f"<AnalyticsQuery(metric='{self.metric}', hash='{self.query_hash[:8]}')>"
