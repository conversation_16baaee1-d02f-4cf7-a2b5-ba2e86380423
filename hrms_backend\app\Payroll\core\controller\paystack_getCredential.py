from flask import url_for, jsonify,request,current_app
from flask.views import Method<PERSON>iew
from flask_smorest import Blueprint, abort
from core.middleware import roles_required
from core.repositories.user import UserRepository
from core.models.paystack_credential import Paystack_IntegrationModel
from ..repositories.user import UserRepository
from core.databases.database import db

blueprint = Blueprint("PaystackCredentials", __name__, description="Operations to get paystack credentials from the database")

@blueprint.route("/paystack_credentials")
class Paystack_Credentials(MethodView):
    @roles_required(['admin'])
    def get(self):

        user = UserRepository().authUser()#Gets the current login user data
        user_id=user.id
        
        paystack_credential =  Paystack_IntegrationModel.query.filter_by(user_id=user_id).first()
        
        if not paystack_credential:
            return jsonify({"message": "Paystack credentials not found"}), 404

        elif paystack_credential:
            data = {
                 "paystack_key": paystack_credential.paystack_key,
                "user_id": paystack_credential.user_id
             }
            
            return jsonify({
                "data":data
               
            }), 200  # Return an HTTP 200 status for success

        
        

        
      


    