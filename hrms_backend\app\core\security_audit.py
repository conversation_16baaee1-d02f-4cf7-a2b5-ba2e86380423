"""
Security audit and hardening utilities for the HRMS system
"""

import logging
import hashlib
import secrets
import re
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
from fastapi import Request, HTTPException, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
import jwt
from passlib.context import Crypt<PERSON>ontext
from passlib.hash import bcrypt

from .config import settings

logger = logging.getLogger(__name__)


class SecurityAuditor:
    """Security audit and hardening utilities"""
    
    def __init__(self):
        self.pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")
        self.security_issues = []
    
    def audit_password_policy(self, password: str) -> Dict[str, Any]:
        """Audit password against security policy"""
        issues = []
        score = 0
        
        # Length check
        if len(password) < 8:
            issues.append("Password must be at least 8 characters long")
        elif len(password) >= 12:
            score += 2
        else:
            score += 1
        
        # Character variety checks
        if not re.search(r'[a-z]', password):
            issues.append("Password must contain lowercase letters")
        else:
            score += 1
            
        if not re.search(r'[A-Z]', password):
            issues.append("Password must contain uppercase letters")
        else:
            score += 1
            
        if not re.search(r'\d', password):
            issues.append("Password must contain numbers")
        else:
            score += 1
            
        if not re.search(r'[!@#$%^&*(),.?":{}|<>]', password):
            issues.append("Password must contain special characters")
        else:
            score += 1
        
        # Common password patterns
        common_patterns = [
            r'123456', r'password', r'admin', r'qwerty',
            r'abc123', r'letmein', r'welcome', r'monkey'
        ]
        
        for pattern in common_patterns:
            if re.search(pattern, password.lower()):
                issues.append("Password contains common patterns")
                score -= 2
                break
        
        # Sequential characters
        if re.search(r'(012|123|234|345|456|567|678|789|890)', password):
            issues.append("Password contains sequential numbers")
            score -= 1
            
        if re.search(r'(abc|bcd|cde|def|efg|fgh|ghi|hij|ijk|jkl|klm|lmn|mno|nop|opq|pqr|qrs|rst|stu|tuv|uvw|vwx|wxy|xyz)', password.lower()):
            issues.append("Password contains sequential letters")
            score -= 1
        
        # Calculate strength
        max_score = 8
        strength_percentage = max(0, min(100, (score / max_score) * 100))
        
        if strength_percentage >= 80:
            strength = "Strong"
        elif strength_percentage >= 60:
            strength = "Medium"
        elif strength_percentage >= 40:
            strength = "Weak"
        else:
            strength = "Very Weak"
        
        return {
            "strength": strength,
            "score": score,
            "max_score": max_score,
            "percentage": strength_percentage,
            "issues": issues,
            "is_valid": len(issues) == 0
        }
    
    def audit_jwt_configuration(self) -> Dict[str, Any]:
        """Audit JWT configuration for security issues"""
        issues = []
        recommendations = []
        
        # Check secret key strength
        secret_key = settings.SECRET_KEY
        if len(secret_key) < 32:
            issues.append("JWT secret key is too short (minimum 32 characters)")
        
        if secret_key == "your-super-secret-jwt-key-change-this-in-production":
            issues.append("JWT secret key is using default value")
        
        # Check for weak patterns in secret key
        if re.match(r'^[a-zA-Z0-9]+$', secret_key):
            recommendations.append("Consider using special characters in JWT secret key")
        
        # Check token expiration
        token_expire_minutes = getattr(settings, 'ACCESS_TOKEN_EXPIRE_MINUTES', 30)
        if token_expire_minutes > 60:
            recommendations.append("Consider shorter JWT token expiration time")
        elif token_expire_minutes > 1440:  # 24 hours
            issues.append("JWT token expiration time is too long")
        
        # Check algorithm
        algorithm = getattr(settings, 'ALGORITHM', 'HS256')
        if algorithm in ['none', 'HS256']:
            if algorithm == 'none':
                issues.append("JWT algorithm 'none' is insecure")
            else:
                recommendations.append("Consider using RS256 for better security")
        
        return {
            "issues": issues,
            "recommendations": recommendations,
            "secret_key_length": len(secret_key),
            "token_expire_minutes": token_expire_minutes,
            "algorithm": algorithm
        }
    
    def audit_database_security(self) -> Dict[str, Any]:
        """Audit database configuration for security issues"""
        issues = []
        recommendations = []
        
        db_url = settings.DATABASE_URL
        
        # Check for credentials in URL
        if '@' in db_url:
            # Extract credentials
            try:
                protocol_part, rest = db_url.split('://', 1)
                if '@' in rest:
                    creds_part, host_part = rest.split('@', 1)
                    if ':' in creds_part:
                        username, password = creds_part.split(':', 1)
                        
                        # Check username
                        if username.lower() in ['admin', 'root', 'postgres', 'sa']:
                            recommendations.append("Consider using a non-administrative database user")
                        
                        # Check password
                        if len(password) < 12:
                            issues.append("Database password is too short")
                        
                        if password.lower() in ['admin', 'password', '123456', 'postgres']:
                            issues.append("Database password is too weak")
                            
            except ValueError:
                recommendations.append("Database URL format could not be parsed")
        
        # Check for localhost/development indicators
        if 'localhost' in db_url or '127.0.0.1' in db_url:
            recommendations.append("Database appears to be on localhost (development setup)")
        
        # Check SSL usage
        if 'sslmode=require' not in db_url and 'ssl=true' not in db_url:
            recommendations.append("Consider enabling SSL for database connections")
        
        return {
            "issues": issues,
            "recommendations": recommendations,
            "uses_ssl": 'ssl' in db_url.lower(),
            "is_localhost": any(host in db_url for host in ['localhost', '127.0.0.1'])
        }
    
    def audit_cors_configuration(self, allowed_origins: List[str]) -> Dict[str, Any]:
        """Audit CORS configuration for security issues"""
        issues = []
        recommendations = []
        
        # Check for wildcard origins
        if "*" in allowed_origins:
            issues.append("CORS allows all origins (*) - security risk")
        
        # Check for localhost origins in production
        localhost_origins = [origin for origin in allowed_origins if 'localhost' in origin or '127.0.0.1' in origin]
        if localhost_origins:
            recommendations.append(f"Localhost origins detected: {localhost_origins} (remove in production)")
        
        # Check for HTTP origins
        http_origins = [origin for origin in allowed_origins if origin.startswith('http://')]
        if http_origins:
            recommendations.append(f"HTTP origins detected: {http_origins} (use HTTPS in production)")
        
        # Check for overly broad origins
        broad_patterns = ['.com', '.org', '.net']
        for origin in allowed_origins:
            for pattern in broad_patterns:
                if origin.endswith(pattern) and origin.count('.') == 1:
                    recommendations.append(f"Overly broad origin: {origin}")
        
        return {
            "issues": issues,
            "recommendations": recommendations,
            "allows_all_origins": "*" in allowed_origins,
            "localhost_origins": localhost_origins,
            "http_origins": http_origins,
            "total_origins": len(allowed_origins)
        }
    
    def audit_rate_limiting(self) -> Dict[str, Any]:
        """Audit rate limiting configuration"""
        issues = []
        recommendations = []
        
        # Check if rate limiting is implemented
        # This would need to be integrated with actual rate limiting middleware
        recommendations.append("Implement rate limiting to prevent abuse")
        recommendations.append("Consider different limits for authenticated vs unauthenticated users")
        recommendations.append("Implement progressive delays for repeated failures")
        
        return {
            "issues": issues,
            "recommendations": recommendations,
            "implemented": False  # Would be True if rate limiting is detected
        }
    
    def generate_security_report(self) -> Dict[str, Any]:
        """Generate comprehensive security audit report"""
        
        # Sample password for policy testing
        sample_password = "TempPassword123!"
        
        report = {
            "timestamp": datetime.now().isoformat(),
            "audits": {
                "password_policy": self.audit_password_policy(sample_password),
                "jwt_configuration": self.audit_jwt_configuration(),
                "database_security": self.audit_database_security(),
                "cors_configuration": self.audit_cors_configuration(
                    getattr(settings, 'ALLOWED_ORIGINS', ["*"])
                ),
                "rate_limiting": self.audit_rate_limiting()
            }
        }
        
        # Calculate overall security score
        total_issues = sum(len(audit.get("issues", [])) for audit in report["audits"].values())
        total_recommendations = sum(len(audit.get("recommendations", [])) for audit in report["audits"].values())
        
        # Score calculation (lower is better for issues)
        max_possible_issues = 20  # Estimated maximum issues
        security_score = max(0, 100 - (total_issues * 10) - (total_recommendations * 2))
        
        report["summary"] = {
            "total_issues": total_issues,
            "total_recommendations": total_recommendations,
            "security_score": security_score,
            "risk_level": self._calculate_risk_level(security_score)
        }
        
        return report
    
    def _calculate_risk_level(self, score: float) -> str:
        """Calculate risk level based on security score"""
        if score >= 80:
            return "Low"
        elif score >= 60:
            return "Medium"
        elif score >= 40:
            return "High"
        else:
            return "Critical"


class SecurityHardening:
    """Security hardening utilities"""
    
    @staticmethod
    def generate_secure_secret_key(length: int = 64) -> str:
        """Generate a cryptographically secure secret key"""
        return secrets.token_urlsafe(length)
    
    @staticmethod
    def hash_password(password: str) -> str:
        """Hash password using bcrypt"""
        pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")
        return pwd_context.hash(password)
    
    @staticmethod
    def verify_password(plain_password: str, hashed_password: str) -> bool:
        """Verify password against hash"""
        pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")
        return pwd_context.verify(plain_password, hashed_password)
    
    @staticmethod
    def generate_csrf_token() -> str:
        """Generate CSRF token"""
        return secrets.token_urlsafe(32)
    
    @staticmethod
    def sanitize_input(input_string: str) -> str:
        """Basic input sanitization"""
        if not isinstance(input_string, str):
            return str(input_string)
        
        # Remove potentially dangerous characters
        sanitized = re.sub(r'[<>"\']', '', input_string)
        
        # Limit length
        return sanitized[:1000]
    
    @staticmethod
    def validate_email(email: str) -> bool:
        """Validate email format"""
        pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        return bool(re.match(pattern, email))


# Security middleware
class SecurityHeadersMiddleware:
    """Add security headers to responses"""
    
    def __init__(self, app):
        self.app = app
    
    async def __call__(self, scope, receive, send):
        if scope["type"] == "http":
            async def send_wrapper(message):
                if message["type"] == "http.response.start":
                    headers = dict(message.get("headers", []))
                    
                    # Add security headers
                    security_headers = {
                        b"x-content-type-options": b"nosniff",
                        b"x-frame-options": b"DENY",
                        b"x-xss-protection": b"1; mode=block",
                        b"strict-transport-security": b"max-age=31536000; includeSubDomains",
                        b"content-security-policy": b"default-src 'self'",
                        b"referrer-policy": b"strict-origin-when-cross-origin"
                    }
                    
                    headers.update(security_headers)
                    message["headers"] = list(headers.items())
                
                await send(message)
            
            await self.app(scope, receive, send_wrapper)
        else:
            await self.app(scope, receive, send)


# Export main components
__all__ = [
    "SecurityAuditor",
    "SecurityHardening", 
    "SecurityHeadersMiddleware"
]
