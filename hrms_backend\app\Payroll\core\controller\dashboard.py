from flask import request
from flask.views import MethodView
from flask_smorest import Blueprint, abort
from core.middleware import roles_required
from core.services.dashboard import DashboardService
from core.utils.responseBuilder import ResponseBuilder

blueprint = Blueprint("Dashboard", __name__, description="dashboards")
@blueprint.route("/dashboard")
class DashBoard(MethodView):
    @roles_required(['admin'])
    def get(self):
        dashboardService = DashboardService()
        data = dashboardService.getDashboardData()
        return ResponseBuilder(data=data, status_code=200).build()
@blueprint.route("/payroll_dashboard", methods=["GET"])
class PayrollDashboard(MethodView):
    @roles_required(['admin'])
    def get(self):
        """Get payroll summary for a given month and/or year."""
        month = request.args.get("month", type=int)  # Extract month (optional)
        year = request.args.get("year", type=int)  # Extract year (required)

        if not year:
            return ResponseBuilder(error="Year is required!", status_code=400).build()

        if month and (month < 1 or month > 12):
            return ResponseBuilder(error="Invalid month! Month should be between 1 and 12.", status_code=400).build()

        dashboard_service = DashboardService()
        payroll_summary = dashboard_service.getPayrollData(month, year)

        # Ensure we return a meaningful response if no data is found
        if not payroll_summary:
            return ResponseBuilder(error="No payroll records found for the given month and/or year.", status_code=404).build()

        return ResponseBuilder(data=payroll_summary, status_code=200).build()
