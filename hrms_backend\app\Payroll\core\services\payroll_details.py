from core.repositories.payroll_details import PayrollDetailsRepository

class PayrollDetailsService:
    def __init__(self) -> None:
        self.repository = PayrollDetailsRepository()

    def createPayrollDetails(self, Kwargs):
        # print(Kwargs)
        return self.repository.createPayrollDetails(**Kwargs)
    
    def getPayrollDetails(self, id):
        return self.repository.getPayrollDetails(id)
    
    def updatePayrollDetails(self, id, **Kwargs):
        return self.repository.updatePayrollDetails(id, **Kwargs)
    
    def getPayrollDetailsByKey(self, Kwarg):
        return self.repository.getPayrollDetailsByKeys(Kwarg)
    
    def deletePayrollDetails(self, id):
        return self.repository.deletePayrollDetails(id)