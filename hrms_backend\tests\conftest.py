import pytest
import asyncio
from typing import Generator
from fastapi.testclient import TestClient
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from sqlalchemy.pool import StaticPool

from app.main import app
from app.db.base import Base
from app.db.session import get_db
from app.core.config import settings

# Test database URL (SQLite in memory for testing)
SQLALCHEMY_DATABASE_URL = "sqlite:///./test.db"

engine = create_engine(
    SQLALCHEMY_DATABASE_URL,
    connect_args={"check_same_thread": False},
    poolclass=StaticPool,
)
TestingSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)


def override_get_db():
    """Override database dependency for testing"""
    try:
        db = TestingSessionLocal()
        yield db
    finally:
        db.close()


@pytest.fixture(scope="session")
def event_loop():
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest.fixture(scope="function")
def db_session():
    """Create a fresh database session for each test"""
    Base.metadata.create_all(bind=engine)
    session = TestingSessionLocal()
    try:
        yield session
    finally:
        session.close()
        Base.metadata.drop_all(bind=engine)


@pytest.fixture(scope="function")
def client(db_session) -> Generator:
    """Create a test client with database override"""
    app.dependency_overrides[get_db] = override_get_db
    with TestClient(app) as c:
        yield c
    app.dependency_overrides.clear()


@pytest.fixture
def sample_organization():
    """Sample organization data for testing"""
    return {
        "name": "Test Organization",
        "domain": "test.com",
        "industry": "Technology",
        "size": "50-100",
        "country": "United States",
        "timezone": "UTC"
    }


@pytest.fixture
def sample_employee():
    """Sample employee data for testing"""
    return {
        "employee_id": "EMP001",
        "email": "<EMAIL>",
        "first_name": "John",
        "last_name": "Doe",
        "phone": "+1234567890",
        "hire_date": "2024-01-01",
        "job_title": "Software Engineer",
        "department": "Engineering",
        "employment_type": "full_time",
        "status": "active"
    }


@pytest.fixture
def sample_user():
    """Sample user data for testing"""
    return {
        "email": "<EMAIL>",
        "password": "testpassword123",
        "role": "EMPLOYEE"
    }


@pytest.fixture
def auth_headers(client, sample_user):
    """Get authentication headers for testing"""
    # Register user
    client.post("/api/auth/register", json=sample_user)
    
    # Login to get token
    response = client.post("/api/auth/login", json={
        "email": sample_user["email"],
        "password": sample_user["password"]
    })
    
    token = response.json()["access_token"]
    return {"Authorization": f"Bearer {token}"}


@pytest.fixture
def sample_leave_request():
    """Sample leave request data for testing"""
    return {
        "leave_type": "ANNUAL",
        "start_date": "2024-06-01",
        "end_date": "2024-06-05",
        "reason": "Vacation",
        "duration_type": "FULL_DAY"
    }


@pytest.fixture
def sample_attendance():
    """Sample attendance data for testing"""
    return {
        "date": "2024-06-01",
        "check_in": "09:00:00",
        "check_out": "17:00:00",
        "status": "present",
        "location": "Office"
    }


@pytest.fixture
def sample_project():
    """Sample project data for testing"""
    return {
        "name": "Test Project",
        "code": "TEST001",
        "description": "A test project",
        "status": "planning",
        "priority": "medium",
        "start_date": "2024-06-01",
        "end_date": "2024-12-31",
        "is_billable": True
    }


@pytest.fixture
def sample_task():
    """Sample task data for testing"""
    return {
        "title": "Test Task",
        "description": "A test task",
        "status": "todo",
        "priority": "medium",
        "estimated_hours": 8
    }


@pytest.fixture
def sample_ticket():
    """Sample ticket data for testing"""
    return {
        "title": "Test Ticket",
        "description": "A test support ticket",
        "ticket_type": "IT_SUPPORT",
        "priority": "medium",
        "category": "Hardware"
    }


@pytest.fixture
def sample_survey():
    """Sample survey data for testing"""
    return {
        "title": "Employee Satisfaction Survey",
        "description": "Annual employee satisfaction survey",
        "survey_type": "satisfaction",
        "start_date": "2024-06-01T00:00:00",
        "end_date": "2024-06-30T23:59:59",
        "is_anonymous": True
    }


@pytest.fixture
def sample_performance_review():
    """Sample performance review data for testing"""
    return {
        "title": "Annual Performance Review 2024",
        "review_type": "annual",
        "review_period_start": "2024-01-01",
        "review_period_end": "2024-12-31",
        "due_date": "2024-12-31",
        "description": "Annual performance review"
    }


@pytest.fixture
def sample_goal():
    """Sample goal data for testing"""
    return {
        "title": "Complete Project X",
        "description": "Successfully complete Project X on time",
        "goal_type": "individual",
        "target_date": "2024-12-31",
        "weight_percentage": 100,
        "success_criteria": "Project delivered on time and within budget"
    }
