#!/usr/bin/env python3
"""
Debug organization mismatch issue
"""

import psycopg2
import os
from dotenv import load_dotenv

load_dotenv()

def debug_organization_mismatch():
    """Debug why user can't see leave policies"""
    
    conn = psycopg2.connect(
        host=os.getenv("DB_HOST", "localhost"),
        database=os.getenv("DB_NAME", "hrms_db"),
        user=os.getenv("DB_USER", "postgres"),
        password=os.getenv("DB_PASSWORD", "password"),
        port=os.getenv("DB_PORT", "5432")
    )
    
    try:
        cursor = conn.cursor()
        
        # Check user organization
        cursor.execute("SELECT email, organization_id FROM users WHERE email = '<EMAIL>'")
        user_info = cursor.fetchone()
        if user_info:
            print(f"User: {user_info[0]}")
            print(f"User's organization_id: {user_info[1]}")
        else:
            print("User not found!")
            return
        
        user_org_id = user_info[1]
        
        # Check organizations
        cursor.execute("SELECT id, name FROM organizations")
        orgs = cursor.fetchall()
        print(f"\nAll organizations:")
        for org in orgs:
            print(f"  - {org[1]} (ID: {org[0]})")
        
        # Check leave policies by organization
        cursor.execute("""
            SELECT organization_id, COUNT(*) as policy_count
            FROM leave_policies 
            GROUP BY organization_id
        """)
        policy_counts = cursor.fetchall()
        print(f"\nLeave policies by organization:")
        for org_id, count in policy_counts:
            cursor.execute("SELECT name FROM organizations WHERE id = %s", (org_id,))
            org_name = cursor.fetchone()
            org_name = org_name[0] if org_name else "Unknown"
            print(f"  - {org_name} (ID: {org_id}): {count} policies")
        
        # Check if user's organization has policies
        cursor.execute("""
            SELECT COUNT(*) FROM leave_policies 
            WHERE organization_id = %s AND is_active = true
        """, (user_org_id,))
        user_org_policies = cursor.fetchone()[0]
        print(f"\nUser's organization has {user_org_policies} active policies")
        
        if user_org_policies == 0:
            print("❌ This is why the user sees 0 policies!")
            print("Solution: Create policies for the user's organization or update user's organization_id")
            
            # Show some sample policies from other orgs
            cursor.execute("""
                SELECT name, leave_type, organization_id 
                FROM leave_policies 
                WHERE is_active = true 
                LIMIT 5
            """)
            sample_policies = cursor.fetchall()
            print("\nSample policies from other organizations:")
            for policy in sample_policies:
                print(f"  - {policy[0]} ({policy[1]}) - Org: {policy[2]}")
        
    except Exception as e:
        print(f"Error: {e}")
    finally:
        cursor.close()
        conn.close()

if __name__ == "__main__":
    debug_organization_mismatch()
