"""Add columns to employee table

Revision ID: ce5b9b635b2c
Revises: 4edfba28688f
Create Date: 2024-10-07 02:55:24.313608

"""
from typing import Sequence, Union

from alembic import op
from sqlalchemy import func, TIMESTAMP, Foreign<PERSON>ey
from sqlalchemy import Column, Integer, Float
from sqlalchemy.dialects.postgresql import BOOLEAN


# revision identifiers, used by Alembic.
revision: str = 'ce5b9b635b2c'
down_revision: Union[str, None] = '4edfba28688f'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    op.add_column('employees', Column('monthly_tax', Float, nullable=True)),
    op.add_column('employees', Column('annual_tax', Float, nullable=True)),
    op.add_column('employees', Column('total_taxable_monthly_sum', Float, nullable=True)),
    op.add_column('employees', Column('total_taxable_annual_sum', Float, nullable=True)),
    op.add_column('employees', Column('total_non_taxable_monthly_sum', Float, nullable=True)),
    op.add_column('employees', Column('total_non_taxable_annual_sum', Float, nullable=True)),
    op.add_column('employees', Column('total_statutory_monthly_sum', Float, nullable=True)),
    op.add_column('employees', Column('total_statutory_annual_sum', Float, nullable=True)),
    op.add_column('employees', Column('total_other_deductions_monthly_sum', Float, nullable=True)),
    op.add_column('employees', Column('total_other_deductions_annual_sum', Float, nullable=True)),
    op.add_column('employees', Column('netpay', Float, nullable=True)),
    op.add_column('employees', Column('is_prorated', BOOLEAN,default=False, nullable=True)),

    


def downgrade() -> None:
    op.drop_column('employees', 'monthly_tax')
    op.drop_column('employees', 'annual_tax')
    op.drop_column('employees', 'total_taxable_monthly_sum')
    op.drop_column('employees', 'total_taxable_annual_sum')
    op.drop_column('employees', 'total_non_taxable_monthly_sum')
    op.drop_column('employees', 'total_non_taxable_annual_sum')
    op.drop_column('employees', 'total_statutory_monthly_sum')
    op.drop_column('employees', 'total_statutory_annual_sum')
    op.drop_column('employees', 'total_other_deductions_monthly_sum')
    op.drop_column('employees', 'total_other_deductions_annual_sum')
    op.drop_column('employees', 'netpay')
    op.drop_column('employees', 'is_prorated')