import hmac
import hashlib
import os
from flask import request, jsonify
from datetime import datetime
from core.repositories.user import UserRepository
from core.services.transaction_history_service import TransactionHistoryService
from core.services.payroll_history import PayrollHistoryService
from core.services.Encrypt_Decrypt import EncryptionService
from core.models.paystack_credential import Paystack_IntegrationModel
from schemas import PayStack_Credentials


class PaystackWebhookService:
    """Handles Paystack webhook events for transfer success and failure."""

    @staticmethod
    def process_webhook():
        """Processes Paystack webhook events and updates the payroll history."""
        try:
            request_data = request.get_data(as_text=True)  # Raw request body
            received_signature = request.headers.get("X-Paystack-Signature")

            # Parse event data
            data = request.get_json()
            # print(f"🔍 Full Webhook Response: {data}")  # Debugging

            event_type = data.get("event")
            event_data = data.get("data", {})

            # print(f"📢 Received Paystack Event: {event_type}")

            # Handle transfer success or failure
            return PaystackWebhookService.handle_transfer_event(event_data, event_type)

        except Exception as e:
            print(f"❌ Error processing webhook: {str(e)}")
            return jsonify({"status": "error", "message": "Internal Server Error"}), 500

    @staticmethod
    def handle_transfer_event(event_data, event_type):
        """Handles Paystack transfer events and updates payroll history."""

        transaction_id = event_data.get("id")
        reference_code = event_data.get("reference")  # ✅ Use reference_code

        # Print extracted values
        # print(f"🔍 Extracted transaction_id: {transaction_id}")
        # print(f"🔍 Extracted reference_code: {reference_code}")

        # Format transferred_at timestamp or fallback to current time
        transferred_at = event_data.get("transferred_at")
        transaction_date = (
            datetime.strptime(transferred_at, "%Y-%m-%dT%H:%M:%S.%fZ").strftime("%Y-%m-%d %H:%M:%S")
            if transferred_at else datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        )

        # Use exact amount from Paystack
        amount = event_data.get("amount")  

        # Determine the appropriate message
        message = f"Payment successful. Amount: {amount} NGN" if event_type == "transfer.success" else f"Payment failed: {event_data.get('reason', 'Unknown error')}"

        # print(f"🔍 Paystack Message: {message}")

        # Update Payroll History (Keep as-is)
        payroll_history_service = PayrollHistoryService()
        payroll_update_response = payroll_history_service.update_payroll_history_webhook_response(
            transaction_id, reference_code, message, transaction_date
        )

        # Update Transaction History 
        transaction_service = TransactionHistoryService()
        transaction_update_response = transaction_service.update_transaction_history_webhook_response(
            transaction_id, reference_code, message, transaction_date
        )

        return payroll_update_response