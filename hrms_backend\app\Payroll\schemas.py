from marshmallow import Schema, validates, ValidationError, fields, validate, EXCLUDE, validates_schema, pre_load

# schema is used to validate incoming request and outgoing responses
# creating, incoming only

class UserSchema(Schema):
    id = fields.Int(dump_only=True)
    first_name = fields.Str(required=True)
    last_name = fields.Str(required=True)
    org_name = fields.Str(required=True)
    email = fields.Email(required=True)
    password = fields.Str(required=True, load_only=True)

class UserUpdateSchema(Schema):
    first_name = fields.Str()
    last_name = fields.Str()
    org_name = fields.Str()
    email = fields.Email()

class ZohoConfigSchema(Schema):
    id = fields.Int(dump_only=True)
    authorization_employees_code =  fields.Str(required=True)
    client_id =  fields.Str(required=True)
    client_secret =  fields.Str(required=True)
    access_token =  fields.Str(required=False)
    refresh_token =  fields.Str(required=False)
    token_expires_at =  fields.Str(required=False)
    is_enabled = fields.Boolean(required=False)
    user_id = fields.Int(dump_only=True)

class ZohoConfigUpdateSchema(Schema):
    id = fields.Int(dump_only=True)
    # authorization_employees_code =  fields.Str()
    # client_id =  fields.Str()
    # client_secret =  fields.Str()
    # access_token =  fields.Str()
    # refresh_token =  fields.Str()
    # token_expires_at =  fields.Str()
    is_enabled = fields.Boolean()
    user_id = fields.Int(dump_only=True)

#zoho_people_attendance Schema
class Zoho_People_Attendance_Schema(Schema):
    id = fields.Int(dump_only=True)
    first_name = fields.Str(required=False, load_default="Null")
    last_name = fields.Str(required=False, load_default="Null")
    email = fields.Str(required=False, load_default="Null")
    gender = fields.Str(required=False, load_default="Null")
    organization_profile = fields.Str(required=False, load_default="Null")
    employee_status = fields.Str(required=False, load_default="Null")
    employee_type = fields.Str(required=False, load_default="Null")
    employee_id = fields.Str(required=False, load_default="Null")
    sort_code = fields.Str(required=False, load_default="Null")
    bank_name = fields.Str(required=False, load_default="Null")
    account_no = fields.Str(required=False, load_default="Null")
    department = fields.Str(required=False, load_default="Null")
    designation = fields.Str(required=False, load_default="Null")
    salary_templates = fields.Str(required=False, load_default="Null")
    mobile_no = fields.Str(required=False, load_default="Null")
    tax_type = fields.Str(required=False, load_default="Null")
    business_unit = fields.Str(required=False, load_default="Null")
    division = fields.Str(required=False, load_default="Null")
    location = fields.Str(required=False, load_default="Null")
    
    gross_pay = fields.Float(required=False, load_default=None)
    nhf_no = fields.Str(required=False, load_default="Null")
    nhf_mortgage_bank = fields.Str(required=False, load_default="Null")
    pension_pfa = fields.Str(required=False, load_default="Null")
    pfa_no = fields.Str(required=False, load_default="Null")
    currency = fields.Str(required=False, load_default="Null")

    annual_leave_days = fields.Float(required=False, load_default=None)
    unpaid_leave_days = fields.Float(required=False, load_default=None)
    sick_leave_days = fields.Float(required=False, load_default=None)
    maternity_paternity_leave_days = fields.Float(required=False, load_default=None)
    casual_leave_days = fields.Float(required=False, load_default=None)
    compassionate_leave_days = fields.Float(required=False, load_default=None)
    total_working_days = fields.Float(required=False, load_default=None)
    total_present_days = fields.Float(required=False, load_default=None)
    total_absent_days = fields.Float(required=False, load_default=None)   



# Organisation Schema
class OrganisationSchema(Schema):
    id = fields.Int(dump_only=True)
    slug = fields.Str(dump_only=True)
    organisation_name = fields.Str(required=True, validate=validate.Length(min=4))
    email= fields.Str(required=True)
    address= fields.Str(required=False, load_default="Null")
    country= fields.Str(required=False, load_default="Null")
    city= fields.Str(required=False, load_default="Null")
    state= fields.Str(required=False, load_default="Null")
    zipcode= fields.Str(required=False, load_default="Null")
    phone= fields.Str(required=False, load_default="Null")
    industry= fields.Str(required=False, load_default="Null")
    type= fields.Str(required=False, load_default="Null")
    user_id =  fields.Int(dump_only=True)
    logoUrl = fields.String(required=False, load_default="Null")




class OrganisationBulkSchema(Schema):
    id = fields.Int(dump_only=True)
    slug = fields.Str(dump_only=True)
    organisation_name = fields.Str(required=True, validate=validate.Length(min=4))
    email= fields.Str(required=True)
    address= fields.Str(required=False, load_default="Null")
    country= fields.Str(required=False, load_default="Null")
    city= fields.Str(required=False, load_default="Null")
    state= fields.Str(required=False, load_default="Null")
    zipcode= fields.Str(required=False, load_default="Null")
    phone= fields.Str(required=False, load_default="Null")
    industry= fields.Str(required=False, load_default="Null")
    type= fields.Str(required=False, load_default="Null")
    user_id =  fields.Int(dump_only=True)
   
    
class OrganisationUpdateSchema(Schema):
    organisation_name = fields.Str(validate=validate.Length(min=4))
    email= fields.Str()
    address= fields.Str(required=False)
    country= fields.Str(required=False)
    city= fields.Str(required=False)
    zipcode= fields.Str(required=False)
    phone= fields.Str(required=False)
    industry= fields.Str(required=False)
    type= fields.Str(required=False)
    logoUrl = fields.Str(required=False)


class LogoUploadSchema(Schema):
    message = fields.Str(required=True)  # Add the message field
    logoUrl = fields.Str(required=False)



class UserLoginSchema(Schema):
    email = fields.Email(required=True)
    password = fields.Str(required=True, load_only=True)

class ForgetPasswordSchema(Schema):
    email = fields.Email(required=True)

class ResetPasswordSchema(Schema):
    password = fields.Str(required=True, validate=validate.Length(min=6)) 
    confirm_password = fields.Str(required=True)
    token = fields.Str(required=True)

class SettingIntegrationSchema(Schema):
    id = fields.Int(dump_only=True)
    name = fields.Str(required=True)
    client_id = fields.Str(required=True)
    client_secrete = fields.Str(required=True)
    refresh_token = fields.Str(required=True)
    org_id = fields.Int(dump_only=True)

class ApprovalSchema(Schema):
    id = fields.Int(dump_only=True)
    role = fields.Str(allow_none=True)
    level = fields.Str(required=True)
    employee_id = fields.Int(required=True)
    user_id = fields.Int(dump_only=True)
    pay_schedules_id = fields.Int(allow_none=True)
    name = fields.Str(allow_none=True)
    email = fields.Str(allow_none=True)
    phone = fields.Str(allow_none=True)
    timestamp = fields.DateTime(dump_only=True)
    user = fields.Nested(UserSchema)

class BenefitSchema(Schema):
    id = fields.Int(dump_only=True)
    benefit_name = fields.Str(required=True)
    payslip_name = fields.Str(required=True)
    component_type = fields.Str(required=True)
    calculation_type = fields.Str(required=True)
    amount = fields.Float(required=False, load_default=None)
    value = fields.Float(required=False, load_default=None)
    duration = fields.Str(required=False, load_default="0.0")
    cycle = fields.Str(required=False, load_default=None)
    preview_payslip = fields.Boolean(required=True)
    user_id = fields.Int(dump_only=True)

class SalaryTemplateBenefitSchema(Schema):
    benefit = fields.Nested(BenefitSchema, dump_only=True, many=False)

class LicenseSchema(Schema):
    id = fields.Int(dump_only=True)
    number_of_license = fields.Int(required=True)
    license_used = fields.Int(required=True)
    approved_by = fields.Int(required=True)
    date_approved = fields.Int()
    user_id =  fields.Int(dump_only=True)

class ApprovalSettingSchema(Schema):
    id = fields.Int(dump_only=True)
    number_of_approval = fields.Int(required=True)
    status = fields.Bool(required=False, allow_none=True, load_default=None)
    organisation_id =  fields.Int(dump_only=True)
    
    @validates_schema
    def validate_number_of_approval(self, data, **kwargs):
        if data.get("number_of_approval", 0) > 10:
            raise ValidationError({"number_of_approval": "Approval level must not be more than 10"})

class WorkingDaySchema(Schema):
    id = fields.Int(dump_only=True)
    employee_id =  fields.Int(dump_only=True)
    month = fields.Str(required=True)
    year = fields.Str(required=True)
    total_working_days = fields.Int(required=True)
    present_days = fields.Int(required=True)
    absent_days = fields.Int(required=True)

class EmployeePayrollSchema(Schema):
    id = fields.Int(dump_only=True)
    gross_pay = fields.Float(required=True)
    net_pay =fields.Float(required=True)
    annual_tax =fields.Float(required=True)
    total_earnings =fields.Float(required=True)
    total_deductions =fields.Float(required=True)
    employee_id =  fields.Int(dump_only=True)
    cost_company = fields.Int(required=True)

# class OrganisationNameSchema(Schema):
#     organisation = fields.Nested(OrganisationSchema, dump_only=True)

class SalaryComponentSchema(Schema):
    id = fields.Int(dump_only=True)
    component_name = fields.Str(required=True)
    payslip_name = fields.Str(required=True)
    component_type = fields.Str(required=True)
    calculation_type = fields.Str(required=True)
    amount = fields.Float(required=False, allow_none=True, load_default=None)
    value = fields.Float(required=False, allow_none=True, load_default=None)
    duration = fields.Str(required=False, allow_none=True, load_default=None)
    cycle = fields.Str(required=False, allow_none=True, load_default=None)
    user_id = fields.Int(dump_only=True)

class AssignSalaryComponentSchema(Schema):
    salary_component_id = fields.Int(required=True, error_messages={"required": "Salary Component ID is required."})
    employee_ids = fields.Str(required=False)
    department_id = fields.Int(required=False)
    designation_id = fields.Int(required=False)
    employment_type = fields.Str(required=False)

    @validates_schema
    def validate_assignment(self, data, **kwargs):
        """Ensure at least one assignment criterion is provided."""
        if not any(key in data for key in ["department_id", "designation_id", "employment_type", "employee_ids"]):
            raise ValidationError("At least one assignment criterion ( department_id, designation_id, employee_ids or employment_type) must be provided.")

class SalaryTemplateComponentSchema(Schema):
    salary_component = fields.Nested(SalaryComponentSchema, dump_only=True, many=False)

class SalaryTemplateSchema(Schema):
    id = fields.Int(dump_only=True)
    template_name = fields.Str(required=True)
    employment_type = fields.Str(required=False, allow_none=True, load_default=None)
    description = fields.Str(required=False, allow_none=True, load_default=None)
    level = fields.Str(required=False, allow_none=True, load_default=None)
    salary_type = fields.Str(required=False, allow_none=True, load_default=None)
    salary_components_id = fields.List(fields.Int(), required=False, allow_none=True, load_default=None)
    benefits_id = fields.List(fields.Int(), required=False, allow_none=True, load_default=None)
    employee_type = fields.Str(required=False, allow_none=True, load_default=None)
    country = fields.Str(required=False, allow_none=True, load_default=None)
    currency = fields.Str(required=False, allow_none=True, load_default=None)
    tax_type = fields.Str(required=False, allow_none=True, load_default=None)
    rate = fields.Str(required=False, allow_none=True, load_default=None)
    work_schedule = fields.Str(required=False, allow_none=True, load_default=None)
    hours_worked = fields.Str(required=False, allow_none=True, load_default=None)
    work_duration = fields.Str(required=False, allow_none=True, load_default=None)
    gross_pay = fields.Float(required=True)
    monthly_tax = fields.Float( as_string=True, required=False, allow_none=True, load_default=None)
    annual_tax = fields.Float(as_string=True, required=False, allow_none=True, load_default=None)
    total_taxable_monthly_sum = fields.Float(as_string=True, required=False, allow_none=True, load_default=None)
    total_taxable_annual_sum = fields.Float(as_string=True, required=False, allow_none=True, load_default=None)
    total_non_taxable_monthly_sum = fields.Float(as_string=True, required=False, allow_none=True, load_default=None)
    total_non_taxable_annual_sum = fields.Float(as_string=True, required=False, allow_none=True, load_default=None)
    total_statutory_monthly_sum = fields.Float(as_string=True, required=False, allow_none=True, load_default=None)
    total_statutory_annual_sum = fields.Float(as_string=True, required=False, allow_none=True, load_default=None)
    total_other_deductions_monthly_sum = fields.Float(as_string=True, required=False, allow_none=True, load_default=None)
    total_other_deductions_annual_sum = fields.Float(as_string=True, required=False, allow_none=True, load_default=None)
    netpay = fields.Float(as_string=True, required=False, allow_none=True, load_default=None)
    salary_template_components = fields.Nested(SalaryTemplateComponentSchema, dump_only=True, many=True)
    salary_template_benefits = fields.Nested(SalaryTemplateBenefitSchema, dump_only=True, many=True)
    user_id = fields.Int(dump_only=True)

class SalaryTemplateUpdateSchema(Schema):
    template_name = fields.Str(required=False)
    employment_type = fields.Str(required=False)
    description = fields.Str(required=False)
    level = fields.Str(required=False)
    salary_type = fields.Str(required=False)
    salary_components_id = fields.List(fields.Int(), required=False)
    benefits_id = fields.List(fields.Int(), required=False)
    employee_type = fields.Str(required=False)
    country = fields.Str(required=False)
    currency = fields.Str(required=False)
    tax_type = fields.Str(required=False)
    rate = fields.Str(required=False)
    work_schedule = fields.Str(required=False)
    hours_worked = fields.Str(required=False)
    work_duration = fields.Str(required=False)
    gross_pay = fields.Float(required=False)
    monthly_tax = fields.Float( as_string=True, required=False)
    annual_tax = fields.Float(as_string=True, required=False)
    total_taxable_monthly_sum = fields.Float(as_string=True, required=False)
    total_taxable_annual_sum = fields.Float(as_string=True, required=False)
    total_non_taxable_monthly_sum = fields.Float(as_string=True, required=False)
    total_non_taxable_annual_sum = fields.Float(as_string=True, required=False)
    total_statutory_monthly_sum = fields.Float(as_string=True, required=False)
    total_statutory_annual_sum = fields.Float(as_string=True, required=False)
    total_other_deductions_monthly_sum = fields.Float(as_string=True, required=False)
    total_other_deductions_annual_sum = fields.Float(as_string=True, required=False)
    netpay = fields.Float(as_string=True, required=False)
    salary_template_components = fields.Nested(SalaryTemplateComponentSchema, dump_only=True, many=True)
    salary_template_benefits = fields.Nested(SalaryTemplateBenefitSchema, dump_only=True, many=True)
    user_id = fields.Int(dump_only=True)

class RemoveComponentFromTemplate(Schema):
        components_id = fields.List(fields.Int(), required=False, allow_none=True, load_default=None)

class RemoveBenefitFromTemplate(Schema):
        benefits_id = fields.List(fields.Int(), required=False, allow_none=True, load_default=None)

class DesignationSchema(Schema):
    id = fields.Int(dump_only=True)
    name = fields.Str(required=True)  
    user_id = fields.Int(dump_only=True)

class DepartmentSchema(Schema):
    id = fields.Int(dump_only=True)
    name = fields.Str(required=True)  
    user_id = fields.Int(dump_only=True)

class EmployeeComponentSchema(Schema):
    salary_component = fields.Nested(SalaryComponentSchema, dump_only=True, many=False)

class EmployeeBenefitSchema(Schema):
    benefit = fields.Nested(BenefitSchema, dump_only=True, many=False)
    
class EmployeeLoginSchema(Schema):
    email = fields.Email(required=True)
    password = fields.Str(required=True)

class EmployeeSalaryTemplateSchema(Schema):
    template_name = fields.Str()

class EmployeeSchema(Schema):
    
    id = fields.Int(dump_only=True)
    first_name = fields.Str(required=True)
    last_name = fields.Str(required=True)
    email = fields.Email(required=True)
    gender=fields.Str(required=True)
    password_hash = fields.Str()
    hire_date = fields.Str(required=False, allow_none=True, load_default=None)
    dob = fields.Str(required=False, allow_none=True, load_default=None)
    department_id =  fields.Int(required=False, allow_none=True, load_default=None)
    designation_id =  fields.Int(required=False, allow_none=True, load_default=None)
    status = fields.Str(required=False, allow_none=True, load_default=None)
    business_unit = fields.Str(required=False, allow_none=True, load_default=None)
    employee_type = fields.Str(required=False, allow_none=True, load_default=None)
    employeeID = fields.Str(required=False, allow_none=True, load_default=None)
    taxID = fields.Str(required=False, allow_none=True, load_default=None)
    division = fields.Str(required=False, allow_none=True, load_default=None)
    employment_type = fields.Str(required=False, allow_none=True, load_default=None)
    bank_name = fields.Str(required=False, allow_none=True, load_default=None)
    sort_code = fields.Str(required=False, allow_none=True, load_default=None)
    address = fields.Str(required=False, allow_none=True, load_default=None)
    city = fields.Str(required=False, allow_none=True, load_default=None)
    state = fields.Str(required=False, allow_none=True, load_default=None)
    country = fields.Str(required=False, allow_none=True, load_default=None)
    zip_code = fields.Str(required=False, allow_none=True, load_default=None)
    phone = fields.Str(required=False, allow_none=True, load_default=None)
    salary_type = fields.Str(required=False, allow_none=True, load_default=None)
    rate = fields.Str(required=False, allow_none=True, load_default=None)
    hours_worked = fields.Str(required=False, allow_none=True, load_default=None)
    number_of_days_worked = fields.Int(required=False, load_default=0)
    role = fields.Str(required=False, allow_none=True, load_default=None)
    organisation = fields.Nested(OrganisationSchema, dump_only=True, attribute="organisation")
    template = fields.Nested(EmployeeSalaryTemplateSchema, dump_only=True, attribute="template")
    department = fields.Nested(DepartmentSchema, dump_only=True, attribute="department")
    designation = fields.Nested(DesignationSchema, dump_only=True, attribute="designation")
    organisation_id =  fields.Int(required=False, allow_none=True, load_default=None)
    template_id =  fields.Int(required=False, allow_none=True, load_default=None)
    bank_account = fields.Str(required=False, allow_none=True, load_default=None)
    tax_type = fields.Str(required=False, allow_none=True, load_default=None)
    level = fields.Str(required=False, allow_none=True, load_default=None)
    currency = fields.Str(required=False, allow_none=True, load_default=None)
    work_schedule = fields.Str(required=False, allow_none=True, load_default=None)
    user_id = fields.Str(dump_only=True)
    gross_pay = fields.Float(as_string=True, required=True)
    monthly_tax = fields.Float( as_string=True, required=False, load_default=None)
    pension_no = fields.Str(required=False, allow_none=True, load_default=None)
    pension_pfa= fields.Str(required=False, allow_none=True, load_default=None)
    nhf_no = fields.Str(required=False, allow_none=True, load_default=None)
    nhf_mortgage_bank = fields.Str(required=False, allow_none=True, load_default=None)
    pfa_name = fields.Str(required=False, allow_none=True, load_default=None)
    pfa_number = fields.Str(required=False, allow_none=True, load_default=None)
    tax_state = fields.Str(required=False, allow_none=True, load_default=None)
    pfc_name = fields.Str(required=False, allow_none=True, load_default=None)
    pfc_account_number = fields.Str(required=False, allow_none=True, load_default=None)
    annual_tax = fields.Float(as_string=True, required=False, load_default=None)
    annual_leave_days = fields.Str(required=False, allow_none=True, load_default=None)
    unpaid_leave_days = fields.Str(required=False, allow_none=True, load_default=None)
    sick_leave_days = fields.Str(required=False, allow_none=True, load_default=None)
    maternity_paternity_leave_days = fields.Str(required=False, allow_none=True, load_default=None)
    casual_leave_days = fields.Str(required=False, allow_none=True, load_default=None)
    compassionate_leave_days = fields.Str(required=False, allow_none=True, load_default=None)
    hours_worked = fields.Str(required=False, allow_none=True, load_default=None)
    total_working_days = fields.Str(required=False, allow_none=True, load_default=None)
    total_present_days = fields.Str(required=False, allow_none=True, load_default=None)
    total_absent_days = fields.Str(required=False, allow_none=True, load_default=None)
    total_taxable_monthly_sum = fields.Float(as_string=True, required=False, load_default=None)
    total_taxable_annual_sum = fields.Float(as_string=True, required=False, load_default=None)
    total_non_taxable_monthly_sum = fields.Float(as_string=True, required=False, load_default=None)
    total_non_taxable_annual_sum = fields.Float(as_string=True, required=False, load_default=None)
    total_statutory_monthly_sum = fields.Float(as_string=True, required=False, load_default=None)
    total_statutory_annual_sum = fields.Float(as_string=True, required=False, load_default=None)
    total_other_deductions_monthly_sum = fields.Float(as_string=True, required=False, load_default=None)
    total_other_deductions_annual_sum = fields.Float(as_string=True, required=False, load_default=None)
    netpay = fields.Float(as_string=True, required=False, load_default=None)
    is_prorated = fields.Bool(required=False, allow_none=True, load_default=None)
    salary_components_id = fields.List(fields.Int(), required=False, allow_none=True, load_default=None)
    benefits_id = fields.List(fields.Int(), required=False, allow_none=True, load_default=None)
    employee_components = fields.Nested(EmployeeComponentSchema, required=False, dump_only=True, many=True)
    employee_benefits = fields.Nested(EmployeeBenefitSchema,required=False, dump_only=True, many=True)
    source_tag = fields.Str(required=True)
    created_at = fields.DateTime(dump_only=True, required=False)
    updated_at = fields.DateTime(dump_only=True, required=False)
# class RemoveComponentFromEmployee(Schema):
#         components_id = fields.List(fields.Int(), required=False, allow_none=True, load_default=None)

class RemoveBenefitFromEmployee(Schema):
        benefits_id = fields.List(fields.Int(), required=False, allow_none=True, load_default=None)
        

class AssignBenefitSchema(Schema):
    employee_id = fields.Int(required=True)
    benefits_id = fields.Int(required=True)

class RemoveBenefitFromEmployeesSchema(Schema):
    employee_id = fields.Int(required=True)
    benefits_id = fields.Int(required=True)

class AssignComponentSchema(Schema):
    employee_id = fields.Int(required=True)
    salary_component_id = fields.Int(required=True)
    

class AssignTemplateSchema(Schema):
    template_id = fields.Int(required=True, error_messages={"required": "Salary templet ID is required."})
    employee_ids = fields.Str(required=False)
    department_id = fields.Int(required=False)
    designation_id = fields.Int(required=False)
    employment_type = fields.Str(required=False)
    

class CreatePaySchedleSchema(Schema):
    id = fields.Int(dump_only=True)
    template_id = fields.Int(required=False)
    employee_ids = fields.Str(required=False) # this field expects "ALL" string from the payload to create using all active employees.
    organisation_id = fields.Int(required=False)
    employment_type = fields.Str(required=False)
    name = fields.Str(required=True)
    pay_date = fields.Str(required=True)
    clear_all = fields.Str(required=True)
    payment_based = fields.Str(required=True)
    number_of_employees = fields.Int(required=False)
    user_id = fields.Int(dump_only=True)
    timestamp = fields.DateTime(dump_only=True)
    

class RemoveComponentFromEmployee(Schema):
    employee_id = fields.Int(required=True)
    salary_component_id = fields.Int(required=True)


class UpdateEmployeeSchema(Schema):
    first_name = fields.Str(required=False)
    last_name = fields.Str(required=False)
    email = fields.Email(required=True)
    gender=fields.Str(required=False)
    hire_date = fields.Str(required=False)
    dob = fields.Str(required=False)
    department_id =  fields.Int(required=False, load_default=None)
    designation_id =  fields.Int(required=False, load_default=None)
    status = fields.Str(required=False)
    business_unit = fields.Str(required=False)
    employee_type = fields.Str(required=False)
    employeeID = fields.Str(required=False)
    taxID = fields.Str(required=False)
    annual_tax = fields.Float(load_default=0)
    monthly_tax = fields.Float(load_default=0)
    division = fields.Str(required=False)
    employment_type = fields.Str(required=False)
    bank_name = fields.Str(required=False)
    sort_code = fields.Str(required=False)
    address = fields.Str(required=False)
    city = fields.Str(required=False)
    state = fields.Str(required=False)
    country = fields.Str(required=False)
    zip_code = fields.Str(required=False)
    phone = fields.Str(required=False)
    salary_type = fields.Str(required=False)
    rate = fields.Str(required=False)
    pension_no = fields.Str(required=False)
    pension_pfa= fields.Float(required=False, load_default=0.0)
    nhf_no = fields.Str(required=False)
    nhf_mortgage_bank = fields.Str(required=False)
    pfa_name = fields.Str(required=False)
    pfa_number = fields.Str(required=False)
    tax_state = fields.Str(required=False)
    pfc_name = fields.Str(required=False)
    pfc_account_number = fields.Str(required=False)
    annual_leave_days = fields.Str(required=False)
    unpaid_leave_days = fields.Str(required=False)
    sick_leave_days = fields.Str(required=False)
    maternity_paternity_leave_days = fields.Str(required=False)
    casual_leave_days = fields.Str(required=False)
    compassionate_leave_days = fields.Str(required=False)
    hours_worked = fields.Str(required=False)
    total_working_days = fields.Str(required=False)
    total_present_days = fields.Str(required=False)
    total_present_days = fields.Str(required=False)
    hours_worked = fields.Str(required=False)
    number_of_days_worked = fields.Int(required=False, load_default=0)
    role = fields.Str(required=False)
    organisation_id =  fields.Int(required=False)
    template_id =  fields.Int(required=False)
    bank_account = fields.Str(required=False)
    tax_type = fields.Str(required=False)
    level = fields.Str(required=False)
    currency = fields.Str(required=False)
    work_schedule = fields.Str(required=False)
    gross_pay = fields.Float(required=False, load_default=0.0)
    salary_components_id = fields.List(fields.Int(), required=False, load_default=[])
    benefits_id = fields.List(fields.Int(), required=False, load_default=[])
    source_tag = fields.Str(required=False)
    total_taxable_monthly_sum = fields.Float(required=False, load_default=0.0)
    total_taxable_annual_sum = fields.Float(required=False)
    total_non_taxable_monthly_sum = fields.Float(required=False, load_default=0.0)
    total_non_taxable_annual_sum = fields.Float(required=False, load_default=0.0)
    total_statutory_monthly_sum = fields.Float(required=False, load_default=0.0)
    total_statutory_annual_sum = fields.Float(required=False, load_default=0.0)
    total_other_deductions_monthly_sum = fields.Float(required=False, load_default=0.0)
    total_other_deductions_annual_sum = fields.Float(required=False, load_default=0.0)
    netpay = fields.Float(required=False, load_default=0.0)
    employee_components = fields.Nested(EmployeeComponentSchema, required=False, dump_only=True, many=True)
    employee_benefits = fields.Nested(EmployeeBenefitSchema,required=False, dump_only=True, many=True)

class PayScheduleSchema(Schema):
    id = fields.Int(dump_only=True)
    date = fields.Str(required=True)
    payment_based = fields.Str(required=True)

class EmployeeSalaryDetailSchema(Schema):
    id = fields.Int(dump_only=True)
    employee_id =  fields.Int(dump_only=True)
    template_id =  fields.Int(dump_only=True)
    amount = fields.Float(required=True)
    salary_type = fields.Str(required=True)



class PayrollDetailSchema(Schema):
    id = fields.Int(dump_only=True)
    processing_id = fields.Int(dump_only=True)
    employee_id = fields.Int(dump_only=True)
    net_salary = fields.Float(required=True)
    approval_status = fields.Int(required=True)
    gross_salary = fields.Float(required=True)
    tax = fields.Float(required=True)
    cost_compony = fields.Float(required=True)
    total_earnings = fields.Float(required=True)
    total_deduction = fields.Float(required=True)
    payment_status = fields.Int(required=True)
    pension = fields.Int(required=True)
    nhf = fields.Int(required=True)

class PayrollProcessingSchema(Schema):
    id = fields.Int(dump_only=True)
    month = fields.Str(required=True)
    year = fields.Str(required=True)
    organisation_id = fields.Int(dump_only=True)

class OperatorSchema(Schema):
    role = fields.Str(required=True)
    employee_id = fields.Int(required=True)
    user_id = fields.Int(dump_only=True)

class TaxRegulationSchema(Schema):
    id = fields.Int(dump_only=True)
    country = fields.Str(required=True)
    tax_rate = fields.Str(required=True)
    tax_type = fields.Str(required=True)
    currency = fields.Float(required=True)

class EmployeeIncomeTaxSchema(Schema):
    id = fields.Int(dump_only=True)
    employee_id = fields.Int(dump_only=True)
    regulation_id = fields.Int(dump_only=True)
    taxable_income = fields.Int(required=True)
    tax_amount = fields.Float(required=True)
    organisation_id = fields.Int(dump_only=True)

class BankSchema(Schema):
    id = fields.Int(dump_only=True)
    name = fields.Str(required=True)
    sort_code = fields.Str(required=True)
    user_id = fields.Int(dump_only=True)




class PaySchedule(Schema):
    id = fields.Int(dump_only=True)
    name = fields.Str(required=True)
    pay_date = fields.Str(required=True)
    clear_all = fields.Str(required=True)
    payment_based = fields.Str(required=True)
    number_of_employees = fields.Int(required=False)
    user_id = fields.Int(dump_only=True)
    organisation_id = fields.Int(dump_only=True)
    salary_template_id = fields.Int(dump_only=True)
    employment_type = fields.Str(dump_only=True)
    timestamp = fields.DateTime(dump_only=True, format="%Y-%m-%d %H:%M:%S")
    is_approved = fields.Bool(allow_none=True, load_default=None)
    approved_date = fields.Date(allow_none=True, load_default=None)

class UpdateBankSchema(Schema):
    name = fields.Str(required=False)
    sort_code = fields.Str(required=False)



class NhfSchema(Schema):
    id = fields.Int(dump_only=True)
    primary_mortgage_bank = fields.Str(required=True)
    recapitalisation_status = fields.Str(required=True)

class PensionSchema(Schema):
    id = fields.Int(dump_only=True)
    pension_name = fields.Str(required=True)
    pension_fund_custodian = fields.Str(required=True)
    pfc_account_number = fields.Int(required=True) 
    address = fields.Str(required=True)
    city = fields.Str(required=True)
    state = fields.Str(required=True)
    country = fields.Str(required=True)
    zip_code = fields.Int(required=True)

class AnnouncementSchema(Schema):
    id = fields.Int(dump_only=True)
    title = fields.Str(required=True)
    sent_to = fields.Str(required=True)
    description = fields.Str(required=True)
    status = fields.Str(required=True)
    date = fields.Str(required=False)
    user_id = fields.Int(dump_only=True)

class UpdateAnnouncementSchema(Schema):
    title = fields.Str(required=False)
    sent_to = fields.Str(required=False)
    description = fields.Str(required=False)
    status = fields.Str(required=False)

class LoanRequestSchema(Schema):
    id = fields.Int(dump_only=True)
    month = fields.Str(required=True)
    principal = fields.Float(as_string=True, required=False, allow_none=True, load_default=None)
    date = fields.Str(required=False, allow_none=True, load_default=None)
    interest = fields.Float(as_string=True, required=False, allow_none=True, load_default=None)
    status = fields.Str(required=True)
    duration = fields.Int(required=True)
    monthly_repayment = fields.Float(as_string=True, required=False, allow_none=True, load_default=None)
    total_repayment = fields.Float(as_string=True, required=False, allow_none=True, load_default=None)
    user_id = fields.Int(dump_only=True)
    disbursed_at = fields.DateTime(required=False, allow_none=True)
    balance = fields.Float(as_string=True, required=False, allow_none=True)


class LoanRepaymentSchema(Schema):
    id = fields.Int(dump_only=True)
    payroll_id = fields.Int(required=True)
    loan_id = fields.Int(required=True)
    amount = fields.Float(as_string=True, required=True)
    created_at = fields.DateTime(dump_only=True)

class LoanApprovalSchema(Schema):
    employee_id = fields.Int(required=True)
    status = fields.Str(required=True, validate=validate.OneOf(["disbursed", "rejected", "pending"]))
    user_id = fields.Int(dump_only=True)


class UpdateLoanRequestSchema(Schema):
    month = fields.Str(required=False)
    principal = fields.Float(as_string=True, required=False)
    date = fields.Str(required=False)
    interest = fields.Float(as_string=True, required=False)
    status = fields.Str(required=False)
    duration = fields.Int(required=False)
    monthly_repayment = fields.Float(as_string=True, required=False)
    total_repayment = fields.Float(as_string=True, required=False)

class EmployeeProfileUpdateSchema(Schema):
    first_name = fields.Str(required=False, allow_none=True, load_default=None)
    last_name = fields.Str(required=False, allow_none=True, load_default=None)
    email = fields.Email(required=False, allow_none=True, load_default=None)
    dob = fields.Str(required=False, allow_none=True, load_default=None)
    bank_name = fields.Str(required=False, allow_none=True, load_default=None)
    sort_code = fields.Str(required=False, allow_none=True, load_default=None)
    address = fields.Str(required=False, allow_none=True, load_default=None)
    city = fields.Str(required=False, allow_none=True, load_default=None)
    state = fields.Str(required=False, allow_none=True, load_default=None)
    country = fields.Str(required=False, allow_none=True, load_default=None)
    zip_code = fields.Str(required=False, allow_none=True, load_default=None)
    phone = fields.Str(required=False, allow_none=True, load_default=None)
    bank_account = fields.Str(required=False, allow_none=True, load_default=None)
    tax_type = fields.Str(required=False, allow_none=True, load_default=None)
    work_schedule = fields.Str(required=False, allow_none=True, load_default=None)



class OTPSchema(Schema):
    id = fields.Int(dump_only=True)
    email = fields.Email(required=True)
    user_id = fields.Int(required=True)
    otp = fields.Str(required=True)
    created_at = fields.DateTime(dump_only=True)
    
class OTPVerifySchema(Schema):
    email = fields.Email(required=True)
    otp = fields.Str(required=True)
    
class ResendOTPSchema(Schema):
    email = fields.Email(required=True)

class PayStack_Credentials(Schema):
    id = fields.Int(dump_only=True)
    paystack_key = fields.Str(required=True)
    user_id = fields.Int(dump_only=True)


class EmployeeDetailsSchema(Schema):
    id = fields.Int(dump_only=True)
    first_name = fields.Str()
    last_name = fields.Str()
    email = fields.Str()

class TransactionHistoryListSchema(Schema):
    id = fields.Int(dump_only=True)
    payroll_history_id = fields.Str(required=True)
    amount_paid = fields.Str(required=True)
    currency = fields.Str(required=True)
    failures = fields.Str(required=True)
    transaction_id = fields.Str(required=True)
    integration = fields.Str(required=True)
    reason = fields.Str(required=True)
    recipient_code = fields.Str(required=True)
    reference_code = fields.Str(required=True)
    request =  fields.Str(required=True)
    transaction_process_status = fields.Str(required=True)   
    transfer_code = fields.Str(required=True)
    transaction_message = fields.Str(required=True)
    paystack_status = fields.Str(required=True)
    verified_bank_name = fields.Str(required=True)
    verified_bank_sort_code = fields.Str(required=True)
    verified_account_number = fields.Str(required=True)
    verified_account_name = fields.Str(required=True)
    transferred_at = fields.Str(required=True)
    createdAt = fields.Str(required=True)
    updatedAt = fields.Str(required=True)
    employee = fields.Nested(EmployeeDetailsSchema)

class TransactionHistorySchema(Schema):
    id = fields.Int(dump_only=True)
    payroll_history_id = fields.Str(required=True)
    employee_id = fields.Int(dump_only=True)
    amount_paid = fields.Str(required=True)
    currency = fields.Str(required=True)
    failures = fields.Str(required=True)
    transaction_id = fields.Str(required=True)
    integration = fields.Str(required=True)
    reason = fields.Str(required=True)
    recipient_code = fields.Str(required=True)
    reference_code = fields.Str(required=True)
    request =  fields.Str(required=True)
    transaction_process_status = fields.Str(required=True)   
    transfer_code = fields.Str(required=True)
    transaction_message = fields.Str(required=True)
    paystack_status = fields.Str(required=True)
    verified_bank_name = fields.Str(required=True)
    verified_bank_sort_code = fields.Str(required=True)
    verified_account_number = fields.Str(required=True)
    verified_account_name = fields.Str(required=True)
    transferred_at = fields.Str(required=True)
    createdAt = fields.Str(required=True)
    updatedAt = fields.Str(required=True)
    user_id = fields.Int(dump_only=True)

class PayrollPaymentSchema(Schema):
    amount_paid = fields.Str(required=True)
    transaction_message = fields.Str(required=True) 
    paystack_status = fields.Str(required=True) 
    createdAt= fields.Str(required=True)


    
class PayrollSummarySchema(Schema):
    id = fields.Int(dump_only=True) 
    employee_id = fields.Int(required=True)
    gross_pay = fields.Float(required=True)
    tax_type = fields.Str(required=True) 
    monthly_tax = fields.Float(required=True) 
    annual_tax = fields.Float(required=True) 
    cra = fields.Float(required=True) 
    taxable_income = fields.Float(required=True)
    statutory_deductions = fields.Float(required=True)
    pay_after_tax_deduction = fields.Float(required=True)
    net_pay_sum = fields.Float(required=True)  # Net Pay
    total_earnings_after_tax_and_other_deductions = fields.Float(required=True)


class UserChangePasswordSchema(Schema):
    email = fields.Email(required=True)
    password = fields.Str(required=True)


class UserUpdatePasswordSchema(Schema):
    email = fields.Email(required=True)
    old_password = fields.Str(required=True)
    new_password = fields.Str(required=True)
    

class ProrateSalarySchema(Schema):
    id = fields.Int(dump_only=True)
    first_name = fields.Str(required=False, allow_none=True, load_default=None)
    last_name = fields.Str(required=False, allow_none=True, load_default=None)
    email = fields.Email(required=False, allow_none=True, load_default=None)
    is_prorated = fields.Bool(required=False, allow_none=True, load_default=None)
    gross_pay = fields.Float(required=True)
    p_id = fields.Int(required=True)
    monthly_tax = fields.Float(required=False, allow_none=True, load_default=None)
    annual_tax = fields.Float(required=False, allow_none=True, load_default=None)
    total_taxable_monthly_sum = fields.Float(required=False, allow_none=True, load_default=None)
    total_taxable_annual_sum = fields.Float(required=False, allow_none=True, load_default=None)
    total_non_taxable_monthly_sum = fields.Float(required=False, allow_none=True, load_default=None)
    total_non_taxable_annual_sum = fields.Float(required=False, allow_none=True, load_default=None)
    total_statutory_monthly_sum = fields.Float(required=False, allow_none=True, load_default=None)
    total_statutory_annual_sum = fields.Float(required=False, allow_none=True, load_default=None)
    total_other_deductions_monthly_sum = fields.Float(required=False, allow_none=True, load_default=None)
    total_other_deductions_annual_sum = fields.Float(required=False, allow_none=True, load_default=None)
    netpay = fields.Float(required=True)
    user_id = fields.Int(dump_only=True)
    timestamp = fields.DateTime(dump_only=True)
    employee_id = fields.Int(required=False, allow_none=True, load_default=None)
    tax_overtime = fields.Bool(required=False, allow_none=True, load_default=None)
    overtime_hours = fields.Float(required=False, allow_none=True, load_default=0.0)
    overtime_rate = fields.Float(required=False, allow_none=True, load_default=0.0)
    overtime_amount = fields.Float(required=False, allow_none=True, load_default=0.0)
    salary_arrears = fields.Float(required=False, allow_none=True, load_default=0.0)
    surcharge = fields.Float(required=False, allow_none=True, load_default=0.0)
    
    @pre_load
    def clean_float_fields(self, data, **kwargs):
        """Convert empty string values to 0.0 and ensure numbers are float type."""
        float_fields = [
            "monthly_tax", "annual_tax", "total_taxable_monthly_sum", "total_taxable_annual_sum",
            "total_non_taxable_monthly_sum", "total_non_taxable_annual_sum",
            "total_statutory_monthly_sum", "total_statutory_annual_sum",
            "total_other_deductions_monthly_sum", "total_other_deductions_annual_sum",
            "overtime_hours", "overtime_rate", "overtime_amount",
            "salary_arrears", "surcharge", "netpay", "gross_pay"
        ]
        for field in float_fields:
            if field in data:
                value = data[field]
                if isinstance(value, str):  # If it's a string, attempt conversion
                    data[field] = float(value) if value.strip() else 0.0
                elif value is None:  # If it's explicitly None, replace with 0.0
                    data[field] = 0.0
        return data
    
class OldPayrollHistorySchema(Schema):
    id = fields.Int(dump_only=True, required=False, allow_none=True, load_default=None)
    first_name = fields.Str(required=False, allow_none=True, load_default=None)
    last_name = fields.Str(required=False, allow_none=True, load_default=None)
    email = fields.Str(required=False, allow_none=True, load_default=None)
    hire_date = fields.Str(required=False, allow_none=True, load_default=None)
    dob = fields.Str(required=False, allow_none=True, load_default=None)
    department_id = fields.Int(required=False, allow_none=True, load_default=None)
    designation_id = fields.Int(required=False, allow_none=True, load_default=None)
    user_id = fields.Int(required=False, allow_none=True, load_default=None)
    organisation_id = fields.Int(required=False, allow_none=True, load_default=None)
    template_id = fields.Int(required=False, allow_none=True, load_default=None)
    business_unit = fields.Str(required=False, allow_none=True, load_default=None)
    division = fields.Str(required=False, allow_none=True, load_default=None)
    taxID = fields.Str(required=False, allow_none=True, load_default=None)
    employee_id = fields.Int(required=False, allow_none=True, load_default=None)
    employee_type = fields.Str(required=False, allow_none=True, load_default=None)
    status = fields.Str(required=False, allow_none=True, load_default=None)
    role = fields.Str(required=False, allow_none=True, load_default=None)
    employment_type = fields.Str(required=False, allow_none=True, load_default=None)
    bank_name = fields.Str(required=False, allow_none=True, load_default=None)
    bank_account = fields.Str(required=False, allow_none=True, load_default=None)
    salary_type = fields.Str(required=False, allow_none=True, load_default=None)
    rate = fields.Str(required=False, allow_none=True, load_default=None)
    hours_worked = fields.Str(required=False, allow_none=True, load_default=None)
    number_of_days_worked = fields.Int(required=False, allow_none=True, load_default=None)
    sort_code = fields.Str(required=False, allow_none=True, load_default=None)
    address = fields.Str(required=False, allow_none=True, load_default=None)
    country = fields.Str(required=False, allow_none=True, load_default=None)
    state = fields.Str(required=False, allow_none=True, load_default=None)
    city = fields.Str(required=False, allow_none=True, load_default=None)
    zip_code = fields.Str(required=False, allow_none=True, load_default=None)
    phone = fields.Str(required=False, allow_none=True, load_default=None)
    level = fields.Str(required=False, allow_none=True, load_default=None)
    tax_type = fields.Str(required=False, allow_none=True, load_default=None)
    currency = fields.Str(required=False, allow_none=True, load_default=None)
    work_schedule = fields.Str(required=False, allow_none=True, load_default=None)
    gross_pay = fields.Float(required=False, load_default=None)
    prorated_gross = fields.Float(required=False, load_default=None)
    prorated_net = fields.Float(required=False, load_default=None)
    monthly_tax = fields.Float(required=False)
    annual_tax = fields.Float(required=False)
    prorated_monthly_tax = fields.Float(required=False)
    prorated_annual_tax = fields.Float(required=False)
    total_taxable_monthly_sum = fields.Float(required=False, allow_none=True, load_default=None)
    total_taxable_annual_sum = fields.Float(required=False, allow_none=True, load_default=None)
    total_non_taxable_monthly_sum = fields.Float(required=False, allow_none=True, load_default=None)
    total_non_taxable_annual_sum = fields.Float(required=False, allow_none=True, load_default=None)
    total_statutory_monthly_sum = fields.Float(required=False, allow_none=True, load_default=None)
    total_statutory_annual_sum = fields.Float(required=False, allow_none=True, load_default=None)
    total_other_deductions_monthly_sum = fields.Float(required=False, allow_none=True, load_default=None)
    total_other_deductions_annual_sum = fields.Float(required=False, allow_none=True, load_default=None)
    netpay = fields.Float(required=False, allow_none=True, load_default=None)
    is_prorated = fields.Boolean(required=False)
    is_processed = fields.Boolean(required=False)
    is_processed_created = fields.Str(required=False)
    message = fields.Str(required=False)
    paymentID = fields.Str(required=False, allow_none=True, load_default=None)
    payment_status = fields.Str(required=False)
     # Newly added fields
    gender = fields.Str(required=False, allow_none=True, load_default=None)
    source_tag = fields.Str(required=False, allow_none=True, load_default=None)
    pension_no = fields.Str(required=False, allow_none=True, load_default=None)
    pension_pfa = fields.Str(required=False, allow_none=True, load_default=None)
    pfa_name = fields.Str(required=False, allow_none=True, load_default=None)
    pfa_number = fields.Str(required=False, allow_none=True, load_default=None)
    nhf_no = fields.Str(required=False, allow_none=True, load_default=None)
    nhf_mortgage_bank = fields.Str(required=False, allow_none=True, load_default=None)
    tax_state = fields.Str(required=False, allow_none=True, load_default=None)
    pfc_name = fields.Str(required=False, allow_none=True, load_default=None)
    pfc_account_number = fields.Str(required=False, allow_none=True, load_default=None)
    annual_leave_days = fields.Str(required=False, allow_none=True, load_default=None)
    unpaid_leave_days = fields.Str(required=False, allow_none=True, load_default=None)
    sick_leave_days = fields.Str(required=False, allow_none=True, load_default=None)
    maternity_paternity_leave_days = fields.Str(required=False, allow_none=True, load_default=None)
    casual_leave_days = fields.Str(required=False, allow_none=True, load_default=None)
    compassionate_leave_days = fields.Str(required=False, allow_none=True, load_default=None)
    total_working_days = fields.Str(required=False, allow_none=True, load_default=None)
    total_present_days = fields.Str(required=False, allow_none=True, load_default=None)
    total_absent_days = fields.Str(required=False, allow_none=True, load_default=None)
    employeeID = fields.Str(required=False, allow_none=True, load_default=None)
    transaction_id = fields.Str(required=False, allow_none=True, load_default=None)
    transaction_date = fields.Str(required=False)
    
    organisation = fields.Nested(OrganisationSchema, dump_only=True, attribute="organisation")
    template = fields.Nested(SalaryTemplateSchema, dump_only=True, attribute="template")
    department = fields.Nested(DepartmentSchema, dump_only=True, attribute="department")
    designation = fields.Nested(DesignationSchema, dump_only=True, attribute="designation")
    
    salary_components_id = fields.List(fields.Int(), required=False, allow_none=True, load_default=None)
    benefits_id = fields.List(fields.Int(), required=False, allow_none=True, load_default=None)
    employee_components = fields.Nested(EmployeeComponentSchema, required=False, dump_only=True, many=True)
    employee_benefits = fields.Nested(EmployeeBenefitSchema,required=False, dump_only=True, many=True)

    employee = fields.Nested(EmployeeSchema, required=False, allow_none=True, load_default=None)
    
    created_at = fields.DateTime(dump_only=True, required=False, allow_none=True, load_default=None)
    updated_at = fields.DateTime(dump_only=True, required=False, allow_none=True, load_default=None)

    class Meta:
        unknown = EXCLUDE  # Ignore unknown fields in the input data


class CreatePayrollHistorySchema(Schema):
    id = fields.Int(dump_only=True)
    gross_pay = fields.Float(required=False, load_default=None)
    monthly_tax = fields.Float(required=False, load_default=None)
    annual_tax = fields.Float(required=False, load_default=None)
    total_taxable_monthly_sum = fields.Float(required=False, allow_none=True)
    total_taxable_annual_sum = fields.Float(required=False, allow_none=True)
    total_non_taxable_monthly_sum = fields.Float(required=False, allow_none=True)
    total_non_taxable_annual_sum = fields.Float(required=False, allow_none=True)
    total_statutory_monthly_sum = fields.Float(required=False, allow_none=True)
    total_statutory_annual_sum = fields.Float(required=False, allow_none=True)
    total_other_deductions_monthly_sum = fields.Float(required=False, allow_none=True)
    total_other_deductions_annual_sum = fields.Float(required=False, allow_none=True)
    netpay = fields.Float(required=False, allow_none=True)
    pension = fields.Float(required=False, allow_none=True)
    nhf = fields.Float(required=False, allow_none=True)
    cost_to_company = fields.Float(required=False, allow_none=True)
    employee_id = fields.Int(required=False, allow_none=True, load_default=None)
    created_at = fields.DateTime(dump_only=True)
    updated_at = fields.DateTime(dump_only=True)

    class Meta:
        unknown = EXCLUDE  # Ignore unknown fields in the input data
class PayrollHistorySchema(Schema):
    id = fields.Int(dump_only=True)
    gross_pay = fields.Float(required=False, load_default=None)
    monthly_tax = fields.Float(required=False, load_default=None)
    annual_tax = fields.Float(required=False, load_default=None)
    total_taxable_monthly_sum = fields.Float(required=False, allow_none=True)
    total_taxable_annual_sum = fields.Float(required=False, allow_none=True)
    total_non_taxable_monthly_sum = fields.Float(required=False, allow_none=True)
    total_non_taxable_annual_sum = fields.Float(required=False, allow_none=True)
    total_statutory_monthly_sum = fields.Float(required=False, allow_none=True)
    total_statutory_annual_sum = fields.Float(required=False, allow_none=True)
    total_other_deductions_monthly_sum = fields.Float(required=False, allow_none=True)
    total_other_deductions_annual_sum = fields.Float(required=False, allow_none=True)
    netpay = fields.Float(required=False, allow_none=True)
    pension = fields.Float(required=False, allow_none=True)
    salary_arrears = fields.Float(required=False, allow_none=True)
    overtime_amount = fields.Float(required=False, allow_none=True)
    nhf = fields.Float(required=False, allow_none=True)
    cost_to_company = fields.Float(required=False, allow_none=True)
    employee_id = fields.Int(required=False, allow_none=True, load_default=None)
    employee = fields.Nested(EmployeeSchema, required=False, allow_none=True, load_default=None)
    created_at = fields.DateTime(dump_only=True)
    updated_at = fields.DateTime(dump_only=True)

    prorated_monthly_tax = fields.Float(required=False, load_default=None)
    prorated_annual_tax = fields.Float(required=False, load_default=None)
    is_prorated = fields.Boolean(required=False)
    prorated_gross = fields.Float(required=False, load_default=None)
    prorated_net = fields.Float(required=False, load_default=None)
    is_processed = fields.Boolean(required=False)
    is_processed_created = fields.Str(required=False, allow_none=True)
    message = fields.Str(required=False, allow_none=True)
    paymentID = fields.Str(required=False, allow_none=True)
    payment_status = fields.Str(required=False, allow_none=True)
    transaction_id = fields.Str(required=False, allow_none=True)
    transaction_date = fields.Str(required=False, allow_none=True)
    class Meta:
        unknown = EXCLUDE  # Ignore unknown fields in the input data


class UpdatePayrollHistorySchema(Schema):
    first_name = fields.Str(required=False)
    last_name = fields.Str(required=False)
    email = fields.Str(required=False)
    gender = fields.Str(required=False)
    hire_date = fields.Str(required=False)
    dob = fields.Str(required=False)
    department_id = fields.Int(required=False)
    designation_id = fields.Int(required=False)
    user_id = fields.Int(required=False)
    organisation_id = fields.Int(required=False)
    template_id = fields.Int(required=False)
    business_unit = fields.Str(required=False)
    division = fields.Str(required=False)
    taxID = fields.Str(required=False)
    employee_id = fields.Int(required=False)
    employee_type = fields.Str(required=False)
    status = fields.Str(required=False)
    role = fields.Str(required=False)
    employment_type = fields.Str(required=False)
    bank_name = fields.Str(required=False)
    bank_account = fields.Str(required=False)
    salary_type = fields.Str(required=False)
    rate = fields.Str(required=False)
    hours_worked = fields.Str(required=False)
    number_of_days_worked = fields.Int(required=False)
    sort_code = fields.Str(required=False)
    address = fields.Str(required=False)
    country = fields.Str(required=False)
    state = fields.Str(required=False)
    city = fields.Str(required=False)
    zip_code = fields.Str(required=False)
    phone = fields.Str(required=False)
    level = fields.Str(required=False)
    tax_type = fields.Str(required=False)
    currency = fields.Str(required=False)
    work_schedule = fields.Str(required=False)
    gross_pay = fields.Float(required=False)
    monthly_tax = fields.Float(required=False)
    annual_tax = fields.Float(required=False)
    total_taxable_monthly_sum = fields.Float(required=False)
    total_taxable_annual_sum = fields.Float(required=False)
    total_non_taxable_monthly_sum = fields.Float(required=False)
    total_non_taxable_annual_sum = fields.Float(required=False)
    total_statutory_monthly_sum = fields.Float(required=False)
    total_statutory_annual_sum = fields.Float(required=False)
    total_other_deductions_monthly_sum = fields.Float(required=False)
    total_other_deductions_annual_sum = fields.Float(required=False)
    netpay = fields.Float(required=False)
    is_prorated = fields.Boolean(required=False)
    is_processed = fields.Boolean(required=False)
    is_processed_created = fields.Str(required=False)
    message = fields.Str(required=False)
    paymentID = fields.Str(required=False)
    payment_status = fields.Str(required=False)
     # Newly added fields
    gender = fields.Str(required=False)
    source_tag = fields.Str(required=False)
    pension_no = fields.Str(required=False)
    pension_pfa = fields.Str(required=False)
    pfa_name = fields.Str(required=False)
    pfa_number = fields.Str(required=False)
    nhf_no = fields.Str(required=False)
    nhf_mortgage_bank = fields.Str(required=False)
    tax_state = fields.Str(required=False)
    pfc_name = fields.Str(required=False)
    pfc_account_number = fields.Str(required=False)
    annual_leave_days = fields.Str(required=False)
    unpaid_leave_days = fields.Str(required=False)
    sick_leave_days = fields.Str(required=False)
    maternity_paternity_leave_days = fields.Str(required=False)
    casual_leave_days = fields.Str(required=False)
    compassionate_leave_days = fields.Str(required=False)
    total_working_days = fields.Str(required=False)
    total_present_days = fields.Str(required=False)
    total_absent_days = fields.Str(required=False)
    employeeID = fields.Str(required=False)
    transaction_id = fields.Str(required=False)
    is_prorated = fields.Boolean(required=False, load_default=False)
    prorated_gross = fields.Float(required=False, load_default=0)
    prorated_net = fields.Float(required=False, load_default=0)

    class Meta:
        unknown = EXCLUDE  # Ignore unknown fields in the input data

class UpdatePayrollHistoryProratedData(Schema):
    is_prorated = fields.Boolean(load_default=False)
    prorated_gross = fields.Float(load_default=0.00)
    prorated_net = fields.Float(load_default=0.00)
    prorated_monthly_tax = fields.Float(load_default=0.00)
    prorated_annual_tax = fields.Float(load_default=0.00)
    surcharge = fields.Float(load_default=0.00)
    overtime_amount = fields.Float(load_default=0.00)
    salary_arrears = fields.Float(load_default=0.00)

class FilterPayrollHistorySchema(Schema):
    department = fields.String(required=False, allow_none=True)
    date = fields.String(required=False, allow_none=True)  # Change this to String
    designation = fields.String(required=False, allow_none=True)
    status = fields.String(required=False, allow_none=True)

    class Meta:
        # Allows partial data for flexible filtering
        unknown = 'exclude'

class PayrollProcesSchema(Schema):
    payroll_ids = fields.List(fields.Int(), required=True) 
    is_processed_created = fields.DateTime(dump_only=True)
    payment_gateway_id  = fields.Int(required=False, load_default=None)

class PaySlip(Schema):
    id = fields.Int(dump_only=True)
    gross_pay  = fields.Str(required=False, load_default=0)
    template_id = fields.Int(required=False, load_default="Null")
    salary_template_components = fields.Nested(SalaryTemplateComponentSchema, dump_only=True, many=True)

class RemarkSchema(Schema):
    id = fields.Int(dump_only=True, required=False, allow_none=True, load_default=None)
    created_at = fields.DateTime(dump_only=True, required=False, allow_none=True, load_default=None)
    updated_at = fields.DateTime(dump_only=True, required=False, allow_none=True, load_default=None)
    user_id = fields.Int(required=False, allow_none=True, load_default=None)
    employee_id = fields.Int(required=False, allow_none=True, load_default=None)
    payroll_history_id = fields.Int(required=False, allow_none=True, load_default=None)
    remark = fields.Str(required=False, allow_none=True, load_default=None)

class PaymentGatewaySchema(Schema):
    id = fields.Int(dump_only=True, required=False, allow_none=True, load_default=None)
    user_id = fields.Int(required=False, allow_none=True, load_default=None)
    payment_gateway_name = fields.Str(required=False, allow_none=True, load_default=None)


class AdminActivitySchema(Schema):
    id = fields.Int(dump_only=True, required=False, allow_none=True, load_default=None)
    message = fields.Str(required=True, allow_none=False)
    user_id = fields.Int(required=True, allow_none=False)
    created_at = fields.DateTime(dump_only=True, required=False, allow_none=True, load_default=None)
    updated_at = fields.DateTime(dump_only=True, required=False, allow_none=True, load_default=None)
    owner = fields.Nested('UserSchema', dump_only=True)

class EmployeeActivitySchema(Schema):
    id = fields.Int(dump_only=True, required=False, allow_none=True, load_default=None)
    message = fields.Str(required=False, allow_none=True, load_default=None)
    employee_id = fields.Int(required=True, allow_none=False)
    created_at = fields.DateTime(dump_only=True, required=False, allow_none=True, load_default=None)
    updated_at = fields.DateTime(dump_only=True, required=False, allow_none=True, load_default=None)
    employee = fields.Nested('EmployeeSchema', dump_only=True)


class UpdateApprovalSchema(Schema):
    status = fields.String(required=True)
    reason = fields.String(required=False)
    employee_id = fields.Int()
    pay_schedules_id = fields.Int(required=False)
    
    

class ApprovalHistoryResponseSchema(Schema):
    id = fields.Int()
    status = fields.String()
    reason = fields.String()
    approval_id = fields.Int()
    pay_schedules_id = fields.Int()
    employee_id = fields.Int()
    created_at = fields.DateTime()
    date_approved = fields.DateTime()
    approval = fields.Nested(ApprovalSchema)
    pay_schedule = fields.Nested(CreatePaySchedleSchema)
    employee = fields.Nested(EmployeeDetailsSchema)

class EmployeePayslip(Schema):    
    id = fields.Int(dump_only=True)
    user_id = fields.Str(dump_only=True)
    gross_pay = fields.Float(as_string=True, required=True)
    prorated_gross = fields.Float(as_string=True, required=True)
    payschedle_name= fields.Str(required=False, allow_none=True, load_default=None)
    is_prorated = fields.Boolean(required=False, allow_none=True, load_default=None)
    created_at = fields.DateTime(dump_only=True, required=False)
    updated_at = fields.DateTime(dump_only=True, required=False)

class EmailConfigSchema(Schema):
    smtp_server = fields.String(required=True)
    smtp_port = fields.Int(required=True)
    username = fields.String(required=True)
    password = fields.String(required=True)
    sender_email = fields.String(required=True)
    use_tls = fields.Bool(default=False, missing=False)
    use_ssl = fields.Bool(default=False, missing=False)
    is_active = fields.Bool(default=False, missing=False)
    user_id = fields.Str(dump_only=True)

    @validates('use_tls')
    def validate_tls_ssl(self, value):
        if self.context.get('use_ssl') and value:
            raise ValidationError("Cannot use both TLS and SSL at the same time.")

class EmailConfigUpdateSchema(Schema):
    id = fields.Int(dump_only=True)
    active = fields.Boolean()
    user_id = fields.Int(dump_only=True)
