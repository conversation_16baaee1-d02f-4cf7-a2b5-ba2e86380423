from sqlalchemy import <PERSON>um<PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, DateTime, Text, Integer, Foreign<PERSON>ey, Enum as SQLEnum, Date
from sqlalchemy.dialects.postgresql import UUID, JSONB
from sqlalchemy.orm import relationship
from uuid import uuid4
from datetime import datetime, date
import enum

from ..base import BaseModel, AuditMixin


class WorkflowStatus(str, enum.Enum):
    """Workflow status enumeration"""
    NOT_STARTED = "not_started"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    CANCELLED = "cancelled"
    ON_HOLD = "on_hold"


class TaskStatus(str, enum.Enum):
    """Task status enumeration"""
    PENDING = "pending"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    SKIPPED = "skipped"
    FAILED = "failed"
    OVERDUE = "overdue"


class TaskType(str, enum.Enum):
    """Task type enumeration"""
    DOCUMENT_COLLECTION = "document_collection"
    ACCOUNT_SETUP = "account_setup"
    EQUIPMENT_ASSIGNMENT = "equipment_assignment"
    TRAINING = "training"
    ORIENTATION = "orientation"
    MEETING = "meeting"
    APPROVAL = "approval"
    VERIFICATION = "verification"
    CUSTOM = "custom"


class DocumentStatus(str, enum.Enum):
    """Document status enumeration"""
    PENDING = "pending"
    SUBMITTED = "submitted"
    APPROVED = "approved"
    REJECTED = "rejected"
    EXPIRED = "expired"


class OnboardingWorkflow(BaseModel, AuditMixin):
    """Onboarding workflow model"""
    __tablename__ = "onboarding_workflows"

    # References
    employee_id = Column(UUID(as_uuid=True), ForeignKey("employees.id"), nullable=False)
    template_id = Column(UUID(as_uuid=True), ForeignKey("workflow_templates.id"), nullable=True)
    
    # Workflow Information
    title = Column(String(200), nullable=False)
    description = Column(Text, nullable=True)
    status = Column(SQLEnum(WorkflowStatus), nullable=False, default=WorkflowStatus.NOT_STARTED)
    
    # Dates
    start_date = Column(Date, nullable=False)
    expected_completion_date = Column(Date, nullable=True)
    actual_completion_date = Column(Date, nullable=True)
    
    # Assignment
    assigned_to_id = Column(UUID(as_uuid=True), ForeignKey("employees.id"), nullable=True)  # HR/Manager responsible
    buddy_id = Column(UUID(as_uuid=True), ForeignKey("employees.id"), nullable=True)  # Assigned buddy/mentor
    
    # Progress Tracking
    total_tasks = Column(Integer, nullable=False, default=0)
    completed_tasks = Column(Integer, nullable=False, default=0)
    progress_percentage = Column(Integer, nullable=False, default=0)
    
    # Additional Information
    notes = Column(Text, nullable=True)
    custom_fields = Column(JSONB, nullable=True)  # Additional custom data
    
    # Organization
    organization_id = Column(UUID(as_uuid=True), nullable=True, index=True)
    
    # Relationships
    employee = relationship("Employee", foreign_keys=[employee_id], back_populates="onboarding_workflows")
    assigned_to = relationship("Employee", foreign_keys=[assigned_to_id])
    buddy = relationship("Employee", foreign_keys=[buddy_id])
    template = relationship("WorkflowTemplate", back_populates="onboarding_workflows")
    tasks = relationship("OnboardingTask", back_populates="workflow")
    documents = relationship("OnboardingDocument", back_populates="workflow")

    def __repr__(self):
        return f"<OnboardingWorkflow(employee_id='{self.employee_id}', status='{self.status}')>"


class OffboardingWorkflow(BaseModel, AuditMixin):
    """Offboarding workflow model"""
    __tablename__ = "offboarding_workflows"

    # References
    employee_id = Column(UUID(as_uuid=True), ForeignKey("employees.id"), nullable=False)
    template_id = Column(UUID(as_uuid=True), ForeignKey("workflow_templates.id"), nullable=True)
    
    # Workflow Information
    title = Column(String(200), nullable=False)
    description = Column(Text, nullable=True)
    status = Column(SQLEnum(WorkflowStatus), nullable=False, default=WorkflowStatus.NOT_STARTED)
    
    # Offboarding Details
    last_working_date = Column(Date, nullable=False)
    reason_for_leaving = Column(String(100), nullable=True)
    resignation_date = Column(Date, nullable=True)
    notice_period_days = Column(Integer, nullable=True)
    
    # Dates
    start_date = Column(Date, nullable=False)
    expected_completion_date = Column(Date, nullable=True)
    actual_completion_date = Column(Date, nullable=True)
    
    # Assignment
    assigned_to_id = Column(UUID(as_uuid=True), ForeignKey("employees.id"), nullable=True)  # HR responsible
    
    # Progress Tracking
    total_tasks = Column(Integer, nullable=False, default=0)
    completed_tasks = Column(Integer, nullable=False, default=0)
    progress_percentage = Column(Integer, nullable=False, default=0)
    
    # Exit Interview
    exit_interview_scheduled = Column(Boolean, default=False, nullable=False)
    exit_interview_date = Column(DateTime, nullable=True)
    exit_interview_feedback = Column(Text, nullable=True)
    
    # Additional Information
    notes = Column(Text, nullable=True)
    custom_fields = Column(JSONB, nullable=True)
    
    # Organization
    organization_id = Column(UUID(as_uuid=True), nullable=True, index=True)
    
    # Relationships
    employee = relationship("Employee", foreign_keys=[employee_id], back_populates="offboarding_workflows")
    assigned_to = relationship("Employee", foreign_keys=[assigned_to_id])
    template = relationship("WorkflowTemplate", back_populates="offboarding_workflows")
    tasks = relationship("OffboardingTask", back_populates="workflow")
    documents = relationship("OffboardingDocument", back_populates="workflow")

    def __repr__(self):
        return f"<OffboardingWorkflow(employee_id='{self.employee_id}', status='{self.status}')>"


class WorkflowTemplate(BaseModel, AuditMixin):
    """Workflow template model for reusable onboarding/offboarding processes"""
    __tablename__ = "workflow_templates"

    # Template Information
    name = Column(String(200), nullable=False)
    description = Column(Text, nullable=True)
    workflow_type = Column(String(50), nullable=False)  # onboarding, offboarding
    
    # Configuration
    is_active = Column(Boolean, default=True, nullable=False)
    is_default = Column(Boolean, default=False, nullable=False)
    
    # Applicability
    department_ids = Column(JSONB, nullable=True)  # Array of department IDs this applies to
    role_names = Column(JSONB, nullable=True)  # Array of roles this applies to
    
    # Template Structure
    task_templates = Column(JSONB, nullable=False)  # Array of task template definitions
    document_templates = Column(JSONB, nullable=True)  # Array of required documents
    
    # Timing
    default_duration_days = Column(Integer, nullable=True)
    
    # Organization
    organization_id = Column(UUID(as_uuid=True), nullable=True, index=True)
    
    # Relationships
    onboarding_workflows = relationship("OnboardingWorkflow", back_populates="template")
    offboarding_workflows = relationship("OffboardingWorkflow", back_populates="template")

    def __repr__(self):
        return f"<WorkflowTemplate(name='{self.name}', type='{self.workflow_type}')>"


class OnboardingTask(BaseModel, AuditMixin):
    """Onboarding task model"""
    __tablename__ = "onboarding_tasks"

    # References
    workflow_id = Column(UUID(as_uuid=True), ForeignKey("onboarding_workflows.id"), nullable=False)
    
    # Task Information
    title = Column(String(200), nullable=False)
    description = Column(Text, nullable=True)
    task_type = Column(SQLEnum(TaskType), nullable=False)
    status = Column(SQLEnum(TaskStatus), nullable=False, default=TaskStatus.PENDING)
    
    # Ordering and Dependencies
    order_index = Column(Integer, nullable=False)
    depends_on_task_ids = Column(JSONB, nullable=True)  # Array of task IDs this depends on
    
    # Assignment
    assigned_to_id = Column(UUID(as_uuid=True), ForeignKey("employees.id"), nullable=True)
    assigned_to_role = Column(String(100), nullable=True)  # HR, IT, Manager, etc.
    
    # Timing
    due_date = Column(Date, nullable=True)
    estimated_hours = Column(Integer, nullable=True)
    
    # Completion
    completed_date = Column(DateTime, nullable=True)
    completed_by_id = Column(UUID(as_uuid=True), ForeignKey("employees.id"), nullable=True)
    completion_notes = Column(Text, nullable=True)
    
    # Task Configuration
    is_mandatory = Column(Boolean, default=True, nullable=False)
    auto_complete = Column(Boolean, default=False, nullable=False)
    requires_approval = Column(Boolean, default=False, nullable=False)
    
    # Additional Data
    task_data = Column(JSONB, nullable=True)  # Task-specific configuration
    attachments = Column(JSONB, nullable=True)  # Array of attachment URLs
    
    # Organization
    organization_id = Column(UUID(as_uuid=True), nullable=True, index=True)
    
    # Relationships
    workflow = relationship("OnboardingWorkflow", back_populates="tasks")
    assigned_to = relationship("Employee", foreign_keys=[assigned_to_id])
    completed_by = relationship("Employee", foreign_keys=[completed_by_id])

    def __repr__(self):
        return f"<OnboardingTask(title='{self.title}', status='{self.status}')>"


class OffboardingTask(BaseModel, AuditMixin):
    """Offboarding task model"""
    __tablename__ = "offboarding_tasks"

    # References
    workflow_id = Column(UUID(as_uuid=True), ForeignKey("offboarding_workflows.id"), nullable=False)
    
    # Task Information
    title = Column(String(200), nullable=False)
    description = Column(Text, nullable=True)
    task_type = Column(SQLEnum(TaskType), nullable=False)
    status = Column(SQLEnum(TaskStatus), nullable=False, default=TaskStatus.PENDING)
    
    # Ordering and Dependencies
    order_index = Column(Integer, nullable=False)
    depends_on_task_ids = Column(JSONB, nullable=True)
    
    # Assignment
    assigned_to_id = Column(UUID(as_uuid=True), ForeignKey("employees.id"), nullable=True)
    assigned_to_role = Column(String(100), nullable=True)
    
    # Timing
    due_date = Column(Date, nullable=True)
    estimated_hours = Column(Integer, nullable=True)
    
    # Completion
    completed_date = Column(DateTime, nullable=True)
    completed_by_id = Column(UUID(as_uuid=True), ForeignKey("employees.id"), nullable=True)
    completion_notes = Column(Text, nullable=True)
    
    # Task Configuration
    is_mandatory = Column(Boolean, default=True, nullable=False)
    auto_complete = Column(Boolean, default=False, nullable=False)
    requires_approval = Column(Boolean, default=False, nullable=False)
    
    # Additional Data
    task_data = Column(JSONB, nullable=True)
    attachments = Column(JSONB, nullable=True)
    
    # Organization
    organization_id = Column(UUID(as_uuid=True), nullable=True, index=True)
    
    # Relationships
    workflow = relationship("OffboardingWorkflow", back_populates="tasks")
    assigned_to = relationship("Employee", foreign_keys=[assigned_to_id])
    completed_by = relationship("Employee", foreign_keys=[completed_by_id])

    def __repr__(self):
        return f"<OffboardingTask(title='{self.title}', status='{self.status}')>"


class OnboardingDocument(BaseModel, AuditMixin):
    """Onboarding document model"""
    __tablename__ = "onboarding_documents"

    # References
    workflow_id = Column(UUID(as_uuid=True), ForeignKey("onboarding_workflows.id"), nullable=False)
    
    # Document Information
    name = Column(String(200), nullable=False)
    description = Column(Text, nullable=True)
    document_type = Column(String(100), nullable=False)  # contract, id_proof, tax_form, etc.
    
    # Status and Dates
    status = Column(SQLEnum(DocumentStatus), nullable=False, default=DocumentStatus.PENDING)
    submitted_date = Column(DateTime, nullable=True)
    reviewed_date = Column(DateTime, nullable=True)
    expiry_date = Column(Date, nullable=True)
    
    # File Information
    file_url = Column(String(500), nullable=True)
    file_name = Column(String(255), nullable=True)
    file_size = Column(Integer, nullable=True)  # Size in bytes
    file_type = Column(String(50), nullable=True)  # MIME type
    
    # Review Information
    reviewed_by_id = Column(UUID(as_uuid=True), ForeignKey("employees.id"), nullable=True)
    review_notes = Column(Text, nullable=True)
    rejection_reason = Column(Text, nullable=True)
    
    # Requirements
    is_mandatory = Column(Boolean, default=True, nullable=False)
    requires_verification = Column(Boolean, default=False, nullable=False)
    
    # Organization
    organization_id = Column(UUID(as_uuid=True), nullable=True, index=True)
    
    # Relationships
    workflow = relationship("OnboardingWorkflow", back_populates="documents")
    reviewed_by = relationship("Employee")

    def __repr__(self):
        return f"<OnboardingDocument(name='{self.name}', status='{self.status}')>"


class OffboardingDocument(BaseModel, AuditMixin):
    """Offboarding document model"""
    __tablename__ = "offboarding_documents"

    # References
    workflow_id = Column(UUID(as_uuid=True), ForeignKey("offboarding_workflows.id"), nullable=False)
    
    # Document Information
    name = Column(String(200), nullable=False)
    description = Column(Text, nullable=True)
    document_type = Column(String(100), nullable=False)  # clearance_form, handover_doc, etc.
    
    # Status and Dates
    status = Column(SQLEnum(DocumentStatus), nullable=False, default=DocumentStatus.PENDING)
    submitted_date = Column(DateTime, nullable=True)
    reviewed_date = Column(DateTime, nullable=True)
    
    # File Information
    file_url = Column(String(500), nullable=True)
    file_name = Column(String(255), nullable=True)
    file_size = Column(Integer, nullable=True)
    file_type = Column(String(50), nullable=True)
    
    # Review Information
    reviewed_by_id = Column(UUID(as_uuid=True), ForeignKey("employees.id"), nullable=True)
    review_notes = Column(Text, nullable=True)
    
    # Requirements
    is_mandatory = Column(Boolean, default=True, nullable=False)
    
    # Organization
    organization_id = Column(UUID(as_uuid=True), nullable=True, index=True)
    
    # Relationships
    workflow = relationship("OffboardingWorkflow", back_populates="documents")
    reviewed_by = relationship("Employee")

    def __repr__(self):
        return f"<OffboardingDocument(name='{self.name}', status='{self.status}')>"
