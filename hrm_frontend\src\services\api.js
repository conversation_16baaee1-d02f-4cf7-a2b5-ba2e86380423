/**
 * API Service for HRMS Frontend
 * Centralized API communication with error handling and authentication
 */

const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8085/api';

class ApiService {
  constructor() {
    this.baseURL = API_BASE_URL;
  }

  // Get authentication token from localStorage
  getAuthToken() {
    return localStorage.getItem('hrm_auth_token');
  }

  // Get default headers with authentication
  getHeaders() {
    const token = this.getAuthToken();
    const headers = {
      'Content-Type': 'application/json',
    };
    
    if (token) {
      headers['Authorization'] = `Bearer ${token}`;
    }
    
    return headers;
  }

  // Generic request method
  async request(endpoint, options = {}) {
    const url = `${this.baseURL}${endpoint}`;
    const config = {
      headers: this.getHeaders(),
      ...options,
    };

    try {
      const response = await fetch(url, config);

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`HTTP error! status: ${response.status} - ${errorText}`);
      }

      const data = await response.json();
      return data;
    } catch (error) {
      console.error('API request failed:', error);
      throw error;
    }
  }

  // GET request
  async get(endpoint, params = {}) {
    const queryString = new URLSearchParams(params).toString();
    const url = queryString ? `${endpoint}?${queryString}` : endpoint;
    
    return this.request(url, {
      method: 'GET',
    });
  }

  // POST request
  async post(endpoint, data = {}) {
    return this.request(endpoint, {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  // PUT request
  async put(endpoint, data = {}) {
    return this.request(endpoint, {
      method: 'PUT',
      body: JSON.stringify(data),
    });
  }

  // DELETE request
  async delete(endpoint) {
    return this.request(endpoint, {
      method: 'DELETE',
    });
  }

  // Authentication APIs
  async login(credentials) {
    return this.post('/auth/login', credentials);
  }

  async logout() {
    return this.post('/auth/logout');
  }

  async refreshToken() {
    return this.post('/auth/refresh');
  }

  // Employee APIs
  async getEmployees(params = {}) {
    return this.get('/employees/', params);
  }

  async getEmployee(id) {
    return this.get(`/employees/${id}`);
  }

  async createEmployee(data) {
    return this.post('/employees/', data);
  }

  async updateEmployee(id, data) {
    return this.put(`/employees/${id}`, data);
  }

  async deleteEmployee(id) {
    return this.delete(`/employees/${id}`);
  }

  // Attendance APIs
  async getAttendance(params = {}) {
    return this.get('/attendance/', params);
  }

  async checkIn(data) {
    return this.post('/attendance/checkin', data);
  }

  async checkOut(data) {
    return this.post('/attendance/checkout', data);
  }

  async getMyAttendanceStatus() {
    return this.get('/attendance/my/status');
  }

  // Leave APIs
  async getLeaveRequests(params = {}) {
    return this.get('/leave/requests', params);
  }

  async createLeaveRequest(data) {
    console.log('🔍 API Service - Creating leave request with data:', data);
    console.log('🔍 API Service - Duration type being sent:', data.duration_type);
    return this.post('/leave/my/requests', data);
  }

  async getMyLeaveBalance() {
    return this.get('/leave/my/balance');
  }

  async getLeavePolicies() {
    console.log('🔄 API: Getting leave policies...');
    console.log('🌐 API: Base URL:', this.baseURL);
    console.log('🔑 API: Token present:', !!this.getAuthToken());

    try {
      const result = await this.get('/leave/policies');
      console.log('✅ API: Leave policies received:', result);
      return result;
    } catch (error) {
      console.error('❌ API: Leave policies failed:', error);
      throw error;
    }
  }

  async getLeaveDashboard() {
    return this.get('/leave/dashboard');
  }

  async approveLeaveRequest(requestId, approved, comments = '') {
    const endpoint = approved ? 'approve' : 'reject';
    return this.put(`/leave/requests/${requestId}/${endpoint}`, {
      approved,
      comments
    });
  }

  async getPendingLeaveRequests() {
    return this.get('/leave/requests?status=pending');
  }

  async getLeaveCalendar(params = {}) {
    return this.get('/leave/calendar', params);
  }

  // Payroll APIs
  async getPayslips(params = {}) {
    return this.get('/payroll/payslips', params);
  }

  async getMyPayslips(params = {}) {
    return this.get('/payroll/my/payslips', params);
  }

  async getSalaryBreakdown() {
    return this.get('/payroll/my/salary-breakdown');
  }

  // Project APIs
  async getProjects(params = {}) {
    return this.get('/project/', params);
  }

  async createProject(data) {
    return this.post('/project/', data);
  }

  async getProjectTasks(projectId, params = {}) {
    return this.get(`/project/${projectId}/tasks`, params);
  }

  // Kanban APIs
  async getKanbanBoards(params = {}) {
    return this.get('/kanban/boards', params);
  }

  async getKanbanCards(boardId, params = {}) {
    return this.get(`/kanban/boards/${boardId}/cards`, params);
  }

  async createKanbanCard(data) {
    return this.post('/kanban/cards', data);
  }

  async moveKanbanCard(cardId, data) {
    return this.put(`/kanban/cards/${cardId}/move`, data);
  }

  // Task APIs
  async getTasks(params = {}) {
    return this.get('/project/tasks', params);
  }

  async createTask(data) {
    return this.post('/project/tasks', data);
  }

  async updateTask(id, data) {
    return this.put(`/project/tasks/${id}`, data);
  }

  // Ticket APIs
  async getTickets(params = {}) {
    return this.get('/ticket/', params);
  }

  async createTicket(data) {
    return this.post('/ticket/', data);
  }

  async updateTicket(id, data) {
    return this.put(`/ticket/${id}`, data);
  }

  async getMyTickets(params = {}) {
    return this.get('/ticket/my', params);
  }

  // Performance APIs
  async getPerformanceReviews(params = {}) {
    return this.get('/performance/reviews', params);
  }

  async getMyPerformanceReviews(params = {}) {
    return this.get('/performance/my/reviews', params);
  }

  async getPerformanceGoals(params = {}) {
    return this.get('/performance/goals', params);
  }

  async createPerformanceGoal(data) {
    return this.post('/performance/goals', data);
  }

  // Reports APIs
  async getOverviewReport(params = {}) {
    return this.get('/reports/overview', params);
  }

  async getAttendanceReport(params = {}) {
    return this.get('/reports/attendance', params);
  }

  async getLeaveReport(params = {}) {
    return this.get('/reports/leave', params);
  }

  async getPayrollReport(params = {}) {
    return this.get('/reports/payroll', params);
  }

  async getPerformanceReport(params = {}) {
    return this.get('/reports/performance', params);
  }

  async getProjectsReport(params = {}) {
    return this.get('/reports/projects', params);
  }

  async getTicketsReport(params = {}) {
    return this.get('/reports/tickets', params);
  }

  async exportReport(reportType, params = {}) {
    return this.get(`/reports/export/${reportType}`, params);
  }

  // Settings APIs
  async getUserPreferences() {
    return this.get('/settings/user/preferences');
  }

  async updateUserPreferences(data) {
    return this.put('/settings/user/preferences', data);
  }

  async getNotificationSettings() {
    return this.get('/settings/user/notifications');
  }

  async updateNotificationSettings(data) {
    return this.put('/settings/user/notifications', data);
  }

  async getSecuritySettings() {
    return this.get('/settings/user/security');
  }

  async updateSecuritySettings(data) {
    return this.put('/settings/user/security', data);
  }

  async getSystemSettings() {
    return this.get('/settings/system');
  }

  async updateSystemSettings(data) {
    return this.put('/settings/system', data);
  }

  // Timesheet APIs
  async getTimesheets(params = {}) {
    return this.get('/timesheet/', params);
  }

  async createTimesheetEntry(data) {
    return this.post('/timesheet/', data);
  }

  async getTimesheetApprovals(params = {}) {
    return this.get('/timesheet/approvals', params);
  }

  async approveTimesheet(id) {
    return this.put(`/timesheet/${id}/approve`);
  }

  async rejectTimesheet(id, reason) {
    return this.put(`/timesheet/${id}/reject`, { reason });
  }

  // Shift APIs
  async getShifts(params = {}) {
    return this.get('/shift/', params);
  }

  async getMyShiftSchedule(params = {}) {
    return this.get('/shift/my/schedule', params);
  }

  async requestShiftSwap(data) {
    return this.post('/shift/swap/request', data);
  }

  async getShiftSwapRequests(params = {}) {
    return this.get('/shift/swap/requests', params);
  }
}

// Create and export a singleton instance
const apiService = new ApiService();
export default apiService;
