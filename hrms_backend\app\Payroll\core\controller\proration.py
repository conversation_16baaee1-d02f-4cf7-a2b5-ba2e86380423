from flask.views import MethodView
from core.services.payroll_history import PayrollHistoryService
from core.services.remarks import RemarkService
from flask_smorest import Blueprint, abort
from core.middleware import roles_required
from schemas import PayrollHistorySchema,UpdatePayrollHistoryProratedData
from core.services.employee import EmployeeService
from core.utils.responseBuilder import ResponseBuilder
from schemas import ProrateSalarySchema
from core.services.prorate_salaries import ProrateSalaryService
from core.services.component_processor import ComponentProcessor

blueprint = Blueprint("proration", __name__, description="Operations for proration")

@blueprint.route('/prorate-salary/<int:id>', methods=['POST'])
class Proration(MethodView):
    @roles_required(['admin'])
    @blueprint.arguments(ProrateSalarySchema)
    @blueprint.response(201, ProrateSalarySchema)
    def post(self, data, id):
        p_id = data.get("p_id")

        service = ProrateSalaryService()
        employeeService = EmployeeService()

        # Get the employee by ID
        employee = employeeService.getEmployeeById(id)
        if not employee:
            abort(404, message="Employee does not exist")

        # Add the employee ID to the data
        data['employee_id'] = id
        del data["p_id"]

        # Proceed with creating the prorated salary
        new_prorated_salary = service.create_prorate_salary(data)

        if new_prorated_salary:
            self.update_payroll_history(p_id, data)
        
        return new_prorated_salary

    def update_payroll_history(self, p_id, data):
        try:
            # id = data.get("payroll_id")
            payroll_object = {
                "is_prorated" : True,
                "prorated_gross" : data["gross_pay"],
                "prorated_net" : data["netpay"],
                "prorated_monthly_tax" : data["monthly_tax"],
                "prorated_annual_tax" : data["annual_tax"],
                "overtime_amount": data.get("overtime_amount", 0.0),
                "salary_arrears": data.get("salary_arrears", 0.0),
                "surcharge": data.get("surcharge", 0.0),
            }
            payroll_data = UpdatePayrollHistoryProratedData().dump(payroll_object)

            updated = PayrollHistoryService().payroll_history_update(p_id, payroll_data)
            # print("Payroll History Updated:", updated)

            if not updated:
                raise Exception("Payroll history update failed!")

        except KeyError as e:
            print(f"KeyError: Missing key {e}")
            return {"error": f"Missing required key: {str(e)}"}, 400

        except AttributeError as e:
            print(f"AttributeError: {e}")
            return {"error": f"Attribute error: {str(e)}"}, 500

        except ValueError as e:
            print(f"ValueError: {e}")
            return {"error": str(e)}, 400

        except Exception as e:
            print(f"Unexpected error: {e}")
            return {"error": f"An unexpected error occurred: {str(e)}"}, 500
    
@blueprint.route("/prorations")
class ProrationList(MethodView):
    @roles_required(['admin'])
    @blueprint.response(200, PayrollHistorySchema(many=True))
    def get(self):
        payroll_service = PayrollHistoryService()
        payroll_history_objects = payroll_service.get_payroll_history_by_key({"is_prorated": True})

        payroll_history_schema = PayrollHistorySchema(many=True)
        payroll_history_data = payroll_history_schema.dump(payroll_history_objects)
        remark_service = RemarkService()

        res = []
        payroll_history = []
        for item in payroll_history_data:
            prorated_gross = item["prorated_gross"]
            prorated_net = item["prorated_net"]
            prorated_monthly_tax = item["prorated_monthly_tax"]
            prorated_annual_tax = item["prorated_annual_tax"]
            prorated_status = item["is_prorated"]
            processed_status = item["is_processed"]
            processed_time = item["is_processed_created"]
            payment_status = item["payment_status"]
            transaction_message = item["message"] 
            transaction_date = item["transaction_date"] 
            
            #print(item)
            component_processor = ComponentProcessor(
                item["employee"]["id"],
                item["employee"]["gross_pay"],
                prorated_gross,
                prorated_status,
                salary_benefit=item["employee"]["employee_benefits"],
                salary_component=item["employee"]["employee_components"]
            )
            res = component_processor.generate_salary_response()
            res["employee"] = item.get("employee")
            res["created_at"] = item.get("created_at")
            res["updated_at"] = item.get("updated_at")
            res["payroll_id"] = item.get("id")
            res["prorated_gross"] = prorated_gross
            res["prorated_status"] = prorated_status 
            res["prorated_net"] = prorated_net 
            res["prorated_monthly_tax"] = prorated_monthly_tax 
            res["prorated_annual_tax"] = prorated_annual_tax 
            res["is_processed"] = processed_status 
            res["processed_time"] = processed_time
            res["payment_status"] = payment_status
            res["transaction_message"] = transaction_message 
            res["transaction_date"] = transaction_date 
            res["currency"] = item.get("employee").get("currency")
            payroll_history.append(res)         
            
            # Get remarks for this payroll history
            payroll_history_id = item.get("id")
            remark_obj = remark_service.getRemarksByPayrollHistoryId(payroll_history_id) if payroll_history_id else None
            # Extract only the "remark" field
            remark_text = remark_obj[0].remark if remark_obj else None  # Get the first remark if available
            res["remark"] = remark_text  # Store directly as a string

        # Build the final response
        response = ResponseBuilder(
            data=payroll_history,
            status_code=200,
            total=len(payroll_history)
        ).build()
        return response