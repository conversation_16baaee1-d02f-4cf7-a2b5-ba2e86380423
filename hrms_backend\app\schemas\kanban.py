from pydantic import BaseModel, Field, validator
from typing import Optional, List, Dict, Any
from uuid import UUID
from datetime import datetime
from enum import Enum


class BoardType(str, Enum):
    PROJECT = "project"
    TEAM = "team"
    PERSONAL = "personal"
    DEPARTMENT = "department"


class CardPriority(str, Enum):
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


# Kanban Board Schemas
class KanbanBoardBase(BaseModel):
    name: str = Field(..., min_length=1, max_length=200)
    description: Optional[str] = None
    board_type: BoardType = BoardType.TEAM
    is_public: bool = False
    allow_comments: bool = True
    allow_attachments: bool = True
    background_color: Optional[str] = Field(None, pattern="^#[0-9A-Fa-f]{6}$")
    background_image_url: Optional[str] = Field(None, max_length=500)
    auto_assign_cards: bool = False
    card_aging_enabled: bool = False
    wip_limits_enabled: bool = False


class KanbanBoardCreate(KanbanBoardBase):
    project_id: Optional[UUID] = None
    department_id: Optional[UUID] = None


class KanbanBoardUpdate(BaseModel):
    name: Optional[str] = Field(None, min_length=1, max_length=200)
    description: Optional[str] = None
    is_public: Optional[bool] = None
    allow_comments: Optional[bool] = None
    allow_attachments: Optional[bool] = None
    background_color: Optional[str] = Field(None, pattern="^#[0-9A-Fa-f]{6}$")
    background_image_url: Optional[str] = Field(None, max_length=500)
    auto_assign_cards: Optional[bool] = None
    card_aging_enabled: Optional[bool] = None
    wip_limits_enabled: Optional[bool] = None


class KanbanBoardResponse(KanbanBoardBase):
    id: UUID
    organization_id: UUID
    project_id: Optional[UUID] = None
    department_id: Optional[UUID] = None
    owner_id: UUID
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class KanbanBoardListResponse(BaseModel):
    boards: List[KanbanBoardResponse]
    total: int
    skip: int
    limit: int


# Kanban Column Schemas
class KanbanColumnBase(BaseModel):
    name: str = Field(..., min_length=1, max_length=100)
    position: int = Field(..., ge=0)
    color: Optional[str] = Field(None, pattern="^#[0-9A-Fa-f]{6}$")
    wip_limit: Optional[int] = Field(None, ge=0)
    is_done_column: bool = False
    is_backlog_column: bool = False


class KanbanColumnCreate(KanbanColumnBase):
    auto_assign_to: Optional[UUID] = None


class KanbanColumnUpdate(BaseModel):
    name: Optional[str] = Field(None, min_length=1, max_length=100)
    position: Optional[int] = Field(None, ge=0)
    color: Optional[str] = Field(None, pattern="^#[0-9A-Fa-f]{6}$")
    wip_limit: Optional[int] = Field(None, ge=0)
    is_done_column: Optional[bool] = None
    is_backlog_column: Optional[bool] = None
    auto_assign_to: Optional[UUID] = None


class KanbanColumnResponse(KanbanColumnBase):
    id: UUID
    board_id: UUID
    auto_assign_to: Optional[UUID] = None
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


# Kanban Card Schemas
class KanbanCardBase(BaseModel):
    title: str = Field(..., min_length=1, max_length=200)
    description: Optional[str] = None
    priority: CardPriority = CardPriority.MEDIUM
    due_date: Optional[datetime] = None
    start_date: Optional[datetime] = None
    story_points: Optional[int] = Field(None, ge=0)
    estimated_hours: Optional[int] = Field(None, ge=0)  # in minutes
    labels: Optional[List[Dict[str, Any]]] = None
    external_links: Optional[List[Dict[str, Any]]] = None


class KanbanCardCreate(KanbanCardBase):
    column_id: UUID
    position: int = Field(..., ge=0)
    assignee_id: Optional[UUID] = None
    task_id: Optional[UUID] = None
    attachment_urls: Optional[List[str]] = None


class KanbanCardUpdate(BaseModel):
    title: Optional[str] = Field(None, min_length=1, max_length=200)
    description: Optional[str] = None
    column_id: Optional[UUID] = None
    position: Optional[int] = Field(None, ge=0)
    assignee_id: Optional[UUID] = None
    priority: Optional[CardPriority] = None
    due_date: Optional[datetime] = None
    start_date: Optional[datetime] = None
    completed_date: Optional[datetime] = None
    story_points: Optional[int] = Field(None, ge=0)
    estimated_hours: Optional[int] = Field(None, ge=0)
    labels: Optional[List[Dict[str, Any]]] = None
    external_links: Optional[List[Dict[str, Any]]] = None
    attachment_urls: Optional[List[str]] = None


class KanbanCardResponse(KanbanCardBase):
    id: UUID
    board_id: UUID
    column_id: UUID
    position: int
    assignee_id: Optional[UUID] = None
    reporter_id: UUID
    task_id: Optional[UUID] = None
    completed_date: Optional[datetime] = None
    attachment_urls: Optional[List[str]] = None
    created_in_column_at: datetime
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class KanbanCardListResponse(BaseModel):
    cards: List[KanbanCardResponse]
    total: int
    skip: int
    limit: int


# Card Movement Schema
class CardMoveRequest(BaseModel):
    card_id: UUID
    target_column_id: UUID
    target_position: int = Field(..., ge=0)


# Kanban Card Comment Schemas
class KanbanCardCommentBase(BaseModel):
    content: str = Field(..., min_length=1)
    attachment_urls: Optional[List[str]] = None


class KanbanCardCommentCreate(KanbanCardCommentBase):
    parent_comment_id: Optional[UUID] = None


class KanbanCardCommentUpdate(BaseModel):
    content: Optional[str] = Field(None, min_length=1)
    attachment_urls: Optional[List[str]] = None


class KanbanCardCommentResponse(KanbanCardCommentBase):
    id: UUID
    card_id: UUID
    author_id: UUID
    parent_comment_id: Optional[UUID] = None
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


# Kanban Card Checklist Schemas
class KanbanCardChecklistBase(BaseModel):
    name: str = Field(..., min_length=1, max_length=200)
    position: int = Field(..., ge=0)


class KanbanCardChecklistCreate(KanbanCardChecklistBase):
    pass


class KanbanCardChecklistUpdate(BaseModel):
    name: Optional[str] = Field(None, min_length=1, max_length=200)
    position: Optional[int] = Field(None, ge=0)


class KanbanCardChecklistResponse(KanbanCardChecklistBase):
    id: UUID
    card_id: UUID
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


# Kanban Checklist Item Schemas
class KanbanChecklistItemBase(BaseModel):
    name: str = Field(..., min_length=1, max_length=200)
    position: int = Field(..., ge=0)
    is_completed: bool = False
    assignee_id: Optional[UUID] = None
    due_date: Optional[datetime] = None


class KanbanChecklistItemCreate(KanbanChecklistItemBase):
    pass


class KanbanChecklistItemUpdate(BaseModel):
    name: Optional[str] = Field(None, min_length=1, max_length=200)
    position: Optional[int] = Field(None, ge=0)
    is_completed: Optional[bool] = None
    assignee_id: Optional[UUID] = None
    due_date: Optional[datetime] = None


class KanbanChecklistItemResponse(KanbanChecklistItemBase):
    id: UUID
    checklist_id: UUID
    completed_by: Optional[UUID] = None
    completed_at: Optional[datetime] = None
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


# Board Member Schemas
class KanbanBoardMemberBase(BaseModel):
    can_edit_board: bool = False
    can_add_cards: bool = True
    can_edit_cards: bool = True
    can_delete_cards: bool = False
    can_add_members: bool = False
    role: Optional[str] = Field(None, max_length=50)


class KanbanBoardMemberCreate(KanbanBoardMemberBase):
    employee_id: UUID


class KanbanBoardMemberUpdate(BaseModel):
    can_edit_board: Optional[bool] = None
    can_add_cards: Optional[bool] = None
    can_edit_cards: Optional[bool] = None
    can_delete_cards: Optional[bool] = None
    can_add_members: Optional[bool] = None
    role: Optional[str] = Field(None, max_length=50)


class KanbanBoardMemberResponse(KanbanBoardMemberBase):
    id: UUID
    board_id: UUID
    employee_id: UUID
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


# Board with Full Data
class KanbanBoardFullResponse(KanbanBoardResponse):
    columns: List[KanbanColumnResponse] = []
    members: List[KanbanBoardMemberResponse] = []


# Column with Cards
class KanbanColumnWithCardsResponse(KanbanColumnResponse):
    cards: List[KanbanCardResponse] = []


# Bulk Operations
class BulkCardUpdate(BaseModel):
    card_ids: List[UUID]
    column_id: Optional[UUID] = None
    assignee_id: Optional[UUID] = None
    priority: Optional[CardPriority] = None
    labels: Optional[List[Dict[str, Any]]] = None


class BulkCardMove(BaseModel):
    moves: List[CardMoveRequest]
