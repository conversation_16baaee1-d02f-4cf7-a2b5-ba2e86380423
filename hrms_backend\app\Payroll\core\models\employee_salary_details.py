from core.databases.database import db
from core.models.basemodel import ModelBase
class EmployeeSalaryDetailsModel(ModelBase):
    __tablename__ = "employee_salary_details"

    id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    employee_id = db.<PERSON>umn(db.<PERSON><PERSON>, db.<PERSON>('employees.id'), nullable=False)
    template_id = db.<PERSON>umn(db.Integer, db.Foreign<PERSON>ey('salary_templates.id'), nullable=False)
    amount = db.Column(db.Double, nullable=False)
    salary_type = db.Column(db.String(100), nullable=False)
    

    
    
    
      