from core.repositories.departments import DepartmentsRepository

class DepartmentsService:
    def __init__(self) -> None:
        self.repository = DepartmentsRepository()

    def createDepartments(self, Kwargs):
        return self.repository.createDepartments(**Kwargs)
    
    def fetchAll(self):
        departments = self.repository.fetchAll()
        total_department = len(departments)
        return [
            {
                "id": department.id,
                "name": department.name,
                "timestamp": department.timestamp.strftime("%Y-%m-%d"),
                "employee_count": employee_count
            }
            for department, employee_count in departments
        ], total_department
    
    def getDepartments(self, id):
        return self.repository.getDepartments(id)
    
    def updateDepartments(self, id, Kwargs):
        return self.repository.updateDepartments(id, **Kwargs)
    
    def getDepartmentsByKey(self, Kwarg):
        return self.repository.getDepartmentsByKeys(Kwarg)
    
    def deleteDepartments(self, id):
        return self.repository.deleteDepartments(id)