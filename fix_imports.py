#!/usr/bin/env python3
"""
Script to fix import paths in reorganized service files
"""

import os
import re

def fix_imports_in_file(file_path):
    """Fix import paths in a single file"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Fix relative imports that need to go up one more level
        patterns = [
            (r'from \.\.db\.', 'from ...db.'),
            (r'from \.\.schemas\.', 'from ...schemas.'),
            (r'from \.\.core\.', 'from ...core.'),
            (r'from \.\.utils\.', 'from ...utils.'),
        ]
        
        modified = False
        for pattern, replacement in patterns:
            new_content = re.sub(pattern, replacement, content)
            if new_content != content:
                content = new_content
                modified = True
        
        if modified:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"Fixed imports in: {file_path}")
        
    except Exception as e:
        print(f"Error processing {file_path}: {e}")

def main():
    """Main function to fix imports in all service files"""
    service_dirs = [
        'hrms_backend/app/services/hr_management',
        'hrms_backend/app/services/leave_management',
        'hrms_backend/app/services/shift_management',
        'hrms_backend/app/services/ticket_management',
        'hrms_backend/app/services/project_management'
    ]
    
    for service_dir in service_dirs:
        if os.path.exists(service_dir):
            for file_name in os.listdir(service_dir):
                if file_name.endswith('.py') and file_name != '__init__.py':
                    file_path = os.path.join(service_dir, file_name)
                    fix_imports_in_file(file_path)

if __name__ == '__main__':
    main()
