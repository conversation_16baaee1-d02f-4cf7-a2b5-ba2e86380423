"""Change organisation logoUrl column to Text

Revision ID: 4e3f26579fab
Revises: 975407f781aa
Create Date: 2025-03-26 01:44:29.322146

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '4e3f26579fab'
down_revision: Union[str, None] = '975407f781aa'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    op.alter_column("organisations", "logoUrl", type_=sa.Text())

def downgrade() -> None:
    op.alter_column("organisations", "logoUrl", type_=sa.String(200))