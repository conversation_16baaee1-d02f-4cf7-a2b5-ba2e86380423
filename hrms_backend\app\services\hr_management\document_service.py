"""
Document Service for business logic
"""

from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, func
from typing import Optional, List
from uuid import UUID, uuid4
from datetime import datetime, timedelta
from fastapi import HTTPException, status, UploadFile
import logging
import hashlib
import os
from pathlib import Path

from ...db.models.document import Document, DocumentAccess, DocumentShare, DocumentTemplate
from ...db.models.employee import Employee
from ...schemas.document import (
    DocumentCreate, DocumentUpdate, DocumentResponse, DocumentListResponse,
    DocumentUploadRequest, DocumentUploadResponse, DocumentAccessCreate,
    DocumentAccessResponse, DocumentShareCreate, DocumentShareResponse,
    DocumentTemplateCreate, DocumentTemplateUpdate, DocumentTemplateResponse,
    DocumentVersionResponse, DocumentVersionListResponse, DocumentType, DocumentStatus
)
from ...core.security import CurrentUser
from ...core.config import settings

logger = logging.getLogger(__name__)


class DocumentService:
    """Document service for business logic"""

    def __init__(self):
        self.upload_folder = getattr(settings, 'UPLOAD_FOLDER', 'uploads')
        self.max_file_size = getattr(settings, 'MAX_FILE_SIZE', 10485760)  # 10MB default

    async def get_documents(
        self,
        db: Session,
        current_user: CurrentUser,
        skip: int = 0,
        limit: int = 20,
        document_type: Optional[DocumentType] = None,
        status: Optional[DocumentStatus] = None,
        search: Optional[str] = None
    ) -> DocumentListResponse:
        """Get documents with filtering"""
        try:
            query = db.query(Document).filter(
                Document.organization_id == current_user.organization_id
            )

            # Apply filters
            if document_type:
                query = query.filter(Document.document_type == document_type)
            
            if status:
                query = query.filter(Document.status == status)
            else:
                # Default: exclude deleted documents
                query = query.filter(Document.status != DocumentStatus.DELETED)

            if search:
                query = query.filter(
                    or_(
                        Document.name.ilike(f"%{search}%"),
                        Document.description.ilike(f"%{search}%")
                    )
                )

            # Apply access control
            if current_user.role.upper() not in ["ADMIN", "HR"]:
                query = query.filter(
                    or_(
                        Document.is_public == True,
                        Document.uploaded_by == current_user.user_id,
                        Document.allowed_roles.contains([current_user.role.upper()])
                    )
                )

            total = query.count()
            documents = query.offset(skip).limit(limit).all()

            return DocumentListResponse(
                documents=[DocumentResponse.from_orm(doc) for doc in documents],
                total=total,
                skip=skip,
                limit=limit
            )

        except Exception as e:
            logger.error(f"Error getting documents: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Error retrieving documents"
            )

    async def get_document_by_id(
        self,
        db: Session,
        document_id: UUID,
        current_user: CurrentUser
    ) -> Optional[DocumentResponse]:
        """Get document by ID"""
        try:
            document = db.query(Document).filter(
                Document.id == document_id,
                Document.organization_id == current_user.organization_id,
                Document.status != DocumentStatus.DELETED
            ).first()

            if not document:
                return None

            # Check access permissions
            if not await self._check_document_access(document, current_user, "view"):
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="Access denied to this document"
                )

            # Log access
            await self._log_document_access(db, document_id, current_user, "view")

            return DocumentResponse.from_orm(document)

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error getting document {document_id}: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Error retrieving document"
            )

    async def upload_document(
        self,
        db: Session,
        file: UploadFile,
        document_data: DocumentUploadRequest,
        current_user: CurrentUser
    ) -> DocumentResponse:
        """Upload a new document"""
        try:
            # Validate file size
            if file.size and file.size > self.max_file_size:
                raise HTTPException(
                    status_code=status.HTTP_413_REQUEST_ENTITY_TOO_LARGE,
                    detail=f"File size exceeds maximum allowed size of {self.max_file_size} bytes"
                )

            # Generate unique filename
            file_extension = Path(file.filename).suffix
            unique_filename = f"{uuid4()}{file_extension}"
            
            # Create upload directory structure
            upload_dir = Path(self.upload_folder) / current_user.organization_id / "documents"
            upload_dir.mkdir(parents=True, exist_ok=True)
            
            file_path = upload_dir / unique_filename
            
            # Read file content and calculate hash
            content = await file.read()
            file_hash = hashlib.sha256(content).hexdigest()
            
            # Save file to disk
            with open(file_path, "wb") as f:
                f.write(content)

            # Create document record
            document = Document(
                name=document_data.name,
                description=document_data.description,
                document_type=document_data.document_type,
                status=DocumentStatus.ACTIVE,
                file_name=file.filename,
                file_path=str(file_path),
                file_size=len(content),
                mime_type=file.content_type or "application/octet-stream",
                file_hash=file_hash,
                version="1.0",
                is_latest_version=True,
                organization_id=current_user.organization_id,
                uploaded_by=current_user.user_id,
                tags=document_data.tags or [],
                is_public=document_data.is_public,
                allowed_roles=document_data.allowed_roles or [],
                allowed_departments=document_data.allowed_departments or [],
                valid_from=document_data.valid_from,
                valid_until=document_data.valid_until,
                created_by=current_user.user_id
            )

            db.add(document)
            db.commit()
            db.refresh(document)

            logger.info(f"Document uploaded: {document.id} by {current_user.user_id}")
            return DocumentResponse.from_orm(document)

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error uploading document: {e}")
            # Clean up file if it was created
            if 'file_path' in locals() and os.path.exists(file_path):
                os.remove(file_path)
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Error uploading document"
            )

    async def update_document(
        self,
        db: Session,
        document_id: UUID,
        document_data: DocumentUpdate,
        current_user: CurrentUser
    ) -> DocumentResponse:
        """Update document metadata"""
        try:
            document = db.query(Document).filter(
                Document.id == document_id,
                Document.organization_id == current_user.organization_id,
                Document.status != DocumentStatus.DELETED
            ).first()

            if not document:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Document not found"
                )

            # Check edit permissions
            if not await self._check_document_access(document, current_user, "edit"):
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="No permission to edit this document"
                )

            # Update fields
            update_data = document_data.model_dump(exclude_unset=True)
            for field, value in update_data.items():
                setattr(document, field, value)

            document.updated_by = current_user.user_id
            document.updated_at = datetime.utcnow()

            db.commit()
            db.refresh(document)

            logger.info(f"Document updated: {document_id} by {current_user.user_id}")
            return DocumentResponse.from_orm(document)

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error updating document {document_id}: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Error updating document"
            )

    async def delete_document(
        self,
        db: Session,
        document_id: UUID,
        current_user: CurrentUser
    ) -> bool:
        """Soft delete document"""
        try:
            document = db.query(Document).filter(
                Document.id == document_id,
                Document.organization_id == current_user.organization_id,
                Document.status != DocumentStatus.DELETED
            ).first()

            if not document:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Document not found"
                )

            # Check delete permissions (admin/HR or document owner)
            if (current_user.role.upper() not in ["ADMIN", "HR"] and 
                document.uploaded_by != current_user.user_id):
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="No permission to delete this document"
                )

            # Soft delete
            document.status = DocumentStatus.DELETED
            document.updated_by = current_user.user_id
            document.updated_at = datetime.utcnow()

            db.commit()

            logger.info(f"Document deleted: {document_id} by {current_user.user_id}")
            return True

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error deleting document {document_id}: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Error deleting document"
            )

    async def _check_document_access(
        self,
        document: Document,
        current_user: CurrentUser,
        access_type: str
    ) -> bool:
        """Check if user has access to document"""
        try:
            # Admin and HR have full access
            if current_user.role.upper() in ["ADMIN", "HR"]:
                return True

            # Document owner has full access
            if document.uploaded_by == current_user.user_id:
                return True

            # Public documents can be viewed
            if document.is_public and access_type == "view":
                return True

            # Check role-based access
            if document.allowed_roles and current_user.role.upper() in document.allowed_roles:
                return True

            # Check department-based access (if employee has department)
            # This would require getting employee details
            
            return False

        except Exception as e:
            logger.error(f"Error checking document access: {e}")
            return False

    async def _log_document_access(
        self,
        db: Session,
        document_id: UUID,
        current_user: CurrentUser,
        access_type: str,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None
    ):
        """Log document access"""
        try:
            access_log = DocumentAccess(
                document_id=document_id,
                user_id=current_user.user_id,
                access_type=access_type,
                ip_address=ip_address,
                user_agent=user_agent
            )
            
            db.add(access_log)
            db.commit()

        except Exception as e:
            logger.error(f"Error logging document access: {e}")


# Create service instance
document_service = DocumentService()
