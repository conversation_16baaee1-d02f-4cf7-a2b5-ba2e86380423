"""Create employee components table

Revision ID: 6803367aa4ba
Revises: af392e7132f1
Create Date: 2024-07-31 10:15:51.057350

"""
from typing import Sequence, Union

from alembic import op
from sqlalchemy import func, TIMESTAMP, Foreign<PERSON>ey
from sqlalchemy import <PERSON>umn, Integer, String


# revision identifiers, used by Alembic.
revision: str = '6803367aa4ba'
down_revision: Union[str, None] = 'af392e7132f1'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    op.create_table(
        'employee_components_pivot',
        Column('id', Integer, primary_key=True),
        <PERSON>umn('employee_id', Integer, ForeignKey('employees.id')),
        Column('salary_component_id', Integer, ForeignKey('salary_components.id'))
    )

def downgrade() -> None:
    op.drop_table("employee_components_pivot")
