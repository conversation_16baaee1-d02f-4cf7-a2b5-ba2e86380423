"""Add prorated_net to payroll_history table

Revision ID: 316245418208
Revises: 86532b95d501
Create Date: 2025-02-05 02:08:43.937929

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '316245418208'
down_revision: Union[str, None] = '86532b95d501'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    op.add_column("payroll_history", sa.Column("prorated_net", sa.Float, nullable=True))


def downgrade() -> None:
    op.drop_column("payroll_history","prorated_net")
