from datetime import datetime, timedelta
from typing import Optional, Union, Any
from jose import J<PERSON><PERSON>rror, jwt
from passlib.context import Crypt<PERSON>ontext
from fastapi import Depends, HTTPException, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from sqlalchemy.orm import Session
import httpx
import logging

from .config import settings
from .rbac import rbac_manager, Permissions
from ..db.session import get_db

logger = logging.getLogger(__name__)

# Password hashing
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

# JWT token scheme
security = HTTPBearer()


class SecurityManager:
    """Security manager for authentication and authorization"""

    @staticmethod
    def verify_password(plain_password: str, hashed_password: str) -> bool:
        """Verify a password against its hash"""
        return pwd_context.verify(plain_password, hashed_password)

    @staticmethod
    def get_password_hash(password: str) -> str:
        """Hash a password"""
        return pwd_context.hash(password)

    @staticmethod
    def create_access_token(data: dict, expires_delta: Optional[timedelta] = None) -> str:
        """Create JWT access token"""
        to_encode = data.copy()
        if expires_delta:
            expire = datetime.utcnow() + expires_delta
        else:
            expire = datetime.utcnow() + timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)

        to_encode.update({"exp": expire})
        encoded_jwt = jwt.encode(to_encode, settings.SECRET_KEY, algorithm=settings.ALGORITHM)
        return encoded_jwt

    @staticmethod
    def create_refresh_token(data: dict) -> str:
        """Create JWT refresh token"""
        to_encode = data.copy()
        expire = datetime.utcnow() + timedelta(days=settings.REFRESH_TOKEN_EXPIRE_DAYS)
        to_encode.update({"exp": expire})
        encoded_jwt = jwt.encode(to_encode, settings.SECRET_KEY, algorithm=settings.ALGORITHM)
        return encoded_jwt

    @staticmethod
    def verify_token(token: str) -> Optional[dict]:
        """Verify and decode JWT token"""
        try:
            payload = jwt.decode(token, settings.SECRET_KEY, algorithms=[settings.ALGORITHM])
            return payload
        except JWTError as e:
            logger.error(f"JWT verification failed: {e}")
            return None


class AuthService:
    """Authentication service to interact with auth microservice"""

    def __init__(self):
        self.auth_service_url = settings.AUTH_SERVICE_URL

    async def validate_token(self, user_id: str) -> Optional[dict]:
        """Validate token with auth service"""
        try:
            async with httpx.AsyncClient() as client:
                response = await client.post(f"{self.auth_service_url}/validate-token/{user_id}")
                if response.status_code == 200:
                    return response.json()
                return None
        except Exception as e:
            logger.error(f"Error validating token with auth service: {e}")
            return None

    async def get_user_info(self, user_id: str) -> Optional[dict]:
        """Get user information from auth service"""
        try:
            async with httpx.AsyncClient() as client:
                response = await client.get(f"{self.auth_service_url}/fetchUser/{user_id}")
                if response.status_code == 200:
                    return response.json()
                return None
        except Exception as e:
            logger.error(f"Error getting user info from auth service: {e}")
            return None

    async def get_user_role(self, user_id: str) -> Optional[str]:
        """Get user role from auth service"""
        try:
            async with httpx.AsyncClient() as client:
                response = await client.get(f"{self.auth_service_url}/validate-role/{user_id}")
                if response.status_code == 200:
                    data = response.json()
                    return data.get("role")
                return None
        except Exception as e:
            logger.error(f"Error getting user role from auth service: {e}")
            return None


# Global instances
security_manager = SecurityManager()
auth_service = AuthService()


def normalize_role_for_rbac(role: str) -> str:
    """Convert database role format to RBAC format"""
    if not role:
        return "EMPLOYEE"

    # Convert lowercase database roles to uppercase RBAC roles
    role_mapping = {
        "admin": "ADMIN",
        "hr": "HR",
        "manager": "MANAGER",
        "employee": "EMPLOYEE",
        "supervisor": "MANAGER"  # Map supervisor to manager for RBAC
    }

    # If role is already uppercase, return as-is
    if role.upper() == role:
        return role

    # Convert lowercase to uppercase using mapping
    return role_mapping.get(role.lower(), "EMPLOYEE")


class CurrentUser:
    """Current user information"""
    def __init__(self, user_id: str, email: str, role: str, organization_id: Optional[str] = None):
        self.user_id = user_id
        self.email = email
        self.role = normalize_role_for_rbac(role)  # Normalize role for RBAC
        self.organization_id = organization_id


async def get_current_user(
    credentials: HTTPAuthorizationCredentials = Depends(security)
) -> CurrentUser:
    """Get current authenticated user"""
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )

    try:
        # Verify JWT token
        payload = security_manager.verify_token(credentials.credentials)
        if payload is None:
            raise credentials_exception

        user_id: str = payload.get("sub")
        if user_id is None:
            raise credentials_exception

        # For testing purposes, skip external auth service validation
        # In production, you would validate with the auth service

        # Get user info from payload (for testing)
        email = payload.get("email", f"{user_id}@company.com")
        role = payload.get("role", "employee")  # Default to lowercase database format
        organization_id = payload.get("organization_id")

        return CurrentUser(
            user_id=user_id,
            email=email,
            role=role,
            organization_id=organization_id
        )

    except Exception as e:
        logger.error(f"Authentication error: {e}")
        raise credentials_exception


def require_permission(permission: str):
    """Dependency to require specific permission"""
    async def permission_checker(current_user: CurrentUser = Depends(get_current_user)) -> CurrentUser:
        # Extract resource and action from permission
        if ":" in permission:
            resource, action = permission.split(":", 1)
        else:
            resource = permission
            action = "read"

        # Check permission with RBAC
        has_permission = rbac_manager.check_permission(current_user.role, resource, action)

        if not has_permission:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"Insufficient permissions. Required: {permission}"
            )

        return current_user

    return permission_checker


def require_role(required_role: str):
    """Dependency to require specific role"""
    async def role_checker(current_user: CurrentUser = Depends(get_current_user)) -> CurrentUser:
        if current_user.role != required_role:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"Insufficient role. Required: {required_role}"
            )
        return current_user

    return role_checker


def require_any_role(required_roles: list):
    """Dependency to require any of the specified roles"""
    async def role_checker(current_user: CurrentUser = Depends(get_current_user)) -> CurrentUser:
        if current_user.role not in required_roles:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"Insufficient role. Required one of: {required_roles}"
            )
        return current_user

    return role_checker
