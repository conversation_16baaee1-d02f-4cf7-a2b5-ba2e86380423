from sqlalchemy import Column, String, Time, Date, ForeignKey, Boolean, Text, Integer, Enum
from sqlalchemy.dialects.postgresql import UUID, JSONB
from sqlalchemy.orm import relationship
from enum import Enum as PyEnum
from datetime import time, date
from typing import Optional

from ..base import BaseModel, AuditMixin


class ShiftType(PyEnum):
    REGULAR = "regular"
    NIGHT = "night"
    ROTATING = "rotating"
    FLEXIBLE = "flexible"
    SPLIT = "split"


class WeekDay(PyEnum):
    MONDAY = "monday"
    TUESDAY = "tuesday"
    WEDNESDAY = "wednesday"
    THURSDAY = "thursday"
    FRIDAY = "friday"
    SATURDAY = "saturday"
    SUNDAY = "sunday"


class Shift(BaseModel, AuditMixin):
    """Shift definition model"""
    __tablename__ = "shifts"

    name = Column(String(200), nullable=False)
    code = Column(String(20), unique=True, nullable=False, index=True)
    description = Column(Text, nullable=True)
    organization_id = Column(UUID(as_uuid=True), nullable=False, index=True)

    # Shift timing
    start_time = Column(Time, nullable=False)
    end_time = Column(Time, nullable=False)
    shift_type = Column(Enum(ShiftType), nullable=False, default=ShiftType.REGULAR)

    # Duration and breaks
    total_hours = Column(Integer, nullable=False)  # in minutes
    break_duration = Column(Integer, nullable=False, default=60)  # in minutes

    # Grace periods
    late_grace_period = Column(Integer, nullable=False, default=15)  # minutes
    early_departure_grace_period = Column(Integer, nullable=False, default=15)  # minutes

    # Working days
    working_days = Column(JSONB, nullable=False)  # Array of weekdays

    # Timezone
    timezone = Column(String(50), nullable=False, default="UTC")

    # Overtime settings
    overtime_threshold = Column(Integer, nullable=True)  # minutes beyond shift end

    # Flexibility settings (for flexible shifts)
    flexible_start_window = Column(Integer, nullable=True)  # minutes before/after start time
    flexible_end_window = Column(Integer, nullable=True)  # minutes before/after end time

    # Split shift settings (for split shifts)
    split_break_start = Column(Time, nullable=True)
    split_break_end = Column(Time, nullable=True)

    # Status
    is_default = Column(Boolean, default=False)

    # Relationships
    shift_assignments = relationship("ShiftAssignment", back_populates="shift")
    attendance_records = relationship("AttendanceRecord", back_populates="shift")


class ShiftAssignment(BaseModel, AuditMixin):
    """Employee shift assignment"""
    __tablename__ = "shift_assignments"

    employee_id = Column(UUID(as_uuid=True), ForeignKey("employees.id"), nullable=False, index=True)
    shift_id = Column(UUID(as_uuid=True), ForeignKey("shifts.id"), nullable=False, index=True)

    # Assignment period
    start_date = Column(Date, nullable=False)
    end_date = Column(Date, nullable=True)  # NULL means ongoing

    # Override settings for this assignment
    custom_start_time = Column(Time, nullable=True)
    custom_end_time = Column(Time, nullable=True)
    custom_working_days = Column(JSONB, nullable=True)  # Override shift working days

    # Assignment metadata
    assigned_by = Column(UUID(as_uuid=True), ForeignKey("employees.id"), nullable=True)
    assignment_reason = Column(Text, nullable=True)

    # Relationships
    employee = relationship("Employee", foreign_keys=[employee_id])
    shift = relationship("Shift", back_populates="shift_assignments")
    assigner = relationship("Employee", foreign_keys=[assigned_by])


class ShiftSwapRequest(BaseModel, AuditMixin):
    """Shift swap request between employees"""
    __tablename__ = "shift_swap_requests"

    requester_id = Column(UUID(as_uuid=True), ForeignKey("employees.id"), nullable=False)
    target_employee_id = Column(UUID(as_uuid=True), ForeignKey("employees.id"), nullable=False)

    # Original assignments
    requester_shift_assignment_id = Column(UUID(as_uuid=True), ForeignKey("shift_assignments.id"), nullable=False)
    target_shift_assignment_id = Column(UUID(as_uuid=True), ForeignKey("shift_assignments.id"), nullable=False)

    # Swap details
    swap_date = Column(Date, nullable=False)
    reason = Column(Text, nullable=True)

    # Approval workflow
    target_approved = Column(Boolean, nullable=True)  # NULL = pending, True = approved, False = rejected
    target_approved_at = Column(Date, nullable=True)

    manager_approved = Column(Boolean, nullable=True)
    manager_approved_by = Column(UUID(as_uuid=True), ForeignKey("employees.id"), nullable=True)
    manager_approved_at = Column(Date, nullable=True)

    # Final status
    is_completed = Column(Boolean, default=False)
    completed_at = Column(Date, nullable=True)

    # Relationships
    requester = relationship("Employee", foreign_keys=[requester_id])
    target_employee = relationship("Employee", foreign_keys=[target_employee_id])
    requester_assignment = relationship("ShiftAssignment", foreign_keys=[requester_shift_assignment_id])
    target_assignment = relationship("ShiftAssignment", foreign_keys=[target_shift_assignment_id])
    approving_manager = relationship("Employee", foreign_keys=[manager_approved_by])


class ShiftPattern(BaseModel):
    """Rotating shift pattern template"""
    __tablename__ = "shift_patterns"

    name = Column(String(200), nullable=False)
    description = Column(Text, nullable=True)
    organization_id = Column(UUID(as_uuid=True), nullable=False, index=True)

    # Pattern configuration
    pattern_length = Column(Integer, nullable=False)  # Number of days in pattern
    pattern_data = Column(JSONB, nullable=False)  # Array of shift IDs for each day

    # Relationships
    pattern_assignments = relationship("ShiftPatternAssignment", back_populates="pattern")


class ShiftPatternAssignment(BaseModel):
    """Assignment of rotating shift pattern to employee"""
    __tablename__ = "shift_pattern_assignments"

    employee_id = Column(UUID(as_uuid=True), ForeignKey("employees.id"), nullable=False)
    pattern_id = Column(UUID(as_uuid=True), ForeignKey("shift_patterns.id"), nullable=False)

    start_date = Column(Date, nullable=False)
    end_date = Column(Date, nullable=True)
    current_position = Column(Integer, nullable=False, default=0)  # Current position in pattern

    # Relationships
    employee = relationship("Employee", foreign_keys=[employee_id])
    pattern = relationship("ShiftPattern", back_populates="pattern_assignments")
