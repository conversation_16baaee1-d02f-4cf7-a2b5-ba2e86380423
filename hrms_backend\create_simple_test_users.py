#!/usr/bin/env python3
"""
Create simple test users based on actual table structure
"""

import sys
import os
from uuid import uuid4
from datetime import datetime, date

# Add the app directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'app'))

from sqlalchemy.orm import Session
from sqlalchemy import text
from app.db.session import SessionLocal
from app.core.security import SecurityManager

def create_simple_test_users():
    """Create test users for all roles"""
    db = SessionLocal()
    
    try:
        print("Creating simple test users...")
        
        # Test users data
        test_users = [
            {
                'employee_id': 'ADMIN001',
                'first_name': 'Admin',
                'last_name': 'User',
                'email': '<EMAIL>',
                'role': 'ADMIN'
            },
            {
                'employee_id': 'HR001',
                'first_name': 'HR',
                'last_name': 'Manager',
                'email': '<EMAIL>',
                'role': 'HR'
            },
            {
                'employee_id': 'MGR001',
                'first_name': 'Manager',
                'last_name': 'User',
                'email': '<EMAIL>',
                'role': 'MANAGER'
            },
            {
                'employee_id': 'EMP001',
                'first_name': 'Employee',
                'last_name': 'User',
                'email': '<EMAIL>',
                'role': 'EMPLOYEE'
            }
        ]
        
        for user_data in test_users:
            # Check if user already exists
            result = db.execute(text("SELECT email FROM users WHERE email = :email LIMIT 1"), 
                              {'email': user_data['email']})
            existing = result.fetchone()

            if existing:
                print(f"✅ User {user_data['email']} already exists")
                continue
            
            # Create user first
            user_id = uuid4()
            password_hash = SecurityManager.get_password_hash("password123")
            
            user_insert_sql = text("""
                INSERT INTO users (
                    id, email, password, role, is_active, is_verified, created_at, updated_at
                ) VALUES (
                    :id, :email, :password, CAST(:role AS role), :is_active, :is_verified, :created_at, :updated_at
                )
            """)
            
            db.execute(user_insert_sql, {
                'id': user_id,
                'email': user_data['email'],
                'password': password_hash,
                'role': user_data['role'],
                'is_active': True,
                'is_verified': True,
                'created_at': datetime.utcnow(),
                'updated_at': datetime.utcnow()
            })
            
            # Create employee record
            employee_id = uuid4()
            
            employee_insert_sql = text("""
                INSERT INTO employees (
                    id, user_id, employee_id, first_name, last_name, email,
                    hire_date, is_active, created_at, updated_at
                ) VALUES (
                    :id, :user_id, :employee_id, :first_name, :last_name, :email,
                    :hire_date, :is_active, :created_at, :updated_at
                )
            """)
            
            db.execute(employee_insert_sql, {
                'id': employee_id,
                'user_id': user_id,
                'employee_id': user_data['employee_id'],
                'first_name': user_data['first_name'],
                'last_name': user_data['last_name'],
                'email': user_data['email'],
                'hire_date': datetime.utcnow(),
                'is_active': True,
                'created_at': datetime.utcnow(),
                'updated_at': datetime.utcnow()
            })
            
            print(f"✅ Created user: {user_data['first_name']} {user_data['last_name']} ({user_data['email']}) - {user_data['role']}")
        
        db.commit()
        print("\n✅ All test users created successfully!")
        print("\nTest credentials:")
        print("- Admin: <EMAIL> / password123")
        print("- HR: <EMAIL> / password123") 
        print("- Manager: <EMAIL> / password123")
        print("- Employee: <EMAIL> / password123")
        
    except Exception as e:
        print(f"❌ Error creating test users: {e}")
        db.rollback()
        raise
    finally:
        db.close()

if __name__ == "__main__":
    create_simple_test_users()
