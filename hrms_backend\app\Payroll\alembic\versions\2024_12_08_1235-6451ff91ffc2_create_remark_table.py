"""create remark table

Revision ID: 6451ff91ffc2
Revises: d3de5a711d03
Create Date: 2024-12-08 12:35:28.036790

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy import Column, Integer, String, Float
from sqlalchemy import func, DateTime, ForeignKey


# revision identifiers, used by Alembic.
revision: str = '6451ff91ffc2'
down_revision: Union[str, None] = 'd3de5a711d03'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    op.create_table(
    'remark',
     
     Column('id', Integer, primary_key=True, autoincrement=True),
     <PERSON>umn('remark', String(length=200), nullable=True),
     <PERSON><PERSON><PERSON>('employee_id', Integer, ForeignKey('employees.id'), nullable=False),
     <PERSON><PERSON><PERSON>('payroll_history_id', <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>('payroll_history.id'), nullable=False),
     <PERSON>umn('created_at', DateTime, nullable=False, server_default=sa.func.now()),
     <PERSON>umn('updated_at', DateTime, nullable=True, onupdate=sa.func.now()),
     Column('user_id', Integer, ForeignKey('users.id'), nullable=True),

    )

    


def downgrade() -> None:
    op.drop_table('remark')




    