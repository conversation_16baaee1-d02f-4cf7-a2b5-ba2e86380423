from flask import request, url_for, jsonify
from flask.views import <PERSON><PERSON><PERSON><PERSON>
from flask_smorest import Blueprint, abort
from flask_jwt_extended import (
    create_access_token, 
    get_jwt, 
    jwt_required,
    create_refresh_token,
    get_jwt_identity)
from passlib.hash import pbkdf2_sha256
import os
from core.utils.responseBuilder import ResponseBuilder
from core.services.email_service import EmailService
from core.middleware import roles_required
from core.databases.database import db
from core.models import UserModel, PasswordResetToken, EmployeeModel
from schemas import UserChangePasswordSchema, UserSchema, UserLoginSchema, ForgetPasswordSchema, ResetPasswordSchema, UserUpdatePasswordSchema, UserUpdateSchema
from blacklist import BLACKLIST
from sqlalchemy.orm.exc import NoResultFound
from sqlalchemy.exc import IntegrityError
import core.utils.response_message as RESPONSEMESSAGE
import helper
from core.repositories.user import UserRepository
from .otps import OTPGenerator, SendMail
from core.models.otp import OTPModel
from schemas import OTPVerifySchema, ResendOTPSchema
import pytz
from flask import jsonify
from datetime import datetime, timedelta, timezone
from werkzeug.security import check_password_hash, generate_password_hash
from core.services.email_service import EmailService
from core.utils.activity_logger import ActivityLogger
from core.services.activity_employee import EmployeeActivityService
from passlib.exc import InvalidHashError

blueprint = Blueprint("Users", __name__, description="Operations for users")
    
@blueprint.route("/register")
class UserRegister(MethodView):
    @blueprint.arguments(UserSchema)
    def post(self, user_data):
        # Check if the user exists
        existing_user = UserModel.query.filter(UserModel.email==user_data["email"]).first()

        if existing_user:
            abort(409, message="Email already exists")
            
        # Generate OTP
        otp = OTPGenerator.generate_otp()

        # Create a new user
        user = UserModel(
            email=user_data["email"],
            password=pbkdf2_sha256.hash(user_data["password"]),
            first_name=user_data["first_name"],
            last_name=user_data["last_name"],
            org_name=user_data["org_name"]
        )
        try:
            # Start a transaction
            db.session.add(user)
            db.session.commit()

            # Store the OTP in the database associated with the user
            otp_entry = OTPModel(
                email=user_data["email"],
                user_id=user.id,
                otp=otp
            )
            db.session.add(otp_entry)
            db.session.commit()
            
        # Send OTP email
            email_sent = SendMail.send_otp(user.email, otp)
            # email_sent = EmailService.send_email("OTP code", user.email, f"Your otp verification code is {otp}") 
            if not email_sent:
                abort(500, message="Failed to send OTP email")
            
        # except IntegrityError as e:
        #     print(e)
        #     db.session.rollback()
        #     abort(409, message="Failed to create user: Email already exists")
        except Exception as e:
            print(e)
            db.session.rollback()
            abort(500, message=f"Failed to create user: {str(e)}")

        
        # Return status code 201 for successful user creation
        return jsonify({"message": "User created successfully"}), 201

        # userSchema = UserSchema().dump(user)
        # # print(userSchema)
        # identity = userSchema["id"]
        # access_token = create_access_token(identity=identity, fresh=True)
        # refresh_token = create_refresh_token(identity)
        # return {"access_token" : access_token, 
        #         "refresh_token": refresh_token
        #     }, 201

@roles_required(["admin", "employee"], fresh=True)
@blueprint.route("/user")
class User(MethodView):
    #development purpose
    @blueprint.response(200, UserSchema(many=True))
    def get(self):
        return UserModel.query.all()
    
@blueprint.route("/login")
class UserLogin(MethodView):
    @blueprint.arguments(UserLoginSchema)
    def post(self, user_data):
        user = UserModel.query.filter(UserModel.email == user_data["email"]).first()
        
        if user and pbkdf2_sha256.verify(user_data["password"], user.password):
            # print(user_data)
            if user.email_verified:
                # access_token = create_access_token(identity=user.id, fresh=True)
                access_token = create_access_token(identity=str(user.id), additional_claims={"is_admin": True, "is_employee": False})
                refresh_token = create_refresh_token(identity=str(user.id),  additional_claims={"is_admin": True, "is_employee": False})
                
                # ActivityLogger.log_admin_activity(f"User {user.full_name} logged in")
                full_name = f"{user.first_name} {user.last_name}"
                ActivityLogger.log_admin_activity(f"{full_name}, You just logged in", user_id=user.id)
                
                return {"access_token" : access_token, "refresh_token": refresh_token}, 200
            else:
                return {"message":"Email is not verified. please verify your email before loggin in."}, 403
        
        abort(401, message=RESPONSEMESSAGE.INVALID_CREDENTIALS)

@blueprint.route("/employee-login")
class UserLogin(MethodView):
    @blueprint.arguments(UserLoginSchema)
    def post(self, user_data):
        employee = EmployeeModel.query.filter(EmployeeModel.email == user_data["email"], EmployeeModel.status == 'Active').first()
        
        try:
            if employee and employee.password_hash and pbkdf2_sha256.verify(user_data["password"], employee.password_hash):
                access_token = create_access_token(identity=employee.id, additional_claims={"is_admin": False, "is_employee": True})
                refresh_token = create_refresh_token(employee.id, additional_claims={"is_admin": False, "is_employee": True})
                
                message =f"{employee.first_name} {employee.last_name}, You just logged in"
                EmployeeActivityService().logActivity(employee.id, message)
                    
                return {
                    "access_token" : access_token, 
                    "refresh_token": refresh_token, 
                    "id": employee.id, 
                    "organisation": employee.organisation.organisation_name if employee.organisation else "",
                    "email": employee.email,
                    "role": employee.role,
                    "name": f"{employee.first_name.strip()} {employee.last_name.strip()}" if employee.first_name and employee.last_name else ""
                }, 200
        except Exception as e:
            print("Invalid hash format:", str(e))
        
        abort(401, message=RESPONSEMESSAGE.INVALID_CREDENTIALS)

@blueprint.route("/forget")
class ForgetPassword(MethodView):
    @blueprint.arguments(ForgetPasswordSchema)
    def post(self, user_data):
        user = UserModel.query.filter(UserModel.email == user_data["email"]).first()
        
        if user:
            token = helper.generate_reset_token()
            
            expiration_time = datetime.now(timezone.utc) + timedelta(hours=1)
            reset_token = PasswordResetToken(user_id=user.id, token=token, expires_at=expiration_time)
            db.session.add(reset_token)
            db.session.commit()
            
            base_url = helper.get_frontend_host()
            reset_link = f'{base_url}reset_password?token={token}'
            recipient = user.email
            subject = f"Your Password Reset Request"
            message = f"To reset your password, click the following link: {reset_link}"
            EmailService.send_email(subject, recipient, text_body=message, html_body=None, attachments=None)

            data_object = {
                "message": RESPONSEMESSAGE.PASSWORD_RESET_LINK_SENT_SUCCESSFULLY, 
                "url": reset_link
            }
            return ResponseBuilder(data=data_object, status_code=200).build()

        else:
            return ResponseBuilder(data={"message":RESPONSEMESSAGE.EMAIL_DO_NOT_EXIST}, status_code=401).build()
            # abort(401, message=RESPONSEMESSAGE.EMAIL_DO_NOT_EXIST)
        
@blueprint.route("/reset-password")
class ResetPassword(MethodView):
    @blueprint.arguments(ResetPasswordSchema)
    def post(self, user_data):
        token = user_data["token"]
        new_password = user_data["password"]
        confirm_password = user_data["confirm_password"]

        # Retrieve the user associated with the token
        try:
            token_record = PasswordResetToken.query.filter_by(token=token).one()
            user = UserModel.query.get(token_record.user_id)
        except NoResultFound:
            abort(400, message=RESPONSEMESSAGE.INVALID_OR_EXPIRED_TOKEN)

        # Check if passwords match
        if new_password != confirm_password:
            abort(400, message=RESPONSEMESSAGE.PASSWORD_MISMATCH)
        
        # Update password
        user.password = pbkdf2_sha256.hash(new_password)

        # Delete the password reset token
        db.session.delete(token_record)
        db.session.commit()
        return {"message": RESPONSEMESSAGE.PASSWORD_RESET_SUCCESSFULLY}

@blueprint.route("/logout")
class UserLogout(MethodView):
    @roles_required(["admin", "employee"], fresh=True)
    def delete(self):
        jti = get_jwt()["jti"]
        BLACKLIST.add(jti)
        return {"message": RESPONSEMESSAGE.LOGGED_OUT}, 200

@blueprint.route("/refresh")
class TokenRefresh(MethodView):
    @roles_required(["admin"])
    def post(self):
        current_user = get_jwt_identity()
        new_token = create_access_token(identity=current_user, fresh=False)

        jti = get_jwt()["jti"]
        BLACKLIST.add(jti)
        return {"access_token" : new_token}, 200 

@blueprint.route("/user-profile")
class UserProfile(MethodView):
    @roles_required(["admin", "employee"])
    @blueprint.response(200, UserSchema)
    def get(self):
        user = UserRepository().authUser()
        user_profile = UserSchema().dump(user)
        return ResponseBuilder(data=user_profile, status_code=200).build()
    
    @roles_required(["admin"])
    @blueprint.arguments(UserUpdateSchema)
    @blueprint.response(200, UserSchema)
    def put(self, data):
        user = UserRepository().updateLoggedInUser(data)
        ActivityLogger.log_admin_activity("You updated your profile")
        return user
        
@blueprint.route("/verify-otp", methods=["POST"])
class OTPVerify(MethodView):
    @blueprint.arguments(OTPVerifySchema)
    def post(self, otp_data):
        email = otp_data["email"]
        otp = otp_data["otp"]

        # Query the OTPModel to check if the OTP is valid
        otp_entry = OTPModel.query.filter_by(email=email, otp=otp).first()

        if not otp_entry:
            return ResponseBuilder(data={"message": "Invalid OTP or Email"}, status_code=400).build()          
            
        #  # Check if the OTP has expired (assuming 10 minutes expiration)
        # expiration_time = otp_entry.created_at + timedelta(minutes=10)
        # if datetime.now(pytz.timezone('Africa/Lagos')) > expiration_time:
        #     abort(400, message="OTP has expired")
        
        # Make otp_entry.created_at timezone-aware if it's not
        if otp_entry.created_at.tzinfo is None:
            otp_entry.created_at = pytz.timezone('Africa/Lagos').localize(otp_entry.created_at)

        # Check if the OTP has expired (assuming 10 minutes expiration)
        expiration_time = otp_entry.created_at + timedelta(minutes=70)
        current_time = datetime.now(pytz.timezone('Africa/Lagos'))
        if current_time > expiration_time:
            return ResponseBuilder(data={"message": "OTP has expired"}, status_code=400).build()

        # Update user email_verified field
        user = UserModel.query.filter_by(email=email).first()
        if user:
            user.email_verified = True
            db.session.commit()
            
            # Generate access and refresh tokens
            # access_token = create_access_token(identity=user.id, fresh=True)
            # refresh_token = create_refresh_token(user.id)

            access_token = create_access_token(identity=user.id, additional_claims={"is_admin": True, "is_employee": False})
            refresh_token = create_refresh_token(user.id,  additional_claims={"is_admin": True, "is_employee": False})
                
            
            return ResponseBuilder(data={"message": "Email verified successfully", "access_token": access_token, "refresh_token": refresh_token}).build()
        
        return ResponseBuilder(data={"message": "User not found"}, status_code=404).build()


@blueprint.route("/resend-otp", methods=["POST"])
class ResendOTP(MethodView):
    @blueprint.arguments(ResendOTPSchema)
    def post(self, otp_data):
        email = otp_data["email"]

        # Verify if the user exists
        user = UserModel.query.filter_by(email=email).first()
        if not user:
            abort(404, message="User with this email does not exist")

        # Generate a new OTP
        new_otp = OTPGenerator.generate_otp()
        
        # Update the OTP entry in the database
        otp_entry = OTPModel.query.filter_by(email=email).first()
        if otp_entry:
            otp_entry.otp = new_otp
            otp_entry.created_at = datetime.now(pytz.timezone('Africa/Lagos'))
        else:
            # If no previous OTP, create a new entry (fallback)
            otp_entry = OTPModel(
                email=email,
                user_id=user.id,
                otp=new_otp,
                created_at=datetime.now(pytz.timezone('Africa/Lagos'))
            )
            db.session.add(otp_entry)

        try:
            db.session.commit()
        except Exception as e:
            print(f"Failed to update OTP: {str(e)}")
            db.session.rollback()
            abort(500, message="Failed to resend OTP")

        # Send the new OTP via email
        email_sent = SendMail.send_otp(email, new_otp)
        if not email_sent:
            abort(500, message="Failed to send OTP email")

        return {"message": "OTP has been resent successfully"}, 200


@blueprint.route("/change_user_password")
class UserChangePassword(MethodView):
    @blueprint.arguments(UserChangePasswordSchema)
    @blueprint.response(200)
    def patch(self, data):
        # Fetch the user by email
        user = UserModel.query.filter_by(email=data["email"]).first()

        # Check if the user exists
        if user:
            # Update the user's password
            user.password = pbkdf2_sha256.hash(data["password"])  # Make sure to hash the password
            db.session.commit()

            # Send email notification
            subject = "Password Change Confirmation"
            recipient = user.email
            text_body = f"Hello {user.email},\n\nYour password has been successfully changed. If this was not you, please contact support immediately."
            html_body = f"<p>Hello {user.email},</p><p>Your password has been successfully changed. If this was not you, please contact support immediately.</p>"

            EmailService.send_email(subject, recipient, text_body, html_body)

            return {"message": "Password updated successfully, confirmation email sent"}, 200
        else:
            return {"message": "User not found"}, 404


@blueprint.route("/update_user_password")
class UserChangePassword(MethodView):
    @blueprint.arguments(UserUpdatePasswordSchema)
    @blueprint.response(200)
    def patch(self, data):
        # Fetch the user by email
        user = UserModel.query.filter_by(email=data["email"]).first()
        
        # Check if the user exists
        if user:
            # Verify that the old password (current password) is correct
            # if check_password_hash(user.password, data["old_password"]):
            if pbkdf2_sha256.verify(data["old_password"], user.password):
                # Hash the new password and update
                user.password = pbkdf2_sha256.hash(data["new_password"])
                
                db.session.commit()
                return {"message": "Password updated successfully"}, 200
            else:
                return {"message": "Existing password not correct"}, 400
        else:
            return {"message": "User not found"}, 404
