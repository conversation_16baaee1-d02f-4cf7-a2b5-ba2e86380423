"""Me<PERSON> heads

Revision ID: b8ae0542f046
Revises: ba4198c6bdd1, e01b701f0222
Create Date: 2025-04-08 10:51:31.580941

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'b8ae0542f046'
down_revision: Union[str, None] = ('ba4198c6bdd1', 'e01b701f0222')
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    pass


def downgrade() -> None:
    pass
