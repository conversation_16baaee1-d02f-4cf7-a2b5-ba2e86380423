"""add reference_code column to payroll history

Revision ID: 3d99497126f7
Revises: 975407f781aa
Create Date: 2025-03-26 17:11:39.065300

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy import Column, Integer, String, Float



# revision identifiers, used by Alembic.
revision: str = '3d99497126f7'
down_revision: Union[str, None] = '975407f781aa'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None



def upgrade() -> None:
    op.add_column("payroll_history", sa.Column('reference_code', sa.String(length=255), nullable=True))

def downgrade() -> None:
    op.drop_column("payroll_history", "reference_code")

