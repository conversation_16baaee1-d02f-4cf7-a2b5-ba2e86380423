from datetime import datetime
import pytz

nigeria_tz = pytz.timezone("Africa/Lagos")

def get_nigeria_time():
    return datetime.now(nigeria_tz)
# print(get_nigeria_time())  


def get_nigeria_time_to_string():
    return datetime.now(nigeria_tz).strftime("%Y-%m-%d %H:%M:%S")

def safe_float(value, default=0.0):
    try:
        return float(value) if value not in [None, ""] else default
    except (ValueError, TypeError):
        return default