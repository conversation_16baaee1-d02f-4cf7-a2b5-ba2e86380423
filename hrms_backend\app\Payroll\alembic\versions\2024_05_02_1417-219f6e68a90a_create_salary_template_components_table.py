"""create salary_template_components table

Revision ID: 219f6e68a90a
Revises: 5f58cde4eca9
Create Date: 2024-05-02 14:17:23.450300

"""
from typing import Sequence, Union

from alembic import op
from sqlalchemy import func, TIMESTAMP, Foreign<PERSON><PERSON>
from sqlalchemy import Column, Integer, String

# revision identifiers, used by Alembic.
revision: str = '219f6e68a90a'
down_revision: Union[str, None] = '5f58cde4eca9'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None

def upgrade() -> None:
    op.create_table(
        'salary_template_components_pivot',
        <PERSON>umn('id', Integer, primary_key=True),
        Column('salary_template_id', Integer, ForeignKey('salary_templates.id')),
        Column('salary_component_id', Integer, ForeignKey('salary_components.id'))
    )

def downgrade() -> None:
    op.drop_table("salary_template_components_pivot")
