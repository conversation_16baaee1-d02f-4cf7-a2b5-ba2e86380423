from sqlalchemy.orm import Session, joinedload
from sqlalchemy import and_, or_, func
from typing import Optional, List
from uuid import UUID
from fastapi import HTTPException, status
import logging
import hashlib
import time

from ...db.models.employee import Employee, Department, Designation
from ...schemas.employee import (
    EmployeeCreate, EmployeeUpdate, EmployeeResponse, EmployeeListResponse,
    DepartmentCreate, DepartmentUpdate, DepartmentResponse,
    DesignationCreate, DesignationUpdate, DesignationResponse
)
from ...core.security import CurrentUser
from ...core.cache import cache, CacheKeys, PerformanceMonitor

logger = logging.getLogger(__name__)

class EmployeeService:
    """Employee service for business logic"""

    async def get_employees(
        self,
        db: Session,
        skip: int = 0,
        limit: int = 20,
        search: Optional[str] = None,
        department_id: Optional[UUID] = None,
        status: Optional[str] = None,
        current_user: CurrentUser = None
    ) -> EmployeeListResponse:
        """Get employees with filtering and pagination"""
        start_time = time.time()

        try:
            # Generate cache key based on filters
            filters_hash = hashlib.md5(
                f"{search}:{department_id}:{status}".encode()
            ).hexdigest()[:8]

            cache_key = CacheKeys.EMPLOYEE_LIST.format(
                org_id=current_user.organization_id or "none",
                skip=skip,
                limit=limit,
                filters_hash=filters_hash
            )

            # Try to get from cache first
            cached_result = cache.get(cache_key)
            if cached_result:
                PerformanceMonitor.log_cache_performance("GET", cache_key, hit=True)
                return EmployeeListResponse(**cached_result)

            PerformanceMonitor.log_cache_performance("GET", cache_key, hit=False)

            # Use eager loading to reduce N+1 queries
            query = db.query(Employee).options(
                joinedload(Employee.department),
                joinedload(Employee.designation)
            ).filter(Employee.is_active == True)

            # Filter by organization if available (skip if column doesn't exist)
            try:
                if current_user.organization_id and hasattr(Employee, 'organization_id'):
                    query = query.filter(Employee.organization_id == current_user.organization_id)
            except Exception as e:
                logger.warning(f"Organization filter skipped: {e}")

            # Apply filters
            if search:
                search_filter = or_(
                    Employee.first_name.ilike(f"%{search}%"),
                    Employee.last_name.ilike(f"%{search}%"),
                    Employee.email.ilike(f"%{search}%"),
                    Employee.employee_id.ilike(f"%{search}%")
                )
                query = query.filter(search_filter)

            if department_id:
                query = query.filter(Employee.department_id == department_id)

            if status:
                query = query.filter(Employee.status == status)

            # Get total count
            total = query.count()

            # Apply pagination
            employees = query.offset(skip).limit(limit).all()

            # Convert to response format
            employee_responses = []
            for emp in employees:
                emp_response = EmployeeResponse.from_orm(emp)
                emp_response.full_name = emp.full_name
                emp_response.display_name = emp.display_name
                employee_responses.append(emp_response)

            result = EmployeeListResponse(
                employees=employee_responses,
                total=total,
                skip=skip,
                limit=limit
            )

            # Cache the result for 5 minutes
            cache.set(cache_key, result.dict(), ttl=300)

            # Log performance
            execution_time = time.time() - start_time
            PerformanceMonitor.log_query_time("get_employees", execution_time)

            return result

        except Exception as e:
            logger.error(f"Error getting employees: {e}")
            raise HTTPException(
                status_code=500,
                detail="Error retrieving employees"
            )

    async def get_employee_by_id(
        self,
        db: Session,
        employee_id: UUID,
        current_user: CurrentUser
    ) -> Optional[EmployeeResponse]:
        """Get employee by ID"""
        try:
            employee = db.query(Employee).filter(
                Employee.id == employee_id,
                Employee.organization_id == current_user.organization_id,
                Employee.is_active == True
            ).first()

            if not employee:
                return None

            emp_response = EmployeeResponse.from_orm(employee)
            emp_response.full_name = employee.full_name
            emp_response.display_name = employee.display_name
            return emp_response

        except Exception as e:
            logger.error(f"Error getting employee {employee_id}: {e}")
            raise HTTPException(
                status_code=500,
                detail="Error retrieving employee"
            )

    async def create_employee(
        self,
        db: Session,
        employee_data: EmployeeCreate,
        current_user: CurrentUser
    ) -> EmployeeResponse:
        """Create new employee"""
        try:
            # Check if employee_id already exists
            existing = db.query(Employee).filter(
                Employee.employee_id == employee_data.employee_id,
                Employee.organization_id == current_user.organization_id
            ).first()

            if existing:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Employee ID already exists"
                )

            # Check if email already exists
            existing_email = db.query(Employee).filter(
                Employee.email == employee_data.email,
                Employee.organization_id == current_user.organization_id
            ).first()

            if existing_email:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Email already exists"
                )

            # Create employee
            employee = Employee(
                **employee_data.model_dump(exclude={'skills'}),
                organization_id=current_user.organization_id,
                skills=employee_data.skills or []
            )

            db.add(employee)
            db.commit()
            db.refresh(employee)

            emp_response = EmployeeResponse.from_orm(employee)
            emp_response.full_name = employee.full_name
            emp_response.display_name = employee.display_name

            logger.info(f"Employee {employee.employee_id} created by {current_user.user_id}")
            return emp_response

        except HTTPException:
            raise
        except Exception as e:
            db.rollback()
            logger.error(f"Error creating employee: {e}")
            logger.error(f"Employee data: {employee_data}")
            logger.error(f"Current user: {current_user}")
            import traceback
            logger.error(f"Full traceback: {traceback.format_exc()}")
            raise HTTPException(
                status_code=500,
                detail=f"Error creating employee: {str(e)}"
            )

    async def update_employee(
        self,
        db: Session,
        employee_id: UUID,
        employee_data: EmployeeUpdate,
        current_user: CurrentUser
    ) -> Optional[EmployeeResponse]:
        """Update employee"""
        try:
            employee = db.query(Employee).filter(
                Employee.id == employee_id,
                Employee.organization_id == current_user.organization_id,
                Employee.is_active == True
            ).first()

            if not employee:
                return None

            # Update fields
            update_data = employee_data.dict(exclude_unset=True, exclude={'skills'})
            for field, value in update_data.items():
                setattr(employee, field, value)

            # Handle skills separately
            if employee_data.skills is not None:
                employee.skills = employee_data.skills

            employee.updated_by = current_user.user_id

            db.commit()
            db.refresh(employee)

            emp_response = EmployeeResponse.from_orm(employee)
            emp_response.full_name = employee.full_name
            emp_response.display_name = employee.display_name

            logger.info(f"Employee {employee.employee_id} updated by {current_user.user_id}")
            return emp_response

        except Exception as e:
            db.rollback()
            logger.error(f"Error updating employee {employee_id}: {e}")
            raise HTTPException(
                status_code=500,
                detail="Error updating employee"
            )

    async def update_employee_profile(
        self,
        db: Session,
        employee_id: UUID,
        employee_data: EmployeeUpdate,
        current_user: CurrentUser
    ) -> Optional[EmployeeResponse]:
        """Update employee profile (limited fields for self-update)"""
        try:
            employee = db.query(Employee).filter(
                Employee.id == employee_id,
                Employee.organization_id == current_user.organization_id,
                Employee.is_active == True
            ).first()

            if not employee:
                return None

            # Limit fields that can be updated in profile
            allowed_fields = {
                'phone', 'alternate_phone', 'address_line1', 'address_line2',
                'city', 'state', 'country', 'postal_code', 'emergency_contact_name',
                'emergency_contact_phone', 'emergency_contact_relationship',
                'bio', 'skills', 'profile_picture_url'
            }

            update_data = employee_data.dict(exclude_unset=True, exclude={'skills'})
            for field, value in update_data.items():
                if field in allowed_fields:
                    setattr(employee, field, value)

            # Handle skills separately
            if employee_data.skills is not None:
                employee.skills = employee_data.skills

            employee.updated_by = current_user.user_id

            db.commit()
            db.refresh(employee)

            emp_response = EmployeeResponse.from_orm(employee)
            emp_response.full_name = employee.full_name
            emp_response.display_name = employee.display_name

            logger.info(f"Employee profile {employee.employee_id} updated by {current_user.user_id}")
            return emp_response

        except Exception as e:
            db.rollback()
            logger.error(f"Error updating employee profile {employee_id}: {e}")
            raise HTTPException(
                status_code=500,
                detail="Error updating employee profile"
            )

    async def delete_employee(
        self,
        db: Session,
        employee_id: UUID,
        current_user: CurrentUser
    ) -> bool:
        """Delete employee (soft delete)"""
        try:
            employee = db.query(Employee).filter(
                Employee.id == employee_id,
                Employee.organization_id == current_user.organization_id,
                Employee.is_active == True
            ).first()

            if not employee:
                return False

            # Soft delete
            employee.is_active = False
            employee.updated_by = current_user.user_id

            db.commit()

            logger.info(f"Employee {employee.employee_id} deleted by {current_user.user_id}")
            return True

        except Exception as e:
            db.rollback()
            logger.error(f"Error deleting employee {employee_id}: {e}")
            raise HTTPException(
                status_code=500,
                detail="Error deleting employee"
            )

    # Department methods
    async def get_departments(
        self,
        db: Session,
        current_user: CurrentUser
    ) -> List[DepartmentResponse]:
        """Get all departments"""
        try:
            departments = db.query(Department).filter(
                Department.organization_id == current_user.organization_id,
                Department.is_active == True
            ).all()

            return [DepartmentResponse.from_orm(dept) for dept in departments]

        except Exception as e:
            logger.error(f"Error getting departments: {e}")
            raise HTTPException(
                status_code=500,
                detail="Error retrieving departments"
            )

    async def create_department(
        self,
        db: Session,
        department_data: DepartmentCreate,
        current_user: CurrentUser
    ) -> DepartmentResponse:
        """Create new department"""
        try:
            # Check if code already exists
            existing = db.query(Department).filter(
                Department.code == department_data.code,
                Department.organization_id == current_user.organization_id
            ).first()

            if existing:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Department code already exists"
                )

            department = Department(
                **department_data.dict(),
                organization_id=current_user.organization_id,
                created_by=current_user.user_id
            )

            db.add(department)
            db.commit()
            db.refresh(department)

            logger.info(f"Department {department.code} created by {current_user.user_id}")
            return DepartmentResponse.from_orm(department)

        except HTTPException:
            raise
        except Exception as e:
            db.rollback()
            logger.error(f"Error creating department: {e}")
            raise HTTPException(
                status_code=500,
                detail="Error creating department"
            )

    async def update_department(
        self,
        db: Session,
        department_id: UUID,
        department_data: DepartmentUpdate,
        current_user: CurrentUser
    ) -> Optional[DepartmentResponse]:
        """Update department"""
        try:
            department = db.query(Department).filter(
                Department.id == department_id,
                Department.organization_id == current_user.organization_id,
                Department.is_active == True
            ).first()

            if not department:
                return None

            # Update fields
            update_data = department_data.dict(exclude_unset=True)
            for field, value in update_data.items():
                setattr(department, field, value)

            department.updated_by = current_user.user_id

            db.commit()
            db.refresh(department)

            logger.info(f"Department {department.code} updated by {current_user.user_id}")
            return DepartmentResponse.from_orm(department)

        except Exception as e:
            db.rollback()
            logger.error(f"Error updating department {department_id}: {e}")
            raise HTTPException(
                status_code=500,
                detail="Error updating department"
            )

    # Designation methods
    async def get_designations(
        self,
        db: Session,
        current_user: CurrentUser
    ) -> List[DesignationResponse]:
        """Get all designations"""
        try:
            designations = db.query(Designation).filter(
                Designation.organization_id == current_user.organization_id,
                Designation.is_active == True
            ).all()

            return [DesignationResponse.from_orm(desig) for desig in designations]

        except Exception as e:
            logger.error(f"Error getting designations: {e}")
            raise HTTPException(
                status_code=500,
                detail="Error retrieving designations"
            )

    async def create_designation(
        self,
        db: Session,
        designation_data: DesignationCreate,
        current_user: CurrentUser
    ) -> DesignationResponse:
        """Create new designation"""
        try:
            # Check if code already exists
            existing = db.query(Designation).filter(
                Designation.code == designation_data.code,
                Designation.organization_id == current_user.organization_id
            ).first()

            if existing:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Designation code already exists"
                )

            designation = Designation(
                **designation_data.dict(),
                organization_id=current_user.organization_id,
                created_by=current_user.user_id
            )

            db.add(designation)
            db.commit()
            db.refresh(designation)

            logger.info(f"Designation {designation.code} created by {current_user.user_id}")
            return DesignationResponse.from_orm(designation)

        except HTTPException:
            raise
        except Exception as e:
            db.rollback()
            logger.error(f"Error creating designation: {e}")
            raise HTTPException(
                status_code=500,
                detail="Error creating designation"
            )



