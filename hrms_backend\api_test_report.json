{"test_summary": {"total_tests": 9, "passed_tests": 1, "failed_tests": 8, "success_rate": 11.11}, "api_info": {"base_url": "http://localhost:8000", "tested_at": "2025-07-01T13:05:14.782417"}, "test_results": [{"test_name": "Server Health", "success": false, "message": "Cannot connect to server: HTTPConnectionPool(host='localhost', port=8000): Max retries exceeded with url: /health (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001A6FCCBFDC0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))", "timestamp": "2025-07-01T13:04:46.160844", "data": null}, {"test_name": "API Documentation", "success": false, "message": "Error: HTTPConnectionPool(host='localhost', port=8000): Max retries exceeded with url: /docs (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001A6FCD1C310>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))", "timestamp": "2025-07-01T13:04:50.254144", "data": null}, {"test_name": "Authentication Endpoint", "success": false, "message": "Error: HTTPConnectionPool(host='localhost', port=8000): Max retries exceeded with url: /api/auth/login (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001A6FCD1C760>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))", "timestamp": "2025-07-01T13:04:54.342674", "data": null}, {"test_name": "Ticket Endpoints", "success": false, "message": "Error: HTTPConnectionPool(host='localhost', port=8000): Max retries exceeded with url: /api/ticket/ (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001A6FCD1CAC0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))", "timestamp": "2025-07-01T13:04:58.434761", "data": null}, {"test_name": "AI Endpoints", "success": false, "message": "Error: HTTPConnectionPool(host='localhost', port=8000): Max retries exceeded with url: /api/ticket/ai/categorize (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001A6FCD1CEE0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))", "timestamp": "2025-07-01T13:05:02.518409", "data": null}, {"test_name": "Multi-Channel Endpoints", "success": false, "message": "Error: HTTPConnectionPool(host='localhost', port=8000): Max retries exceeded with url: /api/ticket/channels/email/create (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001A6FCD1D240>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))", "timestamp": "2025-07-01T13:05:06.603318", "data": null}, {"test_name": "Reporting Endpoints", "success": false, "message": "Error: HTTPConnectionPool(host='localhost', port=8000): Max retries exceeded with url: /api/ticket/reports/overview (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001A6FCD1D600>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))", "timestamp": "2025-07-01T13:05:10.684630", "data": null}, {"test_name": "Template Endpoints", "success": false, "message": "Error: HTTPConnectionPool(host='localhost', port=8000): Max retries exceeded with url: /api/ticket/templates (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001A6FCD1D990>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))", "timestamp": "2025-07-01T13:05:14.778370", "data": null}, {"test_name": "WebSocket Endpoint", "success": true, "message": "WebSocket endpoint available (placeholder test)", "timestamp": "2025-07-01T13:05:14.781376", "data": null}]}