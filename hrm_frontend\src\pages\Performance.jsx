/**
 * Performance Management Page with RBAC Integration
 * Displays performance reviews, goals, and analytics with role-based access control
 */

import React, { useState } from 'react';
import { Target, TrendingUp, Award, Calendar, Plus, Eye, Edit, Star } from 'lucide-react';
import { usePermissions } from '../hooks/usePermissions';
import { PermissionGate, ConditionalRender } from '../components/ProtectedRoute';

export default function Performance({ activeTab = 'my-reviews' }) {
  const permissions = usePermissions();
  const [selectedPeriod, setSelectedPeriod] = useState('2024');

  // Mock performance data
  const performanceReviews = [
    {
      id: 1,
      period: 'Q4 2023',
      reviewer: 'John Manager',
      status: 'completed',
      overallRating: 4.2,
      submittedDate: '2024-01-15',
      dueDate: '2024-01-10',
      categories: {
        technical: 4.5,
        communication: 4.0,
        teamwork: 4.2,
        leadership: 3.8
      }
    },
    {
      id: 2,
      period: 'Q3 2023',
      reviewer: 'John Manager',
      status: 'completed',
      overallRating: 3.9,
      submittedDate: '2023-10-15',
      dueDate: '2023-10-10',
      categories: {
        technical: 4.0,
        communication: 3.8,
        teamwork: 4.1,
        leadership: 3.7
      }
    },
    {
      id: 3,
      period: 'Q1 2024',
      reviewer: 'John Manager',
      status: 'pending',
      overallRating: null,
      submittedDate: null,
      dueDate: '2024-04-10',
      categories: {}
    }
  ];

  const goals = [
    {
      id: 1,
      title: 'Complete React certification',
      description: 'Obtain React developer certification to improve frontend skills',
      status: 'completed',
      progress: 100,
      dueDate: '2024-03-31',
      category: 'Professional Development'
    },
    {
      id: 2,
      title: 'Lead team project',
      description: 'Successfully lead the HRMS development project',
      status: 'in-progress',
      progress: 75,
      dueDate: '2024-06-30',
      category: 'Leadership'
    },
    {
      id: 3,
      title: 'Improve code review quality',
      description: 'Provide more detailed and constructive code reviews',
      status: 'in-progress',
      progress: 60,
      dueDate: '2024-05-31',
      category: 'Technical'
    },
    {
      id: 4,
      title: 'Mentor junior developers',
      description: 'Mentor 2 junior developers and help them grow',
      status: 'not-started',
      progress: 0,
      dueDate: '2024-12-31',
      category: 'Leadership'
    }
  ];

  const getStatusColor = (status) => {
    switch (status) {
      case 'completed': return 'text-green-600 bg-green-100';
      case 'in-progress': return 'text-blue-600 bg-blue-100';
      case 'pending': return 'text-yellow-600 bg-yellow-100';
      case 'not-started': return 'text-gray-600 bg-gray-100';
      case 'overdue': return 'text-red-600 bg-red-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const renderRatingStars = (rating) => {
    const stars = [];
    const fullStars = Math.floor(rating);
    const hasHalfStar = rating % 1 !== 0;

    for (let i = 0; i < fullStars; i++) {
      stars.push(<Star key={i} size={16} className="text-yellow-400 fill-current" />);
    }

    if (hasHalfStar) {
      stars.push(<Star key="half" size={16} className="text-yellow-400 fill-current opacity-50" />);
    }

    const emptyStars = 5 - Math.ceil(rating);
    for (let i = 0; i < emptyStars; i++) {
      stars.push(<Star key={`empty-${i}`} size={16} className="text-gray-300" />);
    }

    return stars;
  };

  const renderMyReviews = () => (
    <div className="space-y-6">
      {/* Performance Summary */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <Award className="h-8 w-8 text-yellow-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Average Rating</p>
              <p className="text-2xl font-bold text-gray-900">4.1</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <Target className="h-8 w-8 text-blue-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Goals Completed</p>
              <p className="text-2xl font-bold text-gray-900">
                {goals.filter(g => g.status === 'completed').length}/{goals.length}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <TrendingUp className="h-8 w-8 text-green-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Reviews Completed</p>
              <p className="text-2xl font-bold text-gray-900">
                {performanceReviews.filter(r => r.status === 'completed').length}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <Calendar className="h-8 w-8 text-purple-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Pending Reviews</p>
              <p className="text-2xl font-bold text-gray-900">
                {performanceReviews.filter(r => r.status === 'pending').length}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Performance Reviews */}
      <div className="bg-white rounded-lg shadow">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900">Performance Reviews</h3>
        </div>
        <div className="divide-y divide-gray-200">
          {performanceReviews.map((review) => (
            <div key={review.id} className="p-6">
              <div className="flex items-center justify-between">
                <div className="flex-1">
                  <div className="flex items-center space-x-3">
                    <h4 className="text-lg font-medium text-gray-900">{review.period}</h4>
                    <span className={`inline-flex px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(review.status)}`}>
                      {review.status}
                    </span>
                  </div>
                  <div className="mt-2 flex items-center space-x-6 text-sm text-gray-500">
                    <span>Reviewer: {review.reviewer}</span>
                    <span>Due: {review.dueDate}</span>
                    {review.submittedDate && <span>Submitted: {review.submittedDate}</span>}
                  </div>
                  {review.overallRating && (
                    <div className="mt-3">
                      <div className="flex items-center space-x-2">
                        <span className="text-sm text-gray-600">Overall Rating:</span>
                        <div className="flex items-center space-x-1">
                          {renderRatingStars(review.overallRating)}
                          <span className="text-sm font-medium text-gray-900 ml-2">
                            {review.overallRating}/5.0
                          </span>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
                <div className="flex items-center space-x-2">
                  <button className="text-blue-600 hover:text-blue-900">
                    <Eye size={16} />
                  </button>
                  {review.status === 'pending' && (
                    <button className="text-gray-600 hover:text-gray-900">
                      <Edit size={16} />
                    </button>
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );

  const renderMyGoals = () => (
    <div className="space-y-6">
      {/* Goals Header */}
      <div className="flex justify-between items-center">
        <div>
          <h3 className="text-lg font-medium text-gray-900">My Goals</h3>
          <p className="text-sm text-gray-600">Track your professional development goals</p>
        </div>
        <PermissionGate permission="performanceReviewsSelf">
          <button className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 flex items-center space-x-2">
            <Plus size={16} />
            <span>Add Goal</span>
          </button>
        </PermissionGate>
      </div>

      {/* Goals List */}
      <div className="space-y-4">
        {goals.map((goal) => (
          <div key={goal.id} className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center justify-between mb-4">
              <div className="flex-1">
                <div className="flex items-center space-x-3">
                  <h4 className="text-lg font-medium text-gray-900">{goal.title}</h4>
                  <span className={`inline-flex px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(goal.status)}`}>
                    {goal.status.replace('-', ' ')}
                  </span>
                </div>
                <p className="mt-1 text-sm text-gray-600">{goal.description}</p>
                <div className="mt-2 flex items-center space-x-4 text-sm text-gray-500">
                  <span>Category: {goal.category}</span>
                  <span>Due: {goal.dueDate}</span>
                </div>
              </div>
              <div className="flex items-center space-x-2">
                <button className="text-blue-600 hover:text-blue-900">
                  <Eye size={16} />
                </button>
                <button className="text-gray-600 hover:text-gray-900">
                  <Edit size={16} />
                </button>
              </div>
            </div>
            
            {/* Progress Bar */}
            <div className="mt-4">
              <div className="flex justify-between items-center mb-2">
                <span className="text-sm text-gray-600">Progress</span>
                <span className="text-sm font-medium text-gray-900">{goal.progress}%</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div 
                  className="bg-blue-600 h-2 rounded-full" 
                  style={{ width: `${goal.progress}%` }}
                ></div>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );

  const renderTeamPerformance = () => (
    <PermissionGate permission="performanceReviewsTeam">
      <div className="space-y-6">
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Team Performance</h3>
          <p className="text-gray-600">Manage team performance reviews and goals...</p>
        </div>
      </div>
    </PermissionGate>
  );

  const renderContent = () => {
    switch (activeTab) {
      case 'my-reviews':
        return renderMyReviews();
      case 'my-goals':
        return renderMyGoals();
      case 'team-performance':
        return renderTeamPerformance();
      default:
        return renderMyReviews();
    }
  };

  return (
    <div className="p-6">
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-gray-900">Performance Management</h1>
        <p className="text-gray-600">Track your performance reviews and goals</p>
      </div>

      {renderContent()}
    </div>
  );
}
