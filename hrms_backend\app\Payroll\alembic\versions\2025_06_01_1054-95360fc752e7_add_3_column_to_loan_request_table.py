"""add 3 column to loan request table


Revision ID: 95360fc752e7
Revises: 155b0bdbfce8
Create Date: 2025-06-01 10:54:32.038570

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '95360fc752e7'
down_revision: Union[str, None] = '155b0bdbfce8'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    op.add_column('loan_request', sa.Column('balance', sa.Float(), nullable=True))
    op.add_column('loan_request', sa.Column('disbursed_at', sa.DateTime(), nullable=True))
    op.add_column('loan_request', sa.Column('employee_id', sa.Integer(), sa.<PERSON>ey("employees.id"), nullable=True))



def downgrade() -> None:
    op.drop_column('loan_request', 'employee_id')
    op.drop_column('loan_request', 'disbursed_at')
    op.drop_column('loan_request', 'balance')

