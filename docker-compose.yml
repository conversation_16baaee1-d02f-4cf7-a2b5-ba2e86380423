version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15
    container_name: hrms_postgres
    environment:
      POSTGRES_DB: hrms_db
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password123
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./hrms_backend/init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - hrms_network

  # Redis for caching and sessions
  redis:
    image: redis:7-alpine
    container_name: hrms_redis
    ports:
      - "6379:6379"
    networks:
      - hrms_network

  # Backend API
  backend:
    build:
      context: ./hrms_backend
      dockerfile: Dockerfile
    container_name: hrms_backend
    environment:
      - DATABASE_URL=***********************************************/hrms_db
      - REDIS_URL=redis://redis:6379
      - JWT_SECRET_KEY=your-secret-key-here
      - ENVIRONMENT=production
    ports:
      - "8085:8085"
    depends_on:
      - postgres
      - redis
    volumes:
      - ./hrms_backend:/app
    networks:
      - hrms_network
    restart: unless-stopped

  # Frontend
  frontend:
    build:
      context: ./hrm_frontend
      dockerfile: Dockerfile
    container_name: hrms_frontend
    environment:
      - VITE_API_URL=http://localhost:8085/api
    ports:
      - "5173:5173"
    depends_on:
      - backend
    volumes:
      - ./hrm_frontend:/app
      - /app/node_modules
    networks:
      - hrms_network
    restart: unless-stopped

  # Nginx reverse proxy (optional)
  nginx:
    image: nginx:alpine
    container_name: hrms_nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - frontend
      - backend
    networks:
      - hrms_network
    restart: unless-stopped

volumes:
  postgres_data:

networks:
  hrms_network:
    driver: bridge
