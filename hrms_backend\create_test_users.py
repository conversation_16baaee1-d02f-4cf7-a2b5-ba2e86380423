#!/usr/bin/env python3
"""
Create test users for different roles
"""

import sys
import os
from uuid import uuid4
from datetime import datetime, date

# Add the app directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'app'))

from sqlalchemy.orm import Session
from sqlalchemy import text
from app.db.session import SessionLocal

def create_test_users():
    """Create test users for all roles"""
    db = SessionLocal()
    
    try:
        print("Creating test users for all roles...")
        
        # Test users data
        test_users = [
            {
                'employee_id': 'ADMIN001',
                'first_name': 'Admin',
                'last_name': 'User',
                'email': '<EMAIL>',
                'role': 'admin'
            },
            {
                'employee_id': 'HR001',
                'first_name': 'HR',
                'last_name': 'Manager',
                'email': '<EMAIL>',
                'role': 'hr'
            },
            {
                'employee_id': 'MGR001',
                'first_name': 'Manager',
                'last_name': 'User',
                'email': '<EMAIL>',
                'role': 'manager'
            },
            {
                'employee_id': 'EMP001',
                'first_name': 'Employee',
                'last_name': 'User',
                'email': '<EMAIL>',
                'role': 'employee'
            }
        ]
        
        for user_data in test_users:
            # Check if employee already exists
            result = db.execute(text("SELECT employee_id FROM employees WHERE employee_id = :emp_id LIMIT 1"), 
                              {'emp_id': user_data['employee_id']})
            existing = result.fetchone()

            if existing:
                print(f"✅ User {user_data['employee_id']} already exists")
                continue
            
            # Create employee
            employee_id = uuid4()
            
            insert_sql = text("""
                INSERT INTO employees (
                    id, employee_id, first_name, last_name, email,
                    hire_date, is_active, created_at, updated_at, organization_id, employment_type, role
                ) VALUES (
                    :id, :employee_id, :first_name, :last_name, :email,
                    :hire_date, :is_active, :created_at, :updated_at, :organization_id, :employment_type, :role
                )
            """)
            
            db.execute(insert_sql, {
                'id': employee_id,
                'employee_id': user_data['employee_id'],
                'first_name': user_data['first_name'],
                'last_name': user_data['last_name'],
                'email': user_data['email'],
                'hire_date': date.today(),
                'is_active': True,
                'created_at': datetime.utcnow(),
                'updated_at': datetime.utcnow(),
                'organization_id': uuid4(),
                'employment_type': 'FULL_TIME',
                'role': user_data['role']
            })
            
            print(f"✅ Created user: {user_data['first_name']} {user_data['last_name']} ({user_data['employee_id']}) - {user_data['role']}")
        
        db.commit()
        print("\n✅ All test users created successfully!")
        print("\nTest credentials:")
        print("- Admin: ADMIN001 / password123")
        print("- HR: HR001 / password123") 
        print("- Manager: MGR001 / password123")
        print("- Employee: EMP001 / password123")
        
    except Exception as e:
        print(f"❌ Error creating test users: {e}")
        db.rollback()
        raise
    finally:
        db.close()

if __name__ == "__main__":
    create_test_users()
