"""create approvals table

Revision ID: 8e42ed311908
Revises: 70f07005ae0a
Create Date: 2024-04-03 15:04:50.354288

"""
from typing import Sequence, Union

from alembic import op
from sqlalchemy import func, TIMESTAMP, ForeignKey
from sqlalchemy import <PERSON><PERSON>n, Integer, String

# revision identifiers, used by Alembic.
revision: str = '8e42ed311908'
down_revision: Union[str, None] = '70f07005ae0a'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    op.create_table(
        'approvals',
        Column('id', Integer, primary_key=True),
        <PERSON><PERSON><PERSON>('role', String(45), nullable=False),
        <PERSON>umn('level', String(45), unique=True, nullable=False),
        <PERSON><PERSON><PERSON>("employee_id", Integer, ForeignKey("employees.id")),
        <PERSON><PERSON><PERSON>("user_id", <PERSON><PERSON><PERSON>, Foreign<PERSON><PERSON>("users.id")),
        <PERSON>umn("timestamp", TIMESTAMP, server_default=func.now()),
    )
    
def downgrade() -> None:
    op.drop_table("approvals")
