from flask import url_for
from flask.views import MethodView
from flask_smorest import Blueprint, abort
from flask_jwt_extended import jwt_required, get_jwt_identity
from core.middleware import roles_required
from schemas import PayrollProcessingSchema
from sqlalchemy.orm.exc import NoResultFound
from sqlalchemy.exc import SQLAlchemyError, IntegrityError
import core.utils.response_message as RESPONSEMESSAGE
from core.services.payroll_processings import PayrollProcessingsService

blueprint = Blueprint("Payroll Processing", __name__, description="Operations for Payroll Processing")
    
@blueprint.route("/payroll_processings/<id>")
class PayrollProcessingList(MethodView):
    @roles_required(['admin'])
    @blueprint.response(200, PayrollProcessingSchema)
    def get(self, id):
        service = PayrollProcessingsService()
        processing = service.getPayrollProcessings(id)
        if not processing:
            abort(401, message="Payroll Processing does not exist")
        return processing    
    
    @roles_required(['admin'])
    def delete(self, id):
        service = PayrollProcessingsService()
        processing = service.getPayrollProcessings(id)
        if not processing:
            abort(404, message="Payroll Processing does not exist")
        service.deletePayrollProcessings(id)
        return {"message" : "Payroll Processing deleted"}
    
    @roles_required(['admin'])
    @blueprint.arguments(PayrollProcessingSchema)
    @blueprint.response(201, PayrollProcessingSchema)
    def put(self, id, data):
        service = PayrollProcessingsService()
        processing = service.getPayrollProcessings(id)
        if not processing:
            abort(404, message="Payroll Processing does not exist")
        try :
            new_processing = service.updatePayrollProcessings(id, data)
            return new_processing
        except SQLAlchemyError:
                abort(500, message="Error while updating Payroll Processing")
    
@blueprint.route("/payroll_processings")
class PayrollProcessing(MethodView):
    @roles_required(['admin'])
    @blueprint.arguments(PayrollProcessingSchema)
    @blueprint.response(200, PayrollProcessingSchema)
    def post(self, data):
        try:
            service = PayrollProcessingsService()
            processing = service.getPayrollProcessingsByKey({"id": data['id']})
            if not processing:
                new_processing = service.createPayrollProcessings(data)
            else:
                abort(400, message="Payroll Processing already exist")
        except IntegrityError:
            abort(500, message="Error while creating Payroll Processing")
        except SQLAlchemyError as e:
            print(e)
            abort(500, message="Error while creating Payroll Processing")
        return new_processing