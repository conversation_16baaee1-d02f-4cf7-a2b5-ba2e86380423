#!/usr/bin/env python3
"""
Database Schema Fix Script
Fixes missing columns and schema issues in the HRMS database
"""

import os
import sys
import psycopg2
from psycopg2.extensions import ISOLATION_LEVEL_AUTOCOMMIT

# Add the app directory to the Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'app'))

def get_db_connection():
    """Get database connection from environment variables"""
    try:
        conn = psycopg2.connect(
            host=os.getenv('DB_HOST', 'localhost'),
            port=os.getenv('DB_PORT', '5432'),
            database=os.getenv('DB_NAME', 'hrms_db'),
            user=os.getenv('DB_USER', 'postgres'),
            password=os.getenv('DB_PASSWORD', 'password')
        )
        conn.set_isolation_level(ISOLATION_LEVEL_AUTOCOMMIT)
        return conn
    except Exception as e:
        print(f"❌ Error connecting to database: {e}")
        return None

def check_column_exists(cursor, table_name, column_name):
    """Check if a column exists in a table"""
    cursor.execute("""
        SELECT EXISTS (
            SELECT 1 FROM information_schema.columns 
            WHERE table_name = %s AND column_name = %s
        )
    """, (table_name, column_name))
    return cursor.fetchone()[0]

def fix_employee_schema(cursor):
    """Fix missing columns in employees table"""
    print("🔧 Fixing employees table schema...")
    
    # Check and add missing columns
    missing_columns = []
    
    # Check for notes column
    if not check_column_exists(cursor, 'employees', 'notes'):
        missing_columns.append(('notes', 'TEXT'))
    
    # Check for organization_id column (should exist but might be missing)
    if not check_column_exists(cursor, 'employees', 'organization_id'):
        missing_columns.append(('organization_id', 'UUID'))
    
    # Add missing columns
    for column_name, column_type in missing_columns:
        try:
            cursor.execute(f"ALTER TABLE employees ADD COLUMN {column_name} {column_type}")
            print(f"✅ Added column: employees.{column_name}")
        except Exception as e:
            print(f"⚠️ Error adding column {column_name}: {e}")
    
    if not missing_columns:
        print("✅ Employees table schema is up to date")

def fix_leave_requests_schema(cursor):
    """Fix leave_requests table schema and relationships"""
    print("🔧 Fixing leave_requests table schema...")
    
    # Check if leave_requests table exists
    cursor.execute("""
        SELECT EXISTS (
            SELECT 1 FROM information_schema.tables 
            WHERE table_name = 'leave_requests'
        )
    """)
    
    if not cursor.fetchone()[0]:
        print("⚠️ leave_requests table does not exist")
        return
    
    # Check foreign key constraints
    cursor.execute("""
        SELECT constraint_name, column_name 
        FROM information_schema.key_column_usage 
        WHERE table_name = 'leave_requests' 
        AND referenced_table_name = 'employees'
    """)
    
    fk_constraints = cursor.fetchall()
    print(f"📋 Found {len(fk_constraints)} foreign key constraints to employees table")
    
    for constraint_name, column_name in fk_constraints:
        print(f"  - {constraint_name}: {column_name}")

def update_organization_ids(cursor):
    """Update organization_id for existing employees"""
    print("🔧 Updating organization IDs for existing employees...")
    
    try:
        # Get the first organization ID (assuming single organization setup)
        cursor.execute("SELECT id FROM organizations LIMIT 1")
        org_result = cursor.fetchone()
        
        if org_result:
            org_id = org_result[0]
            
            # Update employees without organization_id
            cursor.execute("""
                UPDATE employees 
                SET organization_id = %s 
                WHERE organization_id IS NULL
            """, (org_id,))
            
            updated_count = cursor.rowcount
            print(f"✅ Updated {updated_count} employees with organization_id")
        else:
            print("⚠️ No organizations found in database")
            
    except Exception as e:
        print(f"❌ Error updating organization IDs: {e}")

def main():
    """Main function to fix database schema"""
    print("🚀 Starting database schema fix...")
    
    # Load environment variables
    from dotenv import load_dotenv
    load_dotenv()
    
    # Connect to database
    conn = get_db_connection()
    if not conn:
        print("❌ Failed to connect to database")
        return
    
    try:
        cursor = conn.cursor()
        
        # Fix schemas
        fix_employee_schema(cursor)
        fix_leave_requests_schema(cursor)
        update_organization_ids(cursor)
        
        print("✅ Database schema fix completed successfully!")
        
    except Exception as e:
        print(f"❌ Error during schema fix: {e}")
    finally:
        conn.close()

if __name__ == "__main__":
    main()
