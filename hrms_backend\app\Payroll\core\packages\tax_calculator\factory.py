from core.packages.tax_calculator.nigeria_tax import NigeriaTaxCalculator
from core.packages.tax_calculator.ghana_tax import GhanaTaxCalculator

class TaxCalculatorFactory:
    @staticmethod
    def create_calculator(country, tax_type):
        if country == 'Nigeria':
            return NigeriaTaxCalculator(tax_type)
        elif country == 'Ghana':
            return GhanaTaxCalculator()
        else:
            raise ValueError(f"Tax calculator for {country} not implemented.")
