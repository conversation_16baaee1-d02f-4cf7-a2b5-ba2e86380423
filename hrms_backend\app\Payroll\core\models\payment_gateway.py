from core.databases.database import db
from core.models.basemodel import ModelBase
from sqlalchemy.orm import relationship
from core.models.employees import EmployeeModel
from datetime import datetime

class PaymentGatewayModel(ModelBase):
    __tablename__ = "payment_gateway"

    id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    user_id = db.Column(db.Integer, unique=True, autoincrement=True)
    payment_gateway_name = db.Column(db.String(250), unique=True, nullable=False)
    timestamp = db.Column(db.DateTime, nullable=False, default=datetime.now())
    
