import uuid
import requests
from core.models.paystack_credential import Paystack_IntegrationModel
from core.services.Encrypt_Decrypt import EncryptionService
from schemas import PayStack_Credentials
from ..repositories.user import UserRepository
from dotenv import load_dotenv
import os
import datetime
from datetime import datetime as dt
from zoneinfo import ZoneInfo
import pytz

# Load the secret key from .env
load_dotenv()
SECRET_KEY_ENCRYPT_DECRYPT = os.getenv("SECRET_KEY_ENCRYPT_DECRYPT")
if not SECRET_KEY_ENCRYPT_DECRYPT:
    raise ValueError("Secret key  for encrypting and Decryting not found in environment variables!")

class PaystackService:
    def __init__(self, payroll_id=None, payment_gatwway=None, net_pay=None, prorated_net=None, prorated_status=None, account_number=None, sort_code=None):
        self.account_number = account_number
        self.sort_code = sort_code
        self.reference_code = "" 
        self.amount = prorated_net if prorated_status else net_pay
        self.encrypt_service =  EncryptionService(SECRET_KEY_ENCRYPT_DECRYPT)
        self.base_url = "https://api.paystack.co"
        self.payment_gatwway = payment_gatwway
        self.payroll_id = payroll_id
       
    def initialize_header(self):
        """Defer header initialization until the key is decrypted."""
        self.header = {"Authorization": f"Bearer {self.decrypt_key()}"}
        return self.header
    
    def format_timestamp(self,timestamp):
        utc = pytz.utc
        parsed_timestamp =parsed_timestamp = dt.strptime(timestamp, "%Y-%m-%dT%H:%M:%S.%fZ").replace(tzinfo=utc)
        wat = pytz.timezone("Africa/Lagos")
        wat_timestamp = parsed_timestamp.astimezone(wat)
        formatted_timestamp = wat_timestamp.strftime("%A, %d %B %Y, %I:%M:%S %p WAT")
        return formatted_timestamp
        
    def get_current_data_time(self):
        current_transaction_month_time = datetime.datetime.now()
        current_transaction_month = current_transaction_month_time.strftime("%B")  # Full month name, "November"
        current_transaction_year = current_transaction_month_time.strftime("%Y")  # Year, "2024"
        current_transaction_date = current_transaction_month_time.strftime("%d")  # Day of the month (e.g., 07)
        current_transaction_time = current_transaction_month_time.strftime("%I:%M:%S %p")  # Time in AM/PM format
        return current_transaction_date, current_transaction_month, current_transaction_year, current_transaction_time

    def get_banks(self,secret_key):    
        try:
            url = f"{self.base_url}/bank"
            headers = {"Authorization": f"Bearer {secret_key}"}
            response = requests.get(url, headers=headers)
            # Return the JSON response and status code
            return response.json(), response.status_code
        except requests.RequestException as e:
            # Handle network or API request errors
            print(f"Error while fetching banks: {str(e)}")
            return {"error": "Failed to connect to Paystack API"}, 500
        
    def get_balance(self, secret_key):
        """
        Fetch the current balance from Paystack.
        :param secret_key: The decrypted Paystack secret key.
        :return: A tuple (response_data, status_code).
        """
        try:
            url = f"{self.base_url}/balance"
            headers = self.header
            response = requests.get(url, headers=headers)

            # Return the JSON response and status code
            return response.json(), response.status_code
        except requests.RequestException as e:
            # Handle network or API request errors
            print(f"Error while fetching balance: {str(e)}")
            return {"error": "Failed to connect to Paystack API"}, 500
        
   
    def create_transfer_recipient(self, secret_key, transfer_recipient_data):
        try:
            url = f"{self.base_url}/transferrecipient"
            headers = {
                "Authorization": f"Bearer {secret_key}",
                "Content-Type": "application/json",
            }
            response = requests.post(url, headers=headers, json=transfer_recipient_data)
            return response.json(), response.status_code
        except requests.RequestException as e:
            print(f"Error while creating transfer recipient: {str(e)}")
            return {"error": "Failed to connect to Paystack API"}, 500
        
    def create_transfer(self, secret_key, transfer_data):
        try:
            url = f"{self.base_url}/transfer"
            headers = {
                "Authorization": f"Bearer {secret_key}",
                "Content-Type": "application/json",
            }
            response = requests.post(url, headers=headers, json=transfer_data)
            return response.json(), response.status_code
        except requests.RequestException as e:
            print(f"Error while initiating transfer: {str(e)}")
            return {"error": "Failed to connect to Paystack API"}, 500
        
    def select_payment_gatway(self):
        return self.payment_gatwway
    
    def decrypt_key(self):
        user = UserRepository().authUser()  # Gets the current login user data
        user_id = user.id
        encrpt_paystack_key  = Paystack_IntegrationModel().query.filter_by(user_id=user_id).first()
        if encrpt_paystack_key:
            encrpt_paystack_key_obj = PayStack_Credentials().dump(encrpt_paystack_key)
            encrpt_paystack_key_obj = encrpt_paystack_key_obj.get("paystack_key")
            decrpt_paystack_key  = self.encrypt_service.decrypt(encrpt_paystack_key_obj)
            # print("keys encripted:", decrpt_paystack_key)
            return decrpt_paystack_key
        else:
            return f"No Paystack key"

    def paystack_get(self, endpoint):
        try:
            url = f"{self.base_url}/{endpoint}"
            response = requests.get(url, headers=self.initialize_header())
            return response.json(), response.status_code 
        except requests.RequestException as e:
            print(f"Error while fetching banks: {str(e)}")
            return {"error": "Failed to connect to Paystack API"}, 500

    def paystack_post(self, endpoint, transfer_data):
        try:
            url = f"{self.base_url}/{endpoint}"
            headers = self.header
            headers["Content-Type"] = "application/json"
            response = requests.post(url, headers=headers, json=transfer_data)
            return response.json(), response.status_code  
        except requests.RequestException as e:
            print(f"Error while initiating transfer: {str(e)}")
            return {"error": "Failed to connect to Paystack API"}, 500

    def generate_reference_code(self):
        reference_code = datetime.datetime.now().strftime("%Y%m%d-%H%M%S-") + str(uuid.uuid4())[:8]
        return reference_code
 
    def nuban_check(self):
        endpoint = f"bank/resolve?account_number={self.account_number}&bank_code={self.sort_code}"
        nuban_response = self.paystack_get(endpoint)
        self.account_number =  nuban_response[0].get('data', {}).get('account_number')
        account_name = nuban_response[0].get('data', {}).get('account_name')
        self.sort_code = self.sort_code
        return account_name
    
    def generate_ref_code(self, account_name):
        endpoint = f"transferrecipient"
        payload = {
            "name" : account_name,
            "account_number" : self.account_number,
            "bank_code" : self.sort_code,
            "currency" : "NGN",
        }
        generate_ref_code_response = self.paystack_post(endpoint, payload)  
        data_details = generate_ref_code_response[0].get('data', {}).get('details')
        print(f"data_details ::{data_details}")
        recipient_code = generate_ref_code_response[0].get('data', {}).get('recipient_code')
        if data_details:
            recipient_bank_name = data_details.get('bank_name')
            recipient_account_name = data_details.get('account_name')
            recipient_account_number = data_details.get('account_number')
            recipient_bank_sort_code = data_details.get('bank_code')
        else :
           print(f"dhhdhdhd")
        
        recipient_code = recipient_code
        recipient_bank_sort_code = recipient_bank_sort_code
        return recipient_code, recipient_bank_sort_code,recipient_account_number, recipient_bank_name, recipient_account_name,self.generate_reference_code()
        
    def transfer(self, reference_id, recipient_code):
        endpoint ="transfer"
        payload = {
            "source" : "balance",
            "amount" : self.amount,
            "reference" : reference_id,
            "recipient" : recipient_code,
            "payroll_id": self.payroll_id,
            "reason" : "Salary",
            "metadata": {
                "payroll_id": str(self.payroll_id),
                "reference_code": reference_id,
                # "emcrypted_user_key": self.encrypt_service
            }
        }
        # print(f"Amount paid {self.amount}")
        # print(f"Payload {payload}")
        transfer_response = self.paystack_post(endpoint, payload)
        message  = transfer_response[0].get('status') 
        status = transfer_response[0].get('message')
        return message, status,transfer_response
    
    def paystack_webook(self):
        pass


    def disburse_payment(self):
        # print(f"🔍 Starting NUBAN check for account number: {self.account_number}")
        name = self.nuban_check()
        if not name:
            # print(f"❌ Invalid account details for account: {self.account_number}")
            raise ValueError("Invalid account details.")
        
        # print(f"✔️ NUBAN check successful, account name: {name}")

        # print(f"🔍 Generating recipient reference code for account: {name}")
        recipient_code, recipient_bank_sort_code,recipient_account_number,recipient_bank_name,recipient_account_name,ref_id = self.generate_ref_code(name)
        if not recipient_code:
            # print(f"❌ Failed to generate recipient code for account: {name}")
            raise ValueError("Failed to generate recipient code.")
        
        # print(f"✔️ Recipient code generated successfully: {recipient_code}")
        
        # print(f"🔍 Starting transfer for amount: {self.amount}")
        status,message,transfer_res = self.transfer(ref_id,recipient_code)
        if not status:
            # print(f"❌ Transfer failed with message: {message}")
            raise ValueError(f"Transfer failed: {message}")

        # If everything went fine, print the response and return data
        # print(f"✔️ Transfer successful with status: {status}")
        # print(f"📑 Transfer response data: {transfer_res}")

        return {
            "message": message,
            "status": status,
            "transfer_res": transfer_res,
            "verified_bank_name": recipient_bank_name,
            "verified_account_name": recipient_account_name,
            "verified_account_number": recipient_account_number,
            "verified_bank_sort_code": recipient_bank_sort_code,
            "transaction_date": self.get_current_data_time(),
            "transaction_id": "",  # Placeholder if needed, or set dynamically
        }

        


    

                   