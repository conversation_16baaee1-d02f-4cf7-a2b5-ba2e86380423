import logging
from typing import Dict, List, Optional, Tuple, Any
from datetime import datetime, timedelta
from sqlalchemy.orm import Session
from sqlalchemy import func, and_, or_
from fastapi import HTTPException, status

from ...db.models.ticket import Ticket, TicketType, TicketPriority, TicketStatus
from ...db.models.employee import Employee, Department
from ...core.security import CurrentUser

logger = logging.getLogger(__name__)


class SmartRoutingEngine:
    """AI-powered intelligent ticket routing and assignment"""

    def __init__(self):
        # Agent expertise mapping based on ticket types
        self.expertise_mapping = {
            TicketType.IT_SUPPORT: ["IT Specialist", "System Administrator", "Help Desk", "Technical Support"],
            TicketType.HR_QUERY: ["HR Specialist", "HR Manager", "People Operations", "HR Business Partner"],
            TicketType.FACILITIES: ["Facilities Manager", "Building Operations", "Maintenance", "Security"],
            TicketType.PAYROLL: ["Payroll Specialist", "Payroll Manager", "Finance", "Accounting"],
            TicketType.LEAVE: ["HR Specialist", "Leave Administrator", "People Operations"],
            TicketType.EQUIPMENT: ["IT Specialist", "Asset Manager", "Procurement"],
            TicketType.ACCESS_REQUEST: ["IT Security", "System Administrator", "Access Control"],
            TicketType.COMPLAINT: ["HR Manager", "Employee Relations", "Compliance"],
            TicketType.TRAINING_REQUEST: ["Learning & Development", "Training Coordinator", "HR Specialist"]
        }

        # Skill keywords for advanced matching
        self.skill_keywords = {
            "network": ["network", "cisco", "routing", "switching", "firewall", "vpn"],
            "security": ["security", "access", "permissions", "authentication", "encryption"],
            "database": ["database", "sql", "mysql", "postgresql", "oracle", "mongodb"],
            "email": ["email", "outlook", "exchange", "office365", "gmail"],
            "hardware": ["hardware", "laptop", "desktop", "printer", "monitor"],
            "software": ["software", "application", "program", "installation", "update"],
            "mobile": ["mobile", "phone", "android", "ios", "tablet", "app"],
            "benefits": ["benefits", "insurance", "401k", "retirement", "health"],
            "compliance": ["compliance", "policy", "regulation", "audit", "legal"],
            "onboarding": ["onboarding", "new hire", "orientation", "training"]
        }

    async def find_best_agent(
        self,
        db: Session,
        ticket: Ticket,
        organization_id: str,
        exclude_agents: Optional[List[str]] = None
    ) -> Optional[Dict[str, Any]]:
        """Find the best agent for ticket assignment using AI routing"""
        try:
            # Get available agents
            available_agents = await self._get_available_agents(
                db, organization_id, ticket.ticket_type, exclude_agents
            )

            if not available_agents:
                return None

            # Score agents based on multiple factors
            agent_scores = []
            for agent in available_agents:
                score = await self._calculate_agent_score(db, agent, ticket)
                agent_scores.append({
                    "agent": agent,
                    "score": score,
                    "reasoning": score.get("reasoning", [])
                })

            # Sort by total score
            agent_scores.sort(key=lambda x: x["score"]["total"], reverse=True)
            
            if agent_scores:
                best_match = agent_scores[0]
                return {
                    "agent_id": str(best_match["agent"].id),
                    "agent_name": f"{best_match['agent'].first_name} {best_match['agent'].last_name}",
                    "agent_email": best_match["agent"].email,
                    "score": best_match["score"],
                    "reasoning": best_match["reasoning"],
                    "alternatives": [
                        {
                            "agent_id": str(alt["agent"].id),
                            "agent_name": f"{alt['agent'].first_name} {alt['agent'].last_name}",
                            "score": alt["score"]["total"]
                        }
                        for alt in agent_scores[1:3]  # Top 2 alternatives
                    ]
                }

            return None

        except Exception as e:
            logger.error(f"Error finding best agent: {e}")
            return None

    async def _get_available_agents(
        self,
        db: Session,
        organization_id: str,
        ticket_type: TicketType,
        exclude_agents: Optional[List[str]] = None
    ) -> List[Employee]:
        """Get available agents for the ticket type"""
        try:
            # Base query for active employees
            query = db.query(Employee).filter(
                Employee.organization_id == organization_id,
                Employee.is_active == True
            )

            # Exclude specific agents if provided
            if exclude_agents:
                query = query.filter(~Employee.id.in_(exclude_agents))

            # Filter by expertise/role if available
            if ticket_type in self.expertise_mapping:
                expertise_roles = self.expertise_mapping[ticket_type]
                # This would be enhanced with actual role/skill matching
                # For now, we'll get all active employees
                pass

            agents = query.all()
            
            # Filter out agents who are currently unavailable
            available_agents = []
            for agent in agents:
                if await self._is_agent_available(db, agent):
                    available_agents.append(agent)

            return available_agents

        except Exception as e:
            logger.error(f"Error getting available agents: {e}")
            return []

    async def _is_agent_available(self, db: Session, agent: Employee) -> bool:
        """Check if agent is currently available"""
        try:
            # Check current workload
            current_tickets = db.query(Ticket).filter(
                Ticket.assigned_to == agent.id,
                Ticket.status.in_([TicketStatus.OPEN, TicketStatus.IN_PROGRESS, TicketStatus.PENDING])
            ).count()

            # Define workload thresholds
            max_tickets = 15  # Maximum tickets per agent
            
            if current_tickets >= max_tickets:
                return False

            # Check if agent is on leave (this would integrate with leave management)
            # For now, assume all agents are available
            
            # Check working hours (simplified)
            current_hour = datetime.utcnow().hour
            if 9 <= current_hour <= 17:  # Business hours
                return True
            
            # Outside business hours, only available for critical tickets
            return False

        except Exception as e:
            logger.error(f"Error checking agent availability: {e}")
            return True

    async def _calculate_agent_score(
        self,
        db: Session,
        agent: Employee,
        ticket: Ticket
    ) -> Dict[str, Any]:
        """Calculate comprehensive score for agent-ticket matching"""
        try:
            scores = {
                "expertise": 0.0,
                "workload": 0.0,
                "performance": 0.0,
                "availability": 0.0,
                "skill_match": 0.0
            }
            reasoning = []

            # 1. Expertise Score (30% weight)
            expertise_score = await self._calculate_expertise_score(agent, ticket)
            scores["expertise"] = expertise_score
            if expertise_score > 0.7:
                reasoning.append(f"High expertise match for {ticket.ticket_type.value}")

            # 2. Workload Score (25% weight)
            workload_score = await self._calculate_workload_score(db, agent)
            scores["workload"] = workload_score
            if workload_score > 0.8:
                reasoning.append("Low current workload")
            elif workload_score < 0.3:
                reasoning.append("High current workload")

            # 3. Performance Score (25% weight)
            performance_score = await self._calculate_performance_score(db, agent)
            scores["performance"] = performance_score
            if performance_score > 0.8:
                reasoning.append("Excellent historical performance")

            # 4. Availability Score (10% weight)
            availability_score = await self._calculate_availability_score(agent)
            scores["availability"] = availability_score

            # 5. Skill Match Score (10% weight)
            skill_score = await self._calculate_skill_match_score(agent, ticket)
            scores["skill_match"] = skill_score
            if skill_score > 0.7:
                reasoning.append("Strong skill match for ticket requirements")

            # Calculate weighted total score
            weights = {
                "expertise": 0.30,
                "workload": 0.25,
                "performance": 0.25,
                "availability": 0.10,
                "skill_match": 0.10
            }

            total_score = sum(scores[key] * weights[key] for key in scores)

            return {
                "total": round(total_score, 3),
                "breakdown": scores,
                "reasoning": reasoning
            }

        except Exception as e:
            logger.error(f"Error calculating agent score: {e}")
            return {"total": 0.0, "breakdown": {}, "reasoning": []}

    async def _calculate_expertise_score(self, agent: Employee, ticket: Ticket) -> float:
        """Calculate expertise match score"""
        try:
            # This would be enhanced with actual skill/expertise data
            # For now, use department and role matching
            
            if ticket.ticket_type in self.expertise_mapping:
                required_roles = self.expertise_mapping[ticket.ticket_type]
                
                # Check if agent's role/department matches
                agent_role = getattr(agent, 'role', '') or ''
                agent_dept = getattr(agent, 'department', '') or ''
                
                for role in required_roles:
                    if role.lower() in agent_role.lower() or role.lower() in agent_dept.lower():
                        return 0.9
                
                # Partial match based on department
                if ticket.ticket_type == TicketType.IT_SUPPORT and 'it' in agent_dept.lower():
                    return 0.8
                elif ticket.ticket_type == TicketType.HR_QUERY and 'hr' in agent_dept.lower():
                    return 0.8
                elif ticket.ticket_type == TicketType.FACILITIES and 'facilities' in agent_dept.lower():
                    return 0.8

            return 0.5  # Default score

        except Exception as e:
            logger.error(f"Error calculating expertise score: {e}")
            return 0.5

    async def _calculate_workload_score(self, db: Session, agent: Employee) -> float:
        """Calculate workload-based score (lower workload = higher score)"""
        try:
            # Count current active tickets
            active_tickets = db.query(Ticket).filter(
                Ticket.assigned_to == agent.id,
                Ticket.status.in_([TicketStatus.OPEN, TicketStatus.IN_PROGRESS, TicketStatus.PENDING])
            ).count()

            # Calculate score (inverse of workload)
            max_tickets = 15
            if active_tickets >= max_tickets:
                return 0.0
            
            return 1.0 - (active_tickets / max_tickets)

        except Exception as e:
            logger.error(f"Error calculating workload score: {e}")
            return 0.5

    async def _calculate_performance_score(self, db: Session, agent: Employee) -> float:
        """Calculate historical performance score"""
        try:
            # Get tickets resolved by agent in last 30 days
            thirty_days_ago = datetime.utcnow() - timedelta(days=30)
            
            resolved_tickets = db.query(Ticket).filter(
                Ticket.assigned_to == agent.id,
                Ticket.status == TicketStatus.RESOLVED,
                Ticket.resolved_at >= thirty_days_ago
            ).all()

            if not resolved_tickets:
                return 0.6  # Default score for new agents

            # Calculate metrics
            total_tickets = len(resolved_tickets)
            avg_resolution_time = sum([
                (ticket.resolved_at - ticket.created_at).total_seconds() / 3600
                for ticket in resolved_tickets
            ]) / total_tickets

            sla_breaches = sum(1 for ticket in resolved_tickets if ticket.sla_breach)
            sla_compliance = 1.0 - (sla_breaches / total_tickets)

            # Calculate satisfaction score
            satisfaction_scores = [
                ticket.satisfaction_rating for ticket in resolved_tickets
                if ticket.satisfaction_rating is not None
            ]
            avg_satisfaction = sum(satisfaction_scores) / len(satisfaction_scores) if satisfaction_scores else 3.0

            # Combine metrics
            resolution_score = min(1.0, 48 / avg_resolution_time) if avg_resolution_time > 0 else 0.5
            satisfaction_score = (avg_satisfaction - 1) / 4  # Normalize to 0-1
            
            performance_score = (resolution_score * 0.4 + sla_compliance * 0.4 + satisfaction_score * 0.2)
            
            return min(1.0, performance_score)

        except Exception as e:
            logger.error(f"Error calculating performance score: {e}")
            return 0.6

    async def _calculate_availability_score(self, agent: Employee) -> float:
        """Calculate availability score based on current time and schedule"""
        try:
            current_hour = datetime.utcnow().hour
            current_day = datetime.utcnow().weekday()  # 0 = Monday
            
            # Business hours: 9 AM - 5 PM, Monday-Friday
            if current_day < 5 and 9 <= current_hour <= 17:
                return 1.0
            elif current_day < 5 and (8 <= current_hour < 9 or 17 < current_hour <= 18):
                return 0.7  # Extended hours
            else:
                return 0.3  # Outside business hours

        except Exception as e:
            logger.error(f"Error calculating availability score: {e}")
            return 0.5

    async def _calculate_skill_match_score(self, agent: Employee, ticket: Ticket) -> float:
        """Calculate skill match score based on ticket content"""
        try:
            ticket_text = f"{ticket.title} {ticket.description}".lower()
            
            # This would be enhanced with actual agent skill profiles
            # For now, use keyword matching
            
            skill_matches = 0
            total_skills = len(self.skill_keywords)
            
            for skill, keywords in self.skill_keywords.items():
                if any(keyword in ticket_text for keyword in keywords):
                    # Check if agent has this skill (simplified)
                    # In production, this would check agent's skill profile
                    skill_matches += 1
            
            return skill_matches / total_skills if total_skills > 0 else 0.5

        except Exception as e:
            logger.error(f"Error calculating skill match score: {e}")
            return 0.5

    async def get_routing_analytics(
        self,
        db: Session,
        organization_id: str,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None
    ) -> Dict[str, Any]:
        """Get routing analytics and performance metrics"""
        try:
            if not start_date:
                start_date = datetime.utcnow() - timedelta(days=30)
            if not end_date:
                end_date = datetime.utcnow()

            # Get routing statistics
            total_tickets = db.query(Ticket).filter(
                Ticket.organization_id == organization_id,
                Ticket.created_at >= start_date,
                Ticket.created_at <= end_date
            ).count()

            auto_assigned = db.query(Ticket).filter(
                Ticket.organization_id == organization_id,
                Ticket.created_at >= start_date,
                Ticket.created_at <= end_date,
                Ticket.auto_assigned == True
            ).count()

            # Agent workload distribution
            agent_workload = db.query(
                Employee.id,
                Employee.first_name,
                Employee.last_name,
                func.count(Ticket.id).label('ticket_count')
            ).join(Ticket, Ticket.assigned_to == Employee.id).filter(
                Employee.organization_id == organization_id,
                Ticket.created_at >= start_date,
                Ticket.created_at <= end_date
            ).group_by(Employee.id, Employee.first_name, Employee.last_name).all()

            return {
                "period": {
                    "start_date": start_date.isoformat(),
                    "end_date": end_date.isoformat()
                },
                "summary": {
                    "total_tickets": total_tickets,
                    "auto_assigned": auto_assigned,
                    "auto_assignment_rate": round((auto_assigned / total_tickets * 100), 2) if total_tickets > 0 else 0
                },
                "agent_workload": [
                    {
                        "agent_id": str(agent.id),
                        "agent_name": f"{agent.first_name} {agent.last_name}",
                        "ticket_count": agent.ticket_count
                    }
                    for agent in agent_workload
                ]
            }

        except Exception as e:
            logger.error(f"Error getting routing analytics: {e}")
            return {"error": str(e)}

    async def suggest_reassignment(
        self,
        db: Session,
        ticket_id: str,
        reason: str = "performance"
    ) -> Optional[Dict[str, Any]]:
        """Suggest ticket reassignment based on various factors"""
        try:
            ticket = db.query(Ticket).filter(Ticket.id == ticket_id).first()
            if not ticket:
                return None

            # Find alternative agents
            exclude_current = [str(ticket.assigned_to)] if ticket.assigned_to else []
            
            best_agent = await self.find_best_agent(
                db, ticket, str(ticket.organization_id), exclude_current
            )

            if best_agent:
                return {
                    "current_agent": str(ticket.assigned_to) if ticket.assigned_to else None,
                    "suggested_agent": best_agent,
                    "reason": reason,
                    "confidence": best_agent["score"]["total"]
                }

            return None

        except Exception as e:
            logger.error(f"Error suggesting reassignment: {e}")
            return None
