from pydantic import BaseModel, Field, validator
from typing import Optional, List
from uuid import UUID
from datetime import datetime, date
from enum import Enum


class DelegationStatus(str, Enum):
    PENDING = "pending"
    ACTIVE = "active"
    EXPIRED = "expired"
    REVOKED = "revoked"
    COMPLETED = "completed"


class DelegationType(str, Enum):
    APPROVAL_AUTHORITY = "approval_authority"
    TASK_RESPONSIBILITY = "task_responsibility"
    PROJECT_MANAGEMENT = "project_management"
    TEAM_LEADERSHIP = "team_leadership"
    BUDGET_APPROVAL = "budget_approval"
    LEAVE_APPROVAL = "leave_approval"
    TIMESHEET_APPROVAL = "timesheet_approval"
    EXPENSE_APPROVAL = "expense_approval"
    CUSTOM = "custom"


# Delegation Schemas
class DelegationBase(BaseModel):
    delegation_type: DelegationType
    title: str = Field(..., min_length=1, max_length=200)
    description: Optional[str] = None
    start_date: date
    end_date: date
    is_emergency: bool = False
    reason: str = Field(..., min_length=1, max_length=500)
    specific_permissions: Optional[List[str]] = None
    scope_limitations: Optional[str] = None
    notification_required: bool = True

    @validator('end_date')
    def end_date_must_be_after_start_date(cls, v, values):
        if 'start_date' in values and v <= values['start_date']:
            raise ValueError('End date must be after start date')
        return v


class DelegationCreate(DelegationBase):
    delegate_to: UUID
    auto_revert: bool = True
    requires_approval: bool = True
    approver_id: Optional[UUID] = None


class DelegationUpdate(BaseModel):
    title: Optional[str] = Field(None, min_length=1, max_length=200)
    description: Optional[str] = None
    end_date: Optional[date] = None
    specific_permissions: Optional[List[str]] = None
    scope_limitations: Optional[str] = None
    notification_required: Optional[bool] = None
    auto_revert: Optional[bool] = None


class DelegationResponse(DelegationBase):
    id: UUID
    delegator_id: UUID
    delegate_to: UUID
    organization_id: UUID
    status: DelegationStatus
    approved_by: Optional[UUID] = None
    approved_at: Optional[datetime] = None
    activated_at: Optional[datetime] = None
    revoked_by: Optional[UUID] = None
    revoked_at: Optional[datetime] = None
    revocation_reason: Optional[str] = None
    auto_revert: bool
    requires_approval: bool
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class DelegationListResponse(BaseModel):
    delegations: List[DelegationResponse]
    total: int
    skip: int
    limit: int


# Delegation Activity Schemas
class DelegationActivityBase(BaseModel):
    activity_type: str = Field(..., max_length=50)
    description: str = Field(..., min_length=1)
    metadata: Optional[dict] = None


class DelegationActivityCreate(DelegationActivityBase):
    pass


class DelegationActivityResponse(DelegationActivityBase):
    id: UUID
    delegation_id: UUID
    performed_by: UUID
    performed_at: datetime
    created_at: datetime

    class Config:
        from_attributes = True


# Approval Delegation Schemas
class ApprovalDelegationBase(BaseModel):
    approval_type: str = Field(..., max_length=100)
    workflow_id: Optional[UUID] = None
    approval_level: int = Field(1, ge=1)
    max_amount: Optional[float] = Field(None, ge=0)
    department_scope: Optional[List[UUID]] = None
    employee_scope: Optional[List[UUID]] = None


class ApprovalDelegationCreate(ApprovalDelegationBase):
    delegation_id: UUID


class ApprovalDelegationUpdate(BaseModel):
    approval_level: Optional[int] = Field(None, ge=1)
    max_amount: Optional[float] = Field(None, ge=0)
    department_scope: Optional[List[UUID]] = None
    employee_scope: Optional[List[UUID]] = None


class ApprovalDelegationResponse(ApprovalDelegationBase):
    id: UUID
    delegation_id: UUID
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


# Delegation Approval Schemas
class DelegationApprovalRequest(BaseModel):
    delegation_ids: List[UUID]
    approved: bool
    comments: Optional[str] = Field(None, max_length=1000)


class DelegationApprovalResponse(BaseModel):
    delegation_id: UUID
    approved: bool
    approved_by: UUID
    approved_at: datetime
    comments: Optional[str] = None


# Delegation Revocation Schema
class DelegationRevocationRequest(BaseModel):
    reason: str = Field(..., min_length=1, max_length=500)
    immediate: bool = False
    notify_delegate: bool = True


# Bulk Operations
class BulkDelegationUpdate(BaseModel):
    delegation_ids: List[UUID]
    end_date: Optional[date] = None
    status: Optional[DelegationStatus] = None


# Delegation Summary
class DelegationSummary(BaseModel):
    total_delegations: int
    active_delegations: int
    pending_delegations: int
    expired_delegations: int
    delegations_by_type: dict
    recent_activities: List[DelegationActivityResponse]
