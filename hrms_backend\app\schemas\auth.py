from pydantic import BaseModel, EmailStr
from typing import Optional
from uuid import UUID

class LoginRequest(BaseModel):
    """Login request schema"""
    email: str  # Changed from EmailStr to str to allow employee_id
    password: str

class UserData(BaseModel):
    """User data schema"""
    id: str
    email: str
    name: str
    role: str
    organization_id: Optional[str] = None

class LoginResponse(BaseModel):
    """Login response schema"""
    access_token: str
    token_type: str
    user: UserData
    message: str

class RegisterRequest(BaseModel):
    """Registration request schema"""
    first_name: str
    last_name: str
    email: EmailStr
    password: str
    role: str = "EMPLOYEE"
    organization_id: Optional[UUID] = None

class TokenValidationResponse(BaseModel):
    """Token validation response schema"""
    valid: bool
    user_id: str
    email: str
    role: str
    organization_id: Optional[str] = None

class UserResponse(BaseModel):
    """User response schema"""
    id: str
    email: str
    name: str
    role: str
    organization_id: Optional[str] = None

class PasswordChangeRequest(BaseModel):
    """Password change request schema"""
    current_password: str
    new_password: str

class PasswordResetRequest(BaseModel):
    """Password reset request schema"""
    email: EmailStr

class PasswordResetConfirm(BaseModel):
    """Password reset confirmation schema"""
    token: str
    new_password: str
