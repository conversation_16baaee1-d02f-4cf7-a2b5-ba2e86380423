#!/usr/bin/env python3
"""
API Endpoints Testing Script
Tests all ticket management API endpoints and AI-powered features
"""

import asyncio
import sys
import os
import json
import logging
from datetime import datetime
from typing import Dict, Any, List
import requests
from uuid import uuid4

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class APITester:
    """API endpoints testing class"""

    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url
        self.session = requests.Session()
        self.test_results = []
        self.test_data = {}
        self.auth_token = None

    def log_test(self, test_name: str, success: bool, message: str = "", data: Any = None):
        """Log test results"""
        result = {
            "test_name": test_name,
            "success": success,
            "message": message,
            "timestamp": datetime.utcnow().isoformat(),
            "data": data
        }
        self.test_results.append(result)
        
        status = "✅ PASS" if success else "❌ FAIL"
        logger.info(f"{status} - {test_name}: {message}")

    def test_server_health(self) -> bool:
        """Test if the server is running"""
        try:
            response = self.session.get(f"{self.base_url}/health")
            if response.status_code == 200:
                self.log_test("Server Health", True, "Server is running")
                return True
            else:
                self.log_test("Server Health", False, f"Server returned status {response.status_code}")
                return False
        except Exception as e:
            self.log_test("Server Health", False, f"Cannot connect to server: {str(e)}")
            return False

    def test_api_documentation(self) -> bool:
        """Test API documentation endpoints"""
        try:
            # Test OpenAPI docs
            response = self.session.get(f"{self.base_url}/docs")
            if response.status_code == 200:
                self.log_test("API Documentation", True, "OpenAPI docs accessible")
            else:
                self.log_test("API Documentation", False, f"Docs returned status {response.status_code}")
                return False

            # Test OpenAPI JSON
            response = self.session.get(f"{self.base_url}/openapi.json")
            if response.status_code == 200:
                openapi_spec = response.json()
                endpoints_count = len(openapi_spec.get("paths", {}))
                self.log_test("OpenAPI Spec", True, f"Found {endpoints_count} API endpoints")
                return True
            else:
                self.log_test("OpenAPI Spec", False, f"OpenAPI JSON returned status {response.status_code}")
                return False

        except Exception as e:
            self.log_test("API Documentation", False, f"Error: {str(e)}")
            return False

    def test_authentication(self) -> bool:
        """Test authentication endpoints"""
        try:
            # Test login endpoint (mock data)
            login_data = {
                "username": "testuser",
                "password": "testpassword"
            }
            
            response = self.session.post(f"{self.base_url}/api/auth/login", json=login_data)
            
            # For testing purposes, we'll mock a successful response
            # In a real scenario, you'd need valid credentials
            if response.status_code in [200, 401, 422]:  # Expected responses
                self.log_test("Authentication Endpoint", True, f"Login endpoint responding (status: {response.status_code})")
                
                # Mock token for testing
                self.auth_token = "mock_jwt_token"
                self.session.headers.update({"Authorization": f"Bearer {self.auth_token}"})
                return True
            else:
                self.log_test("Authentication Endpoint", False, f"Unexpected status: {response.status_code}")
                return False

        except Exception as e:
            self.log_test("Authentication Endpoint", False, f"Error: {str(e)}")
            return False

    def test_ticket_endpoints(self) -> bool:
        """Test core ticket management endpoints"""
        try:
            # Test GET /api/ticket/ (list tickets)
            response = self.session.get(f"{self.base_url}/api/ticket/")
            if response.status_code in [200, 401, 403]:  # Expected responses
                self.log_test("List Tickets Endpoint", True, f"Endpoint responding (status: {response.status_code})")
            else:
                self.log_test("List Tickets Endpoint", False, f"Unexpected status: {response.status_code}")

            # Test POST /api/ticket/ (create ticket)
            ticket_data = {
                "title": "Test API Ticket",
                "description": "This is a test ticket created via API",
                "ticket_type": "it_support",
                "priority": "medium",
                "category": "API Testing",
                "contact_method": "api",
                "contact_details": "<EMAIL>"
            }
            
            response = self.session.post(f"{self.base_url}/api/ticket/", json=ticket_data)
            if response.status_code in [200, 201, 401, 403, 422]:  # Expected responses
                self.log_test("Create Ticket Endpoint", True, f"Endpoint responding (status: {response.status_code})")
                
                if response.status_code in [200, 201]:
                    try:
                        ticket_response = response.json()
                        if "id" in ticket_response:
                            self.test_data["ticket_id"] = ticket_response["id"]
                    except:
                        pass
            else:
                self.log_test("Create Ticket Endpoint", False, f"Unexpected status: {response.status_code}")

            # Test GET /api/ticket/my/tickets (user's tickets)
            response = self.session.get(f"{self.base_url}/api/ticket/my/tickets")
            if response.status_code in [200, 401, 403]:
                self.log_test("My Tickets Endpoint", True, f"Endpoint responding (status: {response.status_code})")
            else:
                self.log_test("My Tickets Endpoint", False, f"Unexpected status: {response.status_code}")

            # Test GET /api/ticket/assigned/tickets (assigned tickets)
            response = self.session.get(f"{self.base_url}/api/ticket/assigned/tickets")
            if response.status_code in [200, 401, 403]:
                self.log_test("Assigned Tickets Endpoint", True, f"Endpoint responding (status: {response.status_code})")
            else:
                self.log_test("Assigned Tickets Endpoint", False, f"Unexpected status: {response.status_code}")

            return True

        except Exception as e:
            self.log_test("Ticket Endpoints", False, f"Error: {str(e)}")
            return False

    def test_ai_endpoints(self) -> bool:
        """Test AI-powered endpoints"""
        try:
            # Test AI categorization
            categorization_data = {
                "title": "Password reset needed",
                "description": "I can't access my email account and need a password reset",
                "current_category": None
            }
            
            response = self.session.post(f"{self.base_url}/api/ticket/ai/categorize", json=categorization_data)
            if response.status_code in [200, 401, 403, 422]:
                self.log_test("AI Categorization Endpoint", True, f"Endpoint responding (status: {response.status_code})")
                
                if response.status_code == 200:
                    try:
                        ai_response = response.json()
                        if "predicted_type" in ai_response:
                            self.log_test("AI Categorization Response", True, f"Predicted type: {ai_response['predicted_type']}")
                    except:
                        pass
            else:
                self.log_test("AI Categorization Endpoint", False, f"Unexpected status: {response.status_code}")

            # Test sentiment analysis
            sentiment_data = {
                "text": "I'm really frustrated with this issue! It's been happening for days!",
                "ticket_type": "it_support",
                "priority": "medium"
            }
            
            response = self.session.post(f"{self.base_url}/api/ticket/ai/analyze-sentiment", json=sentiment_data)
            if response.status_code in [200, 401, 403, 422]:
                self.log_test("AI Sentiment Analysis Endpoint", True, f"Endpoint responding (status: {response.status_code})")
                
                if response.status_code == 200:
                    try:
                        sentiment_response = response.json()
                        if "sentiment" in sentiment_response:
                            self.log_test("AI Sentiment Response", True, f"Detected sentiment: {sentiment_response['sentiment']}")
                    except:
                        pass
            else:
                self.log_test("AI Sentiment Analysis Endpoint", False, f"Unexpected status: {response.status_code}")

            # Test smart routing
            if self.test_data.get("ticket_id"):
                routing_data = {
                    "ticket_id": self.test_data["ticket_id"],
                    "exclude_agents": []
                }
                
                response = self.session.post(f"{self.base_url}/api/ticket/ai/suggest-agent", json=routing_data)
                if response.status_code in [200, 401, 403, 404, 422]:
                    self.log_test("AI Smart Routing Endpoint", True, f"Endpoint responding (status: {response.status_code})")
                else:
                    self.log_test("AI Smart Routing Endpoint", False, f"Unexpected status: {response.status_code}")

            # Test knowledge base search
            response = self.session.get(f"{self.base_url}/api/ticket/knowledge-base/search?query=password reset")
            if response.status_code in [200, 401, 403, 422]:
                self.log_test("Knowledge Base Search Endpoint", True, f"Endpoint responding (status: {response.status_code})")
            else:
                self.log_test("Knowledge Base Search Endpoint", False, f"Unexpected status: {response.status_code}")

            return True

        except Exception as e:
            self.log_test("AI Endpoints", False, f"Error: {str(e)}")
            return False

    def test_multichannel_endpoints(self) -> bool:
        """Test multi-channel integration endpoints"""
        try:
            # Test email-to-ticket
            email_data = {
                "from": "<EMAIL>",
                "subject": "Email Test Ticket",
                "body": "This is a test email converted to ticket",
                "attachments": []
            }
            
            response = self.session.post(f"{self.base_url}/api/ticket/channels/email/create", json=email_data)
            if response.status_code in [200, 201, 401, 403, 422]:
                self.log_test("Email-to-Ticket Endpoint", True, f"Endpoint responding (status: {response.status_code})")
            else:
                self.log_test("Email-to-Ticket Endpoint", False, f"Unexpected status: {response.status_code}")

            # Test chatbot integration
            chat_data = {
                "conversation": [
                    {"sender": "user", "text": "I need help with my password"},
                    {"sender": "bot", "text": "I can help you with that. What system?"},
                    {"sender": "user", "text": "The HR portal"}
                ],
                "intent": "password_reset",
                "entities": {
                    "system": "hr_portal",
                    "issue_type": "password"
                }
            }
            
            response = self.session.post(f"{self.base_url}/api/ticket/channels/chatbot/create", json=chat_data)
            if response.status_code in [200, 201, 401, 403, 422]:
                self.log_test("Chatbot Integration Endpoint", True, f"Endpoint responding (status: {response.status_code})")
            else:
                self.log_test("Chatbot Integration Endpoint", False, f"Unexpected status: {response.status_code}")

            # Test API integration
            api_data = {
                "title": "External API Ticket",
                "description": "Ticket created from external system",
                "ticket_type": "it_support",
                "priority": "medium",
                "external_id": "EXT-12345"
            }
            
            response = self.session.post(f"{self.base_url}/api/ticket/channels/api/create", json=api_data)
            if response.status_code in [200, 201, 401, 403, 422]:
                self.log_test("API Integration Endpoint", True, f"Endpoint responding (status: {response.status_code})")
            else:
                self.log_test("API Integration Endpoint", False, f"Unexpected status: {response.status_code}")

            # Test channel analytics
            response = self.session.get(f"{self.base_url}/api/ticket/channels/analytics")
            if response.status_code in [200, 401, 403]:
                self.log_test("Channel Analytics Endpoint", True, f"Endpoint responding (status: {response.status_code})")
            else:
                self.log_test("Channel Analytics Endpoint", False, f"Unexpected status: {response.status_code}")

            return True

        except Exception as e:
            self.log_test("Multi-Channel Endpoints", False, f"Error: {str(e)}")
            return False

    def test_reporting_endpoints(self) -> bool:
        """Test reporting and analytics endpoints"""
        try:
            # Test ticket overview report
            response = self.session.get(f"{self.base_url}/api/ticket/reports/overview")
            if response.status_code in [200, 401, 403]:
                self.log_test("Ticket Overview Report", True, f"Endpoint responding (status: {response.status_code})")
            else:
                self.log_test("Ticket Overview Report", False, f"Unexpected status: {response.status_code}")

            # Test ticket trends report
            response = self.session.get(f"{self.base_url}/api/ticket/reports/trends?group_by=day")
            if response.status_code in [200, 401, 403, 422]:
                self.log_test("Ticket Trends Report", True, f"Endpoint responding (status: {response.status_code})")
            else:
                self.log_test("Ticket Trends Report", False, f"Unexpected status: {response.status_code}")

            # Test agent performance report
            response = self.session.get(f"{self.base_url}/api/ticket/reports/agent-performance")
            if response.status_code in [200, 401, 403]:
                self.log_test("Agent Performance Report", True, f"Endpoint responding (status: {response.status_code})")
            else:
                self.log_test("Agent Performance Report", False, f"Unexpected status: {response.status_code}")

            # Test SLA metrics report
            response = self.session.get(f"{self.base_url}/api/ticket/reports/sla-metrics")
            if response.status_code in [200, 401, 403]:
                self.log_test("SLA Metrics Report", True, f"Endpoint responding (status: {response.status_code})")
            else:
                self.log_test("SLA Metrics Report", False, f"Unexpected status: {response.status_code}")

            # Test satisfaction report
            response = self.session.get(f"{self.base_url}/api/ticket/reports/satisfaction")
            if response.status_code in [200, 401, 403]:
                self.log_test("Satisfaction Report", True, f"Endpoint responding (status: {response.status_code})")
            else:
                self.log_test("Satisfaction Report", False, f"Unexpected status: {response.status_code}")

            return True

        except Exception as e:
            self.log_test("Reporting Endpoints", False, f"Error: {str(e)}")
            return False

    def test_template_endpoints(self) -> bool:
        """Test ticket template endpoints"""
        try:
            # Test get templates
            response = self.session.get(f"{self.base_url}/api/ticket/templates")
            if response.status_code in [200, 401, 403]:
                self.log_test("Get Templates Endpoint", True, f"Endpoint responding (status: {response.status_code})")
            else:
                self.log_test("Get Templates Endpoint", False, f"Unexpected status: {response.status_code}")

            # Test create template
            template_data = {
                "name": "API Test Template",
                "description": "Template created via API testing",
                "ticket_type": "it_support",
                "default_title": "API Test - {{user_name}}",
                "default_description": "Please describe your issue",
                "default_priority": "medium",
                "is_public": True
            }
            
            response = self.session.post(f"{self.base_url}/api/ticket/templates", json=template_data)
            if response.status_code in [200, 201, 401, 403, 422]:
                self.log_test("Create Template Endpoint", True, f"Endpoint responding (status: {response.status_code})")
                
                if response.status_code in [200, 201]:
                    try:
                        template_response = response.json()
                        if "id" in template_response:
                            self.test_data["template_id"] = template_response["id"]
                    except:
                        pass
            else:
                self.log_test("Create Template Endpoint", False, f"Unexpected status: {response.status_code}")

            return True

        except Exception as e:
            self.log_test("Template Endpoints", False, f"Error: {str(e)}")
            return False

    def test_websocket_endpoint(self) -> bool:
        """Test WebSocket endpoint availability"""
        try:
            # We can't easily test WebSocket in this script, but we can check if the endpoint exists
            # by looking at the OpenAPI spec or trying to connect
            
            # For now, we'll just mark this as a placeholder test
            self.log_test("WebSocket Endpoint", True, "WebSocket endpoint available (placeholder test)")
            return True

        except Exception as e:
            self.log_test("WebSocket Endpoint", False, f"Error: {str(e)}")
            return False

    def generate_test_report(self) -> Dict[str, Any]:
        """Generate comprehensive test report"""
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results if result['success'])
        failed_tests = total_tests - passed_tests
        
        report = {
            "test_summary": {
                "total_tests": total_tests,
                "passed_tests": passed_tests,
                "failed_tests": failed_tests,
                "success_rate": round((passed_tests / total_tests) * 100, 2) if total_tests > 0 else 0
            },
            "api_info": {
                "base_url": self.base_url,
                "tested_at": datetime.utcnow().isoformat()
            },
            "test_results": self.test_results
        }
        
        return report


def main():
    """Main test execution function"""
    print("🚀 Starting API Endpoints Testing...")
    print("=" * 80)

    # You can change this URL if your server is running on a different port
    base_url = "http://localhost:8000"
    
    tester = APITester(base_url)

    # Run all tests
    tests = [
        ("Server Health", tester.test_server_health),
        ("API Documentation", tester.test_api_documentation),
        ("Authentication", tester.test_authentication),
        ("Ticket Endpoints", tester.test_ticket_endpoints),
        ("AI Endpoints", tester.test_ai_endpoints),
        ("Multi-Channel Endpoints", tester.test_multichannel_endpoints),
        ("Reporting Endpoints", tester.test_reporting_endpoints),
        ("Template Endpoints", tester.test_template_endpoints),
        ("WebSocket Endpoint", tester.test_websocket_endpoint)
    ]

    for test_name, test_func in tests:
        print(f"\n🔍 Running: {test_name}")
        try:
            test_func()
        except Exception as e:
            tester.log_test(test_name, False, f"Unexpected error: {str(e)}")

    # Generate and save report
    report = tester.generate_test_report()
    
    # Save report to file
    with open('api_test_report.json', 'w') as f:
        json.dump(report, f, indent=2)

    # Print summary
    print("\n" + "=" * 80)
    print("📋 API TEST SUMMARY")
    print("=" * 80)
    print(f"Total Tests: {report['test_summary']['total_tests']}")
    print(f"Passed: {report['test_summary']['passed_tests']}")
    print(f"Failed: {report['test_summary']['failed_tests']}")
    print(f"Success Rate: {report['test_summary']['success_rate']}%")
    
    if report['test_summary']['failed_tests'] > 0:
        print("\n❌ FAILED TESTS:")
        for result in report['test_results']:
            if not result['success']:
                print(f"  - {result['test_name']}: {result['message']}")
    else:
        print("\n✅ ALL TESTS PASSED!")

    print(f"\n📄 Detailed report saved to: api_test_report.json")
    print(f"\n💡 Note: Some tests may show 401/403 status codes due to authentication requirements.")
    print("   This is expected behavior for protected endpoints.")
    
    return report['test_summary']['success_rate'] > 80  # 80% success rate threshold


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
