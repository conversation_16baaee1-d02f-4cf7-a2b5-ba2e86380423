/**
 * Leave Status Tracker Component
 * Features: Visual workflow progress, status timeline, approval chain
 */

import React from 'react';
import {
  CheckCircle,
  Clock,
  XCircle,
  User,
  Calendar,
  FileText,
  AlertCircle,
  MessageSquare
} from 'lucide-react';

const LeaveStatusTracker = ({ request, onClose }) => {
  const getStatusSteps = () => {
    const baseSteps = [
      {
        id: 'submitted',
        title: 'Application Submitted',
        description: 'Leave request has been submitted',
        icon: FileText,
        status: 'completed',
        timestamp: request.applied_at
      },
      {
        id: 'review',
        title: 'Under Review',
        description: 'Manager is reviewing your request',
        icon: Clock,
        status: request.status === 'pending' ? 'current' : 
                request.status === 'approved' || request.status === 'rejected' ? 'completed' : 'pending',
        timestamp: request.status !== 'pending' ? request.approved_at : null
      }
    ];

    if (request.status === 'approved') {
      baseSteps.push({
        id: 'approved',
        title: 'Approved',
        description: `Approved by ${request.approved_by_name || 'Manager'}`,
        icon: CheckCircle,
        status: 'completed',
        timestamp: request.approved_at
      });
    } else if (request.status === 'rejected') {
      baseSteps.push({
        id: 'rejected',
        title: 'Rejected',
        description: request.rejection_reason || 'Request was rejected',
        icon: XCircle,
        status: 'rejected',
        timestamp: request.approved_at
      });
    }

    return baseSteps;
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'completed': return 'text-green-600 bg-green-100';
      case 'current': return 'text-blue-600 bg-blue-100';
      case 'rejected': return 'text-red-600 bg-red-100';
      default: return 'text-gray-400 bg-gray-100';
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'completed': return 'border-green-500 bg-green-500';
      case 'current': return 'border-blue-500 bg-blue-500';
      case 'rejected': return 'border-red-500 bg-red-500';
      default: return 'border-gray-300 bg-white';
    }
  };

  const formatDate = (dateString) => {
    if (!dateString) return '';
    return new Date(dateString).toLocaleString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const steps = getStatusSteps();

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-2xl shadow-2xl w-full max-w-2xl max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="agno-gradient text-white p-6">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-xl font-bold">Leave Request Status</h2>
              <p className="text-blue-100 mt-1">Track your leave request progress</p>
            </div>
            <button
              onClick={onClose}
              className="text-white hover:text-blue-200 transition-colors"
            >
              <XCircle size={24} />
            </button>
          </div>
        </div>

        {/* Request Details */}
        <div className="p-6 border-b border-gray-200">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700">Leave Type</label>
              <p className="mt-1 text-sm text-gray-900">{request.leave_type_name || request.leave_type}</p>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700">Duration</label>
              <p className="mt-1 text-sm text-gray-900">
                {new Date(request.start_date).toLocaleDateString()} - {new Date(request.end_date).toLocaleDateString()}
              </p>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700">Total Days</label>
              <p className="mt-1 text-sm text-gray-900">{request.total_days} days</p>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700">Current Status</label>
              <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                request.status === 'approved' ? 'bg-green-100 text-green-800' :
                request.status === 'rejected' ? 'bg-red-100 text-red-800' :
                'bg-amber-100 text-amber-800'
              }`}>
                {request.status.charAt(0).toUpperCase() + request.status.slice(1)}
              </span>
            </div>
          </div>

          {request.reason && (
            <div className="mt-4">
              <label className="block text-sm font-medium text-gray-700">Reason</label>
              <p className="mt-1 text-sm text-gray-900 p-3 bg-gray-50 rounded-lg">
                {request.reason}
              </p>
            </div>
          )}
        </div>

        {/* Status Timeline */}
        <div className="p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-6">Request Timeline</h3>
          
          <div className="flow-root">
            <ul className="-mb-8">
              {steps.map((step, stepIdx) => {
                const Icon = step.icon;
                return (
                  <li key={step.id}>
                    <div className="relative pb-8">
                      {stepIdx !== steps.length - 1 ? (
                        <span
                          className={`absolute top-4 left-4 -ml-px h-full w-0.5 ${
                            step.status === 'completed' || step.status === 'rejected' 
                              ? 'bg-green-500' 
                              : 'bg-gray-300'
                          }`}
                          aria-hidden="true"
                        />
                      ) : null}
                      
                      <div className="relative flex space-x-3">
                        <div>
                          <span className={`h-8 w-8 rounded-full flex items-center justify-center ring-8 ring-white ${getStatusIcon(step.status)}`}>
                            <Icon className="w-4 h-4 text-white" aria-hidden="true" />
                          </span>
                        </div>
                        
                        <div className="min-w-0 flex-1 pt-1.5 flex justify-between space-x-4">
                          <div>
                            <p className="text-sm font-medium text-gray-900">
                              {step.title}
                            </p>
                            <p className="text-sm text-gray-500">
                              {step.description}
                            </p>
                          </div>
                          
                          {step.timestamp && (
                            <div className="text-right text-sm whitespace-nowrap text-gray-500">
                              {formatDate(step.timestamp)}
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  </li>
                );
              })}
            </ul>
          </div>
        </div>

        {/* Additional Information */}
        {(request.contact_number || request.emergency_contact || request.handover_notes) && (
          <div className="px-6 pb-6 border-t border-gray-200">
            <h4 className="text-md font-semibold text-gray-900 mb-4 mt-6">Additional Information</h4>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {request.contact_number && (
                <div>
                  <label className="block text-sm font-medium text-gray-700">Contact Number</label>
                  <p className="mt-1 text-sm text-gray-900">{request.contact_number}</p>
                </div>
              )}
              
              {request.emergency_contact && (
                <div>
                  <label className="block text-sm font-medium text-gray-700">Emergency Contact</label>
                  <p className="mt-1 text-sm text-gray-900">{request.emergency_contact}</p>
                </div>
              )}
            </div>

            {request.handover_notes && (
              <div className="mt-4">
                <label className="block text-sm font-medium text-gray-700">Handover Notes</label>
                <p className="mt-1 text-sm text-gray-900 p-3 bg-gray-50 rounded-lg">
                  {request.handover_notes}
                </p>
              </div>
            )}
          </div>
        )}

        {/* Actions */}
        <div className="px-6 py-4 bg-gray-50 flex justify-between items-center">
          <div className="flex items-center space-x-4">
            {request.status === 'pending' && (
              <div className="flex items-center text-sm text-amber-600">
                <Clock size={16} className="mr-2" />
                <span>Awaiting approval</span>
              </div>
            )}
            
            {request.status === 'approved' && (
              <div className="flex items-center text-sm text-green-600">
                <CheckCircle size={16} className="mr-2" />
                <span>Request approved</span>
              </div>
            )}
            
            {request.status === 'rejected' && (
              <div className="flex items-center text-sm text-red-600">
                <XCircle size={16} className="mr-2" />
                <span>Request rejected</span>
              </div>
            )}
          </div>
          
          <div className="flex space-x-3">
            {request.status === 'pending' && (
              <button className="px-4 py-2 text-sm border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50">
                Withdraw Request
              </button>
            )}
            
            <button
              onClick={onClose}
              className="px-4 py-2 text-sm agno-bg-primary text-white rounded-lg hover:bg-blue-700"
            >
              Close
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default LeaveStatusTracker;
