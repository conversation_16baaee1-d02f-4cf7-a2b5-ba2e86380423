from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, func
from typing import Optional, List, Dict, Any
from uuid import UUID
from datetime import datetime, date
from fastapi import HTTPException, status
import logging

from ...db.models.engagement import (
    Survey, SurveyQuestion, SurveyResponse, SurveyAnswer, FeedbackRequest, FeedbackResponse
)
from ...db.models.employee import Employee
from ...schemas.engagement import (
    SurveyCreate, SurveyUpdate, SurveyResponse as SurveyResponseSchema,
    SurveyListResponse, QuestionCreate, QuestionUpdate, QuestionResponse,
    SurveyResponseCreate, SurveyResponseUpdate, SurveyResponseResponse,
    AnswerCreate, AnswerUpdate, AnswerResponse, FeedbackCreate,
    FeedbackUpdate, FeedbackResponse, FeedbackListResponse,
    RecognitionCreate, RecognitionUpdate, RecognitionResponse,
    RecognitionListResponse, BadgeCreate, BadgeUpdate, BadgeResponse,
    SurveyAnalytics, EngagementMetrics, EngagementDashboard,
    BulkSurveyInvite, BulkRecognition, SurveyStatus, SurveyType,
    QuestionType, ResponseStatus
)
from ...core.security import CurrentUser
from ...core.websocket_manager import notification_manager

logger = logging.getLogger(__name__)


class EngagementService:
    """Engagement service for business logic"""

    async def create_survey(
        self,
        db: Session,
        survey_data: SurveyCreate,
        current_user: CurrentUser
    ) -> SurveyResponseSchema:
        """Create new survey"""
        try:
            # Create survey
            survey = Survey(
                **survey_data.dict(exclude={'target_departments', 'target_employees', 'exclude_employees'}),
                organization_id=current_user.organization_id,
                created_by=current_user.user_id,
                status=SurveyStatus.DRAFT,
                total_questions=0,
                total_responses=0,
                completion_rate=0.0,
                target_departments=survey_data.target_departments or [],
                target_employees=survey_data.target_employees or [],
                exclude_employees=survey_data.exclude_employees or []
            )

            db.add(survey)
            db.commit()
            db.refresh(survey)

            logger.info(f"Survey {survey.title} created by {current_user.user_id}")
            return SurveyResponseSchema.from_orm(survey)

        except Exception as e:
            db.rollback()
            logger.error(f"Error creating survey: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Error creating survey"
            )

    async def get_surveys(
        self,
        db: Session,
        survey_type: Optional[SurveyType] = None,
        status: Optional[SurveyStatus] = None,
        search: Optional[str] = None,
        skip: int = 0,
        limit: int = 20,
        current_user: CurrentUser = None
    ) -> SurveyListResponse:
        """Get surveys with filtering"""
        try:
            query = db.query(Survey).filter(
                Survey.organization_id == current_user.organization_id
            )

            # Apply filters
            if survey_type:
                query = query.filter(Survey.survey_type == survey_type)

            if status:
                query = query.filter(Survey.status == status)

            if search:
                search_filter = or_(
                    Survey.title.ilike(f"%{search}%"),
                    Survey.description.ilike(f"%{search}%")
                )
                query = query.filter(search_filter)

            # Get total count
            total = query.count()

            # Apply pagination and ordering
            surveys = query.order_by(
                Survey.created_at.desc()
            ).offset(skip).limit(limit).all()

            # Convert to response format
            survey_responses = [SurveyResponseSchema.from_orm(survey) for survey in surveys]

            return SurveyListResponse(
                surveys=survey_responses,
                total=total,
                skip=skip,
                limit=limit
            )

        except Exception as e:
            logger.error(f"Error getting surveys: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Error retrieving surveys"
            )

    async def create_recognition(
        self,
        db: Session,
        recognition_data: RecognitionCreate,
        current_user: CurrentUser
    ) -> RecognitionResponse:
        """Create employee recognition"""
        try:
            # Verify recipient exists
            recipient = db.query(Employee).filter(
                Employee.id == recognition_data.recipient_id,
                Employee.organization_id == current_user.organization_id,
                Employee.is_active == True
            ).first()

            if not recipient:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Recipient not found"
                )

            # Verify badge exists if provided
            if recognition_data.badge_id:
                badge = db.query(Badge).filter(
                    Badge.id == recognition_data.badge_id,
                    Badge.organization_id == current_user.organization_id,
                    Badge.is_active == True
                ).first()

                if not badge:
                    raise HTTPException(
                        status_code=status.HTTP_404_NOT_FOUND,
                        detail="Badge not found"
                    )

            # Create recognition
            recognition = Recognition(
                **recognition_data.dict(),
                organization_id=current_user.organization_id,
                giver_id=current_user.user_id,
                created_by=current_user.user_id
            )

            db.add(recognition)
            db.commit()
            db.refresh(recognition)

            # Send notification to recipient
            await notification_manager.notify_recognition_received(
                str(recognition.recipient_id),
                {
                    "recognition_id": str(recognition.id),
                    "title": recognition.title,
                    "message": recognition.message,
                    "points": recognition.points_awarded,
                    "giver": current_user.email
                }
            )

            logger.info(f"Recognition created for {recognition.recipient_id} by {current_user.user_id}")
            return RecognitionResponse.from_orm(recognition)

        except HTTPException:
            raise
        except Exception as e:
            db.rollback()
            logger.error(f"Error creating recognition: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Error creating recognition"
            )

    async def create_feedback(
        self,
        db: Session,
        feedback_data: FeedbackCreate,
        current_user: CurrentUser
    ) -> FeedbackResponse:
        """Create feedback"""
        try:
            # Verify recipient exists if provided
            if feedback_data.recipient_id:
                recipient = db.query(Employee).filter(
                    Employee.id == feedback_data.recipient_id,
                    Employee.organization_id == current_user.organization_id,
                    Employee.is_active == True
                ).first()

                if not recipient:
                    raise HTTPException(
                        status_code=status.HTTP_404_NOT_FOUND,
                        detail="Recipient not found"
                    )

            # Create feedback
            feedback = Feedback(
                **feedback_data.dict(exclude={'attachment_urls'}),
                organization_id=current_user.organization_id,
                submitter_id=None if feedback_data.is_anonymous else current_user.user_id,
                status="open",
                attachment_urls=feedback_data.attachment_urls or [],
                created_by=current_user.user_id
            )

            db.add(feedback)
            db.commit()
            db.refresh(feedback)

            # Send notification if not anonymous and has recipient
            if not feedback_data.is_anonymous and feedback_data.recipient_id:
                await notification_manager.notify_feedback_received(
                    str(feedback_data.recipient_id),
                    {
                        "feedback_id": str(feedback.id),
                        "title": feedback.title,
                        "category": feedback.category,
                        "submitter": current_user.email
                    }
                )

            logger.info(f"Feedback created by {current_user.user_id}")
            return FeedbackResponse.from_orm(feedback)

        except HTTPException:
            raise
        except Exception as e:
            db.rollback()
            logger.error(f"Error creating feedback: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Error creating feedback"
            )

    async def get_engagement_dashboard(
        self,
        db: Session,
        current_user: CurrentUser
    ) -> EngagementDashboard:
        """Get engagement dashboard data"""
        try:
            # Organization metrics
            total_employees = db.query(Employee).filter(
                Employee.organization_id == current_user.organization_id,
                Employee.is_active == True
            ).count()

            total_surveys = db.query(Survey).filter(
                Survey.organization_id == current_user.organization_id
            ).count()

            active_surveys = db.query(Survey).filter(
                Survey.organization_id == current_user.organization_id,
                Survey.status == SurveyStatus.ACTIVE
            ).all()

            total_recognitions = db.query(Recognition).filter(
                Recognition.organization_id == current_user.organization_id
            ).count()

            total_feedback = db.query(Feedback).filter(
                Feedback.organization_id == current_user.organization_id
            ).count()

            # Recent recognitions
            recent_recognitions = db.query(Recognition).filter(
                Recognition.organization_id == current_user.organization_id,
                Recognition.is_public == True
            ).order_by(Recognition.created_at.desc()).limit(10).all()

            # Trending feedback
            trending_feedback = db.query(Feedback).filter(
                Feedback.organization_id == current_user.organization_id,
                Feedback.status == "open"
            ).order_by(Feedback.created_at.desc()).limit(5).all()

            organization_metrics = {
                "total_employees": total_employees,
                "total_surveys": total_surveys,
                "active_surveys_count": len(active_surveys),
                "total_recognitions": total_recognitions,
                "total_feedback": total_feedback,
                "engagement_score": 75.5,  # This would be calculated from survey data
                "satisfaction_score": 82.3  # This would be calculated from survey data
            }

            return EngagementDashboard(
                organization_metrics=organization_metrics,
                department_metrics=[],  # Would be calculated per department
                recent_recognitions=[RecognitionResponse.from_orm(r) for r in recent_recognitions],
                active_surveys=[SurveyResponseSchema.from_orm(s) for s in active_surveys],
                trending_feedback=[FeedbackResponse.from_orm(f) for f in trending_feedback],
                engagement_trends={}  # Would contain historical trend data
            )

        except Exception as e:
            logger.error(f"Error getting engagement dashboard: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Error retrieving engagement dashboard"
            )

    async def get_survey_analytics(
        self,
        db: Session,
        survey_id: UUID,
        current_user: CurrentUser
    ) -> Optional[SurveyAnalytics]:
        """Get survey analytics"""
        try:
            survey = db.query(Survey).filter(
                Survey.id == survey_id,
                Survey.organization_id == current_user.organization_id
            ).first()

            if not survey:
                return None

            # Get survey responses
            responses = db.query(SurveyResponse).filter(
                SurveyResponse.survey_id == survey_id
            ).all()

            completed_responses = [r for r in responses if r.status == ResponseStatus.COMPLETED]

            # Calculate metrics
            total_invited = len(survey.target_employees) if survey.target_employees else 0
            total_responses = len(responses)
            completion_rate = (len(completed_responses) / total_invited * 100) if total_invited > 0 else 0

            # Calculate average completion time
            completion_times = [
                (r.completed_at - r.started_at).total_seconds() / 60
                for r in completed_responses
                if r.completed_at and r.started_at
            ]
            avg_completion_time = sum(completion_times) / len(completion_times) if completion_times else 0

            return SurveyAnalytics(
                survey_id=survey_id,
                total_invited=total_invited,
                total_responses=total_responses,
                completion_rate=completion_rate,
                average_completion_time_minutes=avg_completion_time,
                response_rate_by_department={},  # Would be calculated per department
                question_analytics=[],  # Would contain per-question analytics
                sentiment_analysis=None  # Would contain sentiment analysis if available
            )

        except Exception as e:
            logger.error(f"Error getting survey analytics: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Error retrieving survey analytics"
            )
