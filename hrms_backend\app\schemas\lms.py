from pydantic import BaseModel, Field, validator
from typing import Optional, List, Dict, Any
from datetime import datetime, date
from uuid import UUID
from decimal import Decimal

from ..db.models.lms import CourseStatus, CourseType, EnrollmentStatus, AssessmentType, CertificationStatus


class CourseCreate(BaseModel):
    """Course creation schema"""
    title: str = Field(..., max_length=200, description="Course title")
    description: str = Field(..., description="Course description")
    short_description: Optional[str] = Field(None, max_length=500, description="Short description")
    course_type: CourseType = Field(..., description="Course type")
    category: Optional[str] = Field(None, max_length=100, description="Course category")
    duration_hours: Optional[int] = Field(None, ge=1, description="Duration in hours")
    difficulty_level: Optional[str] = Field(None, description="Difficulty level")
    prerequisites: Optional[List[UUID]] = Field(None, description="Prerequisite course IDs")
    learning_objectives: Optional[List[str]] = Field(None, description="Learning objectives")
    thumbnail_url: Optional[str] = Field(None, max_length=500, description="Thumbnail URL")
    video_url: Optional[str] = Field(None, max_length=500, description="Video URL")
    materials: Optional[List[Dict[str, Any]]] = Field(None, description="Course materials")
    instructor_id: Optional[UUID] = Field(None, description="Instructor employee ID")
    instructor_name: Optional[str] = Field(None, max_length=200, description="External instructor name")
    instructor_bio: Optional[str] = Field(None, description="Instructor bio")
    max_enrollments: Optional[int] = Field(None, ge=1, description="Maximum enrollments")
    completion_criteria: Optional[Dict[str, Any]] = Field(None, description="Completion criteria")
    start_date: Optional[date] = Field(None, description="Course start date")
    end_date: Optional[date] = Field(None, description="Course end date")
    enrollment_deadline: Optional[date] = Field(None, description="Enrollment deadline")
    provides_certificate: bool = Field(default=False, description="Provides certificate")
    certificate_template_url: Optional[str] = Field(None, max_length=500, description="Certificate template URL")
    certificate_validity_months: Optional[int] = Field(None, ge=1, description="Certificate validity in months")
    is_free: bool = Field(default=True, description="Is course free")
    price: Optional[Decimal] = Field(None, description="Course price")
    currency: str = Field(default="USD", max_length=3, description="Currency code")


class CourseUpdate(BaseModel):
    """Course update schema"""
    title: Optional[str] = Field(None, max_length=200)
    description: Optional[str] = Field(None)
    short_description: Optional[str] = Field(None, max_length=500)
    course_type: Optional[CourseType] = Field(None)
    category: Optional[str] = Field(None, max_length=100)
    status: Optional[CourseStatus] = Field(None)
    duration_hours: Optional[int] = Field(None, ge=1)
    difficulty_level: Optional[str] = Field(None)
    prerequisites: Optional[List[UUID]] = Field(None)
    learning_objectives: Optional[List[str]] = Field(None)
    thumbnail_url: Optional[str] = Field(None, max_length=500)
    video_url: Optional[str] = Field(None, max_length=500)
    materials: Optional[List[Dict[str, Any]]] = Field(None)
    instructor_id: Optional[UUID] = Field(None)
    instructor_name: Optional[str] = Field(None, max_length=200)
    instructor_bio: Optional[str] = Field(None)
    max_enrollments: Optional[int] = Field(None, ge=1)
    completion_criteria: Optional[Dict[str, Any]] = Field(None)
    start_date: Optional[date] = Field(None)
    end_date: Optional[date] = Field(None)
    enrollment_deadline: Optional[date] = Field(None)
    provides_certificate: Optional[bool] = Field(None)
    certificate_template_url: Optional[str] = Field(None, max_length=500)
    certificate_validity_months: Optional[int] = Field(None, ge=1)
    is_free: Optional[bool] = Field(None)
    price: Optional[Decimal] = Field(None)


class CourseResponse(BaseModel):
    """Course response schema"""
    id: UUID
    title: str
    description: str
    short_description: Optional[str]
    course_type: CourseType
    status: CourseStatus
    category: Optional[str]
    duration_hours: Optional[int]
    difficulty_level: Optional[str]
    prerequisites: Optional[List[UUID]]
    learning_objectives: Optional[List[str]]
    thumbnail_url: Optional[str]
    video_url: Optional[str]
    materials: Optional[List[Dict[str, Any]]]
    instructor_id: Optional[UUID]
    instructor_name: Optional[str]
    instructor_bio: Optional[str]
    max_enrollments: Optional[int]
    current_enrollments: int
    completion_criteria: Optional[Dict[str, Any]]
    start_date: Optional[date]
    end_date: Optional[date]
    enrollment_deadline: Optional[date]
    provides_certificate: bool
    certificate_template_url: Optional[str]
    certificate_validity_months: Optional[int]
    is_free: bool
    price: Optional[Decimal]
    currency: str
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class CourseModuleCreate(BaseModel):
    """Course module creation schema"""
    course_id: UUID = Field(..., description="Course ID")
    title: str = Field(..., max_length=200, description="Module title")
    description: Optional[str] = Field(None, description="Module description")
    order_index: int = Field(..., ge=0, description="Order within course")
    content_type: str = Field(..., max_length=50, description="Content type")
    content_url: Optional[str] = Field(None, max_length=500, description="Content URL")
    content_text: Optional[str] = Field(None, description="Text content")
    duration_minutes: Optional[int] = Field(None, ge=1, description="Duration in minutes")
    is_mandatory: bool = Field(default=True, description="Is module mandatory")
    prerequisites: Optional[List[UUID]] = Field(None, description="Prerequisite module IDs")


class CourseModuleResponse(BaseModel):
    """Course module response schema"""
    id: UUID
    course_id: UUID
    title: str
    description: Optional[str]
    order_index: int
    content_type: str
    content_url: Optional[str]
    content_text: Optional[str]
    duration_minutes: Optional[int]
    is_mandatory: bool
    prerequisites: Optional[List[UUID]]
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class CourseEnrollmentCreate(BaseModel):
    """Course enrollment creation schema"""
    course_id: UUID = Field(..., description="Course ID")
    employee_id: UUID = Field(..., description="Employee ID")
    deadline: Optional[date] = Field(None, description="Completion deadline")
    enrollment_type: str = Field(default="self", description="Enrollment type")


class CourseEnrollmentResponse(BaseModel):
    """Course enrollment response schema"""
    id: UUID
    course_id: UUID
    employee_id: UUID
    status: EnrollmentStatus
    enrolled_date: datetime
    start_date: Optional[datetime]
    completion_date: Optional[datetime]
    deadline: Optional[date]
    progress_percentage: int
    modules_completed: int
    total_modules: Optional[int]
    time_spent_minutes: int
    final_score: Optional[Decimal]
    passing_score: Optional[Decimal]
    attempts: int
    max_attempts: Optional[int]
    enrolled_by_id: Optional[UUID]
    enrollment_type: str
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class ModuleProgressUpdate(BaseModel):
    """Module progress update schema"""
    status: str = Field(..., description="Progress status")
    time_spent_minutes: int = Field(default=0, ge=0, description="Time spent in minutes")
    notes: Optional[str] = Field(None, description="Progress notes")
    bookmarks: Optional[List[Dict[str, Any]]] = Field(None, description="Bookmarks")


class AssessmentCreate(BaseModel):
    """Assessment creation schema"""
    course_id: UUID = Field(..., description="Course ID")
    module_id: Optional[UUID] = Field(None, description="Module ID (if module-specific)")
    title: str = Field(..., max_length=200, description="Assessment title")
    description: Optional[str] = Field(None, description="Assessment description")
    assessment_type: AssessmentType = Field(..., description="Assessment type")
    questions: List[Dict[str, Any]] = Field(..., description="Assessment questions")
    time_limit_minutes: Optional[int] = Field(None, ge=1, description="Time limit in minutes")
    max_attempts: int = Field(default=3, ge=1, description="Maximum attempts")
    passing_score: Decimal = Field(default=70.0, ge=0, le=100, description="Passing score percentage")
    available_from: Optional[datetime] = Field(None, description="Available from date")
    available_until: Optional[datetime] = Field(None, description="Available until date")
    auto_grade: bool = Field(default=True, description="Auto-grade assessment")
    show_results_immediately: bool = Field(default=True, description="Show results immediately")


class AssessmentResponse(BaseModel):
    """Assessment response schema"""
    id: UUID
    course_id: UUID
    module_id: Optional[UUID]
    title: str
    description: Optional[str]
    assessment_type: AssessmentType
    questions: List[Dict[str, Any]]
    time_limit_minutes: Optional[int]
    max_attempts: int
    passing_score: Decimal
    is_active: bool
    available_from: Optional[datetime]
    available_until: Optional[datetime]
    auto_grade: bool
    show_results_immediately: bool
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class AssessmentAttemptCreate(BaseModel):
    """Assessment attempt creation schema"""
    assessment_id: UUID = Field(..., description="Assessment ID")
    responses: Dict[str, Any] = Field(..., description="Assessment responses")


class AssessmentAttemptResponse(BaseModel):
    """Assessment attempt response schema"""
    id: UUID
    assessment_id: UUID
    enrollment_id: UUID
    attempt_number: int
    started_date: datetime
    submitted_date: Optional[datetime]
    time_taken_minutes: Optional[int]
    responses: Optional[Dict[str, Any]]
    score: Optional[Decimal]
    passed: Optional[bool]
    status: str
    graded_by_id: Optional[UUID]
    graded_date: Optional[datetime]
    feedback: Optional[str]
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class CertificationCreate(BaseModel):
    """Certification creation schema"""
    employee_id: UUID = Field(..., description="Employee ID")
    course_id: Optional[UUID] = Field(None, description="Course ID (if course-based)")
    name: str = Field(..., max_length=200, description="Certification name")
    issuing_organization: str = Field(..., max_length=200, description="Issuing organization")
    certification_number: Optional[str] = Field(None, max_length=100, description="Certification number")
    issued_date: date = Field(..., description="Issue date")
    expiry_date: Optional[date] = Field(None, description="Expiry date")
    verification_url: Optional[str] = Field(None, max_length=500, description="Verification URL")
    certificate_url: Optional[str] = Field(None, max_length=500, description="Certificate URL")
    description: Optional[str] = Field(None, description="Description")
    skills_gained: Optional[List[str]] = Field(None, description="Skills gained")


class CertificationResponse(BaseModel):
    """Certification response schema"""
    id: UUID
    employee_id: UUID
    course_id: Optional[UUID]
    name: str
    issuing_organization: str
    certification_number: Optional[str]
    issued_date: date
    expiry_date: Optional[date]
    status: CertificationStatus
    verification_url: Optional[str]
    certificate_url: Optional[str]
    description: Optional[str]
    skills_gained: Optional[List[str]]
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class CourseListResponse(BaseModel):
    """Course list response"""
    items: List[CourseResponse]
    total: int
    page: int
    size: int
    pages: int


class EnrollmentListResponse(BaseModel):
    """Enrollment list response"""
    items: List[CourseEnrollmentResponse]
    total: int
    page: int
    size: int
    pages: int


class CertificationListResponse(BaseModel):
    """Certification list response"""
    items: List[CertificationResponse]
    total: int
    page: int
    size: int
    pages: int
