#!/usr/bin/env python3
"""
Check what employees data is actually in the database
"""

import sys
import os

# Add the app directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'app'))

from sqlalchemy.orm import Session
from sqlalchemy import text
from app.db.session import SessionLocal

def check_employees_data():
    """Check what employees are in the database"""
    db = SessionLocal()
    
    try:
        print("Checking employees data...")
        
        # Get all employees
        result = db.execute(text("""
            SELECT id, employee_id, first_name, last_name, email, 
                   department, position, is_active, user_id
            FROM employees 
            ORDER BY created_at
        """))
        
        employees = result.fetchall()
        
        if employees:
            print(f"\nFound {len(employees)} employees:")
            print("-" * 80)
            for emp in employees:
                print(f"ID: {emp[0]}")
                print(f"Employee ID: {emp[1]}")
                print(f"Name: {emp[2]} {emp[3]}")
                print(f"Email: {emp[4]}")
                print(f"Department: {emp[5]}")
                print(f"Position: {emp[6]}")
                print(f"Active: {emp[7]}")
                print(f"User ID: {emp[8]}")
                print("-" * 40)
        else:
            print("No employees found")
            
        # Check users
        result = db.execute(text("SELECT id, email, role FROM users"))
        users = result.fetchall()
        print(f"\nFound {len(users)} users:")
        for user in users:
            print(f"  {user[1]} - {user[2]}")
        
    except Exception as e:
        print(f"❌ Error checking employees data: {e}")
    finally:
        db.close()

if __name__ == "__main__":
    check_employees_data()
