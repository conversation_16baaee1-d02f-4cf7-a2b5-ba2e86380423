"""add column to approval model

Revision ID: 9ceabeec6a3c
Revises: acb71d2e9572
Create Date: 2025-04-24 15:38:06.962738

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '9ceabeec6a3c'
down_revision: Union[str, None] = 'acb71d2e9572'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    op.add_column(
        "approvals",
        sa.Column('pay_schedules_id', sa.<PERSON><PERSON>, sa.<PERSON>("pay_schedules.id"), nullable=True)
    )

def downgrade() -> None:
    op.drop_column("approvals", "pay_schedules_id")

