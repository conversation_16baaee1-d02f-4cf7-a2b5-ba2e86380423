"""
Redis caching utilities for performance optimization
"""

import redis
import json
import pickle
from typing import Any, Optional, Union
from datetime import timed<PERSON><PERSON>
import logging

from .config import settings

logger = logging.getLogger(__name__)


class CacheManager:
    """Redis cache manager for performance optimization"""
    
    def __init__(self):
        try:
            self.redis_client = redis.from_url(
                settings.REDIS_URL,
                decode_responses=False,  # Keep as bytes for pickle
                socket_connect_timeout=5,
                socket_timeout=5,
                retry_on_timeout=True
            )
            # Test connection
            self.redis_client.ping()
            logger.info("✅ Redis cache connection established")
        except Exception as e:
            logger.warning(f"⚠️ Redis cache not available: {e}")
            self.redis_client = None
    
    def _serialize(self, value: Any) -> bytes:
        """Serialize value for storage"""
        try:
            # Try JSON first for simple types
            if isinstance(value, (str, int, float, bool, list, dict)):
                return json.dumps(value).encode('utf-8')
            else:
                # Use pickle for complex objects
                return pickle.dumps(value)
        except Exception:
            # Fallback to pickle
            return pickle.dumps(value)
    
    def _deserialize(self, value: bytes) -> Any:
        """Deserialize value from storage"""
        try:
            # Try JSON first
            return json.loads(value.decode('utf-8'))
        except (json.JSONDecodeError, UnicodeDecodeError):
            # Fallback to pickle
            return pickle.loads(value)
    
    def get(self, key: str) -> Optional[Any]:
        """Get value from cache"""
        if not self.redis_client:
            return None
        
        try:
            value = self.redis_client.get(key)
            if value is None:
                return None
            return self._deserialize(value)
        except Exception as e:
            logger.warning(f"Cache get error for key {key}: {e}")
            return None
    
    def set(
        self, 
        key: str, 
        value: Any, 
        ttl: Optional[Union[int, timedelta]] = None
    ) -> bool:
        """Set value in cache with optional TTL"""
        if not self.redis_client:
            return False
        
        try:
            serialized_value = self._serialize(value)
            
            if ttl is None:
                return self.redis_client.set(key, serialized_value)
            elif isinstance(ttl, timedelta):
                return self.redis_client.setex(key, ttl, serialized_value)
            else:
                return self.redis_client.setex(key, ttl, serialized_value)
        except Exception as e:
            logger.warning(f"Cache set error for key {key}: {e}")
            return False
    
    def delete(self, key: str) -> bool:
        """Delete key from cache"""
        if not self.redis_client:
            return False
        
        try:
            return bool(self.redis_client.delete(key))
        except Exception as e:
            logger.warning(f"Cache delete error for key {key}: {e}")
            return False
    
    def delete_pattern(self, pattern: str) -> int:
        """Delete all keys matching pattern"""
        if not self.redis_client:
            return 0
        
        try:
            keys = self.redis_client.keys(pattern)
            if keys:
                return self.redis_client.delete(*keys)
            return 0
        except Exception as e:
            logger.warning(f"Cache delete pattern error for {pattern}: {e}")
            return 0
    
    def exists(self, key: str) -> bool:
        """Check if key exists in cache"""
        if not self.redis_client:
            return False
        
        try:
            return bool(self.redis_client.exists(key))
        except Exception as e:
            logger.warning(f"Cache exists error for key {key}: {e}")
            return False
    
    def increment(self, key: str, amount: int = 1) -> Optional[int]:
        """Increment counter in cache"""
        if not self.redis_client:
            return None
        
        try:
            return self.redis_client.incr(key, amount)
        except Exception as e:
            logger.warning(f"Cache increment error for key {key}: {e}")
            return None
    
    def expire(self, key: str, ttl: Union[int, timedelta]) -> bool:
        """Set expiration for existing key"""
        if not self.redis_client:
            return False
        
        try:
            if isinstance(ttl, timedelta):
                return bool(self.redis_client.expire(key, int(ttl.total_seconds())))
            else:
                return bool(self.redis_client.expire(key, ttl))
        except Exception as e:
            logger.warning(f"Cache expire error for key {key}: {e}")
            return False
    
    def get_stats(self) -> dict:
        """Get cache statistics"""
        if not self.redis_client:
            return {"status": "unavailable"}
        
        try:
            info = self.redis_client.info()
            return {
                "status": "connected",
                "used_memory": info.get("used_memory_human", "unknown"),
                "connected_clients": info.get("connected_clients", 0),
                "total_commands_processed": info.get("total_commands_processed", 0),
                "keyspace_hits": info.get("keyspace_hits", 0),
                "keyspace_misses": info.get("keyspace_misses", 0),
                "hit_rate": (
                    info.get("keyspace_hits", 0) / 
                    max(info.get("keyspace_hits", 0) + info.get("keyspace_misses", 0), 1)
                ) * 100
            }
        except Exception as e:
            logger.warning(f"Cache stats error: {e}")
            return {"status": "error", "error": str(e)}


# Global cache instance
cache = CacheManager()


def cache_key(*args) -> str:
    """Generate cache key from arguments"""
    return ":".join(str(arg) for arg in args)


def cached(
    ttl: Union[int, timedelta] = 300,  # 5 minutes default
    key_prefix: str = "cache"
):
    """Decorator for caching function results"""
    def decorator(func):
        def wrapper(*args, **kwargs):
            # Generate cache key
            key_parts = [key_prefix, func.__name__]
            key_parts.extend(str(arg) for arg in args)
            key_parts.extend(f"{k}:{v}" for k, v in sorted(kwargs.items()))
            key = cache_key(*key_parts)
            
            # Try to get from cache
            result = cache.get(key)
            if result is not None:
                logger.debug(f"Cache hit for {key}")
                return result
            
            # Execute function and cache result
            result = func(*args, **kwargs)
            cache.set(key, result, ttl)
            logger.debug(f"Cache miss for {key}, result cached")
            return result
        
        return wrapper
    return decorator


# Cache key patterns for different data types
class CacheKeys:
    """Standard cache key patterns"""
    
    # Employee data
    EMPLOYEE = "employee:{employee_id}"
    EMPLOYEE_LIST = "employees:{org_id}:{skip}:{limit}:{filters_hash}"
    EMPLOYEE_DEPARTMENTS = "departments:{org_id}"
    EMPLOYEE_DESIGNATIONS = "designations:{org_id}"
    
    # Attendance data
    ATTENDANCE_STATUS = "attendance:status:{employee_id}:{date}"
    ATTENDANCE_RECORDS = "attendance:records:{employee_id}:{start_date}:{end_date}"
    
    # Timesheet data
    TIMESHEET_SUMMARY = "timesheet:summary:{employee_id}:{period}"
    TIMESHEET_ENTRIES = "timesheet:entries:{employee_id}:{start_date}:{end_date}"
    
    # Project data
    PROJECT_LIST = "projects:{org_id}:{skip}:{limit}"
    PROJECT_DETAILS = "project:{project_id}"
    
    # Kanban data
    KANBAN_BOARDS = "kanban:boards:{org_id}"
    KANBAN_BOARD = "kanban:board:{board_id}"
    
    # System data
    USER_PERMISSIONS = "permissions:{user_id}"
    ORGANIZATION_SETTINGS = "org:settings:{org_id}"


# Performance monitoring
class PerformanceMonitor:
    """Monitor and log performance metrics"""
    
    @staticmethod
    def log_query_time(query_name: str, execution_time: float):
        """Log database query execution time"""
        if execution_time > 1.0:  # Log slow queries (>1 second)
            logger.warning(f"Slow query detected: {query_name} took {execution_time:.2f}s")
        elif execution_time > 0.5:  # Log medium queries (>0.5 second)
            logger.info(f"Medium query: {query_name} took {execution_time:.2f}s")
    
    @staticmethod
    def log_cache_performance(operation: str, key: str, hit: bool = None):
        """Log cache operation performance"""
        if hit is not None:
            status = "HIT" if hit else "MISS"
            logger.debug(f"Cache {operation} {status}: {key}")
        else:
            logger.debug(f"Cache {operation}: {key}")


# Export main components
__all__ = [
    "cache",
    "cached",
    "cache_key", 
    "CacheKeys",
    "CacheManager",
    "PerformanceMonitor"
]
