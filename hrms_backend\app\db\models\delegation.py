from sqlalchemy import Column, String, DateTime, Date, Foreign<PERSON>ey, <PERSON>olean, Text, Enum, Integer
from sqlalchemy.dialects.postgresql import UUID, JSONB
from sqlalchemy.orm import relationship
from enum import Enum as PyEnum
from datetime import datetime, date
from typing import Optional

from ..base import BaseModel, AuditMixin


class DelegationType(PyEnum):
    TASK = "task"
    APPROVAL = "approval"
    RESPONSIBILITY = "responsibility"
    AUTHORITY = "authority"
    TEMPORARY_ROLE = "temporary_role"


class DelegationStatus(PyEnum):
    ACTIVE = "active"
    PENDING = "pending"
    EXPIRED = "expired"
    REVOKED = "revoked"
    COMPLETED = "completed"


class Delegation(BaseModel, AuditMixin):
    """Delegation model for tasks, approvals, and responsibilities"""
    __tablename__ = "delegations"

    # Basic information
    title = Column(String(200), nullable=False)
    description = Column(Text, nullable=True)
    organization_id = Column(UUID(as_uuid=True), nullable=False, index=True)

    # Delegation parties
    delegator_id = Column(UUID(as_uuid=True), ForeignKey("employees.id"), nullable=False, index=True)
    delegate_id = Column(UUID(as_uuid=True), ForeignKey("employees.id"), nullable=False, index=True)

    # Delegation details
    delegation_type = Column(Enum(DelegationType), nullable=False)
    status = Column(Enum(DelegationStatus), nullable=False, default=DelegationStatus.PENDING)

    # Timeline
    start_date = Column(Date, nullable=False)
    end_date = Column(Date, nullable=True)  # NULL means indefinite
    effective_from = Column(DateTime(timezone=True), nullable=False)
    effective_until = Column(DateTime(timezone=True), nullable=True)

    # Scope and permissions
    scope_description = Column(Text, nullable=False)
    permissions = Column(JSONB, nullable=True)  # Array of permission objects
    limitations = Column(Text, nullable=True)

    # Specific delegations
    task_ids = Column(JSONB, nullable=True)  # Array of task IDs for task delegation
    approval_types = Column(JSONB, nullable=True)  # Array of approval types
    responsibility_areas = Column(JSONB, nullable=True)  # Array of responsibility areas

    # Approval workflow
    requires_approval = Column(Boolean, default=False)
    approved_by = Column(UUID(as_uuid=True), ForeignKey("employees.id"), nullable=True)
    approved_at = Column(DateTime(timezone=True), nullable=True)
    approval_comments = Column(Text, nullable=True)

    # Acceptance
    accepted_by_delegate = Column(Boolean, nullable=True)  # NULL = pending, True = accepted, False = rejected
    accepted_at = Column(DateTime(timezone=True), nullable=True)
    delegate_comments = Column(Text, nullable=True)

    # Completion and handover
    completion_notes = Column(Text, nullable=True)
    handover_completed = Column(Boolean, default=False)
    handover_date = Column(DateTime(timezone=True), nullable=True)

    # Notifications
    notify_delegator = Column(Boolean, default=True)
    notify_on_actions = Column(Boolean, default=True)

    # Auto-revert settings
    auto_revert_on_return = Column(Boolean, default=True)
    revert_date = Column(DateTime(timezone=True), nullable=True)

    # Relationships
    delegator = relationship("Employee", foreign_keys=[delegator_id])
    delegate = relationship("Employee", foreign_keys=[delegate_id])
    approver = relationship("Employee", foreign_keys=[approved_by])
    activities = relationship("DelegationActivity", back_populates="delegation")


class DelegationActivity(BaseModel):
    """Delegation activity log"""
    __tablename__ = "delegation_activities"

    delegation_id = Column(UUID(as_uuid=True), ForeignKey("delegations.id"), nullable=False, index=True)
    user_id = Column(UUID(as_uuid=True), ForeignKey("employees.id"), nullable=False)

    # Activity details
    activity_type = Column(String(50), nullable=False)  # created, accepted, used, completed, etc.
    description = Column(Text, nullable=False)

    # Activity data
    activity_data = Column(JSONB, nullable=True)  # Additional activity metadata

    # Related entities
    related_entity_type = Column(String(50), nullable=True)  # task, approval, etc.
    related_entity_id = Column(UUID(as_uuid=True), nullable=True)

    # Relationships
    delegation = relationship("Delegation", back_populates="activities")
    user = relationship("Employee")


class DelegationTemplate(BaseModel):
    """Delegation template for common delegation scenarios"""
    __tablename__ = "delegation_templates"

    name = Column(String(200), nullable=False)
    description = Column(Text, nullable=True)
    organization_id = Column(UUID(as_uuid=True), nullable=False, index=True)

    # Template configuration
    delegation_type = Column(Enum(DelegationType), nullable=False)
    default_duration_days = Column(Integer, nullable=True)

    # Default settings
    default_permissions = Column(JSONB, nullable=True)
    default_limitations = Column(Text, nullable=True)
    default_scope = Column(Text, nullable=True)

    # Approval settings
    requires_approval = Column(Boolean, default=False)
    auto_approve_for_roles = Column(JSONB, nullable=True)  # Array of role names

    # Usage tracking
    usage_count = Column(Integer, default=0)
    is_active = Column(Boolean, default=True)


class DelegationRule(BaseModel):
    """Automatic delegation rules"""
    __tablename__ = "delegation_rules"

    name = Column(String(200), nullable=False)
    description = Column(Text, nullable=True)
    organization_id = Column(UUID(as_uuid=True), nullable=False, index=True)

    # Rule conditions
    trigger_conditions = Column(JSONB, nullable=False)  # When to trigger delegation

    # Delegation configuration
    delegation_type = Column(Enum(DelegationType), nullable=False)
    auto_delegate_to = Column(UUID(as_uuid=True), ForeignKey("employees.id"), nullable=True)
    delegation_duration_days = Column(Integer, nullable=True)

    # Rule settings
    is_active = Column(Boolean, default=True)
    priority = Column(Integer, default=0)  # Higher priority rules execute first

    # Relationships
    default_delegate = relationship("Employee", foreign_keys=[auto_delegate_to])


class ApprovalDelegation(BaseModel, AuditMixin):
    """Specific approval delegation tracking"""
    __tablename__ = "approval_delegations"

    delegation_id = Column(UUID(as_uuid=True), ForeignKey("delegations.id"), nullable=False)

    # Approval details
    approval_type = Column(String(100), nullable=False)  # leave_approval, expense_approval, etc.
    approval_entity_type = Column(String(50), nullable=False)  # leave_request, expense_claim, etc.
    approval_entity_id = Column(UUID(as_uuid=True), nullable=False)

    # Delegation usage
    used_at = Column(DateTime(timezone=True), nullable=False, default=datetime.utcnow)
    decision = Column(String(50), nullable=False)  # approved, rejected, etc.
    comments = Column(Text, nullable=True)

    # Original approver notification
    original_approver_notified = Column(Boolean, default=False)
    notification_sent_at = Column(DateTime(timezone=True), nullable=True)

    # Relationships
    delegation = relationship("Delegation")


class TaskDelegation(BaseModel, AuditMixin):
    """Specific task delegation tracking"""
    __tablename__ = "task_delegations"

    delegation_id = Column(UUID(as_uuid=True), ForeignKey("delegations.id"), nullable=False)
    task_id = Column(UUID(as_uuid=True), ForeignKey("tasks.id"), nullable=False)

    # Delegation details
    delegated_at = Column(DateTime(timezone=True), nullable=False, default=datetime.utcnow)
    completed_at = Column(DateTime(timezone=True), nullable=True)

    # Progress tracking
    progress_updates = Column(JSONB, nullable=True)  # Array of progress update objects

    # Handover
    handover_notes = Column(Text, nullable=True)
    handover_completed = Column(Boolean, default=False)

    # Relationships
    delegation = relationship("Delegation")
    task = relationship("Task")
