from flask import url_for, jsonify,request,current_app
from flask.views import Method<PERSON>iew
from flask_smorest import Blueprint, abort
from schemas import EmployeeSchema
from core.utils.responseBuilder import ResponseBuilder
from core.middleware import roles_required
from core.repositories.user import UserRepository
from core.models.zoho_config import ZohoConfig
from ..repositories.user import UserRepository
from core.databases.database import db

blueprint = Blueprint("zohoAttendence", __name__, description="Operations for Zoho People Attedence")
    
@blueprint.route("/zoho_attendance")
class Zoho_People_List(MethodView):
    @roles_required(['admin'])
    @blueprint.response(200, EmployeeSchema)
    def get(self):

        user = UserRepository().authUser()#Gets the current login user data
        user_id=user.id
        
        zoho_integration = ZohoConfig.query.filter_by(user_id=user_id).first()

        if zoho_integration:

            data = {
                "client_id":zoho_integration.client_id,
                "client_secret":zoho_integration.client_secret,
                 "code":zoho_integration.authorization_employees_code,
                 "user_id":zoho_integration.user_id
            }

            # print(f"Client ID:{zoho_integration.client_id}, Client Secret:{zoho_integration.client_secret}, Code:{zoho_integration.authorization_employees_code}")
            return jsonify({
                             "data":data
                              })
      


    