"""create working_days table

Revision ID: ee6fd56b6289
Revises: 15d4df5de92f
Create Date: 2024-04-03 15:05:03.225163

"""
from typing import Sequence, Union

from alembic import op
from sqlalchemy import func, TIMESTAMP, ForeignKey
from sqlalchemy import Column, Integer, String, Float

# revision identifiers, used by Alembic.
revision: str = 'ee6fd56b6289'
down_revision: Union[str, None] = '15d4df5de92f'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    op.create_table(
        'working_days',
        Column('id', Integer, primary_key=True),
        <PERSON>umn('month', String(50), nullable=False),
        <PERSON><PERSON>n('year', String(50), nullable=False),
        <PERSON>umn('total_working_days', Integer, nullable=False),
        <PERSON><PERSON><PERSON>('present_days', Integer, nullable=False),
        <PERSON>umn('absent_days', Integer, nullable=False),
        <PERSON><PERSON><PERSON>('employee_id', Integer, Foreign<PERSON>ey("employees.id")),
        <PERSON><PERSON><PERSON>("timestamp", TIMESTAMP, server_default=func.now()),
    )


def downgrade() -> None:
     op.drop_table("working_days")