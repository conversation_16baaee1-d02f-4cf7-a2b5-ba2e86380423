"""create benefits table

Revision ID: 76231ea93797
Revises: 5382ee23b696
Create Date: 2024-04-03 15:04:10.292301

"""
from typing import Sequence, Union

from alembic import op
from sqlalchemy import func, TIMESTAMP, ForeignKey
from sqlalchemy import Column, Integer, String, Float

# revision identifiers, used by Alembic.
revision: str = '76231ea93797'
down_revision: Union[str, None] = '5382ee23b696'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    op.create_table(
        'benefits',
        Column('id', Integer, primary_key=True),
        <PERSON>umn('benefit_name', String(100), nullable=False),
        Column('payslip_name', String(100), nullable=False),
        Column('component_type',String(100), nullable=False),
        Column('calculation_type', String(100), nullable=False),
        Column('cycle', String(45), nullable=True),
        Column('duration', String(45), nullable=True),
        Column('amount', Float, nullable=True),
        Column('value', Float, nullable=True),
        Column('user_id', Integer, ForeignKey("users.id")),
        Column("timestamp", TIMESTAMP, server_default=func.now()),
    )
    
def downgrade() -> None:
    op.drop_table("benefits")