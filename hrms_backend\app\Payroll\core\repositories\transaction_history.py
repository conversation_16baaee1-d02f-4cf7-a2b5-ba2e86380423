from core.models.transaction_history import TransactionHistory
from core.databases.database import db
from core.repositories.user import UserRepository
from sqlalchemy import desc
import traceback
class TransactionHistoryRepository:
    @classmethod
    def create_transaction(cls, **kwargs):
        try:
            user_id = UserRepository.authUserId()
            kwargs['user_id'] = user_id
            transaction_history = TransactionHistory(**kwargs)
            db.session.add(transaction_history)
            db.session.commit()
            return transaction_history
        except Exception as e:
            db.session.rollback()
            print(f"Error creating transaction: {e}")
            raise

    @classmethod
    def fetchAll(self):
        # return TransactionHistory.query.filter_by(user_id=UserRepository().authUserId()).order_by(TransactionHistory.createdAt.desc()).all()
        return (
            TransactionHistory.query
            .filter_by(user_id=UserRepository().authUserId())
            .order_by(TransactionHistory.createdAt.desc())
            .options(db.joinedload(TransactionHistory.employee))  
            .all()
        )


    @classmethod
    def getTransactionById(self, id):
        return TransactionHistory.query.filter(TransactionHistory.id == id).first()
    
    @classmethod
    def get_transaction_by_reference(cls, transaction_id, reference_code):
        """Fetch transaction history using `reference_code` instead of `transaction_id`."""
        return TransactionHistory.query.filter(
            (TransactionHistory.transaction_id == str(transaction_id)) |
            (TransactionHistory.reference_code == reference_code)  # ✅ Use reference_code
        ).first()

    # @classmethod
    # def getTransactionBySecret_key(self, client_id):
    #     return Employee_Paystack_Transaction_RepCodeModel.query.filter_by(client_id=client_id).first()

    # @classmethod
    # def update_paystack_transaction(self, id, **kwargs):
    #     paystack_transaction_emp= Employee_Paystack_Transaction_RepCodeModel.query.filter_by(transaction_id=id).first()
    #     if paystack_transaction_emp:
    #         for key, value in kwargs.items():
    #             setattr(paystack_transaction_emp, key, value)
    #         db.session.commit()
    #         return paystack_transaction_emp
    #     else:
    #         return None

    # @classmethod
    # def deleteTransaction(self, id):
    #     Employee_Paystack_Transaction_RepCodeModel.query.filter(Employee_Paystack_Transaction_RepCodeModel.id == id).delete()
    #     db.session.commit()
    #     return
    
  