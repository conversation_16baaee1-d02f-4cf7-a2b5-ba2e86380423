from fastapi import APIRouter, HTTPException, status, Depends
from sqlalchemy.orm import Session
from typing import Optional
from uuid import UUID
import logging

from ..core.security import get_current_user, CurrentUser, security_manager
from ..db.session import get_db
from ..db.models.employee import Employee
from ..schemas.auth import LoginRequest, LoginResponse, RegisterRequest, UserResponse, TokenValidationResponse
from ..services.hr_management.employee_service import EmployeeService

logger = logging.getLogger(__name__)

router = APIRouter()
employee_service = EmployeeService()

@router.post("/login", response_model=LoginResponse)
async def login(
    credentials: LoginRequest,
    db: Session = Depends(get_db)
):
    """Authenticate user and return access token"""
    try:
        # Find employee by email or employee_id
        # Use raw SQL to avoid model column issues
        result = db.execute(
            "SELECT id, employee_id, first_name, last_name, user_id, email FROM employees WHERE (email = :email OR employee_id = :emp_id) AND is_active = true LIMIT 1",
            {"email": credentials.email, "emp_id": credentials.email}
        ).fetchone()

        if not result:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid email/employee ID or password"
            )
        
        # For now, we'll create a simple password verification
        # In production, you'd verify against a hashed password
        # This is a temporary implementation for integration
        
        # Determine role based on employee_id
        role = "employee"  # default
        if result.employee_id == "SUPER001":
            role = "super_admin"
        elif result.employee_id == "ADMIN001":
            role = "admin"
        elif result.employee_id == "HR001":
            role = "hr"
        elif result.employee_id == "MGR001":
            role = "manager"
        elif result.employee_id == "EMP001":
            role = "employee"

        # Get user's organization_id from users table using raw SQL
        user_org_id = None
        try:
            user_query = db.execute(
                "SELECT organization_id FROM users WHERE email = :email",
                {"email": result.email}
            ).fetchone()
            if user_query and user_query[0]:
                user_org_id = str(user_query[0])
        except Exception as e:
            logger.error(f"Error getting user organization: {e}")
            # Fallback: use a default organization if available
            default_org_query = db.execute(
                "SELECT id FROM organizations WHERE is_active = true LIMIT 1"
            ).fetchone()
            if default_org_query:
                user_org_id = str(default_org_query[0])

        # Create access token
        access_token = security_manager.create_access_token(
            data={
                "sub": str(result.user_id or result.id),
                "employee_id": result.employee_id,
                "role": role.upper(),
                "organization_id": user_org_id
            }
        )

        # Prepare user data
        user_data = {
            "id": str(result.id),
            "email": result.email or result.employee_id,  # Use actual email if available
            "name": f"{result.first_name} {result.last_name}",
            "role": role,
            "organization_id": user_org_id
        }
        
        return LoginResponse(
            access_token=access_token,
            token_type="bearer",
            user=user_data,
            message="Login successful"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Login failed: {str(e)}"
        )

@router.post("/register", response_model=dict)
async def register(
    user_data: RegisterRequest,
    db: Session = Depends(get_db)
):
    """Register a new user"""
    try:
        # Check if user already exists
        existing_employee = db.query(Employee).filter(
            Employee.email == user_data.email
        ).first()
        
        if existing_employee:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="User with this email already exists"
            )
        
        # Create new employee record
        from ..schemas.employee import EmployeeCreate
        employee_create = EmployeeCreate(
            first_name=user_data.first_name,
            last_name=user_data.last_name,
            email=user_data.email,
            role=user_data.role,
            organization_id=user_data.organization_id
        )
        
        # Use employee service to create the employee
        new_employee = await employee_service.create_employee(
            db=db,
            employee_data=employee_create,
            current_user=None  # No current user for registration
        )
        
        return {
            "message": "User registered successfully",
            "user_id": str(new_employee.id)
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Registration failed: {str(e)}"
        )

@router.get("/validate", response_model=TokenValidationResponse)
async def validate_token(
    current_user: CurrentUser = Depends(get_current_user)
):
    """Validate the current access token"""
    return TokenValidationResponse(
        valid=True,
        user_id=current_user.user_id,
        email=current_user.email,
        role=current_user.role,
        organization_id=current_user.organization_id
    )

@router.get("/me", response_model=UserResponse)
async def get_current_user_info(
    current_user: CurrentUser = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get current user information"""
    try:
        employee = db.query(Employee).filter(
            Employee.id == UUID(current_user.user_id)
        ).first()
        
        if not employee:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Employee not found"
            )
        
        # Normalize role for API response
        from ..core.security import normalize_role_for_rbac
        normalized_role = normalize_role_for_rbac(employee.role or "employee")

        return UserResponse(
            id=str(employee.id),
            email=employee.email,
            name=f"{employee.first_name} {employee.last_name}",
            role=normalized_role,
            organization_id=str(employee.organization_id) if employee.organization_id else None
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get user info: {str(e)}"
        )

@router.post("/logout")
async def logout():
    """Logout user (client-side token removal)"""
    return {"message": "Logout successful"}
