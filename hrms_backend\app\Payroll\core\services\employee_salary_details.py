from core.repositories.employee_salary_details import EmployeeSalDetRepository

class EmployeeSalDetsService:
    def __init__(self) -> None:
        self.repository = EmployeeSalDetRepository()

    def createEmployeeSalDets(self, Kwargs):
        # print(Kwargs)
        return self.repository.createEmployeeSalDet(**Kwargs)
    
    def getEmployeeSalDetail(self, id):
        return self.repository.getEmployeeSalDetails(id)
    
    def updateEmployeeSalDet(self, id, **Kwargs):
        return self.repository.updateEmployeeSalDets(id, **Kwargs)
    
    def getEmployeeSalDetByKey(self, Kwarg):
        return self.repository.getEmployeeSalDetByKeys(Kwarg)
    
    def deleteEmployeeSalDets(self, id):
        return self.repository.deleteEmployeeSalDets(id)