from core.repositories.designations import DesignationsRepository

class DesignationsService:
    def __init__(self) -> None:
        self.repository = DesignationsRepository()

    def createDesignations(self, Kwargs):
        # print(Kwargs)
        return self.repository.createDesignations(**Kwargs)
    
    def fetchAll(self):
        designations = self.repository.fetchAll()
        total_designation = len(designations)
        return [
            {
                "id": designation.id,
                "name": designation.name,
                "timestamp": designation.timestamp.strftime("%Y-%m-%d"),
                "employee_count": employee_count
            }
            for designation, employee_count in designations
        ], total_designation
    
    def getDesignations(self, id):
        return self.repository.getDesignations(id)
    
    def updateDesignations(self, id, Kwargs):
        return self.repository.updateDesignations(id, **Kwargs)
    
    def getDesignationsByKey(self, Kwarg):
        return self.repository.getDesignationsByKeys(Kwarg)
    
    def deleteDesignations(self, id):
        return self.repository.deleteDesignations(id)