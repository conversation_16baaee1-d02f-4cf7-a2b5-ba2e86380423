"""Create loan request table

Revision ID: af392e7132f1
Revises: 275e0de6e0ec
Create Date: 2024-07-15 16:45:13.815602

"""
from typing import Sequence, Union

from alembic import op
from sqlalchemy import func, TIMESTAMP, ForeignKey
from sqlalchemy import Column, Integer, String, Text, Float, DateTime


# revision identifiers, used by Alembic.
revision: str = 'af392e7132f1'
down_revision: Union[str, None] = '275e0de6e0ec'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    op.create_table(
        'loan_request',
        Column('id', Integer, primary_key=True),
        Column('month', String(45), nullable=False),
        <PERSON>umn("timestamp", TIMESTAMP, server_default=func.now()),
        <PERSON>umn('principal', Float, nullable=False),
        <PERSON>umn('date', DateTime, nullable=True, server_default=func.now()),
        Column('interest', Float, nullable=False),
        Column('status', String(45), nullable=False),
        <PERSON>umn('monthly_repayment', Float, nullable=False),
        Column('duration', String(45), nullable=False),
        Column('total_repayment', Float, nullable=False),
        Column('user_id', Integer, ForeignKey("users.id")),
    )


def downgrade() -> None:
    op.drop_table("loan_request")

