from flask.views import <PERSON>View
from flask_smorest import Blueprint, abort
from sqlalchemy.exc import SQLAlchemyError
from core.middleware import roles_required
from schemas import AdminActivitySchema
from core.services.activity_admin import AdminActivityService
from core.utils.responseBuilder import ResponseBuilder

blueprint = Blueprint("AdminActivity", __name__, description="Operations for Admin Activity Logs")

@blueprint.route("/admin-activities/<id>")
class AdminActivity(MethodView):
    @roles_required(['admin'])
    @blueprint.response(200, AdminActivitySchema)
    def get(self, id):
        service = AdminActivityService()
        activity = service.getActivityById(id)
        if not activity:
            abort(404, message="Admin activity not found")
        return ResponseBuilder(data=AdminActivitySchema().dump(activity), status_code=200).build()

    @roles_required(['admin'])
    def delete(self, id):
        service = AdminActivityService()
        activity = service.getActivityById(id)
        if not activity:
            abort(404, message="Admin activity not found")
        service.deleteActivity(id)
        return {"message": "Admin activity deleted successfully"}

    @roles_required(['admin'])
    @blueprint.arguments(AdminActivitySchema)
    @blueprint.response(201, AdminActivitySchema)
    def put(self, data, id):
        service = AdminActivityService()
        activity = service.getActivityById(id)
        if not activity:
            abort(404, message="Admin activity not found")
        try:
            updated = service.repository.updateActivity(id, **data)
            return updated
        except SQLAlchemyError:
            abort(500, message="Error updating admin activity")


@blueprint.route("/admin-activities")
class AdminActivityList(MethodView):
    @roles_required(['admin'])
    @blueprint.response(200, AdminActivitySchema)
    def get(self):
        service = AdminActivityService()
        activities, total = service.fetchAllActivities()
        return ResponseBuilder(data=AdminActivitySchema(many=True).dump(activities), status_code=200, total=total).build()

    @roles_required(['admin'])
    @blueprint.arguments(AdminActivitySchema)
    @blueprint.response(201, AdminActivitySchema)
    def post(self, data):
        try:
            service = AdminActivityService()
            created = service.logActivity(data.get("message"))
            return created
        except SQLAlchemyError:
            abort(500, message="Failed to log admin activity")