from core.databases.database import db
from core.models.basemodel import ModelBase
from sqlalchemy.orm import relationship
from datetime import datetime

class Employee_Paystack_Transaction_RepCodeModel(ModelBase):
    __tablename__ = "emp_repCode_paystack_transaction"

    id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    emp_name = db.Column(db.String(45), nullable=False)
    bank_name = db.Column(db.String(45), nullable=False)
    bank_account = db.Column(db.String(45), nullable=False)
    bank_sort_code = db.Column(db.String(45), nullable=False)
    emp_id = db.Column(db.String(45), nullable=False)
    emp_rep_code = db.Column(db.String(45), nullable=False)
    emp_ref_code = db.Column(db.String(45), nullable=False)
    emp_gross = db.Column(db.String(45), nullable=False)
    currency = db.Column(db.String(45), nullable=False)
    status = db.Column(db.String(45), nullable=False)
    message = db.Column(db.String(250), nullable=False)
    transaction_id = db.Column(db.String(250), nullable=False)
    transaction_month = db.Column(db.String(250), nullable=False)
    transaction_year = db.Column(db.String(250), nullable=False)
    transaction_time = db.Column(db.String(250), nullable=False)
    user_id = db.Column(db.Integer, db.ForeignKey((ModelBase.dbSchema() + '.users.id')), nullable=False) 
    timestamp = db.Column(db.DateTime, nullable=False, default=datetime.now()) 
