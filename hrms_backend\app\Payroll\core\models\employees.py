from core.databases.database import db
from core.models.basemodel import ModelBase
from sqlalchemy.orm import relationship
from sqlalchemy.dialects.postgresql import NUMERIC
from core.utils.helper import get_nigeria_time

class EmployeeModel(ModelBase):
    __tablename__ = "employees"

    id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    first_name = db.Column(db.String(45), nullable=True)
    last_name = db.Column(db.String(45), nullable=True)
    email = db.Column(db.String(100), nullable=True)
    gender = db.Column(db.String(100), nullable=True)
    hire_date = db.Column(db.String(50), nullable=True)
    dob = db.Column(db.String(50), nullable=True)
    department_id = db.Column(db.Integer, db.ForeignKey((ModelBase.dbSchema() + '.departments.id')), nullable=True)
    designation_id = db.Column(db.Integer, db.<PERSON>Key((ModelBase.dbSchema() + '.designations.id')), nullable=True)
    user_id = db.Column(db.Integer, db.ForeignKey((ModelBase.dbSchema() + '.users.id')), nullable=True) 
    organisation_id = db.Column(db.Integer, db.ForeignKey((ModelBase.dbSchema() + '.organisations.id')), nullable=True) 
    template_id = db.Column(db.Integer, db.ForeignKey((ModelBase.dbSchema() + '.salary_templates.id')), nullable=True) 
    business_unit = db.Column(db.String(50), nullable=True)
    division = db.Column(db.String(50), nullable=True)
    taxID = db.Column(db.String(45), nullable=True)
    employeeID = db.Column(db.String(45), nullable=True)
    employee_type = db.Column(db.String(45), nullable=True)
    # status = db.Column(db.String(45), nullable=True)
    role = db.Column(db.String(45), nullable=True)
    employment_type = db.Column(db.String(45), nullable=True)
    bank_name = db.Column(db.String(45), nullable=True)
    bank_account = db.Column(db.String(45), nullable=True)
    salary_type = db.Column(db.String(45), nullable=True)
    rate = db.Column(db.String(45), nullable=True)
    hours_worked = db.Column(db.String(45), nullable=True)
    number_of_days_worked = db.Column(db.Integer, nullable=True)
    sort_code = db.Column(db.String(45), nullable=True)
    address = db.Column(db.String(45), nullable=True)
    country = db.Column(db.String(45), nullable=True)
    state = db.Column(db.String(45), nullable=True)
    city = db.Column(db.String(45), nullable=True)
    zip_code = db.Column(db.String(45), nullable=True)
    phone = db.Column(db.String(45), nullable=True)
    level = db.Column(db.String(45), nullable=True)
    tax_type = db.Column(db.String(45), nullable=True)
    currency = db.Column(db.String(45), nullable=True)
    work_schedule = db.Column(db.String(45), nullable=True)
    status = db.Column(db.String(45), nullable=True)
    role = db.Column(db.String(45), nullable=True)
    gross_pay = db.Column(db.Float, nullable=True)
    monthly_tax = db.Column(db.Float, nullable=True)
    annual_tax = db.Column(db.Float, nullable=True)
    source_tag = db.Column(db.String(45), nullable=True)
    pension_no = db.Column(db.String, nullable=True)
    pension_pfa = db.Column(db.String, nullable=True)
    pfa_name = db.Column(db.String, nullable=True)
    pfa_number = db.Column(db.String, nullable=True)
    nhf_no = db.Column(db.String, nullable=True)
    nhf_mortgage_bank = db.Column(db.String(45), nullable=True)
    tax_state = db.Column(db.String, nullable=True)
    pfc_name = db.Column(db.String, nullable=True)
    pfc_account_number = db.Column(db.String, nullable=True)
    password_hash = db.Column(db.String(150), nullable=False)
    annual_leave_days = db.Column(db.String(45), nullable=True)
    unpaid_leave_days = db.Column(db.String(45), nullable=True)
    sick_leave_days = db.Column(db.String(45), nullable=True)
    maternity_paternity_leave_days = db.Column(db.String(45), nullable=True)
    casual_leave_days = db.Column(db.String(45), nullable=True)
    compassionate_leave_days = db.Column(db.String(45), nullable=True)
    hours_worked = db.Column(db.String(45), nullable=True)
    total_working_days = db.Column(db.String(45), nullable=True)
    total_present_days = db.Column(db.String(45), nullable=True)
    total_absent_days = db.Column(db.String(45), nullable=True)
    total_taxable_monthly_sum = db.Column(db.Float, nullable=True)
    total_taxable_annual_sum = db.Column(db.Float, nullable=True)
    total_non_taxable_monthly_sum = db.Column(db.Float, nullable=True)
    total_non_taxable_annual_sum = db.Column(db.Float, nullable=True)
    total_statutory_monthly_sum = db.Column(db.Float, nullable=True)
    total_statutory_annual_sum = db.Column(db.Float, nullable=True)
    total_other_deductions_monthly_sum = db.Column(db.Float, nullable=True)
    total_other_deductions_annual_sum = db.Column(db.Float, nullable=True)
    netpay = db.Column(db.Float, nullable=True)
    is_prorated = db.Column(db.Boolean, default=False, nullable=True)
    # pension_value = db.Column(db.Float, nullable=True)
    # nhf_value = db.Column(db.Float, nullable=True)
    created_at = db.Column(db.DateTime(timezone=True), nullable=False, default=get_nigeria_time)
    updated_at = db.Column(db.DateTime(timezone=True), nullable=False, default=get_nigeria_time, onupdate=get_nigeria_time)

    employee_components = db.relationship('EmployeeComponentsPivotModel', back_populates='employee', cascade='all, delete, delete-orphan')
    employee_benefits = db.relationship('EmployeeBenefitsPivotModel', back_populates='employee')

    payroll_histories = db.relationship(
        'PayrollHistoryModel',
        back_populates='employee',
        cascade='all,delete, delete-orphan'
    )

    # childrenOrgN = db.relationship("OrganisationModel", back_populates="childrenEmp")
    # childrenDep = db.relationship("DepartmentModel", back_populates="childEmpM")
    # childrenDes = db.relationship("DesignationModel", backref="childEmpDes")
    # childrenEIT = db.relationship("EmployeeIncTaxModel", backref="childrenEmp")
    # childrenEpay = db.relationship("EmployeePayrollModel")
    # childrenEsalD = db.relationship("EmployeeSalDetailsModel")
    # childrenPdetails = db.relationship("PayrollDetailsModel")
    # childrenSalCom = db.relationship("SalaryComponentModel")
    # childrenSalTemp = db.relationship("SalaryTemplateModel")
    # childrenWday = db.relationship("WorkingDaysModel")


    
    
    
      