from fastapi import APIRouter, HTTPException, status, Depends, Query
from sqlalchemy.orm import Session
from typing import List, Optional
from uuid import UUID

from ..core.security import get_current_user, require_permission, CurrentUser
from ..core.rbac import Permissions
from ..db.session import get_db
from ..schemas.lms import (
    CourseCreate, CourseUpdate, CourseResponse, CourseListResponse,
    CourseModuleCreate, CourseModuleResponse, CourseEnrollmentCreate,
    CourseEnrollmentResponse, EnrollmentListResponse, ModuleProgressUpdate,
    AssessmentCreate, AssessmentResponse, AssessmentAttemptCreate,
    AssessmentAttemptResponse, CertificationCreate, CertificationResponse,
    CertificationListResponse
)
from ..services.hr_management.lms_service import LMSService
from ..db.models.lms import CourseStatus, CourseType, EnrollmentStatus

router = APIRouter()
lms_service = LMSService()


# Course Endpoints
@router.post("/courses", response_model=CourseResponse)
async def create_course(
    course_data: CourseCreate,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.LMS_CREATE))
):
    """Create a new course"""
    return await lms_service.create_course(db, course_data, current_user)


@router.get("/courses", response_model=CourseListResponse)
async def get_courses(
    skip: int = Query(0, ge=0),
    limit: int = Query(20, ge=1, le=100),
    status: Optional[CourseStatus] = Query(None),
    course_type: Optional[CourseType] = Query(None),
    category: Optional[str] = Query(None),
    instructor_id: Optional[UUID] = Query(None),
    search: Optional[str] = Query(None),
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.LMS_READ))
):
    """Get courses with filtering and pagination"""
    courses, total = await lms_service.get_courses(
        db=db,
        skip=skip,
        limit=limit,
        status=status,
        course_type=course_type,
        category=category,
        instructor_id=instructor_id,
        search=search,
        current_user=current_user
    )
    
    return CourseListResponse(
        items=courses,
        total=total,
        page=skip // limit + 1,
        size=limit,
        pages=(total + limit - 1) // limit
    )


@router.get("/courses/{course_id}", response_model=CourseResponse)
async def get_course(
    course_id: UUID,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.LMS_READ))
):
    """Get a specific course"""
    return await lms_service.get_course(db, course_id)


@router.put("/courses/{course_id}", response_model=CourseResponse)
async def update_course(
    course_id: UUID,
    course_data: CourseUpdate,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.LMS_UPDATE))
):
    """Update a course"""
    return await lms_service.update_course(db, course_id, course_data, current_user)


@router.post("/courses/{course_id}/publish", response_model=CourseResponse)
async def publish_course(
    course_id: UUID,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.LMS_UPDATE))
):
    """Publish a course"""
    return await lms_service.publish_course(db, course_id, current_user)


# Course Module Endpoints
@router.post("/courses/{course_id}/modules", response_model=CourseModuleResponse)
async def create_course_module(
    course_id: UUID,
    module_data: CourseModuleCreate,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.LMS_CREATE))
):
    """Create a course module"""
    # Ensure the course_id in the URL matches the one in the data
    module_data.course_id = course_id
    return await lms_service.create_course_module(db, module_data, current_user)


@router.get("/courses/{course_id}/modules", response_model=List[CourseModuleResponse])
async def get_course_modules(
    course_id: UUID,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.LMS_READ))
):
    """Get modules for a course"""
    from ..db.models.lms import CourseModule
    
    modules = db.query(CourseModule).filter(
        CourseModule.course_id == course_id
    ).order_by(CourseModule.order_index).all()
    
    return modules


# Enrollment Endpoints
@router.post("/enrollments", response_model=CourseEnrollmentResponse)
async def enroll_employee(
    enrollment_data: CourseEnrollmentCreate,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.LMS_CREATE))
):
    """Enroll an employee in a course"""
    return await lms_service.enroll_employee(db, enrollment_data, current_user)


@router.get("/enrollments", response_model=EnrollmentListResponse)
async def get_enrollments(
    skip: int = Query(0, ge=0),
    limit: int = Query(20, ge=1, le=100),
    course_id: Optional[UUID] = Query(None),
    employee_id: Optional[UUID] = Query(None),
    status: Optional[EnrollmentStatus] = Query(None),
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.LMS_READ))
):
    """Get course enrollments with filtering and pagination"""
    from ..db.models.lms import CourseEnrollment
    
    query = db.query(CourseEnrollment)
    
    # Apply organization filter
    if current_user.organization_id:
        query = query.filter(CourseEnrollment.organization_id == current_user.organization_id)
    
    # Apply filters
    if course_id:
        query = query.filter(CourseEnrollment.course_id == course_id)
    
    if employee_id:
        query = query.filter(CourseEnrollment.employee_id == employee_id)
    
    if status:
        query = query.filter(CourseEnrollment.status == status)
    
    # Get total count
    total = query.count()
    
    # Apply pagination
    enrollments = query.offset(skip).limit(limit).all()
    
    return EnrollmentListResponse(
        items=enrollments,
        total=total,
        page=skip // limit + 1,
        size=limit,
        pages=(total + limit - 1) // limit
    )


@router.get("/enrollments/{enrollment_id}", response_model=CourseEnrollmentResponse)
async def get_enrollment(
    enrollment_id: UUID,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.LMS_READ))
):
    """Get a specific enrollment"""
    from ..db.models.lms import CourseEnrollment
    
    enrollment = db.query(CourseEnrollment).filter(
        CourseEnrollment.id == enrollment_id
    ).first()
    
    if not enrollment:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Enrollment not found"
        )
    
    return enrollment


@router.put("/enrollments/{enrollment_id}/modules/{module_id}/progress")
async def update_module_progress(
    enrollment_id: UUID,
    module_id: UUID,
    progress_data: ModuleProgressUpdate,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.LMS_UPDATE))
):
    """Update module progress"""
    return await lms_service.update_module_progress(
        db, enrollment_id, module_id, progress_data, current_user
    )


# Assessment Endpoints
@router.post("/courses/{course_id}/assessments", response_model=AssessmentResponse)
async def create_assessment(
    course_id: UUID,
    assessment_data: AssessmentCreate,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.LMS_CREATE))
):
    """Create an assessment for a course"""
    from ..db.models.lms import Assessment
    
    # Ensure the course_id in the URL matches the one in the data
    assessment_data.course_id = course_id
    
    assessment = Assessment(
        **assessment_data.dict(),
        organization_id=current_user.organization_id
    )
    
    db.add(assessment)
    db.commit()
    db.refresh(assessment)
    
    return assessment


@router.get("/courses/{course_id}/assessments", response_model=List[AssessmentResponse])
async def get_course_assessments(
    course_id: UUID,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.LMS_READ))
):
    """Get assessments for a course"""
    from ..db.models.lms import Assessment
    
    assessments = db.query(Assessment).filter(
        Assessment.course_id == course_id,
        Assessment.is_active == True
    ).all()
    
    return assessments


@router.post("/assessments/{assessment_id}/attempts", response_model=AssessmentAttemptResponse)
async def submit_assessment_attempt(
    assessment_id: UUID,
    attempt_data: AssessmentAttemptCreate,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.LMS_CREATE))
):
    """Submit an assessment attempt"""
    from ..db.models.lms import AssessmentAttempt, Assessment, CourseEnrollment
    
    # Verify assessment exists
    assessment = db.query(Assessment).filter(Assessment.id == assessment_id).first()
    if not assessment:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Assessment not found"
        )
    
    # Find enrollment for this course and employee
    enrollment = db.query(CourseEnrollment).filter(
        CourseEnrollment.course_id == assessment.course_id,
        CourseEnrollment.employee_id == current_user.user_id
    ).first()
    
    if not enrollment:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Enrollment not found"
        )
    
    # Get attempt number
    attempt_count = db.query(AssessmentAttempt).filter(
        AssessmentAttempt.assessment_id == assessment_id,
        AssessmentAttempt.enrollment_id == enrollment.id
    ).count()
    
    # Check attempt limits
    if assessment.max_attempts and attempt_count >= assessment.max_attempts:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Maximum attempts exceeded"
        )
    
    # Create attempt
    attempt = AssessmentAttempt(
        assessment_id=assessment_id,
        enrollment_id=enrollment.id,
        attempt_number=attempt_count + 1,
        responses=attempt_data.responses,
        organization_id=current_user.organization_id,
        status="submitted",
        submitted_date=datetime.utcnow()
    )
    
    db.add(attempt)
    db.commit()
    db.refresh(attempt)
    
    return attempt


# Certification Endpoints
@router.post("/certifications", response_model=CertificationResponse)
async def create_certification(
    certification_data: CertificationCreate,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.LMS_CREATE))
):
    """Create a certification"""
    from ..db.models.lms import Certification
    
    certification = Certification(
        **certification_data.dict(),
        organization_id=current_user.organization_id
    )
    
    db.add(certification)
    db.commit()
    db.refresh(certification)
    
    return certification


@router.get("/certifications", response_model=CertificationListResponse)
async def get_certifications(
    skip: int = Query(0, ge=0),
    limit: int = Query(20, ge=1, le=100),
    employee_id: Optional[UUID] = Query(None),
    course_id: Optional[UUID] = Query(None),
    status: Optional[str] = Query(None),
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.LMS_READ))
):
    """Get certifications with filtering and pagination"""
    from ..db.models.lms import Certification
    
    query = db.query(Certification)
    
    # Apply organization filter
    if current_user.organization_id:
        query = query.filter(Certification.organization_id == current_user.organization_id)
    
    # Apply filters
    if employee_id:
        query = query.filter(Certification.employee_id == employee_id)
    
    if course_id:
        query = query.filter(Certification.course_id == course_id)
    
    if status:
        query = query.filter(Certification.status == status)
    
    # Get total count
    total = query.count()
    
    # Apply pagination
    certifications = query.offset(skip).limit(limit).all()
    
    return CertificationListResponse(
        items=certifications,
        total=total,
        page=skip // limit + 1,
        size=limit,
        pages=(total + limit - 1) // limit
    )
