"""
API endpoints for feeds/activity system
"""

from typing import List, Optional
from uuid import UUID
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session

from ..core.database import get_db
from ..core.auth import get_current_user
from ..services.feeds_service import FeedsService
from ..schemas.feeds import (
    FeedPostCreate, FeedPostUpdate, FeedPostResponse, FeedPostList,
    FeedCommentCreate, FeedCommentUpdate, FeedCommentResponse,
    FeedFilters, FeedStatsResponse, FeedNotificationResponse,
    FeedType, FeedPriority
)
from ..schemas.user import UserResponse

router = APIRouter()


@router.post("/posts", response_model=FeedPostResponse)
async def create_post(
    post_data: FeedPostCreate,
    current_user: UserResponse = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Create a new feed post"""
    feeds_service = FeedsService(db)
    
    post = feeds_service.create_post(
        post_data=post_data,
        author_id=current_user.id,
        author_name=f"{current_user.first_name} {current_user.last_name}",
        author_avatar=current_user.profile_picture,
        organization_id=current_user.organization_id
    )
    
    return post


@router.get("/posts", response_model=FeedPostList)
async def get_posts(
    page: int = Query(1, ge=1),
    per_page: int = Query(20, ge=1, le=100),
    feed_type: Optional[FeedType] = None,
    priority: Optional[FeedPriority] = None,
    author_id: Optional[UUID] = None,
    search: Optional[str] = None,
    current_user: UserResponse = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get feed posts with filtering and pagination"""
    feeds_service = FeedsService(db)
    
    filters = FeedFilters(
        feed_type=feed_type,
        priority=priority,
        author_id=author_id,
        search=search
    )
    
    posts, total = feeds_service.get_posts(
        user_id=current_user.id,
        organization_id=current_user.organization_id,
        filters=filters,
        page=page,
        per_page=per_page
    )
    
    has_next = page * per_page < total
    has_prev = page > 1
    
    return FeedPostList(
        posts=posts,
        total=total,
        page=page,
        per_page=per_page,
        has_next=has_next,
        has_prev=has_prev
    )


@router.get("/posts/{post_id}", response_model=FeedPostResponse)
async def get_post(
    post_id: UUID,
    current_user: UserResponse = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get a specific post by ID"""
    feeds_service = FeedsService(db)
    
    post = feeds_service.get_post_by_id(post_id, current_user.id)
    if not post:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Post not found"
        )
    
    return post


@router.put("/posts/{post_id}", response_model=FeedPostResponse)
async def update_post(
    post_id: UUID,
    post_data: FeedPostUpdate,
    current_user: UserResponse = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Update a feed post"""
    feeds_service = FeedsService(db)
    
    post = feeds_service.update_post(post_id, post_data, current_user.id)
    if not post:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Post not found"
        )
    
    return post


@router.delete("/posts/{post_id}")
async def delete_post(
    post_id: UUID,
    current_user: UserResponse = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Delete a feed post"""
    feeds_service = FeedsService(db)
    
    success = feeds_service.delete_post(post_id, current_user.id)
    if not success:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Post not found"
        )
    
    return {"message": "Post deleted successfully"}


@router.post("/posts/{post_id}/like")
async def like_post(
    post_id: UUID,
    current_user: UserResponse = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Like or unlike a post"""
    feeds_service = FeedsService(db)
    
    is_liked = feeds_service.like_post(
        post_id=post_id,
        user_id=current_user.id,
        user_name=f"{current_user.first_name} {current_user.last_name}"
    )
    
    return {"is_liked": is_liked}


@router.post("/posts/{post_id}/comments", response_model=FeedCommentResponse)
async def create_comment(
    post_id: UUID,
    comment_data: FeedCommentCreate,
    current_user: UserResponse = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Create a comment on a post"""
    feeds_service = FeedsService(db)
    
    comment = feeds_service.create_comment(
        post_id=post_id,
        comment_data=comment_data,
        author_id=current_user.id,
        author_name=f"{current_user.first_name} {current_user.last_name}",
        author_avatar=current_user.profile_picture
    )
    
    return comment


@router.get("/posts/{post_id}/comments")
async def get_post_comments(
    post_id: UUID,
    page: int = Query(1, ge=1),
    per_page: int = Query(10, ge=1, le=50),
    current_user: UserResponse = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get comments for a post"""
    feeds_service = FeedsService(db)
    
    comments, total = feeds_service.get_post_comments(post_id, page, per_page)
    
    return {
        "comments": comments,
        "total": total,
        "page": page,
        "per_page": per_page,
        "has_next": page * per_page < total,
        "has_prev": page > 1
    }


@router.get("/stats", response_model=FeedStatsResponse)
async def get_feed_stats(
    current_user: UserResponse = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get feed statistics"""
    feeds_service = FeedsService(db)
    
    stats = feeds_service.get_feed_stats(current_user.organization_id)
    
    return stats


@router.get("/notifications")
async def get_notifications(
    page: int = Query(1, ge=1),
    per_page: int = Query(20, ge=1, le=100),
    current_user: UserResponse = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get user notifications"""
    feeds_service = FeedsService(db)
    
    notifications, total = feeds_service.get_user_notifications(
        current_user.id, page, per_page
    )
    
    return {
        "notifications": notifications,
        "total": total,
        "page": page,
        "per_page": per_page,
        "has_next": page * per_page < total,
        "has_prev": page > 1
    }


@router.put("/notifications/{notification_id}/read")
async def mark_notification_read(
    notification_id: UUID,
    current_user: UserResponse = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Mark a notification as read"""
    feeds_service = FeedsService(db)
    
    success = feeds_service.mark_notification_as_read(notification_id, current_user.id)
    if not success:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Notification not found"
        )
    
    return {"message": "Notification marked as read"}
