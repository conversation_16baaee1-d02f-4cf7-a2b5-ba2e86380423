import {
  Home,
  Briefcase,
  Clock,
  Calendar,
  BarChart2,
  Settings,
  MoreHorizontal,
  Users,
  UserCheck,
  FileText,
  DollarSign,
  Kanban,
  CheckSquare,
  Timer,
  MessageSquare,
  TrendingUp,
  Award,
  Shield,
  Activity,
  ClipboardCheck,
  UserPlus
} from "lucide-react";
import { usePermissions } from '../hooks/usePermissions';
import { PermissionGate } from './ProtectedRoute';
import { useAuth } from '../contexts/AuthContext';
import { ROLES } from '../services/permissions';

export default function Sidebar({ activeView, setActiveView }) {
  const permissions = usePermissions();
  const { userRole } = useAuth();

  // Define all sidebar menu items with their permission requirements
  const allMenuItems = [
    {
      id: 'dashboard',
      icon: <Home size={18} />,
      label: 'Dashboard',
      permission: 'dashboard',
      active: activeView === 'dashboard'
    },
    {
      id: 'employees',
      icon: <Users size={18} />,
      label: 'Employees',
      permission: 'employeeDirectory',
      active: activeView === 'employees'
    },
    {
      id: 'onboarding',
      icon: <UserPlus size={18} />,
      label: 'Onboarding',
      permission: 'onboardingRead',
      active: activeView === 'onboarding'
    },
    {
      id: 'attendance',
      icon: <UserCheck size={18} />,
      label: 'Attendance',
      permission: 'attendanceTrackerSelf',
      active: activeView === 'attendance'
    },
    {
      id: 'calendar',
      icon: <Calendar size={18} />,
      label: 'Calendar',
      permission: 'calendarEvents',
      active: activeView === 'calendar'
    },
    {
      id: 'leave',
      icon: <Clock size={18} />,
      label: 'Leave',
      permission: 'leaveRequestsSelf',
      active: activeView === 'leave'
    },
    {
      id: 'time-tracker',
      icon: <Timer size={18} />,
      label: 'Time Tracker',
      permission: 'timeTracker',
      active: activeView === 'time-tracker'
    },
    {
      id: 'payroll',
      icon: <DollarSign size={18} />,
      label: 'Payroll',
      permission: 'payrollPayslipsSelf',
      active: activeView === 'payroll'
    },
    {
      id: 'projects',
      icon: <Kanban size={18} />,
      label: 'Projects',
      permission: 'projectKanbanBoards',
      active: activeView === 'projects'
    },
    {
      id: 'tasks',
      icon: <CheckSquare size={18} />,
      label: 'Tasks',
      permission: 'taskManagement',
      active: activeView === 'tasks'
    },
    {
      id: 'tickets',
      icon: <MessageSquare size={18} />,
      label: 'Tickets',
      permission: 'ticketingSystemSelf',
      active: activeView === 'tickets'
    },
    {
      id: 'performance',
      icon: <Award size={18} />,
      label: 'Performance',
      permission: 'performanceReviewsSelf',
      active: activeView === 'performance'
    },
    {
      id: 'reports',
      icon: <BarChart2 size={18} />,
      label: 'Reports',
      permission: 'systemLogs',
      active: activeView === 'reports'
    },
    {
      id: 'settings',
      icon: <Settings size={18} />,
      label: 'Settings',
      permission: 'systemSettings',
      active: activeView === 'settings'
    },
    {
      id: 'timesheet-approval',
      icon: <ClipboardCheck size={18} />,
      label: 'Timesheet Approval',
      permission: 'timesheetApproval',
      active: activeView === 'timesheet-approval'
    }
  ];

  // Filter menu items based on user role
  const getMenuItemsForRole = () => {
    if (userRole === ROLES.SUPER_ADMIN) {
      // Super admin sees only: Dashboard, Onboarding, Leave, Timesheet Approval, Projects, Tasks
      return allMenuItems.filter(item =>
        ['dashboard', 'onboarding', 'leave', 'timesheet-approval', 'projects', 'tasks'].includes(item.id)
      );
    }

    // For all other roles, show all items (filtered by permissions)
    return allMenuItems;
  };

  const menuItems = getMenuItemsForRole();

  return (
    <div className="w-16 agno-bg-primary-dark flex flex-col items-center text-white">
      <div className="p-3">
        <div className="w-10 h-10 flex items-center justify-center">
          {/* AgnoConnect Logo */}
          <svg viewBox="0 0 36 36" fill="none" xmlns="http://www.w3.org/2000/svg">
            <circle cx="12" cy="12" r="8" fill="#F47C20" />
            <circle cx="24" cy="12" r="8" fill="#0B2A5A" />
            <circle cx="18" cy="24" r="8" fill="#F47C20" />
          </svg>
        </div>
      </div>

      <div className="flex-1 w-full h-full">
        <div className="flex flex-col items-center gap-2 mt-6">
          {menuItems.map((item) => (
            <PermissionGate
              key={item.id}
              permission={item.permission}
              hideOnDenied={true}
            >
              <NavItem
                icon={item.icon}
                label={item.label}
                active={item.active}
                onClick={() => setActiveView && setActiveView(item.id)}
              />
            </PermissionGate>
          ))}
        </div>
      </div>
    </div>
  );
}

function NavItem({ icon, label, active, onClick }) {
  return (
    <div
      className={`w-full flex flex-col items-center py-2 text-xs cursor-pointer transition-colors ${
        active ? "agno-bg-orange" : "hover:bg-agno-primary"
      }`}
      onClick={onClick}
      title={label}
    >
      <div className="w-6 h-6 flex items-center justify-center">{icon}</div>
      <span className="mt-1 text-[10px] text-center leading-tight">{label}</span>
    </div>
  );
}
