from core.repositories.working_days import WorkingDaysRepository

class WorkingDaysService:
    def __init__(self) -> None:
        self.repository = WorkingDaysRepository()

    def createWorkingDays(self, Kwargs):
        # print(Kwargs)
        return self.repository.createWorkingDays(**Kwargs)
    
    def getWorkingDays(self, id):
        return self.repository.getWorkingDays(id)
    
    def updateWorkingDays(self, id, **Kwargs):
        return self.repository.updateWorkingDays(id, **Kwargs)
    
    def getWorkingDaysByKey(self, Kwarg):
        return self.repository.getWorkingDaysByKeys(Kwarg)
    
    def deleteWorkingDays(self, id):
        return self.repository.deleteWorkingDays(id)