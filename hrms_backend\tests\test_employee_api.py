#!/usr/bin/env python3
"""
Test Employee API Integration
"""

import requests
import json

BASE_URL = "http://localhost:8085"

def test_employee_api():
    """Test employee API endpoints"""
    print("🧪 Testing Employee API")
    print("=" * 40)
    
    # Get auth token
    try:
        auth_response = requests.post(f"{BASE_URL}/api/auth/login", json={
            "email": "ADMIN001",
            "password": "password123"
        })
        
        if auth_response.status_code != 200:
            print(f"❌ Auth failed: {auth_response.status_code}")
            print(f"Response: {auth_response.text}")
            return
        
        token = auth_response.json()["access_token"]
        headers = {"Authorization": f"Bearer {token}"}
        print("✅ Authentication successful")
        
        # Test employee endpoints
        endpoints = [
            ("GET", "/api/employees/", "Get employees"),
            ("GET", "/api/employees/departments/", "Get departments"),
            ("GET", "/api/employees/designations/", "Get designations"),
        ]
        
        for method, endpoint, description in endpoints:
            try:
                response = requests.get(f"{BASE_URL}{endpoint}", headers=headers)
                print(f"\n{description}:")
                print(f"  Status: {response.status_code}")
                
                if response.status_code == 200:
                    data = response.json()
                    print(f"  Response: {json.dumps(data, indent=2)[:200]}...")
                else:
                    print(f"  Error: {response.text}")
                    
            except Exception as e:
                print(f"  Error: {e}")
                
    except Exception as e:
        print(f"❌ Test failed: {e}")

if __name__ == "__main__":
    test_employee_api()
