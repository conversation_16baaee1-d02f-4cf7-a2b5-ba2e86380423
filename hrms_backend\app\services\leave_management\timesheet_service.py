"""
Optimized Timesheet business logic service with caching
"""

import logging
import time
import hashlib
from typing import Optional, List, Dict, Any
from uuid import UUID
from datetime import date, datetime, timedelta
from sqlalchemy.orm import Session, joinedload
from sqlalchemy import and_, or_, func, desc
from fastapi import HTTPException

from ...db.models.timesheet import TimesheetEntry
from ...db.models.project import Project
from ...db.models.employee import Employee
from ...schemas.timesheet import (
    TimesheetEntryCreate, TimesheetEntryUpdate, TimesheetEntryResponse,
    TimesheetListResponse, TimesheetSummary
)
from ...core.security import CurrentUser
from ...core.cache import cache, CacheKeys, PerformanceMonitor

logger = logging.getLogger(__name__)


class TimesheetService:
    """Optimized timesheet service with caching and performance monitoring"""

    async def get_timesheet_entries(
        self,
        db: Session,
        employee_id: Optional[UUID] = None,
        start_date: Optional[date] = None,
        end_date: Optional[date] = None,
        skip: int = 0,
        limit: int = 20,
        current_user: CurrentUser = None
    ) -> TimesheetListResponse:
        """Get timesheet entries with caching and optimization"""
        start_time = time.time()

        try:
            # Generate cache key
            cache_key = CacheKeys.TIMESHEET_ENTRIES.format(
                employee_id=employee_id or current_user.user_id,
                start_date=start_date or "none",
                end_date=end_date or "none"
            )

            # Try cache first
            cached_result = cache.get(cache_key)
            if cached_result:
                PerformanceMonitor.log_cache_performance("GET", cache_key, hit=True)
                return TimesheetListResponse(**cached_result)

            PerformanceMonitor.log_cache_performance("GET", cache_key, hit=False)

            # Use eager loading to prevent N+1 queries
            query = db.query(TimesheetEntry).options(
                joinedload(TimesheetEntry.project),
                joinedload(TimesheetEntry.employee)
            )

            # Apply filters
            if employee_id:
                query = query.filter(TimesheetEntry.employee_id == employee_id)
            elif current_user:
                query = query.filter(TimesheetEntry.employee_id == current_user.user_id)

            if start_date:
                query = query.filter(TimesheetEntry.start_time >= start_date)

            if end_date:
                query = query.filter(TimesheetEntry.start_time <= end_date)

            # Get total count
            total = query.count()

            # Apply pagination and ordering
            entries = query.order_by(desc(TimesheetEntry.start_time)).offset(skip).limit(limit).all()

            # Calculate totals
            total_hours = sum(entry.duration_minutes / 60.0 for entry in entries)
            billable_hours = sum(
                entry.duration_minutes / 60.0 for entry in entries if entry.billable
            )

            # Convert to response format
            entry_responses = [
                TimesheetEntryResponse.from_orm(entry) for entry in entries
            ]

            result = TimesheetListResponse(
                entries=entry_responses,
                total=total,
                total_hours=total_hours,
                billable_hours=billable_hours,
                skip=skip,
                limit=limit
            )

            # Cache for 2 minutes (shorter TTL for frequently changing data)
            cache.set(cache_key, result.dict(), ttl=120)

            # Log performance
            execution_time = time.time() - start_time
            PerformanceMonitor.log_query_time("get_timesheet_entries", execution_time)

            return result

        except Exception as e:
            logger.error(f"Error getting timesheet entries: {e}")
            raise HTTPException(
                status_code=500,
                detail="Error retrieving timesheet entries"
            )

    async def get_timesheet_summary(
        self,
        db: Session,
        employee_id: Optional[UUID] = None,
        start_date: Optional[date] = None,
        end_date: Optional[date] = None,
        current_user: CurrentUser = None
    ) -> TimesheetSummary:
        """Get timesheet summary with caching"""
        start_time = time.time()

        try:
            # Generate cache key
            period = f"{start_date or 'none'}:{end_date or 'none'}"
            cache_key = CacheKeys.TIMESHEET_SUMMARY.format(
                employee_id=employee_id or current_user.user_id,
                period=hashlib.md5(period.encode()).hexdigest()[:8]
            )

            # Try cache first
            cached_result = cache.get(cache_key)
            if cached_result:
                PerformanceMonitor.log_cache_performance("GET", cache_key, hit=True)
                return TimesheetSummary(**cached_result)

            PerformanceMonitor.log_cache_performance("GET", cache_key, hit=False)

            # Build query
            query = db.query(TimesheetEntry)

            if employee_id:
                query = query.filter(TimesheetEntry.employee_id == employee_id)
            elif current_user:
                query = query.filter(TimesheetEntry.employee_id == current_user.user_id)

            if start_date:
                query = query.filter(TimesheetEntry.start_time >= start_date)

            if end_date:
                query = query.filter(TimesheetEntry.start_time <= end_date)

            # Get aggregated data
            entries = query.all()

            total_hours = sum(entry.duration_minutes / 60.0 for entry in entries)
            billable_hours = sum(
                entry.duration_minutes / 60.0 for entry in entries if entry.billable
            )
            non_billable_hours = total_hours - billable_hours

            # Project breakdown
            project_breakdown = {}
            for entry in entries:
                project_name = entry.project_name or "No Project"
                if project_name not in project_breakdown:
                    project_breakdown[project_name] = {
                        "total_hours": 0,
                        "billable_hours": 0
                    }

                hours = entry.duration_minutes / 60.0
                project_breakdown[project_name]["total_hours"] += hours
                if entry.billable:
                    project_breakdown[project_name]["billable_hours"] += hours

            result = TimesheetSummary(
                total_hours=total_hours,
                billable_hours=billable_hours,
                non_billable_hours=non_billable_hours,
                total_entries=len(entries),
                project_breakdown=project_breakdown
            )

            # Cache for 5 minutes
            cache.set(cache_key, result.dict(), ttl=300)

            # Log performance
            execution_time = time.time() - start_time
            PerformanceMonitor.log_query_time("get_timesheet_summary", execution_time)

            return result

        except Exception as e:
            logger.error(f"Error getting timesheet summary: {e}")
            raise HTTPException(
                status_code=500,
                detail="Error retrieving timesheet summary"
            )
