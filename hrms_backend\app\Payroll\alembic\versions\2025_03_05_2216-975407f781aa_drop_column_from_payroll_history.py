"""drop column from payroll history

Revision ID: 975407f781aa
Revises: 24a1bd9b519d
Create Date: 2025-03-05 22:16:19.091407

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '975407f781aa'
down_revision: Union[str, None] = '24a1bd9b519d'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    with op.batch_alter_table('payroll_history') as batch_op:
        batch_op.drop_column('first_name')
        batch_op.drop_column('last_name')
        batch_op.drop_column('email')
        batch_op.drop_column('hire_date')
        batch_op.drop_column('dob')
        batch_op.drop_column('department_id')
        batch_op.drop_column('designation_id')
        # batch_op.drop_column('user_id')
        batch_op.drop_column('organisation_id')
        batch_op.drop_column('template_id')
        batch_op.drop_column('business_unit')
        batch_op.drop_column('division')
        batch_op.drop_column('taxID')
        # batch_op.drop_column('employee_id')
        batch_op.drop_column('employee_type')
        batch_op.drop_column('status')
        batch_op.drop_column('role')
        batch_op.drop_column('employment_type')
        batch_op.drop_column('bank_name')
        batch_op.drop_column('bank_account')
        batch_op.drop_column('salary_type')
        batch_op.drop_column('rate')
        batch_op.drop_column('hours_worked')
        batch_op.drop_column('number_of_days_worked')
        batch_op.drop_column('sort_code')
        batch_op.drop_column('address')
        batch_op.drop_column('country')
        batch_op.drop_column('state')
        batch_op.drop_column('city')
        batch_op.drop_column('zip_code')
        batch_op.drop_column('phone')
        batch_op.drop_column('level')
        batch_op.drop_column('tax_type')
        batch_op.drop_column('currency')
        batch_op.drop_column('work_schedule')
        batch_op.drop_column('gender')
        batch_op.drop_column('source_tag')
        batch_op.drop_column('pension_no')
        batch_op.drop_column('pension_pfa')
        batch_op.drop_column('pfa_name')
        batch_op.drop_column('pfa_number')
        batch_op.drop_column('nhf_no')
        batch_op.drop_column('nhf_mortgage_bank')
        batch_op.drop_column('tax_state')
        batch_op.drop_column('pfc_name')
        batch_op.drop_column('pfc_account_number')
        batch_op.drop_column('employeeID')
        batch_op.drop_column('annual_leave_days')
        batch_op.drop_column('unpaid_leave_days')
        batch_op.drop_column('sick_leave_days')
        batch_op.drop_column('maternity_paternity_leave_days')
        batch_op.drop_column('casual_leave_days')
        batch_op.drop_column('compassionate_leave_days')
        batch_op.drop_column('total_working_days')
        batch_op.drop_column('total_present_days')
        batch_op.drop_column('total_absent_days')
        batch_op.add_column(sa.Column("pension", sa.Float, nullable=True))
        batch_op.add_column(sa.Column("nhf", sa.Float, nullable=True))
        batch_op.add_column(sa.Column("cost_to_company", sa.Float, nullable=True))


def downgrade() -> None:
    with op.batch_alter_table('payroll_history') as batch_op:
        batch_op.add_column(sa.Column('first_name', sa.String(length=45), nullable=True))
        batch_op.add_column(sa.Column('last_name', sa.String(length=45), nullable=True))
        batch_op.add_column(sa.Column('email', sa.String(length=100), nullable=True))
        batch_op.add_column(sa.Column('hire_date', sa.String(length=50), nullable=True))
        batch_op.add_column(sa.Column('dob', sa.String(length=50), nullable=True))
        batch_op.add_column(sa.Column('department_id', sa.Integer, sa.ForeignKey('departments.id'), nullable=True))
        batch_op.add_column(sa.Column('designation_id', sa.Integer, sa.ForeignKey('designations.id'), nullable=True))
        # batch_op.add_column(sa.Column('user_id', sa.Integer, sa.ForeignKey('users.id'), nullable=True))
        batch_op.add_column(sa.Column('organisation_id', sa.Integer, sa.ForeignKey('organisations.id'), nullable=True))
        batch_op.add_column(sa.Column('template_id', sa.Integer, sa.ForeignKey('salary_templates.id'), nullable=True))
        batch_op.add_column(sa.Column('business_unit', sa.String(length=50), nullable=True))
        batch_op.add_column(sa.Column('division', sa.String(length=50), nullable=True))
        batch_op.add_column(sa.Column('taxID', sa.String(length=45), nullable=True))
        # batch_op.add_column(sa.Column('employee_id', sa.Integer, sa.ForeignKey('employees.id'), nullable=False))
        batch_op.add_column(sa.Column('employee_type', sa.String(length=45), nullable=True))
        batch_op.add_column(sa.Column('status', sa.String(length=45), nullable=True))
        batch_op.add_column(sa.Column('role', sa.String(length=45), nullable=True))
        batch_op.add_column(sa.Column('employment_type', sa.String(length=45), nullable=True))
        batch_op.add_column(sa.Column('bank_name', sa.String(length=45), nullable=True))
        batch_op.add_column(sa.Column('bank_account', sa.String(length=45), nullable=True))
        batch_op.add_column(sa.Column('salary_type', sa.String(length=45), nullable=True))
        batch_op.add_column(sa.Column('rate', sa.String(length=45), nullable=True))
        batch_op.add_column(sa.Column('hours_worked', sa.String(length=45), nullable=True))
        batch_op.add_column(sa.Column('number_of_days_worked', sa.Integer, nullable=True))
        batch_op.add_column(sa.Column('sort_code', sa.String(length=45), nullable=True))
        batch_op.add_column(sa.Column('address', sa.String(length=45), nullable=True))
        batch_op.add_column(sa.Column('country', sa.String(length=45), nullable=True))
        batch_op.add_column(sa.Column('state', sa.String(length=45), nullable=True))
        batch_op.add_column(sa.Column('city', sa.String(length=45), nullable=True))
        batch_op.add_column(sa.Column('zip_code', sa.String(length=45), nullable=True))
        batch_op.add_column(sa.Column('phone', sa.String(length=45), nullable=True))
        batch_op.add_column(sa.Column('level', sa.String(length=45), nullable=True))
        batch_op.add_column(sa.Column('tax_type', sa.String(length=45), nullable=True))
        batch_op.add_column(sa.Column('currency', sa.String(length=45), nullable=True))
        batch_op.add_column(sa.Column('work_schedule', sa.String(length=45), nullable=True))
        batch_op.add_column(sa.Column('gender', sa.String(length=100), nullable=True))
        batch_op.add_column(sa.Column('source_tag', sa.String(length=45), nullable=True))
        batch_op.add_column(sa.Column('pension_no', sa.String, nullable=True))
        batch_op.add_column(sa.Column('pension_pfa', sa.String, nullable=True))
        batch_op.add_column(sa.Column('pfa_name', sa.String, nullable=True))
        batch_op.add_column(sa.Column('pfa_number', sa.String, nullable=True))
        batch_op.add_column(sa.Column('nhf_no', sa.String, nullable=True))
        batch_op.add_column(sa.Column('nhf_mortgage_bank', sa.String(length=45), nullable=True))
        batch_op.add_column(sa.Column('tax_state', sa.String, nullable=True))
        batch_op.add_column(sa.Column('pfc_name', sa.String, nullable=True))
        batch_op.add_column(sa.Column('pfc_account_number', sa.String, nullable=True))
        batch_op.add_column(sa.Column('employeeID', sa.String(length=45), nullable=True))
        batch_op.add_column(sa.Column('annual_leave_days', sa.String(length=45), nullable=True))
        batch_op.add_column(sa.Column('unpaid_leave_days', sa.String(length=45), nullable=True))
        batch_op.add_column(sa.Column('sick_leave_days', sa.String(length=45), nullable=True))
        batch_op.add_column(sa.Column('maternity_paternity_leave_days', sa.String(length=45), nullable=True))
        batch_op.add_column(sa.Column('casual_leave_days', sa.String(length=45), nullable=True))
        batch_op.add_column(sa.Column('compassionate_leave_days', sa.String(length=45), nullable=True))
        batch_op.add_column(sa.Column('total_working_days', sa.String(length=45), nullable=True))
        batch_op.add_column(sa.Column('total_present_days', sa.String(length=45), nullable=True))
        batch_op.add_column(sa.Column('total_absent_days', sa.String(length=45), nullable=True))
        batch_op.drop_column('pension')
        batch_op.drop_column('nhf')
        batch_op.drop_column('cost_to_company')
