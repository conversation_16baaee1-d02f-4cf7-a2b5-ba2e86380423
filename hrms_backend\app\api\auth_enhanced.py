from fastapi import APIRouter, HTTPException, status, Depends, Request
from sqlalchemy.orm import Session
from typing import List
from datetime import datetime, timedelta
import logging

from ..core.security import get_current_user, CurrentUser
from ..core.two_factor_auth import (
    TwoFactorAuthManager, SMSAuthManager, EmailAuthManager, RefreshTokenManager
)
from ..core.config import settings
from ..db.session import get_db
from ..db.models.user import User, RefreshToken, AuditLog
from ..schemas.auth_enhanced import (
    TwoFactorSetupRequest, TwoFactorSetupResponse, TwoFactorEnableRequest,
    TwoFactorEnableResponse, TwoFactorDisableRequest, TwoFactorVerifyRequest,
    LoginRequest, LoginResponse, TwoFactorLoginRequest, RefreshTokenRequest,
    RefreshTokenResponse, LogoutRequest, PasswordChangeRequest,
    PasswordResetRequest, PasswordResetConfirmRequest, EmailVerificationRequest,
    EmailVerificationConfirmRequest, PhoneVerificationRequest,
    PhoneVerificationConfirmRequest, UserResponse, SessionResponse,
    SecuritySettingsResponse, AuditLogResponse
)

router = APIRouter()
logger = logging.getLogger(__name__)


@router.post("/2fa/setup", response_model=TwoFactorSetupResponse)
async def setup_2fa(
    request: TwoFactorSetupRequest,
    current_user: CurrentUser = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Setup 2FA for the current user"""
    try:
        # Get user from database
        user = db.query(User).filter(User.id == current_user.user_id).first()
        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="User not found"
            )
        
        # Verify password
        from ..core.security import SecurityManager
        if not SecurityManager.verify_password(request.password, user.password_hash):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid password"
            )
        
        # Setup 2FA
        secret, qr_code = TwoFactorAuthManager.setup_2fa(user, db)
        
        return TwoFactorSetupResponse(
            secret=secret,
            qr_code=qr_code
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error setting up 2FA: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to setup 2FA"
        )


@router.post("/2fa/enable", response_model=TwoFactorEnableResponse)
async def enable_2fa(
    request: TwoFactorEnableRequest,
    current_user: CurrentUser = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Enable 2FA for the current user"""
    try:
        # Get user from database
        user = db.query(User).filter(User.id == current_user.user_id).first()
        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="User not found"
            )
        
        # Enable 2FA
        success, backup_codes = TwoFactorAuthManager.enable_2fa(user, request.totp_token, db)
        
        if not success:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid TOTP token"
            )
        
        return TwoFactorEnableResponse(
            enabled=True,
            backup_codes=backup_codes or []
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error enabling 2FA: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to enable 2FA"
        )


@router.post("/2fa/disable")
async def disable_2fa(
    request: TwoFactorDisableRequest,
    current_user: CurrentUser = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Disable 2FA for the current user"""
    try:
        # Get user from database
        user = db.query(User).filter(User.id == current_user.user_id).first()
        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="User not found"
            )
        
        # Disable 2FA
        success = TwoFactorAuthManager.disable_2fa(user, request.password, db)
        
        if not success:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid password"
            )
        
        return {"message": "2FA disabled successfully"}
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error disabling 2FA: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to disable 2FA"
        )


@router.post("/2fa/verify")
async def verify_2fa(
    request: TwoFactorVerifyRequest,
    current_user: CurrentUser = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Verify 2FA token"""
    try:
        # Get user from database
        user = db.query(User).filter(User.id == current_user.user_id).first()
        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="User not found"
            )
        
        if not user.two_fa_enabled:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="2FA is not enabled for this user"
            )
        
        # Verify token
        if request.token_type == "totp":
            success = TwoFactorAuthManager.verify_totp_token(user.two_fa_secret, request.token)
        elif request.token_type == "backup":
            success = TwoFactorAuthManager.verify_backup_code(user, request.token, db)
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid token type"
            )
        
        if not success:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid token"
            )
        
        return {"message": "Token verified successfully"}
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error verifying 2FA token: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to verify token"
        )


@router.post("/login/enhanced", response_model=LoginResponse)
async def enhanced_login(
    request: LoginRequest,
    http_request: Request,
    db: Session = Depends(get_db)
):
    """Enhanced login with 2FA support"""
    try:
        # Find user by email or username
        user = db.query(User).filter(
            (User.email == request.email) | (User.username == request.email)
        ).first()
        
        if not user:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid credentials"
            )
        
        # Verify password
        from ..core.security import SecurityManager
        if not SecurityManager.verify_password(request.password, user.password_hash):
            # Increment login attempts
            user.login_attempts += 1
            if user.login_attempts >= 5:
                user.locked_until = datetime.utcnow() + timedelta(minutes=30)
            db.commit()
            
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid credentials"
            )
        
        # Check if account is locked
        if user.locked_until and user.locked_until > datetime.utcnow():
            raise HTTPException(
                status_code=status.HTTP_423_LOCKED,
                detail="Account is temporarily locked due to multiple failed login attempts"
            )
        
        # Reset login attempts on successful password verification
        user.login_attempts = 0
        user.locked_until = None
        
        # Check if 2FA is required
        if user.two_fa_enabled:
            return LoginResponse(
                access_token="",
                token_type="bearer",
                expires_in=0,
                requires_2fa=True
            )
        
        # Generate tokens
        token_data = {
            "sub": str(user.id),
            "email": user.email,
            "role": user.role.name if user.role else "employee"
        }
        
        access_token = SecurityManager.create_access_token(token_data)
        
        # Create refresh token if requested
        refresh_token = None
        if request.remember_me:
            device_info = request.device_info or {}
            ip_address = http_request.client.host if http_request.client else None
            refresh_token = RefreshTokenManager.create_refresh_token(
                str(user.id), device_info, ip_address, db
            )
        
        # Update last login
        user.last_login = datetime.utcnow()
        db.commit()
        
        return LoginResponse(
            access_token=access_token,
            refresh_token=refresh_token,
            token_type="bearer",
            expires_in=settings.ACCESS_TOKEN_EXPIRE_MINUTES * 60,
            requires_2fa=False,
            user=UserResponse.from_orm(user)
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error during enhanced login: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Login failed"
        )


@router.post("/login/2fa", response_model=LoginResponse)
async def login_with_2fa(
    request: TwoFactorLoginRequest,
    http_request: Request,
    db: Session = Depends(get_db)
):
    """Login with 2FA verification"""
    try:
        # Find user and verify password (similar to enhanced_login)
        user = db.query(User).filter(
            (User.email == request.email) | (User.username == request.email)
        ).first()
        
        if not user:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid credentials"
            )
        
        from ..core.security import SecurityManager
        if not SecurityManager.verify_password(request.password, user.password_hash):
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid credentials"
            )
        
        # Verify 2FA token
        if request.token_type == "totp":
            success = TwoFactorAuthManager.verify_totp_token(user.two_fa_secret, request.two_fa_token)
        elif request.token_type == "backup":
            success = TwoFactorAuthManager.verify_backup_code(user, request.two_fa_token, db)
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid token type"
            )
        
        if not success:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid 2FA token"
            )
        
        # Generate tokens and complete login (similar to enhanced_login)
        token_data = {
            "sub": str(user.id),
            "email": user.email,
            "role": user.role.name if user.role else "employee"
        }
        
        access_token = SecurityManager.create_access_token(token_data)
        
        refresh_token = None
        if request.remember_me:
            device_info = request.device_info or {}
            ip_address = http_request.client.host if http_request.client else None
            refresh_token = RefreshTokenManager.create_refresh_token(
                str(user.id), device_info, ip_address, db
            )
        
        user.last_login = datetime.utcnow()
        db.commit()
        
        return LoginResponse(
            access_token=access_token,
            refresh_token=refresh_token,
            token_type="bearer",
            expires_in=settings.ACCESS_TOKEN_EXPIRE_MINUTES * 60,
            requires_2fa=False,
            user=UserResponse.from_orm(user)
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error during 2FA login: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="2FA login failed"
        )


@router.post("/refresh", response_model=RefreshTokenResponse)
async def refresh_access_token(
    request: RefreshTokenRequest,
    db: Session = Depends(get_db)
):
    """Refresh access token using refresh token"""
    try:
        # Verify refresh token
        refresh_token = RefreshTokenManager.verify_refresh_token(request.refresh_token, db)
        if not refresh_token:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid or expired refresh token"
            )
        
        # Get user
        user = db.query(User).filter(User.id == refresh_token.user_id).first()
        if not user or not user.is_active:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="User not found or inactive"
            )
        
        # Generate new tokens
        from ..core.security import SecurityManager
        token_data = {
            "sub": str(user.id),
            "email": user.email,
            "role": user.role.name if user.role else "employee"
        }
        
        new_access_token = SecurityManager.create_access_token(token_data)
        new_refresh_token = SecurityManager.create_refresh_token(token_data)
        
        # Revoke old refresh token and create new one
        RefreshTokenManager.revoke_refresh_token(request.refresh_token, db)
        
        new_refresh_token_record = RefreshToken(
            token=new_refresh_token,
            user_id=user.id,
            expires_at=datetime.utcnow() + timedelta(days=settings.REFRESH_TOKEN_EXPIRE_DAYS),
            device_info=refresh_token.device_info,
            ip_address=refresh_token.ip_address
        )
        db.add(new_refresh_token_record)
        db.commit()
        
        return RefreshTokenResponse(
            access_token=new_access_token,
            refresh_token=new_refresh_token,
            token_type="bearer",
            expires_in=settings.ACCESS_TOKEN_EXPIRE_MINUTES * 60
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error refreshing token: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to refresh token"
        )
