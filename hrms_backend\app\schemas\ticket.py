from pydantic import BaseModel, <PERSON>, validator
from typing import Optional, List
from uuid import UUID
from datetime import datetime
from enum import Enum


class TicketStatus(str, Enum):
    OPEN = "open"
    IN_PROGRESS = "in_progress"
    PENDING = "pending"
    RESOLVED = "resolved"
    CLOSED = "closed"
    CANCELLED = "cancelled"


class TicketPriority(str, Enum):
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    URGENT = "urgent"
    CRITICAL = "critical"


class TicketType(str, Enum):
    IT_SUPPORT = "it_support"
    HR_QUERY = "hr_query"
    FACILITIES = "facilities"
    PAYROLL = "payroll"
    LEAVE = "leave"
    EQUIPMENT = "equipment"
    ACCESS_REQUEST = "access_request"
    COMPLAINT = "complaint"
    SUGGESTION = "suggestion"
    OTHER = "other"


# Ticket Schemas
class TicketBase(BaseModel):
    title: str = Field(..., min_length=1, max_length=200)
    description: str = Field(..., min_length=1)
    ticket_type: TicketType
    category: Optional[str] = Field(None, max_length=100)
    subcategory: Optional[str] = Field(None, max_length=100)
    priority: TicketPriority = TicketPriority.MEDIUM
    contact_method: Optional[str] = Field(None, max_length=50)
    contact_details: Optional[str] = Field(None, max_length=255)
    location: Optional[str] = Field(None, max_length=255)
    asset_tag: Optional[str] = Field(None, max_length=100)


class TicketCreate(TicketBase):
    department_id: Optional[UUID] = None
    attachment_urls: Optional[List[str]] = None


class TicketUpdate(BaseModel):
    title: Optional[str] = Field(None, min_length=1, max_length=200)
    description: Optional[str] = Field(None, min_length=1)
    status: Optional[TicketStatus] = None
    priority: Optional[TicketPriority] = None
    category: Optional[str] = Field(None, max_length=100)
    subcategory: Optional[str] = Field(None, max_length=100)
    assigned_to: Optional[UUID] = None
    assigned_team: Optional[str] = Field(None, max_length=100)
    contact_method: Optional[str] = Field(None, max_length=50)
    contact_details: Optional[str] = Field(None, max_length=255)
    location: Optional[str] = Field(None, max_length=255)
    asset_tag: Optional[str] = Field(None, max_length=100)
    resolution: Optional[str] = None
    resolution_category: Optional[str] = Field(None, max_length=100)
    attachment_urls: Optional[List[str]] = None


class TicketResponse(TicketBase):
    id: UUID
    ticket_number: str
    requester_id: UUID
    assigned_to: Optional[UUID] = None
    assigned_team: Optional[str] = None
    organization_id: UUID
    department_id: Optional[UUID] = None
    status: TicketStatus
    due_date: Optional[datetime] = None
    first_response_at: Optional[datetime] = None
    resolved_at: Optional[datetime] = None
    closed_at: Optional[datetime] = None
    sla_breach: bool = False
    response_sla_hours: Optional[int] = None
    resolution_sla_hours: Optional[int] = None
    attachment_urls: Optional[List[str]] = None
    resolution: Optional[str] = None
    resolution_category: Optional[str] = None
    satisfaction_rating: Optional[int] = None
    satisfaction_feedback: Optional[str] = None
    auto_assigned: bool = False
    assignment_rules_applied: Optional[List[dict]] = None
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class TicketListResponse(BaseModel):
    tickets: List[TicketResponse]
    total: int
    skip: int
    limit: int


# Ticket Comment Schemas
class TicketCommentBase(BaseModel):
    content: str = Field(..., min_length=1)
    is_internal: bool = False
    is_solution: bool = False
    time_spent_minutes: Optional[int] = Field(None, ge=0)


class TicketCommentCreate(TicketCommentBase):
    attachment_urls: Optional[List[str]] = None


class TicketCommentUpdate(BaseModel):
    content: Optional[str] = Field(None, min_length=1)
    is_internal: Optional[bool] = None
    is_solution: Optional[bool] = None
    time_spent_minutes: Optional[int] = Field(None, ge=0)
    attachment_urls: Optional[List[str]] = None


class TicketCommentResponse(TicketCommentBase):
    id: UUID
    ticket_id: UUID
    author_id: UUID
    attachment_urls: Optional[List[str]] = None
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


# Ticket Assignment Schema
class TicketAssignmentRequest(BaseModel):
    ticket_ids: List[UUID]
    assigned_to: Optional[UUID] = None
    assigned_team: Optional[str] = Field(None, max_length=100)
    notify_assignee: bool = True


# Ticket Resolution Schema
class TicketResolutionRequest(BaseModel):
    resolution: str = Field(..., min_length=1)
    resolution_category: Optional[str] = Field(None, max_length=100)
    notify_requester: bool = True


# Ticket Escalation Schemas
class TicketEscalationBase(BaseModel):
    reason: str = Field(..., min_length=1)
    escalation_level: int = Field(1, ge=1)


class TicketEscalationCreate(TicketEscalationBase):
    escalated_to: UUID


class TicketEscalationResponse(TicketEscalationBase):
    id: UUID
    ticket_id: UUID
    escalated_by: UUID
    escalated_to: UUID
    escalated_at: datetime
    acknowledged_at: Optional[datetime] = None
    resolved_at: Optional[datetime] = None
    is_active: bool = True
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True
