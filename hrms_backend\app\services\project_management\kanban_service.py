from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, func
from typing import Optional, List, Dict, Any
from uuid import UUID
from datetime import datetime
from fastapi import HTTPException, status
import logging

from ...db.models.kanban import (
    KanbanBoard, KanbanColumn, KanbanCard, KanbanCardComment,
    KanbanCardChecklist, KanbanChecklistItem, KanbanBoardMember
)
from ...db.models.employee import Employee
from ...db.models.project import Project
from ...schemas.kanban import (
    KanbanBoardCreate, KanbanBoardUpdate, KanbanBoardResponse,
    KanbanBoardListResponse, KanbanBoardFullResponse,
    KanbanColumnCreate, KanbanColumnUpdate, KanbanColumnResponse,
    KanbanColumnWithCardsResponse, KanbanCardCreate, KanbanCardUpdate,
    KanbanCardResponse, KanbanCardListResponse, CardMoveRequest,
    KanbanCardCommentCreate, KanbanCardCommentUpdate, KanbanCardCommentResponse,
    KanbanBoardMemberCreate, KanbanBoardMemberUpdate, KanbanBoardMemberResponse,
    BulkCardUpdate, BulkCardMove, BoardType
)
from ...core.security import CurrentUser
from ...core.websocket_manager import notification_manager

logger = logging.getLogger(__name__)


class KanbanService:
    """Kanban service for business logic"""

    async def create_board(
        self,
        db: Session,
        board_data: KanbanBoardCreate,
        current_user: CurrentUser
    ) -> KanbanBoardResponse:
        """Create new Kanban board"""
        try:
            # Verify project exists if provided
            if board_data.project_id:
                project = db.query(Project).filter(
                    Project.id == board_data.project_id,
                    Project.organization_id == current_user.organization_id,
                    Project.is_active == True
                ).first()

                if not project:
                    raise HTTPException(
                        status_code=status.HTTP_404_NOT_FOUND,
                        detail="Project not found"
                    )

            # Create board
            board = KanbanBoard(
                **board_data.dict(),
                organization_id=current_user.organization_id,
                owner_id=current_user.user_id,
                created_by=current_user.user_id
            )

            db.add(board)
            db.commit()
            db.refresh(board)

            # Create default columns
            await self._create_default_columns(db, board.id, current_user)

            # Add creator as board member with full permissions
            await self._add_board_member(
                db, board.id, UUID(current_user.user_id),
                is_admin=True, current_user=current_user
            )

            logger.info(f"Kanban board {board.name} created by {current_user.user_id}")
            return KanbanBoardResponse.from_orm(board)

        except HTTPException:
            raise
        except Exception as e:
            db.rollback()
            logger.error(f"Error creating Kanban board: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Error creating Kanban board"
            )

    async def get_boards(
        self,
        db: Session,
        board_type: Optional[BoardType] = None,
        project_id: Optional[UUID] = None,
        search: Optional[str] = None,
        skip: int = 0,
        limit: int = 20,
        current_user: CurrentUser = None
    ) -> KanbanBoardListResponse:
        """Get Kanban boards with filtering"""
        try:
            # Base query - boards user has access to
            query = db.query(KanbanBoard).filter(
                KanbanBoard.organization_id == current_user.organization_id,
                or_(
                    KanbanBoard.is_public == True,
                    KanbanBoard.owner_id == current_user.user_id,
                    KanbanBoard.id.in_(
                        db.query(KanbanBoardMember.board_id).filter(
                            KanbanBoardMember.employee_id == current_user.user_id
                        )
                    )
                )
            )

            # Apply filters
            if board_type:
                query = query.filter(KanbanBoard.board_type == board_type)

            if project_id:
                query = query.filter(KanbanBoard.project_id == project_id)

            if search:
                search_filter = or_(
                    KanbanBoard.name.ilike(f"%{search}%"),
                    KanbanBoard.description.ilike(f"%{search}%")
                )
                query = query.filter(search_filter)

            # Get total count
            total = query.count()

            # Apply pagination and ordering
            boards = query.order_by(
                KanbanBoard.created_at.desc()
            ).offset(skip).limit(limit).all()

            # Convert to response format
            board_responses = [KanbanBoardResponse.from_orm(board) for board in boards]

            return KanbanBoardListResponse(
                boards=board_responses,
                total=total,
                skip=skip,
                limit=limit
            )

        except Exception as e:
            logger.error(f"Error getting Kanban boards: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Error retrieving Kanban boards"
            )

    async def get_board_with_data(
        self,
        db: Session,
        board_id: UUID,
        current_user: CurrentUser
    ) -> Optional[KanbanBoardFullResponse]:
        """Get board with columns and cards"""
        try:
            # Check board access
            board = await self._get_accessible_board(db, board_id, current_user)
            if not board:
                return None

            # Get columns with cards
            columns = db.query(KanbanColumn).filter(
                KanbanColumn.board_id == board_id
            ).order_by(KanbanColumn.position).all()

            columns_with_cards = []
            for column in columns:
                cards = db.query(KanbanCard).filter(
                    KanbanCard.column_id == column.id
                ).order_by(KanbanCard.position).all()

                column_response = KanbanColumnWithCardsResponse.from_orm(column)
                column_response.cards = [KanbanCardResponse.from_orm(card) for card in cards]
                columns_with_cards.append(column_response)

            # Get board members
            members = db.query(KanbanBoardMember).filter(
                KanbanBoardMember.board_id == board_id
            ).all()

            # Create full response
            board_response = KanbanBoardFullResponse.from_orm(board)
            board_response.columns = [KanbanColumnResponse.from_orm(col) for col in columns]
            board_response.members = [KanbanBoardMemberResponse.from_orm(member) for member in members]

            return board_response

        except Exception as e:
            logger.error(f"Error getting board with data {board_id}: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Error retrieving board data"
            )

    async def create_card(
        self,
        db: Session,
        board_id: UUID,
        card_data: KanbanCardCreate,
        current_user: CurrentUser
    ) -> KanbanCardResponse:
        """Create new Kanban card"""
        try:
            # Check board access
            board = await self._get_accessible_board(db, board_id, current_user)
            if not board:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Board not found or access denied"
                )

            # Verify column exists and belongs to board
            column = db.query(KanbanColumn).filter(
                KanbanColumn.id == card_data.column_id,
                KanbanColumn.board_id == board_id
            ).first()

            if not column:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Column not found"
                )

            # Verify assignee exists if provided
            if card_data.assignee_id:
                assignee = db.query(Employee).filter(
                    Employee.id == card_data.assignee_id,
                    Employee.organization_id == current_user.organization_id,
                    Employee.is_active == True
                ).first()

                if not assignee:
                    raise HTTPException(
                        status_code=status.HTTP_404_NOT_FOUND,
                        detail="Assignee not found"
                    )

            # Create card
            card = KanbanCard(
                **card_data.dict(exclude={'labels', 'external_links', 'attachment_urls'}),
                board_id=board_id,
                reporter_id=current_user.user_id,
                labels=card_data.labels or [],
                external_links=card_data.external_links or [],
                attachment_urls=card_data.attachment_urls or [],
                created_in_column_at=datetime.utcnow(),
                created_by=current_user.user_id
            )

            db.add(card)
            db.commit()
            db.refresh(card)

            # Send real-time update
            await notification_manager.notify_kanban_update(
                str(board_id),
                {
                    "type": "card_created",
                    "card_id": str(card.id),
                    "column_id": str(card.column_id),
                    "card_title": card.title,
                    "created_by": current_user.email
                }
            )

            logger.info(f"Kanban card {card.title} created by {current_user.user_id}")
            return KanbanCardResponse.from_orm(card)

        except HTTPException:
            raise
        except Exception as e:
            db.rollback()
            logger.error(f"Error creating Kanban card: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Error creating Kanban card"
            )

    async def move_card(
        self,
        db: Session,
        board_id: UUID,
        move_request: CardMoveRequest,
        current_user: CurrentUser
    ) -> KanbanCardResponse:
        """Move card to different column/position"""
        try:
            # Check board access
            board = await self._get_accessible_board(db, board_id, current_user)
            if not board:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Board not found or access denied"
                )

            # Get card
            card = db.query(KanbanCard).filter(
                KanbanCard.id == move_request.card_id,
                KanbanCard.board_id == board_id
            ).first()

            if not card:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Card not found"
                )

            # Verify target column
            target_column = db.query(KanbanColumn).filter(
                KanbanColumn.id == move_request.target_column_id,
                KanbanColumn.board_id == board_id
            ).first()

            if not target_column:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Target column not found"
                )

            old_column_id = card.column_id
            old_position = card.position

            # Get old column for logging
            old_column = db.query(KanbanColumn).filter(
                KanbanColumn.id == old_column_id
            ).first()

            # Update card position and column
            card.column_id = move_request.target_column_id
            card.position = move_request.target_position
            card.created_in_column_at = datetime.utcnow()
            card.updated_by = current_user.user_id

            # If moving to done column, set completed date
            if target_column.is_done_column and not card.completed_date:
                card.completed_date = datetime.utcnow()
            elif not target_column.is_done_column and card.completed_date:
                card.completed_date = None

            # Reorder other cards in both columns
            await self._reorder_cards_in_column(db, old_column_id, old_position, "remove")
            await self._reorder_cards_in_column(db, move_request.target_column_id, move_request.target_position, "insert")

            db.commit()
            db.refresh(card)

            # Log activity for audit trail
            await self._log_card_activity(
                db, card.id, "card_moved",
                {
                    "old_column": old_column.name if old_column else "Unknown",
                    "new_column": target_column.name,
                    "old_position": old_position,
                    "new_position": card.position
                },
                current_user
            )

            # Send enhanced real-time update
            await notification_manager.notify_card_move(
                str(board_id),
                {
                    "card_id": str(card.id),
                    "card_title": card.title,
                    "old_column_id": str(old_column_id),
                    "old_column_name": old_column.name if old_column else "Unknown",
                    "new_column_id": str(card.column_id),
                    "new_column_name": target_column.name,
                    "old_position": old_position,
                    "new_position": card.position,
                    "moved_by": current_user.email,
                    "moved_by_name": f"{current_user.first_name} {current_user.last_name}",
                    "timestamp": datetime.utcnow().isoformat(),
                    "is_completed": card.completed_date is not None
                }
            )

            # Notify assignee if card was assigned and moved
            if card.assignee_id and card.assignee_id != current_user.user_id:
                await notification_manager.notify_user(
                    str(card.assignee_id),
                    "card_moved",
                    {
                        "card_id": str(card.id),
                        "card_title": card.title,
                        "new_column": target_column.name,
                        "moved_by": current_user.email,
                        "board_name": board.name
                    }
                )

            logger.info(f"Kanban card {card.id} moved from {old_column.name if old_column else 'Unknown'} to {target_column.name} by {current_user.user_id}")
            return KanbanCardResponse.from_orm(card)

        except HTTPException:
            raise
        except Exception as e:
            db.rollback()
            logger.error(f"Error moving Kanban card: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Error moving Kanban card"
            )

    async def bulk_move_cards(
        self,
        db: Session,
        board_id: UUID,
        bulk_move: BulkCardMove,
        current_user: CurrentUser
    ) -> Dict[str, Any]:
        """Move multiple cards at once"""
        try:
            # Check board access
            board = await self._get_accessible_board(db, board_id, current_user)
            if not board:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Board not found or access denied"
                )

            moved_cards = []
            failed_moves = []

            for move_request in bulk_move.moves:
                try:
                    moved_card = await self.move_card(db, board_id, move_request, current_user)
                    moved_cards.append({
                        "card_id": str(moved_card.id),
                        "status": "success"
                    })
                except Exception as e:
                    failed_moves.append({
                        "card_id": str(move_request.card_id),
                        "error": str(e),
                        "status": "failed"
                    })

            # Send bulk update notification
            await notification_manager.notify_kanban_update(
                str(board_id),
                {
                    "type": "bulk_card_move",
                    "moved_count": len(moved_cards),
                    "failed_count": len(failed_moves),
                    "moved_by": current_user.email,
                    "timestamp": datetime.utcnow().isoformat()
                }
            )

            return {
                "total_requested": len(bulk_move.moves),
                "successful_moves": len(moved_cards),
                "failed_moves": len(failed_moves),
                "moved_cards": moved_cards,
                "failed_cards": failed_moves
            }

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error in bulk card move: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Error in bulk card move operation"
            )

    async def _log_card_activity(
        self,
        db: Session,
        card_id: UUID,
        activity_type: str,
        activity_data: Dict[str, Any],
        current_user: CurrentUser
    ):
        """Log card activity for audit trail"""
        try:
            # This could be expanded to store in a dedicated activity log table
            # For now, we'll just log it
            logger.info(f"Card Activity - Card: {card_id}, Type: {activity_type}, "
                       f"Data: {activity_data}, User: {current_user.user_id}")

            # Could also send to external audit system or store in database
            # activity_log = CardActivityLog(
            #     card_id=card_id,
            #     activity_type=activity_type,
            #     activity_data=activity_data,
            #     performed_by=current_user.user_id,
            #     performed_at=datetime.utcnow()
            # )
            # db.add(activity_log)

        except Exception as e:
            logger.error(f"Error logging card activity: {e}")
            # Don't raise exception for logging failures

    # Helper methods
    async def _get_accessible_board(
        self,
        db: Session,
        board_id: UUID,
        current_user: CurrentUser
    ) -> Optional[KanbanBoard]:
        """Get board if user has access"""
        return db.query(KanbanBoard).filter(
            KanbanBoard.id == board_id,
            KanbanBoard.organization_id == current_user.organization_id,
            or_(
                KanbanBoard.is_public == True,
                KanbanBoard.owner_id == current_user.user_id,
                KanbanBoard.id.in_(
                    db.query(KanbanBoardMember.board_id).filter(
                        KanbanBoardMember.employee_id == current_user.user_id
                    )
                )
            )
        ).first()

    async def _create_default_columns(
        self,
        db: Session,
        board_id: UUID,
        current_user: CurrentUser
    ):
        """Create default columns for new board"""
        default_columns = [
            {"name": "Backlog", "position": 0, "is_backlog_column": True, "color": "#6B7280"},
            {"name": "To Do", "position": 1, "color": "#EF4444"},
            {"name": "In Progress", "position": 2, "color": "#F59E0B"},
            {"name": "Review", "position": 3, "color": "#8B5CF6"},
            {"name": "Done", "position": 4, "is_done_column": True, "color": "#10B981"}
        ]

        for col_data in default_columns:
            column = KanbanColumn(
                board_id=board_id,
                **col_data,
                created_by=current_user.user_id
            )
            db.add(column)

        db.commit()

    async def _add_board_member(
        self,
        db: Session,
        board_id: UUID,
        employee_id: UUID,
        is_admin: bool = False,
        current_user: CurrentUser = None
    ):
        """Add member to board"""
        member = KanbanBoardMember(
            board_id=board_id,
            employee_id=employee_id,
            can_edit_board=is_admin,
            can_add_cards=True,
            can_edit_cards=True,
            can_delete_cards=is_admin,
            can_add_members=is_admin,
            role="admin" if is_admin else "member",
            created_by=current_user.user_id if current_user else employee_id
        )
        db.add(member)
        db.commit()

    async def _reorder_cards_in_column(
        self,
        db: Session,
        column_id: UUID,
        position: int,
        operation: str  # "insert" or "remove"
    ):
        """Reorder cards when inserting or removing"""
        if operation == "insert":
            # Shift cards down to make space
            db.query(KanbanCard).filter(
                KanbanCard.column_id == column_id,
                KanbanCard.position >= position
            ).update({
                KanbanCard.position: KanbanCard.position + 1
            })
        elif operation == "remove":
            # Shift cards up to fill gap
            db.query(KanbanCard).filter(
                KanbanCard.column_id == column_id,
                KanbanCard.position > position
            ).update({
                KanbanCard.position: KanbanCard.position - 1
            })
