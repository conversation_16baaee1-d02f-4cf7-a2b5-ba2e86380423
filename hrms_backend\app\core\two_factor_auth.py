import pyotp
import qrcode
import io
import base64
import secrets
import string
from typing import Optional, List, Tuple
from datetime import datetime, timed<PERSON>ta
from sqlalchemy.orm import Session
from fastapi import HTTPException, status
import logging

from ..db.models.user import User, RefreshToken
from .config import settings

logger = logging.getLogger(__name__)


class TwoFactorAuthManager:
    """Two-Factor Authentication manager"""

    @staticmethod
    def generate_secret() -> str:
        """Generate a new TOTP secret"""
        return pyotp.random_base32()

    @staticmethod
    def generate_qr_code(user_email: str, secret: str) -> str:
        """Generate QR code for TOTP setup"""
        try:
            # Create TOTP URI
            totp_uri = pyotp.totp.TOTP(secret).provisioning_uri(
                name=user_email,
                issuer_name=settings.APP_NAME
            )
            
            # Generate QR code
            qr = qrcode.QRCode(version=1, box_size=10, border=5)
            qr.add_data(totp_uri)
            qr.make(fit=True)
            
            # Create QR code image
            img = qr.make_image(fill_color="black", back_color="white")
            
            # Convert to base64 string
            buffer = io.BytesIO()
            img.save(buffer, format='PNG')
            img_str = base64.b64encode(buffer.getvalue()).decode()
            
            return f"data:image/png;base64,{img_str}"
        except Exception as e:
            logger.error(f"Error generating QR code: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to generate QR code"
            )

    @staticmethod
    def verify_totp_token(secret: str, token: str, window: int = 1) -> bool:
        """Verify TOTP token"""
        try:
            totp = pyotp.TOTP(secret)
            return totp.verify(token, valid_window=window)
        except Exception as e:
            logger.error(f"Error verifying TOTP token: {e}")
            return False

    @staticmethod
    def generate_backup_codes(count: int = 10) -> List[str]:
        """Generate backup codes for 2FA"""
        codes = []
        for _ in range(count):
            # Generate 8-character alphanumeric code
            code = ''.join(secrets.choice(string.ascii_uppercase + string.digits) for _ in range(8))
            codes.append(code)
        return codes

    @staticmethod
    def verify_backup_code(user: User, code: str, db: Session) -> bool:
        """Verify and consume a backup code"""
        try:
            if not user.backup_codes:
                return False
            
            backup_codes = user.backup_codes
            if code.upper() in backup_codes:
                # Remove the used backup code
                backup_codes.remove(code.upper())
                user.backup_codes = backup_codes
                db.commit()
                return True
            return False
        except Exception as e:
            logger.error(f"Error verifying backup code: {e}")
            db.rollback()
            return False

    @staticmethod
    def enable_2fa(user: User, totp_token: str, db: Session) -> Tuple[bool, Optional[List[str]]]:
        """Enable 2FA for a user"""
        try:
            if not user.two_fa_secret:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="2FA secret not found. Please setup 2FA first."
                )
            
            # Verify the TOTP token
            if not TwoFactorAuthManager.verify_totp_token(user.two_fa_secret, totp_token):
                return False, None
            
            # Generate backup codes
            backup_codes = TwoFactorAuthManager.generate_backup_codes()
            
            # Enable 2FA
            user.two_fa_enabled = True
            user.backup_codes = backup_codes
            db.commit()
            
            return True, backup_codes
        except Exception as e:
            logger.error(f"Error enabling 2FA: {e}")
            db.rollback()
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to enable 2FA"
            )

    @staticmethod
    def disable_2fa(user: User, password: str, db: Session) -> bool:
        """Disable 2FA for a user"""
        try:
            from .security import SecurityManager
            
            # Verify password
            if not SecurityManager.verify_password(password, user.password_hash):
                return False
            
            # Disable 2FA
            user.two_fa_enabled = False
            user.two_fa_secret = None
            user.backup_codes = None
            db.commit()
            
            return True
        except Exception as e:
            logger.error(f"Error disabling 2FA: {e}")
            db.rollback()
            return False

    @staticmethod
    def setup_2fa(user: User, db: Session) -> Tuple[str, str]:
        """Setup 2FA for a user (generate secret and QR code)"""
        try:
            # Generate new secret
            secret = TwoFactorAuthManager.generate_secret()
            
            # Save secret to user (but don't enable 2FA yet)
            user.two_fa_secret = secret
            user.two_fa_enabled = False
            db.commit()
            
            # Generate QR code
            qr_code = TwoFactorAuthManager.generate_qr_code(user.email, secret)
            
            return secret, qr_code
        except Exception as e:
            logger.error(f"Error setting up 2FA: {e}")
            db.rollback()
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to setup 2FA"
            )


class SMSAuthManager:
    """SMS-based authentication manager"""

    @staticmethod
    def generate_sms_code() -> str:
        """Generate 6-digit SMS verification code"""
        return ''.join(secrets.choice(string.digits) for _ in range(6))

    @staticmethod
    async def send_sms_code(phone_number: str, code: str) -> bool:
        """Send SMS verification code"""
        try:
            # TODO: Integrate with SMS service (Twilio, AWS SNS, etc.)
            # For now, just log the code (in production, remove this)
            logger.info(f"SMS code for {phone_number}: {code}")
            
            # Simulate SMS sending
            return True
        except Exception as e:
            logger.error(f"Error sending SMS code: {e}")
            return False

    @staticmethod
    def verify_sms_code(stored_code: str, provided_code: str, timestamp: datetime) -> bool:
        """Verify SMS code with expiration check"""
        try:
            # Check if code has expired (5 minutes)
            if datetime.utcnow() - timestamp > timedelta(minutes=5):
                return False
            
            return stored_code == provided_code
        except Exception as e:
            logger.error(f"Error verifying SMS code: {e}")
            return False


class EmailAuthManager:
    """Email-based authentication manager"""

    @staticmethod
    def generate_email_code() -> str:
        """Generate 6-digit email verification code"""
        return ''.join(secrets.choice(string.digits) for _ in range(6))

    @staticmethod
    async def send_email_code(email: str, code: str) -> bool:
        """Send email verification code"""
        try:
            # TODO: Integrate with email service (SendGrid, AWS SES, etc.)
            # For now, just log the code (in production, remove this)
            logger.info(f"Email code for {email}: {code}")
            
            # Simulate email sending
            return True
        except Exception as e:
            logger.error(f"Error sending email code: {e}")
            return False

    @staticmethod
    def verify_email_code(stored_code: str, provided_code: str, timestamp: datetime) -> bool:
        """Verify email code with expiration check"""
        try:
            # Check if code has expired (10 minutes)
            if datetime.utcnow() - timestamp > timedelta(minutes=10):
                return False
            
            return stored_code == provided_code
        except Exception as e:
            logger.error(f"Error verifying email code: {e}")
            return False


class RefreshTokenManager:
    """Refresh token management"""

    @staticmethod
    def create_refresh_token(user_id: str, device_info: dict, ip_address: str, db: Session) -> str:
        """Create and store refresh token"""
        try:
            from .security import SecurityManager
            
            # Generate refresh token
            token_data = {"sub": user_id, "type": "refresh"}
            token = SecurityManager.create_refresh_token(token_data)
            
            # Store in database
            refresh_token = RefreshToken(
                token=token,
                user_id=user_id,
                expires_at=datetime.utcnow() + timedelta(days=settings.REFRESH_TOKEN_EXPIRE_DAYS),
                device_info=device_info,
                ip_address=ip_address
            )
            
            db.add(refresh_token)
            db.commit()
            
            return token
        except Exception as e:
            logger.error(f"Error creating refresh token: {e}")
            db.rollback()
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to create refresh token"
            )

    @staticmethod
    def verify_refresh_token(token: str, db: Session) -> Optional[RefreshToken]:
        """Verify refresh token"""
        try:
            refresh_token = db.query(RefreshToken).filter(
                RefreshToken.token == token,
                RefreshToken.is_revoked == False,
                RefreshToken.expires_at > datetime.utcnow()
            ).first()
            
            return refresh_token
        except Exception as e:
            logger.error(f"Error verifying refresh token: {e}")
            return None

    @staticmethod
    def revoke_refresh_token(token: str, db: Session) -> bool:
        """Revoke refresh token"""
        try:
            refresh_token = db.query(RefreshToken).filter(RefreshToken.token == token).first()
            if refresh_token:
                refresh_token.is_revoked = True
                refresh_token.revoked_at = datetime.utcnow()
                db.commit()
                return True
            return False
        except Exception as e:
            logger.error(f"Error revoking refresh token: {e}")
            db.rollback()
            return False

    @staticmethod
    def revoke_all_user_tokens(user_id: str, db: Session) -> bool:
        """Revoke all refresh tokens for a user"""
        try:
            db.query(RefreshToken).filter(
                RefreshToken.user_id == user_id,
                RefreshToken.is_revoked == False
            ).update({
                "is_revoked": True,
                "revoked_at": datetime.utcnow()
            })
            db.commit()
            return True
        except Exception as e:
            logger.error(f"Error revoking all user tokens: {e}")
            db.rollback()
            return False
