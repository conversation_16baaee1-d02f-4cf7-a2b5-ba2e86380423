from core.databases.database import db
from core.models.basemodel import ModelBase
from datetime import datetime

class ApprovalHistoryModel(ModelBase):
    __tablename__ = "approval_histories"

    id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    reason = db.Column(db.String(200), nullable=True)
    status = db.Column(db.String(50), nullable=True)
    approval_id = db.Column(db.Integer, db.<PERSON>ey(ModelBase.dbSchema() + '.approvals.id', ondelete='CASCADE'),nullable=False)
    pay_schedules_id = db.Column(db.<PERSON><PERSON><PERSON>, db.ForeignKey((ModelBase.dbSchema() + '.pay_schedules.id')), nullable=False) 
    employee_id = db.Column(db.Integer, db.ForeignKey((ModelBase.dbSchema() + '.employees.id')), nullable=False)
    created_at = db.Column(db.DateTime, nullable=False, default=datetime.now())
    date_approved = db.Column(db.DateTime, nullable=True )
    pay_schedule = db.relationship("PaySchedulesModel", backref="approval_histories", lazy="joined")
    employee = db.relationship("EmployeeModel", backref="approval_histories", lazy="joined")



