from fastapi import <PERSON><PERSON><PERSON><PERSON>, WebSocket, WebSocketDisconnect, Depends, HTTPException, status
from typing import Optional
import logging
import json

from ..core.security import get_current_user_from_token, CurrentUser
from ..services.ticket_management.realtime_notification_service import RealtimeNotificationService
from ..core.websocket_manager import ConnectionManager

logger = logging.getLogger(__name__)

router = APIRouter()
notification_service = RealtimeNotificationService()
connection_manager = ConnectionManager()


@router.websocket("/notifications")
async def websocket_notifications(
    websocket: WebSocket,
    token: Optional[str] = None
):
    """WebSocket endpoint for real-time ticket notifications"""
    try:
        # Authenticate user from token
        if not token:
            await websocket.close(code=status.WS_1008_POLICY_VIOLATION, reason="Authentication required")
            return
        
        try:
            current_user = await get_current_user_from_token(token)
        except Exception as e:
            await websocket.close(code=status.WS_1008_POLICY_VIOLATION, reason="Invalid authentication")
            return
        
        # Connect user to notifications
        await notification_service.connect_user(
            websocket,
            current_user.user_id,
            current_user.organization_id,
            current_user.role
        )
        
        try:
            while True:
                # Listen for incoming messages
                data = await websocket.receive_text()
                message = json.loads(data)
                
                # Handle different message types
                await handle_websocket_message(websocket, current_user, message)
                
        except WebSocketDisconnect:
            logger.info(f"WebSocket disconnected for user {current_user.user_id}")
        except Exception as e:
            logger.error(f"WebSocket error for user {current_user.user_id}: {e}")
        finally:
            await notification_service.disconnect_user(websocket, current_user.user_id)
            
    except Exception as e:
        logger.error(f"WebSocket connection error: {e}")
        try:
            await websocket.close(code=status.WS_1011_INTERNAL_ERROR, reason="Internal server error")
        except:
            pass


async def handle_websocket_message(
    websocket: WebSocket,
    current_user: CurrentUser,
    message: dict
):
    """Handle incoming WebSocket messages"""
    try:
        message_type = message.get("type")
        
        if message_type == "ping":
            # Respond to ping with pong
            await websocket.send_text(json.dumps({
                "type": "pong",
                "timestamp": message.get("timestamp")
            }))
            
        elif message_type == "subscribe":
            # Subscribe to specific notification types
            notification_types = message.get("notification_types", [])
            # Update user subscription preferences
            # This would be implemented based on your requirements
            
        elif message_type == "unsubscribe":
            # Unsubscribe from specific notification types
            notification_types = message.get("notification_types", [])
            # Update user subscription preferences
            # This would be implemented based on your requirements
            
        elif message_type == "mark_read":
            # Mark notifications as read
            notification_ids = message.get("notification_ids", [])
            for notification_id in notification_ids:
                await notification_service.mark_notification_read(
                    current_user.user_id, notification_id
                )
            
        elif message_type == "get_notifications":
            # Get recent notifications
            notifications = await notification_service.get_user_notifications(
                current_user.user_id, message.get("limit", 50)
            )
            await websocket.send_text(json.dumps({
                "type": "notifications",
                "data": notifications
            }))
            
        else:
            # Unknown message type
            await websocket.send_text(json.dumps({
                "type": "error",
                "message": f"Unknown message type: {message_type}"
            }))
            
    except Exception as e:
        logger.error(f"Error handling WebSocket message: {e}")
        await websocket.send_text(json.dumps({
            "type": "error",
            "message": "Error processing message"
        }))


@router.get("/notifications/active-users")
async def get_active_users(
    current_user: CurrentUser = Depends(get_current_user)
):
    """Get list of currently active users"""
    try:
        active_users = await connection_manager.get_active_users(current_user.organization_id)
        return {
            "active_users": active_users,
            "total_count": len(active_users)
        }
    except Exception as e:
        logger.error(f"Error getting active users: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error retrieving active users"
        )


@router.get("/notifications/stats")
async def get_notification_stats(
    current_user: CurrentUser = Depends(get_current_user)
):
    """Get WebSocket connection statistics"""
    try:
        stats = await connection_manager.get_connection_stats()
        return stats
    except Exception as e:
        logger.error(f"Error getting notification stats: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error retrieving notification statistics"
        )


@router.post("/notifications/broadcast")
async def broadcast_notification(
    message: dict,
    organization_id: Optional[str] = None,
    current_user: CurrentUser = Depends(get_current_user)
):
    """Broadcast notification to organization (admin only)"""
    try:
        # Check if user has admin permissions
        if current_user.role not in ["SUPER_ADMIN", "ADMIN"]:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Insufficient permissions"
            )
        
        target_org = organization_id or current_user.organization_id
        
        # Broadcast message
        sent_count = await connection_manager.broadcast_to_organization(
            target_org, message
        )
        
        return {
            "message": "Notification broadcasted successfully",
            "recipients": sent_count
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error broadcasting notification: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error broadcasting notification"
        )


@router.post("/notifications/test")
async def test_notification(
    notification_type: str,
    test_data: dict,
    current_user: CurrentUser = Depends(get_current_user)
):
    """Test notification system (admin only)"""
    try:
        # Check if user has admin permissions
        if current_user.role not in ["SUPER_ADMIN", "ADMIN"]:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Insufficient permissions"
            )
        
        # Send test notification
        test_notification = {
            "type": f"test_{notification_type}",
            "message": f"Test notification: {notification_type}",
            "test_data": test_data,
            "sent_by": f"{current_user.first_name} {current_user.last_name}",
            "timestamp": "2024-01-01T00:00:00Z"
        }
        
        success = await notification_service._send_to_user(
            current_user.user_id, test_notification
        )
        
        return {
            "message": "Test notification sent",
            "success": success,
            "notification": test_notification
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error sending test notification: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error sending test notification"
        )


@router.delete("/notifications/cleanup")
async def cleanup_stale_connections(
    max_idle_minutes: int = 30,
    current_user: CurrentUser = Depends(get_current_user)
):
    """Clean up stale WebSocket connections (admin only)"""
    try:
        # Check if user has admin permissions
        if current_user.role not in ["SUPER_ADMIN", "ADMIN"]:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Insufficient permissions"
            )
        
        cleaned_count = await connection_manager.cleanup_stale_connections(max_idle_minutes)
        
        return {
            "message": "Stale connections cleaned up",
            "cleaned_connections": cleaned_count
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error cleaning up connections: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error cleaning up connections"
        )


@router.post("/notifications/ping-all")
async def ping_all_connections(
    current_user: CurrentUser = Depends(get_current_user)
):
    """Ping all WebSocket connections to check health (admin only)"""
    try:
        # Check if user has admin permissions
        if current_user.role not in ["SUPER_ADMIN", "ADMIN"]:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Insufficient permissions"
            )
        
        ping_results = await connection_manager.ping_all_connections()
        
        return {
            "message": "Ping completed",
            "results": ping_results
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error pinging connections: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error pinging connections"
        )
