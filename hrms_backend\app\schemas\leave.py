from pydantic import BaseModel, <PERSON>, validator
from typing import Optional, List
from uuid import UUID
from datetime import date, datetime
from decimal import Decimal
from enum import Enum


class LeaveType(str, Enum):
    ANNUAL = "annual"
    SICK = "sick"
    MATERNITY = "maternity"
    PATERNITY = "paternity"
    PERSONAL = "personal"
    EMERGENCY = "emergency"
    BEREAVEMENT = "bereavement"
    STUDY = "study"
    SABBATICAL = "sabbatical"
    UNPAID = "unpaid"


class LeaveStatus(str, Enum):
    PENDING = "PENDING"
    APPROVED = "APPROVED"
    REJECTED = "REJECTED"
    CANCELLED = "CANCELLED"
    WITHDRAWN = "WITHDRAWN"


class LeaveDuration(str, Enum):
    FULL_DAY = "FULL_DAY"
    HALF_DAY_MORNING = "HALF_DAY_MORNING"
    HALF_DAY_AFTERNOON = "HALF_DAY_AFTERNOON"
    HOURLY = "HOURLY"


# Leave Policy Schemas
class LeavePolicyBase(BaseModel):
    name: str = Field(..., min_length=1, max_length=200)
    leave_type: LeaveType
    annual_entitlement: Decimal = Field(..., ge=0)
    max_carry_forward: Optional[Decimal] = Field(0, ge=0)
    max_accumulation: Optional[Decimal] = None
    accrual_frequency: str = Field("monthly", pattern="^(monthly|quarterly|yearly|MONTHLY|QUARTERLY|YEARLY)$")
    accrual_start_date: str = Field("hire_date", pattern="^(hire_date|calendar_year|\\d{4}-\\d{2}-\\d{2})$")
    min_notice_days: int = Field(1, ge=0)
    max_consecutive_days: Optional[int] = Field(None, ge=1)
    min_application_days: Decimal = Field(0.5, ge=0)


class LeavePolicyCreate(LeavePolicyBase):
    requires_approval: bool = True
    auto_approve_threshold: Optional[int] = Field(None, ge=1)
    requires_documentation: bool = False
    documentation_threshold: Optional[int] = Field(None, ge=1)
    applicable_genders: Optional[List[str]] = None
    applicable_employment_types: Optional[List[str]] = None
    available_during_probation: bool = True
    probation_entitlement: Optional[Decimal] = Field(None, ge=0)


class LeavePolicyUpdate(BaseModel):
    name: Optional[str] = Field(None, min_length=1, max_length=200)
    annual_entitlement: Optional[Decimal] = Field(None, ge=0)
    max_carry_forward: Optional[Decimal] = Field(None, ge=0)
    max_accumulation: Optional[Decimal] = None
    min_notice_days: Optional[int] = Field(None, ge=0)
    max_consecutive_days: Optional[int] = Field(None, ge=1)
    min_application_days: Optional[Decimal] = Field(None, ge=0)
    requires_approval: Optional[bool] = None
    auto_approve_threshold: Optional[int] = Field(None, ge=1)
    requires_documentation: Optional[bool] = None
    documentation_threshold: Optional[int] = Field(None, ge=1)
    available_during_probation: Optional[bool] = None
    probation_entitlement: Optional[Decimal] = Field(None, ge=0)


class LeavePolicyResponse(LeavePolicyBase):
    id: UUID
    organization_id: UUID
    requires_approval: bool
    auto_approve_threshold: Optional[int] = None
    requires_documentation: bool
    documentation_threshold: Optional[int] = None
    applicable_genders: Optional[List[str]] = None
    applicable_employment_types: Optional[List[str]] = None
    available_during_probation: bool
    probation_entitlement: Optional[Decimal] = None
    is_active: bool
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


# Leave Balance Schemas
class LeaveBalanceResponse(BaseModel):
    id: UUID
    employee_id: UUID
    leave_policy_id: UUID
    year: int
    opening_balance: Decimal
    accrued_balance: Decimal
    used_balance: Decimal
    pending_balance: Decimal
    carried_forward: Decimal
    available_balance: Decimal

    # Related data
    leave_policy: Optional[LeavePolicyResponse] = None

    class Config:
        from_attributes = True


# Leave Request Schemas
class LeaveRequestBase(BaseModel):
    start_date: date
    end_date: date
    duration_type: LeaveDuration = LeaveDuration.FULL_DAY
    reason: str = Field(..., min_length=1, max_length=1000)
    contact_number: Optional[str] = Field(None, max_length=20)
    emergency_contact: Optional[str] = Field(None, max_length=200)
    handover_notes: Optional[str] = None
    handover_to: Optional[UUID] = None

    @validator('end_date')
    def end_date_must_be_after_start_date(cls, v, values):
        if 'start_date' in values and v < values['start_date']:
            raise ValueError('End date must be after or equal to start date')
        return v


class LeaveRequestCreate(LeaveRequestBase):
    leave_policy_id: UUID
    attachment_urls: Optional[List[str]] = None


class LeaveRequestUpdate(BaseModel):
    start_date: Optional[date] = None
    end_date: Optional[date] = None
    duration_type: Optional[LeaveDuration] = None
    reason: Optional[str] = Field(None, min_length=1, max_length=1000)
    contact_number: Optional[str] = Field(None, max_length=20)
    emergency_contact: Optional[str] = Field(None, max_length=200)
    handover_notes: Optional[str] = None
    handover_to: Optional[UUID] = None
    attachment_urls: Optional[List[str]] = None


class LeaveRequestResponse(LeaveRequestBase):
    id: UUID
    employee_id: UUID
    leave_policy_id: UUID
    total_days: Decimal
    status: LeaveStatus
    applied_at: datetime
    approved_by: Optional[UUID] = None
    approved_at: Optional[datetime] = None
    rejection_reason: Optional[str] = None
    hr_processed_by: Optional[UUID] = None
    hr_processed_at: Optional[datetime] = None
    hr_notes: Optional[str] = None
    cancelled_by: Optional[UUID] = None
    cancelled_at: Optional[datetime] = None
    cancellation_reason: Optional[str] = None
    attachment_urls: Optional[List[str]] = None

    # Related data
    leave_policy: Optional[LeavePolicyResponse] = None
    employee_name: Optional[str] = None
    employee_email: Optional[str] = None
    department: Optional[str] = None
    leave_type_name: Optional[str] = None
    leave_type: Optional[str] = None

    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class LeaveRequestListResponse(BaseModel):
    requests: List[LeaveRequestResponse]
    total: int
    skip: int
    limit: int


# Leave Approval Schemas
class LeaveApprovalRequest(BaseModel):
    leave_request_ids: List[UUID]
    approved: bool
    comments: Optional[str] = Field(None, max_length=1000)


class LeaveApprovalResponse(BaseModel):
    leave_request_id: UUID
    approved: bool
    approved_by: UUID
    approved_at: datetime
    comments: Optional[str] = None


# Leave Calendar Schemas
class LeaveCalendarBase(BaseModel):
    date: date
    name: str = Field(..., min_length=1, max_length=200)
    description: Optional[str] = None
    is_optional: bool = False
    holiday_type: str = Field("public", pattern="^(public|religious|regional)$")


class LeaveCalendarCreate(LeaveCalendarBase):
    applicable_locations: Optional[List[str]] = None
    applicable_departments: Optional[List[UUID]] = None


class LeaveCalendarUpdate(BaseModel):
    name: Optional[str] = Field(None, min_length=1, max_length=200)
    description: Optional[str] = None
    is_optional: Optional[bool] = None
    holiday_type: Optional[str] = Field(None, pattern="^(public|religious|regional)$")
    applicable_locations: Optional[List[str]] = None
    applicable_departments: Optional[List[UUID]] = None


class LeaveCalendarResponse(LeaveCalendarBase):
    id: UUID
    organization_id: UUID
    applicable_locations: Optional[List[str]] = None
    applicable_departments: Optional[List[UUID]] = None
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


# Leave Encashment Schemas
class LeaveEncashmentBase(BaseModel):
    days_to_encash: Decimal = Field(..., gt=0)
    reason: Optional[str] = Field(None, max_length=1000)


class LeaveEncashmentCreate(LeaveEncashmentBase):
    leave_policy_id: UUID


class LeaveEncashmentResponse(LeaveEncashmentBase):
    id: UUID
    employee_id: UUID
    leave_policy_id: UUID
    rate_per_day: Decimal
    total_amount: Decimal
    status: LeaveStatus
    requested_at: datetime
    approved_by: Optional[UUID] = None
    approved_at: Optional[datetime] = None
    processed_by: Optional[UUID] = None
    processed_at: Optional[datetime] = None
    payment_reference: Optional[str] = None

    # Related data
    leave_policy: Optional[LeavePolicyResponse] = None

    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


# Leave Summary and Reports
class LeaveSummary(BaseModel):
    employee_id: UUID
    leave_type: LeaveType
    total_entitlement: Decimal
    used_days: Decimal
    pending_days: Decimal
    available_days: Decimal
    carry_forward: Decimal


class LeaveReport(BaseModel):
    period_start: date
    period_end: date
    summaries: List[LeaveSummary]
    total_employees: int


# Bulk Operations
class BulkLeaveApproval(BaseModel):
    leave_request_ids: List[UUID]
    approved: bool
    comments: Optional[str] = None


class LeaveBalanceAdjustment(BaseModel):
    employee_id: UUID
    leave_policy_id: UUID
    adjustment_days: Decimal
    reason: str = Field(..., min_length=1, max_length=500)
    adjustment_type: str = Field(..., pattern="^(add|deduct)$")
