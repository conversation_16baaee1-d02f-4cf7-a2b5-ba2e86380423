from fastapi import Request, Response, HTTPException, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.responses import JSONResponse
from typing import Optional, Dict, Any, List
import time
import hashlib
import hmac
import logging
from datetime import datetime, timedelta
from collections import defaultdict
import ipaddress
import re

from .config import settings
from .audit_logger import AuditLogger
from .encryption import encryption_manager

logger = logging.getLogger(__name__)


class SecurityHeaders:
    """Security headers for HTTP responses"""
    
    @staticmethod
    def get_security_headers() -> Dict[str, str]:
        """Get standard security headers"""
        return {
            # Prevent XSS attacks
            "X-Content-Type-Options": "nosniff",
            "X-Frame-Options": "DENY",
            "X-XSS-Protection": "1; mode=block",
            
            # HTTPS enforcement
            "Strict-Transport-Security": "max-age=31536000; includeSubDomains; preload",
            
            # Content Security Policy
            "Content-Security-Policy": (
                "default-src 'self'; "
                "script-src 'self' 'unsafe-inline' 'unsafe-eval'; "
                "style-src 'self' 'unsafe-inline'; "
                "img-src 'self' data: https:; "
                "font-src 'self' https:; "
                "connect-src 'self' https:; "
                "frame-ancestors 'none';"
            ),
            
            # Referrer policy
            "Referrer-Policy": "strict-origin-when-cross-origin",
            
            # Permissions policy
            "Permissions-Policy": (
                "geolocation=(), "
                "microphone=(), "
                "camera=(), "
                "payment=(), "
                "usb=(), "
                "magnetometer=(), "
                "gyroscope=(), "
                "speaker=()"
            ),
            
            # Cache control for sensitive data
            "Cache-Control": "no-store, no-cache, must-revalidate, private",
            "Pragma": "no-cache",
            "Expires": "0"
        }


class RateLimiter:
    """Rate limiting implementation"""
    
    def __init__(self):
        self.requests = defaultdict(list)
        self.blocked_ips = defaultdict(datetime)
        
    def is_rate_limited(self, client_ip: str, endpoint: str, limit: int = 60, window: int = 60) -> bool:
        """Check if client is rate limited"""
        now = time.time()
        key = f"{client_ip}:{endpoint}"
        
        # Clean old requests
        self.requests[key] = [req_time for req_time in self.requests[key] if now - req_time < window]
        
        # Check if blocked
        if client_ip in self.blocked_ips:
            if datetime.utcnow() < self.blocked_ips[client_ip]:
                return True
            else:
                del self.blocked_ips[client_ip]
        
        # Check rate limit
        if len(self.requests[key]) >= limit:
            # Block IP for 15 minutes
            self.blocked_ips[client_ip] = datetime.utcnow() + timedelta(minutes=15)
            logger.warning(f"Rate limit exceeded for IP {client_ip} on endpoint {endpoint}")
            return True
        
        # Add current request
        self.requests[key].append(now)
        return False


class IPWhitelist:
    """IP whitelist/blacklist management"""
    
    def __init__(self):
        self.whitelist = self._load_whitelist()
        self.blacklist = self._load_blacklist()
    
    def _load_whitelist(self) -> List[str]:
        """Load IP whitelist from configuration"""
        # This would typically load from database or config file
        return getattr(settings, 'IP_WHITELIST', [])
    
    def _load_blacklist(self) -> List[str]:
        """Load IP blacklist from configuration"""
        # This would typically load from database or config file
        return getattr(settings, 'IP_BLACKLIST', [])
    
    def is_ip_allowed(self, client_ip: str) -> bool:
        """Check if IP is allowed"""
        try:
            client_addr = ipaddress.ip_address(client_ip)
            
            # Check blacklist first
            for blocked_ip in self.blacklist:
                if client_addr in ipaddress.ip_network(blocked_ip, strict=False):
                    return False
            
            # If whitelist is empty, allow all (except blacklisted)
            if not self.whitelist:
                return True
            
            # Check whitelist
            for allowed_ip in self.whitelist:
                if client_addr in ipaddress.ip_network(allowed_ip, strict=False):
                    return True
            
            return False
            
        except ValueError:
            logger.error(f"Invalid IP address: {client_ip}")
            return False


class RequestValidator:
    """Request validation and sanitization"""
    
    @staticmethod
    def validate_content_type(request: Request) -> bool:
        """Validate content type for POST/PUT requests"""
        if request.method in ["POST", "PUT", "PATCH"]:
            content_type = request.headers.get("content-type", "")
            allowed_types = [
                "application/json",
                "application/x-www-form-urlencoded",
                "multipart/form-data"
            ]
            return any(allowed_type in content_type for allowed_type in allowed_types)
        return True
    
    @staticmethod
    def validate_user_agent(request: Request) -> bool:
        """Validate user agent to detect potential bots/attacks"""
        user_agent = request.headers.get("user-agent", "")
        
        # Block empty user agents
        if not user_agent:
            return False
        
        # Block known malicious patterns
        malicious_patterns = [
            r"sqlmap",
            r"nikto",
            r"nmap",
            r"masscan",
            r"zap",
            r"burp",
            r"<script",
            r"javascript:",
            r"vbscript:"
        ]
        
        for pattern in malicious_patterns:
            if re.search(pattern, user_agent, re.IGNORECASE):
                return False
        
        return True
    
    @staticmethod
    def validate_request_size(request: Request, max_size: int = 10 * 1024 * 1024) -> bool:
        """Validate request size (default 10MB)"""
        content_length = request.headers.get("content-length")
        if content_length:
            try:
                size = int(content_length)
                return size <= max_size
            except ValueError:
                return False
        return True


class SecurityMiddleware(BaseHTTPMiddleware):
    """Comprehensive security middleware"""
    
    def __init__(self, app, enable_rate_limiting: bool = True, enable_ip_filtering: bool = False):
        super().__init__(app)
        self.rate_limiter = RateLimiter() if enable_rate_limiting else None
        self.ip_whitelist = IPWhitelist() if enable_ip_filtering else None
        self.request_validator = RequestValidator()
        
    async def dispatch(self, request: Request, call_next):
        """Process request through security checks"""
        start_time = time.time()
        client_ip = self._get_client_ip(request)
        
        try:
            # Security checks
            security_check = await self._perform_security_checks(request, client_ip)
            if security_check:
                return security_check
            
            # Process request
            response = await call_next(request)
            
            # Add security headers
            self._add_security_headers(response)
            
            # Log request
            await self._log_request(request, response, client_ip, time.time() - start_time)
            
            return response
            
        except Exception as e:
            logger.error(f"Security middleware error: {e}")
            return JSONResponse(
                status_code=500,
                content={"detail": "Internal server error"}
            )
    
    async def _perform_security_checks(self, request: Request, client_ip: str) -> Optional[Response]:
        """Perform all security checks"""
        
        # IP filtering
        if self.ip_whitelist and not self.ip_whitelist.is_ip_allowed(client_ip):
            logger.warning(f"Blocked request from non-whitelisted IP: {client_ip}")
            return JSONResponse(
                status_code=403,
                content={"detail": "Access denied"}
            )
        
        # Rate limiting
        if self.rate_limiter:
            endpoint = f"{request.method}:{request.url.path}"
            if self.rate_limiter.is_rate_limited(client_ip, endpoint):
                return JSONResponse(
                    status_code=429,
                    content={"detail": "Rate limit exceeded"},
                    headers={"Retry-After": "900"}  # 15 minutes
                )
        
        # Request validation
        if not self.request_validator.validate_content_type(request):
            logger.warning(f"Invalid content type from {client_ip}: {request.headers.get('content-type')}")
            return JSONResponse(
                status_code=400,
                content={"detail": "Invalid content type"}
            )
        
        if not self.request_validator.validate_user_agent(request):
            logger.warning(f"Suspicious user agent from {client_ip}: {request.headers.get('user-agent')}")
            return JSONResponse(
                status_code=400,
                content={"detail": "Invalid user agent"}
            )
        
        if not self.request_validator.validate_request_size(request):
            logger.warning(f"Request too large from {client_ip}")
            return JSONResponse(
                status_code=413,
                content={"detail": "Request entity too large"}
            )
        
        return None
    
    def _get_client_ip(self, request: Request) -> str:
        """Get client IP address considering proxies"""
        # Check for forwarded headers (in order of preference)
        forwarded_headers = [
            "X-Forwarded-For",
            "X-Real-IP",
            "X-Client-IP",
            "CF-Connecting-IP"  # Cloudflare
        ]
        
        for header in forwarded_headers:
            if header in request.headers:
                # Take the first IP in case of multiple
                ip = request.headers[header].split(",")[0].strip()
                if ip:
                    return ip
        
        # Fallback to direct connection
        return request.client.host if request.client else "unknown"
    
    def _add_security_headers(self, response: Response):
        """Add security headers to response"""
        security_headers = SecurityHeaders.get_security_headers()
        for header, value in security_headers.items():
            response.headers[header] = value
    
    async def _log_request(self, request: Request, response: Response, client_ip: str, duration: float):
        """Log request for security monitoring"""
        try:
            # Log suspicious activities
            if response.status_code >= 400:
                logger.warning(
                    f"HTTP {response.status_code} - {request.method} {request.url.path} "
                    f"from {client_ip} - Duration: {duration:.3f}s"
                )
            
            # Log to audit trail for sensitive endpoints
            sensitive_paths = ["/api/auth/", "/api/admin/", "/api/users/"]
            if any(path in str(request.url.path) for path in sensitive_paths):
                # This would integrate with your audit logging system
                pass
                
        except Exception as e:
            logger.error(f"Error logging request: {e}")


class APIKeyValidator:
    """API key validation for external integrations"""
    
    def __init__(self):
        self.api_keys = self._load_api_keys()
    
    def _load_api_keys(self) -> Dict[str, Dict[str, Any]]:
        """Load API keys from secure storage"""
        # This would typically load from database
        return {}
    
    def validate_api_key(self, api_key: str, required_scopes: List[str] = None) -> Optional[Dict[str, Any]]:
        """Validate API key and check scopes"""
        if not api_key:
            return None
        
        # Hash the API key for lookup
        key_hash = hashlib.sha256(api_key.encode()).hexdigest()
        
        key_info = self.api_keys.get(key_hash)
        if not key_info:
            return None
        
        # Check if key is active
        if not key_info.get("active", False):
            return None
        
        # Check expiration
        expires_at = key_info.get("expires_at")
        if expires_at and datetime.utcnow() > expires_at:
            return None
        
        # Check scopes
        if required_scopes:
            key_scopes = key_info.get("scopes", [])
            if not all(scope in key_scopes for scope in required_scopes):
                return None
        
        return key_info


class RequestSignatureValidator:
    """Validate request signatures for webhook security"""
    
    @staticmethod
    def validate_signature(
        payload: bytes,
        signature: str,
        secret: str,
        algorithm: str = "sha256"
    ) -> bool:
        """Validate HMAC signature"""
        try:
            # Create expected signature
            expected_signature = hmac.new(
                secret.encode(),
                payload,
                getattr(hashlib, algorithm)
            ).hexdigest()
            
            # Compare signatures (constant time comparison)
            return hmac.compare_digest(signature, expected_signature)
            
        except Exception as e:
            logger.error(f"Error validating signature: {e}")
            return False


# Global instances
security_headers = SecurityHeaders()
api_key_validator = APIKeyValidator()
signature_validator = RequestSignatureValidator()
