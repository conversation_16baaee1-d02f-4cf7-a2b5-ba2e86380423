from core.databases.database import db
from core.models.basemodel import ModelBase
from datetime import datetime

class AnnouncementModel(ModelBase):
    __tablename__ = "announcements"

    id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    title = db.Column(db.String(45), nullable=False)
    timestamp = db.Column(db.DateTime, nullable=False, default=datetime.now()) 
    sent_to = db.Column(db.String(45), nullable=False)
    description = db.Column(db.String(2000), nullable=False)
    status = db.Column(db.String(45), nullable=False)
    date = db.Column(db.String(50), nullable=True)
    user_id = db.Column(db.Integer, db.ForeignKey((ModelBase.dbSchema() + '.users.id')), nullable=False) 
