#!/usr/bin/env python3
"""
Add a simple test employee for authentication testing
"""

import sys
import os
from uuid import uuid4
from datetime import datetime, date

# Add the app directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'app'))

from sqlalchemy.orm import Session
from sqlalchemy import text
from app.db.session import SessionLocal

def add_test_employee():
    """Add a simple test employee"""
    db = SessionLocal()
    
    try:
        print("Adding test employee...")
        
        # Check if employee already exists
        result = db.execute(text("SELECT first_name FROM employees WHERE first_name = 'Admin' LIMIT 1"))
        existing = result.fetchone()

        if existing:
            print("✅ Test employee already exists")
            return
        
        # Create test organization ID
        org_id = uuid4()
        
        # Insert a simple test employee directly
        employee_id = uuid4()
        
        insert_sql = text("""
            INSERT INTO employees (
                id, user_id, employee_id, first_name, last_name,
                hire_date, is_active, created_at, updated_at
            ) VALUES (
                :id, :user_id, :employee_id, :first_name, :last_name,
                :hire_date, :is_active, :created_at, :updated_at
            )
        """)
        
        db.execute(insert_sql, {
            'id': employee_id,
            'user_id': employee_id,  # Use same ID for user_id
            'employee_id': 'ADMIN001',
            'first_name': 'Admin',
            'last_name': 'User',
            'hire_date': date.today(),
            'is_active': True,
            'created_at': datetime.utcnow(),
            'updated_at': datetime.utcnow()
        })
        
        # Add another test employee
        employee_id2 = uuid4()
        
        db.execute(insert_sql, {
            'id': employee_id2,
            'user_id': employee_id2,  # Use same ID for user_id
            'employee_id': 'EMP001',
            'first_name': 'John',
            'last_name': 'Doe',
            'hire_date': date.today(),
            'is_active': True,
            'created_at': datetime.utcnow(),
            'updated_at': datetime.utcnow()
        })
        
        db.commit()
        print("✅ Test employees created successfully!")
        print("  - Admin User (ADMIN001)")
        print("  - John Doe (EMP001)")
        print(f"  - Organization ID: {org_id}")
        
    except Exception as e:
        print(f"❌ Error adding test employee: {e}")
        db.rollback()
        raise
    finally:
        db.close()

if __name__ == "__main__":
    add_test_employee()
