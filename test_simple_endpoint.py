#!/usr/bin/env python3
"""
Test the simple employees test endpoint
"""

import requests
import json

BASE_URL = "http://localhost:8085"

def test_simple_endpoint():
    """Test the simple employees test endpoint"""
    try:
        # Test the simple endpoint first
        response = requests.get(f"{BASE_URL}/api/employees/test")
        print(f"Test endpoint status: {response.status_code}")
        print(f"Test endpoint response: {response.text}")
        
        # Test with authentication
        credentials = {
            "email": "<EMAIL>",
            "password": "password123"
        }
        
        login_response = requests.post(f"{BASE_URL}/api/auth/login", json=credentials)
        if login_response.status_code == 200:
            token = login_response.json().get('access_token')
            headers = {"Authorization": f"Bearer {token}"}
            
            # Test authenticated endpoint
            response = requests.get(f"{BASE_URL}/api/employees/test", headers=headers)
            print(f"Authenticated test endpoint status: {response.status_code}")
            print(f"Authenticated test endpoint response: {response.text}")
        
    except Exception as e:
        print(f"Test failed: {e}")

if __name__ == "__main__":
    test_simple_endpoint()
