from fastapi import <PERSON><PERSON><PERSON>, WebSocket, WebSocketDisconnect, Depends
from fastapi.middleware.cors import CORSMiddleware
import uvicorn
import logging

# Import API routers
from .api import (
    auth, employee, attendance, leave, payroll,
    performance, shift, timesheet, project,
    kanban, ticket, delegation, engagement, reports
)
from .api import settings as settings_api
from .api import onboarding, simple_onboarding
# from .api import auth_enhanced, recruitment, lms, reporting
# from .api import shift_pattern, wfm_integration, dashboard, document, project_dashboard
# from .api import websocket

# Import WebSocket and core modules
from .core.websocket_manager import connection_manager
from .core.config import settings
from .core.security import get_current_user, require_permission
from .core.security_middleware import SecurityMiddleware
from .db.session import create_tables, test_connection

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Create FastAPI app
app = FastAPI(
    title="Agnoshin HRMS Backend",
    description="Human Resource Management System Backend API",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.ALLOWED_ORIGINS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Add security middleware
app.add_middleware(
    SecurityMiddleware,
    enable_rate_limiting=getattr(settings, 'RATE_LIMIT_ENABLED', True),
    enable_ip_filtering=getattr(settings, 'IP_FILTERING_ENABLED', False)
)

# Startup event
@app.on_event("startup")
async def startup_event():
    """Initialize application on startup"""
    logger.info("Starting AgnoConnect HRMS Backend...")

    # Test database connection
    if test_connection():
        logger.info("✅ Database connection successful!")
        create_tables()
        logger.info("✅ Database tables ready!")
    else:
        logger.error("❌ Database connection failed!")
        raise Exception("Could not connect to database")

    logger.info("✅ HRMS Backend started successfully!")

# Shutdown event
@app.on_event("shutdown")
async def shutdown_event():
    """Cleanup on shutdown"""
    logger.info("Shutting down AgnoConnect HRMS Backend...")

# Health check endpoint
@app.get("/health")
async def health_check():
    """Health check endpoint"""
    from datetime import datetime
    return {
        "status": "healthy",
        "timestamp": datetime.utcnow().isoformat(),
        "service": "HRMS Backend",
        "version": "1.0.0"
    }

# Include API routers
app.include_router(auth.router, prefix="/api/auth", tags=["authentication"])
app.include_router(employee.router, prefix="/api/employees", tags=["employees"])
app.include_router(attendance.router, prefix="/api/attendance", tags=["attendance"])
app.include_router(leave.router, prefix="/api/leave", tags=["leave"])
app.include_router(payroll.router, prefix="/api/payroll", tags=["payroll"])
app.include_router(performance.router, prefix="/api/performance", tags=["performance"])
app.include_router(shift.router, prefix="/api/shift", tags=["shift"])
app.include_router(timesheet.router, prefix="/api/timesheet", tags=["timesheet"])
app.include_router(project.router, prefix="/api/project", tags=["project"])
app.include_router(kanban.router, prefix="/api/kanban", tags=["kanban"])
app.include_router(ticket.router, prefix="/api/ticket", tags=["ticket"])
app.include_router(delegation.router, prefix="/api/delegation", tags=["delegation"])
app.include_router(engagement.router, prefix="/api/engagement", tags=["engagement"])
app.include_router(reports.router, prefix="/api/reports", tags=["reports"])
app.include_router(settings_api.router, prefix="/api/settings", tags=["settings"])
# app.include_router(project_dashboard.router, prefix="/api/project-management", tags=["project-management"])

# Include new enhanced API routers (temporarily disabled)
# app.include_router(auth_enhanced.router, prefix="/api/auth", tags=["enhanced-auth"])
# app.include_router(recruitment.router, prefix="/api/recruitment", tags=["recruitment"])
# app.include_router(lms.router, prefix="/api/lms", tags=["learning-management"])
app.include_router(onboarding.router, prefix="/api/onboarding", tags=["onboarding"])
app.include_router(simple_onboarding.router, prefix="/api/simple-onboarding", tags=["simple-onboarding"])
# app.include_router(reporting.router, prefix="/api/reporting", tags=["advanced-reporting"])

# Include WFM and enhanced shift management routers (temporarily disabled)
# app.include_router(shift_pattern.router, prefix="/api/shift-patterns", tags=["shift-patterns"])
# app.include_router(wfm_integration.router, prefix="/api/wfm", tags=["workforce-management"])
# app.include_router(dashboard.router, prefix="/api/dashboard", tags=["dashboard"])
# app.include_router(document.router, prefix="/api/documents", tags=["document-management"])

# Include WebSocket router (temporarily disabled)
# app.include_router(websocket.router, prefix="/api/websocket", tags=["websocket"])

# WebSocket endpoint for real-time features
@app.websocket("/ws/{user_id}")
async def websocket_endpoint(websocket: WebSocket, user_id: str, room: str = None):
    """WebSocket endpoint for real-time communication"""
    await connection_manager.connect(websocket, user_id, room)
    try:
        while True:
            # Receive messages from client
            data = await websocket.receive_text()

            # Handle ping/pong for connection keep-alive
            if data == "ping":
                await websocket.send_text("pong")
            else:
                # Handle other message types as needed
                logger.info(f"Received message from {user_id}: {data}")

    except WebSocketDisconnect:
        connection_manager.disconnect(websocket)
        logger.info(f"WebSocket disconnected for user {user_id}")

# Root endpoint
@app.get("/")
async def root():
    """Root endpoint"""
    return {
        "message": "Welcome to AgnoConnect HRMS Backend API",
        "version": "1.0.0",
        "docs": "/docs",
        "redoc": "/redoc"
    }

# Health check endpoint
@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy",
        "service": "hrms-backend",
        "version": "1.0.0"
    }

# WebSocket stats endpoint
@app.get("/ws/stats")
async def websocket_stats():
    """Get WebSocket connection statistics"""
    return connection_manager.get_connection_stats()

# Debug endpoint for testing authentication
@app.get("/debug/auth")
async def debug_auth(current_user = Depends(get_current_user)):
    """Debug endpoint to test authentication without RBAC"""
    return {
        "message": "Authentication successful",
        "user_id": current_user.user_id,
        "email": current_user.email,
        "role": current_user.role,
        "organization_id": current_user.organization_id
    }

# Debug endpoint for testing RBAC
@app.get("/debug/rbac")
async def debug_rbac(current_user = Depends(get_current_user)):
    """Debug endpoint to test RBAC permissions"""
    from .core.rbac import rbac_manager

    # Test specific permission
    has_employee_read = rbac_manager.check_permission(current_user.role, "employee", "read")

    return {
        "message": "RBAC test",
        "user_role": current_user.role,
        "has_employee_read": has_employee_read,
        "test_permissions": {
            "employee:read": rbac_manager.check_permission(current_user.role, "employee", "read"),
            "employee:create": rbac_manager.check_permission(current_user.role, "employee", "create"),
            "attendance:read": rbac_manager.check_permission(current_user.role, "attendance", "read"),
        }
    }

# Debug endpoint for testing require_permission
@app.get("/debug/permission")
async def debug_permission(current_user = Depends(require_permission("employee:read"))):
    """Debug endpoint to test require_permission function"""
    return {
        "message": "Permission check passed",
        "user_role": current_user.role,
        "required_permission": "employee:read"
    }

# Debug endpoint for testing employee service directly
@app.get("/debug/employee-service")
async def debug_employee_service(current_user = Depends(get_current_user)):
    """Debug endpoint to test employee service directly"""
    from .services.employee_service import EmployeeService
    from .db.session import get_db
    from fastapi import HTTPException

    employee_service = EmployeeService()
    db = next(get_db())

    try:
        result = await employee_service.get_employees(
            db=db,
            skip=0,
            limit=20,
            current_user=current_user
        )
        return {
            "message": "Employee service call successful",
            "result": result
        }
    except HTTPException as e:
        return {
            "message": "Employee service HTTPException",
            "status_code": e.status_code,
            "detail": e.detail,
            "error_type": type(e).__name__
        }
    except Exception as e:
        return {
            "message": "Employee service call failed",
            "error": str(e),
            "error_type": type(e).__name__
        }

if __name__ == "__main__":
    uvicorn.run(
        "app.main:app",
        host="0.0.0.0",
        port=8085,
        reload=settings.DEBUG,
        log_level="info"
    )
