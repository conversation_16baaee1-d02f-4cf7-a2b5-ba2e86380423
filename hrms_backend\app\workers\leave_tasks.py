from celery import Celery
from sqlalchemy.orm import Session
from datetime import datetime, date, timedelta
from decimal import Decimal
import logging

from ..core.celery_app import celery_app
from ..db.session import SessionLocal
from ..db.models.leave import LeaveBalance, LeavePolicy, LeaveRequest
from ..db.models.employee import Employee

logger = logging.getLogger(__name__)


@celery_app.task(bind=True)
def update_leave_balances(self):
    """Update leave balances for all employees (monthly accrual)"""
    db = SessionLocal()
    
    try:
        today = date.today()
        current_year = today.year
        
        # Get all active leave policies with monthly accrual
        policies = db.query(LeavePolicy).filter(
            LeavePolicy.is_active == True,
            LeavePolicy.accrual_frequency == "monthly"
        ).all()
        
        total_updated = 0
        
        for policy in policies:
            # Get all active employees for this organization
            employees = db.query(Employee).filter(
                Employee.organization_id == policy.organization_id,
                Employee.is_active == True
            ).all()
            
            for employee in employees:
                try:
                    # Get or create leave balance for current year
                    balance = db.query(LeaveBalance).filter(
                        LeaveBalance.employee_id == employee.id,
                        LeaveBalance.leave_policy_id == policy.id,
                        LeaveBalance.year == current_year
                    ).first()
                    
                    if not balance:
                        # Create new balance
                        balance = LeaveBalance(
                            employee_id=employee.id,
                            leave_policy_id=policy.id,
                            year=current_year,
                            opening_balance=Decimal(0),
                            accrued_balance=Decimal(0),
                            used_balance=Decimal(0),
                            pending_balance=Decimal(0),
                            carried_forward=Decimal(0),
                            available_balance=Decimal(0)
                        )
                        db.add(balance)
                    
                    # Calculate monthly accrual
                    monthly_accrual = policy.annual_entitlement / 12
                    
                    # Check if employee is eligible (not in probation if policy doesn't allow)
                    if not policy.available_during_probation:
                        # Check if employee is still in probation (assuming 3 months)
                        probation_end = employee.hire_date + timedelta(days=90)
                        if today < probation_end:
                            continue
                    
                    # Add monthly accrual
                    balance.accrued_balance += monthly_accrual
                    balance.available_balance = (
                        balance.accrued_balance + 
                        balance.carried_forward - 
                        balance.used_balance - 
                        balance.pending_balance
                    )
                    
                    # Apply maximum accumulation limit
                    if policy.max_accumulation and balance.available_balance > policy.max_accumulation:
                        balance.available_balance = policy.max_accumulation
                        balance.accrued_balance = (
                            policy.max_accumulation + 
                            balance.used_balance + 
                            balance.pending_balance - 
                            balance.carried_forward
                        )
                    
                    total_updated += 1
                    
                except Exception as e:
                    logger.error(f"Error updating balance for employee {employee.id}: {e}")
                    continue
        
        db.commit()
        logger.info(f"Updated leave balances for {total_updated} employee-policy combinations")
        return {"status": "completed", "balances_updated": total_updated}
        
    except Exception as e:
        db.rollback()
        logger.error(f"Error updating leave balances: {e}")
        raise self.retry(exc=e, countdown=60, max_retries=3)
    finally:
        db.close()


@celery_app.task(bind=True)
def process_leave_carry_forward(self, organization_id: str):
    """Process leave carry forward at year end"""
    db = SessionLocal()
    
    try:
        current_year = date.today().year
        previous_year = current_year - 1
        
        # Get all leave policies for organization
        policies = db.query(LeavePolicy).filter(
            LeavePolicy.organization_id == organization_id,
            LeavePolicy.is_active == True
        ).all()
        
        total_processed = 0
        
        for policy in policies:
            if policy.max_carry_forward <= 0:
                continue  # No carry forward allowed
            
            # Get all balances for previous year
            previous_balances = db.query(LeaveBalance).filter(
                LeaveBalance.leave_policy_id == policy.id,
                LeaveBalance.year == previous_year
            ).all()
            
            for prev_balance in previous_balances:
                try:
                    # Calculate carry forward amount
                    unused_balance = prev_balance.available_balance
                    carry_forward = min(unused_balance, policy.max_carry_forward)
                    
                    if carry_forward > 0:
                        # Get or create current year balance
                        current_balance = db.query(LeaveBalance).filter(
                            LeaveBalance.employee_id == prev_balance.employee_id,
                            LeaveBalance.leave_policy_id == policy.id,
                            LeaveBalance.year == current_year
                        ).first()
                        
                        if not current_balance:
                            current_balance = LeaveBalance(
                                employee_id=prev_balance.employee_id,
                                leave_policy_id=policy.id,
                                year=current_year,
                                opening_balance=Decimal(0),
                                accrued_balance=Decimal(0),
                                used_balance=Decimal(0),
                                pending_balance=Decimal(0),
                                carried_forward=carry_forward,
                                available_balance=carry_forward
                            )
                            db.add(current_balance)
                        else:
                            current_balance.carried_forward = carry_forward
                            current_balance.available_balance += carry_forward
                        
                        total_processed += 1
                
                except Exception as e:
                    logger.error(f"Error processing carry forward for balance {prev_balance.id}: {e}")
                    continue
        
        db.commit()
        logger.info(f"Processed carry forward for {total_processed} balances in organization {organization_id}")
        return {"status": "completed", "carry_forwards_processed": total_processed}
        
    except Exception as e:
        db.rollback()
        logger.error(f"Error processing leave carry forward: {e}")
        raise self.retry(exc=e, countdown=60, max_retries=3)
    finally:
        db.close()


@celery_app.task(bind=True)
def send_leave_expiry_reminders(self):
    """Send reminders for leaves expiring soon"""
    db = SessionLocal()
    
    try:
        from ..core.websocket_manager import notification_manager
        
        # Check for leaves expiring in next 30 days
        expiry_date = date.today() + timedelta(days=30)
        year_end = date(date.today().year, 12, 31)
        
        # Get balances with available leave that might expire
        balances = db.query(LeaveBalance).join(LeavePolicy).filter(
            LeaveBalance.year == date.today().year,
            LeaveBalance.available_balance > 0,
            LeavePolicy.max_carry_forward < LeaveBalance.available_balance
        ).all()
        
        reminder_count = 0
        for balance in balances:
            try:
                expiring_days = balance.available_balance - (balance.leave_policy.max_carry_forward or 0)
                
                if expiring_days > 0:
                    # Send notification
                    await notification_manager.notify_user(
                        str(balance.employee_id),
                        "leave_expiry_reminder",
                        {
                            "leave_type": balance.leave_policy.leave_type,
                            "expiring_days": float(expiring_days),
                            "expiry_date": year_end.isoformat(),
                            "message": f"You have {expiring_days} {balance.leave_policy.leave_type} days expiring on {year_end}"
                        }
                    )
                    reminder_count += 1
            
            except Exception as e:
                logger.error(f"Error sending expiry reminder for balance {balance.id}: {e}")
                continue
        
        logger.info(f"Sent leave expiry reminders to {reminder_count} employees")
        return {"status": "completed", "reminders_sent": reminder_count}
        
    except Exception as e:
        logger.error(f"Error sending leave expiry reminders: {e}")
        raise self.retry(exc=e, countdown=60, max_retries=3)
    finally:
        db.close()


@celery_app.task(bind=True)
def auto_approve_leave_requests(self):
    """Auto approve leave requests based on policy thresholds"""
    db = SessionLocal()
    
    try:
        from ..schemas.leave import LeaveStatus
        
        # Get pending leave requests
        pending_requests = db.query(LeaveRequest).join(LeavePolicy).filter(
            LeaveRequest.status == LeaveStatus.PENDING,
            LeavePolicy.auto_approve_threshold.isnot(None),
            LeaveRequest.total_days <= LeavePolicy.auto_approve_threshold
        ).all()
        
        auto_approved = 0
        for request in pending_requests:
            try:
                request.status = LeaveStatus.APPROVED
                request.approved_at = datetime.utcnow()
                # Set system as approver (could be a special system user ID)
                request.approved_by = None  # System approval
                
                # Update leave balance
                balance = db.query(LeaveBalance).filter(
                    LeaveBalance.employee_id == request.employee_id,
                    LeaveBalance.leave_policy_id == request.leave_policy_id,
                    LeaveBalance.year == date.today().year
                ).first()
                
                if balance:
                    balance.pending_balance -= request.total_days
                    balance.used_balance += request.total_days
                
                auto_approved += 1
                
            except Exception as e:
                logger.error(f"Error auto-approving leave request {request.id}: {e}")
                continue
        
        db.commit()
        logger.info(f"Auto-approved {auto_approved} leave requests")
        return {"status": "completed", "auto_approved": auto_approved}
        
    except Exception as e:
        db.rollback()
        logger.error(f"Error in auto approval: {e}")
        raise self.retry(exc=e, countdown=60, max_retries=3)
    finally:
        db.close()


@celery_app.task(bind=True)
def cleanup_old_leave_requests(self, days_to_keep: int = 365):
    """Clean up old leave requests"""
    db = SessionLocal()
    
    try:
        cutoff_date = datetime.now() - timedelta(days=days_to_keep)
        
        # Delete old leave requests (keep approved/rejected for record)
        deleted_count = db.query(LeaveRequest).filter(
            LeaveRequest.created_at < cutoff_date,
            LeaveRequest.status.in_(['cancelled', 'withdrawn'])
        ).delete()
        
        db.commit()
        
        logger.info(f"Cleaned up {deleted_count} old leave requests")
        return {"status": "completed", "requests_deleted": deleted_count}
        
    except Exception as e:
        db.rollback()
        logger.error(f"Error cleaning up leave requests: {e}")
        raise self.retry(exc=e, countdown=60, max_retries=3)
    finally:
        db.close()


@celery_app.task(bind=True)
def escalate_pending_leave_request(self, leave_request_id: str):
    """Auto-escalate pending leave requests"""
    db = SessionLocal()

    try:
        from ..schemas.leave import LeaveStatus
        from ..db.models.leave import LeaveRequest
        from ..db.models.employee import Employee
        from ..core.websocket_manager import notification_manager

        # Get leave request
        leave_request = db.query(LeaveRequest).filter(
            LeaveRequest.id == leave_request_id,
            LeaveRequest.status == LeaveStatus.PENDING
        ).first()

        if not leave_request:
            logger.warning(f"Leave request {leave_request_id} not found or not pending")
            return {"status": "skipped", "reason": "Request not found or not pending"}

        # Get employee details
        employee = db.query(Employee).filter(Employee.id == leave_request.employee_id).first()
        if not employee:
            logger.error(f"Employee not found for leave request {leave_request_id}")
            return {"status": "error", "reason": "Employee not found"}

        # Determine escalation path
        escalation_target = None
        escalation_message = ""

        if employee.role == "employee":
            # Employee requests escalate to HR if manager hasn't responded
            escalation_target = "HR"
            escalation_message = f"Leave request from {employee.first_name} {employee.last_name} has been escalated to HR due to no manager response"
        elif employee.role == "manager":
            # Manager requests escalate to senior HR/admin
            escalation_target = "ADMIN"
            escalation_message = f"Manager leave request from {employee.first_name} {employee.last_name} has been escalated to Admin"

        if escalation_target:
            # Send escalation notification
            await notification_manager.notify_escalation(
                escalation_target,
                {
                    "leave_request_id": str(leave_request.id),
                    "employee_name": f"{employee.first_name} {employee.last_name}",
                    "employee_id": employee.employee_id,
                    "start_date": leave_request.start_date.isoformat(),
                    "end_date": leave_request.end_date.isoformat(),
                    "total_days": float(leave_request.total_days),
                    "reason": leave_request.reason,
                    "escalation_reason": "No response within required timeframe",
                    "message": escalation_message
                }
            )

            logger.info(f"Escalated leave request {leave_request_id} to {escalation_target}")
            return {"status": "escalated", "target": escalation_target}
        else:
            logger.warning(f"No escalation path defined for role {employee.role}")
            return {"status": "skipped", "reason": "No escalation path defined"}

    except Exception as e:
        logger.error(f"Error escalating leave request {leave_request_id}: {e}")
        raise self.retry(exc=e, countdown=300, max_retries=3)  # Retry after 5 minutes
    finally:
        db.close()
