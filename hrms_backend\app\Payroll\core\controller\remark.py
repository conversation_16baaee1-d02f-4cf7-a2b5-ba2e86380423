from flask import request
from flask.views import MethodView
from core.middleware import roles_required
from flask_smorest import Blueprint
from core.services.remarks import RemarkService
from core.utils.responseBuilder import ResponseBuilder
from flask_smorest import Blueprint, abort
from schemas import RemarkSchema

blueprint = Blueprint("employee_remark", __name__, description="Endpoints for employee remarks")
@blueprint.route('/employees/remarks', methods=['POST'])

class CreateRemarkController(MethodView):
    @roles_required(['admin'])
    @blueprint.arguments(RemarkSchema)  # Use RemarkSchema for validation
    @blueprint.response(201, RemarkSchema)  # Respond with RemarkSchema
    def post(self, data):
        """
        Create a remark for a payroll history record.
        """
        try:
            # Extract validated fields from the schema
            employee_id = data.get("employee_id")
            payroll_history_id = data.get("payroll_history_id")
            remark_text = data.get("remark")

            # Validate required fields
            if not employee_id or not payroll_history_id or not remark_text:
                abort(400, message="Employee ID, Payroll History ID, and Remark are required.")

            # Create the remark
            result = RemarkService.createRemark(employee_id, payroll_history_id, remark_text)
            return result  # Response automatically serialized by RemarkSchema
        except Exception as e:
            print(e)  # Consider logging this
            abort(500, message="An unexpected error occurred while creating the remark.")


@blueprint.route('/employees/remarks/payroll-history/<int:payroll_history_id>', methods=['GET'])
class GetRemarksByPayrollHistoryController(MethodView):
    @roles_required(['admin'])
    @blueprint.response(200, RemarkSchema(many=True))  # Respond with a list of RemarkSchema
    def get(self, payroll_history_id):
        """
        Get all remarks associated with a specific payroll history ID.
        """
        try:
            # Fetch remarks by payroll history ID
            remarks = RemarkService.getRemarksByPayrollHistoryId(payroll_history_id)
            if not remarks:
                abort(404, message=f"No remarks found for payroll history ID {payroll_history_id}.")
            
            return remarks  # Automatically serialized by RemarkSchema
        except Exception as e:
            print(e)  # Log the error for debugging
            abort(500, message="An unexpected error occurred while retrieving remarks.")


@blueprint.route('/employees/remarks/<int:remark_id>', methods=['PUT'])
class UpdateRemarkController(MethodView):
    @roles_required(['admin'])
    @blueprint.arguments(RemarkSchema)  # Use RemarkSchema for validation
    @blueprint.response(200, RemarkSchema)  # Respond with the updated RemarkSchema
    def put(self, data, remark_id):
        """
        Update a specific remark by the remark ID.
        """
        try:
            service = RemarkService()  # Create an instance of the service
            updated_remark = service.updateRemark(remark_id, **data)

            if not updated_remark:
                abort(404, message="Remark not found.")

            return updated_remark  # Automatically serialized by RemarkSchema
        except ValueError as ve:
            abort(400, message=str(ve))
        except Exception as e:
            print(e)  # Log the error for debugging
            abort(500, message="An unexpected error occurred while updating the remark.")



@blueprint.route('/employees/remarks/<int:remark_id>', methods=['DELETE'])
class DeleteRemarkController(MethodView):
    @roles_required(['admin'])
    def delete(self, remark_id):
        """
        Delete a specific remark by the remark ID.
        """
        try:
            # Call the service to delete the remark
            deleted_remark = RemarkService.deleteRemark(remark_id)

            if not deleted_remark:
                # If the remark does not exist, return a 404 error with a friendly message
                return ResponseBuilder(data={"message": f"Remark with ID {remark_id} not found."}, status_code=404).build()

            # Return a success message for the deleted remark
            return ResponseBuilder(data={"message": f"Remark with ID {remark_id} deleted successfully."}, status_code=200).build()
        except Exception as e:
            print(e)  # Log the error for debugging
            return ResponseBuilder(data={"message": "An unexpected error occurred while deleting the remark."}, status_code=500).build()
