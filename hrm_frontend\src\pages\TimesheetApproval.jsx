/**
 * Timesheet Approval Page with RBAC Integration
 * Displays timesheet approval workflow with role-based access control
 */

import React, { useState } from 'react';
import { Clock, CheckCircle, XCircle, Eye, Calendar, User, Filter } from 'lucide-react';
import { usePermissions } from '../hooks/usePermissions';
import { PermissionGate, ConditionalRender } from '../components/ProtectedRoute';

export default function TimesheetApproval({ activeTab = 'pending-approval' }) {
  const permissions = usePermissions();
  const [selectedStatus, setSelectedStatus] = useState('pending');
  const [selectedEmployee, setSelectedEmployee] = useState('all');

  // Mock timesheet data
  const timesheets = [
    {
      id: 1,
      employee: '<PERSON>',
      department: 'Engineering',
      weekEnding: '2024-01-19',
      totalHours: 42.5,
      regularHours: 40,
      overtimeHours: 2.5,
      status: 'pending',
      submittedDate: '2024-01-20',
      projects: [
        { name: 'HRMS Development', hours: 35 },
        { name: 'Code Review', hours: 7.5 }
      ]
    },
    {
      id: 2,
      employee: '<PERSON>',
      department: 'Marketing',
      weekEnding: '2024-01-19',
      totalHours: 40,
      regularHours: 40,
      overtimeHours: 0,
      status: 'pending',
      submittedDate: '2024-01-20',
      projects: [
        { name: 'Campaign Planning', hours: 25 },
        { name: 'Content Creation', hours: 15 }
      ]
    },
    {
      id: 3,
      employee: 'Mike Johnson',
      department: 'Engineering',
      weekEnding: '2024-01-12',
      totalHours: 38,
      regularHours: 38,
      overtimeHours: 0,
      status: 'approved',
      submittedDate: '2024-01-13',
      approvedDate: '2024-01-15',
      approvedBy: 'Team Lead',
      projects: [
        { name: 'Bug Fixes', hours: 20 },
        { name: 'Testing', hours: 18 }
      ]
    },
    {
      id: 4,
      employee: 'Alice Brown',
      department: 'Design',
      weekEnding: '2024-01-12',
      totalHours: 35,
      regularHours: 35,
      overtimeHours: 0,
      status: 'rejected',
      submittedDate: '2024-01-13',
      rejectedDate: '2024-01-16',
      rejectedBy: 'Team Lead',
      rejectionReason: 'Insufficient project details',
      projects: [
        { name: 'UI Design', hours: 35 }
      ]
    }
  ];

  const getStatusColor = (status) => {
    switch (status) {
      case 'approved': return 'text-green-600 bg-green-100';
      case 'pending': return 'text-yellow-600 bg-yellow-100';
      case 'rejected': return 'text-red-600 bg-red-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'approved': return <CheckCircle size={16} />;
      case 'pending': return <Clock size={16} />;
      case 'rejected': return <XCircle size={16} />;
      default: return <Clock size={16} />;
    }
  };

  const handleApprove = (timesheetId) => {
    console.log('Approving timesheet:', timesheetId);
    // API call to approve timesheet
  };

  const handleReject = (timesheetId) => {
    console.log('Rejecting timesheet:', timesheetId);
    // API call to reject timesheet
  };

  const renderPendingApproval = () => (
    <div className="space-y-6">
      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <Clock className="h-8 w-8 text-yellow-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Pending Approval</p>
              <p className="text-2xl font-bold text-gray-900">
                {timesheets.filter(t => t.status === 'pending').length}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <CheckCircle className="h-8 w-8 text-green-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Approved This Week</p>
              <p className="text-2xl font-bold text-gray-900">
                {timesheets.filter(t => t.status === 'approved').length}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <XCircle className="h-8 w-8 text-red-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Rejected</p>
              <p className="text-2xl font-bold text-gray-900">
                {timesheets.filter(t => t.status === 'rejected').length}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <User className="h-8 w-8 text-blue-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Total Employees</p>
              <p className="text-2xl font-bold text-gray-900">
                {new Set(timesheets.map(t => t.employee)).size}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Filters */}
      <div className="flex justify-between items-center">
        <div className="flex space-x-4">
          <select 
            value={selectedStatus} 
            onChange={(e) => setSelectedStatus(e.target.value)}
            className="border border-gray-300 rounded-md px-3 py-2"
          >
            <option value="all">All Status</option>
            <option value="pending">Pending</option>
            <option value="approved">Approved</option>
            <option value="rejected">Rejected</option>
          </select>
          <select 
            value={selectedEmployee} 
            onChange={(e) => setSelectedEmployee(e.target.value)}
            className="border border-gray-300 rounded-md px-3 py-2"
          >
            <option value="all">All Employees</option>
            {Array.from(new Set(timesheets.map(t => t.employee))).map(employee => (
              <option key={employee} value={employee}>{employee}</option>
            ))}
          </select>
        </div>
      </div>

      {/* Timesheets Table */}
      <div className="bg-white rounded-lg shadow overflow-hidden">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Employee</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Week Ending</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Total Hours</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Overtime</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Submitted</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {timesheets
              .filter(timesheet => selectedStatus === 'all' || timesheet.status === selectedStatus)
              .filter(timesheet => selectedEmployee === 'all' || timesheet.employee === selectedEmployee)
              .map((timesheet) => (
              <tr key={timesheet.id} className="hover:bg-gray-50">
                <td className="px-6 py-4 whitespace-nowrap">
                  <div>
                    <div className="text-sm font-medium text-gray-900">{timesheet.employee}</div>
                    <div className="text-sm text-gray-500">{timesheet.department}</div>
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  {timesheet.weekEnding}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  {timesheet.totalHours}h
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  {timesheet.overtimeHours}h
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <span className={`inline-flex items-center space-x-1 px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(timesheet.status)}`}>
                    {getStatusIcon(timesheet.status)}
                    <span className="capitalize">{timesheet.status}</span>
                  </span>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  {timesheet.submittedDate}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                  <div className="flex space-x-2">
                    <button className="text-blue-600 hover:text-blue-900">
                      <Eye size={16} />
                    </button>
                    {timesheet.status === 'pending' && (
                      <>
                        <button 
                          onClick={() => handleApprove(timesheet.id)}
                          className="text-green-600 hover:text-green-900"
                        >
                          <CheckCircle size={16} />
                        </button>
                        <button 
                          onClick={() => handleReject(timesheet.id)}
                          className="text-red-600 hover:text-red-900"
                        >
                          <XCircle size={16} />
                        </button>
                      </>
                    )}
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );

  const renderApprovalHistory = () => (
    <div className="space-y-6">
      <div className="bg-white rounded-lg shadow">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900">Approval History</h3>
        </div>
        <div className="p-6">
          <div className="space-y-4">
            {timesheets
              .filter(t => t.status !== 'pending')
              .map((timesheet) => (
              <div key={timesheet.id} className="border border-gray-200 rounded-lg p-4">
                <div className="flex justify-between items-start">
                  <div>
                    <h4 className="text-sm font-medium text-gray-900">
                      {timesheet.employee} - Week ending {timesheet.weekEnding}
                    </h4>
                    <p className="text-sm text-gray-500">
                      {timesheet.totalHours} hours ({timesheet.overtimeHours} overtime)
                    </p>
                    {timesheet.status === 'approved' && (
                      <p className="text-sm text-green-600">
                        Approved by {timesheet.approvedBy} on {timesheet.approvedDate}
                      </p>
                    )}
                    {timesheet.status === 'rejected' && (
                      <div>
                        <p className="text-sm text-red-600">
                          Rejected by {timesheet.rejectedBy} on {timesheet.rejectedDate}
                        </p>
                        <p className="text-sm text-gray-600">
                          Reason: {timesheet.rejectionReason}
                        </p>
                      </div>
                    )}
                  </div>
                  <span className={`inline-flex items-center space-x-1 px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(timesheet.status)}`}>
                    {getStatusIcon(timesheet.status)}
                    <span className="capitalize">{timesheet.status}</span>
                  </span>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );

  const renderContent = () => {
    switch (activeTab) {
      case 'pending-approval':
        return renderPendingApproval();
      case 'approval-history':
        return renderApprovalHistory();
      default:
        return renderPendingApproval();
    }
  };

  return (
    <PermissionGate permission="timesheetApproval">
      <div className="p-6">
        <div className="mb-6">
          <h1 className="text-2xl font-bold text-gray-900">Timesheet Approval</h1>
          <p className="text-gray-600">Review and approve employee timesheets</p>
        </div>

        {renderContent()}
      </div>
    </PermissionGate>
  );
}
