from core.models.salary_template_benefit_pivot import SalaryTemplateBenefitsPivotModel
from core.databases.database import db

class SalaryTemplatesBenefitRepository:
    def __init__(self) -> None:
        self.model = SalaryTemplateBenefitsPivotModel

    @classmethod
    def create(self, template_id, benefits_id ):
        salary_pivote = SalaryTemplateBenefitsPivotModel(
            salary_template_id = template_id,
            benefits_id = benefits_id,
        )
        db.session.add(salary_pivote)
        db.session.commit()
        return salary_pivote
    
    @classmethod
    def getAttachedTemplate(self, template_id):
        return SalaryTemplateBenefitsPivotModel.query.filter_by(salary_template_id=template_id).all()
        
    @classmethod
    def delete(self, components_ids):
        db.session.query(SalaryTemplateBenefitsPivotModel) \
            .filter(SalaryTemplateBenefitsPivotModel.id.in_(components_ids)) \
            .delete(synchronize_session=False)      
        db.session.commit()
        return 
    
    @classmethod
    def delete_components_by_template_id(self, template_id):
        db.session.query(SalaryTemplateBenefitsPivotModel) \
            .filter(SalaryTemplateBenefitsPivotModel.salary_template_id == template_id) \
            .delete(synchronize_session=False)
        db.session.commit()