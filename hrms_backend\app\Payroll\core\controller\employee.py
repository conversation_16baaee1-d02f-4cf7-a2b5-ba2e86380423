from datetime import datetime, timed<PERSON><PERSON>
from flask import request, url_for, jsonify
from flask.views import MethodView
from core.models.departments import DepartmentModel
from core.models.designations import DesignationModel
from core.models.payroll_history import PayrollHistoryModel
from core.models.salary_tempplate_component_pivot import SalaryTemplateComponentsPivotModel
from core.repositories.user import UserRepository
from core.services.payroll_history import PayrollHistoryService
from core.services.remarks import RemarkService
from flask_smorest import Blueprint, abort
from schemas import AssignBenefitSchema, EmployeeSchema, FilterPayrollHistorySchema, OrganisationBulkSchema, PayrollHistorySchema, RemarkSchema, RemoveBenefitFromEmployeesSchema, UpdateEmployeeSchema, RemoveComponentFromEmployee, RemoveBenefitFromEmployee, EmployeeProfileUpdateSchema, EmployeeLoginSchema, UpdatePayrollHistorySchema,SalaryTemplateSchema
from sqlalchemy.exc import SQLAlchemyError
from core.services.employee import EmployeeService
import core.utils.response_message as RESPONSEMESSAGE
from core.services.organisation import OrganisationService
from core.services.departments import DepartmentsService
from core.repositories.employees_component import EmployeesComponentRepository
from core.repositories.employees_benefit import EmployeesBenefitRepository
from core.services.benefits import BenefitsService
from core.services.salary_components import SalaryComponentsService
from core.services.designations import DesignationsService
from core.services.salary_templates import SalaryTemplatesService
from core.utils.responseBuilder import ResponseBuilder
from passlib.hash import pbkdf2_sha256
from core.models.employees import EmployeeModel
from flask_jwt_extended import create_access_token, create_refresh_token
from flask_jwt_extended import get_jwt
from core.services.payroll_service import PayrollService
from schemas import AssignComponentSchema
from schemas import ProrateSalarySchema
from core.services.prorate_salaries import ProrateSalaryService
from core.repositories.designations import DesignationsRepository
from core.repositories.departments import DepartmentsRepository
from core.databases.database import db
import calendar
import time
import json
import os
import csv
from core.services.component_processor import ComponentProcessor
from core.utils.activity_logger import ActivityLogger
from core.middleware import roles_required 
from passlib.exc import InvalidHashError

blueprint = Blueprint("employee", __name__, description="Operations for employees")

ALLOWED_EXTENSIONS = {'csv'}

@blueprint.route('/employees/upload')
class Employee_BulkUpload(MethodView):
    def get(self):
        return jsonify({"message": "Upload Bulk employee"})

    def allowed_file(self,filename):
        return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

   # @blueprint.arguments
    @roles_required(['admin'])
    def post(self):
        total_records = 0
        existing_user_count = 0
        new_user_count = 0   
        
        datas_csv = request.get_json() 
        if not datas_csv:
            return jsonify({"error": "No data received"}), 400       
        
        employee_service = EmployeeService()
        employees = []

        for row in datas_csv:
            total_records += 1       
            #Check if user exist

            check_user_id = EmployeeService().getEmployeeByKey({'employeeID' :row['employee_id']})
            
            check_user_email = EmployeeService().getEmployeeByKey({'email' :row['email']})
             
            check_user_id = len(check_user_id)
            check_user_email = len(check_user_email)
            

             
            if check_user_email <= 0 or check_user_id <= 0 :
                
               
                new_user_count += 1
                # Extract and process department, designation, organization details
                department_id = DepartmentsRepository().get_or_create_department(row['department'])
                designation_id = DesignationsRepository().get_or_create_designation(row['designation'])
                check_salary_template =  SalaryTemplatesService().getSalaryTemplatesByKey({"template_name":row['salary_template']})
                check_organization = OrganisationService().getOrganisationByKey({"organisation_name": row['organisation']})
                
                count_org = len(check_organization)
                count_salary_temp = len(check_salary_template)
                


                if count_salary_temp <=0:

                    print(f"Creating new salary template")

                    salary_temp_object = {
                        "template_name": row['salary_template'],
                        
                    }

                    salary_temp_schema = SalaryTemplateSchema().dump(salary_temp_object)
                    salary_temp_object = SalaryTemplatesService().createSalaryTemplates(salary_temp_schema)
                    template_id = salary_temp_object.id
                    print(f"Created Templated id: {template_id}")
                else:

                    print(f"Getting existing Salary Template")
                    salary_template_object = check_salary_template[0]
                    salary_template = SalaryTemplateSchema().dump(salary_template_object)
                    template_id = salary_template["id"]

                    print(f"Existing... Templated id: {template_id}")

                if count_org <= 0:
                    print(f"Creating new organisation")
                    slug = f"{row['organisation']}{calendar.timegm(time.gmtime())}"
                    org_name = slug.replace(' ', '')
                    org_input_object = {
                        "slug": org_name,
                        "organisation_name": row['organisation'],
                        "email": f"{org_name}@email.com"
                    }

                    org_schema = OrganisationBulkSchema().dump(org_input_object)
                    organization_object = OrganisationService().createOrganisation(org_schema)
                    organization_id = organization_object.id
                    print(f"Created organization ID: {organization_id}")
                else:
                    print(f"Getting existing organisation")
                    organisaation_object = check_organization[0]
                    org_schema = OrganisationBulkSchema().dump(organisaation_object)
                    organization_id = org_schema["id"]

                # # Build the employee data
                employee = {
                    "first_name": row['first_name'],
                    "last_name": row['last_name'],
                    "email": row['email'],
                    "gender": row['gender'],
                    "business_unit": row['business_unit'],
                    "employee_type": row['employee_type'],
                    "employment_type": row['employment_type'],
                    "division": row['division'],
                    "phone": row['phone'],
                    "bank_name": row['bank_name'],
                    "sort_code": row['bank_code'],
                    "bank_account": row['bank_account'],
                    "address": row['address'],
                    "state": row['state'],
                    "city": row['city'],
                    "country": row['country'],
                    "currency": row['currency'],
                    "zip_code": row['zip_code'],
                    "tax_type": row['tax_type'],
                    "taxID": row['tax_id'],
                    "dob": row['dob'],
                    "hire_date": row['hire_date'],
                    "status": row['employee_status'],
                    "employeeID": row['employee_id'],
                    "role": row['role'],
                    "salary_type": row['salary_type'],
                    "rate": row['rate'],
                    "annual_leave_days":"None",
                    "unpaid_leave_days":"None",
                    "sick_leave_days":"None",
                    "maternity_paternity_leave_days":"None",
                    "casual_leave_days":"None",
                    "compassionate_leave_days":"None",
                    "hours_worked": row['hours_worked'],
                    "total_working_days": row['number_of_days_worked'],
                    "total_present_days": "None",
                    "total_absent_days": "None",
                    "level": row['level'],
                    "work_schedule": row['work_schedule'],
                    "gross_pay": row['gross_pay'],
                    "pension_no": row['pension_no'],
                    "pension_pfa": row['pension_pfa'],
                    "nhf_no": row['nhf_no'],
                    "nhf_mortgage_bank": row['nhf_mortgage_bank'],
                    "department_id": department_id,
                    "designation_id": designation_id,
                    "organisation_id": organization_id,
                    "template_id":template_id,
                    "source_tag":"CSV Bulk Upload",
                    "password_hash": pbkdf2_sha256.hash("password"),
                   }
       
                employees.append(employee)
                # print(f"Processed employee: {employee}")

                # Create employee in the system
                employee_service.createEmployee(employee)
            else:
                existing_user_count +=1

        print("New Employees uploaded count",new_user_count)
        print("Existing uploaded Employee Count",existing_user_count)
        print("Total uploaded Employee", total_records)
        # Response
        return jsonify({
             "message": f"{total_records} records received, {existing_user_count} already exist, {new_user_count} added."
            }), 201

    
@blueprint.route("/employee/<id>")
class Employee(MethodView):
    @roles_required(['admin'])
    @blueprint.response(200, EmployeeSchema)
    def get(self, id):
        emaployeeService = EmployeeService()
        employee = emaployeeService.getEmployeeById(id)
        # print("emplyee by ID",employee)
        if not employee:
            abort(401, message="Employee does not exist")
        employee_details = EmployeeSchema().dump(employee)
        # print("single employee",employee_details)
        # testing the component porocessor
        # res = ComponentProcessor(employee_details["id"], employee_details["gross_pay"], employee_details["template_id"]).generate_salary_response()
        # print(res)
        return ResponseBuilder(data=employee_details, status_code=200).build()
    
    
    @roles_required(['admin'])
    def delete(self, id):         
        emaployeeService = EmployeeService()
        employee = emaployeeService.getEmployeeById(id)
        if not employee:
            abort(404, message="Department does not exist")
        emaployeeService.deleteEmployee(id)
        return {"message" : "Department deleted"}
    
    @roles_required(['admin'])
    @blueprint.arguments(UpdateEmployeeSchema)
    @blueprint.response(201, EmployeeSchema)
    def put(self, data, id):
        selected_components = set(data.get("salary_components_id", [])) 
        selected_benefits = set(data.get("benefits_id", []))  

        employeeService = EmployeeService()
        employee = employeeService.getEmployeeById(id)

        if not employee:
            abort(404, message="Employee does not exist")

        try:
            new_employee = employeeService.updateEmployee(id, data)

            # Fetch existing components id
            existing_components = EmployeesComponentRepository().getAttachedEmployee(id)
            existing_benefits = EmployeesBenefitRepository().getAttachedEmployee(id)

            existing_component_ids = {comp.salary_component_id for comp in existing_components}
            existing_benefit_ids = {ben.benefits_id for ben in existing_benefits}
            print("existing_component_ids", existing_component_ids)
            print("existing_benefit_ids", existing_benefit_ids)
            # Add new components
            for component_id in selected_components:
                if component_id not in existing_component_ids:
                    component = SalaryComponentsService().getSalaryComponents(component_id)
                    if component:
                        EmployeesComponentRepository().create(id, component.id)

            # Add new benefits
            for benefit_id in selected_benefits:
                if benefit_id not in existing_benefit_ids:
                    benefit = BenefitsService().getBenefits(benefit_id)
                    if benefit:
                        EmployeesBenefitRepository().create(id, benefit.id)

            return new_employee

        except SQLAlchemyError as e:
            print(e)
            abort(500, message="Error while updating Employee")


@blueprint.route("/employee_profile/<id>")
class Employee(MethodView):
    @blueprint.response(200, EmployeeSchema)
    def get(self, id):
        employeeService = EmployeeService()
        employee = employeeService.getEmployeeById(id)
        if not employee:
            abort(401, message="Employee does not exist")
        employee_details = EmployeeSchema().dump(employee)
        return ResponseBuilder(data=employee_details, status_code=200).build()
    
    @blueprint.arguments(EmployeeProfileUpdateSchema)
    @blueprint.response(201, EmployeeSchema)
    def put(self, data, id):
        print("PUT Request Received:")
        print(f"Raw Data: {data}")

        employeeService = EmployeeService()
        employee = employeeService.getEmployeeById(id)
        if not employee:
            abort(404, message="Employee does not exist")

        selected_components = data.get("salary_components_id")
        selected_benefits = data.get("benefits_id")

        print(f"Selected Components: {selected_components}")
        print(f"Selected Benefits: {selected_benefits}")

        try:
            # Log incoming data
            print("Cleaned Data Before Processing:")
            print(data)

            # Remove fields that are not directly updatable
            if 'salary_components_id' in data:
                del data['salary_components_id']
            if 'benefits_id' in data:
                del data['benefits_id']
            if data.get('monthly_tax') is not None:
                del data['monthly_tax']
            if data.get('annual_tax') is not None:
                del data['annual_tax']

            # Validate related fields
            if data.get("department_id") is not None:
                department = DepartmentsService().getDepartments(data['department_id'])
                if not department:
                    abort(400, message="Invalid department")

            if data.get("designation_id") is not None:
                designation = DesignationsService().getDesignations(data['designation_id'])
                if not designation:
                    abort(400, message="Invalid designation")

            if data.get("organisation_id") is not None:
                organisation = OrganisationService().getOrganisationById(data['organisation_id'])
                if not organisation:
                    abort(400, message="Invalid organisation")

            if data.get("template_id") is not None:
                template = SalaryTemplatesService().getSalaryTemplates(data['template_id'])
                if not template:
                    abort(400, message="Invalid salary template")

            # Update the main employee record
            updated_employee = employeeService.updateEmployee(id, data)
            print("Updated Employee Record:", updated_employee)

            # Handle salary components
            if selected_components is not None:
                EmployeesComponentRepository().deleteByEmployeeId(id)
                for component_id in selected_components:
                    component = SalaryComponentsService().getSalaryComponents(component_id)
                    if component:
                        EmployeesComponentRepository().create(id, component.id)
                        print(f"Component {component.id} added to employee {id}")

            # Handle benefits
            if selected_benefits is not None:
                EmployeesBenefitRepository().deleteByEmployeeId(id)
                for benefit_id in selected_benefits:
                    benefit = BenefitsService().getBenefits(benefit_id)
                    if benefit:
                        EmployeesBenefitRepository().create(id, benefit.id)
                        print(f"Benefit {benefit.id} added to employee {id}")

            return updated_employee

        except SQLAlchemyError as e:
            print("Error while updating Employee:", e)
            abort(500, message="Error while updating Employee")


    
@blueprint.route("/employee")
class EmployeeList(MethodView):
    @roles_required(['admin'])
    @blueprint.response(200, EmployeeSchema)
    def get(self, page: int = None, size: int = None):
        emaployeeService = EmployeeService()
        employee_list, total_employee = emaployeeService.fetchEmployee(offset=None, size=size)
        employee_schema = EmployeeSchema(many=True)
        employee_list = employee_schema.dump(employee_list)
        employee_data = []
        for item in employee_list:
            component_processor = ComponentProcessor(
                item["id"],
                item["gross_pay"],
                0,
                False,
                salary_benefit=item["employee_benefits"],
                salary_component=item["employee_components"]
            )
            res = component_processor.generate_salary_response()
            res["employee"] = item
            employee_data.append(res)
        return ResponseBuilder(data=employee_data, status_code=200, total=total_employee).build()
    # def get(self, page: int = None, size: int = None):
    #     emaployeeService = EmployeeService()
    #     employee_list, total_employee = emaployeeService.fetchEmployee(offset=None, size=size)
    #     employee_schema = EmployeeSchema(many=True)

    #     # Serialize the data
    #     employee_list = employee_schema.dump(employee_list)

    #     # Transform the salary_template_components
    #     for employee in employee_list:
    #         if "template" in employee and "salary_template_components" in employee["template"]:
    #             employee["template"]["salary_template"] = [
    #                 {
    #                     "amount": component["salary_component"]["amount"],
    #                     "calculation_type": component["salary_component"]["calculation_type"],
    #                     "component_name": component["salary_component"]["component_name"],
    #                     "component_type": component["salary_component"]["component_type"],
    #                     "cycle": component["salary_component"]["cycle"],
    #                     "id": component["salary_component"]["id"],
    #                     "payslip_name": component["salary_component"]["payslip_name"],
    #                     "value": component["salary_component"]["value"],
    #                 }
    #                 for component in employee["template"].pop("salary_template_components", [])
    #             ]

    #     return ResponseBuilder(data=employee_list, status_code=200, total=total_employee).build()
    
    
    @roles_required(['admin'])
    @blueprint.arguments(EmployeeSchema)
    @blueprint.response(200, EmployeeSchema)
    def post(self, data):
        try:
            emaployeeService = EmployeeService()
            employee = emaployeeService.getEmployeeByKey({"email": data['email']})
            selected_components = data.get("salary_components_id")
            selected_benefits = data.get("benefits_id")

            # if data['monthly_tax'] is not None:
            #     del data['monthly_tax']
            # if data['annual_tax'] is not None:
            #     del data['annual_tax']
            if 'salary_components_id' in data:
                del data['salary_components_id']
            if 'benefits_id' in data:
                del data['benefits_id']
            
            if data.get("department_id") is not None:
                department = DepartmentsService().getDepartments(data['department_id'])
                if not department:
                    abort(400, message="invalid department")
            
            if data.get("designation_id") is not None:
                designation = DesignationsService().getDesignations(data['designation_id'])
                if not designation:
                    abort(400, message="invalid designation")

            if data["password_hash"]:
                data["password_hash"] = pbkdf2_sha256.hash(data["password_hash"])
            else:
                data["password_hash"] = pbkdf2_sha256.hash("password")

            if data.get("organisation_id") is not None:
                organisation = OrganisationService().getOrganisationById(data['organisation_id'])
                if not organisation:
                    abort(400, message="invalid organisation")

            if data.get("template_id") is not None:
                template = SalaryTemplatesService().getSalaryTemplates(data['template_id'])
                if not template:
                    abort(400, message="invalid salary template")

            if not employee:
                new_employee = emaployeeService.createEmployee(data)
                ActivityLogger.log_admin_activity(f"You created a new employee: {new_employee.first_name} {new_employee.last_name}")
            else:
                abort(400, message="Employee already exist")
    

            if selected_components is not None:
                    for component_id in selected_components:
                        component = SalaryComponentsService().getSalaryComponents(component_id)
                        if component:
                            EmployeesComponentRepository().create(new_employee.id, component.id)

            if selected_benefits is not None:
                    for benefit_id in selected_benefits:
                        benefit = BenefitsService().getBenefits(benefit_id)
                        if benefit:
                            EmployeesBenefitRepository().create(new_employee.id, benefit.id)

        except SQLAlchemyError as e:
            print(e)
            abort(500, message="Error while creating Employee")
            
        return new_employee
    
@blueprint.route("/employee/<id>/salary_component")
class EmployeeComponent(MethodView):
    @roles_required(['admin'])
    @blueprint.arguments(RemoveComponentFromEmployee)
    @blueprint.response(200)
    def post(self, data, id ):
        selected_components = data.get("components_id")

        service = EmployeeService()
        employee = service.getEmployeeById(id)
        if not employee:
            abort(401, message="Employee does not exist")

        employee_compo = employee.employee_components
        emp_comp_id = [component.id for component in employee_compo if component.salary_component.id in selected_components]
        try:
            EmployeesComponentRepository().delete(emp_comp_id)
        except Exception as e:
            print(e)
        return {"message" : "Salary Components detached from Employee"}
    
@blueprint.route("/employee/<id>/benefit")
class EmployeeBenefit(MethodView):
    @roles_required(['admin'])
    @blueprint.arguments(RemoveBenefitFromEmployee)
    @blueprint.response(200)
    def post(self, data, id ):
        selected_benefits = data.get("benefits_id")

        service = EmployeeService()
        employee = service.getEmployeeById(id)
        if not employee:
            abort(401, message="Employee does not exist")

        employee_benefit = employee.employee_benefits
        emp_benefit_id = [benefit.id for benefit in employee_benefit if benefit.benefit.id in selected_benefits]
        try:
            EmployeesBenefitRepository().delete(emp_benefit_id)
        except Exception as e:
            print(e)
        return {"message" : "Other Benefits detached from Employee"}
    
@blueprint.route("/employee_auth")
class EmployeeLogin(MethodView):
    @blueprint.arguments(EmployeeLoginSchema)
    @blueprint.response(200)
    def post(self, data ):
        employee = EmployeeModel.query.filter(EmployeeModel.email == data["email"]).first()
        
        try:
            if employee and employee.password_hash and pbkdf2_sha256.verify(data["password"], employee.password_hash):
                access_token = create_access_token("employee_user", additional_claims={"is_employee": True})
                refresh_token = create_refresh_token(employee.id, additional_claims={"is_employee": True})
                return {"access_token" : access_token, "refresh_token": refresh_token}, 200
        except InvalidHashError:
            print("Invalid hash format:", employee.password_hash)
       
        abort(401, message=RESPONSEMESSAGE.INVALID_CREDENTIALS)
        

@blueprint.route("/employee_tax")
class EmployeeList(MethodView):
    @roles_required(['admin'])
    def get(self, page: int = 1, size: int = 10):
        offset = (page - 1) * size
        payroll_service = PayrollService()
 
        # Call the payroll service to calculate taxes for each employee
        payroll_data = payroll_service.create_payroll_schedule(payroll_period=12)

        # Return the payroll data directly in the response
        return ResponseBuilder(data=payroll_data, status_code=200).build()

@blueprint.route("/employee/count")
class EmployeeCount(MethodView):
    @roles_required(['admin'])
    def get(self):
        employeeService = EmployeeService()
        total_employee = employeeService.countAllEmployees()  # Implement this method
        return ResponseBuilder(data={"total_employees": total_employee}, status_code=200).build()

@blueprint.route("/employees_summary")
class EmployeeSummary(MethodView):
    @roles_required(['admin'])
    def get(self):
        employeeService = EmployeeService()
        total_employee = employeeService.getEmployeeSummary() 
        return ResponseBuilder(data=total_employee, status_code=200).build()

@blueprint.route("/employee/recent")
class RecentEmployees(MethodView):
    @roles_required(['admin'])
    def get(self):
        employeeService = EmployeeService()
        recent_employees = employeeService.fetchRecentEmployees()

        # Serialize the data
        employee_recent_schema = EmployeeSchema(many=True)
        recent_employees_data = employee_recent_schema.dump(recent_employees)

        # Return the serialized data
        return ResponseBuilder(data={"recent_employees": recent_employees_data}, status_code=200).build()


@blueprint.route("/employee/<id>/assign_component")
class AssignComponent(MethodView):
    @roles_required(['admin'])
    @blueprint.arguments(AssignComponentSchema)
    @blueprint.response(200)
    def post(self, data, id):
        """
        Assign a single salary component to an employee.
        """
        employeeService = EmployeeService()
        salaryComponentService = SalaryComponentsService()
        employeeComponentRepo = EmployeesComponentRepository()

        # Get the employee by ID
        employee = employeeService.getEmployeeById(id)
        if not employee:
            abort(404, message="Employee does not exist")

        # Get the salary component by ID from the request data
        component_id = data.get("salary_component_id")
        component = salaryComponentService.getSalaryComponents(component_id)
        if not component:
            abort(400, message="Invalid salary component")

        # Check if the component is already assigned to the employee
        existing_component = employeeComponentRepo.getAttachedEmployeeComponent(employee.id, component.id)
        if existing_component:
            return {"message": f"This Salary Component is already assigned to this Employee"}, 400

        try:
            # Create the association between the employee and the component
            employeeComponentRepo.create(employee.id, component.id)
            return {"message": "Salary component assigned to employee successfully"}, 200
        except SQLAlchemyError as e:
            print(e)
            abort(500, message="Error while assigning salary component to employee")
            
            
@blueprint.route("/employee/<id>/assign_component")
class AllComponent(MethodView):
    @roles_required(['admin'])
    @blueprint.response(200)
    def get(self, id):
        """
        Get all salary components assigned to an employee.
        """
        employeeService = EmployeeService()
        employeeComponentRepo = EmployeesComponentRepository()

        # Get the employee by ID
        employee = employeeService.getEmployeeById(id)
        if not employee:
            abort(404, message="Employee does not exist")

        try:
            # Fetch all components assigned to the employee
            components = employeeComponentRepo.getAttachedEmployee(employee.id)

            # Format the response data with component details
            response_data = [
                {
                    "component_id": comp.salary_component.id,
                    "component_name": comp.salary_component.component_name,
                    "component_type": comp.salary_component.component_type,
                    "payslip_name": comp.salary_component.payslip_name,
                    "calculation_type": comp.salary_component.calculation_type,
                    "duration": comp.salary_component.duration,
                    "cycle": comp.salary_component.cycle,
                    "value": comp.salary_component.value,
                    "amount": comp.salary_component.amount,
                }
                for comp in components
            ]

            return {"components": response_data}, 200
        except SQLAlchemyError as e:
            print(e)
            abort(500, message="Error while retrieving assigned components")
            
@blueprint.route("/employee/<id>/component/<component_id>")
class SingleComponent(MethodView):
    @roles_required(['admin'])
    @blueprint.response(200)
    def get(self, id, component_id):
        """
        Get a single salary component assigned to an employee.
        """
        employeeService = EmployeeService()
        employeeComponentRepo = EmployeesComponentRepository()

        # Get the employee by ID
        employee = employeeService.getEmployeeById(id)
        if not employee:
            abort(404, message="Employee does not exist")

        # Fetch the specific component assigned to the employee
        component = employeeComponentRepo.getSingleComponent(employee.id, component_id)
        if not component:
            abort(404, message="The specified salary component is not assigned to the employee")

        try:
            # Format the response data with the component details
            response_data = {
                "component_id": component.salary_component.id,
                "component_name": component.salary_component.component_name,
                "component_type": component.salary_component.component_type,
                "payslip_name": component.salary_component.payslip_name,
                "calculation_type": component.salary_component.calculation_type,
                "duration": component.salary_component.duration,
                "cycle": component.salary_component.cycle,
                "value": component.salary_component.value,
                "amount": component.salary_component.amount,
            }

            return {"component": response_data}, 200
        except SQLAlchemyError as e:
            print(e)
            abort(500, message="Error while retrieving the assigned component")


@blueprint.route("/employee/<id>/remove_component")
class RemoveComponent(MethodView):
    @roles_required(['admin'])
    @blueprint.arguments(RemoveComponentFromEmployee)
    @blueprint.response(200)
    def post(self, data, id):
        """
        Remove a salary component from an employee.
        """
        employeeService = EmployeeService()
        employeeComponentRepo = EmployeesComponentRepository()

        # Get the employee by ID
        employee = employeeService.getEmployeeById(id)
        if not employee:
            abort(404, message="Employee does not exist")

        # Extract the component ID from the request data
        component_id = data.get("salary_component_id")

        # Find the employee component association using the pivot table
        employee_compo = employee.employee_components
        emp_comp_id = [
            component.id for component in employee_compo
            if component.salary_component.id == component_id
        ]

        if not emp_comp_id:
            abort(400, message="The specified salary component is not associated with the employee")

        try:
            # Delete the association between the employee and the component
            # Pass both the employee ID and the list of component IDs to the delete method
            employeeComponentRepo.delete(employee.id, emp_comp_id)
            return {"message": "Salary component removed from employee successfully"}, 200
        except SQLAlchemyError as e:
            print(e)
            abort(500, message="Error while removing salary component from employee")


@blueprint.route("/employee/<id>/assign_benefit")
class AssignBenefit(MethodView):
    @roles_required(['admin'])
    @blueprint.arguments(AssignBenefitSchema)
    @blueprint.response(200)
    def post(self, data, id):
        """
        Assign a single salary benefit to an employee.
        """
        employeeService = EmployeeService()
        benefitsService = BenefitsService()
        employeebenefitRepo = EmployeesBenefitRepository()

        # Get the employee by ID
        employee = employeeService.getEmployeeById(id)
        if not employee:
            abort(404, message="Employee does not exist")

        # Get the salary benefit by ID from the request data
        benefit_id = data.get("benefits_id")
        benefit = benefitsService.getBenefits(benefit_id)
        if not benefit:
            abort(400, message="Invalid salary benefit")

        # Check if the benefit is already assigned to the employee
        existing_benefit = employeebenefitRepo.getAttachedEmployeeBenefit(employee.id, benefit.id)
        if existing_benefit:
            return {"message": f"This Benefit is already assigned to this Employee."}, 400

        try:
            # Create the association between the employee and the benefit
            employeebenefitRepo.create(employee.id, benefit.id)
            return {"message": "Salary benefit assigned to employee successfully"}, 200
        except SQLAlchemyError as e:
            print(e)
            abort(500, message="Error while assigning salary benefit to employee")
            

# Endpoint to get all benefits assigned to an employee
@blueprint.route("/employee/<id>/benefits")
class EmployeeBenefits(MethodView):
    @roles_required(['admin'])
    @blueprint.response(200)
    def get(self, id):
        """
        Get all benefits assigned to an employee.
        """
        employeeService = EmployeeService()
        employeeBenefitsRepo = EmployeesBenefitRepository()

        # Get the employee by ID
        employee = employeeService.getEmployeeById(id)
        if not employee:
            abort(404, message="Employee does not exist")

        try:
            # Fetch all benefits assigned to the employee
            benefits = employeeBenefitsRepo.getBenefitsByEmployeeId(employee.id)

            # Format the response data with benefit details
            response_data = [
                {
                    "benefit_id": benefit.benefit.id,
                    "benefit_name": benefit.benefit.benefit_name,
                    "payslip_name": benefit.benefit.payslip_name,
                    "component_type": benefit.benefit.component_type,
                    "calculation_type": benefit.benefit.calculation_type,
                    "duration": benefit.benefit.duration,
                    "cycle": benefit.benefit.cycle,
                    "value": benefit.benefit.value,
                    "amount": benefit.benefit.amount,
                }
                for benefit in benefits
            ]

            return {"benefits": response_data}, 200
        except SQLAlchemyError as e:
            print(e)
            abort(500, message="Error while retrieving assigned benefits")


# Endpoint to get a specific benefit assigned to an employee
@blueprint.route("/employee/<id>/benefit/<benefit_id>")
class SingleBenefit(MethodView):
    @roles_required(['admin'])
    @blueprint.response(200)
    def get(self, id, benefit_id):
        """
        Get a single benefit assigned to an employee.
        """
        employeeService = EmployeeService()
        employeeBenefitsRepo = EmployeesBenefitRepository()

        # Get the employee by ID
        employee = employeeService.getEmployeeById(id)
        if not employee:
            abort(404, message="Employee does not exist")

        # Fetch the specific benefit assigned to the employee
        benefit = employeeBenefitsRepo.getBenefitByEmployeeAndBenefitId(employee.id, benefit_id)
        if not benefit:
            abort(404, message="The specified benefit is not assigned to the employee")

        try:
            # Format the response data with the benefit details
            response_data = {
                "benefit_id": benefit.benefit.id,
                "benefit_name": benefit.benefit.benefit_name,
                "payslip_name": benefit.benefit.payslip_name,
                "component_type": benefit.benefit.component_type,
                "calculation_type": benefit.benefit.calculation_type,
                "duration": benefit.benefit.duration,
                "cycle": benefit.benefit.cycle,
                "value": benefit.benefit.value,
                "amount": benefit.benefit.amount,
            }

            return {"benefit": response_data}, 200
        except SQLAlchemyError as e:
            print(e)
            abort(500, message="Error while retrieving the assigned benefit")


# Endpoint to remove a benefit from an employee
@blueprint.route("/employee/<id>/remove_benefit")
class RemoveBenefit(MethodView):
    @roles_required(['admin'])
    @blueprint.arguments(RemoveBenefitFromEmployeesSchema)
    @blueprint.response(200)
    def post(self, data, id):
        """
        Remove a benefit from an employee.
        """
        employeeService = EmployeeService()
        employeeBenefitsRepo = EmployeesBenefitRepository()

        # Get the employee by ID
        employee = employeeService.getEmployeeById(id)
        if not employee:
            abort(404, message="Employee does not exist")

        # Extract the benefit ID from the request data
        benefit_id = data.get("benefits_id")

        # Find the employee benefit association using the pivot table
        employee_benefits = employee.employee_benefits  # Assuming a similar relationship exists
        emp_benefit_id = [
            benefit.id for benefit in employee_benefits
            if benefit.benefit.id == benefit_id
        ]

        if not emp_benefit_id:
            abort(400, message="The specified benefit is not associated with the employee")

        try:
            # Delete the association between the employee and the benefit
            employeeBenefitsRepo.delete(employee.id, emp_benefit_id)  # Pass the employee ID and the list of benefit IDs
            return {"message": "Benefit removed from employee successfully"}, 200
        except SQLAlchemyError as e:
            print(e)
            abort(500, message="Error while removing benefit from employee")




###### CODES FOR PRORATION OF EMPLOYEES SALARY ###############


# 1. Get Method to Get All Employees' Prorated Salaries
@blueprint.route('/prorate-salary')
class ProrateSalaryList(MethodView):
    @roles_required(['admin'])
    @blueprint.response(200, ProrateSalarySchema(many=True))
    def get(self):
        """
        Retrieve all prorated salaries for the authenticated user.
        """
        # Get the authenticated user's ID
        user_id = UserRepository.authUserId()

        # Fetch the prorated salaries for this user
        service = ProrateSalaryService()
        prorated_salaries, total_count = service.get_prorate_salaries_by_user(user_id)

        # Convert the model instances to dictionaries using the schema
        prorated_salaries_data = ProrateSalarySchema(many=True).dump(prorated_salaries)

        # # Debug: Print the fetched records
        # print(f"Fetched prorated salaries for user {user_id}: {prorated_salaries_data}")
        # print(f"Total count: {total_count}")

        return ResponseBuilder(data=prorated_salaries_data, status_code=200, total=total_count).build()


# @blueprint.route('/prorate-salary/<int:id>', methods=['POST'])
# class ProrateSalary(MethodView):
#     @roles_required(['admin'])
#     @blueprint.arguments(ProrateSalarySchema)
#     @blueprint.response(201, ProrateSalarySchema)
#     def post(self, data, id):
#         """
#         Prorate a single employee's salary.
#         """
#         p_id = data.get("p_id")

#         service = ProrateSalaryService()
#         employeeService = EmployeeService()

#         # Get the employee by ID
#         employee = employeeService.getEmployeeById(id)
#         if not employee:
#             abort(404, message="Employee does not exist")

#         # Add the employee ID to the data
#         data['employee_id'] = id
#         del data["p_id"]

#         # Proceed with creating the prorated salary
#         new_prorated_salary = service.create_prorate_salary(data)

#         if new_prorated_salary:
#             self.update_payroll_history(p_id, data)
        
#         return new_prorated_salary

#     def update_payroll_history(self, p_id, data):
#         # print("data ", data)
#         try:
#             print(f"payroll id {p_id}")
#             # id = data.get("payroll_id")
#             payroll_object = {
#                 "is_prorated" : True,
#                 "prorated_gross" : data["gross_pay"],
#                 "prorated_net" : data["netpay"],
#                 "prorated_monthly_tax" : data["monthly_tax"],
#                 "prorated_annual_tax" : data["annual_tax"]
#             }
#             payroll_data = UpdatePayrollHistoryProratedData().dump(payroll_object)

#             updated = PayrollHistoryService().payroll_history_update(p_id, payroll_data)
#             print("Payroll History Updated:", updated)

#             if not updated:
#                 raise Exception("Payroll history update failed!")

#         except KeyError as e:
#             print(f"KeyError: Missing key {e}")
#             return {"error": f"Missing required key: {str(e)}"}, 400

#         except AttributeError as e:
#             print(f"AttributeError: {e}")
#             return {"error": f"Attribute error: {str(e)}"}, 500

#         except ValueError as e:
#             print(f"ValueError: {e}")
#             return {"error": str(e)}, 400

#         except Exception as e:
#             print(f"Unexpected error: {e}")
#             return {"error": f"An unexpected error occurred: {str(e)}"}, 500
        
# 3. Get Method to Get a Single Employee's Prorated Data
@blueprint.route('/prorate-salary/<int:employee_id>')
class ProrateSalaryDetail(MethodView):
    @roles_required(['admin'])
    @blueprint.response(200, ProrateSalarySchema)
    def get(self, employee_id):
        """
        Get a single employee's prorated salary.

        This endpoint retrieves the prorated salary record for a
        specific employee identified by their ID. It returns the
        details of the prorated salary, including calculations and
        timestamps.
        """
        service = ProrateSalaryService()
        prorated_salary = service.get_prorate_salary_by_id(employee_id)

        # If no record is found, return a 404 error
        if not prorated_salary:
            abort(404, message="Prorated salary record not found.")

        # Convert the model instance to a dictionary using the schema
        prorated_salary_data = ProrateSalarySchema().dump(prorated_salary)

        # Debug: Print the fetched record
        # print(f"Fetched prorated salary for employee {employee_id}: {prorated_salary_data}")

        return ResponseBuilder(data=prorated_salary_data, status_code=200).build()



@blueprint.route("/employee/active")
class ActiveEmployeesWithProrate(MethodView):
    @roles_required(['admin'])
    def get(self):
        """
        Return active employees, including those with and without prorated salary details.
        For employees with a prorated record for the current month, their data is updated with prorated details.
        """
        employeeService = EmployeeService()
        prorateSalaryService = ProrateSalaryService()

        size = request.args.get('size', 50, type=int)
        offset = request.args.get('offset', 0, type=int)

        # Fetch active employees
        active_employees, total_active = employeeService.fetchActiveEmployees(offset, size)

        employee_schema = EmployeeSchema(many=True)
        employees_with_prorate = []

        # Get the current month and year
        current_month = datetime.now().month
        current_year = datetime.now().year

        # Loop through active employees to update with prorated details if available
        for employee in active_employees:
            # Fetch the most recent prorated salary record for the employee
            prorated_salary = prorateSalaryService.get_most_recent_prorate_salary_by_employee_id(employee.id)

            # Serialize employee data
            employee_data = employee_schema.dump([employee])[0]

            # Check if a valid prorated salary record exists for the current month and year
            if prorated_salary and prorated_salary.timestamp.month == current_month and prorated_salary.timestamp.year == current_year:
                # Update employee data with prorated salary details
                employee_data['gross_pay'] = prorated_salary.gross_pay or employee.gross_pay
                employee_data['monthly_tax'] = prorated_salary.monthly_tax or employee.monthly_tax
                employee_data['annual_tax'] = prorated_salary.annual_tax or employee.annual_tax
                employee_data['total_taxable_monthly_sum'] = prorated_salary.total_taxable_monthly_sum or employee.total_taxable_monthly_sum
                employee_data['total_taxable_annual_sum'] = prorated_salary.total_taxable_annual_sum or employee.total_taxable_annual_sum
                employee_data['total_non_taxable_monthly_sum'] = prorated_salary.total_non_taxable_monthly_sum or employee.total_non_taxable_monthly_sum
                employee_data['total_non_taxable_annual_sum'] = prorated_salary.total_non_taxable_annual_sum or employee.total_non_taxable_annual_sum
                employee_data['total_statutory_monthly_sum'] = prorated_salary.total_statutory_monthly_sum or employee.total_statutory_monthly_sum
                employee_data['total_statutory_annual_sum'] = prorated_salary.total_statutory_annual_sum or employee.total_statutory_annual_sum
                employee_data['total_other_deductions_monthly_sum'] = prorated_salary.total_other_deductions_monthly_sum or employee.total_other_deductions_monthly_sum
                employee_data['total_other_deductions_annual_sum'] = prorated_salary.total_other_deductions_annual_sum or employee.total_other_deductions_annual_sum
                employee_data['netpay'] = prorated_salary.netpay or employee.netpay
                employee_data['is_prorated'] = prorated_salary.is_prorated or employee.is_prorated

                # Serialize prorated salary details
                prorate_salary_schema = ProrateSalarySchema()
                employee_data['prorated_salary'] = prorate_salary_schema.dump(prorated_salary)

            # Append the employee (with or without prorated details) to the result list
            employees_with_prorate.append(employee_data)

        total_filtered = len(employees_with_prorate)

        return ResponseBuilder(
            data={"employees": employees_with_prorate, "total_active": total_filtered},
            status_code=200
        ).build()



@blueprint.route("/employee/active-prorated")
class ActiveEmployeesWithProrate(MethodView):
    @roles_required(['admin'])
    def get(self):
        """
        Return only active employees with prorated salary details for the current month.
        """
        employeeService = EmployeeService()
        prorateSalaryService = ProrateSalaryService()

        size = request.args.get('size', 50, type=int)
        offset = request.args.get('offset', 0, type=int)

        # Get the current month and year
        current_month = datetime.now().month
        current_year = datetime.now().year

        # Fetch active employees
        active_employees, total_active = employeeService.fetchActiveEmployees(offset, size)

        # Initialize the employee schema
        employee_schema = EmployeeSchema(many=True)
        employees_with_prorate = []

        # Loop through active employees to filter those with prorated salary records for the current month
        for employee in active_employees:
            # Fetch the latest prorated salary record for the employee within the current month and year
            prorated_salary = prorateSalaryService.get_latest_prorate_salary_for_month(employee.id, current_month, current_year)

            # Check if a prorated salary record exists for the current month and year
            if prorated_salary:
                # Serialize employee data
                employee_data = employee_schema.dump([employee])[0]

                # Update employee data with prorated salary details
                employee_data['gross_pay'] = prorated_salary.gross_pay or employee.gross_pay
                employee_data['monthly_tax'] = prorated_salary.monthly_tax or employee.monthly_tax
                employee_data['annual_tax'] = prorated_salary.annual_tax or employee.annual_tax
                employee_data['total_taxable_monthly_sum'] = prorated_salary.total_taxable_monthly_sum or employee.total_taxable_monthly_sum
                employee_data['total_taxable_annual_sum'] = prorated_salary.total_taxable_annual_sum or employee.total_taxable_annual_sum
                employee_data['total_non_taxable_monthly_sum'] = prorated_salary.total_non_taxable_monthly_sum or employee.total_non_taxable_monthly_sum
                employee_data['total_non_taxable_annual_sum'] = prorated_salary.total_non_taxable_annual_sum or employee.total_non_taxable_annual_sum
                employee_data['total_statutory_monthly_sum'] = prorated_salary.total_statutory_monthly_sum or employee.total_statutory_monthly_sum
                employee_data['total_statutory_annual_sum'] = prorated_salary.total_statutory_annual_sum or employee.total_statutory_annual_sum
                employee_data['total_other_deductions_monthly_sum'] = prorated_salary.total_other_deductions_monthly_sum or employee.total_other_deductions_monthly_sum
                employee_data['total_other_deductions_annual_sum'] = prorated_salary.total_other_deductions_annual_sum or employee.total_other_deductions_annual_sum
                employee_data['netpay'] = prorated_salary.netpay or employee.netpay
                employee_data['is_prorated'] = prorated_salary.is_prorated or employee.is_prorated

                # Serialize prorated salary details
                prorate_salary_schema = ProrateSalarySchema()
                employee_data['prorated_salary'] = prorate_salary_schema.dump(prorated_salary)

                # Add the combined data to the list
                employees_with_prorate.append(employee_data)

        total_filtered = len(employees_with_prorate)

        # Build the final response with only employees who have prorated salary details for the current month
        return ResponseBuilder(
            data={"employees": employees_with_prorate, "total_active_with_prorate": total_filtered},
            status_code=200
        ).build()               

@blueprint.route("/employee/active/<int:employee_id>")
class EmployeeProrateSalaryDetails(MethodView):
    @roles_required(['admin'])
    def get(self, employee_id):
        """
        Get the details for a single employee by employee_id, including prorated salary if available for the current month.
        """
        prorateSalaryService = ProrateSalaryService()
        employeeService = EmployeeService()

        # Fetch the employee by id to confirm existence
        employee = employeeService.getEmployeeById(employee_id)
        if not employee:
            return ResponseBuilder(
                data={"error": "Employee not found"},
                status_code=404
            ).build()

        # Serialize basic employee data
        employee_schema = EmployeeSchema()
        employee_data = employee_schema.dump(employee)

        # Get the current month and year
        current_month = datetime.now().month
        current_year = datetime.now().year

        # Fetch the most recent prorated salary record for the current month, if it exists
        prorated_salary = prorateSalaryService.get_most_recent_prorate_salary_by_employee_id(employee_id)

        # Check if the record is from the current month and year
        if prorated_salary and prorated_salary.timestamp.month == current_month and prorated_salary.timestamp.year == current_year:
            # Update employee data with prorated salary values if they exist
            employee_data['gross_pay'] = prorated_salary.gross_pay or employee.gross_pay
            employee_data['monthly_tax'] = prorated_salary.monthly_tax or employee.monthly_tax
            employee_data['annual_tax'] = prorated_salary.annual_tax or employee.annual_tax
            employee_data['total_taxable_monthly_sum'] = prorated_salary.total_taxable_monthly_sum or employee.total_taxable_monthly_sum
            employee_data['total_taxable_annual_sum'] = prorated_salary.total_taxable_annual_sum or employee.total_taxable_annual_sum
            employee_data['total_non_taxable_monthly_sum'] = prorated_salary.total_non_taxable_monthly_sum or employee.total_non_taxable_monthly_sum
            employee_data['total_non_taxable_annual_sum'] = prorated_salary.total_non_taxable_annual_sum or employee.total_non_taxable_annual_sum
            employee_data['total_statutory_monthly_sum'] = prorated_salary.total_statutory_monthly_sum or employee.total_statutory_monthly_sum
            employee_data['total_statutory_annual_sum'] = prorated_salary.total_statutory_annual_sum or employee.total_statutory_annual_sum
            employee_data['total_other_deductions_monthly_sum'] = prorated_salary.total_other_deductions_monthly_sum or employee.total_other_deductions_monthly_sum
            employee_data['total_other_deductions_annual_sum'] = prorated_salary.total_other_deductions_annual_sum or employee.total_other_deductions_annual_sum
            employee_data['netpay'] = prorated_salary.netpay or employee.netpay
            employee_data['is_prorated'] = prorated_salary.is_prorated or employee.is_prorated

            # Serialize prorated salary details
            prorate_salary_schema = ProrateSalarySchema()
            employee_data['prorated_salary'] = prorate_salary_schema.dump(prorated_salary)
        else:
            # If no prorated salary for the current month, set prorated_salary field as None
            employee_data['prorated_salary'] = None

        return ResponseBuilder(
            data=employee_data,
            status_code=200
        ).build()


@blueprint.route("/payroll-history")
class PayrollHistoryList(MethodView):
    @roles_required(['admin'])
    @blueprint.response(200, PayrollHistorySchema(many=True))
    def get(self):
        payroll_service = PayrollHistoryService()
        payroll_history_objects = payroll_service.get_payroll_history_by_key({"is_processed": True})

        payroll_history_schema = PayrollHistorySchema(many=True)
        payroll_history_data = payroll_history_schema.dump(payroll_history_objects)
        remark_service = RemarkService()

        res = []
        payroll_history = []
        for item in payroll_history_data:
            prorated_gross = item["prorated_gross"]
            prorated_net = item["prorated_net"]
            prorated_monthly_tax = item["prorated_monthly_tax"]
            prorated_annual_tax = item["prorated_annual_tax"]
            prorated_status = item["is_prorated"]
            processed_status = item["is_processed"]
            processed_time = item["is_processed_created"]
            payment_status = item["payment_status"]
            transaction_message = item["message"] 
            transaction_date = item["transaction_date"] 
            
            #print(item)
            component_processor = ComponentProcessor(
                item["employee"]["id"],
                item["employee"]["gross_pay"],
                prorated_gross,
                prorated_status,
                salary_benefit=item["employee"]["employee_benefits"],
                salary_component=item["employee"]["employee_components"]
            )
            res = component_processor.generate_salary_response()
            res["employee"] = item.get("employee")
            res["created_at"] = item.get("created_at")
            res["updated_at"] = item.get("updated_at")
            res["payroll_id"] = item.get("id")
            res["prorated_gross"] = prorated_gross
            res["prorated_status"] = prorated_status 
            res["prorated_net"] = prorated_net 
            res["prorated_monthly_tax"] = prorated_monthly_tax 
            res["prorated_annual_tax"] = prorated_annual_tax 
            res["is_processed"] = processed_status 
            res["processed_time"] = processed_time
            res["payment_status"] = payment_status
            res["transaction_message"] = transaction_message 
            res["transaction_date"] = transaction_date 
            res["currency"] = item.get("employee").get("currency")
            payroll_history.append(res)         
            
            # Get remarks for this payroll history
            payroll_history_id = item.get("id")
            remark_obj = remark_service.getRemarksByPayrollHistoryId(payroll_history_id) if payroll_history_id else None
            # Extract only the "remark" field
            remark_text = remark_obj[0].remark if remark_obj else None  # Get the first remark if available
            res["remark"] = remark_text  # Store directly as a string

        # Build the final response
        response = ResponseBuilder(
            data=payroll_history,
            status_code=200,
            total=len(payroll_history)
        ).build()
        return response
    
@blueprint.route('/payroll-history/create', methods=['POST'])
class PayrollHistory(MethodView):
    @roles_required(['admin'])
    @blueprint.arguments(PayrollHistorySchema(many=True))  # Accept a list of records
    @blueprint.response(201, PayrollHistorySchema(many=True))
    def post(self, data_list):
        """
        Create payroll history records for multiple employees.
        """
        
        # print(f"Received payroll history data: {data_list}")
        service = PayrollHistoryService()
        employee_service = EmployeeService()
        new_payroll_histories = []
        # print(f"Initiate a payroll list BK{new_payroll_history}")

        for data in data_list:
            # Check if the employee exists for each record
            employee = employee_service.getEmployeeById(data['employee_id'])
            if not employee:
                abort(404, message=f"Employee with ID {data['employee_id']} does not exist")

            # Proceed with creating the payroll history record for each employee
            new_payroll_history = service.create_payroll_history(data)
            new_payroll_histories.append(new_payroll_history)

        return new_payroll_histories

            

@blueprint.route('/payroll-history/employee/<int:employee_id>', methods=['GET'])
class PayrollHistoryByEmployee(MethodView):

    @roles_required(['admin'])
    @blueprint.response(200, PayrollHistorySchema(many=True))
    def get(self, employee_id):
        """Get payroll history for a specific employee."""
        payroll_service = PayrollHistoryService()
        employee_service = EmployeeService()

        try:
            # Check if the employee exists
            employee = employee_service.getEmployeeById(employee_id)
            if not employee:
                return ResponseBuilder(data={"message": "Employee not found."}, status_code=404).build()

            # Fetch payroll history records for the specified employee
            payroll_history_records = payroll_service.get_payroll_history_by_employee_id(employee_id)

            if not payroll_history_records:
                return ResponseBuilder(data={"message": "No payroll history found for this employee."}, status_code=404).build()

            # Serialize the data using the PayrollHistorySchema
            serialized_data = PayrollHistorySchema(many=True).dump(payroll_history_records)

            return ResponseBuilder(data=serialized_data, status_code=200).build()
        
        except Exception as e:
            print(e)  # Consider logging this instead
            return ResponseBuilder(data={"message": "Error retrieving payroll history"}, status_code=500).build()



@blueprint.route('/payroll-history/employee/<int:employee_id>', methods=['PUT'])
class UpdatePayrollHistory(MethodView):

    @roles_required(['admin'])
    @blueprint.arguments(UpdatePayrollHistorySchema)
    @blueprint.response(200, PayrollHistorySchema)
    def put(self, data, employee_id):
        payroll_service = PayrollHistoryService()
        
        # Fetch the payroll history by employee ID
        payroll_history = payroll_service.get_payroll_history_by_employee_id(employee_id)
        if not payroll_history:
            abort(404, message="Payroll history record does not exist.")
        
        # Remove created_at and updated_at fields from the data
        data.pop('created_at', None)
        data.pop('updated_at', None)
        
        try:
            # Update the payroll history record
            updated_payroll_history = payroll_service.update_payroll_history(employee_id, **data)
            
            # Serialize the updated object using PayrollHistorySchema to ensure JSON compatibility
            result = PayrollHistorySchema().dump(updated_payroll_history)
            return ResponseBuilder(data=result, status_code=200).build()
        
        except SQLAlchemyError as e:
            print(e)  # Log the error for debugging
            abort(500, message="Error while updating payroll history")



@blueprint.route('/payroll-history/<id>', methods=['DELETE'])
class DeletePayrollHistoryController(MethodView):
    @roles_required(['admin'])
    def delete(self, id):
        """
        Delete a payroll history record by ID, along with its associated remarks.
        """
        payroll_service = PayrollHistoryService()

        try:
            # Attempt to delete the payroll history record
            # print(f"Attempting to delete payroll history with ID {payroll_history_id}")
            deleted_payroll_history = payroll_service.deletePayrollHistory(id)
            
            if not deleted_payroll_history:
                # Return a 404 if the record does not exist
                # print(f"Payroll history with ID {payroll_history_id} does not exist")
                abort(404, message=f"Payroll history record with ID {id} does not exist.")

            # Return a success message if the deletion was successful
            # print(f"Payroll history with ID {payroll_history_id} deleted successfully")
            return ResponseBuilder(
                data={"message": f"Payroll history record with ID {id} deleted successfully."},
                status_code=200
            ).build()

        except Exception as e:
            # print(f"Error occurred while deleting payroll history with ID {payroll_history_id}: {e}")
            abort(500, message="An unexpected error occurred while deleting payroll history.")


@blueprint.route('/payroll-history/bulk-delete', methods=['DELETE'])
class BulkDeletePayrollHistoryController(MethodView):
    @roles_required(['admin'])
    def delete(self):
        """
        Delete multiple payroll history records by providing a list of IDs.
        """
        payroll_service = PayrollHistoryService()

        try:
            # Extract the list of IDs from the request JSON
            data = request.get_json()
            if not data or 'ids' not in data or not isinstance(data['ids'], list):
                abort(400, message="Invalid request payload. Expected a JSON object with a 'ids' list.")

            ids_to_delete = data['ids']

            if not ids_to_delete:
                return ResponseBuilder(
                    data={"message": "No IDs provided for deletion."},
                    status_code=200
                ).build()

            # Attempt to delete the payroll history records
            deleted_count = payroll_service.bulkDeletePayrollHistory(ids_to_delete)

            if deleted_count == 0:
                abort(404, message="No payroll history records were found for the provided IDs.")

            # Return a success message with the count of deleted records
            return ResponseBuilder(
                data={
                    "message": f"Successfully deleted {deleted_count} payroll history records.",
                    "deleted_count": deleted_count
                },
                status_code=200
            ).build()

        except Exception as e:
            db.session.rollback()
            print(f"Error occurred while deleting payroll histories: {e}")
            raise




@blueprint.route('/payroll-history/filter', methods=['GET'])
class FilterPayrollHistory(MethodView):
    @roles_required(['admin'])
    @blueprint.response(200, PayrollHistorySchema(many=True))
    def get(self):
        try:
            # Extract query parameters from the request
            filters = request.args

            query = PayrollHistoryModel.query.join(EmployeeModel, PayrollHistoryModel.employee_id == EmployeeModel.id)
            
            # Filter for records where is_processed is True
            query = query.filter(PayrollHistoryModel.is_processed == True)
            # print(f"Initial count of Payroll History records (is_processed=True): {query.count()}")

            if filters:
                if filters.get("department"):
                    department_name = filters.get("department").strip()
                    department = DepartmentModel.query.filter_by(name=department_name).first()

                    if department:
                        query = query.filter(EmployeeModel.department_id == department.id)
                        # print(f"Filtered by department '{department_name}', count: {query.count()}")  # Debug print
                    else:
                        # print(f"Department '{department_name}' not found, returning empty.")  # Debug print
                        return ResponseBuilder(data=[], status_code=200).build()

                if filters.get("designation"):
                    designation_name = filters.get("designation").strip()
                    designation = DesignationModel.query.filter_by(name=designation_name).first()

                    if designation:
                        query = query.filter(EmployeeModel.designation_id == designation.id)
                        # print(f"Filtered by designation '{designation_name}', count: {query.count()}")  # Debug print
                    else:
                        # print(f"Designation '{designation_name}' not found, returning empty.")  # Debug print
                        return ResponseBuilder(data=[], status_code=200).build()

                if filters.get("date"):
                    try:
                        if len(filters.get("date")) > 7:
                            abort(400, message="Invalid date format. Please use YYYY-MM.")

                        filter_date = datetime.strptime(filters.get("date"), "%Y-%m").date()

                        start_of_month = datetime.combine(filter_date, datetime.min.time())
                        next_month = filter_date.replace(day=28) + timedelta(days=4)  # Move to next month
                        end_of_month = datetime.combine(next_month.replace(day=1), datetime.max.time())

                        query = query.filter(
                            PayrollHistoryModel.created_at >= start_of_month,
                            PayrollHistoryModel.created_at <= end_of_month
                        )

                        # print(f"Filtered by date '{filters.get('date')}', count: {query.count()}")  # Debug print

                    except ValueError:
                        abort(400, message="Invalid date format. Please use YYYY-MM.")

                if filters.get("status"):
                    if filters.get("status") == "True":
                        query = query.filter(PayrollHistoryModel.payment_status == "True")
                    elif filters.get("status") == "False":
                        query = query.filter(PayrollHistoryModel.payment_status == "False")
                    elif filters.get("status") == "Unpaid":
                        query = query.filter(PayrollHistoryModel.payment_status.is_(None))
                    else:
                        return ResponseBuilder(data={"message": "Invalid status. Use 'True', 'False', or 'Unpaid'."}, status_code=400).build()
                    # print(f"Filtered by status '{filters.get('status')}', count: {query.count()}")

                    
                payroll_data = query.all()

                if not payroll_data:
                    # print("No payroll history records found after filtering.")  # Debug print
                    return ResponseBuilder(data=[], status_code=200).build()

                # print(f"Final count of Payroll History records to be returned: {len(payroll_data)}")  # Debug print

                # Serialize and return the data
                serialized_data = PayrollHistorySchema(many=True).dump(payroll_data)
                return ResponseBuilder(data=serialized_data, status_code=200).build()

        except Exception as e:
            print(e)  # Logging is preferred for production
            return ResponseBuilder(data={"message": "Error processing payroll history filter request"}, status_code=500).build()



@blueprint.route('/payroll-history-report', methods=['GET'])
class PayrollHistoryReport(MethodView):
    @roles_required(['admin'])
    def get(self):
        """
        Generate a monthly payroll report for all employees.
        """
        try:
            # Get the current date and extract the month and year
            current_date = datetime.now()
            month = current_date.month
            year = current_date.year

            # Use the current month and year to filter records
            service = PayrollHistoryService()
            payroll_data = service.get_monthly_payroll_report(month, year)

            # Assuming serialized_data is formatted properly (e.g., as a dictionary or list)
            serialized_data = payroll_data

            # Return response using ResponseBuilder
            return ResponseBuilder(data=serialized_data, status_code=200).build()

        except Exception as e:
            print(e)  # Consider logging this instead
            return ResponseBuilder(data={"message": "Error retrieving payroll history"}, status_code=500).build()

