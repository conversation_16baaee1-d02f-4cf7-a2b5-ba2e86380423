import re
import json
import logging
from typing import Dict, List, Optional, Tuple, Any
from datetime import datetime
from sqlalchemy.orm import Session
from fastapi import HTTPException, status

from ...db.models.ticket import Ticket, TicketType, TicketPriority
from ...core.security import CurrentUser
from ...core.config import settings

logger = logging.getLogger(__name__)


class AICategorization:
    """AI-powered ticket categorization and tagging service"""

    def __init__(self):
        self.category_keywords = {
            TicketType.IT_SUPPORT: [
                "password", "login", "access", "computer", "laptop", "software", "hardware",
                "network", "internet", "wifi", "email", "outlook", "system", "error",
                "crash", "slow", "virus", "malware", "backup", "restore", "printer",
                "scanner", "monitor", "keyboard", "mouse", "installation", "update"
            ],
            TicketType.HR_QUERY: [
                "hr", "human resources", "policy", "handbook", "benefits", "insurance",
                "vacation", "sick leave", "maternity", "paternity", "holiday", "pto",
                "payroll", "salary", "bonus", "promotion", "performance", "review",
                "training", "development", "onboarding", "offboarding", "termination"
            ],
            TicketType.FACILITIES: [
                "office", "building", "room", "desk", "chair", "lighting", "temperature",
                "hvac", "air conditioning", "heating", "cleaning", "maintenance",
                "repair", "parking", "security", "access card", "badge", "elevator",
                "restroom", "kitchen", "cafeteria", "meeting room", "conference"
            ],
            TicketType.PAYROLL: [
                "payroll", "salary", "pay", "wage", "overtime", "bonus", "commission",
                "deduction", "tax", "withholding", "direct deposit", "paycheck",
                "paystub", "w2", "w4", "1099", "benefits", "401k", "retirement"
            ],
            TicketType.LEAVE: [
                "leave", "vacation", "pto", "sick", "personal", "holiday", "time off",
                "absence", "fmla", "maternity", "paternity", "bereavement", "jury duty",
                "military", "sabbatical", "unpaid", "paid", "request", "approval"
            ],
            TicketType.EQUIPMENT: [
                "equipment", "laptop", "computer", "monitor", "keyboard", "mouse",
                "headset", "phone", "mobile", "tablet", "charger", "cable", "dock",
                "request", "replacement", "new", "broken", "damaged", "upgrade"
            ],
            TicketType.ACCESS_REQUEST: [
                "access", "permission", "account", "user", "login", "password",
                "system", "application", "database", "folder", "file", "share",
                "drive", "vpn", "remote", "security", "clearance", "badge"
            ],
            TicketType.COMPLAINT: [
                "complaint", "issue", "problem", "concern", "harassment", "discrimination",
                "bullying", "inappropriate", "unprofessional", "conflict", "dispute",
                "grievance", "ethics", "violation", "misconduct", "report"
            ],
            TicketType.SUGGESTION: [
                "suggestion", "idea", "improvement", "feedback", "recommendation",
                "enhancement", "feature", "process", "workflow", "efficiency",
                "productivity", "innovation", "proposal", "change", "better"
            ],
            TicketType.TRAINING_REQUEST: [
                "training", "course", "workshop", "seminar", "certification",
                "education", "learning", "development", "skill", "knowledge",
                "conference", "webinar", "class", "program", "curriculum"
            ]
        }

        self.priority_keywords = {
            TicketPriority.CRITICAL: [
                "critical", "emergency", "urgent", "down", "outage", "broken",
                "not working", "crashed", "failed", "security breach", "data loss",
                "system failure", "production down", "cannot access", "immediate"
            ],
            TicketPriority.URGENT: [
                "urgent", "asap", "immediately", "priority", "important", "deadline",
                "time sensitive", "blocking", "preventing work", "affecting multiple"
            ],
            TicketPriority.HIGH: [
                "high", "soon", "affecting work", "impacting", "needed today",
                "business impact", "customer facing", "revenue impact"
            ],
            TicketPriority.MEDIUM: [
                "medium", "normal", "standard", "regular", "when possible",
                "convenience", "enhancement", "improvement"
            ],
            TicketPriority.LOW: [
                "low", "minor", "cosmetic", "nice to have", "future", "eventually",
                "suggestion", "feedback", "documentation"
            ]
        }

        self.sentiment_indicators = {
            "negative": [
                "frustrated", "angry", "upset", "disappointed", "terrible", "awful",
                "horrible", "worst", "hate", "annoying", "ridiculous", "unacceptable",
                "fed up", "sick of", "can't believe", "outrageous", "disgusted"
            ],
            "urgent": [
                "urgent", "emergency", "asap", "immediately", "critical", "help",
                "stuck", "blocked", "can't work", "deadline", "time sensitive",
                "production", "customer waiting", "business critical"
            ],
            "positive": [
                "thank you", "appreciate", "great", "excellent", "wonderful",
                "helpful", "quick", "fast", "efficient", "professional"
            ]
        }

    async def categorize_ticket(
        self,
        title: str,
        description: str,
        current_category: Optional[str] = None
    ) -> Dict[str, Any]:
        """Categorize ticket using AI/NLP analysis"""
        try:
            # Combine title and description for analysis
            text = f"{title} {description}".lower()
            
            # Analyze ticket type
            predicted_type = await self._predict_ticket_type(text)
            
            # Analyze priority
            predicted_priority = await self._predict_priority(text)
            
            # Generate tags
            suggested_tags = await self._generate_tags(text, predicted_type)
            
            # Analyze sentiment and urgency
            sentiment_analysis = await self._analyze_sentiment(text)
            
            # Calculate confidence scores
            confidence_scores = await self._calculate_confidence(text, predicted_type, predicted_priority)
            
            return {
                "predicted_type": predicted_type.value if predicted_type else None,
                "predicted_priority": predicted_priority.value if predicted_priority else None,
                "suggested_tags": suggested_tags,
                "sentiment_analysis": sentiment_analysis,
                "confidence_scores": confidence_scores,
                "ai_processed": True,
                "processed_at": datetime.utcnow().isoformat()
            }

        except Exception as e:
            logger.error(f"Error in AI categorization: {e}")
            return {
                "predicted_type": None,
                "predicted_priority": None,
                "suggested_tags": [],
                "sentiment_analysis": {"sentiment": "neutral", "urgency_score": 0.5},
                "confidence_scores": {"type": 0.0, "priority": 0.0},
                "ai_processed": False,
                "error": str(e)
            }

    async def _predict_ticket_type(self, text: str) -> Optional[TicketType]:
        """Predict ticket type based on content analysis"""
        try:
            type_scores = {}
            
            for ticket_type, keywords in self.category_keywords.items():
                score = 0
                for keyword in keywords:
                    # Count keyword occurrences with weight
                    occurrences = len(re.findall(r'\b' + re.escape(keyword) + r'\b', text))
                    score += occurrences * (len(keyword) / 10)  # Longer keywords get higher weight
                
                type_scores[ticket_type] = score

            # Return type with highest score if above threshold
            if type_scores:
                best_type = max(type_scores, key=type_scores.get)
                if type_scores[best_type] > 0.5:  # Minimum confidence threshold
                    return best_type

            return None

        except Exception as e:
            logger.error(f"Error predicting ticket type: {e}")
            return None

    async def _predict_priority(self, text: str) -> Optional[TicketPriority]:
        """Predict ticket priority based on content analysis"""
        try:
            priority_scores = {}
            
            for priority, keywords in self.priority_keywords.items():
                score = 0
                for keyword in keywords:
                    if keyword in text:
                        score += len(keyword) / 10
                
                priority_scores[priority] = score

            # Check for urgency indicators
            urgency_multiplier = 1.0
            if any(word in text for word in self.sentiment_indicators["urgent"]):
                urgency_multiplier = 2.0
            
            if any(word in text for word in self.sentiment_indicators["negative"]):
                urgency_multiplier *= 1.5

            # Apply urgency multiplier
            for priority in priority_scores:
                if priority in [TicketPriority.URGENT, TicketPriority.CRITICAL]:
                    priority_scores[priority] *= urgency_multiplier

            # Return priority with highest score
            if priority_scores:
                best_priority = max(priority_scores, key=priority_scores.get)
                if priority_scores[best_priority] > 0.3:
                    return best_priority

            return TicketPriority.MEDIUM  # Default priority

        except Exception as e:
            logger.error(f"Error predicting priority: {e}")
            return TicketPriority.MEDIUM

    async def _generate_tags(self, text: str, ticket_type: Optional[TicketType]) -> List[str]:
        """Generate relevant tags for the ticket"""
        try:
            tags = set()
            
            # Add type-specific tags
            if ticket_type and ticket_type in self.category_keywords:
                for keyword in self.category_keywords[ticket_type]:
                    if keyword in text:
                        tags.add(keyword.replace(" ", "_"))

            # Add common technical tags
            technical_terms = [
                "windows", "mac", "linux", "office365", "outlook", "teams",
                "vpn", "wifi", "network", "server", "database", "api",
                "mobile", "android", "ios", "browser", "chrome", "firefox"
            ]
            
            for term in technical_terms:
                if term in text:
                    tags.add(term)

            # Add urgency tags
            if any(word in text for word in self.sentiment_indicators["urgent"]):
                tags.add("urgent")
            
            if any(word in text for word in self.sentiment_indicators["negative"]):
                tags.add("escalation_candidate")

            return list(tags)[:10]  # Limit to 10 tags

        except Exception as e:
            logger.error(f"Error generating tags: {e}")
            return []

    async def _analyze_sentiment(self, text: str) -> Dict[str, Any]:
        """Analyze sentiment and urgency of the ticket"""
        try:
            sentiment_score = 0.5  # Neutral baseline
            urgency_score = 0.5
            
            # Check for negative sentiment
            negative_count = sum(1 for word in self.sentiment_indicators["negative"] if word in text)
            if negative_count > 0:
                sentiment_score = max(0.0, 0.5 - (negative_count * 0.2))
            
            # Check for positive sentiment
            positive_count = sum(1 for word in self.sentiment_indicators["positive"] if word in text)
            if positive_count > 0:
                sentiment_score = min(1.0, 0.5 + (positive_count * 0.2))
            
            # Check for urgency indicators
            urgent_count = sum(1 for word in self.sentiment_indicators["urgent"] if word in text)
            if urgent_count > 0:
                urgency_score = min(1.0, 0.5 + (urgent_count * 0.3))
            
            # Determine sentiment category
            if sentiment_score < 0.3:
                sentiment = "negative"
            elif sentiment_score > 0.7:
                sentiment = "positive"
            else:
                sentiment = "neutral"
            
            # Determine urgency level
            if urgency_score > 0.8:
                urgency_level = "critical"
            elif urgency_score > 0.6:
                urgency_level = "high"
            elif urgency_score > 0.4:
                urgency_level = "medium"
            else:
                urgency_level = "low"

            return {
                "sentiment": sentiment,
                "sentiment_score": round(sentiment_score, 2),
                "urgency_level": urgency_level,
                "urgency_score": round(urgency_score, 2),
                "requires_escalation": sentiment_score < 0.3 or urgency_score > 0.8
            }

        except Exception as e:
            logger.error(f"Error analyzing sentiment: {e}")
            return {
                "sentiment": "neutral",
                "sentiment_score": 0.5,
                "urgency_level": "medium",
                "urgency_score": 0.5,
                "requires_escalation": False
            }

    async def _calculate_confidence(
        self,
        text: str,
        predicted_type: Optional[TicketType],
        predicted_priority: Optional[TicketPriority]
    ) -> Dict[str, float]:
        """Calculate confidence scores for predictions"""
        try:
            type_confidence = 0.0
            priority_confidence = 0.0
            
            # Calculate type confidence
            if predicted_type:
                matching_keywords = sum(
                    1 for keyword in self.category_keywords[predicted_type]
                    if keyword in text
                )
                total_keywords = len(self.category_keywords[predicted_type])
                type_confidence = min(1.0, matching_keywords / (total_keywords * 0.1))
            
            # Calculate priority confidence
            if predicted_priority:
                matching_keywords = sum(
                    1 for keyword in self.priority_keywords[predicted_priority]
                    if keyword in text
                )
                total_keywords = len(self.priority_keywords[predicted_priority])
                priority_confidence = min(1.0, matching_keywords / (total_keywords * 0.2))
            
            return {
                "type": round(type_confidence, 2),
                "priority": round(priority_confidence, 2),
                "overall": round((type_confidence + priority_confidence) / 2, 2)
            }

        except Exception as e:
            logger.error(f"Error calculating confidence: {e}")
            return {"type": 0.0, "priority": 0.0, "overall": 0.0}

    async def suggest_similar_tickets(
        self,
        db: Session,
        text: str,
        organization_id: str,
        limit: int = 5
    ) -> List[Dict[str, Any]]:
        """Find similar tickets for knowledge base suggestions"""
        try:
            # This is a simplified implementation
            # In production, you would use vector similarity or more advanced NLP
            
            keywords = text.lower().split()[:10]  # Use first 10 words
            
            # Query similar tickets (simplified approach)
            similar_tickets = db.query(Ticket).filter(
                Ticket.organization_id == organization_id,
                Ticket.status.in_(["resolved", "closed"])
            ).limit(limit * 3).all()  # Get more to filter
            
            # Score tickets based on keyword overlap
            scored_tickets = []
            for ticket in similar_tickets:
                ticket_text = f"{ticket.title} {ticket.description}".lower()
                score = sum(1 for keyword in keywords if keyword in ticket_text)
                
                if score > 0:
                    scored_tickets.append({
                        "ticket_id": str(ticket.id),
                        "ticket_number": ticket.ticket_number,
                        "title": ticket.title,
                        "resolution": ticket.resolution,
                        "similarity_score": score,
                        "resolved_at": ticket.resolved_at.isoformat() if ticket.resolved_at else None
                    })
            
            # Sort by similarity score and return top results
            scored_tickets.sort(key=lambda x: x["similarity_score"], reverse=True)
            return scored_tickets[:limit]

        except Exception as e:
            logger.error(f"Error finding similar tickets: {e}")
            return []

    async def auto_suggest_resolution(
        self,
        ticket_type: TicketType,
        description: str
    ) -> Optional[str]:
        """Auto-suggest resolution based on ticket type and description"""
        try:
            # Knowledge base of common resolutions
            resolution_templates = {
                TicketType.IT_SUPPORT: {
                    "password": "Password has been reset. Please check your email for the new temporary password and change it upon first login.",
                    "email": "Email issue has been resolved. Please restart Outlook and try again. If the problem persists, contact IT support.",
                    "network": "Network connectivity issue has been resolved. Please restart your computer and reconnect to the network.",
                    "software": "Software issue has been resolved through reinstallation/update. Please test the application and confirm it's working."
                },
                TicketType.HR_QUERY: {
                    "leave": "Leave request has been processed according to company policy. Please check your leave balance in the HR portal.",
                    "benefits": "Benefits information has been provided. Please review the attached documents and contact HR if you have additional questions.",
                    "policy": "Policy clarification has been provided. Please refer to the employee handbook for complete details."
                },
                TicketType.FACILITIES: {
                    "maintenance": "Maintenance request has been completed. Please verify that the issue has been resolved.",
                    "access": "Access issue has been resolved. Please test your access card/badge and contact security if problems persist.",
                    "room": "Room/space request has been processed. Please check the booking system for confirmation."
                }
            }
            
            if ticket_type in resolution_templates:
                for keyword, template in resolution_templates[ticket_type].items():
                    if keyword in description.lower():
                        return template
            
            return None

        except Exception as e:
            logger.error(f"Error suggesting resolution: {e}")
            return None
