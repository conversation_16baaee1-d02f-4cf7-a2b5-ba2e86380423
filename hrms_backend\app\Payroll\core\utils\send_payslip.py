import requests
from flask import current_app
from flask_mail import Mail, Message
import requests
import json
import time
import smtplib
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText
from email.mime.base import MIMEBase
from email import encoders
from config import Config  # Import the email settings


PDFMONKEY_URL = "https://api.pdfmonkey.io/api/v1/documents"
PDFMONKEY_API_KEY = "t-QngzUDXbjHC5_LsUsHhsjrRTQergiw"  # Replace with your actual key
TEMPLATE_ID = "8A118503-A1DF-4341-9A4B-0ED467568B5B"  # Your PDFMonkey template ID


def generate_payslip_pdf(employee_payslip):
    """Generates a payslip PDF using PDFMonkey and returns the download URL."""

    if not TEMPLATE_ID:
        # print("🚨 ERROR: TEMPLATE_ID is missing!")
        return None  

    headers = {
        "Authorization": f"Bearer {PDFMONKEY_API_KEY}",
        "Content-Type": "application/json"
    }

    payload = {
        "document": {
            "document_template_id": TEMPLATE_ID,
            "payload": employee_payslip,
            "status": "pending"
        }
    }

    # # 🔹 PRINT the payload being sent for debugging
    # print("===== 📤 DEBUG: JSON PAYLOAD SENT TO PDFMONKEY =====")
    # print(json.dumps(payload, indent=4))

    response = requests.post(PDFMONKEY_URL, json=payload, headers=headers)
    
    if response.status_code != 201:
        # print("🚨 ERROR: Failed to generate PDF")
        # print(response.text)  # Print the error response from PDFMonkey
        return None  

    document_id = response.json()["document"]["id"]
    # print(f"✅ PDF request successful. Document ID: {document_id}")

    # Poll PDFMonkey API until the document is ready
    status_url = f"{PDFMONKEY_URL}/{document_id}"

    for attempt in range(10):  # Retry up to 10 times (~50 seconds total wait)
        time.sleep(5)  # Wait 5 seconds before checking status

        status_response = requests.get(status_url, headers=headers)
        if status_response.status_code != 200:
            # print("🚨 ERROR: Failed to check document status")
            return None  

        document_status = status_response.json()["document"]
        # print(f"📄 PDF Status [{attempt + 1}/10]: {document_status['status']}")

        if document_status["status"] == "success":
            pdf_url = document_status["download_url"]
            # print(f"✅ PDF Ready! Download URL: {pdf_url}")
            return pdf_url

    # print("🚨 ERROR: PDF generation took too long.")
    return None  
    
    


def send_payslip(employee_email, pdf_url):
    """Sends an email with the generated PDF payslip attached."""
    
    # print(f"📩 Sending payslip to {employee_email}...")

    # # Step 1: Download the PDF
    #Fetch the PDF content directly from the URL
    response = requests.get(pdf_url)
    if response.status_code != 200:
        return {"error": "Failed to download PDF"}

    # Step 2: Set up the email
    msg = MIMEMultipart()
    msg["From"] = Config.DEFAULT_FROM_EMAIL
    msg["To"] = employee_email
    msg["Subject"] = "Your Monthly Payslip"

    # Email body
    body = """
    Hello,

    Please find attached your payslip for this month.

    Best regards,
    Payroll Team
    """
    msg.attach(MIMEText(body, "plain"))

    # # Step 3: Attach the PDF file
    # Attach the PDF file directly from memory
    part = MIMEBase("application", "octet-stream")
    part.set_payload(response.content)  # Use the content from the request directly
    encoders.encode_base64(part)
    part.add_header("Content-Disposition", "attachment; filename=Payslip.pdf")
    msg.attach(part)

    # Step 4: Send the email using SMTP
    try:
        server = smtplib.SMTP(Config.MAIL_SERVER, Config.MAIL_PORT)
        server.starttls() if Config.MAIL_USE_TLS else None  # Enable TLS if configured
        server.login(Config.MAIL_USERNAME, Config.MAIL_PASSWORD)
        server.sendmail(Config.DEFAULT_FROM_EMAIL, employee_email, msg.as_string())
        server.quit()

        # print(f"✅ Payslip sent successfully to {employee_email}")
        return {"message": f"Payslip sent successfully to {employee_email}"}

    except Exception as e:
        # print(f"🚨 ERROR: Failed to send email: {str(e)}")
        return {"error": "Failed to send email"}


















# from flask import Flask
# from flask_mail import Mail, Message
# from config import Config  # ✅ Import config file

# # Initialize Flask App
# app = Flask(__name__)

# # Load Configurations from config.py
# app.config.from_object(Config)  # ✅ Automatically loads all mail settings

# # Initialize Flask-Mail
# mail = Mail(app)

# def send_payslip(recipient_email, pdf_url=None):
#     """
#     Send a test email without a PDF attachment if `pdf_url` is not needed.
#     """
#     try:
#         with app.app_context():
#             msg = Message(
#                 subject="Test Payslip Email",
#                 sender=app.config["MAIL_DEFAULT_SENDER"],  # ✅ Now it's correctly set
#                 recipients=[recipient_email],
#                 body="Hello, this is a test email to check SMTP delivery without attachments."
#             )

#             # 🛑 Skip PDF for testing
#             if pdf_url:
#                 print("⚠️ PDF attachment is skipped for testing.")

#             mail.send(msg)
#             print(f"✅ Email successfully sent to {recipient_email}")
#             return {"status": "success", "message": f"Email sent to {recipient_email}"}

#     except Exception as e:
#         print(f"🚨 Error sending email: {str(e)}")
#         return {"status": "error", "message": str(e)}
