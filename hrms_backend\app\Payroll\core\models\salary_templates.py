from core.databases.database import db
from core.models.basemodel import ModelBase
from sqlalchemy.orm import relationship
from datetime import datetime
from core.models.salary_tempplate_component_pivot import SalaryTemplateComponentsPivotModel
from core.models.salary_template_benefit_pivot import SalaryTemplateBenefitsPivotModel
from core.models.employees import EmployeeModel
from core.models.salary_components import SalaryComponentsModel
from core.models.benefits import BenefitsModel
class SalaryTemplatesModel(ModelBase):
    __tablename__ = "salary_templates"

    id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    template_name = db.Column(db.String(100), unique=True, nullable=True)
    employment_type = db.Column(db.String(45), nullable=True)
    description = db.Column(db.String(45), nullable=True)
    level = db.Column(db.String(45), nullable=True)
    salary_type = db.Column(db.String(45), nullable=True)
    employee_type = db.Column(db.String(45), nullable=True)
    country = db.Column(db.String(45), nullable=True)
    currency = db.Column(db.String(45), nullable=True)
    rate = db.Column(db.String(45), nullable=True)
    work_schedule = db.Column(db.String(45), nullable=True)
    hours_worked = db.Column(db.String(45), nullable=True)
    work_duration = db.Column(db.String(45), nullable=True)
    tax_type = db.Column(db.String(45), nullable=True)
    gross_pay = db.Column(db.Float, nullable=True)
    monthly_tax = db.Column(db.Float, nullable=True)
    annual_tax = db.Column(db.Float, nullable=True)
    total_taxable_monthly_sum = db.Column(db.Float, nullable=True)
    total_taxable_annual_sum = db.Column(db.Float, nullable=True)
    total_non_taxable_monthly_sum = db.Column(db.Float, nullable=True)
    total_non_taxable_annual_sum = db.Column(db.Float, nullable=True)
    total_statutory_monthly_sum = db.Column(db.Float, nullable=True)
    total_statutory_annual_sum = db.Column(db.Float, nullable=True)
    total_other_deductions_monthly_sum = db.Column(db.Float, nullable=True)
    total_other_deductions_annual_sum = db.Column(db.Float, nullable=True)
    netpay = db.Column(db.Float, nullable=True)
    user_id = db.Column(db.Integer, db.ForeignKey((ModelBase.dbSchema() + '.users.id')), nullable=False) 
    timestamp = db.Column(db.DateTime, nullable=False, default=datetime.now())
    employees = db.relationship('EmployeeModel', backref='template', cascade='all, delete-orphan')
    
    salary_template_components = db.relationship('SalaryTemplateComponentsPivotModel', back_populates='salary_template')
    salary_template_benefits = db.relationship('SalaryTemplateBenefitsPivotModel', back_populates='salary_template')
    pay_schedules = db.relationship("PaySchedulesModel", back_populates="salary_template", cascade="all, delete-orphan")

    # benefits = db.relationship('BenefitsModel', backref='salary_templates', lazy='dynamic')
 