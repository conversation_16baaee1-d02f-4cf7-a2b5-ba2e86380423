from core.repositories.zoho_people_integration_attendance import ZohoPeopleIntegrationAttedanceRepository

class ZohoPeopleIntegrationService:
    def __init__(self) -> None:
        self.repository = ZohoPeopleIntegrationAttedanceRepository()

    # Create or add a new Zoho Integration record
    def createZoho_attendance(self, **Kwargs):
        return self.repository.createZohoPeopleIntegrationAttendance(**Kwargs)

    # Get a Zoho Integration record by ID
    def getZoho_attendanceById(self, id):
        return self.repository.get_zoho_attendanceById(id)

    # Update a Zoho Integration record by ID
    def updateZoho_attendance(self, id, **Kwargs):
        return self.repository.update_zoho_attedance(id, **Kwargs)

    
    # Delete a Zoho Integration record by ID
    def deleteZoho_attendance(self, id):
        return self.repository.delete_zoho_attendance(id)


    def fetch_zoho_people_attendence(self):
        zoho_attendance = self.repository.fetch_zoho_attendance()
        print("aaaaaaadaddddadadad",zoho_attendance)
        total_zoho_attendance = len(zoho_attendance)
        return  zoho_attendance, total_zoho_attendance



