from core.databases.database import db
from core.models.basemodel import ModelBase

class WorkingDaysModel(ModelBase):
    __tablename__ = "working_days"

    id = db.<PERSON>umn(db.Integer, primary_key=True, autoincrement=True)
    employee_id = db.<PERSON>umn(db.<PERSON>teger, db.<PERSON>('employees.id'), nullable=False) 
    month = db.Column(db.String(50), nullable=False)
    year = db.Column(db.String(50), nullable=False)
    total_working_days = db.Column(db.Integer, nullable=False)
    present_days = db.Column(db.Integer, nullable=False)
    absent_days = db.Column(db.Integer, nullable=False)
    
    
      