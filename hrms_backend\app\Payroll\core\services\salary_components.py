from core.repositories.salary_components import SalaryComponentsRepository

class SalaryComponentsService:
    def __init__(self) -> None:
        self.repository = SalaryComponentsRepository()

    def createSalaryComponents(self, Kwargs):
        return self.repository.createSalaryComponents(**Kwargs)
    
    def fetchAll(self):
        salary_components = self.repository.fetchAll()
        total_salary_components = len(salary_components)
        return salary_components, total_salary_components
    
    def getSalaryComponents(self, id):
        return self.repository.getSalaryComponents(id)
    
    def updateSalaryComponents(self, id, Kwargs):
        return self.repository.updateSalaryComponents(id, **Kwargs)
    
    def getSalaryComponentsByKey(self, Kwarg):
        return self.repository.getSalaryComponentsByKeys(Kwarg)
    
    def deleteSalaryComponents(self, id):
        return self.repository.deleteSalaryComponents(id)
    
    # def searchSalaryComponent(self, search_value, offset, size):
    #     salary_components = self.repository.searchSalaryComponent(search_value, offset, size)
    #     total_salary_components = len(salary_components)
    #     return salary_components, total_salary_components

    # def sortSalaryComponent(self, sort_key, offset, size, sort_dir='asc'):
    #     salary_components = self.repository.sortSalaryComponent(sort_key, offset, size, sort_dir)
    #     total_salary_components = len(salary_components)
    #     return salary_components, total_salary_components

    # def fetchSalaryComponent(self, offset: int, size: int):
    #     salary_components = self.repository.fetchSalaryComponent(offset, size)
    #     total_salary_components = len(salary_components)
    #     return salary_components, total_salary_components