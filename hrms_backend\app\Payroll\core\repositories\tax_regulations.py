from core.models.tax_regulations import TaxRegulationsModel
from core.databases.database import db

class TaxRegulationsRepository:
    def __init__(self) -> None:
        pass

    @classmethod
    def createTaxRegulations(self, country, tax_rate, tax_type, currency):
        tax_regulations = TaxRegulationsModel(
            country=country,
            tax_rate=tax_rate,
            tax_type=tax_type,
            currency=currency
        )
        db.session.add(tax_regulations)
        db.session.commit()
        return tax_regulations

    @classmethod
    def getTaxRegulations(self, id):
        return TaxRegulationsModel.query.filter(TaxRegulationsModel.id == id).first()
    
    @classmethod
    def getTaxRegulationsByKeys(self, kwargs):
        return TaxRegulationsModel.query.filter_by(**kwargs).all()

    @classmethod
    def updateTaxRegulations(self, id, **kwargs):
        tax_regulations = TaxRegulationsModel.query.filter_by(id=id).first()
        if tax_regulations:
            for key, value in kwargs.items():
                setattr(tax_regulations, key, value)
            db.session.commit()
            return tax_regulations
        else:
            return None

    @classmethod
    def deleteTaxRegulations(self, id):
        return TaxRegulationsModel.query.filter(TaxRegulationsModel.id == id).delete()
        