from datetime import datetime
from core.models.payroll_history import PayrollHistoryModel
from core.databases.database import db
from core.models.remark import RemarkModel
from core.repositories.user import UserRepository
from sqlalchemy import desc
import traceback
from sqlalchemy import func, extract
from sqlalchemy.exc import NoResultFound, SQLAlchemyError
from core.utils.helper import get_nigeria_time_to_string
from core.models.pay_schedules import PaySchedulesModel
from core.models.employees import EmployeeModel
class PayrollHistoryRepository:
    def __init__(self) -> None:
        pass

    @classmethod
    def create_payroll_history(cls, **kwargs):
        user_id = UserRepository.authUserId()
        kwargs['user_id'] = user_id
        # print(kwargs)
        payroll_history = PayrollHistoryModel(**kwargs)
        db.session.add(payroll_history)
        db.session.commit()

        return payroll_history
    
    @classmethod
    def bulk_create_payroll_history(cls, payroll_data):
        try:
            user_id = UserRepository.authUserId()
            payroll_objects = [PayrollHistoryModel(**data, user_id=user_id) for data in payroll_data]

            db.session.bulk_save_objects(payroll_objects)
            db.session.commit()
            return payroll_objects
        except SQLAlchemyError as e:
            db.session.rollback()  
            print(f"Database error: {str(e)}") 
            return {"error": "Failed to create payroll history", "details": str(e)}
        except Exception as e:
            db.session.rollback()
            print(f"Unexpected error: {str(e)}")
            return {"error": "An unexpected error occurred", "details": str(e)}


    @classmethod
    def fetchPayrollHistory(cls):
        user_id = UserRepository().authUserId()
        return PayrollHistoryModel.query.filter_by(user_id=user_id).order_by(PayrollHistoryModel.created_at.desc()).all()

    @classmethod
    def get_by_id(cls, id):
        try:
            return PayrollHistoryModel.query.filter(
                PayrollHistoryModel.id == id,
                # PayrollHistoryModel.user_id == UserRepository.authUserId()
            ).first()
        except Exception as e:
            print(f"Error fetching payroll history by ID: {str(e)}")
            traceback.print_exc()
            return None

    @staticmethod
    def get_by_employee_id(employee_id):
        return (
            PayrollHistoryModel.query
            .filter_by(employee_id=employee_id)
            .order_by(PayrollHistoryModel.created_at.desc())
            .all()
        )

    @classmethod
    def update_payroll_history(cls, id, **kwargs):
        """
        Update an existing payroll history record by ID with provided fields.
        
        :param id: ID of the payroll history record to update.
        :param kwargs: Fields to update in the payroll history record.
        :return: Updated payroll history record or None if not found.
        """
       
        try:
            payroll_history = PayrollHistoryModel.query.filter_by(id=id).first() 
            if payroll_history:
                for key, value in kwargs.items():
                    setattr(payroll_history, key, value)
                db.session.commit()
            return payroll_history
        except Exception as e:
            print(f"Error updating payroll history record: {str(e)}")
            traceback.print_exc()
            return None

    classmethod
    def get_payroll_history_by_id(self, id):
        """Fetch a payroll history record by its ID."""
        try:
            # Query the PayrollHistoryModel to find the record by ID
            payroll_history_record = PayrollHistoryModel.query.get(id)

            # Check if the record exists
            if not payroll_history_record:
                return None  # No record found
            
            return payroll_history_record  # Return the found record
        
        except NoResultFound:
            return None  # Handle case where no result is found gracefully
        except Exception as e:
            print(e)  # Log unexpected errors
            return None  # Or you can raise an error based on your error handling strategy
        
    def get_unprocessed_payroll_history_ids(self, year: int, month: int):
     # Create a datetime object for the first day of the given month and year
     start_date = datetime(year, month, 1)

     # Determine the end of the date range (first day of the next month)
     # If December, next month will be January of the next year
     if month == 12:
        end_date = datetime(year + 1, 1, 1)
     else:
        end_date = datetime(year, month + 1, 1)

     # Query the database to get all payroll history IDs that:
     ids = db.session.query(PayrollHistoryModel.id).filter(
        PayrollHistoryModel.created_at >= start_date,   
        PayrollHistoryModel.created_at < end_date,      
        PayrollHistoryModel.is_processed == False      
     ).all()

     # Extract just the ID values from the result tuples
     id_list = [id_tuple[0] for id_tuple in ids]
     return id_list
    

    def delete_unprocessed_payroll_history(self):
        try:
            deleted_count = db.session.query(PayrollHistoryModel).filter_by(is_processed=False).delete()
            db.session.commit()
            return {"message": f"Successfully deleted {deleted_count} unprocessed payroll records."}
        except SQLAlchemyError as e:
            db.session.rollback()
            print(f"Error deleting unprocessed payroll: {e}")
            return {"message": "An error occurred while deleting unprocessed payroll history."}, 500

    def get_by_employee_and_schedule(self, employee_id: int, pay_schedule_id: int):
        try:
            return PayrollHistoryModel.query.filter_by(
                employee_id=employee_id,
                pay_schedle_id=pay_schedule_id
            ).first()
        except Exception as e:
            print(f"Error retrieving payroll history: {e}")
            return None


    @classmethod
    def fetchPayrollHistoryByKeys(self, filters=None):
        user_id = UserRepository().authUserId()
        
        query = PayrollHistoryModel.query.filter_by(user_id=user_id)

        if filters:
            for key, value in filters.items():
                query = query.filter(getattr(PayrollHistoryModel, key) == value)

        return query.order_by(PayrollHistoryModel.created_at.desc()).all()

    @classmethod
    def get_existing_payroll_ids(self, ids):
        return {p.id for p in PayrollHistoryModel.query.filter(PayrollHistoryModel.id.in_(ids)).all()}

    @classmethod
    def bulk_update_payroll_history(self, ids):
        payroll_updated = PayrollHistoryModel.query.filter(PayrollHistoryModel.id.in_(ids))\
            .update({"is_processed": True, "is_processed_created": get_nigeria_time_to_string()}, synchronize_session=False)
        db.session.commit()
        return payroll_updated
    
    @classmethod
    def get_existing_payroll_ids_for_current_month(self):
        user_id = UserRepository().authUserId()

        current_month = datetime.now().month
        current_year = datetime.now().year

        return {
            p.employee_id for p in PayrollHistoryModel.query
            .filter(
                PayrollHistoryModel.user_id == user_id,
                extract('month', PayrollHistoryModel.created_at) == current_month,
                extract('year', PayrollHistoryModel.created_at) == current_year
            )
            .all()
        }
        
    @classmethod
    def get_payroll_history(self, transaction_id, reference_code):
        """Fetch payroll history using `reference_code` instead of `payroll_id`."""
        return PayrollHistoryModel.query.filter(
            (PayrollHistoryModel.transaction_id == str(transaction_id)) | 
            (PayrollHistoryModel.reference_code == reference_code)  # ✅ Use reference_code
        ).first()

    @classmethod
    def getPayrollsByLatestSchedule(self):
        user_id = UserRepository().authUserId()
        latest_schedule = db.session.query(
            PaySchedulesModel.id
        ).filter_by(user_id=user_id).order_by(
            PaySchedulesModel.id.desc()
        ).limit(1).scalar_subquery()

        payrolls = PayrollHistoryModel.query.filter(
            PayrollHistoryModel.user_id == user_id,
            PayrollHistoryModel.pay_schedle_id == latest_schedule,
            PayrollHistoryModel.is_processed == False
        ).all()
        return payrolls

    def get_employee_ids_in_pay_schedule(self, schedule_id):
        results = PayrollHistoryModel.query \
            .with_entities(PayrollHistoryModel.employee_id) \
            .filter_by(pay_schedle_id=schedule_id) \
            .all()
        return [r.employee_id for r in results]

    def get_active_employees_not_in_schedule(self, existing_ids):
        query = EmployeeModel.query
        if existing_ids:
            query = query.filter(~EmployeeModel.id.in_(existing_ids))
        return query.all()

    def get_by_pay_schedule_id(cls, pay_schedule_id: int, page: int, limit: int):
        query = PayrollHistoryModel.query.filter_by(pay_schedle_id=pay_schedule_id)
        total = query.count()
        records = query.offset((page - 1) * limit).limit(limit).all()
        return records, total
    
    @classmethod
    def sync_active_employees_to_schedule(cls, schedule_id):
        # Step 1: Get all active employees
        active_employees = EmployeeModel.query.filter_by(status="Active").all()

        # Step 2: Get employee IDs already in payroll history for this schedule
        existing_ids = {
            r.employee_id for r in PayrollHistoryModel.query
            .with_entities(PayrollHistoryModel.employee_id)
            .filter_by(pay_schedle_id=schedule_id)
            .all()
        }

        # Step 3: Filter active employees not in existing payroll history
        new_employees = [emp for emp in active_employees if emp.id not in existing_ids]
        
        # ✅ Fetch the schedule object once
        schedule = PaySchedulesModel.query.get(schedule_id)
        if not schedule:
            raise ValueError(f"Pay schedule with ID {schedule_id} not found.")

        # Step 4: Prepare and create new payroll history records
        new_records = []
        user_id = UserRepository.authUserId()

        for emp in new_employees:
            record = PayrollHistoryModel(
                employee_id=emp.id,
                pay_schedle_id=schedule_id,
                user_id=user_id,
                payschedle_name=schedule.name,
                gross_pay=emp.gross_pay or 0.0,
                monthly_tax=emp.monthly_tax or 0.0,
                annual_tax=emp.annual_tax or 0.0,
                total_taxable_monthly_sum=emp.total_taxable_monthly_sum or 0.0,
                total_taxable_annual_sum=emp.total_taxable_annual_sum or 0.0,
                total_non_taxable_monthly_sum=emp.total_non_taxable_monthly_sum or 0.0,
                total_non_taxable_annual_sum=emp.total_non_taxable_annual_sum or 0.0,
                total_statutory_monthly_sum=emp.total_statutory_monthly_sum or 0.0,
                total_statutory_annual_sum=emp.total_statutory_annual_sum or 0.0,
                total_other_deductions_monthly_sum=emp.total_other_deductions_monthly_sum or 0.0,
                total_other_deductions_annual_sum=emp.total_other_deductions_annual_sum or 0.0,
                netpay=emp.netpay or 0.0,
                pension=0.0,
                nhf=0.0,
                cost_to_company=emp.gross_pay or 0.0
            )
            new_records.append(record)

        db.session.bulk_save_objects(new_records)
        db.session.commit()

        return new_records

