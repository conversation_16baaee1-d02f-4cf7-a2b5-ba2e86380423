/**
 * Holiday Management Component
 * Features: Holiday calendar, regional holidays, custom holidays
 */

import React, { useState, useEffect } from 'react';
import {
  Calendar,
  Plus,
  Edit,
  Trash2,
  Globe,
  MapPin,
  Users,
  Save,
  X,
  AlertCircle,
  CheckCircle
} from 'lucide-react';
import apiService from '../../services/api';

const HolidayManager = () => {
  const [holidays, setHolidays] = useState([]);
  const [loading, setLoading] = useState(true);
  const [showForm, setShowForm] = useState(false);
  const [editingHoliday, setEditingHoliday] = useState(null);
  const [selectedYear, setSelectedYear] = useState(new Date().getFullYear());
  const [formData, setFormData] = useState({
    date: '',
    name: '',
    description: '',
    holiday_type: 'public',
    is_optional: false,
    applicable_locations: [],
    applicable_departments: []
  });
  const [errors, setErrors] = useState({});

  useEffect(() => {
    loadHolidays();
  }, [selectedYear]);

  const loadHolidays = async () => {
    try {
      setLoading(true);
      const response = await apiService.get(`/leave/calendar?year=${selectedYear}`);
      setHolidays(response.holidays || []);
    } catch (error) {
      console.error('Error loading holidays:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (field, value) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: null }));
    }
  };

  const validateForm = () => {
    const newErrors = {};

    if (!formData.date) newErrors.date = 'Date is required';
    if (!formData.name.trim()) newErrors.name = 'Holiday name is required';

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async () => {
    if (!validateForm()) return;

    try {
      if (editingHoliday) {
        await apiService.put(`/leave/calendar/${editingHoliday.id}`, formData);
      } else {
        await apiService.post('/leave/calendar', formData);
      }
      
      await loadHolidays();
      resetForm();
    } catch (error) {
      console.error('Error saving holiday:', error);
    }
  };

  const handleEdit = (holiday) => {
    setEditingHoliday(holiday);
    setFormData({
      date: holiday.date,
      name: holiday.name,
      description: holiday.description || '',
      holiday_type: holiday.holiday_type,
      is_optional: holiday.is_optional,
      applicable_locations: holiday.applicable_locations || [],
      applicable_departments: holiday.applicable_departments || []
    });
    setShowForm(true);
  };

  const handleDelete = async (holidayId) => {
    if (window.confirm('Are you sure you want to delete this holiday?')) {
      try {
        await apiService.delete(`/leave/calendar/${holidayId}`);
        await loadHolidays();
      } catch (error) {
        console.error('Error deleting holiday:', error);
      }
    }
  };

  const resetForm = () => {
    setFormData({
      date: '',
      name: '',
      description: '',
      holiday_type: 'public',
      is_optional: false,
      applicable_locations: [],
      applicable_departments: []
    });
    setEditingHoliday(null);
    setShowForm(false);
    setErrors({});
  };

  const getHolidayTypeIcon = (type) => {
    switch (type) {
      case 'public': return <Globe className="w-4 h-4 text-blue-500" />;
      case 'regional': return <MapPin className="w-4 h-4 text-green-500" />;
      case 'religious': return <Calendar className="w-4 h-4 text-purple-500" />;
      default: return <Calendar className="w-4 h-4 text-gray-500" />;
    }
  };

  const getHolidayTypeColor = (type) => {
    switch (type) {
      case 'public': return 'bg-blue-100 text-blue-800';
      case 'regional': return 'bg-green-100 text-green-800';
      case 'religious': return 'bg-purple-100 text-purple-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const years = Array.from({ length: 5 }, (_, i) => new Date().getFullYear() + i);

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Holiday Management</h2>
          <p className="text-gray-600 mt-1">Manage company holidays and observances</p>
        </div>
        
        <div className="flex items-center space-x-3">
          <select
            value={selectedYear}
            onChange={(e) => setSelectedYear(parseInt(e.target.value))}
            className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-agno-primary focus:border-transparent"
          >
            {years.map(year => (
              <option key={year} value={year}>{year}</option>
            ))}
          </select>
          
          <button
            onClick={() => setShowForm(true)}
            className="flex items-center px-4 py-2 agno-bg-primary text-white rounded-lg hover:bg-blue-700"
          >
            <Plus size={16} className="mr-2" />
            Add Holiday
          </button>
        </div>
      </div>

      {/* Holidays List */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        {loading ? (
          <div className="flex items-center justify-center h-64">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-agno-primary"></div>
          </div>
        ) : holidays.length === 0 ? (
          <div className="text-center py-12">
            <Calendar className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-500">No holidays configured for {selectedYear}</p>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Date
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Holiday Name
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Type
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {holidays.map((holiday) => (
                  <tr key={holiday.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-medium text-gray-900">
                        {new Date(holiday.date).toLocaleDateString('en-US', {
                          weekday: 'short',
                          month: 'short',
                          day: 'numeric'
                        })}
                      </div>
                      <div className="text-sm text-gray-500">
                        {new Date(holiday.date).toLocaleDateString()}
                      </div>
                    </td>
                    <td className="px-6 py-4">
                      <div className="text-sm font-medium text-gray-900">{holiday.name}</div>
                      {holiday.description && (
                        <div className="text-sm text-gray-500">{holiday.description}</div>
                      )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        {getHolidayTypeIcon(holiday.holiday_type)}
                        <span className={`ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getHolidayTypeColor(holiday.holiday_type)} capitalize`}>
                          {holiday.holiday_type}
                        </span>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex flex-col space-y-1">
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                          holiday.is_optional 
                            ? 'bg-amber-100 text-amber-800' 
                            : 'bg-green-100 text-green-800'
                        }`}>
                          {holiday.is_optional ? 'Optional' : 'Mandatory'}
                        </span>
                        {holiday.applicable_locations?.length > 0 && (
                          <span className="text-xs text-gray-500">
                            {holiday.applicable_locations.length} locations
                          </span>
                        )}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <div className="flex items-center space-x-2">
                        <button
                          onClick={() => handleEdit(holiday)}
                          className="text-agno-primary hover:text-blue-700"
                        >
                          <Edit size={16} />
                        </button>
                        <button
                          onClick={() => handleDelete(holiday.id)}
                          className="text-red-600 hover:text-red-800"
                        >
                          <Trash2 size={16} />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>

      {/* Holiday Form Modal */}
      {showForm && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-2xl shadow-2xl w-full max-w-2xl max-h-[90vh] overflow-hidden">
            {/* Header */}
            <div className="agno-gradient text-white p-6">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="text-xl font-bold">
                    {editingHoliday ? 'Edit Holiday' : 'Add Holiday'}
                  </h3>
                  <p className="text-blue-100 mt-1">Configure holiday details and applicability</p>
                </div>
                <button
                  onClick={resetForm}
                  className="text-white hover:text-blue-200 transition-colors"
                >
                  <X size={24} />
                </button>
              </div>
            </div>

            {/* Form Content */}
            <div className="p-6 space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Date */}
                <div>
                  <label className="block text-sm font-semibold text-gray-700 mb-2">
                    Date *
                  </label>
                  <input
                    type="date"
                    value={formData.date}
                    onChange={(e) => handleInputChange('date', e.target.value)}
                    className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-agno-primary focus:border-transparent ${
                      errors.date ? 'border-red-500' : 'border-gray-300'
                    }`}
                  />
                  {errors.date && (
                    <p className="text-red-500 text-sm mt-1 flex items-center">
                      <AlertCircle size={14} className="mr-1" />
                      {errors.date}
                    </p>
                  )}
                </div>

                {/* Holiday Type */}
                <div>
                  <label className="block text-sm font-semibold text-gray-700 mb-2">
                    Holiday Type
                  </label>
                  <select
                    value={formData.holiday_type}
                    onChange={(e) => handleInputChange('holiday_type', e.target.value)}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-agno-primary focus:border-transparent"
                  >
                    <option value="public">Public Holiday</option>
                    <option value="regional">Regional Holiday</option>
                    <option value="religious">Religious Holiday</option>
                  </select>
                </div>

                {/* Holiday Name */}
                <div className="md:col-span-2">
                  <label className="block text-sm font-semibold text-gray-700 mb-2">
                    Holiday Name *
                  </label>
                  <input
                    type="text"
                    value={formData.name}
                    onChange={(e) => handleInputChange('name', e.target.value)}
                    className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-agno-primary focus:border-transparent ${
                      errors.name ? 'border-red-500' : 'border-gray-300'
                    }`}
                    placeholder="e.g., New Year's Day"
                  />
                  {errors.name && (
                    <p className="text-red-500 text-sm mt-1 flex items-center">
                      <AlertCircle size={14} className="mr-1" />
                      {errors.name}
                    </p>
                  )}
                </div>

                {/* Description */}
                <div className="md:col-span-2">
                  <label className="block text-sm font-semibold text-gray-700 mb-2">
                    Description
                  </label>
                  <textarea
                    value={formData.description}
                    onChange={(e) => handleInputChange('description', e.target.value)}
                    rows={3}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-agno-primary focus:border-transparent resize-none"
                    placeholder="Optional description or significance of the holiday"
                  />
                </div>

                {/* Optional Holiday */}
                <div className="md:col-span-2">
                  <div className="flex items-center">
                    <input
                      type="checkbox"
                      checked={formData.is_optional}
                      onChange={(e) => handleInputChange('is_optional', e.target.checked)}
                      className="h-4 w-4 text-agno-primary focus:ring-agno-primary border-gray-300 rounded"
                    />
                    <label className="ml-2 text-sm text-gray-700">
                      Optional holiday (employees can choose to observe)
                    </label>
                  </div>
                </div>
              </div>
            </div>

            {/* Footer */}
            <div className="px-6 py-4 bg-gray-50 flex justify-end space-x-3">
              <button
                onClick={resetForm}
                className="px-6 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50"
              >
                Cancel
              </button>
              <button
                onClick={handleSubmit}
                className="flex items-center px-6 py-2 agno-bg-primary text-white rounded-lg hover:bg-blue-700"
              >
                <Save size={16} className="mr-2" />
                {editingHoliday ? 'Update Holiday' : 'Add Holiday'}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default HolidayManager;
