#!/usr/bin/env python3
"""
Check table structure in the database
"""

import sys
import os

# Add the app directory to Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'app'))

from sqlalchemy import text
from app.db.session import engine

def check_table_structure():
    """Check the structure of key tables"""
    try:
        with engine.connect() as conn:
            # Check users table
            print("=== USERS TABLE STRUCTURE ===")
            result = conn.execute(text("""
                SELECT column_name, data_type, is_nullable 
                FROM information_schema.columns 
                WHERE table_name = 'users' AND table_schema = 'public'
                ORDER BY ordinal_position
            """))
            
            users_columns = result.fetchall()
            if users_columns:
                for row in users_columns:
                    print(f"  {row[0]} ({row[1]}) - Nullable: {row[2]}")
            else:
                print("  No users table found")
            
            print("\n=== TICKETS TABLE STRUCTURE ===")
            result = conn.execute(text("""
                SELECT column_name, data_type, is_nullable 
                FROM information_schema.columns 
                WHERE table_name = 'tickets' AND table_schema = 'public'
                ORDER BY ordinal_position
            """))
            
            tickets_columns = result.fetchall()
            if tickets_columns:
                for row in tickets_columns:
                    print(f"  {row[0]} ({row[1]}) - Nullable: {row[2]}")
            else:
                print("  No tickets table found")
            
            print("\n=== ORGANIZATIONS TABLE STRUCTURE ===")
            result = conn.execute(text("""
                SELECT column_name, data_type, is_nullable 
                FROM information_schema.columns 
                WHERE table_name = 'organizations' AND table_schema = 'public'
                ORDER BY ordinal_position
            """))
            
            orgs_columns = result.fetchall()
            if orgs_columns:
                for row in orgs_columns:
                    print(f"  {row[0]} ({row[1]}) - Nullable: {row[2]}")
            else:
                print("  No organizations table found")
            
            print("\n=== EMPLOYEES TABLE STRUCTURE ===")
            result = conn.execute(text("""
                SELECT column_name, data_type, is_nullable 
                FROM information_schema.columns 
                WHERE table_name = 'employees' AND table_schema = 'public'
                ORDER BY ordinal_position
            """))
            
            emp_columns = result.fetchall()
            if emp_columns:
                for row in emp_columns:
                    print(f"  {row[0]} ({row[1]}) - Nullable: {row[2]}")
            else:
                print("  No employees table found")
                
            # Check if we can insert test data
            print("\n=== TESTING DATA INSERTION ===")
            
            # Test organization insertion
            from uuid import uuid4
            from datetime import datetime
            
            org_id = str(uuid4())
            try:
                conn.execute(text("""
                    INSERT INTO organizations (id, name, created_at, updated_at, is_active)
                    VALUES (:id, :name, :created_at, :updated_at, :is_active)
                """), {
                    'id': org_id,
                    'name': 'Test Organization Structure',
                    'created_at': datetime.utcnow(),
                    'updated_at': datetime.utcnow(),
                    'is_active': True
                })
                print("✅ Organization insertion successful")
                
                # Test user insertion with correct columns
                user_id = str(uuid4())
                user_columns_dict = {col[0]: col for col in users_columns}
                
                user_data = {
                    'id': user_id,
                    'email': '<EMAIL>',
                    'password_hash': 'hashed_password',
                    'first_name': 'Structure',
                    'last_name': 'Test',
                    'organization_id': org_id,
                    'created_at': datetime.utcnow(),
                    'updated_at': datetime.utcnow(),
                    'is_active': True
                }
                
                # Only include columns that exist
                filtered_user_data = {}
                for key, value in user_data.items():
                    if key in user_columns_dict:
                        filtered_user_data[key] = value
                
                if filtered_user_data:
                    columns = ', '.join(filtered_user_data.keys())
                    placeholders = ', '.join([f':{key}' for key in filtered_user_data.keys()])
                    
                    conn.execute(text(f"""
                        INSERT INTO users ({columns})
                        VALUES ({placeholders})
                    """), filtered_user_data)
                    print("✅ User insertion successful")
                else:
                    print("❌ No valid user columns found")
                
                # Test ticket insertion with correct columns
                ticket_id = str(uuid4())
                ticket_columns_dict = {col[0]: col for col in tickets_columns}
                
                ticket_data = {
                    'id': ticket_id,
                    'ticket_number': 'TKT-STRUCT-001',
                    'title': 'Structure Test Ticket',
                    'description': 'Testing ticket structure',
                    'ticket_type': 'it_support',
                    'priority': 'medium',
                    'status': 'open',
                    'organization_id': org_id,
                    'created_at': datetime.utcnow(),
                    'updated_at': datetime.utcnow(),
                    'is_active': True
                }
                
                # Only include columns that exist
                filtered_ticket_data = {}
                for key, value in ticket_data.items():
                    if key in ticket_columns_dict:
                        filtered_ticket_data[key] = value
                
                if filtered_ticket_data:
                    columns = ', '.join(filtered_ticket_data.keys())
                    placeholders = ', '.join([f':{key}' for key in filtered_ticket_data.keys()])
                    
                    conn.execute(text(f"""
                        INSERT INTO tickets ({columns})
                        VALUES ({placeholders})
                    """), filtered_ticket_data)
                    print("✅ Ticket insertion successful")
                else:
                    print("❌ No valid ticket columns found")
                
                # Cleanup
                conn.execute(text("DELETE FROM tickets WHERE id = :id"), {'id': ticket_id})
                conn.execute(text("DELETE FROM users WHERE id = :id"), {'id': user_id})
                conn.execute(text("DELETE FROM organizations WHERE id = :id"), {'id': org_id})
                conn.commit()
                print("✅ Cleanup successful")
                
            except Exception as e:
                print(f"❌ Data insertion test failed: {e}")
                conn.rollback()
                
    except Exception as e:
        print(f"Error checking table structure: {e}")

if __name__ == "__main__":
    check_table_structure()
