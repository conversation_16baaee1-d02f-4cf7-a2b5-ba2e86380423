"""
Check Database Schema
Simple script to check the actual database schema
"""

import sys
import os
from sqlalchemy import text

# Add the app directory to the Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'app'))

from app.db.session import SessionLocal

def check_employees_table():
    """Check the employees table schema"""
    
    db = SessionLocal()
    
    try:
        print("🔍 Checking employees table schema...")
        print("=" * 50)
        
        # Get table columns
        query = text("""
            SELECT column_name, data_type, is_nullable, column_default
            FROM information_schema.columns 
            WHERE table_name = 'employees' 
            ORDER BY ordinal_position;
        """)
        
        result = db.execute(query)
        columns = result.fetchall()
        
        if columns:
            print("✅ Employees table columns:")
            print("-" * 30)
            for col in columns:
                nullable = "NULL" if col[2] == "YES" else "NOT NULL"
                default = f" DEFAULT {col[3]}" if col[3] else ""
                print(f"  {col[0]:<25} {col[1]:<20} {nullable}{default}")
        else:
            print("❌ Employees table not found")
        
        # Check users table too
        print("\n🔍 Checking users table schema...")
        print("=" * 50)
        
        query = text("""
            SELECT column_name, data_type, is_nullable, column_default
            FROM information_schema.columns 
            WHERE table_name = 'users' 
            ORDER BY ordinal_position;
        """)
        
        result = db.execute(query)
        columns = result.fetchall()
        
        if columns:
            print("✅ Users table columns:")
            print("-" * 30)
            for col in columns:
                nullable = "NULL" if col[2] == "YES" else "NOT NULL"
                default = f" DEFAULT {col[3]}" if col[3] else ""
                print(f"  {col[0]:<25} {col[1]:<20} {nullable}{default}")
        else:
            print("❌ Users table not found")
            
    except Exception as e:
        print(f"❌ Error checking schema: {e}")
    finally:
        db.close()

if __name__ == "__main__":
    check_employees_table()
