"""Create paystack_credential table

Revision ID: d3de5a711d03
Revises: c5ea1b33f300
Create Date: 2024-11-07 11:15:23.339124

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy import func, TIMESTAMP, ForeignKey
from sqlalchemy import Column, Integer, String


# revision identifiers, used by Alembic.
revision: str = 'd3de5a711d03'
down_revision: Union[str, None] = 'c5ea1b33f300'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None



def upgrade() -> None:
    op.create_table(
        'pay_stack_credential',
        Column('id', Integer, primary_key=True),
        <PERSON>umn("user_id", Integer, ForeignKey("users.id")),
        Column('paystack_key', String(250), nullable=True),
        Column("timestamp", TIMESTAMP, server_default=func.now()),
        )

def downgrade() -> None:
    op.drop_table("pay_stack_credential")
