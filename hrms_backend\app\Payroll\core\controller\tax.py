from flask import request, jsonify
from flask_smorest import Blueprint, abort
from core.services.tax_calculator_service import TaxCalculatorService

blueprint = Blueprint('tax', __name__, url_prefix='/api')

@blueprint.route('/calculate-tax', methods=['POST'])
def calculate_tax():
    try:
        data = request.get_json()

        country = data.get("country", "Nigeria")
        tax_type = data.get("tax_type", "PAYE")
        annual_earnings = float(data["annual_earnings"])
        annual_pen = float(data.get("annual_pen", 0))
        annual_nhf = float(data.get("annual_nhf", 0))

        tax_service = TaxCalculatorService(country, tax_type)
        annual_tax = tax_service.calculate_annual_tax(annual_earnings, annual_pen, annual_nhf)
        monthly_tax = tax_service.calculate_monthly_tax(annual_tax)

        return jsonify({
            "country": country,
            "tax_type": tax_type,
            "annual_tax": annual_tax,
            "monthly_tax": monthly_tax
        }), 200

    except Exception as e:
        return jsonify({"error": str(e)}), 400
