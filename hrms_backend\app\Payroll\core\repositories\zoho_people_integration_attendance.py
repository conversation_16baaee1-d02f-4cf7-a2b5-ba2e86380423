from core.models.zoho_people_integration_attendance import ZohoPeopleIntegrationAttendanceModel
from core.databases.database import db
from sqlalchemy import desc
import traceback

class ZohoPeopleIntegrationAttedanceRepository:
    def __init__(self) -> None:
        pass

    @classmethod
    def createZohoPeopleIntegrationAttendance(self, first_name, last_name,email,gender,organization_profile,employee_status,employee_type,employee_id,bank_code,bank_name, account_no,department,designation,salary_templates,mobile_no,division,location,nhf_mortgage_bank,nhf_no):
        zoho_attendance = ZohoPeopleIntegrationAttendanceModel(
            
            first_name = first_name,
            last_name = last_name,
            email = email,
            gender = gender,
            organization_profile = organization_profile,
            employee_status = employee_status,
            employee_type = employee_type,
            employee_id = employee_id,
            bank_code = bank_code,
            bank_name = bank_name,
            account_no = account_no,
            department = department,
            designation = designation,
            salary_template = salary_template,
            mobile_no = mobile_no,
            division = division,
            location = nhf_mortgage_bank,
            nhf_no = nhf_no
           
        )

        db.session.add( zoho_attendance )
        db.session.commit()
        return  zoho_attendance 


    
    @classmethod
    def fetchAll(self):
        return ZohoPeopleIntegrationModel.query.order_by(ZohoPeopleIntegrationModel.created_at.desc()).all()

  
        

    @classmethod
    def get_zoho_attendanceById(self, id):
        return ZohoPeopleIntegrationAttendanceModel.query.filter(ZohoPeopleIntegrationAttendanceModel.id == id).first()

    @classmethod
    def get_zoho_attendanceByEmployeeId(self, employee_id):
        return ZohoPeopleIntegrationAttendanceModel.query.filter_by(employee_id=employee_id).first()

    @classmethod
    def update_zoho_attedance(self, id, **kwargs):
        zoho_attendance  = ZohoPeopleIntegrationAttendanceModel.query.filter_by(id=id).first()
        if zoho_attendance :
            for key, value in kwargs.items():
                setattr(integration, key, value)
            db.session.commit()
            return zoho_attendance 
        else:
            return None

    @classmethod
    def delete_zoho_attendance (self, id):
        ZohoPeopleIntegrationAttendanceModel.query.filter(ZohoPeopleIntegrationAttendanceModel.id == id).delete()
        db.session.commit()
        return

    @classmethod
    def fetch_zoho_attendance(self):
        zoho_attendance = ZohoPeopleIntegrationAttendanceModel.query.all()
        print('yyyyyyyrrrrrrr',zoho_attendance)
        return zoho_attendance 
 