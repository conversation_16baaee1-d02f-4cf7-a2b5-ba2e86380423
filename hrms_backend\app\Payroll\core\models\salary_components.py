from core.databases.database import db
from core.models.basemodel import ModelBase
from datetime import datetime
from sqlalchemy.orm import relationship
class SalaryComponentsModel(ModelBase):
    __tablename__ = "salary_components"

    id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    component_name = db.Column(db.String(45), nullable=False)
    payslip_name = db.Column(db.String(45), nullable=False)
    component_type = db.Column(db.String(45), nullable=False)
    calculation_type = db.Column(db.String(45), nullable=False)
    duration = db.Column(db.String(45), nullable=False)
    cycle = db.Column(db.String(45), nullable=False)
    amount = db.Column(db.Float, nullable=True)
    value = db.Column(db.Float, nullable=True)
    user_id = db.Column(db.Integer, db.<PERSON><PERSON>((ModelBase.dbSchema() + '.users.id')), nullable=False) 
    timestamp = db.Column(db.DateTime, nullable=False, default=datetime.now())
    salary_template_components = db.relationship('SalaryTemplateComponentsPivotModel', back_populates='salary_component')
    employee_components = db.relationship('EmployeeComponentsPivotModel', back_populates='salary_component')