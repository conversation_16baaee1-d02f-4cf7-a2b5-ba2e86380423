from core.models.salary_templates import SalaryTemplatesModel
from core.databases.database import db
from core.repositories.user import UserRepository

class SalaryTemplatesRepository:
    def __init__(self) -> None:
        pass

    @classmethod
    def createSalaryTemplates(self, template_name=None, tax_type=None, hours_worked=None, work_duration=None, work_schedule=None, 
                              country=None, currency=None, rate=None, employment_type=None, gross_pay=None, employee_type=None, 
                              description=None, level=None, salary_type=None,
                              monthly_tax=None, annual_tax=None, total_taxable_monthly_sum=None, 
                              total_taxable_annual_sum=None, total_non_taxable_monthly_sum=None, 
                              total_non_taxable_annual_sum=None, total_statutory_monthly_sum=None, 
                              total_statutory_annual_sum=None, total_other_deductions_monthly_sum=None, 
                              total_other_deductions_annual_sum=None, netpay=None):
        
        salary_templates = SalaryTemplatesModel(
            template_name=template_name,
            employment_type=employment_type,
            description=description,
            level=level,
            salary_type=salary_type,
            employee_type=employee_type,
            gross_pay=gross_pay,
            country=country,
            currency=currency,
            tax_type=tax_type,
            rate=rate,
            work_schedule=work_schedule,
            hours_worked=hours_worked,
            work_duration=work_duration,
            
            # Nullable fields
            monthly_tax=monthly_tax,
            annual_tax=annual_tax,
            total_taxable_monthly_sum=total_taxable_monthly_sum,
            total_taxable_annual_sum=total_taxable_annual_sum,
            total_non_taxable_monthly_sum=total_non_taxable_monthly_sum,
            total_non_taxable_annual_sum=total_non_taxable_annual_sum,
            total_statutory_monthly_sum=total_statutory_monthly_sum,
            total_statutory_annual_sum=total_statutory_annual_sum,
            total_other_deductions_monthly_sum=total_other_deductions_monthly_sum,
            total_other_deductions_annual_sum=total_other_deductions_annual_sum,
            netpay=netpay,

            # Non-nullable fields
            user_id=UserRepository.authUserId()
        )

        db.session.add(salary_templates)
        db.session.commit()

        # for component_id in selected_components:
        #     component = SalaryComponentModel.query.get(component_id)
        #     if component:
        #         new_template.salary_components.append(component)

        # db.session.commit()
        # return True, "Salary template created successfully."

        return salary_templates

    @classmethod
    def fetchAll(self):
        return SalaryTemplatesModel.query.filter_by(user_id=UserRepository().authUserId()).order_by(SalaryTemplatesModel.timestamp.desc()).all()
    
    @classmethod
    def getSalaryTemplates(self, id):
        return SalaryTemplatesModel.query.filter(SalaryTemplatesModel.user_id==UserRepository().authUserId(), SalaryTemplatesModel.id == id).first()
    
    @classmethod
    def getSalaryTemplatesByKeys(self, kwargs):
        return SalaryTemplatesModel.query.filter_by(**kwargs).all()

    @classmethod
    def updateSalaryTemplates(self, id, **kwargs):
        salary_templates = SalaryTemplatesModel.query.filter_by(id=id).first()
        if salary_templates:
            for key, value in kwargs.items():
                setattr(salary_templates, key, value)
            db.session.commit()
            return salary_templates
        else:
            return None

    @classmethod
    def deleteSalaryTemplates(self, id):
        SalaryTemplatesModel.query.filter(SalaryTemplatesModel.id == id).delete()
        db.session.commit()
        return
        