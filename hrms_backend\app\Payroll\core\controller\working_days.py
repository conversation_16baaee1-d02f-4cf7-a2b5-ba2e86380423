from flask import url_for
from flask.views import MethodView
from flask_smorest import Blueprint, abort
from core.middleware import roles_required
from schemas import WorkingDaySchema
from sqlalchemy.orm.exc import NoResultFound
from sqlalchemy.exc import SQLAlchemyError, IntegrityError

import core.utils.response_message as RESPONSEMESSAGE
from core.services.working_days import WorkingDaysService

blueprint = Blueprint("Working Day", __name__, description="Operations for Working Days")
    
@blueprint.route("/working_days/<id>")
class WorkingDaysList(MethodView):
    @roles_required(['admin'])
    @blueprint.response(200, WorkingDaySchema)
    def get(self, id):
        service = WorkingDaysService()
        working_day = service.getWorkingDays(id)
        if not working_day:
            abort(401, message="Working day does not exist")
        return working_day    
    
    @roles_required(['admin'])
    def delete(self, id):
        service = WorkingDaysService()
        working_day = service.getWorkingDays(id)
        if not working_day:
            abort(404, message="Working Day does not exist")
        service.deleteWorkingDays(id)
        return {"message" : "Working day deleted"}
    
    @roles_required(['admin'])
    @blueprint.arguments(WorkingDaySchema)
    @blueprint.response(201, WorkingDaySchema)
    def put(self, id, data):
        service = WorkingDaysService()
        working_day = service.getWorkingDays(id)
        if not working_day:
            abort(404, message="Working Day does not exist")
        try :
            new_working_day = service.updateWorkingDays(id, data)
            return new_working_day
        except SQLAlchemyError:
                abort(500, message="Error while updating Working Day")
    
@blueprint.route("/working_days")
class WorkingDay(MethodView):
    @roles_required(['admin'])
    @blueprint.arguments(WorkingDaySchema)
    @blueprint.response(200, WorkingDaySchema)
    def post(self, data):
        try:
            service = WorkingDaysService()
            working_day = service.getWorkingDaysByKey({"id": data['id']})
            if not working_day:
                new_working_day = service.createWorkingDays(data)
            else:
                abort(400, message="Working Day already exist")
        except IntegrityError:
            abort(500, message="Error while creating Working Day")
        except SQLAlchemyError as e:
            print(e)
            abort(500, message="Error while creating Working Day")
        return new_working_day