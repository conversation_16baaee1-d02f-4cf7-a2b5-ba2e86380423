"""
Timesheet Pydantic schemas for request/response validation
"""

from pydantic import BaseModel, Field, validator
from typing import Optional, List
from datetime import datetime, date, time
from uuid import UUID
from enum import Enum

class TimesheetStatus(str, Enum):
    DRAFT = "draft"
    SUBMITTED = "submitted"
    APPROVED = "approved"
    REJECTED = "rejected"

class TimesheetEntryType(str, Enum):
    REGULAR = "regular"
    OVERTIME = "overtime"
    BREAK = "break"
    MEETING = "meeting"

# Base schemas
class TimesheetEntryBase(BaseModel):
    project_id: Optional[UUID] = None
    task_id: Optional[UUID] = None
    description: str = Field(..., min_length=1, max_length=500)
    start_time: datetime
    end_time: datetime
    entry_type: TimesheetEntryType = TimesheetEntryType.REGULAR
    billable: bool = True

class TimesheetBase(BaseModel):
    week_ending: date
    total_hours: float = Field(..., ge=0, le=168)  # Max 168 hours per week
    status: TimesheetStatus = TimesheetStatus.DRAFT
    notes: Optional[str] = Field(None, max_length=1000)

# Create schemas
class TimesheetEntryCreate(TimesheetEntryBase):
    pass

class TimesheetCreate(TimesheetBase):
    entries: List[TimesheetEntryCreate] = []

# Update schemas
class TimesheetEntryUpdate(BaseModel):
    project_id: Optional[UUID] = None
    task_id: Optional[UUID] = None
    description: Optional[str] = Field(None, min_length=1, max_length=500)
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    entry_type: Optional[TimesheetEntryType] = None
    billable: Optional[bool] = None

class TimesheetUpdate(BaseModel):
    week_ending: Optional[date] = None
    total_hours: Optional[float] = Field(None, ge=0, le=168)
    status: Optional[TimesheetStatus] = None
    notes: Optional[str] = Field(None, max_length=1000)

# Response schemas
class TimesheetEntryResponse(TimesheetEntryBase):
    id: UUID
    timesheet_id: UUID
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True

class TimesheetResponse(TimesheetBase):
    id: UUID
    employee_id: UUID
    entries: List[TimesheetEntryResponse] = []
    submitted_at: Optional[datetime] = None
    approved_at: Optional[datetime] = None
    approved_by: Optional[UUID] = None
    rejected_at: Optional[datetime] = None
    rejected_by: Optional[UUID] = None
    rejection_reason: Optional[str] = None
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True

class TimesheetListResponse(BaseModel):
    timesheets: List[TimesheetResponse]
    total: int
    page: int
    per_page: int
    pages: int

# Approval schemas
class TimesheetApprovalRequest(BaseModel):
    timesheet_ids: List[UUID]
    notes: Optional[str] = Field(None, max_length=500)

class TimesheetRejectionRequest(BaseModel):
    timesheet_ids: List[UUID]
    reason: str = Field(..., min_length=1, max_length=500)

# Summary schemas
class TimesheetSummary(BaseModel):
    total_timesheets: int
    pending_approval: int
    approved: int
    rejected: int
    total_hours: float
    billable_hours: float

class TimesheetReport(BaseModel):
    employee_id: UUID
    employee_name: str
    department: str
    week_ending: date
    total_hours: float
    billable_hours: float
    overtime_hours: float
    status: TimesheetStatus
