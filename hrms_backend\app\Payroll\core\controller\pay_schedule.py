from venv import logger
from flask import request
from flask.views import MethodView
from core.services.employee import EmployeeService
from core.services.payroll_history import PayrollHistoryService
from flask_smorest import Blueprint, abort
from core.middleware import roles_required
from core.databases.database import db
from core.services.loan import LoanService
from schemas import CreatePaySchedleSchema, PaySchedule, PayrollHistorySchema, EmployeeSchema
from sqlalchemy.exc import SQLAlchemyError, IntegrityError
import core.utils.response_message as RESPONSEMESSAGE
from core.services.pay_schedules import PaySchedulesService
from core.utils.responseBuilder import ResponseBuilder
from sqlalchemy import extract
from core.utils.activity_logger import ActivityLogger
from core.utils.approval_triggers import ApprovalFlowHandler
from core.services.component_processor import ComponentProcessor
from core.services.remarks import RemarkService
from core.utils.helper import safe_float

blueprint = Blueprint("Pay_Schedule", __name__, description="Operations for Pay_Schedule")
    
@blueprint.route("/pay_schedule/<id>")
class Payschedule(MethodView):
    @roles_required(['admin'])
    @blueprint.response(200, PaySchedule)
    def get(self, id):
        service = PaySchedulesService()
        pay_schedule = service.getPaySchedule(id)
        if not pay_schedule:
            abort(401, message="Pay Schedule does not exist")
        pay_schedule_details = PaySchedule().dump(pay_schedule)
        return ResponseBuilder(data=pay_schedule_details, status_code=200).build()   
    
    @roles_required(['admin'])
    def delete(self, id):
        service = PaySchedulesService()
        pay_schedule = service.getPaySchedule(id)
        if not pay_schedule:
            abort(404, message="Pay Schedule does not exist")
        service.deletePaySchedules(id)
        return {"message" : "Pay Schedule deleted"}
    
    @roles_required(['admin'])
    @blueprint.arguments(PaySchedule)
    @blueprint.response(201, PaySchedule)
    def put(self, data, id):
        service = PaySchedulesService()
        pay_schedule = service.getPaySchedule(id)
        if not pay_schedule:
            abort(404, message="Pay Schedule does not exist")
        try :
            new_pay_schedule = service.updatePaySchedules(id, data)
            ActivityLogger.log_admin_activity(f"You updated this pay schedule: {new_pay_schedule.name}")
            return new_pay_schedule
        except SQLAlchemyError:
                abort(500, message="Error while updating bank")
    
@blueprint.route("/pay_schedule")
class PayscheduleList(MethodView):
    @roles_required(['admin'])
    @blueprint.response(200, PaySchedule)
    def get(self):
        page = request.args.get('page', 1, type=int)
        limit = request.args.get('limit', 10, type=int)
        pay_schedule_service = PaySchedulesService()
        pay_schedule_list, total_schedule = pay_schedule_service.getAllPaySchedule(page, limit)

        schedule_schema = PaySchedule(many=True)
        pay_schedule_list = schedule_schema.dump(pay_schedule_list)
        total_pages = (total_schedule + limit - 1) // limit  # Calculate total pages

          # Only metadata goes in `meta`
        meta = {
            "total": total_schedule,
            "current_page": page,
            "per_page": limit,
            "total_pages": total_pages
        }

        return ResponseBuilder(
            data=pay_schedule_list,
            status_code=200,
            meta=meta
        ).build()

        # return ResponseBuilder(data=pay_schedule_list, status_code=200, total=total_schedule).build()
    
        
    @roles_required(['admin'])
    @blueprint.arguments(CreatePaySchedleSchema)
    @blueprint.response(200, PaySchedule)
    def post(self, data):
        service = PaySchedulesService()
        employee_service = EmployeeService()
        payroll_service = PayrollHistoryService()

        if service.hasUnapprovedPaySchedule():
            abort(400, message="An unapproved pay schedule already exists.")

        try:
            template_id = data.pop("template_id", None)
            employee_ids = data.pop("employee_ids", None)
            organisation_id = data.pop("organisation_id", None)
            employment_type = data.pop("employment_type", None)
            clear_all = data.get("clear_all", "No").strip().lower()

            # Validate and normalize month and year
            if clear_all == "yes":
                payroll_service.clear_unprocessed_payroll_history()

            if service.getPaySchedulesByKey({"name": data.get("name")}):
                abort(400, message="A pay schedule with this name already exists.")

            # Prepare pay schedule data
            pay_schedule_data = {
                key: data[key] for key in data if key in {
                    "name", "pay_date", "clear_all", "payment_based", 
                    "number_of_employees", "user_id"
                }
            }
            pay_schedule_data.update({
                "salary_template_id": template_id,
                "organisation_id": organisation_id,
                "employment_type": employment_type
            })

            # Create new pay schedule
            new_schedule = service.createPaySchedules(pay_schedule_data)
            ActivityLogger.log_admin_activity(f"Created pay schedule: {new_schedule.name}")

            # print("new_schedule ", new_schedule)
            
            #@TODO if approval settings is turned on then start approval flow
            ApprovalFlowHandler(
                name=new_schedule.name,
                user_id=new_schedule.user_id,
                pay_schedule_id=new_schedule.id,
                db_session=db.session
            )

            # Retrieve employees
            employees = get_filtered_employees(employee_service, employee_ids, organisation_id, employment_type, template_id)
            if not employees:
                abort(404, message="No employees found for the given criteria")

            build_payroll_records(employees, new_schedule)
            logger.info(f"Payroll records created for schedule {new_schedule.id}")

        except IntegrityError:
            db.session.rollback()
            abort(500, message="Database integrity error while creating pay schedule.")
        except SQLAlchemyError as e:
            db.session.rollback()
            logger.error(f"SQLAlchemy error: {e}")
            abort(500, message="Unexpected database error while creating pay schedule.")

        return new_schedule
        
@blueprint.route("/pay_schedule_cycle/<int:pay_schedule_id>")
class PayrollHistoryBySchedule(MethodView):
    @roles_required(['admin','employee'])
    @blueprint.response(200, PayrollHistorySchema(many=True))
    def get(self, pay_schedule_id):
        page = request.args.get('page', 1, type=int)
        limit = request.args.get('limit', 10000, type=int)

        payroll_service = PayrollHistoryService()
        remark_service = RemarkService()

        payroll_records, total = payroll_service.get_payroll_histories_by_schedule(pay_schedule_id, page, limit)

        if not payroll_records:
            return ResponseBuilder(data=[], status_code=200, total=0).build()

        payroll_schema = PayrollHistorySchema(many=True)
        serialized_data = payroll_schema.dump(payroll_records)

        formatted_history = []

        for item in serialized_data:
            employee = item.get("employee", {})
            prorated_gross = item.get("prorated_gross")
            prorated_net = item.get("prorated_net")
            prorated_status = item.get("is_prorated")
            prorated_monthly_tax = item.get("prorated_monthly_tax")
            prorated_annual_tax = item.get("prorated_annual_tax")
            processed_status = item.get("is_processed")
            processed_time = item.get("is_processed_created")
            arrears = item.get("salary_arrears")
            overtime = item.get("overtime_amount")

            # Compute component response
            component_processor = ComponentProcessor(
                employee.get("id"),
                employee.get("gross_pay"),
                prorated_gross,
                prorated_status,
                salary_benefit=employee.get("employee_benefits", []),
                salary_component=employee.get("employee_components", [])
            )

            res = component_processor.generate_salary_response()

            # Merge all required fields into response
            res.update({
                "employee": employee,
                "created_at": item.get("created_at"),
                "updated_at": item.get("updated_at"),
                "payroll_id": item.get("id"),
                "prorated_gross": prorated_gross,
                "prorated_status": prorated_status,
                "prorated_net": float(prorated_net or 0) + float(arrears or 0) + float(overtime or 0),
                "prorated_monthly_tax": prorated_monthly_tax,
                "prorated_annual_tax": prorated_annual_tax,
                "is_processed": processed_status,
                "salary_arrears": arrears,
                "overtime_amount": overtime,
                "processed_time": processed_time,
                "currency": employee.get("currency")
            })

            # Attach remark (if any)
            payroll_id = item.get("id")
            remark = None
            if payroll_id:
                remark_obj = remark_service.getRemarksByPayrollHistoryId(payroll_id)
                remark = remark_obj[0].remark if remark_obj else None

            res["remark"] = remark

            formatted_history.append(res)

        return ResponseBuilder(
            data=formatted_history,
            status_code=200,
            total=total,
            meta={
                "current_page": page,
                "per_page": limit,
                "total_pages": (total + limit - 1) // limit
            }
        ).build()

@blueprint.route("/pay_schedule_sync/<int:schedule_id>")
class SyncEmployeesToSchedule(MethodView):
    @roles_required(['admin'])
    @blueprint.response(200, PayrollHistorySchema(many=True))
    def get(self, schedule_id):
        try:        
            pay_schedule = PaySchedulesService().getPaySchedule(schedule_id)
            if not pay_schedule:
                return {"message": "Pay schedule not found."}, 404
                        
            employees = get_pay_schedule_option(
                payment_based=getattr(pay_schedule, "payment_based", None),
                org_id=getattr(pay_schedule, "organisation_id", None),
                emp_type=getattr(pay_schedule, "employment_type", None),
                template_id=getattr(pay_schedule, "template_id", None)
            )

            if not employees:
                abort(404, message="No employees found for the given criteria")
            
            payroll_data = build_payroll_records(employees, pay_schedule)
            PayrollHistoryService().createOrUpdatePayrollHistory(payroll_data)
            return ResponseBuilder(
                status_code=200,
                message='Payroll history synchronized successfully'
            ).build()

        except Exception as e:
            db.session.rollback()
            print(f"Error syncing payroll history: {e}")
            return {"message": "An error occurred during synchronization."}, 500
        

def get_filtered_employees(service, ids, org_id, emp_type, template_id):
    if ids:
        return service.getEmployeesByIds(ids)
    if org_id:
        return service.getEmployeesByOrganisation(org_id)
    if emp_type:
        return service.getEmployeesByType(emp_type)
    if template_id:
        return service.getEmployeesByTemplate(template_id)
    abort(400, message="No valid employee filter provided.")

def build_payroll_records(employees, schedule):
    employee_schema = EmployeeSchema(many=True)
    employees_data = employee_schema.dump(employees)

    payroll_service = PayrollHistoryService()
    loan_service = LoanService()

    for emp in employees_data:
        try:
            employee_id = emp["id"]
            base_netpay = float(emp.get("netpay", 0.0))

            # Step 1: Create initial payroll record
            payroll_data = {
                "employee_id": employee_id,
                "pay_schedle_id": schedule.id,
                "payschedle_name": schedule.name,
                "gross_pay": float(emp.get("gross_pay", 0.0)),
                "monthly_tax": float(emp.get("monthly_tax", 0.0)),
                "annual_tax": float(emp.get("annual_tax", 0.0)),
                "total_taxable_monthly_sum": float(emp.get("total_taxable_monthly_sum", 0.0)),
                "total_taxable_annual_sum": float(emp.get("total_taxable_annual_sum", 0.0)),
                "total_non_taxable_monthly_sum": float(emp.get("total_non_taxable_monthly_sum", 0.0)),
                "total_non_taxable_annual_sum": float(emp.get("total_non_taxable_annual_sum", 0.0)),
                "total_statutory_monthly_sum": float(emp.get("total_statutory_monthly_sum", 0.0)),
                "total_statutory_annual_sum": float(emp.get("total_statutory_annual_sum", 0.0)),
                "total_other_deductions_monthly_sum": float(emp.get("total_other_deductions_monthly_sum", 0.0)),
                "total_other_deductions_annual_sum": float(emp.get("total_other_deductions_annual_sum", 0.0)),
                "netpay": base_netpay,
                "monthly_loan_repayment": 0.0,  # to be updated later
                "pension": 0.0,
                "nhf": 0.0,
                "cost_to_company": float(emp.get("gross_pay", 0.0))
            }

            # Step 2: Save the payroll record
            payroll_record = payroll_service.create_payroll_history(payroll_data)

            # Step 3: Apply loan deductions to the saved payroll record
            updated_payroll = loan_service.apply_loan_deductions_to_employee_payroll(
                employee_id=employee_id,
                payroll_data={"id": payroll_record.id, "netpay": base_netpay}
            )

            # Step 4: Update the payroll record with the new deduction info
            payroll_service.update_payroll_history(
                payroll_record.id,
                netpay=updated_payroll.get("netpay", base_netpay),
                monthly_loan_repayment=updated_payroll.get("monthly_loan_repayment", 0.0)
            )

        except Exception as e:
            print(f"Failed to process employee {emp.get('id')}: {str(e)}")
            continue


def get_pay_schedule_option(payment_based, org_id=None, emp_type=None, template_id=None):
    payment_based = payment_based.strip().lower()
    service = EmployeeService()

    if payment_based == "organisation" and org_id:
        return service.getEmployeesByOrganisation(org_id)
    elif payment_based == "employment type" and emp_type:
        return service.getEmployeesByType(emp_type)
    elif payment_based == "template" and template_id:
        return service.getEmployeesByTemplate(template_id)
    else:
        return service.getEmployeesByIds('ALL')
