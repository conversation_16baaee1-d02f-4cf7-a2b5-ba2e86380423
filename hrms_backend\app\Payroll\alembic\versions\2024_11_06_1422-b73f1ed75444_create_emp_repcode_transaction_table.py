"""Create emp_repCode_Transaction  table

Revision ID: b73f1ed75444
Revises: 7283cbbb5efc
Create Date: 2024-11-06 14:22:10.926322

"""
from typing import Sequence, Union
from sqlalchemy import Column, Integer, String, Float
from sqlalchemy import func, DateTime, ForeignKey

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'b73f1ed75444'
down_revision: Union[str, None] = '7283cbbb5efc'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None



def upgrade() -> None:
    op.create_table(
        'emp_repCode_paystack_transaction',
        Column('id', Integer, primary_key=True, autoincrement=True),
        Column('emp_name', String(length=45), nullable=True),
        Column('bank_name', String(length=45), nullable=True),
        Column('bank_account', String(length=45), nullable=True),
        Column('bank_sort_code', String(length=45), nullable=True),
        Column('emp_id', String(length=45), nullable=True),
        Column('emp_rep_code', String(length=45), nullable=True),
        Column('emp_ref_code', String(length=45), nullable=True),
        Column('emp_gross', String(length=45), nullable=True),
        Column('currency', String(length=45), nullable=True),
        Column('status', String(length=45), nullable=True),
        Column('transaction_id', String(length=250), nullable=True),
        Column('transaction_month', String(length=250), nullable=True),
        Column('transaction_year', String(length=250), nullable=True),
        Column('transaction_time', String(length=250), nullable=True),
        Column('message', String(length=250), nullable=True),
        Column('user_id', Integer, ForeignKey('users.id'), nullable=True),
        Column('timestamp', DateTime, nullable=False, default=sa.func.now()),
    )


def downgrade() -> None:
    op.drop_table('emp_repCode_paystack_transaction')
