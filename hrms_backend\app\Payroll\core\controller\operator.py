from flask import url_for, jsonify
from flask.views import <PERSON><PERSON>ie<PERSON>
from flask_smorest import Blueprint, abort
from core.middleware import roles_required
from schemas import OperatorSchema, EmployeeSchema
from sqlalchemy.orm.exc import NoResultFound
from sqlalchemy.exc import SQLAlchemyError, IntegrityError
import core.utils.response_message as RESPONSEMESSAGE
from core.services.user_operator import OperatorService
from core.services.employee import EmployeeService
from core.utils.responseBuilder import ResponseBuilder
from core.services.approvals import ApprovalsService

blueprint = Blueprint("Operator", __name__, description="Operations for operators")
    
@blueprint.route("/operator/<id>")
class Operator(MethodView):
    @roles_required(['admin'])
    def delete(self, id):
        operator_service = OperatorService()
        operator = EmployeeService().getEmployeeById(id)
        if not operator:
            abort(404, message="Operator does not exist")

        delete_operator = operator_service.deleteOperator(id)
        if delete_operator:
            ApprovalsService().findAndDelete(id)

        return {"message" : "Operator deleted"}
    
    
    
@blueprint.route("/operator")
class OperatorList(MethodView):
    @roles_required(['admin'])
    @blueprint.response(200, EmployeeSchema)
    def get(self):
        operator_service = OperatorService()
        operator_list, total_operators = operator_service.fetchAll()
        employee_schema = EmployeeSchema(many=True)
        operator_list = employee_schema.dump(operator_list)
        return ResponseBuilder(data=operator_list, status_code=200, total=total_operators).build()
    
    @roles_required(['admin'])
    @blueprint.arguments(OperatorSchema)
    @blueprint.response(200, EmployeeSchema)
    def post(self, data):
        employee = data["employee_id"]

        if "employee_id" in data:
            del data["employee_id"]

        try:
            operator_service = OperatorService()
            operator = operator_service.getOperatorsByKey({"id": employee, "role": data['role']})
            if operator:
                abort(400, message="Employee already set as an operator")
            
            operator_service.createOperator(employee, data['role'])
            return {"message" : "Operator assigned successfully"}
        
        except IntegrityError:
            abort(500, message="Error while creating a bank")
        except SQLAlchemyError as e:
            print(e)
            abort(500, message="Error while creating a bank")
        