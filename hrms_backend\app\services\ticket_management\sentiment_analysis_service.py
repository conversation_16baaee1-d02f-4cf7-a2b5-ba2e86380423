import re
import logging
from typing import Dict, List, Optional, Tuple, Any
from datetime import datetime
from sqlalchemy.orm import Session
from fastapi import HTTPException, status

from ...db.models.ticket import Ticket, TicketPriority, TicketStatus
from ...db.models.employee import Employee
from ...core.security import CurrentUser

logger = logging.getLogger(__name__)


class SentimentAnalysisService:
    """Advanced sentiment analysis and urgency detection for tickets"""

    def __init__(self):
        # Sentiment lexicons
        self.positive_words = [
            "thank", "thanks", "appreciate", "grateful", "pleased", "happy",
            "excellent", "great", "wonderful", "amazing", "fantastic", "perfect",
            "helpful", "quick", "fast", "efficient", "professional", "friendly",
            "satisfied", "good", "nice", "awesome", "brilliant", "outstanding"
        ]

        self.negative_words = [
            "angry", "frustrated", "upset", "disappointed", "terrible", "awful",
            "horrible", "worst", "hate", "annoying", "ridiculous", "unacceptable",
            "disgusted", "furious", "outraged", "fed up", "sick", "tired",
            "broken", "failed", "useless", "pathetic", "disaster", "nightmare",
            "incompetent", "unprofessional", "rude", "slow", "delayed"
        ]

        self.urgency_indicators = [
            "urgent", "emergency", "asap", "immediately", "critical", "help",
            "stuck", "blocked", "can't work", "deadline", "time sensitive",
            "production", "down", "outage", "not working", "broken", "failed",
            "customer waiting", "business critical", "revenue impact", "escalate",
            "manager", "supervisor", "complaint", "legal", "compliance"
        ]

        self.escalation_triggers = [
            "manager", "supervisor", "escalate", "complaint", "legal", "lawsuit",
            "attorney", "discrimination", "harassment", "violation", "breach",
            "unethical", "inappropriate", "misconduct", "abuse", "threat",
            "safety", "security", "data breach", "privacy", "confidential"
        ]

        # Intensity modifiers
        self.intensifiers = [
            "very", "extremely", "really", "absolutely", "completely", "totally",
            "incredibly", "tremendously", "exceptionally", "remarkably", "highly",
            "severely", "seriously", "desperately", "urgently", "critically"
        ]

        self.diminishers = [
            "slightly", "somewhat", "a bit", "a little", "kind of", "sort of",
            "rather", "fairly", "quite", "pretty", "relatively", "moderately"
        ]

        # Emotion categories
        self.emotion_keywords = {
            "anger": ["angry", "furious", "mad", "rage", "outraged", "livid", "irate"],
            "frustration": ["frustrated", "annoyed", "irritated", "fed up", "sick of"],
            "anxiety": ["worried", "concerned", "anxious", "nervous", "stressed", "panic"],
            "sadness": ["sad", "disappointed", "upset", "depressed", "down"],
            "fear": ["scared", "afraid", "terrified", "worried", "concerned"],
            "joy": ["happy", "pleased", "delighted", "thrilled", "excited"],
            "satisfaction": ["satisfied", "content", "pleased", "happy", "grateful"]
        }

    async def analyze_sentiment(
        self,
        text: str,
        ticket_type: Optional[str] = None,
        priority: Optional[str] = None
    ) -> Dict[str, Any]:
        """Comprehensive sentiment analysis of ticket content"""
        try:
            text_lower = text.lower()
            
            # Basic sentiment scoring
            sentiment_score = await self._calculate_sentiment_score(text_lower)
            
            # Urgency detection
            urgency_analysis = await self._detect_urgency(text_lower)
            
            # Emotion detection
            emotions = await self._detect_emotions(text_lower)
            
            # Escalation risk assessment
            escalation_risk = await self._assess_escalation_risk(text_lower)
            
            # Priority recommendation
            recommended_priority = await self._recommend_priority(
                sentiment_score, urgency_analysis, escalation_risk, priority
            )
            
            # Generate insights and recommendations
            insights = await self._generate_insights(
                sentiment_score, urgency_analysis, emotions, escalation_risk
            )

            return {
                "sentiment": {
                    "polarity": sentiment_score["polarity"],
                    "score": sentiment_score["score"],
                    "confidence": sentiment_score["confidence"],
                    "intensity": sentiment_score["intensity"]
                },
                "urgency": urgency_analysis,
                "emotions": emotions,
                "escalation_risk": escalation_risk,
                "recommended_priority": recommended_priority,
                "insights": insights,
                "processed_at": datetime.utcnow().isoformat()
            }

        except Exception as e:
            logger.error(f"Error in sentiment analysis: {e}")
            return {
                "sentiment": {"polarity": "neutral", "score": 0.0, "confidence": 0.0},
                "urgency": {"level": "medium", "score": 0.5},
                "emotions": {},
                "escalation_risk": {"level": "low", "score": 0.0},
                "recommended_priority": "medium",
                "insights": [],
                "error": str(e)
            }

    async def _calculate_sentiment_score(self, text: str) -> Dict[str, Any]:
        """Calculate sentiment polarity and intensity"""
        try:
            words = re.findall(r'\b\w+\b', text)
            
            positive_count = 0
            negative_count = 0
            intensity_multiplier = 1.0
            
            for i, word in enumerate(words):
                # Check for intensifiers/diminishers
                if i > 0:
                    prev_word = words[i-1]
                    if prev_word in self.intensifiers:
                        intensity_multiplier = 1.5
                    elif prev_word in self.diminishers:
                        intensity_multiplier = 0.7
                    else:
                        intensity_multiplier = 1.0
                
                # Count sentiment words
                if word in self.positive_words:
                    positive_count += intensity_multiplier
                elif word in self.negative_words:
                    negative_count += intensity_multiplier
            
            # Calculate polarity score (-1 to 1)
            total_sentiment = positive_count + negative_count
            if total_sentiment == 0:
                polarity_score = 0.0
                polarity = "neutral"
            else:
                polarity_score = (positive_count - negative_count) / total_sentiment
                if polarity_score > 0.1:
                    polarity = "positive"
                elif polarity_score < -0.1:
                    polarity = "negative"
                else:
                    polarity = "neutral"
            
            # Calculate confidence based on number of sentiment words
            confidence = min(1.0, total_sentiment / 10)
            
            # Calculate intensity
            intensity = min(2.0, max(positive_count, negative_count) / 5)
            
            return {
                "polarity": polarity,
                "score": round(polarity_score, 3),
                "confidence": round(confidence, 3),
                "intensity": round(intensity, 3),
                "positive_count": positive_count,
                "negative_count": negative_count
            }

        except Exception as e:
            logger.error(f"Error calculating sentiment score: {e}")
            return {"polarity": "neutral", "score": 0.0, "confidence": 0.0, "intensity": 0.0}

    async def _detect_urgency(self, text: str) -> Dict[str, Any]:
        """Detect urgency level and indicators"""
        try:
            urgency_score = 0.0
            detected_indicators = []
            
            for indicator in self.urgency_indicators:
                if indicator in text:
                    urgency_score += len(indicator) / 100  # Weight by word length
                    detected_indicators.append(indicator)
            
            # Check for time-related urgency
            time_patterns = [
                r'\b(today|now|immediately|asap|urgent)\b',
                r'\b(deadline|due|expires?|ends?)\b',
                r'\b(minutes?|hours?|days?)\s+(left|remaining|ago)\b',
                r'\b(can\'t|cannot)\s+(work|access|login|connect)\b'
            ]
            
            for pattern in time_patterns:
                if re.search(pattern, text):
                    urgency_score += 0.3
                    detected_indicators.append("time_sensitive")
            
            # Normalize urgency score
            urgency_score = min(1.0, urgency_score)
            
            # Determine urgency level
            if urgency_score >= 0.8:
                level = "critical"
            elif urgency_score >= 0.6:
                level = "high"
            elif urgency_score >= 0.4:
                level = "medium"
            else:
                level = "low"
            
            return {
                "level": level,
                "score": round(urgency_score, 3),
                "indicators": list(set(detected_indicators)),
                "time_sensitive": any("time" in ind for ind in detected_indicators)
            }

        except Exception as e:
            logger.error(f"Error detecting urgency: {e}")
            return {"level": "medium", "score": 0.5, "indicators": []}

    async def _detect_emotions(self, text: str) -> Dict[str, float]:
        """Detect specific emotions in the text"""
        try:
            emotion_scores = {}
            
            for emotion, keywords in self.emotion_keywords.items():
                score = 0.0
                for keyword in keywords:
                    if keyword in text:
                        score += 1.0
                
                # Normalize by number of keywords
                emotion_scores[emotion] = round(score / len(keywords), 3)
            
            # Return only emotions with significant scores
            return {emotion: score for emotion, score in emotion_scores.items() if score > 0.1}

        except Exception as e:
            logger.error(f"Error detecting emotions: {e}")
            return {}

    async def _assess_escalation_risk(self, text: str) -> Dict[str, Any]:
        """Assess risk of escalation based on content"""
        try:
            risk_score = 0.0
            risk_factors = []
            
            # Check for escalation trigger words
            for trigger in self.escalation_triggers:
                if trigger in text:
                    risk_score += 0.2
                    risk_factors.append(trigger)
            
            # Check for multiple negative words (indicates frustration)
            negative_count = sum(1 for word in self.negative_words if word in text)
            if negative_count >= 3:
                risk_score += 0.3
                risk_factors.append("high_negative_sentiment")
            
            # Check for caps (indicates shouting/anger)
            caps_ratio = sum(1 for c in text if c.isupper()) / len(text) if text else 0
            if caps_ratio > 0.3:
                risk_score += 0.2
                risk_factors.append("excessive_caps")
            
            # Check for repeated punctuation (indicates emphasis/frustration)
            if re.search(r'[!?]{2,}', text):
                risk_score += 0.1
                risk_factors.append("excessive_punctuation")
            
            # Normalize risk score
            risk_score = min(1.0, risk_score)
            
            # Determine risk level
            if risk_score >= 0.7:
                level = "high"
            elif risk_score >= 0.4:
                level = "medium"
            else:
                level = "low"
            
            return {
                "level": level,
                "score": round(risk_score, 3),
                "factors": risk_factors,
                "requires_immediate_attention": risk_score >= 0.7
            }

        except Exception as e:
            logger.error(f"Error assessing escalation risk: {e}")
            return {"level": "low", "score": 0.0, "factors": []}

    async def _recommend_priority(
        self,
        sentiment: Dict[str, Any],
        urgency: Dict[str, Any],
        escalation_risk: Dict[str, Any],
        current_priority: Optional[str] = None
    ) -> str:
        """Recommend ticket priority based on analysis"""
        try:
            # Start with medium priority
            priority_score = 0.5
            
            # Adjust based on urgency
            urgency_weights = {
                "critical": 1.0,
                "high": 0.8,
                "medium": 0.5,
                "low": 0.2
            }
            priority_score = max(priority_score, urgency_weights.get(urgency["level"], 0.5))
            
            # Adjust based on negative sentiment
            if sentiment["polarity"] == "negative":
                priority_score += abs(sentiment["score"]) * 0.3
            
            # Adjust based on escalation risk
            if escalation_risk["level"] == "high":
                priority_score += 0.4
            elif escalation_risk["level"] == "medium":
                priority_score += 0.2
            
            # Determine final priority
            if priority_score >= 0.9:
                return "critical"
            elif priority_score >= 0.7:
                return "urgent"
            elif priority_score >= 0.5:
                return "high"
            elif priority_score >= 0.3:
                return "medium"
            else:
                return "low"

        except Exception as e:
            logger.error(f"Error recommending priority: {e}")
            return "medium"

    async def _generate_insights(
        self,
        sentiment: Dict[str, Any],
        urgency: Dict[str, Any],
        emotions: Dict[str, float],
        escalation_risk: Dict[str, Any]
    ) -> List[str]:
        """Generate actionable insights based on analysis"""
        try:
            insights = []
            
            # Sentiment insights
            if sentiment["polarity"] == "negative" and sentiment["confidence"] > 0.5:
                insights.append("Customer shows negative sentiment - consider priority handling")
            
            if sentiment["intensity"] > 1.5:
                insights.append("High emotional intensity detected - may require empathetic response")
            
            # Urgency insights
            if urgency["level"] in ["critical", "high"]:
                insights.append(f"High urgency detected - {urgency['level']} priority recommended")
            
            if urgency.get("time_sensitive"):
                insights.append("Time-sensitive request - immediate attention required")
            
            # Emotion insights
            if "anger" in emotions and emotions["anger"] > 0.3:
                insights.append("Customer anger detected - consider escalation or senior agent assignment")
            
            if "frustration" in emotions and emotions["frustration"] > 0.3:
                insights.append("Customer frustration detected - provide clear timeline and updates")
            
            if "anxiety" in emotions and emotions["anxiety"] > 0.3:
                insights.append("Customer anxiety detected - provide reassurance and frequent updates")
            
            # Escalation insights
            if escalation_risk["level"] == "high":
                insights.append("High escalation risk - consider manager notification")
            
            if escalation_risk.get("requires_immediate_attention"):
                insights.append("Immediate attention required - potential escalation scenario")
            
            # Specific risk factor insights
            if "legal" in escalation_risk.get("factors", []):
                insights.append("Legal concerns mentioned - notify compliance team")
            
            if "harassment" in escalation_risk.get("factors", []):
                insights.append("Harassment mentioned - escalate to HR immediately")
            
            return insights

        except Exception as e:
            logger.error(f"Error generating insights: {e}")
            return []

    async def monitor_sentiment_trends(
        self,
        db: Session,
        organization_id: str,
        days: int = 30
    ) -> Dict[str, Any]:
        """Monitor sentiment trends over time"""
        try:
            # This would analyze sentiment trends from stored ticket data
            # For now, return mock data structure
            
            return {
                "period_days": days,
                "organization_id": organization_id,
                "trends": {
                    "overall_sentiment": {
                        "current": 0.2,  # Slightly positive
                        "previous": 0.1,
                        "change": 0.1
                    },
                    "urgency_distribution": {
                        "critical": 5,
                        "high": 15,
                        "medium": 60,
                        "low": 20
                    },
                    "escalation_rate": {
                        "current": 8.5,  # Percentage
                        "previous": 12.0,
                        "change": -3.5
                    }
                },
                "recommendations": [
                    "Sentiment trending positive - maintain current service levels",
                    "Escalation rate decreasing - good progress on customer satisfaction"
                ]
            }

        except Exception as e:
            logger.error(f"Error monitoring sentiment trends: {e}")
            return {"error": str(e)}

    async def suggest_response_tone(
        self,
        sentiment_analysis: Dict[str, Any]
    ) -> Dict[str, str]:
        """Suggest appropriate response tone based on sentiment analysis"""
        try:
            sentiment = sentiment_analysis.get("sentiment", {})
            urgency = sentiment_analysis.get("urgency", {})
            escalation_risk = sentiment_analysis.get("escalation_risk", {})
            emotions = sentiment_analysis.get("emotions", {})
            
            # Determine response tone
            if escalation_risk.get("level") == "high":
                tone = "formal_empathetic"
                approach = "Acknowledge concerns immediately, escalate to senior staff"
            elif sentiment.get("polarity") == "negative" and sentiment.get("intensity", 0) > 1.0:
                tone = "empathetic_solution_focused"
                approach = "Show understanding, focus on resolution steps"
            elif urgency.get("level") in ["critical", "high"]:
                tone = "urgent_professional"
                approach = "Immediate acknowledgment, clear action plan"
            elif "anxiety" in emotions and emotions["anxiety"] > 0.3:
                tone = "reassuring_detailed"
                approach = "Provide detailed explanation and timeline"
            elif sentiment.get("polarity") == "positive":
                tone = "appreciative_efficient"
                approach = "Thank customer, maintain positive momentum"
            else:
                tone = "professional_helpful"
                approach = "Standard professional response"
            
            return {
                "recommended_tone": tone,
                "approach": approach,
                "key_elements": self._get_tone_elements(tone)
            }

        except Exception as e:
            logger.error(f"Error suggesting response tone: {e}")
            return {
                "recommended_tone": "professional_helpful",
                "approach": "Standard professional response",
                "key_elements": ["Clear communication", "Solution-focused", "Professional courtesy"]
            }

    def _get_tone_elements(self, tone: str) -> List[str]:
        """Get key elements for different response tones"""
        tone_elements = {
            "formal_empathetic": [
                "Formal acknowledgment",
                "Express understanding",
                "Escalation notification",
                "Clear next steps"
            ],
            "empathetic_solution_focused": [
                "Acknowledge frustration",
                "Show understanding",
                "Focus on solutions",
                "Provide timeline"
            ],
            "urgent_professional": [
                "Immediate acknowledgment",
                "Priority handling",
                "Clear action plan",
                "Regular updates"
            ],
            "reassuring_detailed": [
                "Provide reassurance",
                "Detailed explanation",
                "Step-by-step process",
                "Frequent communication"
            ],
            "appreciative_efficient": [
                "Thank customer",
                "Maintain positivity",
                "Efficient resolution",
                "Professional courtesy"
            ],
            "professional_helpful": [
                "Clear communication",
                "Solution-focused",
                "Professional courtesy",
                "Helpful attitude"
            ]
        }
        
        return tone_elements.get(tone, ["Professional", "Helpful", "Clear"])
