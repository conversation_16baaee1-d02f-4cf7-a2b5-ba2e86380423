"""
Test Simple Onboarding API
Test the simplified onboarding functionality
"""

import requests
import json

def test_simple_onboard():
    """Test the simple onboarding API endpoint"""
    
    base_url = "http://localhost:8085/api"
    
    print("🧪 Testing Simple Onboarding API")
    print("=" * 50)
    
    # Step 1: Test endpoint availability
    print("1. Testing endpoint availability...")
    try:
        test_response = requests.get(f"{base_url}/simple-onboarding/test")
        if test_response.status_code == 200:
            print("✅ Simple onboarding API is available")
            print(f"Response: {test_response.json()}")
        else:
            print(f"❌ Test endpoint failed: {test_response.status_code}")
    except Exception as e:
        print(f"❌ Test endpoint error: {e}")
    
    # Step 2: Login to get token
    print("\n2. Logging in...")
    try:
        login_response = requests.post(f"{base_url}/auth/login", json={
            "email": "<EMAIL>",
            "password": "password123"
        })
        
        if login_response.status_code != 200:
            print(f"❌ Login failed: {login_response.status_code}")
            print(login_response.text)
            return
        
        token = login_response.json()["access_token"]
        headers = {"Authorization": f"Bearer {token}"}
        print("✅ Login successful")
    except Exception as e:
        print(f"❌ Login error: {e}")
        return
    
    # Step 3: Check employee count
    print("\n3. Checking current employee count...")
    try:
        count_response = requests.get(f"{base_url}/simple-onboarding/employees/count", headers=headers)
        if count_response.status_code == 200:
            count_data = count_response.json()
            print(f"✅ Current employee count: {count_data.get('count', 'unknown')}")
        else:
            print(f"❌ Count check failed: {count_response.status_code}")
    except Exception as e:
        print(f"❌ Count check error: {e}")
    
    # Step 4: Test simple onboarding
    print("\n4. Testing simple onboarding...")
    
    onboard_data = {
        "employee_name": "John Doe Test",
        "employee_email": "<EMAIL>",
        "start_date": "2024-01-15"
    }
    
    try:
        response = requests.post(
            f"{base_url}/simple-onboarding/simple-onboard",
            json=onboard_data,
            headers=headers
        )
        
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ Simple onboarding successful!")
            print(f"Employee ID: {result['employee_id']}")
            print(f"Employee Name: {result['employee_name']}")
            print(f"Email: {result['email']}")
            print(f"Temporary Password: {result['temporary_password']}")
            print(f"Message: {result['message']}")
        else:
            print(f"❌ Simple onboarding failed: {response.status_code}")
            print("Response:", response.text)
    except Exception as e:
        print(f"❌ Simple onboarding error: {e}")
    
    # Step 5: Check employee count after creation
    print("\n5. Checking employee count after creation...")
    try:
        count_response = requests.get(f"{base_url}/simple-onboarding/employees/count", headers=headers)
        if count_response.status_code == 200:
            count_data = count_response.json()
            print(f"✅ New employee count: {count_data.get('count', 'unknown')}")
        else:
            print(f"❌ Count check failed: {count_response.status_code}")
    except Exception as e:
        print(f"❌ Count check error: {e}")
    
    print("\n📊 Test Summary")
    print("=" * 50)
    print("Simple onboarding API test completed!")

if __name__ == "__main__":
    test_simple_onboard()
