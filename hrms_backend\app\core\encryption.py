import base64
import os
from cryptography.fernet import <PERSON><PERSON><PERSON>
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
from cryptography.hazmat.primitives.ciphers import Cipher, algorithms, modes
from cryptography.hazmat.backends import default_backend
from typing import Union, Optional
import logging
import secrets

from .config import settings

logger = logging.getLogger(__name__)


class EncryptionManager:
    """AES-256 encryption manager for sensitive data"""

    def __init__(self):
        self._encryption_key = self._get_or_generate_key()
        self._fernet = Fernet(self._encryption_key)

    def _get_or_generate_key(self) -> bytes:
        """Get encryption key from environment or generate a new one"""
        try:
            # Try to get key from environment
            if hasattr(settings, 'AES_ENCRYPTION_KEY') and settings.AES_ENCRYPTION_KEY:
                # Decode base64 encoded key
                return base64.urlsafe_b64decode(settings.AES_ENCRYPTION_KEY.encode())
            
            # Generate a new key if not found
            logger.warning("No encryption key found in environment. Generating a new one.")
            key = Fernet.generate_key()
            
            # Log the base64 encoded key for storage in environment
            encoded_key = base64.urlsafe_b64encode(key).decode()
            logger.warning(f"Generated encryption key (store in AES_ENCRYPTION_KEY): {encoded_key}")
            
            return key
            
        except Exception as e:
            logger.error(f"Error getting encryption key: {e}")
            # Fallback to generating a new key
            return Fernet.generate_key()

    def encrypt_string(self, plaintext: str) -> str:
        """Encrypt a string and return base64 encoded result"""
        try:
            if not plaintext:
                return ""
            
            encrypted_data = self._fernet.encrypt(plaintext.encode('utf-8'))
            return base64.urlsafe_b64encode(encrypted_data).decode('utf-8')
            
        except Exception as e:
            logger.error(f"Error encrypting string: {e}")
            raise

    def decrypt_string(self, encrypted_data: str) -> str:
        """Decrypt a base64 encoded encrypted string"""
        try:
            if not encrypted_data:
                return ""
            
            encrypted_bytes = base64.urlsafe_b64decode(encrypted_data.encode('utf-8'))
            decrypted_data = self._fernet.decrypt(encrypted_bytes)
            return decrypted_data.decode('utf-8')
            
        except Exception as e:
            logger.error(f"Error decrypting string: {e}")
            raise

    def encrypt_bytes(self, data: bytes) -> bytes:
        """Encrypt bytes data"""
        try:
            return self._fernet.encrypt(data)
        except Exception as e:
            logger.error(f"Error encrypting bytes: {e}")
            raise

    def decrypt_bytes(self, encrypted_data: bytes) -> bytes:
        """Decrypt bytes data"""
        try:
            return self._fernet.decrypt(encrypted_data)
        except Exception as e:
            logger.error(f"Error decrypting bytes: {e}")
            raise

    def encrypt_file(self, file_path: str, output_path: Optional[str] = None) -> str:
        """Encrypt a file and return the output path"""
        try:
            if not output_path:
                output_path = f"{file_path}.encrypted"
            
            with open(file_path, 'rb') as file:
                file_data = file.read()
            
            encrypted_data = self.encrypt_bytes(file_data)
            
            with open(output_path, 'wb') as encrypted_file:
                encrypted_file.write(encrypted_data)
            
            return output_path
            
        except Exception as e:
            logger.error(f"Error encrypting file: {e}")
            raise

    def decrypt_file(self, encrypted_file_path: str, output_path: Optional[str] = None) -> str:
        """Decrypt a file and return the output path"""
        try:
            if not output_path:
                output_path = encrypted_file_path.replace('.encrypted', '')
            
            with open(encrypted_file_path, 'rb') as encrypted_file:
                encrypted_data = encrypted_file.read()
            
            decrypted_data = self.decrypt_bytes(encrypted_data)
            
            with open(output_path, 'wb') as file:
                file.write(decrypted_data)
            
            return output_path
            
        except Exception as e:
            logger.error(f"Error decrypting file: {e}")
            raise

    @staticmethod
    def generate_salt() -> str:
        """Generate a random salt for password hashing"""
        return base64.urlsafe_b64encode(secrets.token_bytes(32)).decode('utf-8')

    @staticmethod
    def derive_key_from_password(password: str, salt: str) -> bytes:
        """Derive encryption key from password and salt"""
        try:
            salt_bytes = base64.urlsafe_b64decode(salt.encode('utf-8'))
            kdf = PBKDF2HMAC(
                algorithm=hashes.SHA256(),
                length=32,
                salt=salt_bytes,
                iterations=100000,
                backend=default_backend()
            )
            return base64.urlsafe_b64encode(kdf.derive(password.encode('utf-8')))
        except Exception as e:
            logger.error(f"Error deriving key from password: {e}")
            raise


class FieldEncryption:
    """Field-level encryption for database columns"""

    def __init__(self, encryption_manager: EncryptionManager):
        self.encryption_manager = encryption_manager

    def encrypt_pii_field(self, value: Union[str, None]) -> Union[str, None]:
        """Encrypt PII (Personally Identifiable Information) field"""
        if value is None or value == "":
            return value
        
        try:
            return self.encryption_manager.encrypt_string(str(value))
        except Exception as e:
            logger.error(f"Error encrypting PII field: {e}")
            return value  # Return original value if encryption fails

    def decrypt_pii_field(self, encrypted_value: Union[str, None]) -> Union[str, None]:
        """Decrypt PII field"""
        if encrypted_value is None or encrypted_value == "":
            return encrypted_value
        
        try:
            return self.encryption_manager.decrypt_string(encrypted_value)
        except Exception as e:
            logger.error(f"Error decrypting PII field: {e}")
            return encrypted_value  # Return encrypted value if decryption fails

    def encrypt_sensitive_data(self, data: dict, sensitive_fields: list) -> dict:
        """Encrypt sensitive fields in a dictionary"""
        encrypted_data = data.copy()
        
        for field in sensitive_fields:
            if field in encrypted_data and encrypted_data[field] is not None:
                encrypted_data[field] = self.encrypt_pii_field(encrypted_data[field])
        
        return encrypted_data

    def decrypt_sensitive_data(self, encrypted_data: dict, sensitive_fields: list) -> dict:
        """Decrypt sensitive fields in a dictionary"""
        decrypted_data = encrypted_data.copy()
        
        for field in sensitive_fields:
            if field in decrypted_data and decrypted_data[field] is not None:
                decrypted_data[field] = self.decrypt_pii_field(decrypted_data[field])
        
        return decrypted_data


class TokenEncryption:
    """Encryption for tokens and temporary data"""

    def __init__(self, encryption_manager: EncryptionManager):
        self.encryption_manager = encryption_manager

    def create_encrypted_token(self, data: dict, expiry_hours: int = 24) -> str:
        """Create an encrypted token with expiry"""
        import json
        from datetime import datetime, timedelta
        
        try:
            # Add expiry timestamp
            token_data = data.copy()
            token_data['expires_at'] = (datetime.utcnow() + timedelta(hours=expiry_hours)).isoformat()
            
            # Convert to JSON and encrypt
            json_data = json.dumps(token_data)
            encrypted_token = self.encryption_manager.encrypt_string(json_data)
            
            return encrypted_token
            
        except Exception as e:
            logger.error(f"Error creating encrypted token: {e}")
            raise

    def decrypt_token(self, encrypted_token: str) -> Optional[dict]:
        """Decrypt and validate token"""
        import json
        from datetime import datetime
        
        try:
            # Decrypt token
            json_data = self.encryption_manager.decrypt_string(encrypted_token)
            token_data = json.loads(json_data)
            
            # Check expiry
            if 'expires_at' in token_data:
                expires_at = datetime.fromisoformat(token_data['expires_at'])
                if datetime.utcnow() > expires_at:
                    logger.warning("Token has expired")
                    return None
                
                # Remove expiry from returned data
                del token_data['expires_at']
            
            return token_data
            
        except Exception as e:
            logger.error(f"Error decrypting token: {e}")
            return None


# Global encryption manager instance
encryption_manager = EncryptionManager()
field_encryption = FieldEncryption(encryption_manager)
token_encryption = TokenEncryption(encryption_manager)


# Utility functions for easy access
def encrypt_pii(value: str) -> str:
    """Utility function to encrypt PII data"""
    return field_encryption.encrypt_pii_field(value)


def decrypt_pii(encrypted_value: str) -> str:
    """Utility function to decrypt PII data"""
    return field_encryption.decrypt_pii_field(encrypted_value)


def create_secure_token(data: dict, expiry_hours: int = 24) -> str:
    """Utility function to create secure encrypted token"""
    return token_encryption.create_encrypted_token(data, expiry_hours)


def verify_secure_token(token: str) -> Optional[dict]:
    """Utility function to verify and decrypt secure token"""
    return token_encryption.decrypt_token(token)


# PII field names that should be encrypted
PII_FIELDS = [
    'ssn', 'social_security_number', 'tax_id', 'passport_number',
    'driver_license', 'bank_account', 'credit_card', 'phone',
    'personal_email', 'address', 'emergency_contact_phone',
    'emergency_contact_email', 'medical_info', 'salary',
    'bank_details', 'routing_number', 'account_number'
]

# Sensitive fields that may contain PII
SENSITIVE_FIELDS = PII_FIELDS + [
    'notes', 'comments', 'feedback', 'personal_notes',
    'performance_notes', 'disciplinary_notes'
]
