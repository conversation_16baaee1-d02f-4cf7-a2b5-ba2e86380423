/**
 * Professional Leave Application Form Component
 * Features: Multi-step form, validation, file upload, rich UI
 */

import React, { useState, useEffect } from 'react';
import {
  Calendar,
  Clock,
  FileText,
  Upload,
  User,
  Phone,
  AlertCircle,
  CheckCircle,
  XCircle,
  X,
  ArrowLeft,
  ArrowRight
} from 'lucide-react';
import apiService from '../../services/api';

const LeaveApplicationForm = ({ onClose, onSubmit, employeeId }) => {
  const [currentStep, setCurrentStep] = useState(1);
  const [formData, setFormData] = useState({
    leave_policy_id: '',
    start_date: '',
    end_date: '',
    duration_type: 'FULL_DAY',
    reason: '',
    contact_number: '',
    emergency_contact: '',
    handover_notes: '',
    handover_to: '',
    attachment_urls: []
  });
  const [leavePolicies, setLeavePolicies] = useState([]);
  const [employees, setEmployees] = useState([]);
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState({});
  const [leaveBalance, setLeaveBalance] = useState(null);
  const [submitStatus, setSubmitStatus] = useState(null); // 'success', 'error', null

  useEffect(() => {
    loadInitialData();
  }, []);

  const loadInitialData = async () => {
    try {
      console.log('🔄 Loading initial data...');
      console.log('🔑 Auth token:', localStorage.getItem('hrm_auth_token') ? 'Present' : 'Missing');

      const [policiesResponse, employeesResponse] = await Promise.all([
        apiService.getLeavePolicies(),
        apiService.getEmployees()
      ]);

      console.log('📋 Raw policies response:', policiesResponse);
      console.log('👥 Raw employees response:', employeesResponse);

      const policies = Array.isArray(policiesResponse) ? policiesResponse : (policiesResponse.policies || []);
      const employees = Array.isArray(employeesResponse) ? employeesResponse : (employeesResponse.employees || []);

      console.log('✅ Processed policies:', policies.length, 'items');
      console.log('✅ Processed employees:', employees.length, 'items');

      setLeavePolicies(policies);
      setEmployees(employees);

      console.log('🎉 Initial data loaded successfully');
    } catch (error) {
      console.error('❌ Error loading initial data:', error);
      console.error('❌ Error details:', error.message);
    }
  };

  const loadLeaveBalance = async (policyId) => {
    try {
      const balance = await apiService.getMyLeaveBalance();
      const policyBalance = balance.find(b => b.leave_policy_id === policyId);
      setLeaveBalance(policyBalance);
    } catch (error) {
      console.error('Error loading leave balance:', error);
    }
  };

  const handleInputChange = (field, value) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: null }));
    }

    // Load balance when policy changes
    if (field === 'leave_policy_id' && value) {
      loadLeaveBalance(value);
    }
  };

  const validateStep = (step) => {
    const newErrors = {};

    switch (step) {
      case 1:
        if (!formData.leave_policy_id) newErrors.leave_policy_id = 'Please select a leave type';
        if (!formData.start_date) newErrors.start_date = 'Start date is required';
        if (!formData.end_date) newErrors.end_date = 'End date is required';
        if (formData.start_date && formData.end_date && new Date(formData.start_date) > new Date(formData.end_date)) {
          newErrors.end_date = 'End date must be after start date';
        }
        break;
      case 2:
        if (!formData.reason.trim()) newErrors.reason = 'Reason is required';
        if (formData.reason.length < 10) newErrors.reason = 'Please provide a detailed reason (minimum 10 characters)';
        break;
      case 3:
        // Optional validations for contact info
        break;
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const nextStep = () => {
    if (validateStep(currentStep)) {
      setCurrentStep(prev => Math.min(prev + 1, 3));
    }
  };

  const prevStep = () => {
    setCurrentStep(prev => Math.max(prev - 1, 1));
  };

  const handleSubmit = async () => {
    if (!validateStep(currentStep)) return;

    setLoading(true);
    setSubmitStatus(null);

    try {
      // Clean up the form data before submission
      const cleanedFormData = {
        ...formData,
        handover_to: formData.handover_to || null, // Convert empty string to null
        contact_number: formData.contact_number || null,
        emergency_contact: formData.emergency_contact || null,
        handover_notes: formData.handover_notes || null,
        attachment_urls: formData.attachment_urls || []
      };

      console.log('🚀 Form Component - Submitting leave application:', cleanedFormData);
      console.log('🚀 Form Component - Duration type:', cleanedFormData.duration_type);
      console.log('🚀 Form Component - Full form data:', JSON.stringify(cleanedFormData, null, 2));
      await onSubmit(cleanedFormData);
      console.log('✅ Leave application submitted successfully');
      setSubmitStatus('success');

      // Show success message for 2 seconds before closing
      setTimeout(() => {
        onClose();
      }, 2000);
    } catch (error) {
      console.error('❌ Error submitting leave application:', error);
      setSubmitStatus('error');
    } finally {
      setLoading(false);
    }
  };

  const calculateLeaveDays = () => {
    if (!formData.start_date || !formData.end_date) return 0;
    
    const start = new Date(formData.start_date);
    const end = new Date(formData.end_date);
    const diffTime = Math.abs(end - start);
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24)) + 1;
    
    return formData.duration_type === 'HALF_DAY_MORNING' || formData.duration_type === 'HALF_DAY_AFTERNOON'
      ? diffDays * 0.5
      : diffDays;
  };

  const selectedPolicy = leavePolicies.find(p => p.id === formData.leave_policy_id);

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-2xl shadow-2xl w-full max-w-4xl max-h-[90vh] flex flex-col">
        {/* Header */}
        <div className="agno-gradient text-white p-6">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-2xl font-bold">Apply for Leave (v2.0)</h2>
              <p className="text-blue-100 mt-1">Submit your leave request with all required details</p>
            </div>
            <button
              onClick={onClose}
              className="text-white hover:text-blue-200 transition-colors"
            >
              <X size={24} />
            </button>
          </div>
          
          {/* Progress Steps */}
          <div className="flex items-center mt-6 space-x-4">
            {[1, 2, 3].map((step) => (
              <div key={step} className="flex items-center">
                <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
                  step <= currentStep 
                    ? 'bg-white text-agno-primary' 
                    : 'bg-blue-400 text-white'
                }`}>
                  {step < currentStep ? <CheckCircle size={16} /> : step}
                </div>
                {step < 3 && (
                  <div className={`w-12 h-1 mx-2 ${
                    step < currentStep ? 'bg-white' : 'bg-blue-400'
                  }`} />
                )}
              </div>
            ))}
          </div>
        </div>

        {/* Status Message */}
        {submitStatus && (
          <div className={`mx-6 mt-4 p-4 rounded-lg ${
            submitStatus === 'success'
              ? 'bg-green-50 border border-green-200 text-green-800'
              : 'bg-red-50 border border-red-200 text-red-800'
          }`}>
            <div className="flex items-center">
              {submitStatus === 'success' ? (
                <CheckCircle className="w-5 h-5 mr-2" />
              ) : (
                <XCircle className="w-5 h-5 mr-2" />
              )}
              <span className="font-medium">
                {submitStatus === 'success'
                  ? 'Leave application submitted successfully! You will receive a confirmation email shortly.'
                  : 'Failed to submit leave application. Please try again or contact support.'}
              </span>
            </div>
          </div>
        )}

        {/* Form Content */}
        <div className="p-6 overflow-y-auto flex-1">
          {/* Step 1: Leave Details */}
          {currentStep === 1 && (
            <div className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Leave Type */}
                <div className="md:col-span-2">
                  <label className="block text-sm font-semibold text-gray-700 mb-2">
                    <FileText className="inline w-4 h-4 mr-2" />
                    Leave Type *
                  </label>
                  <select
                    value={formData.leave_policy_id}
                    onChange={(e) => handleInputChange('leave_policy_id', e.target.value)}
                    className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-agno-primary focus:border-transparent ${
                      errors.leave_policy_id ? 'border-red-500' : 'border-gray-300'
                    }`}
                  >
                    <option value="">Select leave type</option>
                    {leavePolicies.map((policy) => (
                      <option key={policy.id} value={policy.id}>
                        {policy.name} ({policy.leave_type})
                      </option>
                    ))}
                  </select>
                  {errors.leave_policy_id && (
                    <p className="text-red-500 text-sm mt-1 flex items-center">
                      <AlertCircle size={14} className="mr-1" />
                      {errors.leave_policy_id}
                    </p>
                  )}
                </div>

                {/* Leave Balance Display */}
                {leaveBalance && (
                  <div className="md:col-span-2 bg-blue-50 border border-blue-200 rounded-lg p-4">
                    <h4 className="font-semibold text-blue-800 mb-2">Available Balance</h4>
                    <div className="grid grid-cols-3 gap-4 text-sm">
                      <div>
                        <span className="text-gray-600">Total:</span>
                        <span className="font-medium ml-2">{leaveBalance.total_entitlement} days</span>
                      </div>
                      <div>
                        <span className="text-gray-600">Used:</span>
                        <span className="font-medium ml-2">{leaveBalance.used_balance} days</span>
                      </div>
                      <div>
                        <span className="text-gray-600">Available:</span>
                        <span className="font-medium ml-2 text-green-600">{leaveBalance.available_balance} days</span>
                      </div>
                    </div>
                  </div>
                )}

                {/* Start Date */}
                <div>
                  <label className="block text-sm font-semibold text-gray-700 mb-2">
                    <Calendar className="inline w-4 h-4 mr-2" />
                    Start Date *
                  </label>
                  <input
                    type="date"
                    value={formData.start_date}
                    onChange={(e) => handleInputChange('start_date', e.target.value)}
                    min={new Date().toISOString().split('T')[0]}
                    className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-agno-primary focus:border-transparent ${
                      errors.start_date ? 'border-red-500' : 'border-gray-300'
                    }`}
                  />
                  {errors.start_date && (
                    <p className="text-red-500 text-sm mt-1 flex items-center">
                      <AlertCircle size={14} className="mr-1" />
                      {errors.start_date}
                    </p>
                  )}
                </div>

                {/* End Date */}
                <div>
                  <label className="block text-sm font-semibold text-gray-700 mb-2">
                    <Calendar className="inline w-4 h-4 mr-2" />
                    End Date *
                  </label>
                  <input
                    type="date"
                    value={formData.end_date}
                    onChange={(e) => handleInputChange('end_date', e.target.value)}
                    min={formData.start_date || new Date().toISOString().split('T')[0]}
                    className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-agno-primary focus:border-transparent ${
                      errors.end_date ? 'border-red-500' : 'border-gray-300'
                    }`}
                  />
                  {errors.end_date && (
                    <p className="text-red-500 text-sm mt-1 flex items-center">
                      <AlertCircle size={14} className="mr-1" />
                      {errors.end_date}
                    </p>
                  )}
                </div>

                {/* Duration Type */}
                <div>
                  <label className="block text-sm font-semibold text-gray-700 mb-2">
                    <Clock className="inline w-4 h-4 mr-2" />
                    Duration Type
                  </label>
                  <select
                    value={formData.duration_type}
                    onChange={(e) => handleInputChange('duration_type', e.target.value)}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-agno-primary focus:border-transparent"
                  >
                    <option value="FULL_DAY">Full Day</option>
                    <option value="HALF_DAY_MORNING">Half Day (Morning)</option>
                    <option value="HALF_DAY_AFTERNOON">Half Day (Afternoon)</option>
                  </select>
                </div>

                {/* Total Days Calculation */}
                <div>
                  <label className="block text-sm font-semibold text-gray-700 mb-2">
                    Total Days
                  </label>
                  <div className="px-4 py-3 bg-gray-50 border border-gray-300 rounded-lg">
                    <span className="text-lg font-bold text-agno-primary">
                      {calculateLeaveDays()} days
                    </span>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Step 2: Reason and Documentation */}
          {currentStep === 2 && (
            <div className="space-y-6">
              {/* Reason */}
              <div>
                <label className="block text-sm font-semibold text-gray-700 mb-2">
                  <FileText className="inline w-4 h-4 mr-2" />
                  Reason for Leave *
                </label>
                <textarea
                  value={formData.reason}
                  onChange={(e) => handleInputChange('reason', e.target.value)}
                  placeholder="Please provide a detailed reason for your leave request..."
                  rows={4}
                  className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-agno-primary focus:border-transparent resize-none ${
                    errors.reason ? 'border-red-500' : 'border-gray-300'
                  }`}
                />
                <div className="flex justify-between items-center mt-1">
                  {errors.reason && (
                    <p className="text-red-500 text-sm flex items-center">
                      <AlertCircle size={14} className="mr-1" />
                      {errors.reason}
                    </p>
                  )}
                  <p className="text-sm text-gray-500 ml-auto">
                    {formData.reason.length}/1000 characters
                  </p>
                </div>
              </div>

              {/* File Upload */}
              <div>
                <label className="block text-sm font-semibold text-gray-700 mb-2">
                  <Upload className="inline w-4 h-4 mr-2" />
                  Supporting Documents (Optional)
                </label>
                <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-agno-primary transition-colors">
                  <Upload className="w-8 h-8 text-gray-400 mx-auto mb-2" />
                  <p className="text-sm text-gray-600 mb-2">
                    Drag and drop files here, or click to browse
                  </p>
                  <p className="text-xs text-gray-500">
                    Supported formats: PDF, JPG, PNG (Max 10MB)
                  </p>
                  <input
                    type="file"
                    multiple
                    accept=".pdf,.jpg,.jpeg,.png"
                    className="hidden"
                    onChange={(e) => {
                      // Handle file upload logic here
                      console.log('Files selected:', e.target.files);
                    }}
                  />
                </div>
              </div>

              {/* Leave Policy Information */}
              {selectedPolicy && (
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                  <h4 className="font-semibold text-blue-800 mb-2">Leave Policy Information</h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-3 text-sm">
                    <div>
                      <span className="text-gray-600">Minimum Notice:</span>
                      <span className="font-medium ml-2">{selectedPolicy.min_notice_days} days</span>
                    </div>
                    <div>
                      <span className="text-gray-600">Max Consecutive:</span>
                      <span className="font-medium ml-2">
                        {selectedPolicy.max_consecutive_days || 'No limit'} days
                      </span>
                    </div>
                    {selectedPolicy.requires_documentation && (
                      <div className="md:col-span-2">
                        <span className="text-amber-600 text-sm flex items-center">
                          <AlertCircle size={14} className="mr-1" />
                          Documentation required for this leave type
                        </span>
                      </div>
                    )}
                  </div>
                </div>
              )}
            </div>
          )}

          {/* Step 3: Contact and Handover */}
          {currentStep === 3 && (
            <div className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Contact Number */}
                <div>
                  <label className="block text-sm font-semibold text-gray-700 mb-2">
                    <Phone className="inline w-4 h-4 mr-2" />
                    Contact Number During Leave
                  </label>
                  <input
                    type="tel"
                    value={formData.contact_number}
                    onChange={(e) => handleInputChange('contact_number', e.target.value)}
                    placeholder="+****************"
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-agno-primary focus:border-transparent"
                  />
                </div>

                {/* Emergency Contact */}
                <div>
                  <label className="block text-sm font-semibold text-gray-700 mb-2">
                    <AlertCircle className="inline w-4 h-4 mr-2" />
                    Emergency Contact
                  </label>
                  <input
                    type="text"
                    value={formData.emergency_contact}
                    onChange={(e) => handleInputChange('emergency_contact', e.target.value)}
                    placeholder="Name and phone number"
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-agno-primary focus:border-transparent"
                  />
                </div>

                {/* Handover To */}
                <div>
                  <label className="block text-sm font-semibold text-gray-700 mb-2">
                    <User className="inline w-4 h-4 mr-2" />
                    Handover Responsibilities To
                  </label>
                  <select
                    value={formData.handover_to}
                    onChange={(e) => handleInputChange('handover_to', e.target.value)}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-agno-primary focus:border-transparent"
                  >
                    <option value="">Select colleague</option>
                    {employees.map((employee) => (
                      <option key={employee.id} value={employee.id}>
                        {employee.first_name} {employee.last_name} - {employee.department}
                      </option>
                    ))}
                  </select>
                </div>
              </div>

              {/* Handover Notes */}
              <div>
                <label className="block text-sm font-semibold text-gray-700 mb-2">
                  <FileText className="inline w-4 h-4 mr-2" />
                  Handover Notes
                </label>
                <textarea
                  value={formData.handover_notes}
                  onChange={(e) => handleInputChange('handover_notes', e.target.value)}
                  placeholder="Provide details about work handover, ongoing projects, important deadlines, etc."
                  rows={4}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-agno-primary focus:border-transparent resize-none"
                />
              </div>

              {/* Summary */}
              <div className="bg-gray-50 border border-gray-200 rounded-lg p-6">
                <h4 className="font-semibold text-gray-800 mb-4">Application Summary</h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="text-gray-600">Leave Type:</span>
                    <span className="font-medium ml-2">{selectedPolicy?.name}</span>
                  </div>
                  <div>
                    <span className="text-gray-600">Duration:</span>
                    <span className="font-medium ml-2">
                      {formData.start_date} to {formData.end_date}
                    </span>
                  </div>
                  <div>
                    <span className="text-gray-600">Total Days:</span>
                    <span className="font-medium ml-2">{calculateLeaveDays()} days</span>
                  </div>
                  <div>
                    <span className="text-gray-600">Duration Type:</span>
                    <span className="font-medium ml-2 capitalize">
                      {formData.duration_type.replace('_', ' ')}
                    </span>
                  </div>
                </div>

                {leaveBalance && (
                  <div className="mt-4 pt-4 border-t border-gray-300">
                    <div className="flex justify-between items-center">
                      <span className="text-gray-600">Available Balance:</span>
                      <span className="font-medium">{leaveBalance.available_balance} days</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-gray-600">After This Leave:</span>
                      <span className={`font-medium ${
                        (leaveBalance.available_balance - calculateLeaveDays()) >= 0
                          ? 'text-green-600'
                          : 'text-red-600'
                      }`}>
                        {leaveBalance.available_balance - calculateLeaveDays()} days
                      </span>
                    </div>
                  </div>
                )}
              </div>
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="border-t bg-gray-50 px-6 py-4 flex justify-between flex-shrink-0">
          <button
            onClick={prevStep}
            disabled={currentStep === 1}
            className="flex items-center px-4 py-2 text-gray-600 hover:text-gray-800 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <ArrowLeft size={16} className="mr-2" />
            Previous
          </button>
          
          <div className="flex space-x-3">
            <button
              onClick={onClose}
              className="px-6 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50"
            >
              Cancel
            </button>
            
            {currentStep < 3 ? (
              <button
                onClick={nextStep}
                className="flex items-center px-6 py-2 agno-bg-primary text-white rounded-lg hover:bg-blue-700"
              >
                Next
                <ArrowRight size={16} className="ml-2" />
              </button>
            ) : (
              <button
                onClick={handleSubmit}
                disabled={loading}
                className="flex items-center px-6 py-2 agno-bg-primary text-white rounded-lg hover:bg-blue-700 disabled:opacity-50"
              >
                {loading ? 'Submitting...' : 'Submit Application'}
              </button>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default LeaveApplicationForm;
