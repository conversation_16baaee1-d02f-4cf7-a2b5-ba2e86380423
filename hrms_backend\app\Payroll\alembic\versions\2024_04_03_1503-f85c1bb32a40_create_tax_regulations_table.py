"""create tax_regulations table

Revision ID: f85c1bb32a40
Revises: 70d37c1591e0
Create Date: 2024-04-03 15:03:49.879503

"""
from typing import Sequence, Union

from alembic import op
from sqlalchemy import func, TIMESTAMP
from sqlalchemy import Column, Integer, String, Float

# revision identifiers, used by Alembic.
revision: str = 'f85c1bb32a40'
down_revision: Union[str, None] = '70d37c1591e0'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    op.create_table(
        'tax_regulations',
        Column('id', Integer, primary_key=True),
        Column('country', String(100), nullable=False),
        Column('tax_rate', String(100), nullable=False),
        Column('tax_type', String(100), nullable=False),
        Column("currency", Float, nullable=False),
        <PERSON>umn("timestamp", TIMESTAMP, server_default=func.now()),
       
    )

def downgrade() -> None:
    op.drop_table("tax_regulations")