from sqlalchemy.orm import Session, joinedload
from sqlalchemy import and_, or_, func
from typing import Optional, List, Dict, Any
from uuid import UUID
from datetime import datetime, timedelta
from fastapi import HTTPException, status
import logging
import json

from ...db.models.ticket import (
    Ticket, TicketWorkflow, TicketActivity, TicketNotification,
    TicketStatus, TicketPriority, TicketType, WorkflowStatus
)
from ...db.models.employee import Employee
from ...core.security import CurrentUser
from ...core.audit_logger import AuditLogger

logger = logging.getLogger(__name__)


class TicketWorkflowService:
    """Service for managing ticket workflows and automation"""

    async def create_workflow(
        self,
        db: Session,
        workflow_data: Dict[str, Any],
        current_user: CurrentUser
    ) -> TicketWorkflow:
        """Create a new ticket workflow"""
        try:
            workflow = TicketWorkflow(
                name=workflow_data["name"],
                description=workflow_data.get("description"),
                trigger_conditions=workflow_data["trigger_conditions"],
                workflow_steps=workflow_data["workflow_steps"],
                is_active=workflow_data.get("is_active", True),
                priority=workflow_data.get("priority", 0),
                organization_id=current_user.organization_id,
                created_by=current_user.user_id
            )

            db.add(workflow)
            db.commit()
            db.refresh(workflow)

            # Log workflow creation
            await AuditLogger.log_action(
                db, current_user.user_id, "workflow_created",
                f"Created workflow: {workflow.name}",
                {"workflow_id": str(workflow.id)}
            )

            logger.info(f"Workflow {workflow.name} created by {current_user.user_id}")
            return workflow

        except Exception as e:
            db.rollback()
            logger.error(f"Error creating workflow: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Error creating workflow"
            )

    async def get_workflows(
        self,
        db: Session,
        current_user: CurrentUser,
        is_active: Optional[bool] = None,
        skip: int = 0,
        limit: int = 20
    ) -> List[TicketWorkflow]:
        """Get workflows for organization"""
        try:
            query = db.query(TicketWorkflow).filter(
                TicketWorkflow.organization_id == current_user.organization_id
            )

            if is_active is not None:
                query = query.filter(TicketWorkflow.is_active == is_active)

            workflows = query.order_by(TicketWorkflow.priority.desc()).offset(skip).limit(limit).all()
            return workflows

        except Exception as e:
            logger.error(f"Error getting workflows: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Error retrieving workflows"
            )

    async def update_workflow(
        self,
        db: Session,
        workflow_id: UUID,
        workflow_data: Dict[str, Any],
        current_user: CurrentUser
    ) -> Optional[TicketWorkflow]:
        """Update a workflow"""
        try:
            workflow = db.query(TicketWorkflow).filter(
                TicketWorkflow.id == workflow_id,
                TicketWorkflow.organization_id == current_user.organization_id
            ).first()

            if not workflow:
                return None

            # Update fields
            for field, value in workflow_data.items():
                if hasattr(workflow, field):
                    setattr(workflow, field, value)

            workflow.updated_by = current_user.user_id
            workflow.updated_at = datetime.utcnow()

            db.commit()
            db.refresh(workflow)

            # Log workflow update
            await AuditLogger.log_action(
                db, current_user.user_id, "workflow_updated",
                f"Updated workflow: {workflow.name}",
                {"workflow_id": str(workflow.id)}
            )

            logger.info(f"Workflow {workflow.name} updated by {current_user.user_id}")
            return workflow

        except Exception as e:
            db.rollback()
            logger.error(f"Error updating workflow {workflow_id}: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Error updating workflow"
            )

    async def delete_workflow(
        self,
        db: Session,
        workflow_id: UUID,
        current_user: CurrentUser
    ) -> bool:
        """Delete a workflow"""
        try:
            workflow = db.query(TicketWorkflow).filter(
                TicketWorkflow.id == workflow_id,
                TicketWorkflow.organization_id == current_user.organization_id
            ).first()

            if not workflow:
                return False

            # Check if workflow is in use
            tickets_using_workflow = db.query(Ticket).filter(
                Ticket.workflow_id == workflow_id,
                Ticket.workflow_status.in_([WorkflowStatus.ACTIVE, WorkflowStatus.PAUSED])
            ).count()

            if tickets_using_workflow > 0:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Cannot delete workflow that is currently in use"
                )

            db.delete(workflow)
            db.commit()

            # Log workflow deletion
            await AuditLogger.log_action(
                db, current_user.user_id, "workflow_deleted",
                f"Deleted workflow: {workflow.name}",
                {"workflow_id": str(workflow.id)}
            )

            logger.info(f"Workflow {workflow.name} deleted by {current_user.user_id}")
            return True

        except HTTPException:
            raise
        except Exception as e:
            db.rollback()
            logger.error(f"Error deleting workflow {workflow_id}: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Error deleting workflow"
            )

    async def trigger_workflow(
        self,
        db: Session,
        ticket: Ticket,
        trigger_event: str,
        current_user: CurrentUser
    ) -> Optional[TicketWorkflow]:
        """Trigger workflow based on ticket conditions"""
        try:
            # Get active workflows for organization
            workflows = db.query(TicketWorkflow).filter(
                TicketWorkflow.organization_id == ticket.organization_id,
                TicketWorkflow.is_active == True
            ).order_by(TicketWorkflow.priority.desc()).all()

            for workflow in workflows:
                if await self._evaluate_trigger_conditions(ticket, workflow.trigger_conditions, trigger_event):
                    # Apply workflow to ticket
                    ticket.workflow_id = workflow.id
                    ticket.workflow_status = WorkflowStatus.ACTIVE
                    ticket.workflow_step = 0

                    db.commit()

                    # Log workflow trigger
                    await self._create_activity(
                        db, ticket.id, "workflow_triggered",
                        f"Workflow '{workflow.name}' triggered by {trigger_event}",
                        current_user
                    )

                    logger.info(f"Workflow {workflow.name} triggered for ticket {ticket.ticket_number}")
                    return workflow

            return None

        except Exception as e:
            logger.error(f"Error triggering workflow for ticket {ticket.id}: {e}")
            return None

    async def process_workflow_step(
        self,
        db: Session,
        ticket_id: UUID,
        step_data: Dict[str, Any],
        current_user: CurrentUser
    ) -> Dict[str, Any]:
        """Process the next workflow step for a ticket"""
        try:
            ticket = db.query(Ticket).options(
                joinedload(Ticket.workflow)
            ).filter(Ticket.id == ticket_id).first()

            if not ticket or not ticket.workflow:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Ticket or workflow not found"
                )

            if ticket.workflow_status != WorkflowStatus.ACTIVE:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Workflow is not active"
                )

            workflow_steps = ticket.workflow.workflow_steps
            current_step = ticket.workflow_step or 0

            if current_step >= len(workflow_steps):
                # Workflow completed
                ticket.workflow_status = WorkflowStatus.COMPLETED
                db.commit()
                return {"success": True, "completed": True}

            # Get current step configuration
            step_config = workflow_steps[current_step]
            
            # Process step based on type
            step_result = await self._process_step_type(
                db, ticket, step_config, step_data, current_user
            )

            if step_result.get("success", False):
                # Move to next step
                ticket.workflow_step = current_step + 1
                
                # Check if workflow is complete
                if ticket.workflow_step >= len(workflow_steps):
                    ticket.workflow_status = WorkflowStatus.COMPLETED
                    
                    # Auto-close ticket if configured
                    if step_config.get("auto_close_on_completion", False):
                        ticket.status = TicketStatus.RESOLVED
                        ticket.resolved_at = datetime.utcnow()

                db.commit()

                # Log step completion
                await self._create_activity(
                    db, ticket.id, "workflow_step_completed",
                    f"Workflow step {current_step + 1} completed: {step_config.get('name', 'Unnamed step')}",
                    current_user
                )

            else:
                # Step failed
                ticket.workflow_status = WorkflowStatus.FAILED
                db.commit()

                # Log step failure
                await self._create_activity(
                    db, ticket.id, "workflow_step_failed",
                    f"Workflow step {current_step + 1} failed: {step_result.get('error', 'Unknown error')}",
                    current_user
                )

            return step_result

        except HTTPException:
            raise
        except Exception as e:
            db.rollback()
            logger.error(f"Error processing workflow step for ticket {ticket_id}: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Error processing workflow step"
            )

    async def _evaluate_trigger_conditions(
        self,
        ticket: Ticket,
        conditions: Dict[str, Any],
        trigger_event: str
    ) -> bool:
        """Evaluate if workflow trigger conditions are met"""
        try:
            # Check trigger event
            if "events" in conditions and trigger_event not in conditions["events"]:
                return False

            # Check ticket type
            if "ticket_types" in conditions and ticket.ticket_type not in conditions["ticket_types"]:
                return False

            # Check priority
            if "priorities" in conditions and ticket.priority not in conditions["priorities"]:
                return False

            # Check status
            if "statuses" in conditions and ticket.status not in conditions["statuses"]:
                return False

            # Check category
            if "categories" in conditions and ticket.category not in conditions["categories"]:
                return False

            # Check department
            if "departments" in conditions and str(ticket.department_id) not in conditions["departments"]:
                return False

            return True

        except Exception as e:
            logger.error(f"Error evaluating trigger conditions: {e}")
            return False

    async def _process_step_type(
        self,
        db: Session,
        ticket: Ticket,
        step_config: Dict[str, Any],
        step_data: Dict[str, Any],
        current_user: CurrentUser
    ) -> Dict[str, Any]:
        """Process workflow step based on its type"""
        try:
            step_type = step_config.get("type")

            if step_type == "auto_assign":
                return await self._process_auto_assign_step(db, ticket, step_config, current_user)
            elif step_type == "status_change":
                return await self._process_status_change_step(db, ticket, step_config, current_user)
            elif step_type == "notification":
                return await self._process_notification_step(db, ticket, step_config, current_user)
            elif step_type == "approval":
                return await self._process_approval_step(db, ticket, step_config, step_data, current_user)
            elif step_type == "escalation":
                return await self._process_escalation_step(db, ticket, step_config, current_user)
            elif step_type == "delay":
                return await self._process_delay_step(db, ticket, step_config, current_user)
            else:
                return {"success": False, "error": f"Unknown step type: {step_type}"}

        except Exception as e:
            logger.error(f"Error processing step type {step_config.get('type')}: {e}")
            return {"success": False, "error": str(e)}

    async def _process_auto_assign_step(
        self,
        db: Session,
        ticket: Ticket,
        step_config: Dict[str, Any],
        current_user: CurrentUser
    ) -> Dict[str, Any]:
        """Process auto-assignment workflow step"""
        try:
            assignment_rules = step_config.get("assignment_rules", {})
            
            # Get target assignee based on rules
            assignee_id = None
            
            if "specific_user" in assignment_rules:
                assignee_id = assignment_rules["specific_user"]
            elif "team" in assignment_rules:
                # Find available team member
                team_members = db.query(Employee).filter(
                    Employee.organization_id == ticket.organization_id,
                    Employee.department == assignment_rules["team"],
                    Employee.is_active == True
                ).all()
                
                if team_members:
                    # Simple round-robin assignment
                    assignee_id = team_members[0].id
            
            if assignee_id:
                ticket.assigned_to = assignee_id
                ticket.assigned_team = assignment_rules.get("team")
                db.commit()
                
                return {"success": True, "assigned_to": str(assignee_id)}
            else:
                return {"success": False, "error": "No suitable assignee found"}

        except Exception as e:
            logger.error(f"Error in auto-assign step: {e}")
            return {"success": False, "error": str(e)}

    async def _process_status_change_step(
        self,
        db: Session,
        ticket: Ticket,
        step_config: Dict[str, Any],
        current_user: CurrentUser
    ) -> Dict[str, Any]:
        """Process status change workflow step"""
        try:
            new_status = step_config.get("new_status")
            if new_status:
                old_status = ticket.status
                ticket.status = TicketStatus(new_status)
                
                if new_status == TicketStatus.RESOLVED:
                    ticket.resolved_at = datetime.utcnow()
                elif new_status == TicketStatus.CLOSED:
                    ticket.closed_at = datetime.utcnow()
                
                db.commit()
                
                return {"success": True, "old_status": old_status, "new_status": new_status}
            else:
                return {"success": False, "error": "No status specified"}

        except Exception as e:
            logger.error(f"Error in status change step: {e}")
            return {"success": False, "error": str(e)}

    async def _process_notification_step(
        self,
        db: Session,
        ticket: Ticket,
        step_config: Dict[str, Any],
        current_user: CurrentUser
    ) -> Dict[str, Any]:
        """Process notification workflow step"""
        try:
            notification_config = step_config.get("notification", {})
            recipients = notification_config.get("recipients", [])
            
            for recipient_type in recipients:
                recipient_id = None
                
                if recipient_type == "requester":
                    recipient_id = ticket.requester_id
                elif recipient_type == "assignee" and ticket.assigned_to:
                    recipient_id = ticket.assigned_to
                elif recipient_type.startswith("user:"):
                    recipient_id = recipient_type.split(":")[1]
                
                if recipient_id:
                    notification = TicketNotification(
                        ticket_id=ticket.id,
                        recipient_id=recipient_id,
                        notification_type=notification_config.get("type", "email"),
                        subject=notification_config.get("subject", f"Ticket {ticket.ticket_number} Update"),
                        message=notification_config.get("message", "Your ticket has been updated"),
                        created_at=datetime.utcnow()
                    )
                    db.add(notification)
            
            db.commit()
            return {"success": True, "notifications_sent": len(recipients)}

        except Exception as e:
            logger.error(f"Error in notification step: {e}")
            return {"success": False, "error": str(e)}

    async def _create_activity(
        self,
        db: Session,
        ticket_id: UUID,
        activity_type: str,
        description: str,
        current_user: CurrentUser
    ):
        """Create ticket activity log"""
        try:
            activity = TicketActivity(
                ticket_id=ticket_id,
                user_id=current_user.user_id,
                activity_type=activity_type,
                description=description,
                created_at=datetime.utcnow()
            )
            db.add(activity)
            db.commit()
        except Exception as e:
            logger.error(f"Error creating activity log: {e}")
