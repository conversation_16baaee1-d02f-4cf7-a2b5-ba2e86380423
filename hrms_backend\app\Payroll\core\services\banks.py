from core.repositories.banks import BanksRepository

class BanksService:
    def __init__(self) -> None:
        self.repository = BanksRepository()

    def createBanks(self, Kwargs):
        return self.repository.createBanks(**Kwargs)
    
    def fetchAll(self):
        banks = self.repository.fetchAll()
        total_banks = len(banks)
        return banks, total_banks

    def getBanks(self, id):
        return self.repository.getBanks(id)
    
    def updateBanks(self, id, Kwargs):
        return self.repository.updateBanks(id, **Kwargs)
    
    def getBanksByKey(self, Kwarg):
        return self.repository.getBanksByKeys(Kwarg)
    
    def deleteBanks(self, id):
        return self.repository.deleteBanks(id)