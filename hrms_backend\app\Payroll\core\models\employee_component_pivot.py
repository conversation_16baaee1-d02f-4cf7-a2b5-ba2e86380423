from core.databases.database import db
from core.models.basemodel import ModelBase
from datetime import datetime

class EmployeeComponentsPivotModel(ModelBase):
    __tablename__ = "employee_components_pivot"

    id = db.Column(db.Integer, primary_key=True)
    # employee_id = db.Column(db.<PERSON><PERSON><PERSON>, db.<PERSON>(ModelBase.dbSchema() + '.employees.id',ondelete="CASCADE"), nullable=False) 
    # salary_component_id = db.Column(db.<PERSON><PERSON><PERSON>, db.<PERSON>ey(ModelBase.dbSchema() + '.salary_components.id',ondelete="CASCADE"), nullable=False) 

    employee_id = db.Column(db.Integer, db.Foreign<PERSON>ey((ModelBase.dbSchema() + '.employees.id'),ondelete='CASCADE'), nullable=False)
    salary_component_id = db.Column(db.<PERSON><PERSON>, db.<PERSON>((ModelBase.dbSchema() + '.salary_components.id'),ondelete='CASCADE'), nullable=False)

    employee = db.relationship('EmployeeModel', back_populates='employee_components', single_parent=True)
    salary_component = db.relationship('SalaryComponentsModel', back_populates='employee_components')


    # employee = db.relationship('EmployeeModel', back_populates='employee_components', single_parent=True)
    # salary_component = db.relationship('SalaryComponentsModel', back_populates='employee_components')
