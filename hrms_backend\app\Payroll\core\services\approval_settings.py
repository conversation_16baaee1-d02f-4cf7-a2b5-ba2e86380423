from core.repositories.approval_settings import ApprovalSettingsRepository
from core.repositories.user import UserRepository
from flask_smorest import abort


class ApprovalSettingsService:
    def __init__(self) -> None:
        self.repository = ApprovalSettingsRepository()

    def createApprovalSettings(self, Kwargs):
        return self.repository.createApprovalSettings(**Kwargs)
    
    def createOrUpdateApprovalSettingss(self, Kwargs):
        existing = self.repository.getFirst()
        if existing:
            return self.repository.updateApprovalSettings(existing.id, **Kwargs)
        return self.repository.createApprovalSettings(**Kwargs)

    def fetchAll(self):
        approval_settings = self.repository.fetchAll()
        total_approval_settings = len(approval_settings)
        return approval_settings, total_approval_settings

    def getApprovalSettings(self, id):
        return self.repository.getApprovalSettings(id)
    
    def updateApprovalSettings(self, id, Kwargs):
        return self.repository.updateApprovalSettings(id, **Kwargs)
    
    def getApprovalSettingsByKey(self, Kwarg):
        return self.repository.getApprovalSettingsByKeys(Kwarg)
    
    def deleteApprovalSettings(self, id):
        return self.repository.deleteApprovalSettings(id)
    
    def getApprovalSettingByAuthenticatedUser(self):
        user_id = UserRepository().authUserId()
        return self.repository.getApprovalSettingsByUserId(user_id)
    
    def validate_payroll_approval_requirements(self, payroll_ids):
        user_id = UserRepository().authUserId()
        approval_settings = self.repository.get_user_approval_settings(user_id)

        if not approval_settings or not approval_settings.status:
            # print("✅ Approval setting off or not found — skipping check")
            return

        first_payroll = self.repository.get_first_payroll_history(payroll_ids)
        if not first_payroll:
            # print("🚫 Payroll record not found")
            abort(404, message="Payroll record not found")

        pay_schedule = self.repository.get_pay_schedule_by_id(first_payroll.pay_schedle_id)
        if not pay_schedule or not pay_schedule.is_approved:
            # print("🚫 Pay schedule not approved")
            abort(400, message="Kindly wait for approval from other parties...")

        # print("✅ Approval settings passed, proceeding with payroll.")
