import smtplib
from email.mime.text import MIMEText
from config import Config

def send_smtp_email(to, subject, body):
    # print(f"[SMTP Email] Sending email to: {to}")

    # Specify utf-8 encoding here 👇
    msg = MIMEText(body)
    msg["Subject"] = subject
    msg["From"] = Config.DEFAULT_FROM_EMAIL
    msg["To"] = to

    try:
        server = smtplib.SMTP(Config.MAIL_SERVER, Config.MAIL_PORT)
        if Config.MAIL_USE_TLS:
            server.starttls()
        server.login(Config.MAIL_USERNAME, Config.MAIL_PASSWORD)
        server.sendmail(Config.DEFAULT_FROM_EMAIL, to, msg.as_string())
        server.quit()

        # print(f"✅ Email sent successfully to {to}")
        return {"message": f"Email sent to {to}"}
    except Exception as e:
        # print(f"🚨 ERROR: Failed to send email to {to}: {str(e)}")
        return {"error": str(e)}
