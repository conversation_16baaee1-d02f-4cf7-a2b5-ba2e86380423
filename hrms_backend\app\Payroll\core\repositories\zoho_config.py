from core.models.zoho_config import ZohoConfig
from core.databases.database import db
from sqlalchemy import desc
import traceback
from core.repositories.user import UserRepository

class ZohoConfigRepository:
    def __init__(self) -> None:
        pass

    @classmethod
    def create_config(self, **kwargs):
        kwargs['user_id']=UserRepository.authUserId()
        integration = ZohoConfig(**kwargs)

        db.session.add(integration)
        db.session.commit()
        return integration

    @classmethod
    def get_config(self):
        user_id = UserRepository.authUserId()
        return ZohoConfig.query.filter(ZohoConfig.user_id == user_id).first()

    @classmethod
    def update_config(self, id, **kwargs):
        integration = ZohoConfig.query.filter_by(id=id).first()
        if integration:
            for key, value in kwargs.items():
                setattr(integration, key, value)
            db.session.commit()
            return integration
        else:
            return None

    @classmethod
    def delete_config(self):
        user_id = UserRepository.authUserId()
        ZohoConfig.query.filter(ZohoConfig.user_id == user_id).delete()
        db.session.commit()
        return
    
    def toggle_config_status(self, enabled: bool):
        config = self.get_config()
        if not config:
            return None
        
        config.is_enabled = enabled
        return self.update_config(config)