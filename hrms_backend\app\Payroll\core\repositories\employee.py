from core.models.employees import EmployeeModel
from core.models.employee_component_pivot import EmployeeComponentsPivotModel
from core.models.employee_benefit_pivot import EmployeeBenefitsPivotModel
from core.models.payroll_history import PayrollHistoryModel
from core.models.prorate_salary import ProrateSalaryModel
from core.models.remark import RemarkModel
from core.models.organisation import OrganisationModel
from core.databases.database import db
from core.repositories.user import UserRepository
from core.repositories.organisation import OrganisationRepository
from sqlalchemy import desc, func
from sqlalchemy.orm import load_only
from datetime import datetime
from sqlalchemy.orm.attributes import flag_modified
import traceback
class EmployeeRepository:
    @classmethod
    def createEmployee(self, first_name, last_name, password_hash, state, gross_pay,source_tag,gender=None,annual_leave_days=None,unpaid_leave_days=None,sick_leave_days=None,maternity_paternity_leave_days=None,casual_leave_days=None,compassionate_leave_days=None,total_working_days=None,total_present_days=None,total_absent_days=None,pension_no=None,pension_pfa=None,nhf_no=None,nhf_mortgage_bank=None,
                       monthly_tax=None, annual_tax=None, total_taxable_monthly_sum=None,
                       total_taxable_annual_sum=None, total_non_taxable_monthly_sum=None,
                       total_non_taxable_annual_sum=None, total_statutory_monthly_sum=None,
                       total_statutory_annual_sum=None, total_other_deductions_monthly_sum=None,
                       total_other_deductions_annual_sum=None, netpay=None, business_unit=None, 
                       tax_type=None, employee_type=None, employment_type=None, division=None,
                       city=None, zip_code=None, country=None, email=None, dob=None, hire_date=None,
                       department_id=None, designation_id=None, status=None, role=None,
                       organisation_id=None, bank_account=None, level=None, currency=None,
                       work_schedule=None, taxID=None, template_id=None, employeeID=None,
                       bank_name=None, sort_code=None, phone=None, address=None, salary_type=None,
                       hours_worked=None, number_of_days_worked=None, rate=None, is_prorated=None, pfa_name=None, pfa_number=None,
                       tax_state=None, pfc_name=None, pfc_account_number=None):

        # Only set the tax and deduction fields to None if not provided
        employee = EmployeeModel(
            first_name=first_name,
            last_name=last_name,
            email=email,
            gender=gender,
            password_hash=password_hash,
            hire_date=hire_date,
            dob=dob,
            state=state,
            address=address,
            city=city,
            zip_code=zip_code,
            country=country,
            department_id=department_id,
            designation_id=designation_id,
            status=status,
            role=role,
            business_unit=business_unit,
            tax_type=tax_type,
            division=division,
            employee_type=employee_type,
            employment_type=employment_type,
            organisation_id=organisation_id,
            bank_account=bank_account,
            bank_name=bank_name,
            sort_code=sort_code,
            phone=phone,
            taxID=taxID,
            salary_type=salary_type,
            hours_worked=hours_worked,
            rate=rate,
            number_of_days_worked=number_of_days_worked,
            employeeID=employeeID,
            template_id=template_id,
            gross_pay=gross_pay,
            source_tag=source_tag,
            annual_leave_days=annual_leave_days,
            unpaid_leave_days=unpaid_leave_days,
            sick_leave_days=sick_leave_days,
            maternity_paternity_leave_days=maternity_paternity_leave_days,
            casual_leave_days=casual_leave_days,
            compassionate_leave_days=compassionate_leave_days,
            total_working_days=total_working_days,
            total_present_days=total_present_days,
            total_absent_days=total_absent_days,
            pension_no=pension_no,
            pension_pfa=pension_pfa,
            nhf_no=nhf_no,
            nhf_mortgage_bank=nhf_mortgage_bank,
            pfa_name=pfa_name,
            pfa_number=pfa_number,
            tax_state =tax_state,
            pfc_name = pfa_name,
            pfc_account_number=pfc_account_number,
            
            # Nullable fields
            monthly_tax=monthly_tax,
            annual_tax=annual_tax,
            total_taxable_monthly_sum=total_taxable_monthly_sum,
            total_taxable_annual_sum=total_taxable_annual_sum,
            total_non_taxable_monthly_sum=total_non_taxable_monthly_sum,
            total_non_taxable_annual_sum=total_non_taxable_annual_sum,
            total_statutory_monthly_sum=total_statutory_monthly_sum,
            total_statutory_annual_sum=total_statutory_annual_sum,
            total_other_deductions_monthly_sum=total_other_deductions_monthly_sum,
            total_other_deductions_annual_sum=total_other_deductions_annual_sum,
            netpay=netpay,
            is_prorated=is_prorated,
            
            # Non-nullable fields can remain here as usual
            level=level,
            currency=currency,
            work_schedule=work_schedule,
            user_id=UserRepository.authUserId()

        )
        

        db.session.add(employee)
        db.session.commit()
        return employee
        
    @classmethod
    def fetchAll(self):
        return EmployeeModel.query.filter_by(user_id = UserRepository.authUserId).order_by(EmployeeModel.created_at.desc()).all()

    @classmethod
    def getEmployeeCountAndGross(self):
        user_id = UserRepository.authUserId()

        result = db.session.query(
            func.count().filter(EmployeeModel.employment_type == "Contract").label("contract_count"),
            func.count().filter(EmployeeModel.employment_type == "Permanent").label("permanent_count"),
            func.coalesce(func.sum(EmployeeModel.gross_pay), 0).label("total_gross_pay")
        ).filter(EmployeeModel.user_id == user_id).first()

        return {
            "contract_count": result.contract_count,
            "permanent_count": result.permanent_count,
            "total_gross_pay": float(result.total_gross_pay)
        }
    
    def getEmployeeByEmailOrId(self, email, employee_id):
        return EmployeeModel.query.filter(
            (EmployeeModel.email == email) | 
            (EmployeeModel.employeeID == employee_id)
        ).first()
    
    @classmethod
    def getEmployee(self, id):
        return EmployeeModel.query.filter(EmployeeModel.id == id).first()
    
    @classmethod
    def getEmployeeByKeys(self, kwargs):
        user_id = UserRepository().authUserId()
        filters = {"user_id": user_id, **kwargs}
        return EmployeeModel.query.filter_by(**filters).all()
    
    @classmethod
    def getEmployeesByDepartment(cls, department_id):
        """Retrieve employees belonging to a specific department"""
        return EmployeeModel.query.filter_by(department_id=department_id).all()
    
    @classmethod
    def getEmployeesByDesignation(cls, designation_id):
        """Retrieve employees with a specific designation"""
        return EmployeeModel.query.filter_by(designation_id=designation_id).all()
    
    @classmethod
    def getEmployeesByType(cls, employment_type):
        """Retrieve employees by their employment type"""
        print("employment_type", employment_type)
        # Call the method to get the actual user ID
        user_id = UserRepository.authUserId()

        return EmployeeModel.query.filter_by(
            user_id=user_id, 
            employment_type=employment_type
        ).all()
           
    @classmethod
    def updateEmployee(self, id, **kwargs):
        employee = EmployeeModel.query.filter_by(id=id).first()
        if employee:
            for key, value in kwargs.items():
                setattr(employee, key, value)
                employee.updated_at = datetime.now()
            db.session.commit()
            return employee
        else:
            return None

    @classmethod
    def deleteEmployee(self, id):
        EmployeeComponentsPivotModel.query.filter(EmployeeComponentsPivotModel.employee_id == id).delete()

        if RemarkModel.query.filter(RemarkModel.employee_id == id):
            RemarkModel.query.filter(RemarkModel.employee_id == id).delete()
        
        if EmployeeBenefitsPivotModel.query.filter(EmployeeBenefitsPivotModel.employee_id == id):
            EmployeeBenefitsPivotModel.query.filter(EmployeeBenefitsPivotModel.employee_id == id).delete()

        if PayrollHistoryModel.query.filter(PayrollHistoryModel.employee_id == id):
            PayrollHistoryModel.query.filter(PayrollHistoryModel.employee_id == id).delete()

        if ProrateSalaryModel.query.filter(ProrateSalaryModel.employee_id == id):
            ProrateSalaryModel.query.filter(ProrateSalaryModel.employee_id == id).delete()

        



        EmployeeModel.query.filter(EmployeeModel.id == id).delete()
        db.session.commit()
        return
    
    @classmethod
    def searchEmployees(self, search_value, offset, size):
        to_search = f'%{search_value}%'
        return EmployeeModel.query.filter(EmployeeModel.name.like(to_search)) \
            .offset(offset).limit(size).all()

    @classmethod
    def sortEmployees(self, sort_key, offset, size, sort_dir: str = "asc",):
        if sort_dir == "desc":
            employees = EmployeeModel.query.order_by(
                desc(getattr(EmployeeModel, sort_key, "name"))
                ).offset(offset=offset).limit(limit=size).all()
        else:
            employees = EmployeeModel.query.order_by(
                desc(getattr(EmployeeModel, sort_key, "name"))
                ).offset(offset=offset).limit(limit=size).all()
        return employees

    @classmethod
    def fetchEmployees(self, offset: int, size: int = 50):
        employees = EmployeeModel.query.filter_by(user_id=UserRepository().authUserId()).order_by(EmployeeModel.created_at.desc()).offset(offset=offset).limit(limit=size).all()
        return employees

    
    @classmethod
    def fetchEmployeesTaxDetails(self, offset: int, size: int = 50):
        # Fetch employees with specific fields: first_name, last_name, gross_pay, and tax_type
        employees = EmployeeModel.query \
            .with_entities(EmployeeModel.first_name, EmployeeModel.last_name, EmployeeModel.gross_pay, EmployeeModel.tax_type) \
            .filter_by(user_id=UserRepository().authUserId()) \
            .order_by(EmployeeModel.created_at.desc()) \
            .offset(offset).limit(size).all()
        return employees
    

    # Method to count all employees for the authenticated user
    @classmethod
    def countEmployees(self):
        return EmployeeModel.query.filter_by(user_id=UserRepository().authUserId()).count()

    @classmethod
    def fetchActiveEmployees(self, offset: int = 0, size: int = 50):
        try:
            # Fetch active employees
            active_employees = EmployeeModel.query.filter_by(user_id=UserRepository().authUserId(), status='Active') \
                .order_by(EmployeeModel.created_at.desc()) \
                .offset(offset).limit(size).all()

            # Count all active employees
            total_active = EmployeeModel.query.filter_by(user_id=UserRepository().authUserId(), status='Active').count()

            return active_employees, total_active

        except Exception as e:
            traceback.print_exc()
            return [], 0
    


    # Method to fetch the last 5 recent employees based on hire_date
    @classmethod
    def fetchRecentEmployees(self):
        employees = EmployeeModel.query \
            .with_entities(EmployeeModel.first_name, EmployeeModel.last_name, EmployeeModel.created_at, EmployeeModel.status) \
            .filter_by(user_id=UserRepository().authUserId()) \
            .order_by(EmployeeModel.hire_date.desc()) \
            .limit(5).all()
        return employees
    
    @classmethod
    def assignTemplateToEmployees(cls, employee_ids, template_id):
        """
        Assign a salary template to multiple employees by updating their template_id.
        Uses a bulk `update()` for 100% reliability.
        """
        try:
            if not isinstance(employee_ids, list):
                employee_ids = [employee_ids]  # Ensure it's always a list

            # Perform a bulk update on all matching employees in a single query
            updated_count = EmployeeModel.query.filter(EmployeeModel.id.in_(employee_ids)).update(
                {"template_id": template_id, "updated_at": datetime.now()}, synchronize_session=False
            )

            db.session.commit()  # Commit changes

            if updated_count == 0:
                # print("❌ No employees were updated!")
                return None

            # print(f"✅ Successfully updated {updated_count} employees with template_id {template_id}")
            return updated_count

        except Exception as e:
            db.session.rollback()
            import traceback
            traceback.print_exc()
            print(f"❌ Error assigning template to employees: {e}")
            return None


        
    @classmethod
    def getEmployeesByOrganisation(cls, organisation_id):
        """Retrieve employees belonging to a specific organisation"""
        return EmployeeModel.query.filter_by(organisation_id=organisation_id).all()

    @classmethod
    def getEmployeesByTemplate(cls, template_id):
        """Retrieve employees assigned to a specific salary template"""
        return EmployeeModel.query.filter_by(template_id=template_id).all()
