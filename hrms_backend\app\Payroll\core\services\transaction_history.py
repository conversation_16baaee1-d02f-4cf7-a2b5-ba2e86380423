from core.repositories.transaction_history import TransactionHistoryRepository

class TransactionHistoryService:
    def __init__(self) -> None:
        self.repository = TransactionHistoryRepository()

    def createTransaction(self, Kwargs):
        return self.repository.create_transaction(**Kwargs)

    def fetchAll(self):
        return self.repository.fetchAll()
    
    def fetchOne(self, id):
        return self.repository.getTransactionById(id)



