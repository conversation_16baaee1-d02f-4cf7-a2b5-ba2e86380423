class NigeriaTaxCalculator:
    PAYE_BANDS = [
        (300000, 0.07),
        (300000, 0.11),
        (500000, 0.15),
        (500000, 0.19),
        (1600000, 0.21),
        (float('inf'), 0.24),
    ]

    def __init__(self, tax_type="PAYE"):
        self.tax_type = (tax_type or 'PAYE').upper()

    def calculate_annual_tax(self, annual_earnings, annual_pen=0, annual_nhf=0):
        tax_methods = {
            "NA": lambda: 0,
            "WHT": lambda: annual_earnings * 0.075,
            "MINTAX": lambda: max(annual_earnings * 0.01, 0),
            "PAYE": lambda: self._calculate_paye(annual_earnings, annual_pen, annual_nhf),
        }

        return tax_methods.get(self.tax_type, lambda: 0)()

    def _calculate_paye(self, earnings, pen, nhf):
        statutory_deductions = pen + nhf
        statutory_earnings = earnings - statutory_deductions
        cra = 0.20 * statutory_earnings + 200000
        taxable_income = statutory_earnings - cra

        if taxable_income <= 0:
            return 0

        return self._calculate_tax_from_bands(taxable_income)

    def _calculate_tax_from_bands(self, taxable_income):
        tax = 0
        for band_limit, rate in self.PAYE_BANDS:
            if taxable_income > band_limit:
                tax += band_limit * rate
                taxable_income -= band_limit
            else:
                tax += taxable_income * rate
                break
        return tax

    def calculate_monthly_tax(self, annual_tax):
        return round(annual_tax / 12, 2)
