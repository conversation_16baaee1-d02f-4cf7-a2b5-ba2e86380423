from core.databases.database import db
from core.models.basemodel import ModelBase
from datetime import datetime
class LicensesModel(ModelBase):
    __tablename__ = "licenses"

    id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    number_of_license = db.Column(db.Integer, nullable=False)
    license_used = db.Column(db.Integer, nullable=False)
    approved_by = db.Column(db.Integer, nullable=False)
    date_approved = db.Column(db.Integer, nullable=False)
    user_id = db.Column(db.Integer, db.<PERSON>Key((ModelBase.dbSchema() + '.users.id')), nullable=False) 
    timestamp = db.Column(db.DateTime, nullable=False, default=datetime.now()) 

   

    
    
    
      