#!/usr/bin/env python3
"""
Check enum types in the database
"""

import psycopg2
import os
from dotenv import load_dotenv

load_dotenv()

def check_enum_types():
    """Check what enum values are available"""
    
    conn = psycopg2.connect(
        host=os.getenv("DB_HOST", "localhost"),
        database=os.getenv("DB_NAME", "hrms_db"),
        user=os.getenv("DB_USER", "postgres"),
        password=os.getenv("DB_PASSWORD", "password"),
        port=os.getenv("DB_PORT", "5432")
    )
    
    try:
        cursor = conn.cursor()
        
        # Check leave type enum values
        cursor.execute("SELECT unnest(enum_range(NULL::leavetype))")
        leave_types = [row[0] for row in cursor.fetchall()]
        print("Available leave types:", leave_types)
        
        # Check leave status enum values
        cursor.execute("SELECT unnest(enum_range(NULL::leavestatus))")
        leave_statuses = [row[0] for row in cursor.fetchall()]
        print("Available leave statuses:", leave_statuses)
        
        # Check leave duration enum values
        cursor.execute("SELECT unnest(enum_range(NULL::leaveduration))")
        leave_durations = [row[0] for row in cursor.fetchall()]
        print("Available leave durations:", leave_durations)
        
    except Exception as e:
        print(f"Error checking enum types: {e}")
    finally:
        cursor.close()
        conn.close()

if __name__ == "__main__":
    check_enum_types()
