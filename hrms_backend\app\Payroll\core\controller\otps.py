import random
import string

from flask_mail import Message
from flask import current_app


class OTPGenerator:
    @staticmethod
    def generate_otp(length=6):
        """Generate a random OTP with the specified length."""
        digits = string.digits
        otp = ''.join(random.choice(digits) for _ in range(length))
        return otp


class SendMail:
    @staticmethod
    def send_otp(email, otp):
        try:
            from app import mail
            
            msg = Message("Your OTP Code",
                          sender=current_app.config['MAIL_USERNAME'],
                          recipients=[email])
            msg.body = f"Your OTP code is: {otp}"
            mail.send(msg)
            return True
        except Exception as e:
            print(f"Failed to send email: {str(e)}")
            return False

