#!/usr/bin/env python3
"""
Create a super admin user for the HRMS system
"""

import sys
import os
from uuid import uuid4
from datetime import datetime

# Add the app directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'app'))

from sqlalchemy.orm import Session
from sqlalchemy import text
from app.db.session import SessionLocal
from app.core.security import get_password_hash

def create_super_admin():
    """Create a super admin user"""
    db = SessionLocal()
    
    try:
        print("Creating Super Admin user...")
        
        # Check if super admin already exists
        result = db.execute(text("SELECT id FROM users WHERE email = :email"), 
                          {'email': '<EMAIL>'})
        existing_user = result.fetchone()
        
        if existing_user:
            print("✅ Super Admin user already exists!")
            print("Email: <EMAIL>")
            print("Password: password123")
            return
        
        # Get organization ID (use the first one or create default)
        result = db.execute(text("SELECT id FROM organizations LIMIT 1"))
        org_result = result.fetchone()
        
        if org_result:
            org_id = org_result[0]
        else:
            # Create default organization
            org_id = uuid4()
            org_insert_sql = text("""
                INSERT INTO organizations (id, name, description, is_active, created_at, updated_at)
                VALUES (:id, :name, :description, :is_active, :created_at, :updated_at)
            """)
            
            db.execute(org_insert_sql, {
                'id': org_id,
                'name': 'Default Organization',
                'description': 'Default organization for HRMS',
                'is_active': True,
                'created_at': datetime.utcnow(),
                'updated_at': datetime.utcnow()
            })
            print("✅ Created default organization")
        
        # Create super admin user
        user_id = uuid4()
        hashed_password = get_password_hash("password123")
        
        user_insert_sql = text("""
            INSERT INTO users (id, email, password, role, organization_id, is_active, is_verified, created_at, updated_at)
            VALUES (:id, :email, :password, :role, :organization_id, :is_active, :is_verified, :created_at, :updated_at)
        """)
        
        db.execute(user_insert_sql, {
            'id': user_id,
            'email': '<EMAIL>',
            'password': hashed_password,
            'role': 'SUPER_ADMIN',
            'organization_id': org_id,
            'is_active': True,
            'is_verified': True,
            'created_at': datetime.utcnow(),
            'updated_at': datetime.utcnow()
        })
        
        print("✅ Created Super Admin user")
        
        # Create corresponding employee record
        employee_id = uuid4()
        employee_insert_sql = text("""
            INSERT INTO employees (
                id, user_id, employee_id, first_name, last_name, email,
                hire_date, is_active, created_at, updated_at
            ) VALUES (
                :id, :user_id, :employee_id, :first_name, :last_name, :email,
                :hire_date, :is_active, :created_at, :updated_at
            )
        """)
        
        db.execute(employee_insert_sql, {
            'id': employee_id,
            'user_id': user_id,
            'employee_id': 'SUPER001',
            'first_name': 'Super',
            'last_name': 'Admin',
            'email': '<EMAIL>',
            'hire_date': datetime.utcnow(),
            'is_active': True,
            'created_at': datetime.utcnow(),
            'updated_at': datetime.utcnow()
        })
        
        print("✅ Created Super Admin employee record")
        
        db.commit()
        
        print("\n🎉 Super Admin user created successfully!")
        print("=" * 50)
        print("Email: <EMAIL>")
        print("Password: password123")
        print("Role: SUPER_ADMIN")
        print("Employee ID: SUPER001")
        print("=" * 50)
        print("\nYou can now login with these credentials!")
        
    except Exception as e:
        print(f"❌ Error creating super admin: {e}")
        db.rollback()
        raise
    finally:
        db.close()

if __name__ == "__main__":
    create_super_admin()
