from sqlalchemy import <PERSON>um<PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, DateTime, Text, Integer, Foreign<PERSON>ey, Enum as SQLE<PERSON>, Numeric, Date
from sqlalchemy.dialects.postgresql import UUID, JSONB
from sqlalchemy.orm import relationship
from uuid import uuid4
from datetime import datetime, date
import enum

from ..base import BaseModel, AuditMixin


class JobStatus(str, enum.Enum):
    """Job posting status enumeration"""
    DRAFT = "draft"
    ACTIVE = "active"
    PAUSED = "paused"
    CLOSED = "closed"
    CANCELLED = "cancelled"


class JobType(str, enum.Enum):
    """Job type enumeration"""
    FULL_TIME = "full_time"
    PART_TIME = "part_time"
    CONTRACT = "contract"
    TEMPORARY = "temporary"
    INTERNSHIP = "internship"
    FREELANCE = "freelance"


class ApplicationStatus(str, enum.Enum):
    """Application status enumeration"""
    APPLIED = "applied"
    SCREENING = "screening"
    INTERVIEW_SCHEDULED = "interview_scheduled"
    INTERVIEWED = "interviewed"
    UNDER_REVIEW = "under_review"
    SHORTLISTED = "shortlisted"
    OFFER_EXTENDED = "offer_extended"
    OFFER_ACCEPTED = "offer_accepted"
    OFFER_DECLINED = "offer_declined"
    REJECTED = "rejected"
    WITHDRAWN = "withdrawn"
    HIRED = "hired"


class InterviewType(str, enum.Enum):
    """Interview type enumeration"""
    PHONE = "phone"
    VIDEO = "video"
    IN_PERSON = "in_person"
    TECHNICAL = "technical"
    BEHAVIORAL = "behavioral"
    PANEL = "panel"
    GROUP = "group"


class InterviewStatus(str, enum.Enum):
    """Interview status enumeration"""
    SCHEDULED = "scheduled"
    COMPLETED = "completed"
    CANCELLED = "cancelled"
    RESCHEDULED = "rescheduled"
    NO_SHOW = "no_show"


class JobPosting(BaseModel, AuditMixin):
    """Job posting model"""
    __tablename__ = "job_postings"

    # Basic Information
    title = Column(String(200), nullable=False, index=True)
    description = Column(Text, nullable=False)
    requirements = Column(Text, nullable=True)
    responsibilities = Column(Text, nullable=True)
    
    # Job Details
    job_type = Column(SQLEnum(JobType), nullable=False)
    department_id = Column(UUID(as_uuid=True), ForeignKey("departments.id"), nullable=False)
    location = Column(String(255), nullable=True)
    remote_allowed = Column(Boolean, default=False, nullable=False)
    
    # Compensation
    salary_min = Column(Numeric(12, 2), nullable=True)
    salary_max = Column(Numeric(12, 2), nullable=True)
    currency = Column(String(3), nullable=True, default="USD")
    benefits = Column(Text, nullable=True)
    
    # Status and Dates
    status = Column(SQLEnum(JobStatus), nullable=False, default=JobStatus.DRAFT)
    posted_date = Column(Date, nullable=True)
    application_deadline = Column(Date, nullable=True)
    start_date = Column(Date, nullable=True)
    
    # Hiring Information
    hiring_manager_id = Column(UUID(as_uuid=True), ForeignKey("employees.id"), nullable=True)
    recruiter_id = Column(UUID(as_uuid=True), ForeignKey("employees.id"), nullable=True)
    positions_available = Column(Integer, nullable=False, default=1)
    positions_filled = Column(Integer, nullable=False, default=0)
    
    # Additional Information
    skills_required = Column(JSONB, nullable=True)  # Array of required skills
    experience_level = Column(String(50), nullable=True)  # Entry, Mid, Senior, Executive
    education_requirements = Column(Text, nullable=True)
    
    # Organization
    organization_id = Column(UUID(as_uuid=True), nullable=True, index=True)
    
    # Relationships
    department = relationship("Department", back_populates="job_postings")
    hiring_manager = relationship("Employee", foreign_keys=[hiring_manager_id], back_populates="managed_job_postings")
    recruiter = relationship("Employee", foreign_keys=[recruiter_id], back_populates="recruited_job_postings")
    applications = relationship("JobApplication", back_populates="job_posting")

    def __repr__(self):
        return f"<JobPosting(title='{self.title}', status='{self.status}')>"


class Candidate(BaseModel, AuditMixin):
    """Candidate model"""
    __tablename__ = "candidates"

    # Personal Information
    first_name = Column(String(100), nullable=False)
    last_name = Column(String(100), nullable=False)
    email = Column(String(255), unique=True, nullable=False, index=True)
    phone = Column(String(20), nullable=True)
    
    # Professional Information
    current_position = Column(String(200), nullable=True)
    current_company = Column(String(200), nullable=True)
    total_experience = Column(Integer, nullable=True)  # Years of experience
    expected_salary = Column(Numeric(12, 2), nullable=True)
    currency = Column(String(3), nullable=True, default="USD")
    
    # Location
    current_location = Column(String(255), nullable=True)
    willing_to_relocate = Column(Boolean, default=False, nullable=False)
    
    # Documents and Links
    resume_url = Column(String(500), nullable=True)
    cover_letter_url = Column(String(500), nullable=True)
    portfolio_url = Column(String(500), nullable=True)
    linkedin_url = Column(String(500), nullable=True)
    
    # Skills and Education
    skills = Column(JSONB, nullable=True)  # Array of skills
    education = Column(JSONB, nullable=True)  # Array of education records
    certifications = Column(JSONB, nullable=True)  # Array of certifications
    
    # Source and Notes
    source = Column(String(100), nullable=True)  # How they found the job
    notes = Column(Text, nullable=True)
    
    # Organization
    organization_id = Column(UUID(as_uuid=True), nullable=True, index=True)
    
    # Relationships
    applications = relationship("JobApplication", back_populates="candidate")

    def __repr__(self):
        return f"<Candidate(name='{self.first_name} {self.last_name}', email='{self.email}')>"


class JobApplication(BaseModel, AuditMixin):
    """Job application model"""
    __tablename__ = "job_applications"

    # References
    job_posting_id = Column(UUID(as_uuid=True), ForeignKey("job_postings.id"), nullable=False)
    candidate_id = Column(UUID(as_uuid=True), ForeignKey("candidates.id"), nullable=False)
    
    # Application Details
    status = Column(SQLEnum(ApplicationStatus), nullable=False, default=ApplicationStatus.APPLIED)
    applied_date = Column(DateTime, nullable=False, default=datetime.utcnow)
    
    # Documents
    resume_url = Column(String(500), nullable=True)
    cover_letter_url = Column(String(500), nullable=True)
    additional_documents = Column(JSONB, nullable=True)  # Array of document URLs
    
    # Responses
    application_responses = Column(JSONB, nullable=True)  # Responses to application questions
    
    # Tracking
    last_updated = Column(DateTime, nullable=False, default=datetime.utcnow)
    assigned_recruiter_id = Column(UUID(as_uuid=True), ForeignKey("employees.id"), nullable=True)
    
    # Scoring and Notes
    screening_score = Column(Integer, nullable=True)  # 1-100 score
    recruiter_notes = Column(Text, nullable=True)
    rejection_reason = Column(Text, nullable=True)
    
    # Organization
    organization_id = Column(UUID(as_uuid=True), nullable=True, index=True)
    
    # Relationships
    job_posting = relationship("JobPosting", back_populates="applications")
    candidate = relationship("Candidate", back_populates="applications")
    assigned_recruiter = relationship("Employee", foreign_keys=[assigned_recruiter_id])
    interviews = relationship("Interview", back_populates="application")
    offers = relationship("JobOffer", back_populates="application")

    def __repr__(self):
        return f"<JobApplication(candidate_id='{self.candidate_id}', job_posting_id='{self.job_posting_id}', status='{self.status}')>"


class Interview(BaseModel, AuditMixin):
    """Interview model"""
    __tablename__ = "interviews"

    # References
    application_id = Column(UUID(as_uuid=True), ForeignKey("job_applications.id"), nullable=False)
    
    # Interview Details
    interview_type = Column(SQLEnum(InterviewType), nullable=False)
    status = Column(SQLEnum(InterviewStatus), nullable=False, default=InterviewStatus.SCHEDULED)
    
    # Scheduling
    scheduled_date = Column(DateTime, nullable=False)
    duration_minutes = Column(Integer, nullable=False, default=60)
    location = Column(String(255), nullable=True)  # Physical location or video link
    
    # Participants
    interviewer_ids = Column(JSONB, nullable=False)  # Array of interviewer employee IDs
    
    # Results
    feedback = Column(Text, nullable=True)
    rating = Column(Integer, nullable=True)  # 1-10 rating
    recommendation = Column(String(50), nullable=True)  # hire, reject, next_round
    
    # Additional Information
    questions_asked = Column(JSONB, nullable=True)  # Array of questions
    candidate_responses = Column(JSONB, nullable=True)  # Candidate responses
    technical_assessment = Column(JSONB, nullable=True)  # Technical assessment results
    
    # Organization
    organization_id = Column(UUID(as_uuid=True), nullable=True, index=True)
    
    # Relationships
    application = relationship("JobApplication", back_populates="interviews")

    def __repr__(self):
        return f"<Interview(application_id='{self.application_id}', type='{self.interview_type}', status='{self.status}')>"


class JobOffer(BaseModel, AuditMixin):
    """Job offer model"""
    __tablename__ = "job_offers"

    # References
    application_id = Column(UUID(as_uuid=True), ForeignKey("job_applications.id"), nullable=False)
    
    # Offer Details
    position_title = Column(String(200), nullable=False)
    department_id = Column(UUID(as_uuid=True), ForeignKey("departments.id"), nullable=False)
    start_date = Column(Date, nullable=False)
    
    # Compensation
    base_salary = Column(Numeric(12, 2), nullable=False)
    currency = Column(String(3), nullable=False, default="USD")
    bonus = Column(Numeric(12, 2), nullable=True)
    benefits = Column(Text, nullable=True)
    
    # Offer Status
    status = Column(String(50), nullable=False, default="pending")  # pending, accepted, declined, withdrawn
    offered_date = Column(DateTime, nullable=False, default=datetime.utcnow)
    response_deadline = Column(Date, nullable=True)
    response_date = Column(DateTime, nullable=True)
    
    # Documents
    offer_letter_url = Column(String(500), nullable=True)
    contract_url = Column(String(500), nullable=True)
    
    # Additional Terms
    probation_period_months = Column(Integer, nullable=True)
    notice_period_days = Column(Integer, nullable=True)
    additional_terms = Column(Text, nullable=True)
    
    # Organization
    organization_id = Column(UUID(as_uuid=True), nullable=True, index=True)
    
    # Relationships
    application = relationship("JobApplication", back_populates="offers")
    department = relationship("Department")

    def __repr__(self):
        return f"<JobOffer(application_id='{self.application_id}', position='{self.position_title}', status='{self.status}')>"
