"""Create payment_gateway table

Revision ID: 603965313e4b
Revises: 8450eaf1db52
Create Date: 2025-02-12 22:30:30.969484

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy import func, TIMESTAMP, Foreign<PERSON>ey
from sqlalchemy import Column, Integer, String


# revision identifiers, used by Alembic.
revision: str = '603965313e4b'
down_revision: Union[str, None] = '8450eaf1db52'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    op.create_table(
        'payment_gateway',
        Column('id', Integer, primary_key=True),
        <PERSON>umn("user_id", Integer, ForeignKey("users.id")),
        <PERSON>umn('payment_gateway_name', String(250), nullable=True),
        <PERSON>umn("timestamp", TIMESTAMP, server_default=func.now()),
        )

def downgrade() -> None:
    op.drop_table("payment_gateway")