from fastapi import APIRouter, HTTPException, status, Depends, Query
from sqlalchemy.orm import Session
from typing import List, Optional
from uuid import UUID
from datetime import date, datetime

from ..core.security import get_current_user, require_permission, CurrentUser
from ..core.rbac import Permissions
from ..db.session import get_db

router = APIRouter()

# Root endpoint
@router.get("/")
async def get_reports_overview(
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.ADMIN_REPORTS))
):
    """Get reports overview"""
    try:
        return {
            "message": "Reports and analytics system",
            "endpoints": [
                "/overview - Get overview report",
                "/attendance - Get attendance report",
                "/payroll - Get payroll report",
                "/performance - Get performance report"
            ]
        }
    except Exception as e:
        return {"error": str(e)}

# Report endpoints
@router.get("/overview")
async def get_overview_report(
    start_date: Optional[date] = Query(None),
    end_date: Optional[date] = Query(None),
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.ADMIN_REPORTS))
):
    """Get overview report with key metrics"""
    # Mock data for now - replace with actual service implementation
    return {
        "total_employees": 156,
        "active_employees": 148,
        "new_hires": 8,
        "turnover_rate": 5.2,
        "avg_attendance": 94.5,
        "total_payroll": 12500000,
        "period": {
            "start_date": start_date or "2024-01-01",
            "end_date": end_date or "2024-01-31"
        }
    }

@router.get("/attendance")
async def get_attendance_report(
    start_date: Optional[date] = Query(None),
    end_date: Optional[date] = Query(None),
    department_id: Optional[UUID] = Query(None),
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.ADMIN_REPORTS))
):
    """Get attendance report"""
    # Mock data for now - replace with actual service implementation
    return {
        "summary": {
            "total_working_days": 22,
            "avg_attendance_rate": 94.5,
            "total_present": 3248,
            "total_absent": 184,
            "total_late": 92
        },
        "department_breakdown": [
            {"department": "Engineering", "employees": 45, "attendance_rate": 95.2, "avg_hours": 8.2},
            {"department": "Marketing", "employees": 28, "attendance_rate": 94.1, "avg_hours": 8.1},
            {"department": "Sales", "employees": 32, "attendance_rate": 93.8, "avg_hours": 8.0},
            {"department": "HR", "employees": 15, "attendance_rate": 96.5, "avg_hours": 8.3},
            {"department": "Finance", "employees": 18, "attendance_rate": 95.8, "avg_hours": 8.2},
            {"department": "Operations", "employees": 18, "attendance_rate": 94.2, "avg_hours": 8.1}
        ],
        "daily_trends": [
            {"date": "2024-01-01", "present": 142, "absent": 6, "late": 3},
            {"date": "2024-01-02", "present": 145, "absent": 3, "late": 2},
            {"date": "2024-01-03", "present": 144, "absent": 4, "late": 4}
        ]
    }

@router.get("/leave")
async def get_leave_report(
    start_date: Optional[date] = Query(None),
    end_date: Optional[date] = Query(None),
    leave_type: Optional[str] = Query(None),
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.ADMIN_REPORTS))
):
    """Get leave report"""
    # Mock data for now - replace with actual service implementation
    return {
        "summary": {
            "total_requests": 413,
            "approved": 375,
            "pending": 22,
            "rejected": 16,
            "approval_rate": 90.8
        },
        "leave_types": [
            {"type": "Annual Leave", "total": 245, "approved": 220, "pending": 15, "rejected": 10},
            {"type": "Sick Leave", "total": 89, "approved": 85, "pending": 2, "rejected": 2},
            {"type": "Personal Leave", "total": 67, "approved": 58, "pending": 5, "rejected": 4},
            {"type": "Maternity Leave", "total": 12, "approved": 12, "pending": 0, "rejected": 0}
        ],
        "monthly_trends": [
            {"month": "Jan", "requests": 45, "approved": 42},
            {"month": "Feb", "requests": 38, "approved": 35},
            {"month": "Mar", "requests": 52, "approved": 48}
        ]
    }

@router.get("/payroll")
async def get_payroll_report(
    start_date: Optional[date] = Query(None),
    end_date: Optional[date] = Query(None),
    department_id: Optional[UUID] = Query(None),
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.PAYROLL_READ))
):
    """Get payroll report"""
    # Mock data for now - replace with actual service implementation
    return {
        "summary": {
            "total_payroll": 12500000,
            "total_employees": 156,
            "avg_salary": 80128,
            "total_deductions": 1875000,
            "net_payroll": 10625000
        },
        "department_breakdown": [
            {"department": "Engineering", "employees": 45, "total_salary": 5400000, "avg_salary": 120000},
            {"department": "Marketing", "employees": 28, "total_salary": 2380000, "avg_salary": 85000},
            {"department": "Sales", "employees": 32, "total_salary": 2400000, "avg_salary": 75000},
            {"department": "HR", "employees": 15, "total_salary": 1350000, "avg_salary": 90000},
            {"department": "Finance", "employees": 18, "total_salary": 1710000, "avg_salary": 95000},
            {"department": "Operations", "employees": 18, "total_salary": 1260000, "avg_salary": 70000}
        ],
        "salary_components": {
            "basic_salary": 7500000,
            "allowances": 3750000,
            "overtime": 1250000,
            "deductions": {
                "tax": 1275000,
                "pension": 375000,
                "nhf": 225000
            }
        }
    }

@router.get("/performance")
async def get_performance_report(
    start_date: Optional[date] = Query(None),
    end_date: Optional[date] = Query(None),
    department_id: Optional[UUID] = Query(None),
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.PERFORMANCE_READ))
):
    """Get performance report"""
    # Mock data for now - replace with actual service implementation
    return {
        "summary": {
            "total_reviews": 148,
            "completed_reviews": 132,
            "pending_reviews": 16,
            "avg_rating": 4.1,
            "completion_rate": 89.2
        },
        "rating_distribution": {
            "5_star": 28,
            "4_star": 65,
            "3_star": 32,
            "2_star": 6,
            "1_star": 1
        },
        "department_performance": [
            {"department": "Engineering", "avg_rating": 4.3, "completed_reviews": 42},
            {"department": "Marketing", "avg_rating": 4.0, "completed_reviews": 25},
            {"department": "Sales", "avg_rating": 3.9, "completed_reviews": 28},
            {"department": "HR", "avg_rating": 4.2, "completed_reviews": 14},
            {"department": "Finance", "avg_rating": 4.1, "completed_reviews": 16},
            {"department": "Operations", "avg_rating": 3.8, "completed_reviews": 15}
        ],
        "goals_summary": {
            "total_goals": 324,
            "completed": 198,
            "in_progress": 89,
            "not_started": 37,
            "completion_rate": 61.1
        }
    }

@router.get("/projects")
async def get_projects_report(
    start_date: Optional[date] = Query(None),
    end_date: Optional[date] = Query(None),
    status: Optional[str] = Query(None),
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.PROJECT_READ))
):
    """Get projects report"""
    # Mock data for now - replace with actual service implementation
    return {
        "summary": {
            "total_projects": 24,
            "active_projects": 18,
            "completed_projects": 5,
            "on_hold_projects": 1,
            "avg_completion_rate": 73.5
        },
        "project_status": [
            {"status": "Planning", "count": 3},
            {"status": "In Progress", "count": 15},
            {"status": "Review", "count": 2},
            {"status": "Completed", "count": 5},
            {"status": "On Hold", "count": 1}
        ],
        "resource_utilization": {
            "total_hours_allocated": 3840,
            "total_hours_logged": 2912,
            "utilization_rate": 75.8
        },
        "top_projects": [
            {"name": "HRMS Development", "progress": 75, "team_size": 8},
            {"name": "Mobile App", "progress": 45, "team_size": 5},
            {"name": "Data Migration", "progress": 100, "team_size": 3}
        ]
    }

@router.get("/tickets")
async def get_tickets_report(
    start_date: Optional[date] = Query(None),
    end_date: Optional[date] = Query(None),
    category: Optional[str] = Query(None),
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.TICKET_READ))
):
    """Get tickets report"""
    # Mock data for now - replace with actual service implementation
    return {
        "summary": {
            "total_tickets": 156,
            "open_tickets": 23,
            "resolved_tickets": 118,
            "closed_tickets": 15,
            "avg_resolution_time": 2.3
        },
        "ticket_categories": [
            {"category": "Technical", "count": 68, "avg_resolution_time": 1.8},
            {"category": "HR", "count": 42, "avg_resolution_time": 3.2},
            {"category": "Facilities", "count": 28, "avg_resolution_time": 2.1},
            {"category": "Training", "count": 18, "avg_resolution_time": 4.5}
        ],
        "priority_breakdown": {
            "high": 15,
            "medium": 89,
            "low": 52
        },
        "resolution_trends": [
            {"week": "Week 1", "created": 12, "resolved": 8},
            {"week": "Week 2", "created": 15, "resolved": 14},
            {"week": "Week 3", "created": 18, "resolved": 16},
            {"week": "Week 4", "created": 11, "resolved": 13}
        ]
    }

@router.get("/export/{report_type}")
async def export_report(
    report_type: str,
    format: str = Query("csv", regex="^(csv|xlsx|pdf)$"),
    start_date: Optional[date] = Query(None),
    end_date: Optional[date] = Query(None),
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.ADMIN_REPORTS))
):
    """Export report in specified format"""
    # Mock response for now - replace with actual export implementation
    return {
        "message": f"Export initiated for {report_type} report in {format} format",
        "export_id": "exp_123456789",
        "status": "processing",
        "estimated_completion": "2024-01-20T10:30:00Z"
    }

@router.get("/export/{export_id}/status")
async def get_export_status(
    export_id: str,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.ADMIN_REPORTS))
):
    """Get export status"""
    # Mock response for now - replace with actual status check
    return {
        "export_id": export_id,
        "status": "completed",
        "download_url": f"/api/reports/export/{export_id}/download",
        "created_at": "2024-01-20T10:25:00Z",
        "completed_at": "2024-01-20T10:28:00Z"
    }

@router.get("/export/{export_id}/download")
async def download_export(
    export_id: str,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.ADMIN_REPORTS))
):
    """Download exported report"""
    # Mock response for now - replace with actual file download
    return {"message": f"Download link for export {export_id}"}
