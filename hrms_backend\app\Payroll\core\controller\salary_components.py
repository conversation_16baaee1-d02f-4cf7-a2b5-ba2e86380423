from flask import request, url_for, jsonify
from flask.views import MethodView
from core.repositories.employees_component import EmployeesComponentRepository
from flask_smorest import Blueprint, abort
from core.middleware import roles_required
from core.services.departments import DepartmentsService
from core.services.designations import DesignationsService
from core.services.employee import EmployeeService
from core.services.employees_components import EmployeesComponentService
from schemas import AssignSalaryComponentSchema, SalaryComponentSchema
from sqlalchemy.orm.exc import NoResultFound
from sqlalchemy.exc import SQLAlchemyError, IntegrityError
import core.utils.response_message as RESPONSEMESSAGE
from core.services.salary_components import SalaryComponentsService
from core.utils.responseBuilder import ResponseBuilder
from core.services.component_processor import ComponentProcessor
from schemas import EmployeeSchema
from core.services.tax_calculator_service import TaxCalculatorService

blueprint = Blueprint("salary_component", __name__, description="Operations for Salary Components")
    
@blueprint.route("/salary_components/<id>")
class SalaryComponent(MethodView):
    @roles_required(['admin'])
    @blueprint.response(200, SalaryComponentSchema)
    def get(self, id):
        service = SalaryComponentsService()
        salary_component = service.getSalaryComponents(id)
        if not salary_component:
            abort(401, message="Component does not exist")
        component_details = SalaryComponentSchema().dump(salary_component)
        return ResponseBuilder(data=component_details, status_code=200).build()

    
    @roles_required(['admin'])
    def delete(self, id):
        service = SalaryComponentsService()
        salary_component = service.getSalaryComponents(id)
        if not salary_component:
            abort(404, message="Template does not exist")

        if salary_component.employee_components:
            abort(400, message=f"Cannot delete component. It is still assigned to employees.")

        service.deleteSalaryComponents(id)
        return {"message" : "Component deleted"}
    
    @roles_required(['admin'])
    @blueprint.arguments(SalaryComponentSchema)
    @blueprint.response(201, SalaryComponentSchema)
    def put(self, data, id):
        service = SalaryComponentsService()
        salary_component = service.getSalaryComponents(id)
        if not salary_component:
            abort(404, message="Component does not exist")
        try :
            new_component = service.updateSalaryComponents(id, data)
            return new_component
        except SQLAlchemyError:
                abort(500, message="Error while updating Component")
    
@blueprint.route("/salary_components")
class SalaryComponentList(MethodView):
    @roles_required(['admin',"employee"])
    @blueprint.response(200, SalaryComponentSchema)
    def get(self):
        components_service = SalaryComponentsService()
        component_list, total_components = components_service.fetchAll()
        component_schema = SalaryComponentSchema(many=True)
        component_list = component_schema.dump(component_list)
        return ResponseBuilder(data=component_list, status_code=200, total=total_components).build()

    
    @roles_required(['admin'])
    @blueprint.arguments(SalaryComponentSchema)
    @blueprint.response(200, SalaryComponentSchema)
    def post(self, data):
        try:
            service = SalaryComponentsService()
            salary_component = service.getSalaryComponentsByKey({"component_name": data['component_name']})
            if not salary_component:
                new_component = service.createSalaryComponents(data)
            else:
                abort(400, message="Component already exist")
        except IntegrityError:
            abort(500, message="Error while creating Component")
        except SQLAlchemyError as e:
            print(e)
            abort(500, message="Error while creating Component")
        return new_component
    
@blueprint.route("/salary_components/assign")
class AssignSalaryComponent(MethodView):
    @roles_required(['admin'])
    @blueprint.arguments(AssignSalaryComponentSchema)  
    def post(self, data):
        """Assign a salary component based on criteria (all, department, designation, employee type)."""

        component_id = data["salary_component_id"]

        # Initialize services
        employee_service = EmployeeService()
        employeeComponentRepo = EmployeesComponentRepository()
        salary_service = SalaryComponentsService()
        department_service = DepartmentsService()
        designation_service = DesignationsService()

        # Validate salary component
        salary_component = salary_service.getSalaryComponents(component_id)
        if not salary_component:
            abort(404, message="Salary component not found")

        employees = []

        # Check assignment criteria
        employee_ids = data.get("employee_ids")
        department_id = data.get("department_id")
        designation_id = data.get("designation_id")
        employment_type = data.get("employment_type")
        
        if employee_ids:
            employees = employee_service.getEmployeesByIds(employee_ids)

        elif department_id:
            department = department_service.getDepartments(department_id)
            if not department:
                abort(404, message="Department not found")
            employees = employee_service.getEmployeesByDepartment(department_id)

        elif designation_id:
            designation = designation_service.getDesignations(designation_id)
            if not designation:
                abort(404, message="Designation not found")
            employees = employee_service.getEmployeesByDesignation(designation_id)

        elif employment_type:
            employees = employee_service.getEmployeesByType(employment_type)

        if not employees:
            abort(404, message="No employees found for the given criteria")
            
        # Assign salary component to each employee
        service = EmployeesComponentService()
        assigned_components = []

        for employee in employees:
            net_pay, monthly_tax, annual_pension, annual_nhf = 0, 0, 0, 0
            
            # Check if the component is already assigned to this employee
            # existing_component = employeeComponentRepo.getAttachedEmployeeComponent(employee.id, component_id)
            existing_components_id = [component.salary_component.id for component in employee.employee_components]
            if component_id not in existing_components_id:
                # Assign only if the component is not already assigned
                assigned_component = service.addSalaryComponent(employee.id, component_id)

                if assigned_component:
                    emplpyee_details = EmployeeSchema().dump(employee)
                    employee_compoents = emplpyee_details["employee_components"]
                    employee_benefits = emplpyee_details["employee_benefits"]
                    tax_type = emplpyee_details["tax_type"]

                    component_processor = ComponentProcessor(
                        employee.id, employee.gross_pay, 0, False, employee_benefits, employee_compoents
                    )
                    processed_salary = component_processor.generate_salary_response()

                    salary_components = [(component.salary_component.id,component.salary_component.component_name.lower()) for component in employee.employee_components]
                    pension_id = next((comp_id for comp_id, name in salary_components if "pension" in name), None)
                    nhf_id = next((comp_id for comp_id, name in salary_components if "nhf" in name), None)

                    pension_data = SalaryComponentsService().getSalaryComponents(pension_id)
                    pension_object = SalaryComponentSchema().dump(pension_data)

                    nhf_data = SalaryComponentsService().getSalaryComponents(nhf_id)
                    nhf_object = SalaryComponentSchema().dump(nhf_data)

                    if pension_object:
                        processed_pension = component_processor._create_component_data(pension_object)
                        annual_pension = float(processed_pension["total_monthly_calculation"].replace(",", "")) * 12
                        # print(f"processed_pension {processed_pension}")
                        # print(f"annual_pension {annual_pension}")

                    if nhf_object:
                        processed_nhf = component_processor._create_component_data(nhf_object)
                        annual_nhf = float(processed_nhf["total_monthly_calculation"].replace(",", "")) * 12
                        # print(f"processed_nhf {processed_nhf}")
                        # print(f"annual_nhf {annual_nhf}")

                    monthly_earnings = float(processed_salary["total_earnings"].replace(",",""))
                    annual_earnings = monthly_earnings * 12
                    total_non_taxable_monthly = processed_salary["total_non_taxable"]
                    total_non_taxable_annual = total_non_taxable_monthly * 12
                    total_monthly_statutory = processed_salary["total_statutory"]
                    total_annual_statutory = total_monthly_statutory * 12
                    total_monthly_other_deduction = processed_salary["total_other_deduction"] 
                    total_annual_other_deduction = total_monthly_other_deduction * 12
                    total_deduction = float(processed_salary["total_deduction"].replace(",",""))

                    calculate_tax = TaxCalculatorService("Nigeria", tax_type)
                    annual_tax = round(calculate_tax.calculate_annual_tax(annual_earnings, annual_pension, annual_nhf), 2)
                    monthly_tax = round(calculate_tax.calculate_monthly_tax(annual_tax),2)
                    all_deductions = total_deduction + monthly_tax
                    net_pay = round(monthly_earnings - (monthly_tax)) + total_non_taxable_monthly - total_monthly_statutory - total_monthly_other_deduction
                    # print(f"Gross pay :", employee.gross_pay)
                    # print(f"Total Monthly Earnings :", monthly_earnings)
                    # print(f"Total Annual Earnings :", annual_earnings)
                    # print(f"Annual Tax :", annual_tax)
                    # print(f"Tax type:", tax_type)
                    # print(f"Monthly Tax :", monthly_tax)
                    # print(f"Total statutory Deductions :", total_deduction)
                    # print(f"total_monthly_statutory :", total_monthly_statutory)
                    # print(f"total_annual_statutory :", total_annual_statutory)
                    # print("total_non_taxable_monthly:", total_non_taxable_monthly)
                    # print("total_non_taxable_annual:", total_non_taxable_annual)
                    # print(f"total_monthly_other_deduction :", total_monthly_other_deduction)
                    # print(f"total_annual_other_deduction :", total_annual_other_deduction)
                    # print(f"Net pay :", net_pay)
                    employee_record = {
                        "monthly_tax": monthly_tax,
                        "annual_tax": round(annual_tax, 2),
                        "total_taxable_monthly_sum": monthly_earnings,
                        "total_taxable_annual_sum": annual_earnings,
                        "total_non_taxable_monthly_sum": total_non_taxable_monthly,
                        "total_non_taxable_annual_sum": total_non_taxable_annual,
                        "total_statutory_monthly_sum": total_monthly_statutory,
                        "total_statutory_annual_sum": total_annual_statutory,
                        "total_other_deductions_monthly_sum": total_monthly_other_deduction,
                        "total_other_deductions_annual_sum": total_annual_other_deduction,
                        "netpay": net_pay
                    }
                    employee_service.updateEmployee(employee.id, employee_record)

                assigned_components.append({
                    "id": assigned_component.id,
                    "salary_component_id": assigned_component.salary_component_id,
                    "employee_id": employee.id
                })
        
        return jsonify({
            "message": "Salary component assigned successfully",
            "data": assigned_components
        }), 201
