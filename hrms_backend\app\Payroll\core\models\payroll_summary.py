
from core.databases.database import db
from core.models.basemodel import ModelBase
from sqlalchemy import Column, Integer, Float, String, ForeignKey
from sqlalchemy.orm import relationship
from core.models.employees import EmployeeModel

class PayrollSummary(ModelBase):
    __tablename__ = 'payrollsummary'

    id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    employee_id = db.Column(db.Integer, db.ForeignKey((ModelBase.dbSchema() + '.employees.id')), nullable=False)  # Reference to 'employees.id'
    gross_pay = Column(Float, nullable=False)
    tax_type = Column(String, nullable=False)
    monthly_tax = Column(Float, nullable=False)
    annual_tax = Column(Float, nullable=False)
    cra = Column(Float, nullable=False)
    taxable_income = Column(Float, nullable=False)
    statutory_deductions = Column(Float, nullable=False)
    pay_after_tax_deduction = Column(Float, nullable=False)
    net_pay_sum = Column(Float, nullable=False)
    total_earnings_after_tax_and_other_deductions = Column(Float, nullable=False)
    