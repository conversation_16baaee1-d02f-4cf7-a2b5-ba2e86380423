# =============================================================================
# HRMS Backend Environment Configuration
# =============================================================================
# Copy this file to .env and update the values according to your environment

# =============================================================================
# DATABASE CONFIGURATION (PostgreSQL)
# =============================================================================
# Option 1: Use complete DATABASE_URL (recommended for production)
DATABASE_URL=postgresql://postgres:admin@localhost:5432/agnoconnect_hrms

# Option 2: Use individual database settings (will be combined automatically)
DB_HOST=localhost
DB_PORT=5432
DB_USER=postgres
DB_PASSWORD=admin
DB_NAME=agnoconnect_hrms
DB_SCHEMA=public

# Database Connection Pool Settings
DB_POOL_SIZE=20
DB_MAX_OVERFLOW=30
DB_POOL_TIMEOUT=30
DB_POOL_RECYCLE=3600
DB_ECHO=false

# =============================================================================
# REDIS CONFIGURATION
# =============================================================================
REDIS_URL=redis://localhost:6379/0

# =============================================================================
# CELERY CONFIGURATION (Background Tasks)
# =============================================================================
CELERY_BROKER_URL=redis://localhost:6379/1
CELERY_RESULT_BACKEND=redis://localhost:6379/2

# =============================================================================
# JWT & AUTHENTICATION
# =============================================================================
SECRET_KEY=your-super-secret-jwt-key-change-this-in-production-make-it-long-and-random
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30
REFRESH_TOKEN_EXPIRE_DAYS=7

# =============================================================================
# CORS SETTINGS
# =============================================================================
ALLOWED_ORIGINS=["http://localhost:3000","http://localhost:5173","http://localhost:5174","http://localhost:8080"]

# =============================================================================
# FILE STORAGE
# =============================================================================
UPLOAD_DIR=uploads
MAX_FILE_SIZE=10485760  # 10MB in bytes

# =============================================================================
# CLOUD STORAGE (Optional)
# =============================================================================
# AWS S3 Configuration
AWS_ACCESS_KEY_ID=your_aws_access_key
AWS_SECRET_ACCESS_KEY=your_aws_secret_key
AWS_REGION=us-east-1
S3_BUCKET_NAME=your-hrms-bucket

# =============================================================================
# EMAIL CONFIGURATION
# =============================================================================
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your-app-password
SMTP_TLS=true
SMTP_SSL=false
FROM_EMAIL=<EMAIL>
FROM_NAME=HRMS System

# =============================================================================
# SMS CONFIGURATION (for 2FA)
# =============================================================================
# Twilio Configuration
TWILIO_ACCOUNT_SID=your_twilio_account_sid
TWILIO_AUTH_TOKEN=your_twilio_auth_token
TWILIO_PHONE_NUMBER=+**********

# =============================================================================
# EXTERNAL SERVICES
# =============================================================================
# Auth Service URL (if using external auth service)
AUTH_SERVICE_URL=http://localhost:8081

# =============================================================================
# APPLICATION SETTINGS
# =============================================================================
APP_NAME=AgnoConnect HRMS
APP_VERSION=1.0.0
DEBUG=true
DEFAULT_TIMEZONE=UTC

# =============================================================================
# SECURITY SETTINGS
# =============================================================================
# AES Encryption Key (32 bytes, base64 encoded)
AES_ENCRYPTION_KEY=your-32-byte-base64-encoded-encryption-key

# Password Policy
MIN_PASSWORD_LENGTH=8
REQUIRE_UPPERCASE=true
REQUIRE_LOWERCASE=true
REQUIRE_NUMBERS=true
REQUIRE_SPECIAL_CHARS=true

# Account Lockout Settings
MAX_LOGIN_ATTEMPTS=5
LOCKOUT_DURATION_MINUTES=30

# =============================================================================
# PAGINATION SETTINGS
# =============================================================================
DEFAULT_PAGE_SIZE=20
MAX_PAGE_SIZE=100

# =============================================================================
# WEBSOCKET SETTINGS
# =============================================================================
WS_HEARTBEAT_INTERVAL=30

# =============================================================================
# FEATURE FLAGS
# =============================================================================
FEATURE_RECRUITMENT_ENABLED=true
FEATURE_LMS_ENABLED=true
FEATURE_ONBOARDING_ENABLED=true
FEATURE_PERFORMANCE_ENABLED=true
FEATURE_PAYROLL_ENABLED=true
FEATURE_ANALYTICS_ENABLED=true
FEATURE_2FA_ENABLED=true
FEATURE_AUDIT_LOGS_ENABLED=true

# =============================================================================
# COMPLIANCE SETTINGS
# =============================================================================
# GDPR Compliance
GDPR_ENABLED=true
DATA_RETENTION_DAYS=2555  # 7 years
ANONYMIZATION_ENABLED=true

# Audit Settings
AUDIT_LOG_RETENTION_DAYS=2555  # 7 years
AUDIT_LOG_LEVEL=INFO
