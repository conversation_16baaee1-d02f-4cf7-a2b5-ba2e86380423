from core.models.setting_integrations import SettingIntegrationsModel
from core.databases.database import db

class SettingIntegrationsRepository:
    def __init__(self) -> None:
        pass

    @classmethod
    def createSettingIntegrations(self, name, client_id, client_secrete, refresh_token, org_id):
        setting_integrations = SettingIntegrationsModel(
            name=name,
            client_id=client_id,
            client_secrete=client_secrete,
            refresh_token=refresh_token,
            org_id=org_id
        )
        db.session.add(setting_integrations)
        db.session.commit()
        return setting_integrations

    @classmethod
    def getSettingIntegrations(self, id):
        return SettingIntegrationsModel.query.filter(SettingIntegrationsModel.id == id).first()
    
    @classmethod
    def getSettingIntegrationsByKeys(self, kwargs):
        return SettingIntegrationsModel.query.filter_by(**kwargs).all()

    @classmethod
    def updateSettingIntegrations(self, id, **kwargs):
        setting_integrations = SettingIntegrationsModel.query.filter_by(id=id).first()
        if setting_integrations:
            for key, value in kwargs.items():
                setattr(setting_integrations, key, value)
            db.session.commit()
            return setting_integrations
        else:
            return None

    @classmethod
    def deleteSettingIntegrations(self, id):
        return SettingIntegrationsModel.query.filter(SettingIntegrationsModel.id == id).delete()
        