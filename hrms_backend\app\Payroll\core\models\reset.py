from core.databases.database import db
from core.models.basemodel import ModelBase
import os

class PasswordResetToken(ModelBase):
    __tablename__ = "password_reset_tokens"

    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey((ModelBase.dbSchema() + '.users.id')), nullable=False)
    token = db.Column(db.String(128), nullable=False, unique=True)
    expires_at = db.Column(db.DateTime, nullable=False)

    user = db.relationship('UserModel', backref=db.backref('password_reset_tokens', lazy=True))

    
