from core.repositories.paystack_integration import PaystackIntegrationRepository

class PaystackIntegrationService:
    def __init__(self) -> None:
        self.repository = PaystackIntegrationRepository()

    # Create or add a new Paystack Integration record
    def createIntegration(self, **Kwargs):
        return self.repository.createIntegration(**Kwargs)

    # Get a Paystack Integration record by ID
    def getIntegrationById(self, id):
        return self.repository.getIntegration(id)

    # Update a Paystack Integration record by ID
    def updateIntegration(self, id, **Kwargs):
        return self.repository.updateIntegration(id, **Kwargs)

    # Get Paystack Integration by specific key (e.g., by id or other attributes)
    def getIntegrationByKey(self, Kwarg):
        return self.repository.getIntegrationByKeys(Kwarg)

    # Delete a Paystack Integration record by ID
    def deleteIntegration(self, id):
        return self.repository.deleteIntegration(id)



