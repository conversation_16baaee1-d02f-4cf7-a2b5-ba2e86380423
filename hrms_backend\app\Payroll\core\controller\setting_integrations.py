from flask import url_for
from flask.views import <PERSON>View
from flask_smorest import Blueprint, abort
from core.middleware import roles_required
from schemas import SettingIntegrationSchema
from sqlalchemy.orm.exc import NoResultFound
from sqlalchemy.exc import SQLAlchemyError, IntegrityError
import core.utils.response_message as RESPONSEMESSAGE
from core.services.setting_integrations import SettingIntegrationsService

blueprint = Blueprint("Setting Integration", __name__, description="Operations for Setting Integration")
    
@blueprint.route("/setting_integrations/<id>")
class SettingIntegrationList(MethodView):
    @roles_required(['admin'])
    @blueprint.response(200, SettingIntegrationSchema)
    def get(self, id):
        service = SettingIntegrationsService()
        setting = service.getSettingIntegrations(id)
        if not setting:
            abort(401, message="Setting Integration does not exist")
        return setting    
    
    @roles_required(['admin'])
    def delete(self, id):
        service = SettingIntegrationsService()
        setting = service.getSettingIntegrations(id)
        if not setting:
            abort(404, message="Setting Integration does not exist")
        service.deleteSettingIntegrations(id)
        return {"message" : "Setting Integration deleted"}
    
    @roles_required(['admin'])
    @blueprint.arguments(SettingIntegrationSchema)
    @blueprint.response(201, SettingIntegrationSchema)
    def put(self, id, data):
        service = SettingIntegrationsService()
        setting = service.getSettingIntegrations(id)
        if not setting:
            abort(404, message="Setting Integration does not exist")
        try :
            new_setting = service.updateSettingIntegrations(id, data)
            return new_setting
        except SQLAlchemyError:
                abort(500, message="Error while updating Setting Integration")
    
@blueprint.route("/setting_integrations")
class SettingIntegration(MethodView):
    @roles_required(['admin'])
    @blueprint.arguments(SettingIntegrationSchema)
    @blueprint.response(200, SettingIntegrationSchema)
    def post(self, data):
        try:
            service = SettingIntegrationsService()
            setting = service.getSettingIntegrationsByKey({"id": data['id']})
            if not setting:
                new_setting = service.createSettingIntegrations(data)
            else:
                abort(400, message="Setting Integration already exist")
        except IntegrityError:
            abort(500, message="Error while creating Setting Integration")
        except SQLAlchemyError as e:
            print(e)
            abort(500, message="Error while creating Setting Integration")
        return new_setting