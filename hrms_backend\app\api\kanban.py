from fastapi import APIRouter, HTTPException, status, Depends, Query
from sqlalchemy.orm import Session
from typing import List, Optional
from uuid import UUID

from ..core.security import get_current_user, require_permission, CurrentUser
from ..core.rbac import Permissions
from ..db.session import get_db
from ..schemas.kanban import (
    KanbanBoardCreate, KanbanBoardUpdate, KanbanBoardResponse,
    KanbanBoardListResponse, KanbanBoardFullResponse,
    KanbanColumnCreate, KanbanColumnUpdate, KanbanColumnResponse,
    KanbanCardCreate, KanbanCardUpdate, KanbanCardResponse,
    KanbanCardListResponse, CardMoveRequest, KanbanCardCommentCreate,
    KanbanCardCommentUpdate, KanbanCardCommentResponse,
    KanbanBoardMemberCreate, KanbanBoardMemberUpdate, KanbanBoardMemberResponse,
    BulkCardUpdate, BulkCardMove, BoardType
)
from ..services.project_management.kanban_service import KanbanService

router = APIRouter()
kanban_service = KanbanService()

# Root endpoint
@router.get("/")
async def get_kanban_overview(
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.KANBAN_READ))
):
    """Get kanban overview"""
    try:
        return {
            "message": "Kanban board management system",
            "endpoints": [
                "/boards - Get kanban boards",
                "/boards/{board_id} - Get specific board",
                "/cards - Get cards",
                "/columns - Get columns"
            ]
        }
    except Exception as e:
        return {"error": str(e)}

# Board endpoints
@router.get("/boards", response_model=KanbanBoardListResponse)
async def get_boards(
    board_type: Optional[BoardType] = Query(None),
    project_id: Optional[UUID] = Query(None),
    search: Optional[str] = Query(None),
    skip: int = Query(0, ge=0),
    limit: int = Query(20, ge=1, le=100),
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.KANBAN_READ))
):
    """Get Kanban boards with filtering"""
    return await kanban_service.get_boards(
        db=db,
        board_type=board_type,
        project_id=project_id,
        search=search,
        skip=skip,
        limit=limit,
        current_user=current_user
    )


@router.post("/boards", response_model=KanbanBoardResponse)
async def create_board(
    board_data: KanbanBoardCreate,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.KANBAN_CREATE))
):
    """Create new Kanban board"""
    return await kanban_service.create_board(db, board_data, current_user)


@router.get("/boards/{board_id}", response_model=KanbanBoardFullResponse)
async def get_board(
    board_id: UUID,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.KANBAN_READ))
):
    """Get Kanban board with full data"""
    board = await kanban_service.get_board_with_data(db, board_id, current_user)
    if not board:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Board not found or access denied"
        )
    return board


@router.put("/boards/{board_id}", response_model=KanbanBoardResponse)
async def update_board(
    board_id: UUID,
    board_data: KanbanBoardUpdate,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.KANBAN_UPDATE))
):
    """Update Kanban board"""
    board = await kanban_service.update_board(db, board_id, board_data, current_user)
    if not board:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Board not found or access denied"
        )
    return board


@router.delete("/boards/{board_id}")
async def delete_board(
    board_id: UUID,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.KANBAN_DELETE))
):
    """Delete Kanban board"""
    success = await kanban_service.delete_board(db, board_id, current_user)
    if not success:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Board not found or access denied"
        )
    return {"message": "Board deleted successfully"}


# Card endpoints
@router.post("/boards/{board_id}/cards", response_model=KanbanCardResponse)
async def create_card(
    board_id: UUID,
    card_data: KanbanCardCreate,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.KANBAN_CREATE))
):
    """Create new card in board"""
    return await kanban_service.create_card(db, board_id, card_data, current_user)


@router.put("/boards/{board_id}/cards/move", response_model=KanbanCardResponse)
async def move_card(
    board_id: UUID,
    move_request: CardMoveRequest,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.KANBAN_UPDATE))
):
    """Move card to different column/position"""
    return await kanban_service.move_card(db, board_id, move_request, current_user)


@router.put("/cards/{card_id}", response_model=KanbanCardResponse)
async def update_card(
    card_id: UUID,
    card_data: KanbanCardUpdate,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.KANBAN_UPDATE))
):
    """Update Kanban card"""
    card = await kanban_service.update_card(db, card_id, card_data, current_user)
    if not card:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Card not found or access denied"
        )
    return card


@router.delete("/cards/{card_id}")
async def delete_card(
    card_id: UUID,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.KANBAN_DELETE))
):
    """Delete Kanban card"""
    success = await kanban_service.delete_card(db, card_id, current_user)
    if not success:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Card not found or access denied"
        )
    return {"message": "Card deleted successfully"}


# Bulk operations
@router.put("/boards/{board_id}/cards/bulk-move")
async def bulk_move_cards(
    board_id: UUID,
    bulk_move: BulkCardMove,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.KANBAN_UPDATE))
):
    """Bulk move cards"""
    results = []
    for move_request in bulk_move.moves:
        try:
            card = await kanban_service.move_card(db, board_id, move_request, current_user)
            results.append({"card_id": move_request.card_id, "success": True, "card": card})
        except Exception as e:
            results.append({"card_id": move_request.card_id, "success": False, "error": str(e)})

    return {"results": results}


@router.put("/boards/{board_id}/cards/bulk-update")
async def bulk_update_cards(
    board_id: UUID,
    bulk_update: BulkCardUpdate,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.KANBAN_UPDATE))
):
    """Bulk update cards"""
    return await kanban_service.bulk_update_cards(db, board_id, bulk_update, current_user)
