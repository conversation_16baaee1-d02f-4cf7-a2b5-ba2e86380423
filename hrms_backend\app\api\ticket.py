from fastapi import APIRouter, HTTPException, status, Depends, Query
from sqlalchemy.orm import Session
from typing import List, Optional
from uuid import UUID
from datetime import datetime

from ..core.security import get_current_user, require_permission, CurrentUser
from ..core.rbac import Permissions
from ..db.session import get_db
from ..schemas.ticket import (
    TicketCreate, TicketUpdate, TicketResponse, TicketListResponse,
    TicketCommentCreate, TicketCommentUpdate, TicketCommentResponse,
    TicketAssignmentRequest, TicketResolutionRequest,
    TicketEscalationCreate, TicketEscalationResponse,
    TicketStatus, TicketPriority, TicketType
)
from ..services.ticket_management.ticket_service import TicketService
from ..services.ticket_management.workflow_service import TicketWorkflowService
from ..services.ticket_management.sla_service import TicketSLAService
from ..services.ticket_management.escalation_service import TicketEscalationService
from ..services.ticket_management.template_service import TicketTemplateService
from ..services.ticket_management.reporting_service import TicketReportingService
from ..services.ticket_management.ai_categorization_service import AICategorization
from ..services.ticket_management.smart_routing_service import SmartRoutingEngine
from ..services.ticket_management.sentiment_analysis_service import SentimentAnalysisService
from ..services.ticket_management.multichannel_service import MultiChannelService
from ..services.ticket_management.realtime_notification_service import RealtimeNotificationService

router = APIRouter()
ticket_service = TicketService()
workflow_service = TicketWorkflowService()
sla_service = TicketSLAService()
escalation_service = TicketEscalationService()
template_service = TicketTemplateService()
reporting_service = TicketReportingService()
ai_categorization = AICategorization()
smart_routing = SmartRoutingEngine()
sentiment_analysis = SentimentAnalysisService()
multichannel_service = MultiChannelService()
notification_service = RealtimeNotificationService()


# Ticket endpoints
@router.get("/")
async def get_tickets(
    status: Optional[TicketStatus] = Query(None),
    priority: Optional[TicketPriority] = Query(None),
    ticket_type: Optional[TicketType] = Query(None),
    assigned_to: Optional[UUID] = Query(None),
    requester_id: Optional[UUID] = Query(None),
    search: Optional[str] = Query(None),
    skip: int = Query(0, ge=0),
    limit: int = Query(20, ge=1, le=100),
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.TICKET_READ))
):
    """Get tickets with filtering"""
    try:
        return await ticket_service.get_tickets(
            db=db,
            status=status,
            priority=priority,
            ticket_type=ticket_type,
            assigned_to=assigned_to,
            requester_id=requester_id,
            search=search,
            skip=skip,
            limit=limit,
            current_user=current_user
        )
    except Exception as e:
        # Fallback to simple response if service fails
        return {
            "tickets": [],
            "total": 0,
            "skip": skip,
            "limit": limit,
            "message": "Ticket service temporarily unavailable"
        }


@router.post("/", response_model=TicketResponse)
async def create_ticket(
    ticket_data: TicketCreate,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.TICKET_CREATE))
):
    """Create new ticket"""
    return await ticket_service.create_ticket(db, ticket_data, current_user)


@router.get("/{ticket_id}", response_model=TicketResponse)
async def get_ticket(
    ticket_id: UUID,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.TICKET_READ))
):
    """Get ticket by ID"""
    tickets = await ticket_service.get_tickets(
        db=db,
        skip=0,
        limit=1,
        current_user=current_user
    )

    # Find the specific ticket
    ticket = next((t for t in tickets.tickets if t.id == ticket_id), None)
    if not ticket:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Ticket not found"
        )

    return ticket


@router.put("/{ticket_id}", response_model=TicketResponse)
async def update_ticket(
    ticket_id: UUID,
    ticket_data: TicketUpdate,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.TICKET_UPDATE))
):
    """Update ticket"""
    ticket = await ticket_service.update_ticket(db, ticket_id, ticket_data, current_user)
    if not ticket:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Ticket not found"
        )
    return ticket


@router.put("/{ticket_id}/assign")
async def assign_ticket(
    ticket_id: UUID,
    assignment_data: TicketAssignmentRequest,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.TICKET_ASSIGN))
):
    """Assign ticket to agent"""
    # For single ticket assignment
    assignment_data.ticket_ids = [ticket_id]
    return await ticket_service.assign_tickets(db, assignment_data, current_user)


@router.put("/{ticket_id}/resolve", response_model=TicketResponse)
async def resolve_ticket(
    ticket_id: UUID,
    resolution_data: TicketResolutionRequest,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.TICKET_RESOLVE))
):
    """Resolve ticket"""
    ticket = await ticket_service.resolve_ticket(db, ticket_id, resolution_data, current_user)
    if not ticket:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Ticket not found"
        )
    return ticket


@router.put("/{ticket_id}/close", response_model=TicketResponse)
async def close_ticket(
    ticket_id: UUID,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.TICKET_RESOLVE))
):
    """Close ticket"""
    ticket_update = TicketUpdate(status=TicketStatus.CLOSED)
    ticket = await ticket_service.update_ticket(db, ticket_id, ticket_update, current_user)
    if not ticket:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Ticket not found"
        )
    return ticket


# My tickets endpoints (for employees to view their own tickets)
@router.get("/my/tickets", response_model=TicketListResponse)
async def get_my_tickets(
    status: Optional[TicketStatus] = Query(None),
    skip: int = Query(0, ge=0),
    limit: int = Query(20, ge=1, le=100),
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(get_current_user)
):
    """Get my tickets"""
    return await ticket_service.get_tickets(
        db=db,
        requester_id=UUID(current_user.user_id),
        status=status,
        skip=skip,
        limit=limit,
        current_user=current_user
    )


@router.get("/assigned/tickets", response_model=TicketListResponse)
async def get_assigned_tickets(
    status: Optional[TicketStatus] = Query(None),
    skip: int = Query(0, ge=0),
    limit: int = Query(20, ge=1, le=100),
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(get_current_user)
):
    """Get tickets assigned to me"""
    return await ticket_service.get_tickets(
        db=db,
        assigned_to=UUID(current_user.user_id),
        status=status,
        skip=skip,
        limit=limit,
        current_user=current_user
    )


# Ticket Comments endpoints
@router.post("/{ticket_id}/comments")
async def add_ticket_comment(
    ticket_id: UUID,
    comment_data: TicketCommentCreate,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.TICKET_UPDATE))
):
    """Add comment to ticket"""
    return await ticket_service.add_comment(db, ticket_id, comment_data, current_user)


@router.get("/{ticket_id}/comments")
async def get_ticket_comments(
    ticket_id: UUID,
    skip: int = Query(0, ge=0),
    limit: int = Query(20, ge=1, le=100),
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.TICKET_READ))
):
    """Get ticket comments"""
    return await ticket_service.get_comments(db, ticket_id, skip, limit, current_user)


@router.put("/comments/{comment_id}")
async def update_ticket_comment(
    comment_id: UUID,
    comment_data: TicketCommentUpdate,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.TICKET_UPDATE))
):
    """Update ticket comment"""
    return await ticket_service.update_comment(db, comment_id, comment_data, current_user)


# Ticket Escalation endpoints
@router.post("/{ticket_id}/escalate")
async def escalate_ticket(
    ticket_id: UUID,
    escalation_data: TicketEscalationCreate,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.TICKET_ASSIGN))
):
    """Escalate ticket"""
    escalation_data_dict = escalation_data.dict()
    escalation_data_dict["ticket_id"] = ticket_id
    return await escalation_service.create_escalation(db, escalation_data_dict, current_user)


@router.get("/escalations")
async def get_escalations(
    ticket_id: Optional[UUID] = Query(None),
    escalated_to: Optional[UUID] = Query(None),
    is_active: Optional[bool] = Query(None),
    skip: int = Query(0, ge=0),
    limit: int = Query(20, ge=1, le=100),
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.TICKET_READ))
):
    """Get escalations"""
    return await escalation_service.get_escalations(
        db, ticket_id, escalated_to, is_active, current_user, skip, limit
    )


@router.put("/escalations/{escalation_id}/acknowledge")
async def acknowledge_escalation(
    escalation_id: UUID,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(get_current_user)
):
    """Acknowledge escalation"""
    return await escalation_service.acknowledge_escalation(db, escalation_id, current_user)


@router.put("/escalations/{escalation_id}/resolve")
async def resolve_escalation(
    escalation_id: UUID,
    resolution_notes: str,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(get_current_user)
):
    """Resolve escalation"""
    return await escalation_service.resolve_escalation(db, escalation_id, resolution_notes, current_user)


# Ticket Templates endpoints
@router.get("/templates")
async def get_ticket_templates(
    ticket_type: Optional[TicketType] = Query(None),
    category_id: Optional[UUID] = Query(None),
    is_public: Optional[bool] = Query(None),
    skip: int = Query(0, ge=0),
    limit: int = Query(20, ge=1, le=100),
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.TICKET_READ))
):
    """Get ticket templates"""
    return await template_service.get_templates(
        db, current_user, ticket_type, category_id, is_public, skip, limit
    )


@router.post("/templates")
async def create_ticket_template(
    template_data: dict,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.TICKET_CREATE))
):
    """Create ticket template"""
    return await template_service.create_template(db, template_data, current_user)


@router.get("/templates/{template_id}")
async def get_ticket_template(
    template_id: UUID,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.TICKET_READ))
):
    """Get specific ticket template"""
    return await template_service.get_template(db, template_id, current_user)


@router.put("/templates/{template_id}")
async def update_ticket_template(
    template_id: UUID,
    template_data: dict,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.TICKET_UPDATE))
):
    """Update ticket template"""
    return await template_service.update_template(db, template_id, template_data, current_user)


@router.delete("/templates/{template_id}")
async def delete_ticket_template(
    template_id: UUID,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.TICKET_DELETE))
):
    """Delete ticket template"""
    success = await template_service.delete_template(db, template_id, current_user)
    if not success:
        raise HTTPException(status_code=404, detail="Template not found")
    return {"message": "Template deleted successfully"}


@router.post("/templates/{template_id}/create-ticket")
async def create_ticket_from_template(
    template_id: UUID,
    form_data: dict,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.TICKET_CREATE))
):
    """Create ticket from template"""
    return await template_service.create_ticket_from_template(db, template_id, form_data, current_user)


@router.post("/templates/{template_id}/duplicate")
async def duplicate_ticket_template(
    template_id: UUID,
    new_name: str,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.TICKET_CREATE))
):
    """Duplicate ticket template"""
    return await template_service.duplicate_template(db, template_id, new_name, current_user)


# SLA Management endpoints
@router.get("/slas")
async def get_slas(
    is_active: Optional[bool] = Query(None),
    skip: int = Query(0, ge=0),
    limit: int = Query(20, ge=1, le=100),
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.ADMIN_SETTINGS))
):
    """Get SLA configurations"""
    return await sla_service.get_slas(db, current_user, is_active, skip, limit)


@router.post("/slas")
async def create_sla(
    sla_data: dict,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.ADMIN_SETTINGS))
):
    """Create SLA configuration"""
    return await sla_service.create_sla(db, sla_data, current_user)


@router.put("/slas/{sla_id}")
async def update_sla(
    sla_id: UUID,
    sla_data: dict,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.ADMIN_SETTINGS))
):
    """Update SLA configuration"""
    return await sla_service.update_sla(db, sla_id, sla_data, current_user)


@router.delete("/slas/{sla_id}")
async def delete_sla(
    sla_id: UUID,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.ADMIN_SETTINGS))
):
    """Delete SLA configuration"""
    success = await sla_service.delete_sla(db, sla_id, current_user)
    if not success:
        raise HTTPException(status_code=404, detail="SLA not found")
    return {"message": "SLA deleted successfully"}


# Workflow Management endpoints
@router.get("/workflows")
async def get_workflows(
    is_active: Optional[bool] = Query(None),
    skip: int = Query(0, ge=0),
    limit: int = Query(20, ge=1, le=100),
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.ADMIN_SETTINGS))
):
    """Get workflow configurations"""
    return await workflow_service.get_workflows(db, current_user, is_active, skip, limit)


@router.post("/workflows")
async def create_workflow(
    workflow_data: dict,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.ADMIN_SETTINGS))
):
    """Create workflow configuration"""
    return await workflow_service.create_workflow(db, workflow_data, current_user)


@router.put("/workflows/{workflow_id}")
async def update_workflow(
    workflow_id: UUID,
    workflow_data: dict,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.ADMIN_SETTINGS))
):
    """Update workflow configuration"""
    return await workflow_service.update_workflow(db, workflow_id, workflow_data, current_user)


@router.delete("/workflows/{workflow_id}")
async def delete_workflow(
    workflow_id: UUID,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.ADMIN_SETTINGS))
):
    """Delete workflow configuration"""
    success = await workflow_service.delete_workflow(db, workflow_id, current_user)
    if not success:
        raise HTTPException(status_code=404, detail="Workflow not found")
    return {"message": "Workflow deleted successfully"}


@router.post("/{ticket_id}/workflow/process")
async def process_workflow_step(
    ticket_id: UUID,
    step_data: dict,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.TICKET_UPDATE))
):
    """Process workflow step for ticket"""
    return await workflow_service.process_workflow_step(db, ticket_id, step_data, current_user)


# Reporting endpoints
@router.get("/reports/overview")
async def get_ticket_overview_report(
    start_date: Optional[datetime] = Query(None),
    end_date: Optional[datetime] = Query(None),
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.ADMIN_REPORTS))
):
    """Get ticket overview report"""
    return await reporting_service.get_ticket_overview(
        db, current_user.organization_id, start_date, end_date
    )


@router.get("/reports/trends")
async def get_ticket_trends_report(
    start_date: Optional[datetime] = Query(None),
    end_date: Optional[datetime] = Query(None),
    group_by: str = Query("day", regex="^(hour|day|week|month)$"),
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.ADMIN_REPORTS))
):
    """Get ticket trends report"""
    return await reporting_service.get_ticket_trends(
        db, current_user.organization_id, start_date, end_date, group_by
    )


@router.get("/reports/breakdown")
async def get_ticket_breakdown_report(
    start_date: Optional[datetime] = Query(None),
    end_date: Optional[datetime] = Query(None),
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.ADMIN_REPORTS))
):
    """Get ticket breakdown report"""
    return await reporting_service.get_ticket_breakdown(
        db, current_user.organization_id, start_date, end_date
    )


@router.get("/reports/agent-performance")
async def get_agent_performance_report(
    start_date: Optional[datetime] = Query(None),
    end_date: Optional[datetime] = Query(None),
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.ADMIN_REPORTS))
):
    """Get agent performance report"""
    return await reporting_service.get_agent_performance(
        db, current_user.organization_id, start_date, end_date
    )


@router.get("/reports/satisfaction")
async def get_satisfaction_report(
    start_date: Optional[datetime] = Query(None),
    end_date: Optional[datetime] = Query(None),
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.ADMIN_REPORTS))
):
    """Get satisfaction metrics report"""
    return await reporting_service.get_satisfaction_metrics(
        db, current_user.organization_id, start_date, end_date
    )


@router.get("/reports/escalations")
async def get_escalation_report(
    start_date: Optional[datetime] = Query(None),
    end_date: Optional[datetime] = Query(None),
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.ADMIN_REPORTS))
):
    """Get escalation report"""
    return await reporting_service.get_escalation_report(
        db, current_user.organization_id, start_date, end_date
    )


@router.get("/reports/sla-metrics")
async def get_sla_metrics_report(
    start_date: Optional[datetime] = Query(None),
    end_date: Optional[datetime] = Query(None),
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.ADMIN_REPORTS))
):
    """Get SLA metrics report"""
    return await sla_service.get_sla_metrics(
        db, current_user.organization_id, start_date, end_date
    )


@router.get("/reports/template-usage")
async def get_template_usage_report(
    start_date: Optional[datetime] = Query(None),
    end_date: Optional[datetime] = Query(None),
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.ADMIN_REPORTS))
):
    """Get template usage report"""
    return await template_service.get_template_usage_stats(
        db, current_user, start_date, end_date
    )


# Advanced Features endpoints
@router.post("/bulk-update")
async def bulk_update_tickets(
    ticket_ids: List[UUID],
    update_data: dict,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.TICKET_UPDATE))
):
    """Bulk update multiple tickets"""
    return await ticket_service.bulk_update_tickets(db, ticket_ids, update_data, current_user)


@router.post("/{ticket_id}/satisfaction")
async def submit_satisfaction_rating(
    ticket_id: UUID,
    rating: int = Query(..., ge=1, le=5),
    feedback: Optional[str] = None,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(get_current_user)
):
    """Submit satisfaction rating for ticket"""
    return await ticket_service.submit_satisfaction_rating(db, ticket_id, rating, feedback, current_user)


@router.get("/knowledge-base/search")
async def search_knowledge_base(
    query: str = Query(..., min_length=3),
    ticket_type: Optional[TicketType] = Query(None),
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(get_current_user)
):
    """Search knowledge base for relevant articles"""
    return await ticket_service.search_knowledge_base(db, query, ticket_type, current_user)


@router.get("/analytics")
async def get_ticket_analytics(
    start_date: Optional[datetime] = Query(None),
    end_date: Optional[datetime] = Query(None),
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.ADMIN_REPORTS))
):
    """Get advanced ticket analytics"""
    return await ticket_service.get_ticket_analytics(
        db, current_user.organization_id, start_date, end_date
    )


# Omnichannel Support endpoints
@router.post("/channels/email/webhook")
async def handle_email_webhook(
    email_data: dict,
    db: Session = Depends(get_db)
):
    """Handle incoming email tickets (webhook endpoint)"""
    # This would be implemented to handle email-to-ticket conversion
    # For now, return a placeholder response
    return {"message": "Email webhook received", "status": "processed"}


@router.post("/channels/chat/webhook")
async def handle_chat_webhook(
    chat_data: dict,
    db: Session = Depends(get_db)
):
    """Handle incoming chat tickets (webhook endpoint)"""
    # This would be implemented to handle chat-to-ticket conversion
    # For now, return a placeholder response
    return {"message": "Chat webhook received", "status": "processed"}


@router.get("/channels/stats")
async def get_channel_statistics(
    start_date: Optional[datetime] = Query(None),
    end_date: Optional[datetime] = Query(None),
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.ADMIN_REPORTS))
):
    """Get omnichannel statistics"""
    return await multichannel_service.get_channel_analytics(
        db, current_user.organization_id, start_date, end_date
    )


# AI-Powered Features endpoints
@router.post("/ai/categorize")
async def ai_categorize_ticket(
    title: str,
    description: str,
    current_category: Optional[str] = None,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(get_current_user)
):
    """AI-powered ticket categorization"""
    return await ai_categorization.categorize_ticket(title, description, current_category)


@router.post("/ai/analyze-sentiment")
async def analyze_ticket_sentiment(
    text: str,
    ticket_type: Optional[str] = None,
    priority: Optional[str] = None,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(get_current_user)
):
    """Analyze sentiment and urgency of ticket content"""
    return await sentiment_analysis.analyze_sentiment(text, ticket_type, priority)


@router.post("/ai/suggest-agent")
async def suggest_best_agent(
    ticket_id: UUID,
    exclude_agents: Optional[List[UUID]] = None,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.TICKET_ASSIGN))
):
    """AI-powered agent suggestion for ticket assignment"""
    ticket = db.query(Ticket).filter(Ticket.id == ticket_id).first()
    if not ticket:
        raise HTTPException(status_code=404, detail="Ticket not found")

    exclude_list = [str(agent_id) for agent_id in exclude_agents] if exclude_agents else None
    return await smart_routing.find_best_agent(
        db, ticket, current_user.organization_id, exclude_list
    )


@router.get("/ai/routing-analytics")
async def get_routing_analytics(
    start_date: Optional[datetime] = Query(None),
    end_date: Optional[datetime] = Query(None),
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.ADMIN_REPORTS))
):
    """Get AI routing analytics"""
    return await smart_routing.get_routing_analytics(
        db, current_user.organization_id, start_date, end_date
    )


@router.post("/ai/suggest-reassignment")
async def suggest_ticket_reassignment(
    ticket_id: UUID,
    reason: str = "performance",
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.TICKET_ASSIGN))
):
    """Suggest ticket reassignment using AI"""
    return await smart_routing.suggest_reassignment(db, str(ticket_id), reason)


@router.get("/ai/similar-tickets")
async def find_similar_tickets(
    text: str,
    limit: int = Query(5, ge=1, le=10),
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(get_current_user)
):
    """Find similar tickets using AI"""
    return await ai_categorization.suggest_similar_tickets(
        db, text, current_user.organization_id, limit
    )


@router.post("/ai/suggest-resolution")
async def suggest_auto_resolution(
    ticket_type: TicketType,
    description: str,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(get_current_user)
):
    """AI-powered resolution suggestion"""
    return await ai_categorization.auto_suggest_resolution(ticket_type, description)


@router.get("/ai/sentiment-trends")
async def get_sentiment_trends(
    days: int = Query(30, ge=1, le=365),
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.ADMIN_REPORTS))
):
    """Get sentiment trends over time"""
    return await sentiment_analysis.monitor_sentiment_trends(
        db, current_user.organization_id, days
    )


@router.post("/ai/response-tone")
async def suggest_response_tone(
    sentiment_analysis_data: dict,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(get_current_user)
):
    """Suggest appropriate response tone based on sentiment analysis"""
    return await sentiment_analysis.suggest_response_tone(sentiment_analysis_data)


# Multi-Channel Integration endpoints
@router.post("/channels/email/create")
async def create_ticket_from_email(
    email_data: dict,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.TICKET_CREATE))
):
    """Create ticket from email"""
    return await multichannel_service.create_ticket_from_email(
        db, email_data, current_user.organization_id
    )


@router.post("/channels/chatbot/create")
async def create_ticket_from_chatbot(
    chat_data: dict,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(get_current_user)
):
    """Create ticket from chatbot interaction"""
    return await multichannel_service.create_ticket_from_chatbot(db, chat_data, current_user)


@router.post("/channels/api/create")
async def create_ticket_from_external_api(
    api_data: dict,
    source_system: str = "external_api",
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(get_current_user)
):
    """Create ticket from external API"""
    return await multichannel_service.create_ticket_from_api(
        db, api_data, current_user, source_system
    )


@router.post("/channels/phone/create")
async def create_ticket_from_phone(
    phone_data: dict,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.TICKET_CREATE))
):
    """Create ticket from phone call"""
    return await multichannel_service.create_ticket_from_phone(
        db, phone_data, current_user.organization_id
    )


@router.get("/channels/analytics")
async def get_multichannel_analytics(
    start_date: Optional[datetime] = Query(None),
    end_date: Optional[datetime] = Query(None),
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.ADMIN_REPORTS))
):
    """Get multi-channel analytics"""
    return await multichannel_service.get_channel_analytics(
        db, current_user.organization_id, start_date, end_date
    )
