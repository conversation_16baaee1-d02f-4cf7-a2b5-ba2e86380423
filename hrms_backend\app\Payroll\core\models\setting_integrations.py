from core.databases.database import db
from core.models.basemodel import ModelBase
class SettingIntegrationsModel(ModelBase):
    __tablename__ = "setting_integrations"

    id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    name = db.Column(db.String(100), nullable=False)
    client_id = db.Column(db.String(100), nullable=False)
    client_secrete = db.Column(db.String(100), nullable=False)
    refresh_token = db.Column(db.String(100), nullable=False)
    org_id = db.Column(db.Integer, db.<PERSON>ey('organisations.id'), nullable=False)

    

    
    
    
      