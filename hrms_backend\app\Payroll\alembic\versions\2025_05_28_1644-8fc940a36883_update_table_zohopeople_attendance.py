"""update table zohopeople attendance

Revision ID: 8fc940a36883
Revises: 883eb9e88818
Create Date: 2025-05-28 16:44:03.943353

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy import Column, Integer, String, Float


# revision identifiers, used by Alembic.
revision: str = '8fc940a36883'
down_revision: Union[str, None] = '883eb9e88818'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None



def upgrade() -> None:
    op.drop_column('attendance_zoho_people_integration', 'employee_type')


def downgrade() -> None:
    op.add_column(
        'attendance_zoho_people_integration',
        op.Column('employee_type', String(45), nullable=False)
    )
