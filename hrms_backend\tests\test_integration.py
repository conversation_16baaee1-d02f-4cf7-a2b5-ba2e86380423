#!/usr/bin/env python3
"""
Integration test for HRMS Backend and Frontend
"""

import requests
import json
import time

def test_backend_health():
    """Test if backend is running and healthy"""
    try:
        response = requests.get("http://localhost:8085/health")
        if response.status_code == 200:
            print("✅ Backend health check passed")
            return True
        else:
            print(f"❌ Backend health check failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Backend health check error: {e}")
        return False

def test_frontend_health():
    """Test if frontend is running"""
    try:
        response = requests.get("http://localhost:5174/")
        if response.status_code == 200:
            print("✅ Frontend is accessible")
            return True
        else:
            print(f"❌ Frontend not accessible: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Frontend health check error: {e}")
        return False

def test_cors():
    """Test CORS configuration"""
    try:
        headers = {
            "Origin": "http://localhost:5174",
            "Content-Type": "application/json"
        }
        response = requests.options("http://localhost:8085/api/auth/login", headers=headers)
        if response.status_code in [200, 204]:
            print("✅ CORS configuration working")
            return True
        else:
            print(f"❌ CORS issue: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ CORS test error: {e}")
        return False

def test_authentication_flow():
    """Test complete authentication flow"""
    print("\n🔐 Testing Authentication Flow")
    print("-" * 40)
    
    # Test admin login
    admin_result = test_login("ADMIN001", "password123", "Admin")
    
    # Test employee login
    employee_result = test_login("EMP001", "password123", "Employee")
    
    # Test invalid login
    test_invalid_login()
    
    return admin_result and employee_result

def test_login(username, password, user_type):
    """Test login for a specific user"""
    url = "http://localhost:8085/api/auth/login"
    
    payload = {
        "email": username,
        "password": password
    }
    
    headers = {
        "Content-Type": "application/json",
        "Origin": "http://localhost:5174"
    }
    
    try:
        print(f"Testing {user_type} login ({username})...")
        response = requests.post(url, json=payload, headers=headers)
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ {user_type} login successful")
            print(f"   User: {data['user']['name']}")
            print(f"   Role: {data['user']['role']}")
            print(f"   Token: {data['access_token'][:50]}...")
            return True
        else:
            print(f"❌ {user_type} login failed: {response.status_code}")
            print(f"   Error: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ {user_type} login error: {e}")
        return False

def test_invalid_login():
    """Test login with invalid credentials"""
    url = "http://localhost:8085/api/auth/login"
    
    payload = {
        "email": "INVALID001",
        "password": "wrongpassword"
    }
    
    headers = {
        "Content-Type": "application/json",
        "Origin": "http://localhost:5174"
    }
    
    try:
        print("Testing invalid login...")
        response = requests.post(url, json=payload, headers=headers)
        
        if response.status_code == 401:
            print("✅ Invalid login correctly rejected")
        else:
            print(f"❌ Invalid login should return 401, got {response.status_code}")
            
    except Exception as e:
        print(f"❌ Invalid login test error: {e}")

def test_database_connection():
    """Test database connectivity through backend"""
    try:
        # Test by trying to login (which requires DB access)
        response = requests.post(
            "http://localhost:8085/api/auth/login",
            json={"email": "ADMIN001", "password": "test"},
            headers={"Content-Type": "application/json"}
        )
        # Any response (even 401) means DB is accessible
        print("✅ Database connection working")
        return True
    except Exception as e:
        print(f"❌ Database connection error: {e}")
        return False

def main():
    """Run all integration tests"""
    print("🧪 HRMS Integration Test Suite")
    print("=" * 50)
    
    # Test backend health
    backend_ok = test_backend_health()
    
    # Test frontend health
    frontend_ok = test_frontend_health()
    
    # Test database connection
    db_ok = test_database_connection()
    
    # Test CORS
    cors_ok = test_cors()
    
    # Test authentication flow
    auth_ok = test_authentication_flow()
    
    print("\n" + "=" * 50)
    print("🎯 Integration Test Results")
    print("=" * 50)
    
    results = {
        "Backend Health": backend_ok,
        "Frontend Health": frontend_ok,
        "Database Connection": db_ok,
        "CORS Configuration": cors_ok,
        "Authentication Flow": auth_ok
    }
    
    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name:<20}: {status}")
    
    all_passed = all(results.values())
    
    print("\n" + "=" * 50)
    if all_passed:
        print("🎉 ALL TESTS PASSED! Integration is working correctly.")
        print("\n📋 Next Steps:")
        print("1. Frontend is running at: http://localhost:5174/")
        print("2. Backend is running at: http://localhost:8085/")
        print("3. Test credentials:")
        print("   - Admin: ADMIN001 / password123")
        print("   - Employee: EMP001 / password123")
    else:
        print("❌ Some tests failed. Please check the issues above.")
    
    return all_passed

if __name__ == "__main__":
    main()
