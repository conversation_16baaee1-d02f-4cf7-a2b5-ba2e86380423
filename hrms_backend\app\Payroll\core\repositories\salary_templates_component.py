from core.models.salary_tempplate_component_pivot import SalaryTemplateComponentsPivotModel
from core.databases.database import db

class SalaryTemplatesComponentRepository:
    def __init__(self) -> None:
        pass

    @classmethod
    def create(self, template_id, salary_components_id ):
        salary_pivote = SalaryTemplateComponentsPivotModel(
            salary_template_id = template_id,
            salary_component_id = salary_components_id,
        )
        db.session.add(salary_pivote)
        db.session.commit()
        return salary_pivote
    
    @classmethod
    def getAttachedTemplate(self, id):
        return SalaryTemplateComponentsPivotModel.query.filter_by(salary_template_id=id).all()
        
    @classmethod
    def delete(self, ids):
        db.session.query(SalaryTemplateComponentsPivotModel) \
            .filter(SalaryTemplateComponentsPivotModel.id.in_(ids)) \
            .delete(synchronize_session=False)      
        db.session.commit()
        return 
    
    @classmethod
    def delete_components_by_template_id(self, template_id):
        db.session.query(SalaryTemplateComponentsPivotModel) \
            .filter(SalaryTemplateComponentsPivotModel.salary_template_id == template_id) \
            .delete(synchronize_session=False)
        db.session.commit()