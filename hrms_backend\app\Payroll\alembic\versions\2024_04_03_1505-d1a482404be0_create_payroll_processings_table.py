"""create payroll_processings table

Revision ID: d1a482404be0
Revises: 8f739b1313c9
Create Date: 2024-04-03 15:05:21.502186

"""
from typing import Sequence, Union

from alembic import op
from sqlalchemy import func, TIMESTAMP, ForeignKey
from sqlalchemy import Column, Integer, String, Float


# revision identifiers, used by Alembic.
revision: str = 'd1a482404be0'
down_revision: Union[str, None] = '8f739b1313c9'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    op.create_table(
        'payroll_processings',
        Column('id', Integer, primary_key=True),
        Column("month", TIMESTAMP, server_default=func.now()),
        Column("year", TIMESTAMP, server_default=func.now()),
        <PERSON>umn("organisation_id", Integer, ForeignKey("organisations.id")),
        <PERSON>umn("timestamp", TIMESTAMP, server_default=func.now()),

    )

def downgrade() -> None:
    op.drop_table("payroll_processings")