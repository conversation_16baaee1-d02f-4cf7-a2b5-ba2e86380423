"""alter 3 column on loan request table


Revision ID: 913d3235e48f
Revises: 95360fc752e7
Create Date: 2025-06-01 10:56:17.645374

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '913d3235e48f'
down_revision: Union[str, None] = '95360fc752e7'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    op.alter_column('loan_request', 'monthly_repayment',
                   existing_type=sa.Float(),
                   nullable=True)
    op.alter_column('loan_request', 'total_repayment',
                   existing_type=sa.Float(),
                   nullable=True)
    
    # 2. Change duration type (PostgreSQL version)
    op.execute('ALTER TABLE loan_request ALTER COLUMN duration TYPE INTEGER USING duration::integer')



def downgrade() -> None:
    op.execute('ALTER TABLE loan_request ALTER COLUMN duration TYPE VARCHAR(45) USING duration::text')
    
    # 2. Revert nullable status
    op.alter_column('loan_request', 'monthly_repayment',
                   existing_type=sa.Float(),
                   nullable=False)
    op.alter_column('loan_request', 'total_repayment',
                   existing_type=sa.Float(),
                   nullable=False)
