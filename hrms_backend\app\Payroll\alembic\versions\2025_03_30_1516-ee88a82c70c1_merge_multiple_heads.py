"""Merge multiple heads

Revision ID: ee88a82c70c1
Revises: 4e3f26579fab, 3d99497126f7
Create Date: 2025-03-30 15:16:34.993535

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'ee88a82c70c1'
down_revision: Union[str, None] = ('4e3f26579fab', '3d99497126f7')
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    pass


def downgrade() -> None:
    pass
