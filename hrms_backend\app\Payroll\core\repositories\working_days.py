from core.models.working_days import WorkingDaysModel
from core.databases.database import db

class WorkingDaysRepository:
    def __init__(self) -> None:
        pass

    @classmethod
    def createWorkingDays(self, employee_id, month, year, total_working_days, present_days, absent_days):
        working_days = WorkingDaysModel(
            employee_id=employee_id,
            month=month,
            year=year,
            total_working_days=total_working_days,
            present_days=present_days,
            absent_days=absent_days
        )
        db.session.add(working_days)
        db.session.commit()
        return working_days

    @classmethod
    def getWorkingDays(self, id):
        return WorkingDaysModel.query.filter(WorkingDaysModel.id == id).first()
    
    @classmethod
    def getWorkingDaysByKeys(self, kwargs):
        return WorkingDaysModel.query.filter_by(**kwargs).all()

    @classmethod
    def updateWorkingDays(self, id, **kwargs):
        working_days = WorkingDaysModel.query.filter_by(id=id).first()
        if working_days:
            for key, value in kwargs.items():
                setattr(working_days, key, value)
            db.session.commit()
            return working_days
        else:
            return None

    @classmethod
    def deleteWorkingDays(self, id):
        return WorkingDaysModel.query.filter(WorkingDaysModel.id == id).delete()
        