# PAYROLL DOCUMENTATION
This documentation provides an overview of the key components and architecture of the Payroll application, which follows a client-server model to ensure secure and efficient communication between the frontend and backend services.

### Client-Server Model
The Payroll application is built on a client-server model where the frontend acts as the client, sending HTTP/HTTPS requests to the backend server. The server processes these requests, performs necessary operations (e.g., database queries, business logic execution), and sends the appropriate response back to the client.

## Key Components
### Service Layer: APIRequest Class
The APIRequest class serves as the service layer within the frontend application. It abstracts the complexity of making HTTP requests, providing a simple interface to send requests to the backend. This class is responsible for:

- Constructing the request URLs based on the provided endpoint.
- Setting the required HTTP headers, 
- including the authorization token.
- Managing the response from the backend, 
- including handling errors.
```
Location: services/api_request.py
```

### Session Management: Cookies Class
The Cookies class is responsible for managing session state in the frontend application. 
It handles:

- Creation and management of session cookies, including storing and retrieving access tokens.
- Verifying the presence of a valid session cookie before allowing authenticated requests.
- Destroying session cookies upon logout or session expiration.
```
Location: services/cookies.py
```

### Middleware & Authorization: authorize Decorator
To ensure that only authenticated users can access certain routes, the authorize decorator is used as middleware. This decorator checks for the presence of a valid session token before allowing the request to proceed. If the token is missing or invalid, the user is redirected to the login page.
```
Location: middleware/user.py
```
### Environment Configuration: .env File
Sensitive configurations, such as the HOST_NAME, are managed through environment variables stored in the .env file. This approach helps keep the application's settings secure and allows for easy configuration changes without modifying the codebase.

```
Location: .env
```