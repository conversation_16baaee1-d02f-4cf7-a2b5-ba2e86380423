from sqlalchemy import Column, String, DateTime, Date, Foreign<PERSON>ey, Boolean, Text, Numeric, Integer, Enum
from sqlalchemy.dialects.postgresql import UUID, JSONB
from sqlalchemy.orm import relationship
from enum import Enum as PyEnum
from datetime import datetime, date
from typing import Optional

from ..base import BaseModel, AuditMixin


class PayrollStatus(PyEnum):
    DRAFT = "draft"
    CALCULATED = "calculated"
    APPROVED = "approved"
    PROCESSED = "processed"
    PAID = "paid"
    CANCELLED = "cancelled"


class PayrollFrequency(PyEnum):
    WEEKLY = "weekly"
    BIWEEKLY = "biweekly"
    MONTHLY = "monthly"
    QUARTERLY = "quarterly"
    ANNUALLY = "annually"


class ComponentType(PyEnum):
    EARNING = "earning"
    DEDUCTION = "deduction"
    BENEFIT = "benefit"
    TAX = "tax"
    CONTRIBUTION = "contribution"


class PayrollPeriod(BaseModel):
    """Payroll period configuration"""
    __tablename__ = "payroll_periods"

    organization_id = Column(UUID(as_uuid=True), nullable=False, index=True)
    name = Column(String(200), nullable=False)

    # Period details
    start_date = Column(Date, nullable=False)
    end_date = Column(Date, nullable=False)
    pay_date = Column(Date, nullable=False)

    # Frequency
    frequency = Column(Enum(PayrollFrequency), nullable=False)

    # Status
    is_locked = Column(Boolean, default=False)
    locked_at = Column(DateTime(timezone=True), nullable=True)
    locked_by = Column(UUID(as_uuid=True), ForeignKey("employees.id"), nullable=True)

    # Processing
    is_processed = Column(Boolean, default=False)
    processed_at = Column(DateTime(timezone=True), nullable=True)
    processed_by = Column(UUID(as_uuid=True), ForeignKey("employees.id"), nullable=True)

    # Relationships
    locker = relationship("Employee", foreign_keys=[locked_by])
    processor = relationship("Employee", foreign_keys=[processed_by])
    payroll_records = relationship("PayrollRecord", back_populates="period")


class PayrollRecord(BaseModel, AuditMixin):
    """Individual employee payroll record"""
    __tablename__ = "payroll_records"

    employee_id = Column(UUID(as_uuid=True), ForeignKey("employees.id"), nullable=False, index=True)
    period_id = Column(UUID(as_uuid=True), ForeignKey("payroll_periods.id"), nullable=False, index=True)

    # Basic salary information
    basic_salary = Column(Numeric(12, 2), nullable=False)
    gross_salary = Column(Numeric(12, 2), nullable=False)
    net_salary = Column(Numeric(12, 2), nullable=False)

    # Totals
    total_earnings = Column(Numeric(12, 2), nullable=False, default=0)
    total_deductions = Column(Numeric(12, 2), nullable=False, default=0)
    total_benefits = Column(Numeric(12, 2), nullable=False, default=0)
    total_taxes = Column(Numeric(12, 2), nullable=False, default=0)

    # Working hours
    regular_hours = Column(Numeric(6, 2), nullable=True, default=0)
    overtime_hours = Column(Numeric(6, 2), nullable=True, default=0)

    # Leave information
    paid_leave_days = Column(Numeric(4, 1), nullable=True, default=0)
    unpaid_leave_days = Column(Numeric(4, 1), nullable=True, default=0)

    # Status
    status = Column(Enum(PayrollStatus), nullable=False, default=PayrollStatus.DRAFT)

    # Processing
    calculated_at = Column(DateTime(timezone=True), nullable=True)
    calculated_by = Column(UUID(as_uuid=True), ForeignKey("employees.id"), nullable=True)
    approved_at = Column(DateTime(timezone=True), nullable=True)
    approved_by = Column(UUID(as_uuid=True), ForeignKey("employees.id"), nullable=True)

    # Payment
    payment_method = Column(String(50), nullable=True)  # bank_transfer, check, cash
    payment_reference = Column(String(100), nullable=True)
    paid_at = Column(DateTime(timezone=True), nullable=True)

    # Documents
    payslip_url = Column(String(500), nullable=True)

    # Relationships
    employee = relationship("Employee", back_populates="payroll_records", foreign_keys=[employee_id])
    period = relationship("PayrollPeriod", back_populates="payroll_records")
    calculator = relationship("Employee", foreign_keys=[calculated_by])
    approver = relationship("Employee", foreign_keys=[approved_by])
    components = relationship("PayrollComponent", back_populates="payroll_record")


class PayrollComponent(BaseModel):
    """Individual payroll component (earnings, deductions, etc.)"""
    __tablename__ = "payroll_components"

    payroll_record_id = Column(UUID(as_uuid=True), ForeignKey("payroll_records.id"), nullable=False, index=True)
    component_definition_id = Column(UUID(as_uuid=True), ForeignKey("payroll_component_definitions.id"), nullable=False)

    # Component details
    name = Column(String(200), nullable=False)
    component_type = Column(Enum(ComponentType), nullable=False)

    # Calculation
    calculation_method = Column(String(50), nullable=False)  # fixed, percentage, formula
    base_amount = Column(Numeric(12, 2), nullable=True)
    percentage = Column(Numeric(5, 2), nullable=True)
    calculated_amount = Column(Numeric(12, 2), nullable=False)

    # Tax implications
    is_taxable = Column(Boolean, default=True)
    tax_category = Column(String(50), nullable=True)

    # Relationships
    payroll_record = relationship("PayrollRecord", back_populates="components")
    component_definition = relationship("PayrollComponentDefinition", back_populates="components")


class PayrollComponentDefinition(BaseModel):
    """Payroll component definition (salary structure)"""
    __tablename__ = "payroll_component_definitions"

    organization_id = Column(UUID(as_uuid=True), nullable=False, index=True)
    name = Column(String(200), nullable=False)
    code = Column(String(50), unique=True, nullable=False, index=True)
    description = Column(Text, nullable=True)

    # Component configuration
    component_type = Column(Enum(ComponentType), nullable=False)
    calculation_method = Column(String(50), nullable=False)  # fixed, percentage, formula, hours_based

    # Default values
    default_amount = Column(Numeric(12, 2), nullable=True)
    default_percentage = Column(Numeric(5, 2), nullable=True)
    calculation_formula = Column(Text, nullable=True)  # For complex calculations

    # Applicability
    applicable_to_all = Column(Boolean, default=True)
    applicable_designations = Column(JSONB, nullable=True)  # Array of designation IDs
    applicable_departments = Column(JSONB, nullable=True)  # Array of department IDs
    applicable_employment_types = Column(JSONB, nullable=True)  # Array of employment types

    # Tax and compliance
    is_taxable = Column(Boolean, default=True)
    tax_category = Column(String(50), nullable=True)
    statutory_component = Column(Boolean, default=False)  # PF, ESI, etc.

    # Display settings
    display_order = Column(Integer, nullable=False, default=0)
    show_in_payslip = Column(Boolean, default=True)

    # Status
    is_active = Column(Boolean, default=True)
    effective_from = Column(Date, nullable=False)
    effective_until = Column(Date, nullable=True)

    # Relationships
    components = relationship("PayrollComponent", back_populates="component_definition")


class SalaryStructure(BaseModel):
    """Employee salary structure"""
    __tablename__ = "salary_structures"

    employee_id = Column(UUID(as_uuid=True), ForeignKey("employees.id"), nullable=False, index=True)
    template_id = Column(UUID(as_uuid=True), ForeignKey("salary_templates.id"), nullable=True)
    name = Column(String(200), nullable=False)

    # Structure details
    basic_salary = Column(Numeric(12, 2), nullable=False)
    gross_salary = Column(Numeric(12, 2), nullable=False)

    # Effective period
    effective_from = Column(Date, nullable=False)
    effective_until = Column(Date, nullable=True)

    # Components
    salary_components = Column(JSONB, nullable=False)  # Array of component configurations

    # Status
    is_active = Column(Boolean, default=True)

    # Approval
    approved_by = Column(UUID(as_uuid=True), ForeignKey("employees.id"), nullable=True)
    approved_at = Column(DateTime(timezone=True), nullable=True)

    # Relationships
    employee = relationship("Employee", foreign_keys=[employee_id])
    template = relationship("SalaryTemplate", back_populates="salary_structures")
    approver = relationship("Employee", foreign_keys=[approved_by])


class PayrollAdjustment(BaseModel, AuditMixin):
    """Payroll adjustments for specific periods"""
    __tablename__ = "payroll_adjustments"

    employee_id = Column(UUID(as_uuid=True), ForeignKey("employees.id"), nullable=False)
    period_id = Column(UUID(as_uuid=True), ForeignKey("payroll_periods.id"), nullable=False)

    # Adjustment details
    adjustment_type = Column(String(50), nullable=False)  # bonus, deduction, arrears, etc.
    description = Column(Text, nullable=False)
    amount = Column(Numeric(12, 2), nullable=False)

    # Tax implications
    is_taxable = Column(Boolean, default=True)

    # Approval
    approved_by = Column(UUID(as_uuid=True), ForeignKey("employees.id"), nullable=True)
    approved_at = Column(DateTime(timezone=True), nullable=True)

    # Processing
    is_processed = Column(Boolean, default=False)
    processed_at = Column(DateTime(timezone=True), nullable=True)

    # Relationships
    employee = relationship("Employee", foreign_keys=[employee_id])
    period = relationship("PayrollPeriod")
    approver = relationship("Employee", foreign_keys=[approved_by])


class TaxConfiguration(BaseModel):
    """Tax configuration for payroll"""
    __tablename__ = "tax_configurations"

    organization_id = Column(UUID(as_uuid=True), nullable=False, index=True)
    name = Column(String(200), nullable=False)
    tax_type = Column(String(50), nullable=False)  # income_tax, social_security, etc.

    # Tax calculation
    calculation_method = Column(String(50), nullable=False)  # percentage, slab, fixed
    tax_slabs = Column(JSONB, nullable=True)  # Array of tax slab configurations

    # Applicability
    applicable_to_all = Column(Boolean, default=True)
    applicable_salary_range_min = Column(Numeric(12, 2), nullable=True)
    applicable_salary_range_max = Column(Numeric(12, 2), nullable=True)

    # Effective period
    effective_from = Column(Date, nullable=False)
    effective_until = Column(Date, nullable=True)

    # Status
    is_active = Column(Boolean, default=True)


class PayrollReport(BaseModel, AuditMixin):
    """Payroll reports and summaries"""
    __tablename__ = "payroll_reports"

    organization_id = Column(UUID(as_uuid=True), nullable=False, index=True)
    period_id = Column(UUID(as_uuid=True), ForeignKey("payroll_periods.id"), nullable=False)

    # Report details
    report_type = Column(String(50), nullable=False)  # summary, detailed, tax_report, etc.
    report_name = Column(String(200), nullable=False)

    # Report data
    report_data = Column(JSONB, nullable=False)  # Report content as JSON

    # File generation
    file_url = Column(String(500), nullable=True)
    file_format = Column(String(20), nullable=True)  # pdf, excel, csv

    # Generation details
    generated_by = Column(UUID(as_uuid=True), ForeignKey("employees.id"), nullable=False)
    generated_at = Column(DateTime(timezone=True), nullable=False, default=datetime.utcnow)

    # Relationships
    period = relationship("PayrollPeriod")
    generator = relationship("Employee", foreign_keys=[generated_by])


# Enhanced Models from Payroll Folder Integration
class SalaryTemplate(BaseModel, AuditMixin):
    """Salary template for standardized salary structures"""
    __tablename__ = "salary_templates"

    organization_id = Column(UUID(as_uuid=True), nullable=False, index=True)
    template_name = Column(String(100), nullable=False)
    description = Column(Text, nullable=True)

    # Template configuration
    employment_type = Column(String(45), nullable=True)
    employee_type = Column(String(45), nullable=True)
    level = Column(String(45), nullable=True)
    salary_type = Column(String(45), nullable=True)  # monthly, hourly, annual

    # Location and currency
    country = Column(String(45), nullable=True)
    currency = Column(String(3), nullable=False, default="USD")

    # Work configuration
    work_schedule = Column(String(45), nullable=True)
    hours_worked = Column(Numeric(5, 2), nullable=True)
    work_duration = Column(String(45), nullable=True)

    # Tax configuration
    tax_type = Column(String(45), nullable=True)

    # Salary amounts
    gross_pay = Column(Numeric(12, 2), nullable=False)
    basic_salary = Column(Numeric(12, 2), nullable=False)

    # Tax calculations
    monthly_tax = Column(Numeric(12, 2), nullable=True)
    annual_tax = Column(Numeric(12, 2), nullable=True)

    # Component totals
    total_taxable_monthly_sum = Column(Numeric(12, 2), nullable=True)
    total_taxable_annual_sum = Column(Numeric(12, 2), nullable=True)
    total_non_taxable_monthly_sum = Column(Numeric(12, 2), nullable=True)
    total_non_taxable_annual_sum = Column(Numeric(12, 2), nullable=True)
    total_statutory_monthly_sum = Column(Numeric(12, 2), nullable=True)
    total_statutory_annual_sum = Column(Numeric(12, 2), nullable=True)
    total_other_deductions_monthly_sum = Column(Numeric(12, 2), nullable=True)
    total_other_deductions_annual_sum = Column(Numeric(12, 2), nullable=True)

    # Net pay
    netpay = Column(Numeric(12, 2), nullable=True)

    # Status
    is_active = Column(Boolean, default=True)

    # Relationships
    salary_structures = relationship("SalaryStructure", back_populates="template")
    template_components = relationship("SalaryTemplateComponent", back_populates="template")


class SalaryTemplateComponent(BaseModel):
    """Components associated with salary templates"""
    __tablename__ = "salary_template_components"

    template_id = Column(UUID(as_uuid=True), ForeignKey("salary_templates.id"), nullable=False)
    component_definition_id = Column(UUID(as_uuid=True), ForeignKey("payroll_component_definitions.id"), nullable=False)

    # Component configuration for this template
    amount = Column(Numeric(12, 2), nullable=True)
    percentage = Column(Numeric(5, 2), nullable=True)
    is_mandatory = Column(Boolean, default=True)

    # Relationships
    template = relationship("SalaryTemplate", back_populates="template_components")
    component_definition = relationship("PayrollComponentDefinition")


class PayrollHistory(BaseModel, AuditMixin):
    """Historical payroll records for tracking and reporting"""
    __tablename__ = "payroll_history"

    employee_id = Column(UUID(as_uuid=True), ForeignKey("employees.id"), nullable=False, index=True)
    pay_schedule_id = Column(UUID(as_uuid=True), nullable=True)
    pay_schedule_name = Column(String(250), nullable=True)

    # Prorated calculations
    prorated_monthly_tax = Column(Numeric(12, 2), nullable=True)
    prorated_annual_tax = Column(Numeric(12, 2), nullable=True)
    prorated_gross = Column(Numeric(12, 2), nullable=True)
    prorated_net = Column(Numeric(12, 2), nullable=True)
    is_prorated = Column(Boolean, default=False)

    # Component totals
    total_taxable_monthly_sum = Column(Numeric(12, 2), nullable=True)
    total_taxable_annual_sum = Column(Numeric(12, 2), nullable=True)
    total_non_taxable_monthly_sum = Column(Numeric(12, 2), nullable=True)
    total_non_taxable_annual_sum = Column(Numeric(12, 2), nullable=True)
    total_statutory_monthly_sum = Column(Numeric(12, 2), nullable=True)
    total_statutory_annual_sum = Column(Numeric(12, 2), nullable=True)
    total_other_deductions_monthly_sum = Column(Numeric(12, 2), nullable=True)
    total_other_deductions_annual_sum = Column(Numeric(12, 2), nullable=True)

    # Final amounts
    netpay = Column(Numeric(12, 2), nullable=True)
    gross_pay = Column(Numeric(12, 2), nullable=True)
    cost_to_company = Column(Numeric(12, 2), nullable=True)

    # Statutory deductions
    pension = Column(Numeric(12, 2), nullable=True)
    nhf = Column(Numeric(12, 2), nullable=True)  # National Housing Fund
    monthly_tax = Column(Numeric(12, 2), nullable=True)
    annual_tax = Column(Numeric(12, 2), nullable=True)

    # Overtime
    overtime_amount = Column(Numeric(12, 2), default=0.0, nullable=True)

    # Processing status
    is_processed = Column(Boolean, default=False)
    is_processed_created = Column(String(250), nullable=True)
    message = Column(String(250), nullable=True)

    # Payment details
    payment_id = Column(String(45), nullable=True)
    payment_status = Column(String(45), nullable=True)
    transaction_id = Column(String(100), nullable=True)
    transaction_date = Column(String(250), nullable=True)
    reference_code = Column(String(100), nullable=True, unique=True)

    # Relationships
    employee = relationship("Employee", back_populates="payroll_histories")


class PaystackCredential(BaseModel, AuditMixin):
    """Paystack payment gateway credentials"""
    __tablename__ = "paystack_credentials"

    organization_id = Column(UUID(as_uuid=True), nullable=False, index=True)

    # Paystack configuration
    public_key = Column(String(200), nullable=False)
    secret_key = Column(String(200), nullable=False)  # Should be encrypted
    webhook_url = Column(String(500), nullable=True)

    # Environment
    is_live = Column(Boolean, default=False)  # False for test, True for live

    # Status
    is_active = Column(Boolean, default=True)

    # Test configuration
    test_public_key = Column(String(200), nullable=True)
    test_secret_key = Column(String(200), nullable=True)


class TransactionHistory(BaseModel, AuditMixin):
    """Payment transaction history"""
    __tablename__ = "transaction_history"

    organization_id = Column(UUID(as_uuid=True), nullable=False, index=True)
    employee_id = Column(UUID(as_uuid=True), ForeignKey("employees.id"), nullable=False)
    payroll_record_id = Column(UUID(as_uuid=True), ForeignKey("payroll_records.id"), nullable=True)

    # Transaction details
    transaction_id = Column(String(100), nullable=False, unique=True)
    reference_code = Column(String(100), nullable=False)
    amount = Column(Numeric(12, 2), nullable=False)
    currency = Column(String(3), nullable=False, default="NGN")

    # Payment gateway details
    gateway = Column(String(50), nullable=False)  # paystack, flutterwave, etc.
    gateway_transaction_id = Column(String(100), nullable=True)
    gateway_reference = Column(String(100), nullable=True)

    # Status
    status = Column(String(50), nullable=False)  # pending, success, failed, cancelled
    gateway_response = Column(JSONB, nullable=True)

    # Timing
    initiated_at = Column(DateTime(timezone=True), nullable=False, default=datetime.utcnow)
    completed_at = Column(DateTime(timezone=True), nullable=True)

    # Relationships
    employee = relationship("Employee")
    payroll_record = relationship("PayrollRecord")
