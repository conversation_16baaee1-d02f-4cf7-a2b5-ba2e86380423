#!/usr/bin/env python3
"""
Recreate database tables to match current models
"""

import sys
import os

# Add the app directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'app'))

from sqlalchemy import text
from app.db.session import SessionLocal, engine
from app.db.base import Base

def recreate_tables():
    """Drop and recreate all tables"""
    db = SessionLocal()

    try:
        print("Recreating database tables...")

        # Drop all tables manually to avoid circular dependency issues
        print("Dropping existing tables manually...")

        # Drop tables in reverse dependency order
        drop_queries = [
            "DROP TABLE IF EXISTS kanban_checklist_items CASCADE;",
            "DROP TABLE IF EXISTS kanban_card_checklists CASCADE;",
            "DROP TABLE IF EXISTS kanban_card_activities CASCADE;",
            "DROP TABLE IF EXISTS kanban_card_comments CASCADE;",
            "DROP TABLE IF EXISTS kanban_cards CASCADE;",
            "DROP TABLE IF EXISTS kanban_board_members CASCADE;",
            "DROP TABLE IF EXISTS kanban_labels CASCADE;",
            "DROP TABLE IF EXISTS kanban_columns CASCADE;",
            "DROP TABLE IF EXISTS kanban_boards CASCADE;",
            "DROP TABLE IF EXISTS kanban_templates CASCADE;",
            "DROP TABLE IF EXISTS task_delegations CASCADE;",
            "DROP TABLE IF EXISTS task_assignments CASCADE;",
            "DROP TABLE IF EXISTS task_comments CASCADE;",
            "DROP TABLE IF EXISTS task_dependencies CASCADE;",
            "DROP TABLE IF EXISTS tasks CASCADE;",
            "DROP TABLE IF EXISTS project_milestones CASCADE;",
            "DROP TABLE IF EXISTS project_assignments CASCADE;",
            "DROP TABLE IF EXISTS projects CASCADE;",
            "DROP TABLE IF EXISTS project_templates CASCADE;",
            "DROP TABLE IF EXISTS ticket_escalations CASCADE;",
            "DROP TABLE IF EXISTS ticket_activities CASCADE;",
            "DROP TABLE IF EXISTS ticket_comments CASCADE;",
            "DROP TABLE IF EXISTS tickets CASCADE;",
            "DROP TABLE IF EXISTS ticket_templates CASCADE;",
            "DROP TABLE IF EXISTS ticket_slas CASCADE;",
            "DROP TABLE IF EXISTS ticket_categories CASCADE;",
            "DROP TABLE IF EXISTS timesheet_approvals CASCADE;",
            "DROP TABLE IF EXISTS time_trackers CASCADE;",
            "DROP TABLE IF EXISTS timesheet_templates CASCADE;",
            "DROP TABLE IF EXISTS timesheet_entries CASCADE;",
            "DROP TABLE IF EXISTS timesheet_periods CASCADE;",
            "DROP TABLE IF EXISTS shift_swap_requests CASCADE;",
            "DROP TABLE IF EXISTS shift_pattern_assignments CASCADE;",
            "DROP TABLE IF EXISTS shift_patterns CASCADE;",
            "DROP TABLE IF EXISTS shift_assignments CASCADE;",
            "DROP TABLE IF EXISTS shifts CASCADE;",
            "DROP TABLE IF EXISTS leave_encashments CASCADE;",
            "DROP TABLE IF EXISTS leave_calendar CASCADE;",
            "DROP TABLE IF EXISTS leave_approval_workflows CASCADE;",
            "DROP TABLE IF EXISTS leave_requests CASCADE;",
            "DROP TABLE IF EXISTS leave_balances CASCADE;",
            "DROP TABLE IF EXISTS leave_policies CASCADE;",
            "DROP TABLE IF EXISTS attendance_logs CASCADE;",
            "DROP TABLE IF EXISTS attendance_records CASCADE;",
            "DROP TABLE IF EXISTS attendance_policies CASCADE;",
            "DROP TABLE IF EXISTS payroll_reports CASCADE;",
            "DROP TABLE IF EXISTS payroll_adjustments CASCADE;",
            "DROP TABLE IF EXISTS payroll_component_definitions CASCADE;",
            "DROP TABLE IF EXISTS payroll_components CASCADE;",
            "DROP TABLE IF EXISTS payroll_records CASCADE;",
            "DROP TABLE IF EXISTS payroll_periods CASCADE;",
            "DROP TABLE IF EXISTS salary_structures CASCADE;",
            "DROP TABLE IF EXISTS tax_configurations CASCADE;",
            "DROP TABLE IF EXISTS engagement_actions CASCADE;",
            "DROP TABLE IF EXISTS feedback_responses CASCADE;",
            "DROP TABLE IF EXISTS feedback_templates CASCADE;",
            "DROP TABLE IF EXISTS feedback_requests CASCADE;",
            "DROP TABLE IF EXISTS survey_answers CASCADE;",
            "DROP TABLE IF EXISTS survey_responses CASCADE;",
            "DROP TABLE IF EXISTS survey_questions CASCADE;",
            "DROP TABLE IF EXISTS surveys CASCADE;",
            "DROP TABLE IF EXISTS survey_templates CASCADE;",
            "DROP TABLE IF EXISTS engagement_metrics CASCADE;",
            "DROP TABLE IF EXISTS performance_cycles CASCADE;",
            "DROP TABLE IF EXISTS performance_templates CASCADE;",
            "DROP TABLE IF EXISTS goal_reviews CASCADE;",
            "DROP TABLE IF EXISTS competencies CASCADE;",
            "DROP TABLE IF EXISTS performance_ratings CASCADE;",
            "DROP TABLE IF EXISTS performance_reviews CASCADE;",
            "DROP TABLE IF EXISTS goal_updates CASCADE;",
            "DROP TABLE IF EXISTS goals CASCADE;",
            "DROP TABLE IF EXISTS approval_delegations CASCADE;",
            "DROP TABLE IF EXISTS delegation_rules CASCADE;",
            "DROP TABLE IF EXISTS delegation_templates CASCADE;",
            "DROP TABLE IF EXISTS delegation_activities CASCADE;",
            "DROP TABLE IF EXISTS delegations CASCADE;",
            "DROP TABLE IF EXISTS employees CASCADE;",
            "DROP TABLE IF EXISTS designations CASCADE;",
            "DROP TABLE IF EXISTS departments CASCADE;",
            "DROP TABLE IF EXISTS organizations CASCADE;",
            "DROP TABLE IF EXISTS otp_records CASCADE;",
            "DROP TABLE IF EXISTS auth_tokens CASCADE;",
            "DROP TABLE IF EXISTS users CASCADE;"
        ]

        for query in drop_queries:
            try:
                db.execute(text(query))
                db.commit()
            except Exception as e:
                print(f"Warning: {query} - {e}")
                db.rollback()

        print("✅ Tables dropped")

        # Create all tables
        print("Creating new tables...")
        Base.metadata.create_all(bind=engine)
        print("✅ Tables created")

        print("\n🎉 Database tables recreated successfully!")

    except Exception as e:
        print(f"❌ Error recreating tables: {e}")
        raise
    finally:
        db.close()

if __name__ == "__main__":
    recreate_tables()
