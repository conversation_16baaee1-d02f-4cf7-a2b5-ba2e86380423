"""update payschedle

Revision ID: 6e4dc2f646f4
Revises: 44f0934df832
Create Date: 2025-04-22 14:27:57.357743

"""
from typing import Sequence, Union
from sqlalchemy import Column, Integer, String, Float
from alembic import op
import sqlalchemy as sa
from sqlalchemy import func, DateTime, ForeignKey
from sqlalchemy.dialects.postgresql import BOOLEAN
from sqlalchemy import func, TIMESTAMP, ForeignKey




# revision identifiers, used by Alembic.
revision: str = '6e4dc2f646f4'
down_revision: Union[str, None] = '44f0934df832'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    op.add_column('pay_schedules', sa.Column('is_approved', BOOLEAN, nullable=True, default=False)),
    op.add_column('pay_schedules', sa.Column('approved_date', TIMESTAMP, nullable=True))


def downgrade() -> None:
    op.drop_column('pay_schedules', 'is_approved')
    op.drop_column('pay_schedules', 'approved_date')

