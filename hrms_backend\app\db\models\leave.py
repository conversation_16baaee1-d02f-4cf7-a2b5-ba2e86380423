from sqlalchemy import Column, String, Date, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Text, Integer, Numeric, DateTime, Enum, Time
from sqlalchemy.dialects.postgresql import UUID, JSONB
from sqlalchemy.orm import relationship
from enum import Enum as PyEnum
from datetime import date, datetime, time
from typing import Optional

from ..base import BaseModel, AuditMixin


class LeaveType(PyEnum):
    ANNUAL = "annual"
    SICK = "sick"
    MATERNITY = "maternity"
    PATERNITY = "paternity"
    PERSONAL = "personal"
    EMERGENCY = "emergency"
    BEREAVEMENT = "bereavement"
    STUDY = "study"
    SABBATICAL = "sabbatical"
    UNPAID = "unpaid"


class LeaveStatus(PyEnum):
    PENDING = "PENDING"
    APPROVED = "APPROVED"
    REJECTED = "REJECTED"
    CANCELLED = "CANCELLED"
    WITHDRAWN = "WITHDRAWN"


class LeaveDuration(PyEnum):
    FULL_DAY = "FULL_DAY"
    HALF_DAY_MORNING = "HALF_DAY_MORNING"
    HALF_DAY_AFTERNOON = "HALF_DAY_AFTERNOON"
    HOURLY = "HOURLY"


class LeavePolicy(BaseModel):
    """Leave policy configuration"""
    __tablename__ = "leave_policies"

    name = Column(String(200), nullable=False)
    leave_type = Column(Enum(LeaveType), nullable=False)
    organization_id = Column(UUID(as_uuid=True), nullable=False, index=True)

    # Entitlement
    annual_entitlement = Column(Numeric(5, 2), nullable=False)  # Days per year
    max_carry_forward = Column(Numeric(5, 2), nullable=True, default=0)
    max_accumulation = Column(Numeric(5, 2), nullable=True)

    # Accrual settings
    accrual_frequency = Column(String(20), nullable=False, default="monthly")  # monthly, quarterly, yearly
    accrual_start_date = Column(String(20), nullable=False, default="hire_date")  # hire_date, calendar_year

    # Application rules
    min_notice_days = Column(Integer, nullable=False, default=1)
    max_consecutive_days = Column(Integer, nullable=True)
    min_application_days = Column(Numeric(3, 1), nullable=False, default=0.5)

    # Approval workflow
    requires_approval = Column(Boolean, default=True)
    auto_approve_threshold = Column(Integer, nullable=True)  # Auto approve if <= this many days

    # Documentation
    requires_documentation = Column(Boolean, default=False)
    documentation_threshold = Column(Integer, nullable=True)  # Require docs if >= this many days

    # Gender/role restrictions
    applicable_genders = Column(JSONB, nullable=True)  # Array of genders
    applicable_employment_types = Column(JSONB, nullable=True)  # Array of employment types

    # Probation settings
    available_during_probation = Column(Boolean, default=True)
    probation_entitlement = Column(Numeric(5, 2), nullable=True)

    # Note: max_carry_forward is already defined above in line 79

    # Status
    is_active = Column(Boolean, default=True)


class LeaveBalance(BaseModel):
    """Employee leave balance tracking"""
    __tablename__ = "leave_balances"

    employee_id = Column(UUID(as_uuid=True), ForeignKey("employees.id"), nullable=False, index=True)
    leave_policy_id = Column(UUID(as_uuid=True), ForeignKey("leave_policies.id"), nullable=False)
    year = Column(Integer, nullable=False)

    # Balance tracking
    opening_balance = Column(Numeric(5, 2), nullable=False, default=0)
    accrued_balance = Column(Numeric(5, 2), nullable=False, default=0)
    used_balance = Column(Numeric(5, 2), nullable=False, default=0)
    pending_balance = Column(Numeric(5, 2), nullable=False, default=0)  # Pending approval
    carried_forward = Column(Numeric(5, 2), nullable=False, default=0)

    # Calculated fields
    available_balance = Column(Numeric(5, 2), nullable=False, default=0)

    # Relationships
    employee = relationship("Employee", foreign_keys=[employee_id])
    leave_policy = relationship("LeavePolicy")

    # Unique constraint
    __table_args__ = (
        {"schema": None},
    )


class LeaveRequest(BaseModel, AuditMixin):
    """Leave request model"""
    __tablename__ = "leave_requests"

    employee_id = Column(UUID(as_uuid=True), ForeignKey("employees.id"), nullable=False, index=True)
    leave_policy_id = Column(UUID(as_uuid=True), ForeignKey("leave_policies.id"), nullable=False)

    # Request details
    start_date = Column(Date, nullable=False)
    end_date = Column(Date, nullable=False)
    total_days = Column(Numeric(4, 1), nullable=False)
    duration_type = Column(Enum(LeaveDuration), nullable=False, default=LeaveDuration.FULL_DAY)

    # Reason and documentation
    reason = Column(Text, nullable=False)
    attachment_urls = Column(JSONB, nullable=True)  # Array of file URLs

    # Contact information during leave
    contact_number = Column(String(20), nullable=True)
    emergency_contact = Column(String(200), nullable=True)

    # Handover details
    handover_notes = Column(Text, nullable=True)
    handover_to = Column(UUID(as_uuid=True), ForeignKey("employees.id"), nullable=True)

    # Status and workflow
    status = Column(Enum(LeaveStatus), nullable=False, default=LeaveStatus.PENDING)
    applied_at = Column(DateTime(timezone=True), nullable=False, default=datetime.utcnow)

    # Approval workflow
    approved_by = Column(UUID(as_uuid=True), ForeignKey("employees.id"), nullable=True)
    approved_at = Column(DateTime(timezone=True), nullable=True)
    rejection_reason = Column(Text, nullable=True)

    # HR processing
    hr_processed_by = Column(UUID(as_uuid=True), ForeignKey("employees.id"), nullable=True)
    hr_processed_at = Column(DateTime(timezone=True), nullable=True)
    hr_notes = Column(Text, nullable=True)

    # Cancellation
    cancelled_by = Column(UUID(as_uuid=True), ForeignKey("employees.id"), nullable=True)
    cancelled_at = Column(DateTime(timezone=True), nullable=True)
    cancellation_reason = Column(Text, nullable=True)

    # Relationships
    employee = relationship("Employee", back_populates="leave_requests", foreign_keys=[employee_id])
    leave_policy = relationship("LeavePolicy")
    approver = relationship("Employee", foreign_keys=[approved_by])
    handover_employee = relationship("Employee", foreign_keys=[handover_to])
    hr_processor = relationship("Employee", foreign_keys=[hr_processed_by])
    canceller = relationship("Employee", foreign_keys=[cancelled_by])


class LeaveApprovalWorkflow(BaseModel):
    """Leave approval workflow configuration"""
    __tablename__ = "leave_approval_workflows"

    leave_policy_id = Column(UUID(as_uuid=True), ForeignKey("leave_policies.id"), nullable=False)
    organization_id = Column(UUID(as_uuid=True), nullable=False, index=True)

    # Workflow configuration
    workflow_name = Column(String(200), nullable=False)
    approval_levels = Column(JSONB, nullable=False)  # Array of approval level configs

    # Conditions
    min_days_threshold = Column(Integer, nullable=True)
    max_days_threshold = Column(Integer, nullable=True)
    department_ids = Column(JSONB, nullable=True)  # Applicable departments
    designation_ids = Column(JSONB, nullable=True)  # Applicable designations

    # Settings
    is_sequential = Column(Boolean, default=True)  # Sequential vs parallel approval
    auto_escalation_days = Column(Integer, nullable=True)

    # Relationships
    leave_policy = relationship("LeavePolicy")


class LeaveCalendar(BaseModel):
    """Company holiday and leave calendar"""
    __tablename__ = "leave_calendar"

    organization_id = Column(UUID(as_uuid=True), nullable=False, index=True)
    date = Column(Date, nullable=False)

    # Holiday details
    name = Column(String(200), nullable=False)
    description = Column(Text, nullable=True)
    is_optional = Column(Boolean, default=False)

    # Applicability
    applicable_locations = Column(JSONB, nullable=True)  # Array of locations
    applicable_departments = Column(JSONB, nullable=True)  # Array of department IDs

    # Type
    holiday_type = Column(String(50), nullable=False, default="public")  # public, religious, regional

    # Recurring settings
    is_recurring = Column(Boolean, default=False)
    recurrence_pattern = Column(JSONB, nullable=True)  # For recurring holidays

    # Unique constraint
    __table_args__ = (
        {"schema": None},
    )


class WeeklyOffPattern(BaseModel, AuditMixin):
    """Weekly off patterns for different locations/departments"""
    __tablename__ = "weekly_off_patterns"

    organization_id = Column(UUID(as_uuid=True), nullable=False, index=True)
    name = Column(String(100), nullable=False)
    description = Column(Text, nullable=True)

    # Pattern definition
    pattern_type = Column(String(20), nullable=False, default="fixed")  # fixed, rotating, alternate
    weekly_off_days = Column(JSONB, nullable=False)  # Array of day numbers (0=Monday, 6=Sunday)

    # Rotating pattern settings
    rotation_weeks = Column(Integer, nullable=True)  # For rotating patterns
    rotation_schedule = Column(JSONB, nullable=True)  # Detailed rotation schedule

    # Applicability
    applicable_locations = Column(JSONB, nullable=True)
    applicable_departments = Column(JSONB, nullable=True)
    applicable_employee_types = Column(JSONB, nullable=True)

    # Validity
    effective_from = Column(Date, nullable=False)
    effective_until = Column(Date, nullable=True)
    is_active = Column(Boolean, default=True)
    is_default = Column(Boolean, default=False)


class HolidayConflict(BaseModel):
    """Track conflicts between holidays, leaves, and attendance"""
    __tablename__ = "holiday_conflicts"

    organization_id = Column(UUID(as_uuid=True), nullable=False, index=True)
    conflict_date = Column(Date, nullable=False)
    conflict_type = Column(String(50), nullable=False)  # leave_overlap, attendance_required, etc.

    # Related entities
    holiday_id = Column(UUID(as_uuid=True), ForeignKey("leave_calendar.id"), nullable=True)
    leave_request_id = Column(UUID(as_uuid=True), ForeignKey("leave_requests.id"), nullable=True)
    employee_id = Column(UUID(as_uuid=True), ForeignKey("employees.id"), nullable=True)

    # Conflict details
    description = Column(Text, nullable=False)
    severity = Column(String(20), nullable=False, default="medium")  # low, medium, high, critical
    status = Column(String(20), nullable=False, default="open")  # open, resolved, ignored

    # Resolution
    resolved_by = Column(UUID(as_uuid=True), ForeignKey("employees.id"), nullable=True)
    resolved_at = Column(DateTime(timezone=True), nullable=True)
    resolution_notes = Column(Text, nullable=True)

    created_at = Column(DateTime(timezone=True), nullable=False, default=datetime.utcnow)


class LocationCalendar(BaseModel, AuditMixin):
    """Location-specific calendar settings"""
    __tablename__ = "location_calendars"

    organization_id = Column(UUID(as_uuid=True), nullable=False, index=True)
    location_name = Column(String(100), nullable=False)
    location_code = Column(String(20), nullable=False)

    # Geographic information
    country = Column(String(100), nullable=True)
    state_province = Column(String(100), nullable=True)
    city = Column(String(100), nullable=True)
    timezone = Column(String(50), nullable=False, default="UTC")

    # Calendar settings
    weekly_off_pattern_id = Column(UUID(as_uuid=True), ForeignKey("weekly_off_patterns.id"), nullable=True)
    working_hours_start = Column(Time, nullable=False, default=time(9, 0))
    working_hours_end = Column(Time, nullable=False, default=time(18, 0))

    # Holiday settings
    follows_national_holidays = Column(Boolean, default=True)
    follows_regional_holidays = Column(Boolean, default=True)
    custom_holidays_only = Column(Boolean, default=False)

    is_active = Column(Boolean, default=True)

    # Relationships
    weekly_off_pattern = relationship("WeeklyOffPattern")


class LeaveEncashment(BaseModel, AuditMixin):
    """Leave encashment requests"""
    __tablename__ = "leave_encashments"

    employee_id = Column(UUID(as_uuid=True), ForeignKey("employees.id"), nullable=False)
    leave_policy_id = Column(UUID(as_uuid=True), ForeignKey("leave_policies.id"), nullable=False)

    # Encashment details
    days_to_encash = Column(Numeric(5, 2), nullable=False)
    rate_per_day = Column(Numeric(10, 2), nullable=False)
    total_amount = Column(Numeric(12, 2), nullable=False)

    # Request details
    reason = Column(Text, nullable=True)
    requested_at = Column(DateTime(timezone=True), nullable=False, default=datetime.utcnow)

    # Approval
    status = Column(Enum(LeaveStatus), nullable=False, default=LeaveStatus.PENDING)
    approved_by = Column(UUID(as_uuid=True), ForeignKey("employees.id"), nullable=True)
    approved_at = Column(DateTime(timezone=True), nullable=True)

    # Processing
    processed_by = Column(UUID(as_uuid=True), ForeignKey("employees.id"), nullable=True)
    processed_at = Column(DateTime(timezone=True), nullable=True)
    payment_reference = Column(String(100), nullable=True)

    # Relationships
    employee = relationship("Employee", foreign_keys=[employee_id])
    leave_policy = relationship("LeavePolicy")
    approver = relationship("Employee", foreign_keys=[approved_by])
    processor = relationship("Employee", foreign_keys=[processed_by])
