#!/usr/bin/env python3
"""
Test super admin login
"""

import requests
import json

BASE_URL = "http://localhost:8085"

def test_super_admin_login():
    """Test super admin login"""
    try:
        print("Testing Super Admin Login")
        print("=" * 40)
        
        # Test super admin login
        credentials = {
            "email": "<EMAIL>",
            "password": "password123"
        }
        
        response = requests.post(f"{BASE_URL}/api/auth/login", json=credentials)
        
        if response.status_code == 200:
            data = response.json()
            user = data.get('user', {})
            
            print("✅ Super Admin login successful!")
            print(f"Name: {user.get('name')}")
            print(f"Email: {user.get('email')}")
            print(f"Role: {user.get('role')}")
            print(f"Organization ID: {user.get('organization_id')}")
            
            # Test accessing a protected endpoint
            token = data.get('access_token')
            headers = {"Authorization": f"Bearer {token}"}
            
            # Test employees endpoint
            emp_response = requests.get(f"{BASE_URL}/api/employees/", headers=headers)
            if emp_response.status_code == 200:
                emp_data = emp_response.json()
                print(f"✅ Can access employees endpoint: {emp_data.get('total', 0)} employees")
            else:
                print(f"❌ Cannot access employees endpoint: {emp_response.status_code}")
            
            return True
            
        else:
            print(f"❌ Super Admin login failed: {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Test failed: {e}")
        return False

if __name__ == "__main__":
    success = test_super_admin_login()
    if success:
        print("\n🎉 Super Admin is ready to use!")
        print("\nLogin credentials:")
        print("Email: <EMAIL>")
        print("Password: password123")
        print("Role: SUPER_ADMIN")
    else:
        print("\n❌ Super Admin login test failed!")
