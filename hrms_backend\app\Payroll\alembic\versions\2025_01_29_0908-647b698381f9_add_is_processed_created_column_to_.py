"""Add is_processed_created column to payroll_history

Revision ID: 647b698381f9
Revises: 4854bff2583d
Create Date: 2025-01-29 09:08:40.378359

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy import Column, Integer, String, Float
from sqlalchemy import func, DateTime, ForeignKey
from sqlalchemy.dialects.postgresql import BOOLEAN


# revision identifiers, used by Alembic.
revision: str = '647b698381f9'
down_revision: Union[str, None] = '4854bff2583d'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
   op.add_column('payroll_history', sa.Column('is_processed_created', String(length=250), nullable=True))

    


def downgrade() -> None:
   op.drop_column('payroll_history', 'is_processed_created')
    
