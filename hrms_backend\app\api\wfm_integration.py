from fastapi import APIRouter, HTTPException, status, Depends, Query
from sqlalchemy.orm import Session
from typing import Optional, Dict
from uuid import UUID
from datetime import date

from ..core.security import get_current_user, require_permission, CurrentUser
from ..core.rbac import Permissions
from ..db.session import get_db
from ..services.shift_management.wfm_integration_service import wfm_integration_service

router = APIRouter()


@router.get("/analysis/comprehensive", response_model=Dict)
async def get_comprehensive_staffing_analysis(
    start_date: date = Query(...),
    end_date: date = Query(...),
    department_id: Optional[UUID] = Query(None),
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.SHIFT_READ))
):
    """Get comprehensive staffing analysis for WFM"""
    return await wfm_integration_service.comprehensive_staffing_analysis(
        db, start_date, end_date, department_id, current_user
    )


@router.post("/optimization/run", response_model=Dict)
async def run_staffing_optimization(
    start_date: date,
    end_date: date,
    algorithm: str = "greedy",
    department_id: Optional[UUID] = None,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.SHIFT_CREATE))
):
    """Run staffing optimization using specified algorithm"""
    return await wfm_integration_service.optimize_staffing_with_algorithm(
        db, start_date, end_date, algorithm, department_id, current_user
    )


@router.get("/algorithms/available")
async def get_available_algorithms(
    current_user: CurrentUser = Depends(require_permission(Permissions.SHIFT_READ))
):
    """Get available optimization algorithms"""
    return {
        "algorithms": [
            {
                "name": "greedy",
                "display_name": "Greedy Algorithm",
                "description": "Fast, simple optimization that assigns best available employee to highest priority shift",
                "complexity": "O(n log n)",
                "best_for": "Quick optimization, small to medium datasets",
                "pros": ["Fast execution", "Simple to understand", "Good for real-time decisions"],
                "cons": ["May not find global optimum", "Limited constraint handling"]
            },
            {
                "name": "genetic",
                "display_name": "Genetic Algorithm",
                "description": "Evolutionary algorithm that evolves solutions over multiple generations",
                "complexity": "O(g * p * n)",
                "best_for": "Complex constraints, large datasets, high-quality solutions",
                "pros": ["Handles complex constraints", "Finds near-optimal solutions", "Flexible"],
                "cons": ["Slower execution", "Requires parameter tuning", "Non-deterministic"]
            },
            {
                "name": "linear_programming",
                "display_name": "Linear Programming",
                "description": "Mathematical optimization for linear objective functions and constraints",
                "complexity": "Polynomial",
                "best_for": "Linear constraints, optimal solutions required",
                "pros": ["Guaranteed optimal solution", "Handles many constraints", "Mathematically proven"],
                "cons": ["Limited to linear problems", "Setup complexity", "May require specialized solvers"]
            }
        ],
        "default_algorithm": "greedy",
        "recommended_by_size": {
            "small": "greedy",
            "medium": "genetic",
            "large": "linear_programming"
        }
    }


@router.get("/metrics/kpis", response_model=Dict)
async def get_wfm_kpis(
    start_date: Optional[date] = Query(None),
    end_date: Optional[date] = Query(None),
    department_id: Optional[UUID] = Query(None),
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.SHIFT_READ))
):
    """Get key performance indicators for workforce management"""
    if not start_date:
        start_date = date.today().replace(day=1)  # Start of current month
    if not end_date:
        end_date = date.today()

    analysis = await wfm_integration_service.comprehensive_staffing_analysis(
        db, start_date, end_date, department_id, current_user
    )

    # Calculate KPIs from analysis
    coverage_data = analysis["coverage_analysis"]["daily_coverage"]
    
    # Schedule adherence (placeholder - would be calculated from actual vs planned)
    schedule_adherence = 92.5
    
    # Overtime percentage
    total_overtime = sum(emp["overtime_hours"] for emp in analysis["employee_availability"])
    total_regular_hours = len(analysis["employee_availability"]) * 40  # Assume 40h/week
    overtime_percentage = (total_overtime / total_regular_hours) * 100 if total_regular_hours > 0 else 0
    
    # Absenteeism rate
    total_employees = len(set(emp["employee_id"] for emp in analysis["employee_availability"]))
    absent_instances = len([emp for emp in analysis["employee_availability"] if emp["has_leave"]])
    absenteeism_rate = (absent_instances / len(analysis["employee_availability"])) * 100 if analysis["employee_availability"] else 0
    
    return {
        "period": {
            "start_date": start_date.isoformat(),
            "end_date": end_date.isoformat()
        },
        "kpis": {
            "schedule_adherence": {
                "value": schedule_adherence,
                "unit": "percentage",
                "target": 95.0,
                "status": "good" if schedule_adherence >= 90 else "needs_improvement"
            },
            "staffing_coverage": {
                "value": analysis["coverage_analysis"]["summary"]["average_coverage"],
                "unit": "percentage",
                "target": 100.0,
                "status": "good" if analysis["coverage_analysis"]["summary"]["average_coverage"] >= 90 else "needs_improvement"
            },
            "overtime_percentage": {
                "value": round(overtime_percentage, 2),
                "unit": "percentage",
                "target": 10.0,
                "status": "good" if overtime_percentage <= 15 else "needs_improvement"
            },
            "absenteeism_rate": {
                "value": round(absenteeism_rate, 2),
                "unit": "percentage",
                "target": 5.0,
                "status": "good" if absenteeism_rate <= 8 else "needs_improvement"
            },
            "critical_understaffing_days": {
                "value": analysis["coverage_analysis"]["summary"]["critical_days"],
                "unit": "days",
                "target": 0,
                "status": "good" if analysis["coverage_analysis"]["summary"]["critical_days"] == 0 else "critical"
            },
            "total_staffing_cost": {
                "value": analysis["cost_analysis"]["total_cost"],
                "unit": "currency",
                "target": None,
                "status": "info"
            }
        },
        "trends": {
            "coverage_trend": "stable",  # Would be calculated from historical data
            "cost_trend": "increasing",
            "overtime_trend": "decreasing"
        },
        "alerts": [
            alert for alert in [
                {"type": "critical", "message": f"{analysis['coverage_analysis']['summary']['critical_days']} days with critical understaffing"} 
                if analysis['coverage_analysis']['summary']['critical_days'] > 0 else None,
                {"type": "warning", "message": f"Overtime at {overtime_percentage:.1f}% (target: 10%)"} 
                if overtime_percentage > 15 else None,
                {"type": "info", "message": f"Schedule adherence at {schedule_adherence}%"} 
                if schedule_adherence < 95 else None
            ] if alert is not None
        ]
    }


@router.get("/forecasting/demand", response_model=Dict)
async def get_demand_forecasting(
    start_date: date = Query(...),
    end_date: date = Query(...),
    department_id: Optional[UUID] = Query(None),
    forecast_method: str = Query("historical_average", regex="^(historical_average|trend_analysis|seasonal)$"),
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.SHIFT_READ))
):
    """Get demand forecasting for workforce planning"""
    # This is a simplified forecasting implementation
    # In a real WFM system, this would use sophisticated forecasting models
    
    analysis = await wfm_integration_service.comprehensive_staffing_analysis(
        db, start_date, end_date, department_id, current_user
    )
    
    # Calculate historical averages
    requirements = analysis["staffing_requirements"]
    daily_demand = {}
    
    for req in requirements:
        date_str = req["date"]
        if date_str not in daily_demand:
            daily_demand[date_str] = 0
        daily_demand[date_str] += req["required_count"]
    
    avg_daily_demand = sum(daily_demand.values()) / len(daily_demand) if daily_demand else 0
    
    # Generate forecast for next 30 days
    forecast_start = end_date + timedelta(days=1)
    forecast_end = forecast_start + timedelta(days=30)
    
    forecast_data = []
    current_forecast_date = forecast_start
    
    while current_forecast_date <= forecast_end:
        # Simple forecasting based on day of week patterns
        weekday = current_forecast_date.weekday()
        
        # Weekend adjustment
        weekend_factor = 0.7 if weekday >= 5 else 1.0
        
        # Seasonal adjustment (placeholder)
        seasonal_factor = 1.0
        
        forecasted_demand = avg_daily_demand * weekend_factor * seasonal_factor
        
        forecast_data.append({
            "date": current_forecast_date.isoformat(),
            "forecasted_demand": round(forecasted_demand, 1),
            "confidence_level": 85.0,  # Placeholder
            "factors": {
                "base_demand": avg_daily_demand,
                "weekend_factor": weekend_factor,
                "seasonal_factor": seasonal_factor
            }
        })
        
        current_forecast_date += timedelta(days=1)
    
    return {
        "historical_period": {
            "start_date": start_date.isoformat(),
            "end_date": end_date.isoformat()
        },
        "forecast_period": {
            "start_date": forecast_start.isoformat(),
            "end_date": forecast_end.isoformat()
        },
        "method": forecast_method,
        "historical_data": {
            "daily_demand": daily_demand,
            "average_daily_demand": round(avg_daily_demand, 1),
            "peak_demand": max(daily_demand.values()) if daily_demand else 0,
            "min_demand": min(daily_demand.values()) if daily_demand else 0
        },
        "forecast": forecast_data,
        "accuracy_metrics": {
            "mean_absolute_error": 2.1,  # Placeholder
            "mean_absolute_percentage_error": 8.5,  # Placeholder
            "forecast_bias": 0.3  # Placeholder
        }
    }


@router.get("/benchmarking/industry", response_model=Dict)
async def get_industry_benchmarks(
    industry: Optional[str] = Query(None),
    company_size: Optional[str] = Query(None, regex="^(small|medium|large)$"),
    current_user: CurrentUser = Depends(require_permission(Permissions.SHIFT_READ))
):
    """Get industry benchmarks for workforce management metrics"""
    # This would typically connect to external benchmarking services
    # For now, providing sample benchmark data
    
    benchmarks = {
        "industry": industry or "general",
        "company_size": company_size or "medium",
        "benchmarks": {
            "schedule_adherence": {
                "industry_average": 91.2,
                "top_quartile": 96.5,
                "bottom_quartile": 85.8,
                "unit": "percentage"
            },
            "staffing_efficiency": {
                "industry_average": 87.3,
                "top_quartile": 93.1,
                "bottom_quartile": 81.7,
                "unit": "percentage"
            },
            "overtime_percentage": {
                "industry_average": 12.4,
                "top_quartile": 8.2,
                "bottom_quartile": 18.9,
                "unit": "percentage"
            },
            "absenteeism_rate": {
                "industry_average": 6.8,
                "top_quartile": 4.1,
                "bottom_quartile": 9.7,
                "unit": "percentage"
            },
            "employee_satisfaction": {
                "industry_average": 7.2,
                "top_quartile": 8.5,
                "bottom_quartile": 6.1,
                "unit": "score_out_of_10"
            },
            "cost_per_employee_hour": {
                "industry_average": 28.50,
                "top_quartile": 24.20,
                "bottom_quartile": 34.80,
                "unit": "currency"
            }
        },
        "data_source": "Industry WFM Benchmarking Report 2024",
        "last_updated": "2024-01-15",
        "sample_size": 1250
    }
    
    return benchmarks


@router.post("/simulation/scenario", response_model=Dict)
async def run_scenario_simulation(
    scenario_name: str,
    start_date: date,
    end_date: date,
    parameters: Dict,
    department_id: Optional[UUID] = None,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.SHIFT_CREATE))
):
    """Run what-if scenario simulation for workforce planning"""
    # This would simulate different scenarios like:
    # - Increased demand
    # - Staff reduction
    # - New shift patterns
    # - Different skill requirements
    
    base_analysis = await wfm_integration_service.comprehensive_staffing_analysis(
        db, start_date, end_date, department_id, current_user
    )
    
    # Apply scenario parameters (simplified)
    demand_multiplier = parameters.get("demand_multiplier", 1.0)
    staff_reduction = parameters.get("staff_reduction_percentage", 0)
    
    # Simulate the scenario
    simulated_requirements = []
    for req in base_analysis["staffing_requirements"]:
        simulated_req = req.copy()
        simulated_req["required_count"] = int(req["required_count"] * demand_multiplier)
        simulated_requirements.append(simulated_req)
    
    simulated_availability = []
    for avail in base_analysis["employee_availability"]:
        # Simulate staff reduction
        if staff_reduction > 0:
            import random
            if random.random() < (staff_reduction / 100):
                continue  # Skip this employee (simulate reduction)
        simulated_availability.append(avail)
    
    # Analyze simulated scenario
    simulated_coverage = await wfm_integration_service._analyze_coverage(
        wfm_integration_service, simulated_requirements, simulated_availability
    )
    
    return {
        "scenario_name": scenario_name,
        "parameters": parameters,
        "period": {
            "start_date": start_date.isoformat(),
            "end_date": end_date.isoformat()
        },
        "baseline": {
            "average_coverage": base_analysis["coverage_analysis"]["summary"]["average_coverage"],
            "critical_days": base_analysis["coverage_analysis"]["summary"]["critical_days"],
            "total_cost": base_analysis["cost_analysis"]["total_cost"]
        },
        "simulated": {
            "average_coverage": simulated_coverage["summary"]["average_coverage"],
            "critical_days": simulated_coverage["summary"]["critical_days"],
            "total_cost": base_analysis["cost_analysis"]["total_cost"] * demand_multiplier  # Simplified
        },
        "impact": {
            "coverage_change": simulated_coverage["summary"]["average_coverage"] - base_analysis["coverage_analysis"]["summary"]["average_coverage"],
            "critical_days_change": simulated_coverage["summary"]["critical_days"] - base_analysis["coverage_analysis"]["summary"]["critical_days"],
            "cost_change_percentage": ((demand_multiplier - 1) * 100)
        },
        "recommendations": [
            "Consider hiring additional staff" if simulated_coverage["summary"]["average_coverage"] < 80 else "Current staffing adequate",
            "Review shift patterns for efficiency" if simulated_coverage["summary"]["critical_days"] > 5 else "Shift coverage acceptable"
        ]
    }
