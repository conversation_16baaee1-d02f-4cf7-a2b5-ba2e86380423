from sqlalchemy import Column, String, Integer, Date, Enum, ForeignKey, Text, Boolean, Numeric, DateTime
from sqlalchemy.dialects.postgresql import UUID, JSONB
from sqlalchemy.orm import relationship, foreign
from enum import Enum as PyEnum
from datetime import date
from typing import Optional

from ..base import BaseModel, AuditMixin


class Organization(BaseModel):
    """Organization model"""
    __tablename__ = "organizations"

    # Basic Information
    name = Column(String(200), nullable=False)
    domain = Column(String(100), unique=True, nullable=True, index=True)
    description = Column(Text, nullable=True)

    # Contact Information
    email = Column(String(255), nullable=True)
    phone = Column(String(20), nullable=True)
    website = Column(String(255), nullable=True)

    # Address Information
    address_line1 = Column(String(255), nullable=True)
    address_line2 = Column(String(255), nullable=True)
    city = Column(String(100), nullable=True)
    state = Column(String(100), nullable=True)
    country = Column(String(100), nullable=True)
    postal_code = Column(String(20), nullable=True)

    # Business Information
    industry = Column(String(100), nullable=True)
    size = Column(String(50), nullable=True)  # small, medium, large, enterprise
    tax_id = Column(String(50), nullable=True)

    # Settings
    timezone = Column(String(50), nullable=False, default="UTC")
    currency = Column(String(3), nullable=False, default="USD")
    date_format = Column(String(20), nullable=False, default="YYYY-MM-DD")

    # Relationships
    employees = relationship("Employee", back_populates="organization", primaryjoin="Organization.id == foreign(Employee.organization_id)")
    departments = relationship("Department", back_populates="organization", primaryjoin="Organization.id == foreign(Department.organization_id)")


class EmployeeStatus(PyEnum):
    ACTIVE = "ACTIVE"
    INACTIVE = "INACTIVE"
    TERMINATED = "TERMINATED"
    ON_LEAVE = "ON_LEAVE"
    PROBATION = "PROBATION"
    NOTICE_PERIOD = "NOTICE_PERIOD"


class MaritalStatus(PyEnum):
    SINGLE = "single"
    MARRIED = "married"
    DIVORCED = "divorced"
    WIDOWED = "widowed"


class Gender(PyEnum):
    MALE = "male"
    FEMALE = "female"
    OTHER = "other"
    PREFER_NOT_TO_SAY = "prefer_not_to_say"


class EmploymentType(PyEnum):
    FULL_TIME = "FULL_TIME"
    PART_TIME = "PART_TIME"
    CONTRACT = "CONTRACT"
    INTERN = "INTERN"
    CONSULTANT = "CONSULTANT"


class Role(PyEnum):
    SUPERADMIN = "superadmin"
    HR = "hr"
    MANAGER = "manager"
    EMPLOYEE = "employee"
    ADMIN = "admin"


class Employee(BaseModel, AuditMixin):
    """Employee model"""
    __tablename__ = "employees"

    # Basic Information
    employee_id = Column(String(20), unique=True, nullable=False, index=True)
    first_name = Column(String(100), nullable=False)
    last_name = Column(String(100), nullable=False)
    middle_name = Column(String(100), nullable=True)
    email = Column(String(255), unique=True, nullable=False, index=True)
    phone = Column(String(20), nullable=True)
    mobile_number = Column(String(20), nullable=True)  # Added mobile number field
    alternate_phone = Column(String(20), nullable=True)

    # Personal Information
    date_of_birth = Column(Date, nullable=True)
    gender = Column(Enum(Gender), nullable=True)
    marital_status = Column(Enum(MaritalStatus), nullable=True)
    nationality = Column(String(100), nullable=True)
    blood_group = Column(String(10), nullable=True)

    # Address Information
    address_line1 = Column(String(255), nullable=True)
    address_line2 = Column(String(255), nullable=True)
    city = Column(String(100), nullable=True)
    state = Column(String(100), nullable=True)
    country = Column(String(100), nullable=True)
    postal_code = Column(String(20), nullable=True)

    # Emergency Contact
    emergency_contact_name = Column(String(200), nullable=True)
    emergency_contact_phone = Column(String(20), nullable=True)
    emergency_contact_relationship = Column(String(100), nullable=True)

    # Employment Information
    hire_date = Column(Date, nullable=False)
    termination_date = Column(Date, nullable=True)
    employment_type = Column(Enum(EmploymentType), nullable=False, default=EmploymentType.FULL_TIME)
    status = Column(Enum(EmployeeStatus), nullable=False, default=EmployeeStatus.ACTIVE)

    # Organizational Information
    organization_id = Column(UUID(as_uuid=True), nullable=False, index=True)
    department_id = Column(UUID(as_uuid=True), ForeignKey("departments.id"), nullable=True)
    designation_id = Column(UUID(as_uuid=True), ForeignKey("designations.id"), nullable=True)
    manager_id = Column(UUID(as_uuid=True), ForeignKey("employees.id"), nullable=True)

    # Work Information
    work_location = Column(String(255), nullable=True)
    timezone = Column(String(50), nullable=True, default="UTC")

    # Profile
    profile_picture_url = Column(String(500), nullable=True)
    bio = Column(Text, nullable=True)
    skills = Column(JSONB, nullable=True)  # JSON array of skills

    # System Information
    user_id = Column(UUID(as_uuid=True), nullable=True, index=True)  # Link to auth service user
    role = Column(String(50), nullable=False, default="employee")  # User role in the system

    # Relationships
    organization = relationship("Organization", back_populates="employees", primaryjoin="foreign(Employee.organization_id) == Organization.id")
    department = relationship("Department", back_populates="employees", foreign_keys=[department_id])
    designation = relationship("Designation", back_populates="employees")
    manager = relationship("Employee", remote_side="Employee.id", back_populates="subordinates")
    subordinates = relationship("Employee", back_populates="manager")
    user = relationship("User", back_populates="employee", uselist=False, primaryjoin="foreign(Employee.user_id) == User.id")

    # Attendance relationships
    attendance_records = relationship("AttendanceRecord", foreign_keys="AttendanceRecord.employee_id", back_populates="employee")
    leave_requests = relationship("LeaveRequest", foreign_keys="LeaveRequest.employee_id", back_populates="employee")
    timesheet_entries = relationship("TimesheetEntry", foreign_keys="TimesheetEntry.employee_id", back_populates="employee")

    # HR Management relationships
    managed_job_postings = relationship("JobPosting", foreign_keys="JobPosting.hiring_manager_id", back_populates="hiring_manager")
    recruited_job_postings = relationship("JobPosting", foreign_keys="JobPosting.recruiter_id", back_populates="recruiter")

    # Learning Management relationships
    taught_courses = relationship("Course", back_populates="instructor")
    course_enrollments = relationship("CourseEnrollment", foreign_keys="CourseEnrollment.employee_id", back_populates="employee")
    certifications = relationship("Certification", back_populates="employee")

    # Onboarding/Offboarding relationships
    onboarding_workflows = relationship("OnboardingWorkflow", foreign_keys="OnboardingWorkflow.employee_id", back_populates="employee")
    offboarding_workflows = relationship("OffboardingWorkflow", foreign_keys="OffboardingWorkflow.employee_id", back_populates="employee")

    # Project relationships
    project_assignments = relationship("ProjectAssignment", foreign_keys="ProjectAssignment.employee_id", back_populates="employee")
    assigned_tasks = relationship("Task", foreign_keys="Task.assignee_id", back_populates="assignee")
    reported_tasks = relationship("Task", foreign_keys="Task.reporter_id", back_populates="reporter")
    task_assignments = relationship("TaskAssignment", foreign_keys="TaskAssignment.assignee_id", back_populates="assignee")

    # Performance relationships
    performance_reviews = relationship("PerformanceReview", foreign_keys="PerformanceReview.employee_id", back_populates="employee")
    goals = relationship("Goal", foreign_keys="Goal.employee_id", back_populates="employee")

    # Payroll relationships
    payroll_records = relationship("PayrollRecord", foreign_keys="PayrollRecord.employee_id", back_populates="employee")
    payroll_histories = relationship("PayrollHistory", foreign_keys="PayrollHistory.employee_id", back_populates="employee")

    @property
    def full_name(self) -> str:
        """Get full name"""
        if self.middle_name:
            return f"{self.first_name} {self.middle_name} {self.last_name}"
        return f"{self.first_name} {self.last_name}"

    @property
    def display_name(self) -> str:
        """Get display name for UI"""
        return f"{self.full_name} ({self.employee_id})"


class Department(BaseModel):
    """Department model"""
    __tablename__ = "departments"

    name = Column(String(200), nullable=False)
    code = Column(String(20), unique=True, nullable=False, index=True)
    description = Column(Text, nullable=True)
    organization_id = Column(UUID(as_uuid=True), nullable=False, index=True)
    parent_department_id = Column(UUID(as_uuid=True), ForeignKey("departments.id"), nullable=True)
    head_id = Column(UUID(as_uuid=True), ForeignKey("employees.id"), nullable=True)

    # Relationships
    organization = relationship("Organization", back_populates="departments", primaryjoin="foreign(Department.organization_id) == Organization.id")
    employees = relationship("Employee", foreign_keys="Employee.department_id", back_populates="department")
    parent_department = relationship("Department", remote_side="Department.id", back_populates="sub_departments")
    sub_departments = relationship("Department", back_populates="parent_department")
    head = relationship("Employee", foreign_keys=[head_id])
    job_postings = relationship("JobPosting", back_populates="department")


class Designation(BaseModel):
    """Designation/Job Title model"""
    __tablename__ = "designations"

    title = Column(String(200), nullable=False)
    code = Column(String(20), unique=True, nullable=False, index=True)
    description = Column(Text, nullable=True)
    level = Column(Integer, nullable=True)  # Hierarchy level
    organization_id = Column(UUID(as_uuid=True), nullable=False, index=True)
    department_id = Column(UUID(as_uuid=True), ForeignKey("departments.id"), nullable=True)

    # Relationships
    employees = relationship("Employee", foreign_keys="Employee.designation_id", back_populates="designation")
    department = relationship("Department")
