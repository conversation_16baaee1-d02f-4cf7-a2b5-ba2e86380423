from core.models.salary_components import SalaryComponentsModel
from core.databases.database import db
from core.repositories.user import UserRepository
from sqlalchemy import desc

class SalaryComponentsRepository:
    def __init__(self) -> None:
        pass

    @classmethod
    def createSalaryComponents(self, component_name, cycle, duration, payslip_name, component_type, calculation_type, amount, value):
        salary_components = SalaryComponentsModel(
            component_name=component_name,
            payslip_name=payslip_name,
            component_type=component_type,
            calculation_type=calculation_type,
            amount=amount,
            value=value,
            duration=duration,
            cycle=cycle,
            user_id=UserRepository.authUserId()
        )
        db.session.add(salary_components)
        db.session.commit()
        return salary_components

    @classmethod
    def fetchAll(self):
        return SalaryComponentsModel.query.filter_by(user_id=UserRepository().authUserId()).order_by(SalaryComponentsModel.timestamp.desc()).all()
    
    @classmethod
    def getSalaryComponents(self, id):
        return SalaryComponentsModel.query.filter(SalaryComponentsModel.id == id).first()
    
    @classmethod
    def getSalaryComponentsByKeys(self, kwargs):
        return SalaryComponentsModel.query.filter_by(user_id=UserRepository().authUserId(), **kwargs).all()

    @classmethod
    def updateSalaryComponents(self, id, **kwargs):
        salary_components = SalaryComponentsModel.query.filter_by(id=id).first()
        if salary_components:
            for key, value in kwargs.items():
                setattr(salary_components, key, value)
            db.session.commit()
            return salary_components
        else:
            return None

    @classmethod
    def deleteSalaryComponents(self, id):
        SalaryComponentsModel.query.filter(SalaryComponentsModel.id == id).delete()
        db.session.commit()
        return
    
    def searchSalaryComponent(self, search_value, offset, size):
        to_search = f'%{search_value}%'
        return SalaryComponentsModel.query.filter(SalaryComponentsModel.name.like(to_search)) \
            .offset(offset).limit(size).all()

    def sortSalaryComponent(self, sort_key, offset, size, sort_dir: str = "asc",):
        if sort_dir == "desc":
            salaryComponents = SalaryComponentsModel.query.order_by(
                desc(getattr(SalaryComponentsModel, sort_key, "name"))
                ).offset(offset=offset).limit(limit=size).all()
        else:
            salaryComponents = SalaryComponentsModel.query.order_by(
                desc(getattr(SalaryComponentsModel, sort_key, "name"))
                ).offset(offset=offset).limit(limit=size).all()
        return salaryComponents

    def fetchSalaryComponent(self, offset: int, size: int):
        salaryComponents = SalaryComponentsModel.query.order_by(SalaryComponentsModel.created_at.desc()).offset(offset=offset).limit(limit=size).all()
        return salaryComponents
