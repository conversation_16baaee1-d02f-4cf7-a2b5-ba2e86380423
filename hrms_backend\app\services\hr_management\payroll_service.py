from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, func, extract
from typing import Optional, List, Dict, Any
from uuid import UUID, uuid4
from datetime import datetime, date, timedelta
from decimal import Decimal
from fastapi import HTTPException, status
import logging
import json
import os
import sys

from ...db.models.payroll import (
    PayrollRecord, SalaryStructure, PayrollComponent, PayrollComponentDefinition,
    TaxConfiguration, PayrollAdjustment
)
from ...db.models.employee import Employee
from ...db.models.attendance import AttendanceRecord
from ...schemas.payroll import (
    PayrollRecordCreate, PayrollRecordUpdate, PayrollRecordResponse,
    PayrollRecordListResponse, SalaryStructureCreate, SalaryStructureUpdate,
    SalaryStructureResponse, PayrollComponentCreate, PayrollComponentUpdate,
    PayrollComponentResponse, SalaryStructureCreate, SalaryStructureUpdate,
    SalaryStructureResponse, PayrollProcessingRequest, PayrollProcessingResponse,
    PayrollApprovalRequest, PayslipResponse, PayrollSummaryReport,
    BulkSalaryUpdate, PayrollStatus, ComponentType
)
from ...core.security import CurrentUser

logger = logging.getLogger(__name__)

# Import all services from the Payroll folder
payroll_path = os.path.join(os.path.dirname(__file__), '..', 'Payroll')
if payroll_path not in sys.path:
    sys.path.append(payroll_path)

try:
    from core.packages.tax_calculator.factory import TaxCalculatorFactory
    from core.services.salary_templates import SalaryTemplatesService as PayrollSalaryTemplatesService
    from core.services.benefits import BenefitsService as PayrollBenefitsService
    from core.services.paystack_service import PaystackService as PayrollPaystackService
    from core.services.component_processor import ComponentProcessor
    from core.services.payroll_history import PayrollHistoryService as PayrollHistoryServiceOriginal
    from core.services.transaction_history import TransactionHistoryService as PayrollTransactionHistoryService
    from core.services.loan_request import LoanRequestService
    from core.services.approval_settings import ApprovalSettingsService
    from core.services.prorate_salaries import ProrateSalariesService
    from core.services.working_days import WorkingDaysService
    from core.services.tax_calculator_service import TaxCalculatorService as PayrollTaxCalculatorService
    from core.services.payslip_service import PayslipService
    from core.services.email_service import EmailService
    from core.services.payroll_processings import PayrollProcessingsService
    from core.services.nhf import NHFService
    from core.services.pensions import PensionsService
    from core.services.salary_components import SalaryComponentsService as PayrollSalaryComponentsService
    PAYROLL_SERVICES_AVAILABLE = True
    logger.info("✅ Advanced Payroll services loaded successfully")
except ImportError as e:
    PAYROLL_SERVICES_AVAILABLE = False
    logger.warning(f"⚠️ Payroll services not available, using fallback implementations: {e}")

    # Create mock classes for fallback
    class MockPayrollService:
        def __init__(self, *args, **kwargs):
            pass
        def __getattr__(self, name):
            return lambda *args, **kwargs: {"error": "Service not available"}

    PayrollSalaryTemplatesService = MockPayrollService
    PayrollBenefitsService = MockPayrollService
    PayrollPaystackService = MockPayrollService
    ComponentProcessor = MockPayrollService
    PayrollHistoryServiceOriginal = MockPayrollService
    PayrollTransactionHistoryService = MockPayrollService
    LoanRequestService = MockPayrollService
    ApprovalSettingsService = MockPayrollService
    ProrateSalariesService = MockPayrollService
    WorkingDaysService = MockPayrollService
    PayrollTaxCalculatorService = MockPayrollService
    PayslipService = MockPayrollService
    EmailService = MockPayrollService
    PayrollProcessingsService = MockPayrollService
    NHFService = MockPayrollService
    PensionsService = MockPayrollService
    PayrollSalaryComponentsService = MockPayrollService
    TaxCalculatorFactory = MockPayrollService

class TaxCalculatorService:
    """Tax calculation service using the advanced tax calculator from Payroll folder"""

    def __init__(self, country: str = "Nigeria", tax_type: str = "PAYE"):
        self.country = country
        self.tax_type = tax_type
        if TAX_CALCULATOR_AVAILABLE:
            self.calculator = TaxCalculatorFactory.create_calculator(country, tax_type)
        else:
            self.calculator = None

    def calculate_annual_tax(self, annual_earnings: float, annual_pen: float = 0, annual_nhf: float = 0) -> float:
        """Calculate annual tax"""
        if self.calculator:
            return self.calculator.calculate_annual_tax(annual_earnings, annual_pen, annual_nhf)
        else:
            # Fallback basic calculation for Nigeria PAYE
            if self.country == "Nigeria" and self.tax_type == "PAYE":
                return self._basic_nigeria_paye_calculation(annual_earnings, annual_pen, annual_nhf)
            return 0.0

    def calculate_monthly_tax(self, annual_tax: float) -> float:
        """Calculate monthly tax from annual tax"""
        if self.calculator:
            return self.calculator.calculate_monthly_tax(annual_tax)
        else:
            return annual_tax / 12

    def _basic_nigeria_paye_calculation(self, annual_earnings: float, annual_pen: float, annual_nhf: float) -> float:
        """Basic Nigeria PAYE calculation as fallback"""
        # Basic tax calculation for Nigeria
        taxable_income = annual_earnings - annual_pen - annual_nhf

        # Nigeria tax bands (simplified)
        if taxable_income <= 300000:
            return taxable_income * 0.07
        elif taxable_income <= 600000:
            return 21000 + (taxable_income - 300000) * 0.11
        elif taxable_income <= 1100000:
            return 54000 + (taxable_income - 600000) * 0.15
        elif taxable_income <= 1600000:
            return 129000 + (taxable_income - 1100000) * 0.19
        elif taxable_income <= 3200000:
            return 224000 + (taxable_income - 1600000) * 0.21
        else:
            return 560000 + (taxable_income - 3200000) * 0.24

class PayrollService:
    """Payroll service for business logic"""

    async def process_payroll(
        self,
        db: Session,
        processing_request: PayrollProcessingRequest,
        current_user: CurrentUser
    ) -> PayrollProcessingResponse:
        """Process payroll for employees"""
        try:
            batch_id = uuid4()

            # Get employees to process
            if processing_request.employee_ids:
                employees = db.query(Employee).filter(
                    Employee.id.in_(processing_request.employee_ids),
                    Employee.organization_id == current_user.organization_id,
                    Employee.is_active == True
                ).all()
            else:
                employees = db.query(Employee).filter(
                    Employee.organization_id == current_user.organization_id,
                    Employee.is_active == True
                ).all()

            processed_count = 0
            failed_count = 0
            total_gross = Decimal(0)
            total_deductions = Decimal(0)
            total_net = Decimal(0)
            errors = []

            for employee in employees:
                try:
                    # Check if payroll already exists for this period
                    existing = db.query(PayrollRecord).filter(
                        PayrollRecord.employee_id == employee.id,
                        PayrollRecord.pay_period_start == processing_request.pay_period_start,
                        PayrollRecord.pay_period_end == processing_request.pay_period_end
                    ).first()

                    if existing and not processing_request.dry_run:
                        errors.append({
                            "employee_id": str(employee.id),
                            "error": "Payroll already exists for this period"
                        })
                        failed_count += 1
                        continue

                    # Get employee salary structure
                    salary = await self._get_employee_salary(db, employee.id)
                    if not salary:
                        errors.append({
                            "employee_id": str(employee.id),
                            "error": "No salary structure found"
                        })
                        failed_count += 1
                        continue

                    # Calculate attendance
                    attendance_data = await self._calculate_attendance(
                        db, employee.id, processing_request.pay_period_start, processing_request.pay_period_end
                    )

                    # Calculate payroll components
                    payroll_calculation = await self._calculate_payroll(
                        db, employee, salary, attendance_data, processing_request
                    )

                    if not processing_request.dry_run:
                        # Create payroll record
                        payroll_record = PayrollRecord(
                            employee_id=employee.id,
                            salary_structure_id=salary.salary_structure_id,
                            pay_period_start=processing_request.pay_period_start,
                            pay_period_end=processing_request.pay_period_end,
                            pay_date=processing_request.pay_date,
                            basic_salary=salary.basic_salary,
                            working_days=attendance_data["working_days"],
                            present_days=attendance_data["present_days"],
                            overtime_hours=attendance_data.get("overtime_hours", Decimal(0)),
                            overtime_rate=payroll_calculation.get("overtime_rate", Decimal(0)),
                            gross_salary=payroll_calculation["gross_salary"],
                            total_deductions=payroll_calculation["total_deductions"],
                            net_salary=payroll_calculation["net_salary"],
                            tax_amount=payroll_calculation["tax_amount"],
                            component_breakdown=payroll_calculation["component_breakdown"],
                            status=PayrollStatus.PROCESSED,
                            currency=salary.currency,
                            created_by=current_user.user_id
                        )

                        db.add(payroll_record)

                    processed_count += 1
                    total_gross += payroll_calculation["gross_salary"]
                    total_deductions += payroll_calculation["total_deductions"]
                    total_net += payroll_calculation["net_salary"]

                except Exception as e:
                    errors.append({
                        "employee_id": str(employee.id),
                        "error": str(e)
                    })
                    failed_count += 1

            if not processing_request.dry_run:
                # Create batch record
                batch = PayrollBatch(
                    id=batch_id,
                    organization_id=current_user.organization_id,
                    pay_period_start=processing_request.pay_period_start,
                    pay_period_end=processing_request.pay_period_end,
                    total_employees=len(employees),
                    processed_count=processed_count,
                    failed_count=failed_count,
                    total_gross=total_gross,
                    total_net=total_net,
                    status="completed" if failed_count == 0 else "partial",
                    created_by=current_user.user_id
                )
                db.add(batch)
                db.commit()

            logger.info(f"Payroll processing completed: {processed_count} processed, {failed_count} failed")

            return PayrollProcessingResponse(
                batch_id=batch_id,
                total_employees=len(employees),
                processed_count=processed_count,
                failed_count=failed_count,
                total_gross=total_gross,
                total_deductions=total_deductions,
                total_net=total_net,
                processing_status="completed" if failed_count == 0 else "partial",
                errors=errors if errors else None
            )

        except Exception as e:
            db.rollback()
            logger.error(f"Error processing payroll: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Error processing payroll"
            )

    # Helper methods
    async def _get_employee_salary(
        self,
        db: Session,
        employee_id: UUID
    ) -> Optional[SalaryStructure]:
        """Get current employee salary"""
        return db.query(SalaryStructure).filter(
            SalaryStructure.employee_id == employee_id,
            SalaryStructure.is_active == True,
            SalaryStructure.effective_from <= date.today()
        ).order_by(SalaryStructure.effective_from.desc()).first()

    async def _calculate_attendance(
        self,
        db: Session,
        employee_id: UUID,
        start_date: date,
        end_date: date
    ) -> Dict[str, Any]:
        """Calculate attendance data for payroll period"""
        # Get attendance records for the period
        attendance_records = db.query(AttendanceRecord).filter(
            AttendanceRecord.employee_id == employee_id,
            AttendanceRecord.date >= start_date,
            AttendanceRecord.date <= end_date
        ).all()

        # Calculate working days (excluding weekends)
        working_days = 0
        current_date = start_date
        while current_date <= end_date:
            if current_date.weekday() < 5:  # Monday to Friday
                working_days += 1
            current_date += timedelta(days=1)

        # Calculate present days and overtime
        present_days = len([r for r in attendance_records if r.status == "present"])
        total_overtime_hours = sum([r.overtime_hours or Decimal(0) for r in attendance_records])

        return {
            "working_days": working_days,
            "present_days": present_days,
            "overtime_hours": total_overtime_hours,
            "attendance_records": attendance_records
        }

    async def _calculate_payroll(
        self,
        db: Session,
        employee: Employee,
        salary: SalaryStructure,
        attendance_data: Dict[str, Any],
        processing_request: PayrollProcessingRequest
    ) -> Dict[str, Any]:
        """Calculate payroll components"""
        # Get salary structure and components
        salary_structure = db.query(SalaryStructure).filter(
            SalaryStructure.id == salary.salary_structure_id
        ).first()

        if not salary_structure:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Salary structure not found"
            )

        # Calculate basic salary based on attendance
        working_days = attendance_data["working_days"]
        present_days = attendance_data["present_days"]

        if working_days > 0:
            daily_salary = salary.basic_salary / working_days
            earned_basic = daily_salary * present_days
        else:
            earned_basic = salary.basic_salary

        # Initialize component breakdown
        component_breakdown = {
            "basic_salary": float(earned_basic)
        }

        # Calculate earnings
        total_earnings = earned_basic

        # Add overtime if applicable
        overtime_amount = Decimal(0)
        if processing_request.include_overtime and attendance_data["overtime_hours"] > 0:
            # Calculate overtime rate (typically 1.5x hourly rate)
            hourly_rate = salary.basic_salary / (working_days * 8)  # Assuming 8 hours per day
            overtime_rate = hourly_rate * Decimal(1.5)
            overtime_amount = overtime_rate * attendance_data["overtime_hours"]
            total_earnings += overtime_amount
            component_breakdown["overtime"] = float(overtime_amount)

        # Get payroll components for this salary structure
        components = db.query(PayrollComponent).filter(
            PayrollComponent.organization_id == employee.organization_id,
            PayrollComponent.is_active == True
        ).all()

        # Calculate component values
        total_deductions = Decimal(0)

        for component in components:
            if component.component_type == ComponentType.EARNING:
                if component.percentage_of_basic:
                    amount = earned_basic * (component.percentage_of_basic / 100)
                elif component.fixed_amount:
                    amount = component.fixed_amount
                else:
                    continue

                total_earnings += amount
                component_breakdown[component.code] = float(amount)

            elif component.component_type == ComponentType.DEDUCTION:
                if component.percentage_of_basic:
                    amount = earned_basic * (component.percentage_of_basic / 100)
                elif component.fixed_amount:
                    amount = component.fixed_amount
                else:
                    continue

                # Apply limits if specified
                if component.max_limit and amount > component.max_limit:
                    amount = component.max_limit
                if component.min_limit and amount < component.min_limit:
                    amount = component.min_limit

                total_deductions += amount
                component_breakdown[component.code] = float(amount)

        # Calculate tax
        tax_amount = await self._calculate_tax(db, employee.organization_id, total_earnings)
        total_deductions += tax_amount
        component_breakdown["tax"] = float(tax_amount)

        # Calculate net salary
        net_salary = total_earnings - total_deductions

        return {
            "gross_salary": total_earnings,
            "total_deductions": total_deductions,
            "net_salary": net_salary,
            "tax_amount": tax_amount,
            "overtime_rate": overtime_amount / attendance_data["overtime_hours"] if attendance_data["overtime_hours"] > 0 else Decimal(0),
            "component_breakdown": component_breakdown
        }

    async def _calculate_tax(
        self,
        db: Session,
        organization_id: UUID,
        gross_salary: Decimal
    ) -> Decimal:
        """Calculate tax based on tax slabs"""
        tax_slabs = db.query(TaxSlab).filter(
            TaxSlab.organization_id == organization_id,
            TaxSlab.is_active == True
        ).order_by(TaxSlab.min_amount).all()

        if not tax_slabs:
            return Decimal(0)

        total_tax = Decimal(0)
        remaining_amount = gross_salary

        for slab in tax_slabs:
            if remaining_amount <= 0:
                break

            # Calculate taxable amount for this slab
            if slab.max_amount:
                slab_amount = min(remaining_amount, slab.max_amount - slab.min_amount)
            else:
                slab_amount = remaining_amount

            if slab_amount > 0:
                tax_for_slab = slab_amount * (slab.tax_rate / 100)
                total_tax += tax_for_slab
                remaining_amount -= slab_amount

        return total_tax

    async def calculate_enhanced_payroll(
        self,
        db: Session,
        employee_id: UUID,
        pay_period_start: date,
        pay_period_end: date,
        current_user: CurrentUser
    ) -> Dict[str, Any]:
        """Enhanced payroll calculation using advanced tax calculator"""

        # Get employee
        employee = db.query(Employee).filter(
            Employee.id == employee_id,
            Employee.organization_id == current_user.organization_id
        ).first()

        if not employee:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Employee not found"
            )

        # Get salary structure
        salary_structure = await self._get_employee_salary(db, employee_id)
        if not salary_structure:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="No salary structure found for employee"
            )

        # Calculate attendance
        attendance_data = await self._calculate_attendance(
            db, employee_id, pay_period_start, pay_period_end
        )

        # Get base salary
        basic_salary = salary_structure.basic_salary
        gross_salary = salary_structure.gross_salary

        # Calculate prorated salary based on attendance
        working_days = (pay_period_end - pay_period_start).days + 1
        present_days = attendance_data.get('present_days', working_days)

        if working_days > 0:
            proration_factor = present_days / working_days
            prorated_basic = basic_salary * Decimal(proration_factor)
            prorated_gross = gross_salary * Decimal(proration_factor)
        else:
            prorated_basic = basic_salary
            prorated_gross = gross_salary

        # Calculate components from salary structure
        components = json.loads(salary_structure.salary_components) if salary_structure.salary_components else []

        total_earnings = prorated_basic
        total_deductions = Decimal(0)
        total_benefits = Decimal(0)
        component_details = []

        for component in components:
            component_amount = self._calculate_component_amount(
                component, prorated_gross, prorated_basic
            )

            component_details.append({
                "name": component.get("name"),
                "type": component.get("type"),
                "amount": float(component_amount),
                "is_taxable": component.get("is_taxable", True)
            })

            if component.get("type") == "earning":
                total_earnings += component_amount
            elif component.get("type") == "deduction":
                total_deductions += component_amount
            elif component.get("type") == "benefit":
                total_benefits += component_amount

        # Calculate tax using enhanced tax calculator
        tax_calculator = TaxCalculatorService(
            country=employee.country or "Nigeria",
            tax_type=employee.tax_type or "PAYE"
        )

        # Calculate annual figures for tax calculation
        annual_earnings = float(total_earnings * 12)
        annual_pension = float(total_deductions * 12)  # Simplified
        annual_nhf = 0  # Can be calculated from components

        annual_tax = tax_calculator.calculate_annual_tax(
            annual_earnings, annual_pension, annual_nhf
        )
        monthly_tax = tax_calculator.calculate_monthly_tax(annual_tax)

        # Add tax as deduction
        total_deductions += Decimal(monthly_tax)

        # Calculate net salary
        net_salary = total_earnings - total_deductions

        return {
            "employee_id": str(employee_id),
            "employee_name": f"{employee.first_name} {employee.last_name}",
            "pay_period_start": pay_period_start.isoformat(),
            "pay_period_end": pay_period_end.isoformat(),
            "basic_salary": float(basic_salary),
            "gross_salary": float(gross_salary),
            "prorated_basic": float(prorated_basic),
            "prorated_gross": float(prorated_gross),
            "total_earnings": float(total_earnings),
            "total_deductions": float(total_deductions),
            "total_benefits": float(total_benefits),
            "monthly_tax": monthly_tax,
            "annual_tax": annual_tax,
            "net_salary": float(net_salary),
            "attendance": attendance_data,
            "components": component_details,
            "proration_factor": proration_factor if working_days > 0 else 1.0
        }

    def _calculate_component_amount(
        self,
        component: Dict[str, Any],
        gross_salary: Decimal,
        basic_salary: Decimal
    ) -> Decimal:
        """Calculate component amount based on configuration"""

        calculation_method = component.get("calculation_method", "fixed")

        if calculation_method == "fixed":
            return Decimal(component.get("amount", 0))
        elif calculation_method == "percentage":
            percentage = Decimal(component.get("percentage", 0)) / 100
            base_amount = gross_salary if component.get("base") == "gross" else basic_salary
            return base_amount * percentage
        elif calculation_method == "formula":
            # For complex formulas, you can implement a formula parser
            # For now, return fixed amount
            return Decimal(component.get("amount", 0))
        else:
            return Decimal(0)

    async def generate_payslip(
        self,
        db: Session,
        payroll_record_id: UUID,
        current_user: CurrentUser
    ) -> Dict[str, Any]:
        """Generate detailed payslip"""

        # Get payroll record
        payroll_record = db.query(PayrollRecord).filter(
            PayrollRecord.id == payroll_record_id
        ).first()

        if not payroll_record:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Payroll record not found"
            )

        # Get employee details
        employee = payroll_record.employee

        # Get components
        components = db.query(PayrollComponent).filter(
            PayrollComponent.payroll_record_id == payroll_record_id
        ).all()

        # Organize components by type
        earnings = []
        deductions = []
        benefits = []

        for component in components:
            component_data = {
                "name": component.name,
                "amount": float(component.calculated_amount),
                "is_taxable": component.is_taxable
            }

            if component.component_type.value == "earning":
                earnings.append(component_data)
            elif component.component_type.value == "deduction":
                deductions.append(component_data)
            elif component.component_type.value == "benefit":
                benefits.append(component_data)

        return {
            "payslip_id": str(payroll_record_id),
            "employee": {
                "id": str(employee.id),
                "name": f"{employee.first_name} {employee.last_name}",
                "employee_id": employee.employee_id,
                "department": employee.department.name if employee.department else None,
                "designation": employee.designation.title if employee.designation else None,
                "email": employee.email
            },
            "pay_period": {
                "start_date": payroll_record.pay_period_start.isoformat(),
                "end_date": payroll_record.pay_period_end.isoformat()
            },
            "salary_details": {
                "basic_salary": float(payroll_record.basic_salary),
                "gross_salary": float(payroll_record.gross_salary),
                "net_salary": float(payroll_record.net_salary),
                "total_earnings": float(payroll_record.total_earnings),
                "total_deductions": float(payroll_record.total_deductions),
                "total_benefits": float(payroll_record.total_benefits),
                "total_taxes": float(payroll_record.total_taxes)
            },
            "components": {
                "earnings": earnings,
                "deductions": deductions,
                "benefits": benefits
            },
            "attendance": {
                "regular_hours": float(payroll_record.regular_hours or 0),
                "overtime_hours": float(payroll_record.overtime_hours or 0),
                "paid_leave_days": float(payroll_record.paid_leave_days or 0),
                "unpaid_leave_days": float(payroll_record.unpaid_leave_days or 0)
            },
            "generated_at": datetime.utcnow().isoformat(),
            "status": payroll_record.status.value
        }

    async def initiate_payment(
        self,
        db: Session,
        payroll_record_id: UUID,
        payment_method: str,
        current_user: CurrentUser
    ) -> Dict[str, Any]:
        """Initiate payment for payroll record using Paystack"""

        # Get payroll record
        payroll_record = db.query(PayrollRecord).filter(
            PayrollRecord.id == payroll_record_id
        ).first()

        if not payroll_record:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Payroll record not found"
            )

        if payroll_record.status != PayrollStatus.APPROVED:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Payroll record must be approved before payment"
            )

        # Get employee details
        employee = payroll_record.employee

        # Generate unique reference
        reference = f"PAY_{payroll_record_id}_{datetime.utcnow().strftime('%Y%m%d%H%M%S')}"

        # Prepare payment data
        payment_data = {
            "amount": int(float(payroll_record.net_salary) * 100),  # Convert to kobo
            "email": employee.email,
            "reference": reference,
            "currency": "NGN",
            "metadata": {
                "payroll_record_id": str(payroll_record_id),
                "employee_id": str(employee.id),
                "employee_name": f"{employee.first_name} {employee.last_name}",
                "pay_period": f"{payroll_record.pay_period_start} to {payroll_record.pay_period_end}"
            }
        }

        try:
            # Initialize Paystack payment
            paystack_response = await self._initialize_paystack_payment(payment_data)

            # Create transaction history record
            from ...db.models.payroll import TransactionHistory
            transaction = TransactionHistory(
                organization_id=current_user.organization_id,
                employee_id=employee.id,
                payroll_record_id=payroll_record_id,
                transaction_id=reference,
                reference_code=reference,
                amount=payroll_record.net_salary,
                currency="NGN",
                gateway="paystack",
                gateway_transaction_id=paystack_response.get("reference"),
                gateway_reference=paystack_response.get("reference"),
                status="pending",
                gateway_response=paystack_response
            )

            db.add(transaction)
            db.commit()

            return {
                "transaction_id": reference,
                "payment_url": paystack_response.get("authorization_url"),
                "reference": reference,
                "amount": float(payroll_record.net_salary),
                "status": "pending"
            }

        except Exception as e:
            logger.error(f"Failed to initiate payment: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to initiate payment"
            )

    async def _initialize_paystack_payment(self, payment_data: Dict[str, Any]) -> Dict[str, Any]:
        """Initialize payment with Paystack using the advanced Payroll folder service"""

        try:
            if PAYROLL_SERVICES_AVAILABLE:
                # Use the advanced Paystack service from Payroll folder
                paystack_service = PayrollPaystackService(
                    payroll_id=payment_data.get("metadata", {}).get("payroll_record_id"),
                    payment_gatwway="paystack",
                    net_pay=payment_data["amount"] / 100,  # Convert from kobo
                    account_number=payment_data.get("metadata", {}).get("account_number"),
                    sort_code=payment_data.get("metadata", {}).get("sort_code")
                )

                # Initialize transfer using the advanced service
                transfer_result = paystack_service.disburse_payment()

                return {
                    "status": transfer_result.get("status", True),
                    "message": transfer_result.get("message", "Transfer initiated"),
                    "data": {
                        "authorization_url": None,  # Direct transfer, no authorization needed
                        "access_code": None,
                        "reference": payment_data['reference'],
                        "transfer_response": transfer_result.get("transfer_res"),
                        "verified_bank_name": transfer_result.get("verified_bank_name"),
                        "verified_account_name": transfer_result.get("verified_account_name"),
                        "verified_account_number": transfer_result.get("verified_account_number"),
                        "transaction_date": transfer_result.get("transaction_date")
                    }
                }
            else:
                # Fallback to mock response
                return {
                    "status": True,
                    "message": "Authorization URL created",
                    "data": {
                        "authorization_url": f"https://checkout.paystack.com/mock_payment_{payment_data['reference']}",
                        "access_code": f"access_code_{payment_data['reference']}",
                        "reference": payment_data['reference']
                    }
                }

        except Exception as e:
            logger.error(f"Failed to initialize Paystack payment: {e}")
            # Return mock response on error
            return {
                "status": False,
                "message": f"Payment initialization failed: {str(e)}",
                "data": {
                    "authorization_url": None,
                    "access_code": None,
                    "reference": payment_data['reference']
                }
            }

    async def get_payment_status(
        self,
        db: Session,
        transaction_id: str,
        current_user: CurrentUser
    ) -> Dict[str, Any]:
        """Get payment status"""

        from ...db.models.payroll import TransactionHistory
        transaction = db.query(TransactionHistory).filter(
            TransactionHistory.transaction_id == transaction_id,
            TransactionHistory.organization_id == current_user.organization_id
        ).first()

        if not transaction:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Transaction not found"
            )

        # In a real implementation, you would check with Paystack API
        # For now, return the stored status
        return {
            "transaction_id": transaction.transaction_id,
            "reference": transaction.reference_code,
            "amount": float(transaction.amount),
            "currency": transaction.currency,
            "status": transaction.status,
            "gateway": transaction.gateway,
            "initiated_at": transaction.initiated_at.isoformat(),
            "completed_at": transaction.completed_at.isoformat() if transaction.completed_at else None
        }

    async def handle_payment_webhook(
        self,
        db: Session,
        webhook_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Handle payment gateway webhooks"""

        try:
            # Extract webhook data
            event = webhook_data.get("event")
            data = webhook_data.get("data", {})
            reference = data.get("reference")

            if not reference:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Invalid webhook data"
                )

            # Find transaction
            from ...db.models.payroll import TransactionHistory
            transaction = db.query(TransactionHistory).filter(
                TransactionHistory.reference_code == reference
            ).first()

            if not transaction:
                logger.warning(f"Transaction not found for reference: {reference}")
                return {"status": "ignored", "message": "Transaction not found"}

            # Update transaction status based on webhook event
            if event == "charge.success":
                transaction.status = "success"
                transaction.completed_at = datetime.utcnow()

                # Update payroll record status
                payroll_record = db.query(PayrollRecord).filter(
                    PayrollRecord.id == transaction.payroll_record_id
                ).first()

                if payroll_record:
                    payroll_record.status = PayrollStatus.PAID
                    payroll_record.paid_at = datetime.utcnow()
                    payroll_record.payment_reference = reference

            elif event == "charge.failed":
                transaction.status = "failed"
                transaction.completed_at = datetime.utcnow()

            # Update gateway response
            transaction.gateway_response = webhook_data

            db.commit()

            return {
                "status": "processed",
                "message": f"Webhook processed for {reference}"
            }

        except Exception as e:
            logger.error(f"Failed to process webhook: {str(e)}")
            db.rollback()
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to process webhook"
            )

    async def get_salary_templates(
        self,
        db: Session,
        skip: int,
        limit: int,
        current_user: CurrentUser
    ) -> Dict[str, Any]:
        """Get salary templates"""

        # This would integrate with the Payroll folder's salary templates
        # For now, return mock data
        return {
            "templates": [
                {
                    "id": "template_1",
                    "template_name": "Standard Employee",
                    "description": "Standard salary template for regular employees",
                    "gross_pay": 150000,
                    "employment_type": "Full-time",
                    "currency": "NGN"
                },
                {
                    "id": "template_2",
                    "template_name": "Senior Developer",
                    "description": "Salary template for senior developers",
                    "gross_pay": 300000,
                    "employment_type": "Full-time",
                    "currency": "NGN"
                }
            ],
            "total": 2,
            "skip": skip,
            "limit": limit
        }

    async def create_salary_template(
        self,
        db: Session,
        template_data: Dict[str, Any],
        current_user: CurrentUser
    ) -> Dict[str, Any]:
        """Create new salary template"""

        # This would integrate with the Payroll folder's template creation
        # For now, return success response
        return {
            "id": f"template_{uuid4()}",
            "message": "Salary template created successfully",
            "template": template_data
        }

    async def get_salary_template_by_id(
        self,
        db: Session,
        template_id: UUID,
        current_user: CurrentUser
    ) -> Dict[str, Any]:
        """Get salary template by ID"""

        # Mock implementation
        return {
            "id": str(template_id),
            "template_name": "Sample Template",
            "description": "Sample salary template",
            "gross_pay": 200000,
            "employment_type": "Full-time",
            "currency": "NGN"
        }

    async def update_salary_template(
        self,
        db: Session,
        template_id: UUID,
        template_data: Dict[str, Any],
        current_user: CurrentUser
    ) -> Dict[str, Any]:
        """Update salary template"""

        # Mock implementation
        return {
            "id": str(template_id),
            "message": "Salary template updated successfully",
            "template": template_data
        }

    async def delete_salary_template(
        self,
        db: Session,
        template_id: UUID,
        current_user: CurrentUser
    ) -> Dict[str, Any]:
        """Delete salary template"""

        # Mock implementation
        return {
            "message": "Salary template deleted successfully"
        }

    async def get_employee_payroll_history(
        self,
        db: Session,
        employee_id: UUID,
        skip: int,
        limit: int,
        start_date: Optional[date],
        end_date: Optional[date],
        current_user: CurrentUser
    ) -> Dict[str, Any]:
        """Get employee payroll history"""

        query = db.query(PayrollRecord).filter(
            PayrollRecord.employee_id == employee_id
        )

        if start_date:
            query = query.filter(PayrollRecord.pay_period_start >= start_date)
        if end_date:
            query = query.filter(PayrollRecord.pay_period_end <= end_date)

        total = query.count()
        records = query.offset(skip).limit(limit).all()

        return {
            "records": [
                {
                    "id": str(record.id),
                    "pay_period_start": record.pay_period_start.isoformat(),
                    "pay_period_end": record.pay_period_end.isoformat(),
                    "gross_salary": float(record.gross_salary),
                    "net_salary": float(record.net_salary),
                    "total_deductions": float(record.total_deductions),
                    "status": record.status.value,
                    "paid_at": record.paid_at.isoformat() if record.paid_at else None
                }
                for record in records
            ],
            "total": total,
            "skip": skip,
            "limit": limit
        }

    async def get_payroll_components(
        self,
        db: Session,
        component_type: Optional[ComponentType],
        skip: int,
        limit: int,
        current_user: CurrentUser
    ) -> Dict[str, Any]:
        """Get payroll component definitions"""

        query = db.query(PayrollComponentDefinition).filter(
            PayrollComponentDefinition.organization_id == current_user.organization_id
        )

        if component_type:
            query = query.filter(PayrollComponentDefinition.component_type == component_type)

        total = query.count()
        components = query.offset(skip).limit(limit).all()

        return {
            "components": [
                {
                    "id": str(component.id),
                    "name": component.name,
                    "code": component.code,
                    "component_type": component.component_type.value,
                    "calculation_method": component.calculation_method,
                    "default_amount": float(component.default_amount) if component.default_amount else None,
                    "default_percentage": float(component.default_percentage) if component.default_percentage else None,
                    "is_taxable": component.is_taxable,
                    "is_active": component.is_active
                }
                for component in components
            ],
            "total": total,
            "skip": skip,
            "limit": limit
        }

    async def create_payroll_component(
        self,
        db: Session,
        component_data: PayrollComponentCreate,
        current_user: CurrentUser
    ) -> Dict[str, Any]:
        """Create new payroll component definition"""

        component = PayrollComponentDefinition(
            organization_id=current_user.organization_id,
            name=component_data.name,
            code=component_data.code,
            description=component_data.description,
            component_type=component_data.component_type,
            calculation_method=component_data.calculation_method,
            default_amount=component_data.default_amount,
            default_percentage=component_data.default_percentage,
            is_taxable=component_data.is_taxable,
            effective_from=component_data.effective_from or date.today()
        )

        db.add(component)
        db.commit()
        db.refresh(component)

        return {
            "id": str(component.id),
            "name": component.name,
            "code": component.code,
            "component_type": component.component_type.value,
            "message": "Payroll component created successfully"
        }

    async def generate_payroll_summary_report(
        self,
        db: Session,
        start_date: date,
        end_date: date,
        department_id: Optional[UUID],
        current_user: CurrentUser
    ) -> Dict[str, Any]:
        """Generate comprehensive payroll summary report"""

        # Base query for payroll records in the date range
        query = db.query(PayrollRecord).filter(
            PayrollRecord.pay_period_start >= start_date,
            PayrollRecord.pay_period_end <= end_date
        )

        # Filter by department if specified
        if department_id:
            query = query.join(Employee).filter(Employee.department_id == department_id)

        payroll_records = query.all()

        # Calculate summary statistics
        total_employees = len(set(record.employee_id for record in payroll_records))
        total_gross_salary = sum(float(record.gross_salary) for record in payroll_records)
        total_net_salary = sum(float(record.net_salary) for record in payroll_records)
        total_deductions = sum(float(record.total_deductions) for record in payroll_records)
        total_taxes = sum(float(record.total_taxes) for record in payroll_records)
        total_benefits = sum(float(record.total_benefits) for record in payroll_records)

        # Calculate averages
        avg_gross_salary = total_gross_salary / total_employees if total_employees > 0 else 0
        avg_net_salary = total_net_salary / total_employees if total_employees > 0 else 0

        # Department breakdown
        department_breakdown = {}
        for record in payroll_records:
            dept_name = record.employee.department.name if record.employee.department else "No Department"
            if dept_name not in department_breakdown:
                department_breakdown[dept_name] = {
                    "employee_count": 0,
                    "total_gross": 0,
                    "total_net": 0,
                    "total_deductions": 0
                }

            department_breakdown[dept_name]["employee_count"] += 1
            department_breakdown[dept_name]["total_gross"] += float(record.gross_salary)
            department_breakdown[dept_name]["total_net"] += float(record.net_salary)
            department_breakdown[dept_name]["total_deductions"] += float(record.total_deductions)

        # Status breakdown
        status_breakdown = {}
        for record in payroll_records:
            status = record.status.value
            if status not in status_breakdown:
                status_breakdown[status] = 0
            status_breakdown[status] += 1

        # Top earners
        top_earners = sorted(payroll_records, key=lambda x: x.gross_salary, reverse=True)[:10]

        return {
            "report_period": {
                "start_date": start_date.isoformat(),
                "end_date": end_date.isoformat()
            },
            "summary": {
                "total_employees": total_employees,
                "total_gross_salary": total_gross_salary,
                "total_net_salary": total_net_salary,
                "total_deductions": total_deductions,
                "total_taxes": total_taxes,
                "total_benefits": total_benefits,
                "average_gross_salary": avg_gross_salary,
                "average_net_salary": avg_net_salary,
                "deduction_percentage": (total_deductions / total_gross_salary * 100) if total_gross_salary > 0 else 0,
                "tax_percentage": (total_taxes / total_gross_salary * 100) if total_gross_salary > 0 else 0
            },
            "department_breakdown": department_breakdown,
            "status_breakdown": status_breakdown,
            "top_earners": [
                {
                    "employee_name": f"{record.employee.first_name} {record.employee.last_name}",
                    "employee_id": record.employee.employee_id,
                    "department": record.employee.department.name if record.employee.department else "N/A",
                    "gross_salary": float(record.gross_salary),
                    "net_salary": float(record.net_salary)
                }
                for record in top_earners
            ],
            "generated_at": datetime.utcnow().isoformat()
        }

    async def generate_tax_report(
        self,
        db: Session,
        start_date: date,
        end_date: date,
        current_user: CurrentUser
    ) -> Dict[str, Any]:
        """Generate tax compliance report"""

        # Get payroll records for the period
        payroll_records = db.query(PayrollRecord).filter(
            PayrollRecord.pay_period_start >= start_date,
            PayrollRecord.pay_period_end <= end_date
        ).all()

        # Calculate tax totals
        total_income_tax = sum(float(record.total_taxes) for record in payroll_records)
        total_gross_income = sum(float(record.gross_salary) for record in payroll_records)

        # Tax by employee
        employee_tax_breakdown = []
        for record in payroll_records:
            employee_tax_breakdown.append({
                "employee_id": record.employee.employee_id,
                "employee_name": f"{record.employee.first_name} {record.employee.last_name}",
                "gross_salary": float(record.gross_salary),
                "taxable_income": float(record.gross_salary - record.total_deductions + record.total_taxes),
                "tax_amount": float(record.total_taxes),
                "tax_rate": (float(record.total_taxes) / float(record.gross_salary) * 100) if record.gross_salary > 0 else 0,
                "pay_period": f"{record.pay_period_start} to {record.pay_period_end}"
            })

        # Monthly breakdown
        monthly_breakdown = {}
        for record in payroll_records:
            month_key = record.pay_period_start.strftime("%Y-%m")
            if month_key not in monthly_breakdown:
                monthly_breakdown[month_key] = {
                    "total_gross": 0,
                    "total_tax": 0,
                    "employee_count": 0
                }

            monthly_breakdown[month_key]["total_gross"] += float(record.gross_salary)
            monthly_breakdown[month_key]["total_tax"] += float(record.total_taxes)
            monthly_breakdown[month_key]["employee_count"] += 1

        return {
            "report_period": {
                "start_date": start_date.isoformat(),
                "end_date": end_date.isoformat()
            },
            "tax_summary": {
                "total_income_tax": total_income_tax,
                "total_gross_income": total_gross_income,
                "average_tax_rate": (total_income_tax / total_gross_income * 100) if total_gross_income > 0 else 0,
                "total_employees": len(payroll_records)
            },
            "monthly_breakdown": monthly_breakdown,
            "employee_breakdown": employee_tax_breakdown,
            "compliance_notes": [
                "Ensure all tax deductions are filed with relevant authorities",
                "Verify employee tax certificates are up to date",
                "Submit monthly tax returns by the 10th of following month"
            ],
            "generated_at": datetime.utcnow().isoformat()
        }

    async def generate_department_analysis(
        self,
        db: Session,
        start_date: date,
        end_date: date,
        current_user: CurrentUser
    ) -> Dict[str, Any]:
        """Generate department-wise payroll analysis"""

        # Get payroll records with department information
        payroll_records = db.query(PayrollRecord).join(Employee).filter(
            PayrollRecord.pay_period_start >= start_date,
            PayrollRecord.pay_period_end <= end_date
        ).all()

        # Group by department
        department_analysis = {}

        for record in payroll_records:
            dept_name = record.employee.department.name if record.employee.department else "No Department"

            if dept_name not in department_analysis:
                department_analysis[dept_name] = {
                    "employee_count": 0,
                    "total_gross": 0,
                    "total_net": 0,
                    "total_deductions": 0,
                    "total_taxes": 0,
                    "employees": [],
                    "avg_salary": 0,
                    "salary_range": {"min": float('inf'), "max": 0}
                }

            dept_data = department_analysis[dept_name]
            dept_data["employee_count"] += 1
            dept_data["total_gross"] += float(record.gross_salary)
            dept_data["total_net"] += float(record.net_salary)
            dept_data["total_deductions"] += float(record.total_deductions)
            dept_data["total_taxes"] += float(record.total_taxes)

            # Track salary range
            gross_salary = float(record.gross_salary)
            dept_data["salary_range"]["min"] = min(dept_data["salary_range"]["min"], gross_salary)
            dept_data["salary_range"]["max"] = max(dept_data["salary_range"]["max"], gross_salary)

            dept_data["employees"].append({
                "employee_id": record.employee.employee_id,
                "name": f"{record.employee.first_name} {record.employee.last_name}",
                "designation": record.employee.designation.title if record.employee.designation else "N/A",
                "gross_salary": gross_salary,
                "net_salary": float(record.net_salary)
            })

        # Calculate averages and finalize data
        for dept_name, dept_data in department_analysis.items():
            if dept_data["employee_count"] > 0:
                dept_data["avg_salary"] = dept_data["total_gross"] / dept_data["employee_count"]
                if dept_data["salary_range"]["min"] == float('inf'):
                    dept_data["salary_range"]["min"] = 0

        # Calculate department cost percentages
        total_payroll_cost = sum(dept["total_gross"] for dept in department_analysis.values())

        for dept_data in department_analysis.values():
            dept_data["cost_percentage"] = (dept_data["total_gross"] / total_payroll_cost * 100) if total_payroll_cost > 0 else 0

        return {
            "report_period": {
                "start_date": start_date.isoformat(),
                "end_date": end_date.isoformat()
            },
            "overall_summary": {
                "total_departments": len(department_analysis),
                "total_payroll_cost": total_payroll_cost,
                "total_employees": sum(dept["employee_count"] for dept in department_analysis.values())
            },
            "department_analysis": department_analysis,
            "cost_distribution": {
                dept_name: {
                    "cost": dept_data["total_gross"],
                    "percentage": dept_data["cost_percentage"]
                }
                for dept_name, dept_data in department_analysis.items()
            },
            "generated_at": datetime.utcnow().isoformat()
        }

    def validatePayrollData(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Validate payroll data"""
        errors = []

        if not data.get('employee_id'):
            errors.append('Employee ID is required')

        if not data.get('pay_period_start'):
            errors.append('Pay period start date is required')

        if not data.get('pay_period_end'):
            errors.append('Pay period end date is required')

        if data.get('basic_salary') and data.get('basic_salary') <= 0:
            errors.append('Basic salary must be greater than 0')

        return {
            "isValid": len(errors) == 0,
            "errors": errors
        }

    # ============ ADVANCED PAYROLL FOLDER INTEGRATION ============

    async def get_advanced_salary_templates(
        self,
        db: Session,
        skip: int = 0,
        limit: int = 20,
        current_user: CurrentUser = None
    ) -> Dict[str, Any]:
        """Get salary templates using the advanced Payroll folder service"""

        if not PAYROLL_SERVICES_AVAILABLE:
            return await self.get_salary_templates(db, skip, limit, current_user)

        try:
            payroll_service = PayrollSalaryTemplatesService()
            templates, total = payroll_service.fetchAll()

            # Convert to our format
            template_list = []
            for template in templates[skip:skip+limit]:
                template_list.append({
                    "id": str(template.id),
                    "template_name": template.template_name,
                    "description": template.description,
                    "employment_type": template.employment_type,
                    "employee_type": template.employee_type,
                    "level": template.level,
                    "salary_type": template.salary_type,
                    "country": template.country,
                    "currency": template.currency,
                    "work_schedule": template.work_schedule,
                    "hours_worked": float(template.hours_worked) if template.hours_worked else None,
                    "tax_type": template.tax_type,
                    "gross_pay": float(template.gross_pay),
                    "basic_salary": float(template.basic_salary),
                    "monthly_tax": float(template.monthly_tax) if template.monthly_tax else None,
                    "annual_tax": float(template.annual_tax) if template.annual_tax else None,
                    "netpay": float(template.netpay) if template.netpay else None,
                    "is_active": template.is_active,
                    "created_at": template.created_at.isoformat() if template.created_at else None
                })

            return {
                "templates": template_list,
                "total": total,
                "skip": skip,
                "limit": limit
            }

        except Exception as e:
            logger.error(f"Failed to get advanced salary templates: {e}")
            return await self.get_salary_templates(db, skip, limit, current_user)

    async def create_advanced_salary_template(
        self,
        db: Session,
        template_data: Dict[str, Any],
        current_user: CurrentUser
    ) -> Dict[str, Any]:
        """Create salary template using advanced Payroll folder service"""

        if not PAYROLL_SERVICES_AVAILABLE:
            return await self.create_salary_template(db, template_data, current_user)

        try:
            payroll_service = PayrollSalaryTemplatesService()

            # Prepare data for Payroll folder format
            payroll_data = {
                "template_name": template_data.get("template_name"),
                "description": template_data.get("description"),
                "employment_type": template_data.get("employment_type"),
                "employee_type": template_data.get("employee_type"),
                "level": template_data.get("level"),
                "salary_type": template_data.get("salary_type", "monthly"),
                "country": template_data.get("country", "Nigeria"),
                "currency": template_data.get("currency", "NGN"),
                "work_schedule": template_data.get("work_schedule"),
                "hours_worked": template_data.get("hours_worked"),
                "tax_type": template_data.get("tax_type", "PAYE"),
                "gross_pay": template_data.get("gross_pay"),
                "basic_salary": template_data.get("basic_salary"),
                "is_active": template_data.get("is_active", True)
            }

            # Add component and benefit IDs if provided
            if "salary_components_id" in template_data:
                payroll_data["salary_components_id"] = template_data["salary_components_id"]
            if "benefits_id" in template_data:
                payroll_data["benefits_id"] = template_data["benefits_id"]

            template = payroll_service.save_salary_template(payroll_data)

            return {
                "id": str(template.id),
                "template_name": template.template_name,
                "message": "Advanced salary template created successfully",
                "template": payroll_data
            }

        except Exception as e:
            logger.error(f"Failed to create advanced salary template: {e}")
            return await self.create_salary_template(db, template_data, current_user)

    async def get_advanced_benefits(
        self,
        db: Session,
        skip: int = 0,
        limit: int = 20,
        current_user: CurrentUser = None
    ) -> Dict[str, Any]:
        """Get benefits using advanced Payroll folder service"""

        if not PAYROLL_SERVICES_AVAILABLE:
            return {"benefits": [], "total": 0, "skip": skip, "limit": limit}

        try:
            payroll_service = PayrollBenefitsService()
            benefits, total = payroll_service.fetchAll()

            benefit_list = []
            for benefit in benefits[skip:skip+limit]:
                benefit_list.append({
                    "id": str(benefit.id),
                    "name": benefit.name,
                    "description": benefit.description,
                    "amount": float(benefit.amount) if benefit.amount else None,
                    "percentage": float(benefit.percentage) if benefit.percentage else None,
                    "is_taxable": benefit.is_taxable,
                    "is_active": benefit.is_active,
                    "created_at": benefit.created_at.isoformat() if benefit.created_at else None
                })

            return {
                "benefits": benefit_list,
                "total": total,
                "skip": skip,
                "limit": limit
            }

        except Exception as e:
            logger.error(f"Failed to get advanced benefits: {e}")
            return {"benefits": [], "total": 0, "skip": skip, "limit": limit}

    async def process_advanced_payroll(
        self,
        db: Session,
        payroll_ids: List[str],
        payment_gateway_id: Optional[str] = None,
        current_user: CurrentUser = None
    ) -> Dict[str, Any]:
        """Process payroll using advanced Payroll folder service"""

        if not PAYROLL_SERVICES_AVAILABLE:
            return {"message": "Advanced payroll processing not available"}

        try:
            # Use the advanced payroll processing service
            payroll_service = PayrollHistoryServiceOriginal()
            approval_service = ApprovalSettingsService()

            # Validate approval requirements
            approval_service.validate_payroll_approval_requirements(payroll_ids)

            # Process payroll
            process_response, verified_payroll_ids = payroll_service.process_payroll(payroll_ids)

            # Initialize payment processing if gateway provided
            success_transactions = []
            failed_transactions = []

            if payment_gateway_id and verified_payroll_ids:
                for payroll_id in verified_payroll_ids:
                    try:
                        # Get employee payroll data
                        employee_data = self._get_employee_payroll_data(payroll_id)
                        if employee_data:
                            # Initialize Paystack payment
                            paystack_service = PayrollPaystackService(
                                payroll_id=payroll_id,
                                payment_gatwway=payment_gateway_id,
                                net_pay=employee_data.get("net_pay"),
                                prorated_net=employee_data.get("prorated_net"),
                                prorated_status=employee_data.get("prorated_status"),
                                account_number=employee_data.get("account_number"),
                                sort_code=employee_data.get("sort_code")
                            )

                            payment_result = paystack_service.initialize_transfer()
                            if payment_result.get("status"):
                                success_transactions.append({
                                    "payroll_id": payroll_id,
                                    "transaction_id": payment_result.get("reference"),
                                    "amount": employee_data.get("net_pay")
                                })
                            else:
                                failed_transactions.append({
                                    "payroll_id": payroll_id,
                                    "error": payment_result.get("message")
                                })
                    except Exception as e:
                        failed_transactions.append({
                            "payroll_id": payroll_id,
                            "error": str(e)
                        })

            return {
                "processed_payroll": f"{process_response} records updated successfully",
                "verified_payroll_ids": verified_payroll_ids,
                "payment_results": {
                    "success_transactions": success_transactions,
                    "failed_transactions": failed_transactions
                },
                "total_processed": len(verified_payroll_ids),
                "total_payment_success": len(success_transactions),
                "total_payment_failed": len(failed_transactions)
            }

        except Exception as e:
            logger.error(f"Failed to process advanced payroll: {e}")
            return {
                "error": "Failed to process payroll",
                "message": str(e)
            }

    def _get_employee_payroll_data(self, payroll_id: str) -> Optional[Dict[str, Any]]:
        """Get employee payroll data for payment processing"""
        # This would fetch the actual employee payroll data
        # For now, return mock data
        return {
            "net_pay": 150000,
            "prorated_net": None,
            "prorated_status": False,
            "account_number": "**********",
            "sort_code": "123456"
        }

    async def get_loan_requests(
        self,
        db: Session,
        skip: int = 0,
        limit: int = 20,
        status: Optional[str] = None,
        current_user: CurrentUser = None
    ) -> Dict[str, Any]:
        """Get loan requests using Payroll folder service"""

        if not PAYROLL_SERVICES_AVAILABLE:
            return {"loan_requests": [], "total": 0, "skip": skip, "limit": limit}

        try:
            loan_service = LoanRequestService()
            loans = loan_service.fetchAll()

            # Filter by status if provided
            if status:
                loans = [loan for loan in loans if loan.status == status]

            total = len(loans)
            loan_list = []

            for loan in loans[skip:skip+limit]:
                loan_list.append({
                    "id": str(loan.id),
                    "employee_id": str(loan.employee_id),
                    "employee_name": f"{loan.employee.first_name} {loan.employee.last_name}" if loan.employee else "N/A",
                    "loan_amount": float(loan.loan_amount),
                    "monthly_deduction": float(loan.monthly_deduction) if loan.monthly_deduction else None,
                    "loan_duration": loan.loan_duration,
                    "interest_rate": float(loan.interest_rate) if loan.interest_rate else None,
                    "status": loan.status,
                    "reason": loan.reason,
                    "requested_date": loan.requested_date.isoformat() if loan.requested_date else None,
                    "approved_date": loan.approved_date.isoformat() if loan.approved_date else None,
                    "is_active": loan.is_active
                })

            return {
                "loan_requests": loan_list,
                "total": total,
                "skip": skip,
                "limit": limit
            }

        except Exception as e:
            logger.error(f"Failed to get loan requests: {e}")
            return {"loan_requests": [], "total": 0, "skip": skip, "limit": limit}

    async def create_loan_request(
        self,
        db: Session,
        loan_data: Dict[str, Any],
        current_user: CurrentUser
    ) -> Dict[str, Any]:
        """Create loan request using Payroll folder service"""

        if not PAYROLL_SERVICES_AVAILABLE:
            return {"error": "Loan request service not available"}

        try:
            loan_service = LoanRequestService()

            loan_request = loan_service.createLoanRequest(loan_data)

            return {
                "id": str(loan_request.id),
                "loan_amount": float(loan_request.loan_amount),
                "status": loan_request.status,
                "message": "Loan request created successfully"
            }

        except Exception as e:
            logger.error(f"Failed to create loan request: {e}")
            return {"error": f"Failed to create loan request: {str(e)}"}

    async def approve_loan_request(
        self,
        db: Session,
        loan_id: str,
        approval_data: Dict[str, Any],
        current_user: CurrentUser
    ) -> Dict[str, Any]:
        """Approve/reject loan request using Payroll folder service"""

        if not PAYROLL_SERVICES_AVAILABLE:
            return {"error": "Loan approval service not available"}

        try:
            loan_service = LoanRequestService()

            updated_loan = loan_service.updateLoanRequest(loan_id, approval_data)

            return {
                "id": str(updated_loan.id),
                "status": updated_loan.status,
                "message": f"Loan request {approval_data.get('status', 'updated')} successfully"
            }

        except Exception as e:
            logger.error(f"Failed to approve loan request: {e}")
            return {"error": f"Failed to approve loan request: {str(e)}"}

    async def calculate_proration(
        self,
        db: Session,
        employee_id: UUID,
        start_date: date,
        end_date: date,
        current_user: CurrentUser
    ) -> Dict[str, Any]:
        """Calculate prorated salary using Payroll folder service"""

        if not PAYROLL_SERVICES_AVAILABLE:
            return {"error": "Proration service not available"}

        try:
            proration_service = ProrateSalariesService()
            working_days_service = WorkingDaysService()

            # Get working days for the period
            working_days = working_days_service.get_working_days_for_period(start_date, end_date)

            # Calculate proration
            proration_result = proration_service.calculate_proration(
                employee_id=str(employee_id),
                start_date=start_date,
                end_date=end_date,
                working_days=working_days
            )

            return {
                "employee_id": str(employee_id),
                "period": {
                    "start_date": start_date.isoformat(),
                    "end_date": end_date.isoformat()
                },
                "working_days": working_days,
                "proration_factor": proration_result.get("proration_factor"),
                "prorated_salary": proration_result.get("prorated_salary"),
                "original_salary": proration_result.get("original_salary"),
                "days_worked": proration_result.get("days_worked"),
                "total_days": proration_result.get("total_days")
            }

        except Exception as e:
            logger.error(f"Failed to calculate proration: {e}")
            return {"error": f"Failed to calculate proration: {str(e)}"}

    async def generate_advanced_payslip(
        self,
        db: Session,
        payroll_record_id: UUID,
        current_user: CurrentUser
    ) -> Dict[str, Any]:
        """Generate payslip using advanced Payroll folder service"""

        if not PAYROLL_SERVICES_AVAILABLE:
            return await self.generate_payslip(db, payroll_record_id, current_user)

        try:
            payslip_service = PayslipService()

            # Generate payslip using the advanced service
            payslip_data = payslip_service.generate_payslip(str(payroll_record_id))

            return {
                "payslip_id": str(payroll_record_id),
                "employee": payslip_data.get("employee_details"),
                "pay_period": payslip_data.get("pay_period"),
                "salary_details": payslip_data.get("salary_breakdown"),
                "components": payslip_data.get("components"),
                "deductions": payslip_data.get("deductions"),
                "benefits": payslip_data.get("benefits"),
                "tax_details": payslip_data.get("tax_calculations"),
                "net_pay": payslip_data.get("net_pay"),
                "generated_at": datetime.utcnow().isoformat(),
                "payslip_url": payslip_data.get("payslip_url")
            }

        except Exception as e:
            logger.error(f"Failed to generate advanced payslip: {e}")
            return await self.generate_payslip(db, payroll_record_id, current_user)

    async def send_payslip_email(
        self,
        db: Session,
        payroll_record_id: UUID,
        current_user: CurrentUser
    ) -> Dict[str, Any]:
        """Send payslip via email using Payroll folder service"""

        if not PAYROLL_SERVICES_AVAILABLE:
            return {"error": "Email service not available"}

        try:
            email_service = EmailService()
            payslip_service = PayslipService()

            # Generate payslip
            payslip_data = payslip_service.generate_payslip(str(payroll_record_id))

            # Send email
            email_result = email_service.send_payslip_email(
                employee_email=payslip_data.get("employee_email"),
                employee_name=payslip_data.get("employee_name"),
                payslip_url=payslip_data.get("payslip_url"),
                pay_period=payslip_data.get("pay_period")
            )

            return {
                "payroll_record_id": str(payroll_record_id),
                "email_sent": email_result.get("success", False),
                "email_address": payslip_data.get("employee_email"),
                "message": email_result.get("message", "Email sent successfully")
            }

        except Exception as e:
            logger.error(f"Failed to send payslip email: {e}")
            return {"error": f"Failed to send payslip email: {str(e)}"}

    async def get_nhf_calculations(
        self,
        db: Session,
        employee_id: Optional[UUID] = None,
        current_user: CurrentUser = None
    ) -> Dict[str, Any]:
        """Get NHF calculations using Payroll folder service"""

        if not PAYROLL_SERVICES_AVAILABLE:
            return {"error": "NHF service not available"}

        try:
            nhf_service = NHFService()

            if employee_id:
                nhf_data = nhf_service.getNHF(str(employee_id))
                return {
                    "employee_id": str(employee_id),
                    "nhf_amount": float(nhf_data.amount) if nhf_data else 0,
                    "nhf_percentage": float(nhf_data.percentage) if nhf_data else 2.5,
                    "is_active": nhf_data.is_active if nhf_data else False
                }
            else:
                nhf_list = nhf_service.fetchAll()
                return {
                    "nhf_records": [
                        {
                            "id": str(nhf.id),
                            "employee_id": str(nhf.employee_id),
                            "amount": float(nhf.amount),
                            "percentage": float(nhf.percentage),
                            "is_active": nhf.is_active
                        }
                        for nhf in nhf_list
                    ],
                    "total": len(nhf_list)
                }

        except Exception as e:
            logger.error(f"Failed to get NHF calculations: {e}")
            return {"error": f"Failed to get NHF calculations: {str(e)}"}

    async def get_pension_calculations(
        self,
        db: Session,
        employee_id: Optional[UUID] = None,
        current_user: CurrentUser = None
    ) -> Dict[str, Any]:
        """Get pension calculations using Payroll folder service"""

        if not PAYROLL_SERVICES_AVAILABLE:
            return {"error": "Pension service not available"}

        try:
            pension_service = PensionsService()

            if employee_id:
                pension_data = pension_service.getPensions(str(employee_id))
                return {
                    "employee_id": str(employee_id),
                    "pension_amount": float(pension_data.amount) if pension_data else 0,
                    "pension_percentage": float(pension_data.percentage) if pension_data else 8.0,
                    "employer_contribution": float(pension_data.employer_contribution) if pension_data else 0,
                    "employee_contribution": float(pension_data.employee_contribution) if pension_data else 0,
                    "is_active": pension_data.is_active if pension_data else False
                }
            else:
                pension_list = pension_service.fetchAll()
                return {
                    "pension_records": [
                        {
                            "id": str(pension.id),
                            "employee_id": str(pension.employee_id),
                            "amount": float(pension.amount),
                            "percentage": float(pension.percentage),
                            "employer_contribution": float(pension.employer_contribution),
                            "employee_contribution": float(pension.employee_contribution),
                            "is_active": pension.is_active
                        }
                        for pension in pension_list
                    ],
                    "total": len(pension_list)
                }

        except Exception as e:
            logger.error(f"Failed to get pension calculations: {e}")
            return {"error": f"Failed to get pension calculations: {str(e)}"}
