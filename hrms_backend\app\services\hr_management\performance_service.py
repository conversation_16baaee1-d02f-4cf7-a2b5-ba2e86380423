from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, func
from typing import Optional, List, Dict, Any
from uuid import UUID
from datetime import datetime, date, timedelta
from decimal import Decimal
from fastapi import HTTPException, status
import logging

from ...db.models.performance import (
    PerformanceReview, Goal, PerformanceTemplate, Competency,
    PerformanceCycle
)
from ...db.models.employee import Employee
from ...schemas.performance import (
    PerformanceReviewCreate, PerformanceReviewUpdate, PerformanceReviewResponse,
    PerformanceReviewListResponse, GoalCreate, GoalUpdate, GoalResponse,
    GoalListResponse, ReviewTemplateCreate, ReviewTemplateUpdate,
    ReviewTemplateResponse, DevelopmentPlanCreate, DevelopmentPlanUpdate,
    DevelopmentPlanResponse, PerformanceAnalytics, PerformanceDashboard,
    BulkReviewCreate, BulkGoalCreate, ReviewStatus, ReviewType,
    GoalStatus, GoalType
)
from ...core.security import CurrentUser
from ...core.websocket_manager import notification_manager

logger = logging.getLogger(__name__)


class PerformanceService:
    """Performance service for business logic"""

    async def create_performance_review(
        self,
        db: Session,
        review_data: PerformanceReviewCreate,
        current_user: CurrentUser
    ) -> PerformanceReviewResponse:
        """Create new performance review"""
        try:
            # Verify employee exists
            employee = db.query(Employee).filter(
                Employee.id == review_data.employee_id,
                Employee.organization_id == current_user.organization_id,
                Employee.is_active == True
            ).first()

            if not employee:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Employee not found"
                )

            # Verify reviewer exists
            reviewer = db.query(Employee).filter(
                Employee.id == review_data.reviewer_id,
                Employee.organization_id == current_user.organization_id,
                Employee.is_active == True
            ).first()

            if not reviewer:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Reviewer not found"
                )

            # Check for overlapping reviews
            overlapping = db.query(PerformanceReview).filter(
                PerformanceReview.employee_id == review_data.employee_id,
                PerformanceReview.status.in_([ReviewStatus.DRAFT, ReviewStatus.IN_PROGRESS]),
                or_(
                    and_(
                        PerformanceReview.review_period_start <= review_data.review_period_start,
                        PerformanceReview.review_period_end >= review_data.review_period_start
                    ),
                    and_(
                        PerformanceReview.review_period_start <= review_data.review_period_end,
                        PerformanceReview.review_period_end >= review_data.review_period_end
                    )
                )
            ).first()

            if overlapping:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Overlapping performance review exists for this period"
                )

            # Create performance review
            review = PerformanceReview(
                **review_data.dict(),
                organization_id=current_user.organization_id,
                status=ReviewStatus.DRAFT,
                development_areas=[],
                strengths=[],
                goals_for_next_period=[],
                created_by=current_user.user_id
            )

            db.add(review)
            db.commit()
            db.refresh(review)

            # Send notification to employee and reviewer
            await self._notify_review_created(review, current_user)

            logger.info(f"Performance review created for employee {review.employee_id} by {current_user.user_id}")
            return PerformanceReviewResponse.from_orm(review)

        except HTTPException:
            raise
        except Exception as e:
            db.rollback()
            logger.error(f"Error creating performance review: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Error creating performance review"
            )

    async def create_goal(
        self,
        db: Session,
        goal_data: GoalCreate,
        current_user: CurrentUser
    ) -> GoalResponse:
        """Create new goal"""
        try:
            # Verify employee exists
            employee = db.query(Employee).filter(
                Employee.id == goal_data.employee_id,
                Employee.organization_id == current_user.organization_id,
                Employee.is_active == True
            ).first()

            if not employee:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Employee not found"
                )

            # Verify parent goal exists if provided
            if goal_data.parent_goal_id:
                parent_goal = db.query(Goal).filter(
                    Goal.id == goal_data.parent_goal_id,
                    Goal.organization_id == current_user.organization_id
                ).first()

                if not parent_goal:
                    raise HTTPException(
                        status_code=status.HTTP_404_NOT_FOUND,
                        detail="Parent goal not found"
                    )

            # Create goal
            goal = Goal(
                **goal_data.dict(),
                organization_id=current_user.organization_id,
                status=GoalStatus.DRAFT,
                progress_percentage=Decimal(0),
                created_by=current_user.user_id
            )

            db.add(goal)
            db.commit()
            db.refresh(goal)

            # Send notification to employee
            await notification_manager.notify_goal_assigned(
                str(goal.employee_id),
                {
                    "goal_id": str(goal.id),
                    "title": goal.title,
                    "target_date": goal.target_date.isoformat(),
                    "assigned_by": current_user.email
                }
            )

            logger.info(f"Goal created for employee {goal.employee_id} by {current_user.user_id}")
            return GoalResponse.from_orm(goal)

        except HTTPException:
            raise
        except Exception as e:
            db.rollback()
            logger.error(f"Error creating goal: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Error creating goal"
            )

    async def get_performance_dashboard(
        self,
        db: Session,
        current_user: CurrentUser
    ) -> PerformanceDashboard:
        """Get performance dashboard data"""
        try:
            # Organization metrics
            total_employees = db.query(Employee).filter(
                Employee.organization_id == current_user.organization_id,
                Employee.is_active == True
            ).count()

            total_reviews = db.query(PerformanceReview).filter(
                PerformanceReview.organization_id == current_user.organization_id
            ).count()

            pending_reviews = db.query(PerformanceReview).filter(
                PerformanceReview.organization_id == current_user.organization_id,
                PerformanceReview.status.in_([ReviewStatus.DRAFT, ReviewStatus.IN_PROGRESS])
            ).all()

            total_goals = db.query(Goal).filter(
                Goal.organization_id == current_user.organization_id
            ).count()

            completed_goals = db.query(Goal).filter(
                Goal.organization_id == current_user.organization_id,
                Goal.status == GoalStatus.COMPLETED
            ).count()

            overdue_goals = db.query(Goal).filter(
                Goal.organization_id == current_user.organization_id,
                Goal.status == GoalStatus.ACTIVE,
                Goal.target_date < date.today()
            ).all()

            # Calculate average ratings
            avg_rating = db.query(func.avg(PerformanceReview.overall_rating)).filter(
                PerformanceReview.organization_id == current_user.organization_id,
                PerformanceReview.overall_rating.isnot(None)
            ).scalar() or 0

            goal_completion_rate = (completed_goals / total_goals * 100) if total_goals > 0 else 0

            organization_metrics = {
                "total_employees": total_employees,
                "total_reviews": total_reviews,
                "pending_reviews_count": len(pending_reviews),
                "total_goals": total_goals,
                "completed_goals": completed_goals,
                "overdue_goals_count": len(overdue_goals),
                "average_rating": float(avg_rating),
                "goal_completion_rate": goal_completion_rate
            }

            return PerformanceDashboard(
                organization_metrics=organization_metrics,
                department_metrics=[],  # Would be calculated per department
                pending_reviews=[PerformanceReviewResponse.from_orm(r) for r in pending_reviews[:10]],
                overdue_goals=[GoalResponse.from_orm(g) for g in overdue_goals[:10]],
                top_performers=[],  # Would be calculated based on ratings
                performance_trends={},  # Would contain historical trend data
                upcoming_review_cycles=[]  # Would contain upcoming review cycles
            )

        except Exception as e:
            logger.error(f"Error getting performance dashboard: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Error retrieving performance dashboard"
            )

    async def get_employee_performance_analytics(
        self,
        db: Session,
        employee_id: UUID,
        current_user: CurrentUser
    ) -> Optional[PerformanceAnalytics]:
        """Get performance analytics for employee"""
        try:
            employee = db.query(Employee).filter(
                Employee.id == employee_id,
                Employee.organization_id == current_user.organization_id,
                Employee.is_active == True
            ).first()

            if not employee:
                return None

            # Get latest review
            latest_review = db.query(PerformanceReview).filter(
                PerformanceReview.employee_id == employee_id,
                PerformanceReview.status == ReviewStatus.COMPLETED
            ).order_by(PerformanceReview.review_period_end.desc()).first()

            # Get goals
            goals = db.query(Goal).filter(
                Goal.employee_id == employee_id
            ).all()

            completed_goals = [g for g in goals if g.status == GoalStatus.COMPLETED]
            overdue_goals = [g for g in goals if g.status == GoalStatus.ACTIVE and g.target_date < date.today()]

            goal_completion_rate = (len(completed_goals) / len(goals) * 100) if goals else 0

            # Determine performance trend (simplified)
            recent_reviews = db.query(PerformanceReview).filter(
                PerformanceReview.employee_id == employee_id,
                PerformanceReview.status == ReviewStatus.COMPLETED,
                PerformanceReview.overall_rating.isnot(None)
            ).order_by(PerformanceReview.review_period_end.desc()).limit(3).all()

            trend = "stable"
            if len(recent_reviews) >= 2:
                if recent_reviews[0].overall_rating > recent_reviews[1].overall_rating:
                    trend = "improving"
                elif recent_reviews[0].overall_rating < recent_reviews[1].overall_rating:
                    trend = "declining"

            return PerformanceAnalytics(
                employee_id=employee_id,
                review_period=f"{latest_review.review_period_start} to {latest_review.review_period_end}" if latest_review else "No reviews",
                overall_rating=latest_review.overall_rating if latest_review else None,
                goal_completion_rate=Decimal(goal_completion_rate),
                total_goals=len(goals),
                completed_goals=len(completed_goals),
                overdue_goals=len(overdue_goals),
                performance_trend=trend,
                strengths=latest_review.strengths if latest_review else [],
                development_areas=latest_review.development_areas if latest_review else [],
                peer_feedback_score=None,  # Would be calculated from peer reviews
                manager_rating=latest_review.overall_rating if latest_review else None,
                self_rating=None  # Would be from self-review
            )

        except Exception as e:
            logger.error(f"Error getting employee performance analytics: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Error retrieving performance analytics"
            )

    # Helper methods
    async def _notify_review_created(
        self,
        review: PerformanceReview,
        current_user: CurrentUser
    ):
        """Send notifications when review is created"""
        try:
            # Notify employee
            await notification_manager.notify_performance_review(
                str(review.employee_id),
                {
                    "review_id": str(review.id),
                    "title": review.title,
                    "due_date": review.due_date.isoformat(),
                    "reviewer": current_user.email,
                    "action": "created"
                }
            )

            # Notify reviewer if different from creator
            if review.reviewer_id != current_user.user_id:
                await notification_manager.notify_performance_review(
                    str(review.reviewer_id),
                    {
                        "review_id": str(review.id),
                        "title": review.title,
                        "employee_id": str(review.employee_id),
                        "due_date": review.due_date.isoformat(),
                        "action": "assigned_to_review"
                    }
                )
        except Exception as e:
            logger.error(f"Error sending review notifications: {e}")

    async def check_overdue_goals(
        self,
        db: Session,
        organization_id: UUID
    ):
        """Check and update overdue goals (Celery task)"""
        try:
            today = date.today()

            # Find overdue goals
            overdue_goals = db.query(Goal).filter(
                Goal.organization_id == organization_id,
                Goal.status == GoalStatus.ACTIVE,
                Goal.target_date < today
            ).all()

            for goal in overdue_goals:
                goal.status = GoalStatus.OVERDUE
                goal.updated_at = datetime.utcnow()

            db.commit()

            logger.info(f"Updated {len(overdue_goals)} overdue goals for organization {organization_id}")
            return len(overdue_goals)

        except Exception as e:
            db.rollback()
            logger.error(f"Error checking overdue goals: {e}")
            raise
