from fastapi import APIRouter, HTTPException, status, Depends, Query
from sqlalchemy.orm import Session
from typing import List, Optional
from uuid import UUID
from datetime import date

from ..core.security import get_current_user, require_permission, CurrentUser
from ..core.rbac import Permissions
from ..db.session import get_db
from ..schemas.employee import (
    EmployeeCreate, EmployeeUpdate, EmployeeResponse, EmployeeListResponse,
    DepartmentCreate, DepartmentUpdate, DepartmentResponse,
    DesignationCreate, DesignationUpdate, DesignationResponse
)
from ..services.hr_management.employee_service import EmployeeService

router = APIRouter()
employee_service = EmployeeService()

# Simple test endpoint
@router.get("/test")
async def test_employees(db: Session = Depends(get_db)):
    """Simple test endpoint to check if employees can be retrieved"""
    try:
        from sqlalchemy import text
        result = db.execute(text("SELECT COUNT(*) FROM employees"))
        count = result.scalar()
        return {"message": "Employees endpoint working", "employee_count": count}
    except Exception as e:
        return {"error": str(e)}

# Employee endpoints
@router.get("/")
async def get_employees(
    skip: int = Query(0, ge=0),
    limit: int = Query(20, ge=1, le=100),
    search: Optional[str] = Query(None),
    department_id: Optional[UUID] = Query(None),
    status: Optional[str] = Query(None),
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.EMPLOYEE_READ))
):
    """Get all employees with filtering and pagination"""
    try:
        from sqlalchemy import text

        # Build the query with optional search
        base_query = """
            SELECT id, employee_id, first_name, last_name, email,
                   department, position, is_active, user_id, created_at
            FROM employees
            WHERE is_active = true
        """

        params = {"limit": limit, "skip": skip}

        # Add search filter if provided
        if search:
            base_query += """
                AND (
                    LOWER(first_name) LIKE LOWER(:search) OR
                    LOWER(last_name) LIKE LOWER(:search) OR
                    LOWER(email) LIKE LOWER(:search) OR
                    LOWER(employee_id) LIKE LOWER(:search)
                )
            """
            params["search"] = f"%{search}%"

        # Add department filter if provided
        if department_id:
            base_query += " AND department = :department"
            params["department"] = str(department_id)

        base_query += " ORDER BY created_at LIMIT :limit OFFSET :skip"

        result = db.execute(text(base_query), params)

        employees = []
        for row in result.fetchall():
            employees.append({
                "id": str(row[0]),
                "employee_id": row[1],
                "first_name": row[2],
                "last_name": row[3],
                "email": row[4],
                "department": row[5] or "N/A",
                "position": row[6] or "N/A",
                "is_active": row[7],
                "user_id": str(row[8]) if row[8] else None,
                "hire_date": row[9].isoformat() if row[9] else None
            })

        # Get total count
        count_query = "SELECT COUNT(*) FROM employees WHERE is_active = true"
        count_params = {}

        if search:
            count_query += """
                AND (
                    LOWER(first_name) LIKE LOWER(:search) OR
                    LOWER(last_name) LIKE LOWER(:search) OR
                    LOWER(email) LIKE LOWER(:search) OR
                    LOWER(employee_id) LIKE LOWER(:search)
                )
            """
            count_params["search"] = f"%{search}%"

        if department_id:
            count_query += " AND department = :department"
            count_params["department"] = str(department_id)

        total_result = db.execute(text(count_query), count_params)
        total = total_result.scalar()

        return {
            "employees": employees,
            "total": total,
            "skip": skip,
            "limit": limit
        }

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error retrieving employees: {str(e)}"
        )


@router.get("/{employee_id}", response_model=EmployeeResponse)
async def get_employee(
    employee_id: UUID,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.EMPLOYEE_READ))
):
    """Get employee by ID"""
    employee = await employee_service.get_employee_by_id(db, employee_id, current_user)
    if not employee:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Employee not found"
        )
    return employee


@router.post("/", response_model=EmployeeResponse)
async def create_employee(
    employee_data: EmployeeCreate,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.EMPLOYEE_CREATE))
):
    """Create new employee"""
    return await employee_service.create_employee(db, employee_data, current_user)


@router.put("/{employee_id}", response_model=EmployeeResponse)
async def update_employee(
    employee_id: UUID,
    employee_data: EmployeeUpdate,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.EMPLOYEE_UPDATE))
):
    """Update employee"""
    employee = await employee_service.update_employee(db, employee_id, employee_data, current_user)
    if not employee:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Employee not found"
        )
    return employee


@router.delete("/{employee_id}")
async def delete_employee(
    employee_id: UUID,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.EMPLOYEE_DELETE))
):
    """Delete employee (soft delete)"""
    success = await employee_service.delete_employee(db, employee_id, current_user)
    if not success:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Employee not found"
        )
    return {"message": "Employee deleted successfully"}


@router.get("/{employee_id}/profile", response_model=EmployeeResponse)
async def get_employee_profile(
    employee_id: UUID,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(get_current_user)
):
    """Get employee profile (employees can view their own profile)"""
    # Allow employees to view their own profile
    if current_user.user_id != str(employee_id):
        # Check if user has permission to view other profiles
        current_user = Depends(require_permission(Permissions.EMPLOYEE_READ))(current_user)

    employee = await employee_service.get_employee_by_id(db, employee_id, current_user)
    if not employee:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Employee not found"
        )
    return employee


@router.put("/{employee_id}/profile", response_model=EmployeeResponse)
async def update_employee_profile(
    employee_id: UUID,
    employee_data: EmployeeUpdate,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(get_current_user)
):
    """Update employee profile (employees can update their own profile)"""
    # Allow employees to update their own profile
    if current_user.user_id != str(employee_id):
        # Check if user has permission to update other profiles
        current_user = Depends(require_permission(Permissions.EMPLOYEE_UPDATE))(current_user)

    employee = await employee_service.update_employee_profile(db, employee_id, employee_data, current_user)
    if not employee:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Employee not found"
        )
    return employee


# Department endpoints
@router.get("/departments/", response_model=List[DepartmentResponse])
async def get_departments(
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.EMPLOYEE_READ))
):
    """Get all departments"""
    return await employee_service.get_departments(db, current_user)


@router.post("/departments/", response_model=DepartmentResponse)
async def create_department(
    department_data: DepartmentCreate,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.EMPLOYEE_CREATE))
):
    """Create new department"""
    return await employee_service.create_department(db, department_data, current_user)


@router.put("/departments/{department_id}", response_model=DepartmentResponse)
async def update_department(
    department_id: UUID,
    department_data: DepartmentUpdate,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.EMPLOYEE_UPDATE))
):
    """Update department"""
    department = await employee_service.update_department(db, department_id, department_data, current_user)
    if not department:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Department not found"
        )
    return department


# Designation endpoints
@router.get("/designations/", response_model=List[DesignationResponse])
async def get_designations(
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.EMPLOYEE_READ))
):
    """Get all designations"""
    return await employee_service.get_designations(db, current_user)


@router.post("/designations/", response_model=DesignationResponse)
async def create_designation(
    designation_data: DesignationCreate,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.EMPLOYEE_CREATE))
):
    """Create new designation"""
    return await employee_service.create_designation(db, designation_data, current_user)
