from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, func
from typing import Optional, List
from uuid import UUID
from datetime import datetime, date, timedelta
from fastapi import HTTPException, status
import logging

from ...db.models.delegation import Delegation, DelegationActivity, ApprovalDelegation
from ...db.models.employee import Employee
from ...schemas.delegation import (
    DelegationCreate, DelegationUpdate, DelegationResponse,
    DelegationListResponse, DelegationActivityCreate, DelegationActivityResponse,
    ApprovalDelegationCreate, ApprovalDelegationUpdate, ApprovalDelegationResponse,
    DelegationApprovalRequest, DelegationRevocationRequest,
    BulkDelegationUpdate, DelegationSummary, DelegationStatus, DelegationType
)
from ...core.security import CurrentUser
from ...core.websocket_manager import notification_manager

logger = logging.getLogger(__name__)


class DelegationService:
    """Delegation service for business logic"""

    async def create_delegation(
        self,
        db: Session,
        delegation_data: DelegationCreate,
        current_user: CurrentUser
    ) -> DelegationResponse:
        """Create new delegation"""
        try:
            # Verify delegate exists
            delegate = db.query(Employee).filter(
                Employee.id == delegation_data.delegate_to,
                Employee.organization_id == current_user.organization_id,
                Employee.is_active == True
            ).first()

            if not delegate:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Delegate not found"
                )

            # Verify approver exists if specified
            if delegation_data.approver_id:
                approver = db.query(Employee).filter(
                    Employee.id == delegation_data.approver_id,
                    Employee.organization_id == current_user.organization_id,
                    Employee.is_active == True
                ).first()

                if not approver:
                    raise HTTPException(
                        status_code=status.HTTP_404_NOT_FOUND,
                        detail="Approver not found"
                    )

            # Check for overlapping delegations
            overlapping = db.query(Delegation).filter(
                Delegation.delegator_id == current_user.user_id,
                Delegation.delegation_type == delegation_data.delegation_type,
                Delegation.status.in_([DelegationStatus.PENDING, DelegationStatus.ACTIVE]),
                or_(
                    and_(
                        Delegation.start_date <= delegation_data.start_date,
                        Delegation.end_date >= delegation_data.start_date
                    ),
                    and_(
                        Delegation.start_date <= delegation_data.end_date,
                        Delegation.end_date >= delegation_data.end_date
                    ),
                    and_(
                        Delegation.start_date >= delegation_data.start_date,
                        Delegation.end_date <= delegation_data.end_date
                    )
                )
            ).first()

            if overlapping:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Overlapping delegation exists for this type and period"
                )

            # Create delegation
            delegation = Delegation(
                **delegation_data.dict(exclude={'specific_permissions'}),
                delegator_id=current_user.user_id,
                organization_id=current_user.organization_id,
                status=DelegationStatus.PENDING if delegation_data.requires_approval else DelegationStatus.ACTIVE,
                specific_permissions=delegation_data.specific_permissions or [],
                created_by=current_user.user_id
            )

            # If no approval required, activate immediately
            if not delegation_data.requires_approval:
                delegation.activated_at = datetime.utcnow()

            db.add(delegation)
            db.commit()
            db.refresh(delegation)

            # Create activity log
            await self._create_activity(
                db, delegation.id, "created",
                f"Delegation created by {current_user.email}", current_user
            )

            # Send notifications
            await self._notify_delegation_created(delegation, current_user)

            logger.info(f"Delegation {delegation.id} created by {current_user.user_id}")
            return DelegationResponse.from_orm(delegation)

        except HTTPException:
            raise
        except Exception as e:
            db.rollback()
            logger.error(f"Error creating delegation: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Error creating delegation"
            )

    async def get_delegations(
        self,
        db: Session,
        status: Optional[DelegationStatus] = None,
        delegation_type: Optional[DelegationType] = None,
        delegator_id: Optional[UUID] = None,
        delegate_to: Optional[UUID] = None,
        search: Optional[str] = None,
        skip: int = 0,
        limit: int = 20,
        current_user: CurrentUser = None
    ) -> DelegationListResponse:
        """Get delegations with filtering"""
        try:
            query = db.query(Delegation).filter(
                Delegation.organization_id == current_user.organization_id
            )

            # Apply filters
            if status:
                query = query.filter(Delegation.status == status)

            if delegation_type:
                query = query.filter(Delegation.delegation_type == delegation_type)

            if delegator_id:
                query = query.filter(Delegation.delegator_id == delegator_id)

            if delegate_to:
                query = query.filter(Delegation.delegate_to == delegate_to)

            if search:
                search_filter = or_(
                    Delegation.title.ilike(f"%{search}%"),
                    Delegation.description.ilike(f"%{search}%")
                )
                query = query.filter(search_filter)

            # Get total count
            total = query.count()

            # Apply pagination and ordering
            delegations = query.order_by(
                Delegation.created_at.desc()
            ).offset(skip).limit(limit).all()

            # Convert to response format
            delegation_responses = [DelegationResponse.from_orm(delegation) for delegation in delegations]

            return DelegationListResponse(
                delegations=delegation_responses,
                total=total,
                skip=skip,
                limit=limit
            )

        except Exception as e:
            logger.error(f"Error getting delegations: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Error retrieving delegations"
            )

    async def approve_delegation(
        self,
        db: Session,
        delegation_id: UUID,
        approved: bool,
        comments: Optional[str],
        current_user: CurrentUser
    ) -> Optional[DelegationResponse]:
        """Approve or reject delegation"""
        try:
            delegation = db.query(Delegation).filter(
                Delegation.id == delegation_id,
                Delegation.organization_id == current_user.organization_id
            ).first()

            if not delegation:
                return None

            if delegation.status != DelegationStatus.PENDING:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Delegation is not pending approval"
                )

            # Update delegation
            if approved:
                delegation.status = DelegationStatus.ACTIVE
                delegation.activated_at = datetime.utcnow()
            else:
                delegation.status = DelegationStatus.REVOKED
                delegation.revoked_by = current_user.user_id
                delegation.revoked_at = datetime.utcnow()
                delegation.revocation_reason = comments or "Rejected during approval"

            delegation.approved_by = current_user.user_id
            delegation.approved_at = datetime.utcnow()
            delegation.updated_by = current_user.user_id

            db.commit()
            db.refresh(delegation)

            # Create activity log
            action = "approved" if approved else "rejected"
            await self._create_activity(
                db, delegation.id, action,
                f"Delegation {action} by {current_user.email}: {comments or 'No comments'}",
                current_user
            )

            # Send notifications
            await self._notify_delegation_status_change(delegation, action, current_user)

            logger.info(f"Delegation {delegation.id} {action} by {current_user.user_id}")
            return DelegationResponse.from_orm(delegation)

        except HTTPException:
            raise
        except Exception as e:
            db.rollback()
            logger.error(f"Error approving delegation {delegation_id}: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Error processing delegation approval"
            )

    async def revoke_delegation(
        self,
        db: Session,
        delegation_id: UUID,
        revocation_data: DelegationRevocationRequest,
        current_user: CurrentUser
    ) -> Optional[DelegationResponse]:
        """Revoke active delegation"""
        try:
            delegation = db.query(Delegation).filter(
                Delegation.id == delegation_id,
                Delegation.organization_id == current_user.organization_id
            ).first()

            if not delegation:
                return None

            if delegation.status not in [DelegationStatus.ACTIVE, DelegationStatus.PENDING]:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Delegation cannot be revoked in current status"
                )

            # Update delegation
            delegation.status = DelegationStatus.REVOKED
            delegation.revoked_by = current_user.user_id
            delegation.revoked_at = datetime.utcnow()
            delegation.revocation_reason = revocation_data.reason
            delegation.updated_by = current_user.user_id

            # If not immediate, set end date to today
            if not revocation_data.immediate:
                delegation.end_date = date.today()

            db.commit()
            db.refresh(delegation)

            # Create activity log
            await self._create_activity(
                db, delegation.id, "revoked",
                f"Delegation revoked by {current_user.email}: {revocation_data.reason}",
                current_user
            )

            # Send notifications
            if revocation_data.notify_delegate:
                await self._notify_delegation_revoked(delegation, current_user)

            logger.info(f"Delegation {delegation.id} revoked by {current_user.user_id}")
            return DelegationResponse.from_orm(delegation)

        except HTTPException:
            raise
        except Exception as e:
            db.rollback()
            logger.error(f"Error revoking delegation {delegation_id}: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Error revoking delegation"
            )

    # Helper methods
    async def _create_activity(
        self,
        db: Session,
        delegation_id: UUID,
        activity_type: str,
        description: str,
        current_user: CurrentUser
    ):
        """Create delegation activity log"""
        try:
            activity = DelegationActivity(
                delegation_id=delegation_id,
                activity_type=activity_type,
                description=description,
                performed_by=current_user.user_id,
                performed_at=datetime.utcnow(),
                created_by=current_user.user_id
            )
            db.add(activity)
            db.commit()
        except Exception as e:
            logger.error(f"Error creating delegation activity: {e}")

    async def _notify_delegation_created(
        self,
        delegation: Delegation,
        current_user: CurrentUser
    ):
        """Send notifications when delegation is created"""
        try:
            # Notify delegate
            await notification_manager.notify_delegation_request(
                str(delegation.delegate_to),
                {
                    "delegation_id": str(delegation.id),
                    "type": delegation.delegation_type,
                    "title": delegation.title,
                    "delegator": current_user.email,
                    "start_date": delegation.start_date.isoformat(),
                    "end_date": delegation.end_date.isoformat(),
                    "requires_approval": delegation.requires_approval
                }
            )

            # Notify approver if approval required
            if delegation.requires_approval and delegation.approver_id:
                await notification_manager.notify_user(
                    str(delegation.approver_id),
                    "delegation_approval_required",
                    {
                        "delegation_id": str(delegation.id),
                        "title": delegation.title,
                        "delegator": current_user.email,
                        "delegate": str(delegation.delegate_to)
                    }
                )
        except Exception as e:
            logger.error(f"Error sending delegation notifications: {e}")

    async def _notify_delegation_status_change(
        self,
        delegation: Delegation,
        action: str,
        current_user: CurrentUser
    ):
        """Send notifications for delegation status changes"""
        try:
            # Notify delegator
            await notification_manager.notify_user(
                str(delegation.delegator_id),
                f"delegation_{action}",
                {
                    "delegation_id": str(delegation.id),
                    "title": delegation.title,
                    "action": action,
                    "actioned_by": current_user.email
                }
            )

            # Notify delegate
            await notification_manager.notify_user(
                str(delegation.delegate_to),
                f"delegation_{action}",
                {
                    "delegation_id": str(delegation.id),
                    "title": delegation.title,
                    "action": action,
                    "actioned_by": current_user.email
                }
            )
        except Exception as e:
            logger.error(f"Error sending delegation status notifications: {e}")

    async def _notify_delegation_revoked(
        self,
        delegation: Delegation,
        current_user: CurrentUser
    ):
        """Send notification when delegation is revoked"""
        try:
            await notification_manager.notify_user(
                str(delegation.delegate_to),
                "delegation_revoked",
                {
                    "delegation_id": str(delegation.id),
                    "title": delegation.title,
                    "reason": delegation.revocation_reason,
                    "revoked_by": current_user.email
                }
            )
        except Exception as e:
            logger.error(f"Error sending delegation revocation notification: {e}")

    async def check_delegation_expiry(
        self,
        db: Session,
        organization_id: UUID
    ):
        """Check and expire delegations (Celery task)"""
        try:
            today = date.today()

            # Find expired delegations
            expired_delegations = db.query(Delegation).filter(
                Delegation.organization_id == organization_id,
                Delegation.status == DelegationStatus.ACTIVE,
                Delegation.end_date < today
            ).all()

            for delegation in expired_delegations:
                delegation.status = DelegationStatus.EXPIRED
                delegation.updated_at = datetime.utcnow()

                # Create activity log
                activity = DelegationActivity(
                    delegation_id=delegation.id,
                    activity_type="expired",
                    description="Delegation automatically expired",
                    performed_by=None,  # System action
                    performed_at=datetime.utcnow()
                )
                db.add(activity)

            db.commit()

            logger.info(f"Expired {len(expired_delegations)} delegations for organization {organization_id}")
            return len(expired_delegations)

        except Exception as e:
            db.rollback()
            logger.error(f"Error checking delegation expiry: {e}")
            raise
