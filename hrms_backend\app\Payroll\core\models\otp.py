from core.databases.database import db
from core.models.basemodel import ModelBase
from sqlalchemy import Foreign<PERSON>ey
from datetime import datetime
from datetime import datetime
import pytz

class OTPModel(ModelBase):
    __tablename__ = "otps"

    id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    email = db.Column(db.String(80), nullable=False)
    user_id = db.Column(db.Integer, db.ForeignKey((ModelBase.dbSchema() + '.users.id')), nullable=False) 
    otp = db.Column(db.String(6), nullable=False)
    created_at = db.Column(db.DateTime, nullable=False, default=lambda: datetime.now(pytz.timezone('Africa/Lagos')))
    
    def __init__(self, email, user_id, otp, **kwargs):
        super().__init__(**kwargs)
        self.email = email
        self.user_id = user_id
        self.otp = otp
        print(f"Created at: {self.created_at}")

