from flask.views import <PERSON>View
from flask_smorest import Blueprint, abort
from core.middleware import roles_required
from schemas import TransactionHistoryListSchema
from core.services.transaction_history import TransactionHistoryService
from core.utils.responseBuilder import ResponseBuilder

blueprint = Blueprint("transaction_history", __name__, description="Operations for transactions")
        
@blueprint.route("/transactions")
class PayscheduleList(MethodView):
    @roles_required(['admin'])
    @blueprint.response(200, TransactionHistoryListSchema)
    def get(self):
        transactions = TransactionHistoryService()
        transactions_list = transactions.fetchAll()
        transactions_data = TransactionHistoryListSchema(many=True).dump(transactions_list)

        return ResponseBuilder(data=transactions_data, status_code=200).build()
    
    