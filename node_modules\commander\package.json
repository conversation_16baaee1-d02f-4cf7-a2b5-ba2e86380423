{"name": "commander", "version": "4.1.1", "description": "the complete solution for node.js command-line programs", "keywords": ["commander", "command", "option", "parser"], "author": "<PERSON><PERSON> <<EMAIL>>", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/tj/commander.js.git"}, "scripts": {"lint": "eslint index.js \"tests/**/*.js\"", "test": "jest && npm run test-typings", "test-typings": "tsc -p tsconfig.json"}, "main": "index", "files": ["index.js", "typings/index.d.ts"], "dependencies": {}, "devDependencies": {"@types/jest": "^24.0.23", "@types/node": "^12.12.11", "eslint": "^6.7.0", "eslint-plugin-jest": "^22.21.0", "jest": "^24.8.0", "standard": "^14.3.1", "typescript": "^3.7.2"}, "typings": "typings/index.d.ts", "engines": {"node": ">= 6"}}