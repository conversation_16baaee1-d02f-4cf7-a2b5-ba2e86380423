import { defineConfig } from "vite";
import react from "@vitejs/plugin-react";
import tailwindcss from "@tailwindcss/vite";
import path from "path";

export default defineConfig({
  plugins: [react(), tailwindcss()],
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "./src"),
    },
  },
  // Ensure public directory is properly configured
  publicDir: "public",
  // Add this to help with image loading issues
  server: {
    fs: {
      strict: false,
    },
  },
  // Add a base URL if needed
  base: './',
});