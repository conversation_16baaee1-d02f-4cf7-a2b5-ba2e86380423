from core.models.employee_component_pivot import EmployeeComponentsPivotModel
from core.models.salary_tempplate_component_pivot import SalaryTemplateComponentsPivotModel
from core.repositories.employee import EmployeeRepository

class EmployeeService:
    def __init__(self) -> None:
        self.repository = EmployeeRepository()

    def createEmployee(self, Kwargs):
        return self.repository.createEmployee(**Kwargs)
    
    def getEmployeeById(self, id):
        return self.repository.getEmployee(id)
    
    def updateEmployee(self, id, Kwargs):
        return self.repository.updateEmployee(id, **Kwargs)
    
    def getEmployeeByKey(self, Kwarg):
        return self.repository.getEmployeeByKeys(Kwarg)
    
    def deleteEmployee(self, id):
        return self.repository.deleteEmployee(id)
    
    @staticmethod
    def getEmployeeSummary():
        summary_data = EmployeeRepository.getEmployeeCountAndGross()

        return {
            "contract_employees": summary_data["contract_count"],
            "permanent_employees": summary_data["permanent_count"],
            "total_gross_pay": round(summary_data["total_gross_pay"], 2)
        }
    
    def getEmployeeByEmailOrId(self, email, employee_id):
        return self.repository.getEmployeeByEmailOrId(email, employee_id)
        
    def getEmployeesByDepartment(self, department_id):
        return self.repository.getEmployeesByDepartment(department_id)
    
    def getEmployeesByDesignation(self, designation_id):
        return self.repository.getEmployeesByDesignation(designation_id)
    
    def getEmployeesByType(self, employment_type):
        """Fetch active employees based on employment type."""
        employees = self.repository.getEmployeesByType(employment_type)
        return [emp for emp in employees if emp.status == "Active"]
    
    def searchEmployee(self, search_value, offset, size):
        employees = self.repository.searchEmployees(search_value, offset, size)
        total_employees = len(employees)
        return employees, total_employees

    def sortEmployee(self, sort_key, offset, size, sort_dir='asc'):
        employees = self.repository.sortEmployees(sort_key, offset, size, sort_dir)
        total_employees = len(employees)
        return employees, total_employees

    def fetchEmployee(self, offset: int, size: int):
        employees = self.repository.fetchEmployees(offset, size)
        total_employees = len(employees)
        return employees, total_employees
    
    def getEmployeesByIds(self, employee_ids):
        """Fetch active employees. If 'ALL' is passed, return full employee objects."""
        all_employees = self.repository.fetchEmployees(0, 10000000)  # Adjust limit if needed

        # If 'ALL' is provided, return all active employees
        if employee_ids == "ALL":
            return [emp for emp in all_employees if emp.status == "Active"]

        # Convert single string ID to integer (if needed)
        try:
            employee_ids = [int(employee_ids)]  # Convert single ID string to a list
        except ValueError:
            return []  # Return empty list for invalid input

        # Filter employees based on provided IDs
        return [emp for emp in all_employees if emp.id in employee_ids and emp.status == "Active"]


    def fetchEmployeeTax(self, offset: int, size: int):
        employees = self.repository.fetchEmployeesTaxDetails(offset, size)
        total_employees = len(employees)
        return employees, total_employees

        # Method to count all employees
    def countAllEmployees(self):
        return self.repository.countEmployees()

    def fetchActiveEmployees(self, offset: int = 0, size: int = 50):
        return self.repository.fetchActiveEmployees(offset, size)
    
    
    def fetchRecentEmployees(self):
        # Fetch last 5 employees by hire_date
        recent_employees = self.repository.fetchRecentEmployees()
        return recent_employees
    
    def updateEmployeeTemplate(self, employee_id: int, template_id: int):
        """Update the template_id for a given employee."""
        employees = self.repository.assignTemplateToEmployees(employee_id, template_id)
        if not employees:
            return False  # No employees found or error occurred
        # No need to set template_id here, it's already updated in assignTemplateToEmployees
        return True
    
    def getSalaryTemplateComponents(self, template_id):
        # Fetch salary template components and return only the `salary_component` field
        return [
            template_component.salary_component
            for template_component in SalaryTemplateComponentsPivotModel.query.filter_by(
                salary_template_id=template_id
            ).all()
        ]

    def getEmployeeComponents(self, employee_id):
        # Fetch employee components and return only the `salary_component` field
        return [
            employee_component.salary_component
            for employee_component in EmployeeComponentsPivotModel.query.filter_by(
                employee_id=employee_id
            ).all()
        ]
        
    def getEmployeesByOrganisation(self, organisation_id):
        """Fetch active employees for a given organisation."""
        employees = self.repository.getEmployeesByOrganisation(organisation_id)
        return [emp for emp in employees if emp.status == "Active"]


    def getEmployeesByTemplate(self, template_id):
        """Fetch active employees for a given template."""
        employees = self.repository.getEmployeesByTemplate(template_id)
        return [emp for emp in employees if emp.status == "Active"]
