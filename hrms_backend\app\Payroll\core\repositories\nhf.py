from core.models.nhf import NhfModel
from core.databases.database import db

class NhfRepository:
    def __init__(self) -> None:
        pass

    @classmethod
    def createNhf(self, primary_mortgage_bank, recapitalisation_status):
        nhf = NhfModel(
            primary_mortgage_bank = primary_mortgage_bank,
            recapitalisation_status = recapitalisation_status
        )
        db.session.add(nhf)
        db.session.commit()
        return nhf

    @classmethod
    def getNhf(self, id):
        return NhfModel.query.filter(NhfModel.id == id).first()
    
    @classmethod
    def getNhfByKeys(self, kwargs):
        return NhfModel.query.filter_by(**kwargs).all()

    @classmethod
    def updateNhf(self, id, **kwargs):
        nhf = NhfModel.query.filter_by(id=id).first()
        if nhf:
            for key, value in kwargs.items():
                setattr(nhf, key, value)
            db.session.commit()
            return nhf
        else:
            return None

    @classmethod
    def deleteNhf(self, id):
        return NhfModel.query.filter(NhfModel.id == id).delete()
        