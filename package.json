{"name": "hrms-unified", "version": "1.0.0", "description": "HRMS - Human Resource Management System (Unified Application)", "main": "run_hrms.py", "scripts": {"start": "python run_hrms.py", "dev": "python run_hrms.py", "install-deps": "python run_hrms.py --install", "check": "python run_hrms.py --check", "backend": "cd hrms_backend && python -m uvicorn app.main:app --host 0.0.0.0 --port 8085 --reload", "frontend": "cd hrm_frontend && npm run dev", "test": "cd hrms_backend && python test_simple_onboard.py"}, "keywords": ["hrms", "hr", "human-resources", "employee-management", "onboarding", "payroll", "attendance", "leave-management"], "author": "HRMS Team", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/your-org/hrms"}, "engines": {"node": ">=16.0.0", "python": ">=3.8.0"}, "dependencies": {"@tailwindcss/postcss": "^4.1.11", "autoprefixer": "^10.4.21", "postcss": "^8.5.6", "tailwindcss": "^3.4.17"}}