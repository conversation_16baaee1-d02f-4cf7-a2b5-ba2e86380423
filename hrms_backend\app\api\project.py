from fastapi import APIRouter, HTTPException, status, Depends, Query
from sqlalchemy.orm import Session
from typing import List, Optional
from uuid import UUID

from ..core.security import get_current_user, require_permission, CurrentUser
from ..core.rbac import Permissions
from ..db.session import get_db
from ..schemas.project import (
    ProjectCreate, ProjectUpdate, ProjectResponse, ProjectListResponse,
    ProjectAssignmentCreate, ProjectAssignmentUpdate, ProjectAssignmentResponse,
    TaskCreate, TaskUpdate, TaskResponse, TaskListResponse,
    TaskCommentCreate, TaskCommentUpdate, TaskCommentResponse,
    TaskDependencyCreate, TaskDependencyResponse,
    ProjectMilestoneCreate, ProjectMilestoneUpdate, ProjectMilestoneResponse,
    BulkTaskUpdate, TaskAssignmentRequest, ProjectStatus, TaskStatus
)
from ..services.project_management.project_service import ProjectService

router = APIRouter()
project_service = ProjectService()


# Project endpoints
@router.get("/", response_model=ProjectListResponse)
async def get_projects(
    status: Optional[ProjectStatus] = Query(None),
    project_manager_id: Optional[UUID] = Query(None),
    department_id: Optional[UUID] = Query(None),
    search: Optional[str] = Query(None),
    skip: int = Query(0, ge=0),
    limit: int = Query(20, ge=1, le=100),
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.PROJECT_READ))
):
    """Get projects with filtering"""
    return await project_service.get_projects(
        db=db,
        status=status,
        project_manager_id=project_manager_id,
        department_id=department_id,
        search=search,
        skip=skip,
        limit=limit,
        current_user=current_user
    )


@router.post("/", response_model=ProjectResponse)
async def create_project(
    project_data: ProjectCreate,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.PROJECT_CREATE))
):
    """Create new project"""
    return await project_service.create_project(db, project_data, current_user)


@router.get("/{project_id}", response_model=ProjectResponse)
async def get_project(
    project_id: UUID,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.PROJECT_READ))
):
    """Get project by ID"""
    project = await project_service.get_project_by_id(db, project_id, current_user)
    if not project:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Project not found"
        )
    return project


@router.put("/{project_id}", response_model=ProjectResponse)
async def update_project(
    project_id: UUID,
    project_data: ProjectUpdate,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.PROJECT_UPDATE))
):
    """Update project"""
    project = await project_service.update_project(db, project_id, project_data, current_user)
    if not project:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Project not found"
        )
    return project


@router.delete("/{project_id}")
async def delete_project(
    project_id: UUID,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.PROJECT_DELETE))
):
    """Delete project (soft delete)"""
    success = await project_service.delete_project(db, project_id, current_user)
    if not success:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Project not found"
        )
    return {"message": "Project deleted successfully"}


# Task endpoints
@router.get("/{project_id}/tasks", response_model=TaskListResponse)
async def get_project_tasks(
    project_id: UUID,
    assignee_id: Optional[UUID] = Query(None),
    status: Optional[TaskStatus] = Query(None),
    priority: Optional[str] = Query(None),
    search: Optional[str] = Query(None),
    skip: int = Query(0, ge=0),
    limit: int = Query(20, ge=1, le=100),
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.TASK_READ))
):
    """Get tasks for a project"""
    return await project_service.get_tasks(
        db=db,
        project_id=project_id,
        assignee_id=assignee_id,
        status=status,
        priority=priority,
        search=search,
        skip=skip,
        limit=limit,
        current_user=current_user
    )


@router.post("/{project_id}/tasks", response_model=TaskResponse)
async def create_task(
    project_id: UUID,
    task_data: TaskCreate,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.TASK_CREATE))
):
    """Create new task in project"""
    # Ensure task is created in the specified project
    task_data.project_id = project_id
    return await project_service.create_task(db, task_data, current_user)


@router.get("/tasks/{task_id}", response_model=TaskResponse)
async def get_task(
    task_id: UUID,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.TASK_READ))
):
    """Get task by ID"""
    tasks = await project_service.get_tasks(
        db=db,
        skip=0,
        limit=1,
        current_user=current_user
    )

    # Find the specific task
    task = next((t for t in tasks.tasks if t.id == task_id), None)
    if not task:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Task not found"
        )

    return task


@router.put("/tasks/{task_id}", response_model=TaskResponse)
async def update_task(
    task_id: UUID,
    task_data: TaskUpdate,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.TASK_UPDATE))
):
    """Update task"""
    task = await project_service.update_task(db, task_id, task_data, current_user)
    if not task:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Task not found"
        )
    return task
