/**
 * Default Image Display Component
 * Shows a default grey image with option to change it for super admin users
 * Designed for AgnoConnect HRM with brand colors
 */

import { useState, useRef } from 'react';
import { Camera, Upload, X, RotateCcw, Grid, Image } from 'lucide-react';
import { defaultImages, coverPresets } from '../utils/defaultImages';

// Generate a default grey placeholder image
const generateGreyPlaceholder = (width = 800, height = 400) => {
  const svg = `
    <svg width="${width}" height="${height}" viewBox="0 0 ${width} ${height}" xmlns="http://www.w3.org/2000/svg">
      <defs>
        <linearGradient id="greyGradient" x1="0%" y1="0%" x2="100%" y2="100%">
          <stop offset="0%" style="stop-color:#f8fafc;stop-opacity:1" />
          <stop offset="100%" style="stop-color:#e2e8f0;stop-opacity:1" />
        </linearGradient>
      </defs>
      <rect width="${width}" height="${height}" fill="url(#greyGradient)"/>
      
      <!-- Subtle pattern -->
      <defs>
        <pattern id="greyDots" x="0" y="0" width="40" height="40" patternUnits="userSpaceOnUse">
          <circle cx="20" cy="20" r="1" fill="#cbd5e1" opacity="0.3"/>
        </pattern>
      </defs>
      <rect width="${width}" height="${height}" fill="url(#greyDots)"/>
      
      <!-- Center icon and text -->
      <g transform="translate(${width/2}, ${height/2})">
        <circle cx="0" cy="-20" r="30" fill="#6b7280" opacity="0.2"/>
        <path d="M-15,-30 L15,-30 L15,-10 L-15,-10 Z M-10,-25 L10,-25 L10,-15 L-10,-15 Z" fill="#9ca3af"/>
        <circle cx="-5" cy="-22" r="2" fill="#6b7280"/>
        <path d="M-8,-18 L-2,-15 L2,-18 L8,-15" stroke="#6b7280" stroke-width="1" fill="none"/>
        <text x="0" y="20" text-anchor="middle" fill="#6b7280" font-family="Arial, sans-serif" font-size="14" opacity="0.8">
          Click to add image
        </text>
      </g>
    </svg>
  `;
  return `data:image/svg+xml;base64,${btoa(svg)}`;
};

export default function DefaultImageDisplay({ onImageChange, className = "" }) {
  const [currentImage, setCurrentImage] = useState(generateGreyPlaceholder());
  const [isUploading, setIsUploading] = useState(false);
  const [showUploadOptions, setShowUploadOptions] = useState(false);
  const [showPresets, setShowPresets] = useState(false);
  const fileInputRef = useRef(null);

  // All users can change images
  const canChangeImage = true;

  // Handle image upload
  const handleImageUpload = async (event) => {
    const file = event.target.files[0];
    if (file) {
      setIsUploading(true);
      
      // Simulate upload delay
      setTimeout(() => {
        const reader = new FileReader();
        reader.onload = (e) => {
          const imageUrl = e.target.result;
          setCurrentImage(imageUrl);
          setIsUploading(false);
          setShowUploadOptions(false);
          
          // Callback to parent component
          if (onImageChange) {
            onImageChange(imageUrl);
          }
        };
        reader.readAsDataURL(file);
      }, 1000);
    }
  };

  // Reset to default grey placeholder
  const resetToDefault = () => {
    const defaultGrey = generateGreyPlaceholder();
    setCurrentImage(defaultGrey);
    setShowUploadOptions(false);
    if (onImageChange) {
      onImageChange(defaultGrey);
    }
  };

  // Select preset image
  const selectPreset = (preset) => {
    setCurrentImage(preset.url);
    setShowUploadOptions(false);
    setShowPresets(false);
    if (onImageChange) {
      onImageChange(preset.url);
    }
  };

  return (
    <div className={`relative w-full h-[400px] overflow-hidden rounded-lg border border-gray-200 ${className}`}>
      {/* Background Image */}
      <img
        src={currentImage}
        alt="Default Display"
        className="w-full h-full object-cover"
      />

      {/* Upload Controls - Available for all users */}
      {canChangeImage && (
        <div className="absolute top-4 right-4">
          <div className="relative">
            <button
              onClick={() => setShowUploadOptions(!showUploadOptions)}
              className="p-2 agno-bg-primary-dark text-white rounded-full shadow-lg hover:bg-opacity-90 transition-all"
              title="Change Image"
            >
              <Camera size={18} />
            </button>

            {/* Upload Options Dropdown */}
            {showUploadOptions && (
              <div className="absolute top-12 right-0 bg-white rounded-lg shadow-lg border border-gray-200 p-2 min-w-[180px] z-10">
                {/* Upload new image */}
                <button
                  onClick={() => fileInputRef.current?.click()}
                  disabled={isUploading}
                  className="w-full flex items-center gap-2 px-3 py-2 text-sm agno-bg-orange text-white rounded hover:bg-opacity-90 disabled:opacity-50 mb-2"
                >
                  <Upload size={16} />
                  {isUploading ? 'Uploading...' : 'Upload Image'}
                </button>

                {/* Browse presets */}
                <button
                  onClick={() => setShowPresets(!showPresets)}
                  className="w-full flex items-center gap-2 px-3 py-2 text-sm bg-gray-100 text-gray-700 rounded hover:bg-gray-200 mb-2"
                >
                  <Grid size={16} />
                  Browse Presets
                </button>

                {/* Reset to default */}
                <button
                  onClick={resetToDefault}
                  className="w-full flex items-center gap-2 px-3 py-2 text-sm bg-gray-100 text-gray-700 rounded hover:bg-gray-200"
                >
                  <RotateCcw size={16} />
                  Reset to Default
                </button>
              </div>
            )}

            {/* Preset Gallery */}
            {showPresets && (
              <div className="absolute top-12 right-0 bg-white rounded-lg shadow-lg border border-gray-200 p-4 w-[320px] z-20">
                <div className="flex items-center justify-between mb-3">
                  <h3 className="font-medium text-gray-900">Choose Preset</h3>
                  <button
                    onClick={() => setShowPresets(false)}
                    className="text-gray-400 hover:text-gray-600"
                  >
                    <X size={16} />
                  </button>
                </div>
                
                <div className="grid grid-cols-2 gap-2">
                  {coverPresets.map((preset) => (
                    <button
                      key={preset.id}
                      onClick={() => selectPreset(preset)}
                      className="relative group overflow-hidden rounded border border-gray-200 hover:border-agno-orange transition-colors"
                    >
                      <img
                        src={preset.thumbnail}
                        alt={preset.name}
                        className="w-full h-16 object-cover"
                      />
                      <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all" />
                      <div className="absolute bottom-0 left-0 right-0 bg-black bg-opacity-50 text-white text-xs p-1 text-center">
                        {preset.name}
                      </div>
                    </button>
                  ))}
                </div>
              </div>
            )}
          </div>

          {/* Hidden file input */}
          <input
            ref={fileInputRef}
            type="file"
            accept="image/*"
            onChange={handleImageUpload}
            className="hidden"
          />
        </div>
      )}

      {/* Loading overlay */}
      {isUploading && (
        <div className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center">
          <div className="bg-white rounded-lg p-4 flex items-center gap-3">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-agno-orange"></div>
            <span className="text-gray-700">Uploading image...</span>
          </div>
        </div>
      )}

      {/* Interactive overlay for all users */}
      {!showUploadOptions && !showPresets && (
        <div className="absolute inset-0 bg-black bg-opacity-0 hover:bg-opacity-5 transition-all cursor-pointer"
             onClick={() => setShowUploadOptions(true)} />
      )}
    </div>
  );
}
