from core.repositories.activity_employees import EmployeeActivityRepository

class EmployeeActivityService:
    def __init__(self) -> None:
        self.repository = EmployeeActivityRepository()

    def logActivity(self, employee_id, message):
        return self.repository.log_activity(employee_id, message)

    def fetchAllActivitiesForEmployee(self, employee_id):
        activities = self.repository.fetch_all_by_employee(employee_id)
        total = len(activities)
        return activities, total

    def getActivityById(self, id):
        return self.repository.fetch_by_id(id)

    def deleteActivity(self, id):
        return self.repository.delete_activity(id)
