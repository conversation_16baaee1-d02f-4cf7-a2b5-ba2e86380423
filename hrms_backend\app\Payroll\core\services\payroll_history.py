from core.models.employees import EmployeeModel
from core.models.payroll_history import PayrollHistoryModel
from core.repositories.payroll_history import PayrollHistoryRepository
from core.repositories.remarks import RemarksRepository
from core.repositories.user import UserRepository
import db
from core.databases.database import db

class PayrollHistoryService:
    def __init__(self) -> None:
        self.repository = PayrollHistoryRepository()
        self.user = PayrollHistoryRepository()
        # print(f"Repository initialized: {self.repository}") 
        
    def create_payroll_history(self, data):
        return self.repository.create_payroll_history(**data)
    
    def createPayrollHistory(self, employees):
        return self.repository.bulk_create_payroll_history(employees)
    
    def createOrUpdatePayrollHistory(self, payroll_data):
        records = ""
        for data in payroll_data:
            existing_record = self.repository.get_by_employee_and_schedule(data["employee_id"], data["pay_schedle_id"])

            if existing_record:                
                records = self.repository.update_payroll_history(existing_record.id, **data)
            else:
                records = self.repository.create_payroll_history(**data)

        return records


    def get_payroll_hostory_id(self, id):
        return self.repository.get_by_id(id)
    
    def get_payroll_history_by_employee_id(self, employee_id=None):
        if employee_id is None:
            return self.user.get_by_employee_id(UserRepository.authUserId())
        return self.repository.get_by_employee_id(employee_id)
    
    def getAllPayrollHistory(self):
        payroll_history = self.repository.fetchPayrollHistory()
        total_histories = len(payroll_history)
        return payroll_history, total_histories
    
    def get_payroll_histories_by_user(self, user_id):
        payroll_histories = PayrollHistoryModel.query.filter_by(user_id=user_id).all()
        total_count = len(payroll_histories)
        return payroll_histories, total_count
    
    def get_most_recent_payroll_history_by_employee_id(self, employee_id):
        return self.repository.get_most_recent_by_employee_id(employee_id)    
    
    def update_payroll_history(self, id, **kwargs):
        """Update payroll history record in the database."""
        payroll_history_record = PayrollHistoryModel.query.get(id)

        print(f"payroll_history_record::  {payroll_history_record}")
        
        if not payroll_history_record:
            return None  # Record not found
        
        # Update the fields using the provided keyword arguments
        for key, value in kwargs.items():
            setattr(payroll_history_record, key, value)

        db.session.commit()  # Commit the changes to the database
        return payroll_history_record  # Return the updated record

    def get_payroll_history_by_id(self, id):
        """Fetch a specific payroll history record by ID."""
        return self.repository.get_payroll_history_by_id(id)
    

    def delete_payroll_history_by_employee_id(self, employee_id):
        # Fetch payroll history by employee ID
        payroll_history_records = PayrollHistoryModel.query.filter_by(employee_id=employee_id).all()
        
        if payroll_history_records:
            for record in payroll_history_records:
                db.session.delete(record)
            db.session.commit()
        return True
    
    def get_payroll_history_by_id(self, payroll_history_id):
        """
        Retrieve payroll history by payroll_history_id.
        """
        return PayrollHistoryModel.query.filter_by(id=payroll_history_id).first()
    
    def delete_payroll_history_by_id(self, id):
        """
        Delete a single payroll history record by payroll_history_id.
        """
        payroll_history = self.get_payroll_history_by_id(id)

        if payroll_history:
            db.session.delete(payroll_history)
            db.session.commit()
            return True
        return False
    
    def filter_payroll_history(self, department=None, designation=None, status=None, date=None):
        filters = []

        if department:
            filters.append(PayrollHistoryModel.department == department)
        if designation:
            filters.append(PayrollHistoryModel.designation == designation)
        if status:
            filters.append(PayrollHistoryModel.status == status)
        if date:
            filters.append(PayrollHistoryModel.created_at == date)

        # Apply the filters dynamically
        payroll_history_records = PayrollHistoryModel.query.filter(and_(*filters)).all()
        return payroll_history_records

    def get_monthly_payroll_report(self, month, year):
        # Query the database and filter records by month and year
        return self.repository.fetch_monthly_payroll_data(month, year)
    
    # def getEmployeeById(self, employee_id):
    #     # Assuming you have an EmployeeModel to query from the database
    #     return db.session.query(EmployeeModel).filter_by(id=employee_id).first()
    
    def getEmployeeById(self, employee_id):
        # print(f"Querying for employee with ID: {employee_id}")
        employee = db.session.query(EmployeeModel).filter_by(id=employee_id).first()
        if employee:
            print(f"Employee found: {employee}")
        else:
            print(f"Employee with ID {employee_id} does not exist.")
        return employee
    
    def deletePayrollHistory(self, payroll_history_id):
        """
        Call the repository method to delete a payroll history record and its associated remarks.
        """
        return RemarksRepository.deletePayrollHistory(payroll_history_id)
    
    def bulkDeletePayrollHistory(self, payroll_history_ids):
     """
      Call the repository method to delete multiple payroll history records and their associated remarks.
     """
     return RemarksRepository.bulkDeletePayrollHistory(payroll_history_ids)
    
    # def update_payroll_history_transaction(self, id, **Kwargs):
    #     return self.repository.update_payroll_history_transaction_id(id,**Kwargs)
    
    # def update_payroll_history_with_transaction_id(self, id, **Kwargs):
    #     return self.repository.update_payroll_history_with_transaction_id(id,**Kwargs)

    # Note: the method update payroll history by payroll id
    def payroll_history_update(self, id, kwargs):
        return self.repository.update_payroll_history(id,**kwargs)
    
    def get_payroll_history_by_key(self, filters):
        return self.repository.fetchPayrollHistoryByKeys(filters)
    
    def emp_is_processed(self,id, **Kwargs):
        pass

    def process_payroll(self, ids):
        existing_ids = self.repository.get_existing_payroll_ids(ids)
        missing_ids = set(ids) - existing_ids
        if not existing_ids:
            return {"error": "No valid payroll IDs found.", "missing_ids": list(missing_ids)}, 400

        update_result = self.repository.bulk_update_payroll_history(existing_ids)
        
        if isinstance(update_result, dict):
            update_result["missing_ids"] = list(missing_ids) if missing_ids else None
        
        return update_result, existing_ids
    
    def verify_payroll_ids(self, ids):
        existing_ids = self.repository.get_existing_payroll_ids(ids)
        missing_ids = set(ids) - existing_ids
        if not existing_ids:
            return {"error": "No valid payroll IDs found.", "missing_ids": list(missing_ids)}, 400
                
        return existing_ids
    
    def filter_duplicated_records(self, employees):
        existing_ids = self.repository.get_existing_payroll_ids_for_current_month()
        # print("Existing Payroll IDs: ", existing_ids)
        
        # Extract only employee IDs
        employee_ids = {employee["id"] for employee in employees}

        # Find employees NOT in the existing payroll
        missing_ids = employee_ids - existing_ids
        
        # print("Missing Employee IDs: ", missing_ids)
        return missing_ids  # Return only the employees that need payroll
    
    def update_payroll_history_webhook_response(self, transaction_id, reference_code, message, transaction_date):
        """Update payroll history message field using `reference_code`."""

        payroll_history = PayrollHistoryRepository.get_payroll_history(transaction_id, reference_code)

        if payroll_history:
            payroll_history.message = message  
            payroll_history.transaction_date = transaction_date  
            db.session.commit()
            # print(f"✅ Payroll history updated for reference_code: {reference_code}")
            return {"status": "success", "message": "Transfer processed"}, 200
        else:
            # print(f"❌ Error: Payroll history not found for reference_code: {reference_code}")
            return {"status": "error", "message": "Payroll history not found"}, 404
    
    def clear_unprocessed_payroll_history(self):
        return self.repository.delete_unprocessed_payroll_history()

    def getPayrollsByLatestSchedule(self):
        return self.repository.getPayrollsByLatestSchedule()
    
    def get_payroll_histories_by_schedule(self, pay_schedule_id: int, page: int, limit: int):
        return self.repository.get_by_pay_schedule_id(pay_schedule_id, page, limit)
    
    def sync_employees_to_pay_schedule(self, schedule_id):
        return PayrollHistoryRepository.sync_active_employees_to_schedule(schedule_id)

