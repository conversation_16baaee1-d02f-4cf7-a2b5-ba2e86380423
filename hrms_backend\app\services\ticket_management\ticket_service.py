from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, func
from typing import Optional, List, Dict, Any
from uuid import UUID
from datetime import datetime, timedelta
from fastapi import HTTPException, status
import logging

from ...db.models.ticket import (
    Ticket, TicketComment, TicketActivity, TicketEscalation,
    TicketCategory, TicketSLA
)
from ...db.models.employee import Employee
from ...schemas.ticket import (
    TicketCreate, TicketUpdate, TicketResponse, TicketListResponse,
    TicketCommentCreate, TicketCommentUpdate, TicketCommentResponse,
    TicketAssignmentRequest, TicketResolutionRequest,
    TicketEscalationCreate, TicketEscalationResponse,
    TicketStatus, TicketPriority, TicketType
)
from ...core.security import CurrentUser
from ...core.websocket_manager import notification_manager

logger = logging.getLogger(__name__)


class TicketService:
    """Ticket service for business logic"""

    async def create_ticket(
        self,
        db: Session,
        ticket_data: TicketCreate,
        current_user: CurrentUser
    ) -> TicketResponse:
        """Create new ticket"""
        try:
            # Generate ticket number
            ticket_count = db.query(Ticket).filter(
                Ticket.organization_id == current_user.organization_id
            ).count()
            ticket_number = f"TKT-{ticket_count + 1:06d}"

            # Get SLA configuration
            sla_config = await self._get_sla_config(
                db, current_user.organization_id, ticket_data.ticket_type, ticket_data.priority
            )

            # Calculate due date based on SLA
            due_date = None
            if sla_config and sla_config.resolution_hours:
                due_date = datetime.utcnow() + timedelta(hours=sla_config.resolution_hours)

            # Create ticket
            ticket = Ticket(
                **ticket_data.dict(exclude={'attachment_urls'}),
                ticket_number=ticket_number,
                requester_id=current_user.user_id,
                organization_id=current_user.organization_id,
                status=TicketStatus.OPEN,
                due_date=due_date,
                response_sla_hours=sla_config.first_response_hours if sla_config else None,
                resolution_sla_hours=sla_config.resolution_hours if sla_config else None,
                attachment_urls=ticket_data.attachment_urls or [],
                created_by=current_user.user_id
            )

            db.add(ticket)
            db.commit()
            db.refresh(ticket)

            # Auto-assign if rules exist
            await self._auto_assign_ticket(db, ticket, current_user)

            # Create activity log
            await self._create_activity(
                db, ticket.id, "created", f"Ticket created by {current_user.email}", current_user
            )

            # Send notifications
            await self._notify_ticket_created(ticket, current_user)

            logger.info(f"Ticket {ticket.ticket_number} created by {current_user.user_id}")
            return TicketResponse.from_orm(ticket)

        except HTTPException:
            raise
        except Exception as e:
            db.rollback()
            logger.error(f"Error creating ticket: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Error creating ticket"
            )

    async def get_tickets(
        self,
        db: Session,
        status: Optional[TicketStatus] = None,
        priority: Optional[TicketPriority] = None,
        ticket_type: Optional[TicketType] = None,
        assigned_to: Optional[UUID] = None,
        requester_id: Optional[UUID] = None,
        search: Optional[str] = None,
        skip: int = 0,
        limit: int = 20,
        current_user: CurrentUser = None
    ) -> TicketListResponse:
        """Get tickets with filtering"""
        try:
            query = db.query(Ticket).filter(
                Ticket.organization_id == current_user.organization_id
            )

            # Apply filters
            if status:
                query = query.filter(Ticket.status == status)

            if priority:
                query = query.filter(Ticket.priority == priority)

            if ticket_type:
                query = query.filter(Ticket.ticket_type == ticket_type)

            if assigned_to:
                query = query.filter(Ticket.assigned_to == assigned_to)

            if requester_id:
                query = query.filter(Ticket.requester_id == requester_id)

            if search:
                search_filter = or_(
                    Ticket.title.ilike(f"%{search}%"),
                    Ticket.description.ilike(f"%{search}%"),
                    Ticket.ticket_number.ilike(f"%{search}%")
                )
                query = query.filter(search_filter)

            # Get total count
            total = query.count()

            # Apply pagination and ordering
            tickets = query.order_by(
                Ticket.created_at.desc()
            ).offset(skip).limit(limit).all()

            # Convert to response format
            ticket_responses = [TicketResponse.from_orm(ticket) for ticket in tickets]

            return TicketListResponse(
                tickets=ticket_responses,
                total=total,
                skip=skip,
                limit=limit
            )

        except Exception as e:
            logger.error(f"Error getting tickets: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Error retrieving tickets"
            )

    async def update_ticket(
        self,
        db: Session,
        ticket_id: UUID,
        ticket_data: TicketUpdate,
        current_user: CurrentUser
    ) -> Optional[TicketResponse]:
        """Update ticket"""
        try:
            ticket = db.query(Ticket).filter(
                Ticket.id == ticket_id,
                Ticket.organization_id == current_user.organization_id
            ).first()

            if not ticket:
                return None

            # Store old values for activity tracking
            old_status = ticket.status
            old_assigned_to = ticket.assigned_to

            # Update fields
            update_data = ticket_data.dict(exclude_unset=True, exclude={'attachment_urls'})
            for field, value in update_data.items():
                setattr(ticket, field, value)

            # Handle attachment URLs
            if ticket_data.attachment_urls is not None:
                ticket.attachment_urls = ticket_data.attachment_urls

            # Update timestamps based on status changes
            if ticket_data.status:
                if ticket_data.status == TicketStatus.RESOLVED and not ticket.resolved_at:
                    ticket.resolved_at = datetime.utcnow()
                elif ticket_data.status == TicketStatus.CLOSED and not ticket.closed_at:
                    ticket.closed_at = datetime.utcnow()

            ticket.updated_by = current_user.user_id

            db.commit()
            db.refresh(ticket)

            # Create activity logs for changes
            if old_status != ticket.status:
                await self._create_activity(
                    db, ticket.id, "status_changed",
                    f"Status changed from {old_status} to {ticket.status}", current_user
                )

            if old_assigned_to != ticket.assigned_to:
                await self._create_activity(
                    db, ticket.id, "assigned",
                    f"Ticket assigned to {ticket.assigned_to}", current_user
                )

                # Send notification to new assignee
                if ticket.assigned_to:
                    await notification_manager.notify_ticket_update(
                        str(ticket.assigned_to),
                        {
                            "ticket_id": str(ticket.id),
                            "ticket_number": ticket.ticket_number,
                            "action": "assigned",
                            "assigned_by": current_user.email
                        }
                    )

            logger.info(f"Ticket {ticket.ticket_number} updated by {current_user.user_id}")
            return TicketResponse.from_orm(ticket)

        except Exception as e:
            db.rollback()
            logger.error(f"Error updating ticket {ticket_id}: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Error updating ticket"
            )

    async def resolve_ticket(
        self,
        db: Session,
        ticket_id: UUID,
        resolution_data: TicketResolutionRequest,
        current_user: CurrentUser
    ) -> Optional[TicketResponse]:
        """Resolve ticket"""
        try:
            ticket = db.query(Ticket).filter(
                Ticket.id == ticket_id,
                Ticket.organization_id == current_user.organization_id
            ).first()

            if not ticket:
                return None

            # Update ticket
            ticket.status = TicketStatus.RESOLVED
            ticket.resolution = resolution_data.resolution
            ticket.resolution_category = resolution_data.resolution_category
            ticket.resolved_at = datetime.utcnow()
            ticket.updated_by = current_user.user_id

            db.commit()
            db.refresh(ticket)

            # Create activity log
            await self._create_activity(
                db, ticket.id, "resolved",
                f"Ticket resolved: {resolution_data.resolution}", current_user
            )

            # Send notification to requester
            if resolution_data.notify_requester:
                await notification_manager.notify_ticket_update(
                    str(ticket.requester_id),
                    {
                        "ticket_id": str(ticket.id),
                        "ticket_number": ticket.ticket_number,
                        "action": "resolved",
                        "resolution": resolution_data.resolution,
                        "resolved_by": current_user.email
                    }
                )

            logger.info(f"Ticket {ticket.ticket_number} resolved by {current_user.user_id}")
            return TicketResponse.from_orm(ticket)

        except Exception as e:
            db.rollback()
            logger.error(f"Error resolving ticket {ticket_id}: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Error resolving ticket"
            )

    # Helper methods
    async def _get_sla_config(
        self,
        db: Session,
        organization_id: UUID,
        ticket_type: TicketType,
        priority: TicketPriority
    ) -> Optional[TicketSLA]:
        """Get SLA configuration for ticket type and priority"""
        return db.query(TicketSLA).filter(
            TicketSLA.organization_id == organization_id,
            TicketSLA.ticket_type == ticket_type,
            TicketSLA.priority == priority,
            TicketSLA.is_active == True
        ).first()

    async def _auto_assign_ticket(
        self,
        db: Session,
        ticket: Ticket,
        current_user: CurrentUser
    ):
        """Auto-assign ticket based on rules"""
        try:
            # Simple round-robin assignment for now
            # In production, this would use more sophisticated rules

            # Get available agents for this ticket type
            agents = db.query(Employee).filter(
                Employee.organization_id == current_user.organization_id,
                Employee.is_active == True,
                # Add role/department filtering based on ticket type
            ).all()

            if agents:
                # Simple assignment to first available agent
                ticket.assigned_to = agents[0].id
                ticket.auto_assigned = True
                db.commit()

                # Send notification
                await notification_manager.notify_ticket_update(
                    str(ticket.assigned_to),
                    {
                        "ticket_id": str(ticket.id),
                        "ticket_number": ticket.ticket_number,
                        "action": "auto_assigned",
                        "title": ticket.title
                    }
                )

        except Exception as e:
            logger.error(f"Error auto-assigning ticket {ticket.id}: {e}")

    async def bulk_update_tickets(
        self,
        db: Session,
        ticket_ids: List[UUID],
        update_data: Dict[str, Any],
        current_user: CurrentUser
    ) -> Dict[str, Any]:
        """Bulk update multiple tickets"""
        try:
            # Verify all tickets exist and user has permission
            tickets = db.query(Ticket).filter(
                Ticket.id.in_(ticket_ids),
                Ticket.organization_id == current_user.organization_id
            ).all()

            if len(tickets) != len(ticket_ids):
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="One or more tickets not found"
                )

            updated_count = 0
            failed_updates = []

            for ticket in tickets:
                try:
                    # Apply updates
                    for field, value in update_data.items():
                        if hasattr(ticket, field) and field not in ['id', 'ticket_number', 'organization_id', 'created_at']:
                            if field == "status" and value:
                                setattr(ticket, field, TicketStatus(value))
                                if value == TicketStatus.RESOLVED:
                                    ticket.resolved_at = datetime.utcnow()
                                elif value == TicketStatus.CLOSED:
                                    ticket.closed_at = datetime.utcnow()
                            elif field == "priority" and value:
                                setattr(ticket, field, TicketPriority(value))
                            else:
                                setattr(ticket, field, value)

                    ticket.updated_by = current_user.user_id
                    ticket.updated_at = datetime.utcnow()

                    # Create activity log
                    await self._create_activity(
                        db, ticket.id, "bulk_updated",
                        f"Ticket bulk updated by {current_user.email}",
                        current_user
                    )

                    updated_count += 1

                except Exception as e:
                    failed_updates.append({
                        "ticket_id": str(ticket.id),
                        "error": str(e)
                    })

            db.commit()

            logger.info(f"Bulk updated {updated_count} tickets by {current_user.user_id}")
            return {
                "updated_count": updated_count,
                "failed_updates": failed_updates,
                "total_requested": len(ticket_ids)
            }

        except HTTPException:
            raise
        except Exception as e:
            db.rollback()
            logger.error(f"Error in bulk update: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Error performing bulk update"
            )

    async def submit_satisfaction_rating(
        self,
        db: Session,
        ticket_id: UUID,
        rating: int,
        feedback: Optional[str],
        current_user: CurrentUser
    ) -> Optional[TicketResponse]:
        """Submit satisfaction rating for a ticket"""
        try:
            ticket = db.query(Ticket).filter(
                Ticket.id == ticket_id,
                Ticket.organization_id == current_user.organization_id,
                Ticket.requester_id == current_user.user_id  # Only requester can rate
            ).first()

            if not ticket:
                return None

            if ticket.status not in [TicketStatus.RESOLVED, TicketStatus.CLOSED]:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Can only rate resolved or closed tickets"
                )

            if not (1 <= rating <= 5):
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Rating must be between 1 and 5"
                )

            ticket.satisfaction_rating = rating
            ticket.satisfaction_feedback = feedback
            ticket.updated_by = current_user.user_id
            ticket.updated_at = datetime.utcnow()

            db.commit()
            db.refresh(ticket)

            # Create activity log
            await self._create_activity(
                db, ticket.id, "satisfaction_rated",
                f"Satisfaction rating submitted: {rating}/5" + (f" - {feedback}" if feedback else ""),
                current_user
            )

            logger.info(f"Satisfaction rating {rating} submitted for ticket {ticket.ticket_number}")
            return TicketResponse.from_orm(ticket)

        except HTTPException:
            raise
        except Exception as e:
            db.rollback()
            logger.error(f"Error submitting satisfaction rating: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Error submitting satisfaction rating"
            )

    async def search_knowledge_base(
        self,
        db: Session,
        query: str,
        ticket_type: Optional[TicketType] = None,
        current_user: CurrentUser = None
    ) -> List[Dict[str, Any]]:
        """Search knowledge base for relevant articles"""
        try:
            # This is a simplified implementation
            # In production, you would integrate with a proper knowledge base system

            # Mock knowledge base articles
            knowledge_base = [
                {
                    "id": "kb-001",
                    "title": "How to reset your password",
                    "content": "To reset your password, go to the login page and click 'Forgot Password'...",
                    "category": "IT Support",
                    "ticket_types": ["it_support", "system_access"],
                    "tags": ["password", "reset", "login", "access"]
                },
                {
                    "id": "kb-002",
                    "title": "Leave application process",
                    "content": "To apply for leave, log into the HRMS system and navigate to Leave Management...",
                    "category": "HR",
                    "ticket_types": ["hr_query", "leave"],
                    "tags": ["leave", "application", "process", "hr"]
                },
                {
                    "id": "kb-003",
                    "title": "Equipment request procedure",
                    "content": "To request new equipment, submit a ticket with detailed specifications...",
                    "category": "Facilities",
                    "ticket_types": ["equipment", "facilities"],
                    "tags": ["equipment", "request", "hardware", "facilities"]
                }
            ]

            # Simple search implementation
            query_words = query.lower().split()
            relevant_articles = []

            for article in knowledge_base:
                score = 0

                # Check if ticket type matches
                if ticket_type and ticket_type.value in article["ticket_types"]:
                    score += 10

                # Check title match
                title_words = article["title"].lower().split()
                for word in query_words:
                    if word in title_words:
                        score += 5

                # Check content match
                content_words = article["content"].lower().split()
                for word in query_words:
                    if word in content_words:
                        score += 2

                # Check tags match
                for word in query_words:
                    if word in article["tags"]:
                        score += 3

                if score > 0:
                    relevant_articles.append({
                        **article,
                        "relevance_score": score
                    })

            # Sort by relevance score
            relevant_articles.sort(key=lambda x: x["relevance_score"], reverse=True)

            # Return top 5 results
            return relevant_articles[:5]

        except Exception as e:
            logger.error(f"Error searching knowledge base: {e}")
            return []

    async def get_ticket_analytics(
        self,
        db: Session,
        organization_id: UUID,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None
    ) -> Dict[str, Any]:
        """Get advanced ticket analytics"""
        try:
            if not start_date:
                start_date = datetime.utcnow() - timedelta(days=30)
            if not end_date:
                end_date = datetime.utcnow()

            # Base query
            base_query = db.query(Ticket).filter(
                Ticket.organization_id == organization_id,
                Ticket.created_at >= start_date,
                Ticket.created_at <= end_date
            )

            # Channel analysis (mock data - would be real in production)
            channel_breakdown = {
                "email": base_query.filter(Ticket.contact_method == "email").count(),
                "phone": base_query.filter(Ticket.contact_method == "phone").count(),
                "web_portal": base_query.filter(Ticket.contact_method == "web_portal").count(),
                "chat": base_query.filter(Ticket.contact_method == "chat").count(),
                "mobile_app": base_query.filter(Ticket.contact_method == "mobile_app").count()
            }

            # First response time analysis
            tickets_with_response = base_query.filter(
                Ticket.first_response_at.isnot(None)
            ).all()

            avg_first_response_hours = 0
            if tickets_with_response:
                total_response_time = sum([
                    (ticket.first_response_at - ticket.created_at).total_seconds() / 3600
                    for ticket in tickets_with_response
                ])
                avg_first_response_hours = total_response_time / len(tickets_with_response)

            # Reopened tickets analysis
            reopened_tickets = base_query.filter(
                Ticket.status == TicketStatus.OPEN,
                Ticket.resolved_at.isnot(None)
            ).count()

            # Customer effort score (mock calculation)
            total_tickets = base_query.count()
            customer_effort_score = 3.2  # Mock score

            return {
                "period": {
                    "start_date": start_date.isoformat(),
                    "end_date": end_date.isoformat()
                },
                "channel_breakdown": channel_breakdown,
                "avg_first_response_hours": round(avg_first_response_hours, 2),
                "reopened_tickets": reopened_tickets,
                "customer_effort_score": customer_effort_score,
                "total_tickets": total_tickets
            }

        except Exception as e:
            logger.error(f"Error getting ticket analytics: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Error retrieving ticket analytics"
            )

    async def _create_activity(
        self,
        db: Session,
        ticket_id: UUID,
        activity_type: str,
        description: str,
        current_user: CurrentUser
    ):
        """Create ticket activity log"""
        try:
            activity = TicketActivity(
                ticket_id=ticket_id,
                activity_type=activity_type,
                description=description,
                user_id=current_user.user_id,
                created_at=datetime.utcnow()
            )
            db.add(activity)
            db.commit()
        except Exception as e:
            logger.error(f"Error creating activity log: {e}")

    async def _notify_ticket_created(
        self,
        ticket: Ticket,
        current_user: CurrentUser
    ):
        """Send notifications when ticket is created"""
        try:
            # Notify assigned agent if auto-assigned
            if ticket.assigned_to:
                await notification_manager.notify_ticket_update(
                    str(ticket.assigned_to),
                    {
                        "ticket_id": str(ticket.id),
                        "ticket_number": ticket.ticket_number,
                        "action": "created",
                        "title": ticket.title,
                        "priority": ticket.priority,
                        "requester": current_user.email
                    }
                )
        except Exception as e:
            logger.error(f"Error sending ticket notifications: {e}")
