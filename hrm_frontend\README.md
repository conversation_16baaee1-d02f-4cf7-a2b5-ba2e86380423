# AgnoConnect HRM Frontend

A modern Human Resource Management (HRM) frontend application built with React, featuring comprehensive role-based access control (RBAC), advanced attendance tracking, and a clean, professional interface with AgnoConnect branding.

## 🚀 Recent Enhancements

This application has been enhanced with advanced attendance and time tracking features, including:

- **Enhanced Attendance Tracking**: Timeline view, real-time timer, check-in/out functionality
- **Advanced Calendar**: FullCalendar integration with attendance events and meeting management
- **Real-time Time Tracker**: Live timer with location tracking and notes
- **Interactive Dashboard**: TopBanner with quick stats and action buttons
- **Improved UI Components**: Material-UI integration alongside Tailwind CSS

## ✨ Features

### Core Features
- **Role-Based Access Control (RBAC)**: Comprehensive permission system with 5 user roles
- **Modern UI**: Clean, responsive design using Tailwind CSS + Material-UI components
- **Employee Management**: Directory, profiles, and organizational structure
- **Dashboard**: Enhanced overview with TopBanner, quick stats, and time tracker
- **Responsive Design**: Works seamlessly on desktop and mobile devices

### Enhanced Attendance Features
- **Timeline View**: Visual timeline showing daily attendance with check-in/out times
- **Real-time Timer**: Live work timer with check-in/out functionality
- **Attendance Summary**: Weekly view with payable days, present/absent tracking
- **Location Tracking**: Work location selection (Office, Home, Client Site)
- **Notes System**: Add notes for check-out and work sessions

### Advanced Calendar
- **FullCalendar Integration**: Professional calendar with event management
- **Attendance Events**: Visual representation of attendance status on calendar
- **Event Management**: Create and manage meetings, deadlines, and events
- **Multiple Views**: Switch between attendance, events, and combined views
- **Interactive Events**: Click events for details and management

### Time Tracking
- **Compact Timer**: Header-integrated timer for quick access
- **Full Time Tracker**: Dedicated page with comprehensive tracking
- **Work Statistics**: Weekly, monthly, and overtime tracking
- **Session Management**: Start/stop work sessions with location and notes

## 🛠 Technology Stack

- **Frontend**: React 18 with Vite
- **Styling**: Tailwind CSS + Material-UI components
- **Icons**: Lucide React + Material-UI Icons
- **Calendar**: FullCalendar React
- **State Management**: React Context API
- **Authentication**: Context-based auth system
- **Animations**: CSS transitions and Material-UI animations

## 📦 Dependencies Added

```json
{
  "@emotion/react": "^11.14.0",
  "@emotion/styled": "^11.14.0",
  "@fullcalendar/daygrid": "^6.1.17",
  "@fullcalendar/interaction": "^6.1.17",
  "@fullcalendar/react": "^6.1.17",
  "@mui/icons-material": "^7.1.1",
  "@mui/material": "^7.1.1",
  "@mui/styled-engine-sc": "^7.1.1",
  "styled-components": "^6.1.18"
}
```

## 🚀 Getting Started

### Prerequisites

- Node.js (version 16 or higher)
- npm or yarn

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd hrm_frontend
```

2. Install dependencies:
```bash
npm install
```

3. Start the development server:
```bash
npm run dev
```

4. Open your browser and navigate to `http://localhost:5173` (or the port shown in terminal)

## 🔐 RBAC System

The application implements a comprehensive Role-Based Access Control system with 5 distinct roles:

### Roles Hierarchy
1. **Super Admin** - Full system access
2. **Admin** - Administrative access with some restrictions
3. **HR** - Human resources focused permissions
4. **Manager** - Team management capabilities
5. **Employee** - Basic user access

### Permission Types
- **Full**: Complete access to the feature
- **Team Only**: Access limited to team members
- **Self Only**: Access limited to own data
- **None**: No access to the feature

### New Permissions Added
- `calendarEvents`: View calendar and events
- `calendarEventManagement`: Create and manage calendar events
- `timeTracker`: Access to time tracking features

For detailed permission matrix, see [RBAC_DOCUMENTATION.md](./RBAC_DOCUMENTATION.md)

## 📁 Project Structure

```
src/
├── components/          # Reusable UI components
│   ├── Header.jsx      # Enhanced header with time tracker
│   ├── Sidebar.jsx     # Main navigation sidebar
│   ├── ProtectedRoute.jsx # RBAC components
│   ├── TimeTracker.jsx # Time tracking components
│   ├── TopBanner.jsx   # Dashboard banner component
│   └── ...
├── contexts/           # React contexts
│   └── AuthContext.jsx # Authentication context
├── hooks/              # Custom React hooks
│   └── usePermissions.js # Permission checking hook
├── pages/              # Page components
│   ├── Dashboard.jsx   # Enhanced dashboard with TopBanner
│   ├── Employees.jsx   # Employee management
│   ├── Attendance.jsx  # Enhanced attendance with timeline
│   ├── Calendar.jsx    # FullCalendar integration
│   └── ...
├── services/           # Business logic and API services
│   └── permissions.js  # Enhanced RBAC permission definitions
├── styles/             # CSS files
│   └── calendar.css    # FullCalendar custom styles
└── utils/              # Utility functions
```

## 🎯 Key Components

### Enhanced Attendance (`src/pages/Attendance.jsx`)
- Timeline view with visual attendance tracking
- Real-time work timer with check-in/out functionality
- Multiple view modes (timeline, grid, list)
- Attendance summary with payable days tracking

### Advanced Calendar (`src/pages/Calendar.jsx`)
- FullCalendar integration with custom styling
- Attendance events visualization
- Event management with RBAC controls
- Multiple view filters (attendance, events, all)

### Time Tracker (`src/components/TimeTracker.jsx`)
- Full-featured time tracking component
- Compact version for header integration
- Location tracking and session notes
- Work statistics and overtime tracking

### Top Banner (`src/components/TopBanner.jsx`)
- Welcome message with personalized greetings
- Quick stats dashboard
- Action buttons with RBAC integration
- Responsive design with gradient background

## 🔧 Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run preview` - Preview production build
- `npm run lint` - Run ESLint

## 🎨 UI/UX Enhancements

- **Material-UI Integration**: Professional components alongside Tailwind CSS
- **FullCalendar**: Advanced calendar functionality with custom styling
- **Real-time Updates**: Live timers and dynamic content updates
- **Responsive Design**: Mobile-first approach with desktop optimization
- **Accessibility**: ARIA labels and keyboard navigation support

## 🚀 Performance Optimizations

- **Code Splitting**: Lazy loading of components
- **Optimized Builds**: Vite's fast build system
- **Efficient State Management**: Context API with minimal re-renders
- **CSS Optimization**: Tailwind CSS purging and Material-UI tree shaking

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📝 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🎨 AgnoConnect Color Scheme

The application uses a professional color palette that reflects AgnoConnect's brand identity:

- **Primary Dark Blue** (#073763): Deep blue background sections
- **Bright Orange** (#F47C20): Buttons, highlights, accents
- **White** (#FFFFFF): Text and backgrounds
- **Light Blue** (#0B2A5A): Header and footer backgrounds
- **Medium Gray** (#6E7C8E): Secondary text and icons
- **Dark Gray/Black** (#2E2E2E): Main text color

## 🙏 Acknowledgments

- Material-UI team for excellent React components
- FullCalendar for robust calendar functionality
- Tailwind CSS for utility-first styling approach
- AgnoConnect design team for the professional color palette
