from flask.views import MethodView
from core.models.approval_history import ApprovalHistoryModel
from core.models.pay_schedules import PaySchedulesModel
from flask_smorest import Blueprint, abort
from core.middleware import roles_required
from core.services.approval_history import ApprovalHistoryService
from schemas import UpdateApprovalSchema, ApprovalHistoryResponseSchema
from core.models.approvals import ApprovalModel
from core.models.user import UserModel
from core.utils.email import send_smtp_email
from core.models.approvals import ApprovalModel
from core.utils.email import send_smtp_email
from core.databases.database import db
from datetime import datetime
from core.utils.responseBuilder import ResponseBuilder


blueprint = Blueprint("Approval History", __name__, description="Operations for Approval history")

@blueprint.route("/approval-history/<int:approval_history_id>")
class ApprovalHistoryController(MethodView):

    @roles_required(['admin'])
    @blueprint.response(200, ApprovalHistoryResponseSchema)
    def get(self, approval_history_id):
        history = ApprovalHistoryService.get_approval_history_by_id(approval_history_id)
        if not history:
            abort(404, message="Approval history not found.")
        return history


@blueprint.route("/approval-history/employee/<int:employee_id>")
class ApprovalHistoryByEmployeeController(MethodView):

    # @roles_required(['admin'])
    @blueprint.response(200, ApprovalHistoryResponseSchema(many=True))
    def get(self, employee_id):
        approvals = ApprovalHistoryService.get_approval_history_by_employee(employee_id)
        return approvals
    
# @blueprint.route("/approval-history/employee/<int:approval_history_id>")
# class ApprovalHistoryByEmployeeController(MethodView):

#     # No @roles_required(['admin'])
#     @blueprint.arguments(UpdateApprovalSchema)
#     @blueprint.response(200, ApprovalHistoryResponseSchema)
#     def put(self, data, approval_history_id):
#         history = ApprovalHistoryService.get_approval_history_by_id(approval_history_id)
#         if not history:
#             abort(404, message="Approval history not found.")

#         if history.employee_id != data["employee_id"]:
#             abort(403, message="You are not authorized to update this approval.")

#         updated = ApprovalHistoryService.update_approval_history(approval_history_id, data)
#         return updated


# @blueprint.route("/approval-history/user/<int:user_id>")
# class ApprovalHistoryByUserController(MethodView):

#     @roles_required(['admin'])
#     @blueprint.response(200, ApprovalHistoryResponseSchema(many=True))
#     def get(self, user_id):
#         approvals = ApprovalHistoryService.get_approval_history_by_user(user_id)
#         return approvals

@blueprint.route("/approval-history/user/all")
class ApprovalHistoryAllController(MethodView):
    @roles_required(['admin'])
    @blueprint.response(200, ApprovalHistoryResponseSchema(many=True))
    def get(self):
        approvals = ApprovalHistoryService.get_all_for_authenticated_user()
        return approvals


@blueprint.route('/approval-summary')
class ApprovalList(MethodView):
    @roles_required(['admin'])
    @blueprint.response(200)
    def get(self):
        service = ApprovalHistoryService()
        summary = service.get_summary()
        return ResponseBuilder(summary, 200, None, None, "Approval Summary Retrieved Successfully").build()

@blueprint.route("/approval-history/user/approved")
class ApprovalHistoryApprovedController(MethodView):
    @roles_required(['admin'])
    @blueprint.response(200, ApprovalHistoryResponseSchema(many=True))
    def get(self):
        approvals = ApprovalHistoryService.get_by_status_for_authenticated_user("approved")
        return approvals


@blueprint.route("/approval-history/user/pending")
class ApprovalHistoryPendingController(MethodView):
    @roles_required(['admin'])
    @blueprint.response(200, ApprovalHistoryResponseSchema(many=True))
    def get(self):
        approvals = ApprovalHistoryService.get_by_status_for_authenticated_user("pending")
        return approvals


@blueprint.route("/approval-history/user/declined")
class ApprovalHistoryDeclinedController(MethodView):
    @roles_required(['admin'])
    @blueprint.response(200, ApprovalHistoryResponseSchema(many=True))
    def get(self):
        approvals = ApprovalHistoryService.get_by_status_for_authenticated_user("declined")
        return approvals


@blueprint.route("/approval-history/employee/<int:employee_id>/all")
class ApprovalHistoryByEmployeeAll(MethodView):
    @roles_required(['employee'])
    @blueprint.response(200, ApprovalHistoryResponseSchema(many=True))
    def get(self, employee_id):
        approvals = ApprovalHistoryService.get_all_by_employee_id(employee_id)
        return approvals


@blueprint.route("/approval-history/employee/<int:employee_id>/approved")
class ApprovalHistoryByEmployeeApproved(MethodView):
    @roles_required(['employee'])
    @blueprint.response(200, ApprovalHistoryResponseSchema(many=True))
    def get(self, employee_id):
        approvals = ApprovalHistoryService.get_by_status_for_employee(employee_id, "approved")
        return approvals


@blueprint.route("/approval-history/employee/<int:employee_id>/pending")
class ApprovalHistoryByEmployeePending(MethodView):
    @roles_required(['employee'])
    @blueprint.response(200, ApprovalHistoryResponseSchema(many=True))
    def get(self, employee_id):
        approvals = ApprovalHistoryService.get_by_status_for_employee(employee_id, "pending")
        return approvals


@blueprint.route("/approval-history/employee/<int:employee_id>/declined")
class ApprovalHistoryByEmployeeDeclined(MethodView):
    @roles_required(['employee'])
    @blueprint.response(200, ApprovalHistoryResponseSchema(many=True))
    def get(self, employee_id):
        approvals = ApprovalHistoryService.get_by_status_for_employee(employee_id, "declined")
        return approvals



@blueprint.route("/approval-history/employee/<int:approval_history_id>") #ensure they pass approval history id as the param for that particular employee
class ApprovalHistoryByEmployeeController(MethodView):
    @roles_required(['admin','employee'])
    @blueprint.arguments(UpdateApprovalSchema)
    @blueprint.response(200, ApprovalHistoryResponseSchema)
    def put(self, data, approval_history_id):
        history = db.session.query(ApprovalHistoryModel).filter_by(
            id=approval_history_id,
            pay_schedules_id=data["pay_schedules_id"]
        ).first()

        if history.employee_id != data["employee_id"]:
            abort(403, message="You are not authorized to update this approval.")

        updated = ApprovalHistoryService.update_approval_history(approval_history_id, data)

        # Now check if approved or declined
        if data["status"].lower() == "approved":
            # print("✅ Approved! Searching for next approver...")
            self.send_email_to_next_approver(history)

        elif data["status"].lower() == "declined":
            # print("❌ Declined! Notifying pay schedule owner...")
            self.notify_owner_of_decline(history)

        return updated


    def send_email_to_next_approver(self, history):

        current_approval = db.session.query(ApprovalModel).filter_by(id=history.approval_id).first()

        if not current_approval:
            # print("⚠️ Current approval record not found.")
            return

        next_approval = (
            db.session.query(ApprovalModel)
            .filter_by(user_id=current_approval.user_id, pay_schedules_id=current_approval.pay_schedules_id)
            .filter(ApprovalModel.level == str(int(current_approval.level) + 1))
            .first()
        )

        if next_approval and next_approval.email:
            send_smtp_email(
                to=next_approval.email,
                subject="Approval Needed",
                body=f"You are required to approve the pay schedule: {current_approval.name}, kindly login your payroll dashboard and submit your response."
            )
            # print(f"📧 Email sent to next approver: {next_approval.email}")

            # ✅ Create the next pending history
            new_history = ApprovalHistoryModel(
                reason="Waiting for approval",
                status="pending",
                approval_id=next_approval.id,
                pay_schedules_id=next_approval.pay_schedules_id,
                employee_id=next_approval.employee_id
            )
            db.session.add(new_history)
            db.session.commit()
            # print(f"📝 Created new approval history for employee {next_approval.employee_id}.")

        else:
            # ✅ No next approver: mark PaySchedule as fully approved
            # print("✅ No more approvers. Finalizing pay schedule approval...")

            pay_schedule = db.session.query(PaySchedulesModel).filter_by(id=current_approval.pay_schedules_id).first()
            if pay_schedule:
                pay_schedule.is_approved = True
                pay_schedule.approved_date = datetime.utcnow()
                db.session.commit()
                # print(f"🎉 PaySchedule {pay_schedule.id} fully approved at {pay_schedule.approved_date}.")

                user = db.session.query(UserModel).filter_by(id=current_approval.user_id).first()
                if user and user.email:
                    send_smtp_email(
                        to=user.email,
                        subject="Pay Schedule Fully Approved",
                        body=f"Your pay schedule '{pay_schedule.name}' has been fully approved successfully!. You can now go ahead and process the payroll"
                    )
                    print(f"📧 Final approval notification sent to user: {user.email}")
                else:
                    # print("⚠️ User not found or no email available.")
                    pass
            # else:
            #     # print("⚠️ PaySchedule not found.")
            #     pass



    def notify_owner_of_decline(self, history):

        current_approval = db.session.query(ApprovalModel).filter_by(id=history.approval_id).first()

        if not current_approval:
            # print("⚠️ Current approval record not found.")
            return

        user = db.session.query(UserModel).filter_by(id=current_approval.user_id).first()

        if user and user.email:
            send_smtp_email(
                to=user.email,
                subject="Pay Schedule Declined",
                body=f"Your pay schedule '{current_approval.name}' has been declined during approval."
            )
            # print(f"📧 Decline notification sent to schedule owner: {user.email}")
        else:
            # print("⚠️ Schedule owner email not found.")
            pass

