{"test_summary": {"total_tests": 4, "passed_tests": 3, "failed_tests": 1, "success_rate": 75.0}, "database_info": {"url": "postgresql://postgres:admin@localhost:5432/agnoconnect_hrms", "host": "localhost", "port": 5432, "database": "agnoconnect_hrms", "schema": "public"}, "features_tested": ["PostgreSQL connectivity", "Table creation and structure", "CRUD operations (Create, Read, Update, Delete)", "JSON/AI metadata storage", "Query performance", "Data integrity"], "timestamp": "2025-07-01T15:36:06.054899"}