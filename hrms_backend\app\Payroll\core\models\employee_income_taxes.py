from core.databases.database import db
from core.models.basemodel import ModelBase
from sqlalchemy.orm import relationship
class EmployeeIncomeTaxModel(ModelBase):
    __tablename__ = "employee_income_taxes"

    id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    employee_id = db.<PERSON>umn(db.In<PERSON>ger, db.<PERSON>ey('employees.id'), nullable=False)
    regulation_id = db.Column(db.Integer, db.ForeignKey('tax_regulations.id'), nullable=False)
    taxable_income = db.Column(db.Integer, nullable=False)
    tax_amount = db.Column(db.Double, nullable=False)
    organisation_id = db.Column(db.Integer, db.Foreign<PERSON>ey('employees.id'), nullable=False) 
    childrenEmp = relationship("EmployeeModel", backref="childrenEIT")     