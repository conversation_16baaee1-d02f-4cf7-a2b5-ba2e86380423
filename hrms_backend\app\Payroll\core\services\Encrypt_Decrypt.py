import os
from cryptography.fernet import Fernet

class EncryptionService:
    def __init__(self, secret_key: str):
        self.fernet = <PERSON>rne<PERSON>(secret_key)

    def encrypt(self, data: str) -> str:
        #Encrypts the given data.
        return self.fernet.encrypt(data.encode()).decode()

    def decrypt(self, encrypted_data: str) -> str:
        #Decrypts the given encrypted data.
        return self.fernet.decrypt(encrypted_data.encode()).decode()
    

