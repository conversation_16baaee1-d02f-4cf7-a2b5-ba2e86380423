"""Increase length of designations.name column

Revision ID: 66cbaafa3968
Revises: 975407f781aa
Create Date: 2025-03-25 16:15:16.473960

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '66cbaafa3968'
down_revision: Union[str, None] = '975407f781aa'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade():
    # Increase the length of the 'name' column
    op.alter_column('designations', 'name',
                    existing_type=sa.String(length=45),
                    type_=sa.String(length=255), 
                    existing_nullable=False)

def downgrade():
    # Revert the length of the 'name' column
    op.alter_column('designations', 'name',
                    existing_type=sa.String(length=255),
                    type_=sa.String(length=45),
                    existing_nullable=False)