#!/usr/bin/env python3
"""
Create test employees for testing the frontend integration
"""

import sys
import os
from uuid import uuid4
from datetime import datetime, date

# Add the app directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'app'))

from sqlalchemy.orm import Session
from sqlalchemy import text
from app.db.session import SessionLocal

def create_test_employees():
    """Create test employees for all the test users"""
    db = SessionLocal()
    
    try:
        print("Creating test employees...")
        
        # Get existing users
        result = db.execute(text("SELECT id, email, role FROM users"))
        users = result.fetchall()
        
        if not users:
            print("No users found. Please create test users first.")
            return
        
        # Check if employees already exist
        result = db.execute(text("SELECT COUNT(*) FROM employees"))
        employee_count = result.scalar()
        
        if employee_count > 0:
            print(f"✅ {employee_count} employees already exist")
            return
        
        # Create additional test employees
        test_employees = [
            {
                'employee_id': 'DEV001',
                'first_name': '<PERSON>',
                'last_name': 'Doe',
                'email': '<EMAIL>',
                'department': 'Engineering',
                'position': 'Senior Developer'
            },
            {
                'employee_id': 'DEV002',
                'first_name': 'Jane',
                'last_name': 'Smith',
                'email': '<EMAIL>',
                'department': 'Engineering',
                'position': 'Frontend Developer'
            },
            {
                'employee_id': 'MKT001',
                'first_name': 'Mike',
                'last_name': 'Johnson',
                'email': '<EMAIL>',
                'department': 'Marketing',
                'position': 'Marketing Specialist'
            },
            {
                'employee_id': 'SAL001',
                'first_name': 'Sarah',
                'last_name': 'Wilson',
                'email': '<EMAIL>',
                'department': 'Sales',
                'position': 'Sales Representative'
            },
            {
                'employee_id': 'FIN001',
                'first_name': 'David',
                'last_name': 'Brown',
                'email': '<EMAIL>',
                'department': 'Finance',
                'position': 'Financial Analyst'
            }
        ]
        
        # Create employees for existing users first
        for user in users:
            user_id, email, role = user
            
            # Check if employee already exists for this user
            result = db.execute(text("SELECT id FROM employees WHERE user_id = :user_id"), {'user_id': user_id})
            existing = result.fetchone()
            
            if existing:
                continue
                
            # Get user details for employee creation
            if email == '<EMAIL>':
                first_name, last_name = 'Admin', 'User'
                employee_id = 'ADMIN001'
                department = 'Administration'
                position = 'System Administrator'
            elif email == '<EMAIL>':
                first_name, last_name = 'HR', 'Manager'
                employee_id = 'HR001'
                department = 'Human Resources'
                position = 'HR Manager'
            elif email == '<EMAIL>':
                first_name, last_name = 'Manager', 'User'
                employee_id = 'MGR001'
                department = 'Management'
                position = 'Team Manager'
            elif email == '<EMAIL>':
                first_name, last_name = 'Employee', 'User'
                employee_id = 'EMP001'
                department = 'General'
                position = 'Employee'
            else:
                continue
            
            # Create employee record
            employee_insert_sql = text("""
                INSERT INTO employees (
                    id, user_id, employee_id, first_name, last_name, email,
                    hire_date, is_active, created_at, updated_at
                ) VALUES (
                    :id, :user_id, :employee_id, :first_name, :last_name, :email,
                    :hire_date, :is_active, :created_at, :updated_at
                )
            """)
            
            db.execute(employee_insert_sql, {
                'id': uuid4(),
                'user_id': user_id,
                'employee_id': employee_id,
                'first_name': first_name,
                'last_name': last_name,
                'email': email,
                'hire_date': datetime.utcnow(),
                'is_active': True,
                'created_at': datetime.utcnow(),
                'updated_at': datetime.utcnow()
            })
            
            print(f"✅ Created employee: {first_name} {last_name} ({email})")
        
        # Create additional test employees without users
        for emp_data in test_employees:
            employee_insert_sql = text("""
                INSERT INTO employees (
                    id, employee_id, first_name, last_name, email,
                    hire_date, is_active, created_at, updated_at
                ) VALUES (
                    :id, :employee_id, :first_name, :last_name, :email,
                    :hire_date, :is_active, :created_at, :updated_at
                )
            """)
            
            db.execute(employee_insert_sql, {
                'id': uuid4(),
                'employee_id': emp_data['employee_id'],
                'first_name': emp_data['first_name'],
                'last_name': emp_data['last_name'],
                'email': emp_data['email'],
                'hire_date': datetime.utcnow(),
                'is_active': True,
                'created_at': datetime.utcnow(),
                'updated_at': datetime.utcnow()
            })
            
            print(f"✅ Created employee: {emp_data['first_name']} {emp_data['last_name']} ({emp_data['email']})")
        
        db.commit()
        print("\n✅ All test employees created successfully!")
        
        # Show final count
        result = db.execute(text("SELECT COUNT(*) FROM employees"))
        final_count = result.scalar()
        print(f"Total employees in database: {final_count}")
        
    except Exception as e:
        print(f"❌ Error creating test employees: {e}")
        db.rollback()
        raise
    finally:
        db.close()

if __name__ == "__main__":
    create_test_employees()
