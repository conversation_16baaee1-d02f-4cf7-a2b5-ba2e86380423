from core.databases.database import db
from core.models.basemodel import ModelBase
from sqlalchemy.orm import relationship
from datetime import datetime
from sqlalchemy.dialects.postgresql import NUMERIC
from core.utils.helper import get_nigeria_time
class PayrollHistoryModel(ModelBase):
    __tablename__ = "payroll_history"

    id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    employee_id = db.Column(db.Integer, db.ForeignKey((ModelBase.dbSchema() + '.employees.id'),ondelete='CASCADE'), nullable=True)
    # employee = db.relationship('EmployeeModel', backref='payroll_history',single_parent=True)
    employee = db.relationship("EmployeeModel", back_populates="payroll_histories")
    pay_schedle_id = db.Column(db.Integer, db.ForeignKey((ModelBase.dbSchema() + '.pay_schedules.id'), ondelete='SET NULL'), nullable=True)
    pay_schedle = db.relationship("PaySchedulesModel", back_populates="pay_schedles")
    payschedle_name = db.Column(db.String(250), nullable=True)

    prorated_monthly_tax = db.Column(db.Float, nullable=True)
    prorated_annual_tax = db.Column(db.Float, nullable=True)
    total_taxable_monthly_sum = db.Column(db.Float, nullable=True)
    total_taxable_annual_sum = db.Column(db.Float, nullable=True)
    total_non_taxable_monthly_sum = db.Column(db.Float, nullable=True)
    total_non_taxable_annual_sum = db.Column(db.Float, nullable=True)
    total_statutory_monthly_sum = db.Column(db.Float, nullable=True)
    total_statutory_annual_sum = db.Column(db.Float, nullable=True)
    total_other_deductions_monthly_sum = db.Column(db.Float, nullable=True)
    total_other_deductions_annual_sum = db.Column(db.Float, nullable=True)
    netpay = db.Column(db.Float, nullable=True)
    pension = db.Column(db.Float, nullable=True)
    gross_pay = db.Column(db.Float, nullable=True)
    cost_to_company = db.Column(db.Float, nullable=True)
    nhf = db.Column(db.Float, nullable=True)
    monthly_tax = db.Column(db.Float, nullable=True)
    annual_tax = db.Column(db.Float, nullable=True)
    is_prorated = db.Column(db.Boolean, default=False, nullable=True)
    prorated_gross = db.Column(db.Float, nullable=True)
    prorated_net = db.Column(db.Float, nullable=True)
    is_processed = db.Column(db.Boolean, default=False, nullable=True)
    is_processed_created = db.Column(db.String(250), nullable=True)
    message = db.Column(db.String(250), nullable=True)
    paymentID = db.Column(db.String(45), nullable=True, name="paymentId")
    payment_status = db.Column(db.String(45), nullable=True)      
    transaction_id = db.Column(db.String, nullable=True)
    transaction_date = db.Column(db.String(250), nullable=True)
    reference_code = db.Column(db.String, nullable=True, unique=True)
    user_id = db.Column(db.Integer, db.ForeignKey((ModelBase.dbSchema() + '.users.id')), nullable=True) 
    created_at = db.Column(db.DateTime, nullable=False, default=get_nigeria_time)
    updated_at = db.Column(db.DateTime, nullable=False, default=get_nigeria_time)
    
    overtime_amount = db.Column(db.Float, default=0.0, nullable=True)
    salary_arrears = db.Column(db.Float, default=0.0, nullable=True)
    surcharge = db.Column(db.Float, default=0.0, nullable=True)
    monthly_loan_repayment = db.Column(db.Float, nullable=False, default=0.0)


    # first_name = db.Column(db.String(45), nullable=True)
    # last_name = db.Column(db.String(45), nullable=True)
    # email = db.Column(db.String(100), nullable=True)
    # hire_date = db.Column(db.String(50), nullable=True)
    # employee_id = db.Column(db.Integer, db.ForeignKey(ModelBase.dbSchema() + '.employees.id', ondelete='CASCADE'), nullable=False)
    # dob = db.Column(db.String(50), nullable=True)
    # department_id = db.Column(db.Integer, db.ForeignKey((ModelBase.dbSchema() + '.departments.id')), nullable=True)
    # designation_id = db.Column(db.Integer, db.ForeignKey((ModelBase.dbSchema() + '.designations.id')), nullable=True)
    # organisation_id = db.Column(db.Integer, db.ForeignKey((ModelBase.dbSchema() + '.organisations.id')), nullable=True) 
    # template_id = db.Column(db.Integer, db.ForeignKey((ModelBase.dbSchema() + '.salary_templates.id')), nullable=True) 
    # employee_id = db.Column(db.Integer, db.ForeignKey((ModelBase.dbSchema() + '.employees.id')), nullable=False)
    # employeeID = db.Column(db.String, nullable=True)
    # Newly added columns from EmployeeModel
    # gender = db.Column(db.String(100), nullable=True)
    # source_tag = db.Column(db.String(45), nullable=True)
    # # pension_no = db.Column(db.String, nullable=True)
    # # pension_pfa = db.Column(db.String, nullable=True)
    # # pfa_name = db.Column(db.String, nullable=True)
    # # pfa_number = db.Column(db.String, nullable=True)
    # # nhf_no = db.Column(db.String, nullable=True)
    # # nhf_mortgage_bank = db.Column(db.String(45), nullable=True)
    # # tax_state = db.Column(db.String, nullable=True)
    # # pfc_name = db.Column(db.String, nullable=True)
    # pfc_account_number = db.Column(db.String, nullable=True)
    # annual_leave_days = db.Column(db.String(45), nullable=True)
    # unpaid_leave_days = db.Column(db.String(45), nullable=True)
    # sick_leave_days = db.Column(db.String(45), nullable=True)
    # maternity_paternity_leave_days = db.Column(db.String(45), nullable=True)
    # casual_leave_days = db.Column(db.String(45), nullable=True)
    # compassionate_leave_days = db.Column(db.String(45), nullable=True)
    # total_working_days = db.Column(db.String(45), nullable=True)
    # total_present_days = db.Column(db.String(45), nullable=True)
    # total_absent_days = db.Column(db.String(45), nullable=True)
    # business_unit = db.Column(db.String(50), nullable=True)
    # division = db.Column(db.String(50), nullable=True)
    # taxID = db.Column(db.String(45), nullable=True)
    # employee_type = db.Column(db.String(45), nullable=True)
    # status = db.Column(db.String(45), nullable=True)
    # role = db.Column(db.String(45), nullable=True)
    # employment_type = db.Column(db.String(45), nullable=True)
    # bank_name = db.Column(db.String(45), nullable=True)
    # bank_account = db.Column(db.String(45), nullable=True)
    # salary_type = db.Column(db.String(45), nullable=True)
    # rate = db.Column(db.String(45), nullable=True)
    # hours_worked = db.Column(db.String(45), nullable=True)
    # number_of_days_worked = db.Column(db.Integer, nullable=True)
    # sort_code = db.Column(db.String(45), nullable=True)
    # address = db.Column(db.String(45), nullable=True)
    # country = db.Column(db.String(45), nullable=True)
    # state = db.Column(db.String(45), nullable=True)
    # city = db.Column(db.String(45), nullable=True)
    # zip_code = db.Column(db.String(45), nullable=True)
    # phone = db.Column(db.String(45), nullable=True)
    # level = db.Column(db.String(45), nullable=True)
    # tax_type = db.Column(db.String(45), nullable=True)
    # currency = db.Column(db.String(45), nullable=True)
    # work_schedule = db.Column(db.String(45), nullable=True)

    # department = db.relationship("DepartmentModel", backref="payroll_histories")
    # designation = db.relationship("DesignationModel", backref="payroll_histories")
    # organisation = db.relationship("OrganisationModel", backref="payroll_histories")
