from pydantic import BaseModel, <PERSON>, validator
from typing import Optional, List, Dict, Any
from uuid import UUID
from datetime import datetime, date
from decimal import Decimal
from enum import Enum


class ReviewStatus(str, Enum):
    DRAFT = "draft"
    IN_PROGRESS = "in_progress"
    PENDING_APPROVAL = "pending_approval"
    COMPLETED = "completed"
    CANCELLED = "cancelled"


class ReviewType(str, Enum):
    ANNUAL = "annual"
    QUARTERLY = "quarterly"
    MONTHLY = "monthly"
    PROBATION = "probation"
    PROJECT_END = "project_end"
    PROMOTION = "promotion"
    CUSTOM = "custom"


class GoalStatus(str, Enum):
    DRAFT = "draft"
    ACTIVE = "active"
    COMPLETED = "completed"
    CANCELLED = "cancelled"
    OVERDUE = "overdue"


class GoalType(str, Enum):
    INDIVIDUAL = "individual"
    TEAM = "team"
    DEPARTMENT = "department"
    COMPANY = "company"


# Performance Review Schemas
class PerformanceReviewBase(BaseModel):
    title: str = Field(..., min_length=1, max_length=200)
    review_type: ReviewType
    review_period_start: date
    review_period_end: date
    due_date: date
    description: Optional[str] = None
    self_review_enabled: bool = True
    peer_review_enabled: bool = False
    manager_review_enabled: bool = True

    @validator('review_period_end')
    def end_date_must_be_after_start_date(cls, v, values):
        if 'review_period_start' in values and v <= values['review_period_start']:
            raise ValueError('Review period end must be after start date')
        return v

    @validator('due_date')
    def due_date_must_be_after_period_end(cls, v, values):
        if 'review_period_end' in values and v < values['review_period_end']:
            raise ValueError('Due date must be after review period end')
        return v


class PerformanceReviewCreate(PerformanceReviewBase):
    employee_id: UUID
    reviewer_id: UUID
    template_id: Optional[UUID] = None


class PerformanceReviewUpdate(BaseModel):
    title: Optional[str] = Field(None, min_length=1, max_length=200)
    due_date: Optional[date] = None
    description: Optional[str] = None
    status: Optional[ReviewStatus] = None
    overall_rating: Optional[Decimal] = Field(None, ge=1, le=5)
    overall_comments: Optional[str] = None
    manager_comments: Optional[str] = None
    employee_comments: Optional[str] = None
    development_areas: Optional[List[str]] = None
    strengths: Optional[List[str]] = None
    goals_for_next_period: Optional[List[str]] = None


class PerformanceReviewResponse(PerformanceReviewBase):
    id: UUID
    employee_id: UUID
    reviewer_id: UUID
    organization_id: UUID
    template_id: Optional[UUID] = None
    status: ReviewStatus
    overall_rating: Optional[Decimal] = None
    overall_comments: Optional[str] = None
    manager_comments: Optional[str] = None
    employee_comments: Optional[str] = None
    development_areas: Optional[List[str]] = None
    strengths: Optional[List[str]] = None
    goals_for_next_period: Optional[List[str]] = None
    submitted_at: Optional[datetime] = None
    approved_at: Optional[datetime] = None
    approved_by: Optional[UUID] = None
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class PerformanceReviewListResponse(BaseModel):
    reviews: List[PerformanceReviewResponse]
    total: int
    skip: int
    limit: int


# Goal Schemas
class GoalBase(BaseModel):
    title: str = Field(..., min_length=1, max_length=200)
    description: str = Field(..., min_length=1)
    goal_type: GoalType = GoalType.INDIVIDUAL
    target_date: date
    weight_percentage: Decimal = Field(100, ge=0, le=100)
    success_criteria: Optional[str] = None
    measurement_method: Optional[str] = None

    @validator('target_date')
    def target_date_must_be_future(cls, v):
        if v <= date.today():
            raise ValueError('Target date must be in the future')
        return v


class GoalCreate(GoalBase):
    employee_id: UUID
    parent_goal_id: Optional[UUID] = None
    linked_review_id: Optional[UUID] = None


class GoalUpdate(BaseModel):
    title: Optional[str] = Field(None, min_length=1, max_length=200)
    description: Optional[str] = Field(None, min_length=1)
    target_date: Optional[date] = None
    weight_percentage: Optional[Decimal] = Field(None, ge=0, le=100)
    success_criteria: Optional[str] = None
    measurement_method: Optional[str] = None
    status: Optional[GoalStatus] = None
    progress_percentage: Optional[Decimal] = Field(None, ge=0, le=100)
    actual_completion_date: Optional[date] = None
    notes: Optional[str] = None


class GoalResponse(GoalBase):
    id: UUID
    employee_id: UUID
    organization_id: UUID
    parent_goal_id: Optional[UUID] = None
    linked_review_id: Optional[UUID] = None
    status: GoalStatus
    progress_percentage: Decimal
    actual_completion_date: Optional[date] = None
    notes: Optional[str] = None
    created_by: UUID
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class GoalListResponse(BaseModel):
    goals: List[GoalResponse]
    total: int
    skip: int
    limit: int


# Review Template Schemas
class ReviewTemplateBase(BaseModel):
    name: str = Field(..., min_length=1, max_length=200)
    description: Optional[str] = None
    review_type: ReviewType
    is_default: bool = False


class ReviewTemplateCreate(ReviewTemplateBase):
    sections: List[Dict[str, Any]]


class ReviewTemplateUpdate(BaseModel):
    name: Optional[str] = Field(None, min_length=1, max_length=200)
    description: Optional[str] = None
    is_default: Optional[bool] = None
    sections: Optional[List[Dict[str, Any]]] = None
    is_active: Optional[bool] = None


class ReviewTemplateResponse(ReviewTemplateBase):
    id: UUID
    organization_id: UUID
    sections: List[Dict[str, Any]]
    created_at: datetime
    updated_at: datetime
    is_active: bool

    class Config:
        from_attributes = True


# Performance Metrics Schemas
class PerformanceMetricBase(BaseModel):
    name: str = Field(..., min_length=1, max_length=100)
    description: Optional[str] = None
    metric_type: str = Field(..., max_length=50)  # "rating", "percentage", "number", "boolean"
    target_value: Optional[Decimal] = None
    weight_percentage: Decimal = Field(100, ge=0, le=100)
    calculation_method: Optional[str] = None


class PerformanceMetricCreate(PerformanceMetricBase):
    pass


class PerformanceMetricUpdate(BaseModel):
    name: Optional[str] = Field(None, min_length=1, max_length=100)
    description: Optional[str] = None
    target_value: Optional[Decimal] = None
    weight_percentage: Optional[Decimal] = Field(None, ge=0, le=100)
    calculation_method: Optional[str] = None
    is_active: Optional[bool] = None


class PerformanceMetricResponse(PerformanceMetricBase):
    id: UUID
    organization_id: UUID
    created_at: datetime
    updated_at: datetime
    is_active: bool

    class Config:
        from_attributes = True


# Review Section Schemas
class ReviewSectionBase(BaseModel):
    section_name: str = Field(..., min_length=1, max_length=100)
    section_description: Optional[str] = None
    order_index: int = Field(..., ge=0)
    weight_percentage: Decimal = Field(100, ge=0, le=100)


class ReviewSectionCreate(ReviewSectionBase):
    review_id: UUID
    questions: List[Dict[str, Any]]


class ReviewSectionUpdate(BaseModel):
    section_name: Optional[str] = Field(None, min_length=1, max_length=100)
    section_description: Optional[str] = None
    order_index: Optional[int] = Field(None, ge=0)
    weight_percentage: Optional[Decimal] = Field(None, ge=0, le=100)
    questions: Optional[List[Dict[str, Any]]] = None


class ReviewSectionResponse(ReviewSectionBase):
    id: UUID
    review_id: UUID
    questions: List[Dict[str, Any]]
    responses: Optional[Dict[str, Any]] = None
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


# Performance Analytics
class PerformanceAnalytics(BaseModel):
    employee_id: UUID
    review_period: str
    overall_rating: Optional[Decimal] = None
    goal_completion_rate: Decimal
    total_goals: int
    completed_goals: int
    overdue_goals: int
    performance_trend: str  # "improving", "declining", "stable"
    strengths: List[str]
    development_areas: List[str]
    peer_feedback_score: Optional[Decimal] = None
    manager_rating: Optional[Decimal] = None
    self_rating: Optional[Decimal] = None


class TeamPerformanceAnalytics(BaseModel):
    department_id: UUID
    department_name: str
    average_rating: Decimal
    goal_completion_rate: Decimal
    total_reviews_completed: int
    total_reviews_pending: int
    top_performers: List[Dict[str, Any]]
    improvement_areas: List[str]
    performance_distribution: Dict[str, int]


# Development Plan Schemas
class DevelopmentPlanBase(BaseModel):
    title: str = Field(..., min_length=1, max_length=200)
    description: str = Field(..., min_length=1)
    start_date: date
    target_completion_date: date
    skills_to_develop: List[str]
    learning_resources: Optional[List[Dict[str, Any]]] = None

    @validator('target_completion_date')
    def target_date_must_be_after_start_date(cls, v, values):
        if 'start_date' in values and v <= values['start_date']:
            raise ValueError('Target completion date must be after start date')
        return v


class DevelopmentPlanCreate(DevelopmentPlanBase):
    employee_id: UUID
    linked_review_id: Optional[UUID] = None


class DevelopmentPlanUpdate(BaseModel):
    title: Optional[str] = Field(None, min_length=1, max_length=200)
    description: Optional[str] = Field(None, min_length=1)
    target_completion_date: Optional[date] = None
    skills_to_develop: Optional[List[str]] = None
    learning_resources: Optional[List[Dict[str, Any]]] = None
    progress_percentage: Optional[Decimal] = Field(None, ge=0, le=100)
    status: Optional[str] = None
    completion_notes: Optional[str] = None


class DevelopmentPlanResponse(DevelopmentPlanBase):
    id: UUID
    employee_id: UUID
    organization_id: UUID
    linked_review_id: Optional[UUID] = None
    progress_percentage: Decimal
    status: str
    completion_notes: Optional[str] = None
    created_by: UUID
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


# Bulk Operations
class BulkReviewCreate(BaseModel):
    employee_ids: List[UUID]
    review_template_id: UUID
    review_period_start: date
    review_period_end: date
    due_date: date
    reviewer_id: Optional[UUID] = None  # If None, use direct manager


class BulkGoalCreate(BaseModel):
    employee_ids: List[UUID]
    goal_template: GoalCreate
    cascade_from_parent: bool = False
    parent_goal_id: Optional[UUID] = None


# Performance Dashboard
class PerformanceDashboard(BaseModel):
    organization_metrics: Dict[str, Any]
    department_metrics: List[TeamPerformanceAnalytics]
    pending_reviews: List[PerformanceReviewResponse]
    overdue_goals: List[GoalResponse]
    top_performers: List[Dict[str, Any]]
    performance_trends: Dict[str, List[float]]
    upcoming_review_cycles: List[Dict[str, Any]]
