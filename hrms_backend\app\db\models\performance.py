from sqlalchemy import Column, String, DateTime, Date, Foreign<PERSON>ey, Boolean, Text, Numeric, Integer, Enum
from sqlalchemy.dialects.postgresql import UUID, JSONB
from sqlalchemy.orm import relationship
from enum import Enum as PyEnum
from datetime import datetime, date
from typing import Optional

from ..base import BaseModel, AuditMixin


class GoalStatus(PyEnum):
    DRAFT = "draft"
    ACTIVE = "active"
    COMPLETED = "completed"
    CANCELLED = "cancelled"
    OVERDUE = "overdue"


class GoalPriority(PyEnum):
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class ReviewStatus(PyEnum):
    NOT_STARTED = "not_started"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    APPROVED = "approved"
    PUBLISHED = "published"


class ReviewType(PyEnum):
    ANNUAL = "annual"
    QUARTERLY = "quarterly"
    MONTHLY = "monthly"
    PROBATION = "probation"
    PROJECT_END = "project_end"
    EXIT = "exit"


class Goal(BaseModel, AuditMixin):
    """Employee goal model"""
    __tablename__ = "goals"

    # Basic information
    title = Column(String(200), nullable=False)
    description = Column(Text, nullable=True)
    organization_id = Column(UUID(as_uuid=True), nullable=False, index=True)

    # Goal ownership
    employee_id = Column(UUID(as_uuid=True), ForeignKey("employees.id"), nullable=False, index=True)
    manager_id = Column(UUID(as_uuid=True), ForeignKey("employees.id"), nullable=True)

    # Goal details
    status = Column(Enum(GoalStatus), nullable=False, default=GoalStatus.DRAFT)
    priority = Column(Enum(GoalPriority), nullable=False, default=GoalPriority.MEDIUM)

    # Timeline
    start_date = Column(Date, nullable=False)
    target_date = Column(Date, nullable=False)
    completed_date = Column(Date, nullable=True)

    # Progress tracking
    progress_percentage = Column(Numeric(5, 2), nullable=False, default=0)

    # Measurement
    success_criteria = Column(Text, nullable=False)
    measurement_method = Column(String(100), nullable=True)
    target_value = Column(Numeric(12, 2), nullable=True)
    current_value = Column(Numeric(12, 2), nullable=True, default=0)
    unit_of_measure = Column(String(50), nullable=True)

    # Categorization
    category = Column(String(100), nullable=True)
    tags = Column(JSONB, nullable=True)  # Array of tags

    # Alignment
    department_goal_id = Column(UUID(as_uuid=True), ForeignKey("goals.id"), nullable=True)
    company_goal_id = Column(UUID(as_uuid=True), ForeignKey("goals.id"), nullable=True)

    # Weightage (for performance reviews)
    weightage = Column(Numeric(5, 2), nullable=True)  # Percentage weightage

    # Relationships
    employee = relationship("Employee", back_populates="goals", foreign_keys=[employee_id])
    manager = relationship("Employee", foreign_keys=[manager_id])
    department_goal = relationship("Goal", remote_side="Goal.id", foreign_keys=[department_goal_id])
    company_goal = relationship("Goal", remote_side="Goal.id", foreign_keys=[company_goal_id])
    updates = relationship("GoalUpdate", back_populates="goal")


class GoalUpdate(BaseModel, AuditMixin):
    """Goal progress update"""
    __tablename__ = "goal_updates"

    goal_id = Column(UUID(as_uuid=True), ForeignKey("goals.id"), nullable=False, index=True)
    updated_by = Column(UUID(as_uuid=True), ForeignKey("employees.id"), nullable=False)

    # Update details
    progress_percentage = Column(Numeric(5, 2), nullable=False)
    current_value = Column(Numeric(12, 2), nullable=True)
    update_notes = Column(Text, nullable=False)

    # Challenges and support
    challenges_faced = Column(Text, nullable=True)
    support_needed = Column(Text, nullable=True)

    # Attachments
    attachment_urls = Column(JSONB, nullable=True)  # Array of file URLs

    # Relationships
    goal = relationship("Goal", back_populates="updates")
    updater = relationship("Employee", foreign_keys=[updated_by])


class PerformanceReview(BaseModel, AuditMixin):
    """Performance review model"""
    __tablename__ = "performance_reviews"

    # Basic information
    title = Column(String(200), nullable=False)
    organization_id = Column(UUID(as_uuid=True), nullable=False, index=True)

    # Review participants
    employee_id = Column(UUID(as_uuid=True), ForeignKey("employees.id"), nullable=False, index=True)
    reviewer_id = Column(UUID(as_uuid=True), ForeignKey("employees.id"), nullable=False)

    # Review details
    review_type = Column(Enum(ReviewType), nullable=False)
    review_period_start = Column(Date, nullable=False)
    review_period_end = Column(Date, nullable=False)

    # Status and timeline
    status = Column(Enum(ReviewStatus), nullable=False, default=ReviewStatus.NOT_STARTED)
    due_date = Column(Date, nullable=False)
    completed_date = Column(Date, nullable=True)

    # Overall ratings
    overall_rating = Column(Numeric(3, 2), nullable=True)  # e.g., 4.5 out of 5
    overall_comments = Column(Text, nullable=True)

    # Self assessment
    self_assessment_completed = Column(Boolean, default=False)
    self_assessment_date = Column(Date, nullable=True)
    self_assessment_comments = Column(Text, nullable=True)

    # Manager assessment
    manager_assessment_completed = Column(Boolean, default=False)
    manager_assessment_date = Column(Date, nullable=True)
    manager_assessment_comments = Column(Text, nullable=True)

    # Development planning
    development_areas = Column(Text, nullable=True)
    development_plan = Column(Text, nullable=True)
    training_recommendations = Column(JSONB, nullable=True)  # Array of training suggestions

    # Career discussion
    career_aspirations = Column(Text, nullable=True)
    career_development_plan = Column(Text, nullable=True)

    # Approval
    approved_by = Column(UUID(as_uuid=True), ForeignKey("employees.id"), nullable=True)
    approved_at = Column(DateTime(timezone=True), nullable=True)

    # Relationships
    employee = relationship("Employee", back_populates="performance_reviews", foreign_keys=[employee_id])
    reviewer = relationship("Employee", foreign_keys=[reviewer_id])
    approver = relationship("Employee", foreign_keys=[approved_by])
    ratings = relationship("PerformanceRating", back_populates="review")
    goals_reviewed = relationship("GoalReview", back_populates="performance_review")


class PerformanceRating(BaseModel, AuditMixin):
    """Performance rating for specific competencies"""
    __tablename__ = "performance_ratings"

    review_id = Column(UUID(as_uuid=True), ForeignKey("performance_reviews.id"), nullable=False, index=True)
    competency_id = Column(UUID(as_uuid=True), ForeignKey("competencies.id"), nullable=False)

    # Ratings
    self_rating = Column(Numeric(3, 2), nullable=True)
    manager_rating = Column(Numeric(3, 2), nullable=True)
    final_rating = Column(Numeric(3, 2), nullable=True)

    # Comments
    self_comments = Column(Text, nullable=True)
    manager_comments = Column(Text, nullable=True)

    # Evidence
    evidence_provided = Column(Text, nullable=True)

    # Relationships
    review = relationship("PerformanceReview", back_populates="ratings")
    competency = relationship("Competency", back_populates="ratings")


class Competency(BaseModel):
    """Competency framework"""
    __tablename__ = "competencies"

    name = Column(String(200), nullable=False)
    description = Column(Text, nullable=True)
    organization_id = Column(UUID(as_uuid=True), nullable=False, index=True)

    # Competency details
    category = Column(String(100), nullable=True)  # technical, behavioral, leadership
    level = Column(String(50), nullable=True)  # beginner, intermediate, advanced

    # Rating scale
    rating_scale_min = Column(Integer, nullable=False, default=1)
    rating_scale_max = Column(Integer, nullable=False, default=5)

    # Behavioral indicators
    behavioral_indicators = Column(JSONB, nullable=True)  # Array of indicator descriptions

    # Applicability
    applicable_designations = Column(JSONB, nullable=True)  # Array of designation IDs
    applicable_departments = Column(JSONB, nullable=True)  # Array of department IDs

    # Status
    is_active = Column(Boolean, default=True)

    # Relationships
    ratings = relationship("PerformanceRating", back_populates="competency")


class GoalReview(BaseModel, AuditMixin):
    """Goal review within performance review"""
    __tablename__ = "goal_reviews"

    performance_review_id = Column(UUID(as_uuid=True), ForeignKey("performance_reviews.id"), nullable=False)
    goal_id = Column(UUID(as_uuid=True), ForeignKey("goals.id"), nullable=False)

    # Achievement assessment
    achievement_percentage = Column(Numeric(5, 2), nullable=False)
    achievement_rating = Column(Numeric(3, 2), nullable=True)

    # Comments
    employee_comments = Column(Text, nullable=True)
    manager_comments = Column(Text, nullable=True)

    # Challenges and learnings
    challenges_faced = Column(Text, nullable=True)
    key_learnings = Column(Text, nullable=True)

    # Relationships
    performance_review = relationship("PerformanceReview", back_populates="goals_reviewed")
    goal = relationship("Goal")


class PerformanceTemplate(BaseModel):
    """Performance review template"""
    __tablename__ = "performance_templates"

    name = Column(String(200), nullable=False)
    description = Column(Text, nullable=True)
    organization_id = Column(UUID(as_uuid=True), nullable=False, index=True)

    # Template configuration
    review_type = Column(Enum(ReviewType), nullable=False)
    competencies = Column(JSONB, nullable=False)  # Array of competency IDs

    # Settings
    include_goals = Column(Boolean, default=True)
    include_self_assessment = Column(Boolean, default=True)
    include_development_planning = Column(Boolean, default=True)

    # Custom sections
    custom_sections = Column(JSONB, nullable=True)  # Array of custom section configs

    # Applicability
    applicable_designations = Column(JSONB, nullable=True)
    applicable_departments = Column(JSONB, nullable=True)

    # Status
    is_active = Column(Boolean, default=True)
    is_default = Column(Boolean, default=False)


class PerformanceCycle(BaseModel):
    """Performance review cycle configuration"""
    __tablename__ = "performance_cycles"

    name = Column(String(200), nullable=False)
    organization_id = Column(UUID(as_uuid=True), nullable=False, index=True)

    # Cycle details
    cycle_year = Column(Integer, nullable=False)
    start_date = Column(Date, nullable=False)
    end_date = Column(Date, nullable=False)

    # Review configuration
    review_type = Column(Enum(ReviewType), nullable=False)
    template_id = Column(UUID(as_uuid=True), ForeignKey("performance_templates.id"), nullable=True)

    # Timeline
    goal_setting_deadline = Column(Date, nullable=True)
    mid_year_review_date = Column(Date, nullable=True)
    final_review_deadline = Column(Date, nullable=False)

    # Participants
    included_employees = Column(JSONB, nullable=True)  # Array of employee IDs or criteria
    excluded_employees = Column(JSONB, nullable=True)  # Array of employee IDs

    # Status
    is_active = Column(Boolean, default=True)
    is_published = Column(Boolean, default=False)

    # Relationships
    template = relationship("PerformanceTemplate")
