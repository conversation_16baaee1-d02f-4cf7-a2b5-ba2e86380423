#!/usr/bin/env python3
"""
Database Setup Script
Sets up the PostgreSQL database for HRMS backend testing
"""

import os
import sys
import logging
from datetime import datetime

# Add the app directory to Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'app'))

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def setup_database():
    """Set up the database for testing"""
    try:
        logger.info("🚀 Setting up HRMS database...")
        
        # Import database components
        from app.db.session import engine, create_tables, test_connection
        from app.core.config import settings
        
        logger.info(f"📊 Database URL: {settings.database_url}")
        
        # Test connection
        logger.info("🔍 Testing database connection...")
        if not test_connection():
            logger.error("❌ Database connection failed!")
            logger.info("Please ensure PostgreSQL is running and credentials are correct.")
            logger.info("Check your .env file for database configuration.")
            return False
        
        logger.info("✅ Database connection successful!")
        
        # Create tables
        logger.info("🏗️ Creating database tables...")
        create_tables()
        logger.info("✅ Database tables created successfully!")
        
        # Verify tables
        from sqlalchemy import inspect
        inspector = inspect(engine)
        tables = inspector.get_table_names()
        
        logger.info(f"📋 Created {len(tables)} tables:")
        for table in sorted(tables):
            logger.info(f"   - {table}")
        
        logger.info("🎉 Database setup completed successfully!")
        return True
        
    except Exception as e:
        logger.error(f"❌ Database setup failed: {e}")
        return False


def main():
    """Main function"""
    print("🚀 HRMS Database Setup")
    print("=" * 50)
    
    success = setup_database()
    
    if success:
        print("\n✅ Database setup completed successfully!")
        print("You can now run the tests with: python run_tests.py")
    else:
        print("\n❌ Database setup failed!")
        print("Please check the error messages above and fix any issues.")
    
    return 0 if success else 1


if __name__ == "__main__":
    sys.exit(main())
