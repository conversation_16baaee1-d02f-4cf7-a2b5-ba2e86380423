from datetime import datetime
from core.databases.database import db
from core.models import remark
from core.models.employees import EmployeeModel
from core.models.payroll_history import PayrollHistoryModel
from core.models.remark import RemarkModel
from core.repositories.user import UserRepository


class RemarksRepository:
    def __init__(self) -> None:
        pass

    @classmethod
    def createRemark(cls, **kwargs):
        user_id = UserRepository.authUserId()
        kwargs['user_id'] = user_id  # Associate the remark with the authenticated user

        # Ensure required fields are present
        if 'employee_id' not in kwargs or 'payroll_history_id' not in kwargs:
            raise ValueError("Both employee_id and payroll_history_id are required.")

        remark = RemarkModel(**kwargs)
        db.session.add(remark)
        db.session.commit()

        return remark

    @classmethod
    def getRemarkById(cls, id):
        return db.session.query(RemarkModel).filter_by(id=id).first()

    @classmethod
    def getRemarksByPayrollHistoryId(cls, payroll_history_id):
        return db.session.query(RemarkModel).filter_by(payroll_history_id=payroll_history_id).all()

    @classmethod
    def getRemarksByEmployeeId(cls, employee_id):
        return db.session.query(RemarkModel).filter_by(employee_id=employee_id).all()

    @classmethod
    def updateRemark(cls, id, **kwargs):
        # Find the remark by ID
        remark = db.session.query(RemarkModel).filter_by(id=id).first()
        if not remark:
            return None

        # Perform the update with filtered kwargs
        db.session.query(RemarkModel).filter_by(id=id).update(kwargs)
        db.session.commit()

        # Refresh and return the updated remark
        db.session.refresh(remark)
        return remark

    @classmethod
    def deleteRemark(cls, remark_id):
        # Find the remark by ID
        remark = db.session.query(RemarkModel).filter_by(id=remark_id).first()
        if not remark:
            return None  # Explicitly return None if the remark doesn't exist

        # Delete the remark
        db.session.delete(remark)
        db.session.commit()
        return remark  # Return the deleted remark object for response handling
    
    @classmethod
    def deletePayrollHistory(cls, payroll_history_id):
        """
        Delete a payroll history record and its associated remarks.
        """
        print(f"Fetching payroll history with ID {payroll_history_id}")
        payroll_history = PayrollHistoryModel.query.filter_by(id=payroll_history_id).first()

        if not payroll_history:
            print(f"No payroll history found for ID {payroll_history_id}")
            return None  # Indicate that the record does not exist

        # Delete associated remarks
        print(f"Deleting remarks for payroll history with ID {payroll_history_id}")
        RemarkModel.query.filter(RemarkModel.payroll_history_id == payroll_history_id).delete()

        # Delete the payroll history record
        print(f"Deleting payroll history record with ID {payroll_history_id}")
        PayrollHistoryModel.query.filter(PayrollHistoryModel.id == payroll_history_id).delete()

        # Commit the transaction
        db.session.commit()
        print(f"Payroll history with ID {payroll_history_id} deleted successfully")
        return payroll_history
    

    @classmethod
    def bulkDeletePayrollHistory(cls, payroll_history_ids):
        if not payroll_history_ids or not isinstance(payroll_history_ids, list):
            print("Invalid input: Expected a list of payroll history IDs.")
            return 0

        try:
            print(f"Fetching payroll histories with IDs {payroll_history_ids}")
            payroll_histories = PayrollHistoryModel.query.filter(PayrollHistoryModel.id.in_(payroll_history_ids)).all()

            if not payroll_histories:
                print("No payroll histories found for the provided IDs.")
                return 0

            print(f"Deleting remarks for payroll histories with IDs {payroll_history_ids}")
            RemarkModel.query.filter(RemarkModel.payroll_history_id.in_(payroll_history_ids)).delete(synchronize_session=False)

            print(f"Deleting payroll histories with IDs {payroll_history_ids}")
            PayrollHistoryModel.query.filter(PayrollHistoryModel.id.in_(payroll_history_ids)).delete(synchronize_session=False)

            db.session.commit()
            print(f"Successfully deleted {len(payroll_histories)} payroll histories and their associated remarks.")

            return len(payroll_histories)

        except Exception as e:
            db.session.rollback()
            print(f"Error occurred while deleting payroll histories: {e}")
