"""create repayment table

Revision ID: f1049b5ff644
Revises: 6bc9e0725c55
Create Date: 2025-05-31 19:55:05.160318

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'f1049b5ff644'
down_revision: Union[str, None] = '6bc9e0725c55'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    op.create_table(
       'loan_repayment',
          sa.Column('id',sa.Integer, primary_key=True, autoincrement=True),
          sa.Column('payroll_id', sa.Integer, sa.<PERSON>ey('payroll_history.id'), nullable=False),
          sa.Column('loan_id', sa.Integer, sa.ForeignKey('loan_request.id'), nullable=False),
          sa.Column('amount', sa.Float, nullable=False),
          sa.Column('created_at', sa.DateTime, server_default=sa.func.now(), nullable=False)
          
     )


def downgrade() -> None:
    op.drop_table('loan_repayment')

