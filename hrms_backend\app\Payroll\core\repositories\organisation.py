from core.models.organisation import OrganisationModel
from core.databases.database import db
from core.repositories.user import UserRepository
from core.services.CloudinaryService import CloudinaryService
from sqlalchemy.orm.attributes import flag_modified
from core.models.employees import EmployeeModel
from sqlalchemy.orm import joinedload
from sqlalchemy import func

class OrganisationRepository:
    def __init__(self) -> None:
        self.cloudinary_service = CloudinaryService()


    @classmethod
    def createOrganisation(self, slug, organisation_name,email, country, address, state, city, zipcode, phone, industry, type, logoUrl=None, logo_file=None):
        try:
             
            logo_url = logoUrl
            # Upload logo to Cloudinary if provided
            # if logo_file:
            #     upload_result = self().cloudinary_service.upload_image(logo_file)
            #     logo_url = upload_result.get("secure_url")

            organisation = OrganisationModel(
                slug=slug,
                organisation_name=organisation_name,
                email=email,
                address=address,
                country=country,
                city=city,
                state=state,
                zipcode=zipcode,
                phone=phone,
                industry=industry,
                type=type,
                logoUrl=logo_url, 
                user_id=UserRepository().authUserId()
            )
            db.session.add(organisation)
            db.session.commit()
        except Exception as e:
            print(e)

        return organisation


    @classmethod
    def fetchAll(self):
        return OrganisationModel.query.filter_by(user_id=UserRepository().authUserId()).all()
    
    @classmethod
    def getOrganisationBySlug(self, slug):
        return OrganisationModel.query.filter_by(user_id=UserRepository().authUserId()).filter(OrganisationModel.slug == slug).order_by(OrganisationModel.timestamp.desc()).all()

    @classmethod
    def getOrganisationById(self, id):
        return OrganisationModel.query.filter_by(user_id=UserRepository().authUserId()).filter(OrganisationModel.id == id).first()

    @classmethod
    def fetchAllOrganisation(self):
        # return OrganisationModel.query.filter_by(user_id=UserRepository().authUserId()).order_by(OrganisationModel.timestamp.desc()).all()
        return (
            db.session.query(
                OrganisationModel,
                func.count(EmployeeModel.id).label("employee_count")
            )
            .filter(OrganisationModel.user_id == UserRepository().authUserId())
            .outerjoin(EmployeeModel, OrganisationModel.id == EmployeeModel.organisation_id)
            .group_by(OrganisationModel.id)
            .order_by(OrganisationModel.timestamp.desc())
            .all()
        )
    
    @classmethod
    def getOrganisationByKeys(self, kwargs):
        return OrganisationModel.query.filter_by(**kwargs).all()

    
    @classmethod
    def updateOrganisation(cls, id, **kwargs):
        organisation = OrganisationModel.query.filter_by(id=id).first()
        if not organisation:
            return None

        # Handle logo upload separately
        logo_file = kwargs.pop("logo_file", None)
        if logo_file:
            try:
                # Delete old logo if it exists
                if organisation.logoUrl:
                    cls().cloudinary_service.delete_image(organisation.logoUrl)
                
                # Upload new logo
                upload_result = cls().cloudinary_service.upload_image(logo_file)
                print(f"Uploading image for organisation {id}")

                if upload_result.get("secure_url"):
                    kwargs["logoUrl"] = upload_result["secure_url"]
                    print(f"Upload result: {upload_result}")

            except Exception as e:
                print(f"Error uploading logo: {e}")

        # Update only provided fields
        for key, value in kwargs.items():
            setattr(organisation, key, value)

        db.session.commit()
        # print("✅ Database commit successful")
        return organisation

    @classmethod
    def deleteOrganisation(self, id):
        OrganisationModel.query.filter(OrganisationModel.id == id).delete()
        db.session.commit()
        return True        