from functools import wraps

from flask import Flask
from flask import jsonify
from flask_jwt_extended import get_jwt
from flask_jwt_extended import verify_jwt_in_request

secrete_key = "DR_OMOH"

from functools import wraps
from flask import jsonify
from flask_jwt_extended import verify_jwt_in_request, get_jwt

def roles_required(allowed_roles, fresh=False):
    def wrapper(fn):
        @wraps(fn)
        def decorator(*args, **kwargs):
            verify_jwt_in_request(optional=False, fresh=fresh)
            claims = get_jwt()

            for role in allowed_roles:
                if claims.get(f"is_{role}"):
                    return fn(*args, **kwargs)

            return jsonify(msg=f"Access denied for is_{role}. Requires one of the roles: {', '.join(allowed_roles)}"), 403
        return decorator
    return wrapper

# def employee_required():
#     def wrapper(fn):
#         @wraps(fn)
#         def decorator(*args, **kwargs):
#             verify_jwt_in_request()
#             claims = get_jwt()
#             if "is_employee" in claims and claims["is_employee"]:
#                 return fn(*args, **kwargs)
#             else:
#                 return jsonify(msg="Employee only!"), 403
#         return decorator
#     return wrapper

# def admin_required():
#     def wrapper(fn):
#         @wraps(fn)
#         def decorator(*args, **kwargs):
#             verify_jwt_in_request()
#             claims = get_jwt()
#             if "is_admin" in claims and claims["is_admin"]:
#                 return fn(*args, **kwargs)
#             else:
#                 return jsonify(msg="Admins only!"), 403
#         return decorator
#     return wrapper
