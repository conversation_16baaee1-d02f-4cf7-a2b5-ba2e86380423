from fastapi import APIRouter, HTTPException, status, Depends, Query, BackgroundTasks
from sqlalchemy.orm import Session
from typing import List, Optional
from uuid import UUID

from ..core.security import get_current_user, require_permission, CurrentUser
from ..core.rbac import Permissions
from ..db.session import get_db
from ..schemas.reporting import (
    ReportTemplateCreate, ReportTemplateUpdate, ReportTemplateResponse,
    ReportTemplateListResponse, ReportGenerationRequest, ReportResponse,
    ReportListResponse, DashboardCreate, DashboardResponse, DashboardListResponse,
    AnalyticsQuery, AnalyticsResult, KPIDefinition, KPIValue
)
from ..services.hr_management.reporting_service import ReportingService
from ..db.models.reporting import ReportType, ReportFormat, ReportStatus

router = APIRouter()
reporting_service = ReportingService()


# Report Template Endpoints
@router.post("/templates", response_model=ReportTemplateResponse)
async def create_report_template(
    template_data: ReportTemplateCreate,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.REPORTING_CREATE))
):
    """Create a new report template"""
    return await reporting_service.create_report_template(db, template_data, current_user)


@router.get("/templates", response_model=ReportTemplateListResponse)
async def get_report_templates(
    skip: int = Query(0, ge=0),
    limit: int = Query(20, ge=1, le=100),
    report_type: Optional[ReportType] = Query(None),
    category: Optional[str] = Query(None),
    is_active: bool = Query(True),
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.REPORTING_READ))
):
    """Get report templates with filtering and pagination"""
    from ..db.models.reporting import ReportTemplate
    
    query = db.query(ReportTemplate)
    
    # Apply organization filter
    if current_user.organization_id:
        query = query.filter(ReportTemplate.organization_id == current_user.organization_id)
    
    # Apply filters
    if report_type:
        query = query.filter(ReportTemplate.report_type == report_type)
    
    if category:
        query = query.filter(ReportTemplate.category == category)
    
    if is_active is not None:
        query = query.filter(ReportTemplate.is_active == is_active)
    
    # Filter by user permissions
    user_roles = [current_user.role] if current_user.role else []
    query = query.filter(ReportTemplate.roles_allowed.op('&&')(user_roles))
    
    # Get total count
    total = query.count()
    
    # Apply pagination
    templates = query.offset(skip).limit(limit).all()
    
    return ReportTemplateListResponse(
        items=templates,
        total=total,
        page=skip // limit + 1,
        size=limit,
        pages=(total + limit - 1) // limit
    )


@router.get("/templates/{template_id}", response_model=ReportTemplateResponse)
async def get_report_template(
    template_id: UUID,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.REPORTING_READ))
):
    """Get a specific report template"""
    from ..db.models.reporting import ReportTemplate
    
    template = db.query(ReportTemplate).filter(
        ReportTemplate.id == template_id,
        ReportTemplate.organization_id == current_user.organization_id
    ).first()
    
    if not template:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Report template not found"
        )
    
    return template


@router.put("/templates/{template_id}", response_model=ReportTemplateResponse)
async def update_report_template(
    template_id: UUID,
    template_data: ReportTemplateUpdate,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.REPORTING_UPDATE))
):
    """Update a report template"""
    from ..db.models.reporting import ReportTemplate
    
    template = db.query(ReportTemplate).filter(
        ReportTemplate.id == template_id,
        ReportTemplate.organization_id == current_user.organization_id
    ).first()
    
    if not template:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Report template not found"
        )
    
    # Update fields
    update_data = template_data.dict(exclude_unset=True)
    for field, value in update_data.items():
        setattr(template, field, value)
    
    db.commit()
    db.refresh(template)
    
    return template


# Report Generation Endpoints
@router.post("/generate", response_model=ReportResponse)
async def generate_report(
    request: ReportGenerationRequest,
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.REPORTING_CREATE))
):
    """Generate a report from template"""
    return await reporting_service.generate_report(db, request, current_user)


@router.get("/reports", response_model=ReportListResponse)
async def get_reports(
    skip: int = Query(0, ge=0),
    limit: int = Query(20, ge=1, le=100),
    template_id: Optional[UUID] = Query(None),
    status: Optional[ReportStatus] = Query(None),
    report_type: Optional[ReportType] = Query(None),
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.REPORTING_READ))
):
    """Get generated reports with filtering and pagination"""
    from ..db.models.reporting import Report
    
    query = db.query(Report)
    
    # Apply organization filter
    if current_user.organization_id:
        query = query.filter(Report.organization_id == current_user.organization_id)
    
    # Apply filters
    if template_id:
        query = query.filter(Report.template_id == template_id)
    
    if status:
        query = query.filter(Report.status == status)
    
    if report_type:
        query = query.filter(Report.report_type == report_type)
    
    # Get total count
    total = query.count()
    
    # Apply pagination
    reports = query.order_by(Report.created_at.desc()).offset(skip).limit(limit).all()
    
    return ReportListResponse(
        items=reports,
        total=total,
        page=skip // limit + 1,
        size=limit,
        pages=(total + limit - 1) // limit
    )


@router.get("/reports/{report_id}", response_model=ReportResponse)
async def get_report(
    report_id: UUID,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.REPORTING_READ))
):
    """Get a specific report"""
    from ..db.models.reporting import Report
    
    report = db.query(Report).filter(
        Report.id == report_id,
        Report.organization_id == current_user.organization_id
    ).first()
    
    if not report:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Report not found"
        )
    
    return report


# Dashboard Endpoints
@router.post("/dashboards", response_model=DashboardResponse)
async def create_dashboard(
    dashboard_data: DashboardCreate,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.REPORTING_CREATE))
):
    """Create a new dashboard"""
    from ..db.models.reporting import Dashboard
    
    dashboard = Dashboard(
        **dashboard_data.dict(),
        created_by=current_user.user_id,
        organization_id=current_user.organization_id
    )
    
    db.add(dashboard)
    db.commit()
    db.refresh(dashboard)
    
    return dashboard


@router.get("/dashboards", response_model=DashboardListResponse)
async def get_dashboards(
    skip: int = Query(0, ge=0),
    limit: int = Query(20, ge=1, le=100),
    category: Optional[str] = Query(None),
    is_public: Optional[bool] = Query(None),
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.REPORTING_READ))
):
    """Get dashboards with filtering and pagination"""
    from ..db.models.reporting import Dashboard
    
    query = db.query(Dashboard)
    
    # Apply organization filter
    if current_user.organization_id:
        query = query.filter(Dashboard.organization_id == current_user.organization_id)
    
    # Apply filters
    if category:
        query = query.filter(Dashboard.category == category)
    
    if is_public is not None:
        query = query.filter(Dashboard.is_public == is_public)
    
    # Filter by user permissions (public dashboards or user has access)
    user_roles = [current_user.role] if current_user.role else []
    query = query.filter(
        or_(
            Dashboard.is_public == True,
            Dashboard.roles_allowed.op('&&')(user_roles)
        )
    )
    
    # Get total count
    total = query.count()
    
    # Apply pagination
    dashboards = query.offset(skip).limit(limit).all()
    
    return DashboardListResponse(
        items=dashboards,
        total=total,
        page=skip // limit + 1,
        size=limit,
        pages=(total + limit - 1) // limit
    )


@router.get("/dashboards/{dashboard_id}", response_model=DashboardResponse)
async def get_dashboard(
    dashboard_id: UUID,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.REPORTING_READ))
):
    """Get a specific dashboard"""
    from ..db.models.reporting import Dashboard
    
    dashboard = db.query(Dashboard).filter(
        Dashboard.id == dashboard_id,
        Dashboard.organization_id == current_user.organization_id
    ).first()
    
    if not dashboard:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Dashboard not found"
        )
    
    # Update view count
    dashboard.view_count += 1
    dashboard.last_accessed = datetime.utcnow()
    db.commit()
    
    return dashboard


# Analytics Endpoints
@router.post("/analytics/query", response_model=AnalyticsResult)
async def execute_analytics_query(
    query: AnalyticsQuery,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.REPORTING_READ))
):
    """Execute an analytics query"""
    return await reporting_service.execute_analytics_query(db, query, current_user)


@router.get("/analytics/metrics")
async def get_available_metrics(
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.REPORTING_READ))
):
    """Get available metrics for analytics"""
    return {
        "metrics": [
            {"name": "employees", "description": "Employee metrics", "dimensions": ["department", "role", "status"]},
            {"name": "attendance", "description": "Attendance metrics", "dimensions": ["date", "employee", "status"]},
            {"name": "leave", "description": "Leave metrics", "dimensions": ["type", "status", "employee"]},
            {"name": "performance", "description": "Performance metrics", "dimensions": ["rating", "period", "employee"]},
            {"name": "recruitment", "description": "Recruitment metrics", "dimensions": ["status", "source", "position"]},
            {"name": "training", "description": "Training metrics", "dimensions": ["course", "status", "completion_rate"]}
        ]
    }


# KPI Endpoints
@router.post("/kpis", response_model=dict)
async def create_kpi(
    kpi_data: KPIDefinition,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.REPORTING_CREATE))
):
    """Create a new KPI"""
    from ..db.models.reporting import KPI
    
    kpi = KPI(
        **kpi_data.dict(),
        organization_id=current_user.organization_id
    )
    
    db.add(kpi)
    db.commit()
    db.refresh(kpi)
    
    return {"id": kpi.id, "message": "KPI created successfully"}


@router.get("/kpis")
async def get_kpis(
    category: Optional[str] = Query(None),
    is_active: bool = Query(True),
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.REPORTING_READ))
):
    """Get KPIs with current values"""
    from ..db.models.reporting import KPI, KPIValue
    from sqlalchemy.orm import joinedload
    
    query = db.query(KPI).options(joinedload(KPI.values))
    
    # Apply organization filter
    if current_user.organization_id:
        query = query.filter(KPI.organization_id == current_user.organization_id)
    
    # Apply filters
    if category:
        query = query.filter(KPI.category == category)
    
    if is_active is not None:
        query = query.filter(KPI.is_active == is_active)
    
    kpis = query.all()
    
    # Get latest values for each KPI
    result = []
    for kpi in kpis:
        latest_value = db.query(KPIValue).filter(
            KPIValue.kpi_id == kpi.id
        ).order_by(KPIValue.period_end.desc()).first()
        
        kpi_data = {
            "id": kpi.id,
            "name": kpi.name,
            "description": kpi.description,
            "category": kpi.category,
            "unit": kpi.unit,
            "target_value": kpi.target_value,
            "current_value": latest_value.value if latest_value else None,
            "status": latest_value.status if latest_value else "no_data",
            "trend": latest_value.trend if latest_value else None,
            "last_updated": latest_value.calculated_at if latest_value else None
        }
        result.append(kpi_data)
    
    return {"kpis": result}
